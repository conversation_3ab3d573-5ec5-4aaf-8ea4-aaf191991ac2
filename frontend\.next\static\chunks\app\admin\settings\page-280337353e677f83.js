(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7122],{21152:(e,r,t)=>{Promise.resolve().then(t.bind(t,43038))},43038:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var s=t(95155),a=t(12115),n=t(35695),u=t(72476),l=t(69327),i=t(28496);let o=()=>{let e=(0,n.useRouter)(),[r,t]=(0,a.useState)(!1),[o,d]=(0,a.useState)(!0);return((0,a.useEffect)(()=>{(async()=>{if(!u.c4())return e.push("/login");try{var r;let s=await l.Nt.getUserData();if("ADMIN"!==s.user_type&&"admin"!==s.role&&(null==(r=s.user)?void 0:r.role)!=="admin")return void e.replace("/settings");"undefined"!=typeof document&&(document.cookie="role=ADMIN; path=/; max-age=86400"),t(!0),d(!1)}catch(r){console.error("Error fetching user data:",r),e.push("/login")}})()},[e]),o)?(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):r?(0,s.jsx)(i.default,{}):null}}},e=>{var r=r=>e(e.s=r);e.O(0,[3464,3983,9327,7200,8441,1684,7358],()=>r(21152)),_N_E=e.O()}]);