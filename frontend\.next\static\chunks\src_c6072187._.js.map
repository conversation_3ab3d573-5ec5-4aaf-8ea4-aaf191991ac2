{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/api/errorHandler.js"], "sourcesContent": ["import { useNotification } from '../contexts/NotificationContext';\r\n\r\n// Error codes and their corresponding handlers\r\nexport const ERROR_PATTERNS = {\r\n  AUTHENTICATION: {\r\n    codes: [401],\r\n    keywords: ['unauthorized', 'authentication', 'token', 'login'],\r\n    handler: 'showAuthError'\r\n  },\r\n  SESSION_EXPIRED: {\r\n    codes: [401],\r\n    keywords: ['expired', 'invalid token', 'token expired'],\r\n    handler: 'showSessionExpiredModal'\r\n  },\r\n  PERMISSION_DENIED: {\r\n    codes: [403],\r\n    keywords: ['permission', 'forbidden', 'access denied'],\r\n    handler: 'showAuthError'\r\n  },\r\n  VALIDATION: {\r\n    codes: [400, 422],\r\n    keywords: ['validation', 'invalid', 'required'],\r\n    handler: 'showValidationError'\r\n  },\r\n  RESUME_REQUIRED: {\r\n    fields: ['resume'],\r\n    keywords: ['resume', 'must be uploaded', 'present in the student profile'],\r\n    handler: 'showMissingResumeModal'\r\n  },\r\n  PROFILE_INCOMPLETE: {\r\n    keywords: ['profile incomplete', 'missing profile', 'update profile'],\r\n    handler: 'showProfileIncompleteModal'\r\n  },\r\n  FILE_UPLOAD: {\r\n    keywords: ['file', 'upload', 'size', 'format', 'extension'],\r\n    handler: 'showFileUploadError'\r\n  },\r\n  NETWORK_ERROR: {\r\n    codes: ['NETWORK_ERROR', 'ECONNREFUSED', 'ERR_NETWORK'],\r\n    keywords: ['network', 'connection', 'timeout'],\r\n    handler: 'showNetworkError'\r\n  },\r\n  MAINTENANCE: {\r\n    codes: [503, 502],\r\n    keywords: ['maintenance', 'service unavailable', 'temporarily unavailable'],\r\n    handler: 'showMaintenanceModal'\r\n  }\r\n};\r\n\r\n// Smart error detection and handling\r\nexport const detectAndHandleError = (error, context = '', notificationHandlers) => {\r\n  const errorData = error?.response?.data || {};\r\n  const errorMessage = (errorData.detail || errorData.message || error.message || '').toLowerCase();\r\n  const statusCode = error?.response?.status;\r\n\r\n  // Check for specific error patterns\r\n  for (const [pattern, config] of Object.entries(ERROR_PATTERNS)) {\r\n    // Check status codes\r\n    if (config.codes && config.codes.includes(statusCode)) {\r\n      // Additional keyword check for more precision\r\n      if (config.keywords && !config.keywords.some(keyword => errorMessage.includes(keyword))) {\r\n        continue;\r\n      }\r\n      \r\n      return handleSpecificError(pattern, error, context, notificationHandlers);\r\n    }\r\n\r\n    // Check for field-specific errors (like resume)\r\n    if (config.fields && config.fields.some(field => errorData[field])) {\r\n      return handleSpecificError(pattern, error, context, notificationHandlers);\r\n    }\r\n\r\n    // Check keywords in error message\r\n    if (config.keywords && config.keywords.some(keyword => errorMessage.includes(keyword))) {\r\n      return handleSpecificError(pattern, error, context, notificationHandlers);\r\n    }\r\n  }\r\n\r\n  // Fallback to generic error handling\r\n  return handleGenericError(error, context, notificationHandlers);\r\n};\r\n\r\nconst handleSpecificError = (pattern, error, context, notificationHandlers) => {\r\n  const config = ERROR_PATTERNS[pattern];\r\n  const handlerName = config.handler;\r\n  \r\n  if (notificationHandlers[handlerName]) {\r\n    switch (handlerName) {\r\n      case 'showMissingResumeModal':\r\n        notificationHandlers.showMissingResumeModal();\r\n        break;\r\n      case 'showSessionExpiredModal':\r\n        notificationHandlers.showSessionExpiredModal();\r\n        break;\r\n      case 'showMaintenanceModal':\r\n        notificationHandlers.showMaintenanceModal();\r\n        break;\r\n      case 'showValidationError':\r\n        const errorData = error?.response?.data || {};\r\n        notificationHandlers.showValidationError(\r\n          `Validation Error ${context ? `in ${context}` : ''}`, \r\n          errorData\r\n        );\r\n        break;\r\n      case 'showAuthError':\r\n        const message = error?.response?.data?.detail || \r\n                       error?.response?.data?.message || \r\n                       `Authentication failed${context ? ` while ${context}` : ''}`;\r\n        notificationHandlers.showAuthError(message);\r\n        break;\r\n      case 'showFileUploadError':\r\n        notificationHandlers.showFileUploadError();\r\n        break;\r\n      case 'showNetworkError':\r\n        notificationHandlers.showNetworkError(error);\r\n        break;\r\n      case 'showProfileIncompleteModal':\r\n        notificationHandlers.showProfileIncompleteModal();\r\n        break;\r\n      default:\r\n        return handleGenericError(error, context, notificationHandlers);\r\n    }\r\n    return true; // Error was handled\r\n  }\r\n  \r\n  return false; // Error not handled\r\n};\r\n\r\nconst handleGenericError = (error, context, notificationHandlers) => {\r\n  if (notificationHandlers.handleApiError) {\r\n    notificationHandlers.handleApiError(error, context);\r\n    return true;\r\n  }\r\n  \r\n  // Ultimate fallback\r\n  console.error('Unhandled error:', error);\r\n  return false;\r\n};\r\n\r\n// Hook for easy error handling in components\r\nexport const useErrorHandler = () => {\r\n  const notificationHandlers = useNotification();\r\n  \r\n  const handleError = (error, context = '') => {\r\n    return detectAndHandleError(error, context, notificationHandlers);\r\n  };\r\n\r\n  return { handleError };\r\n};\r\n\r\n// Axios interceptor setup\r\nexport const setupErrorInterceptor = (axiosInstance, notificationHandlers) => {\r\n  axiosInstance.interceptors.response.use(\r\n    (response) => response,\r\n    (error) => {\r\n      // Automatically handle common errors\r\n      detectAndHandleError(error, 'API request', notificationHandlers);\r\n      return Promise.reject(error);\r\n    }\r\n  );\r\n};\r\n\r\nexport default {\r\n  detectAndHandleError,\r\n  useErrorHandler,\r\n  setupErrorInterceptor,\r\n  ERROR_PATTERNS\r\n}; "], "names": [], "mappings": ";;;;;;;AAAA;;;AAGO,MAAM,iBAAiB;IAC5B,gBAAgB;QACd,OAAO;YAAC;SAAI;QACZ,UAAU;YAAC;YAAgB;YAAkB;YAAS;SAAQ;QAC9D,SAAS;IACX;IACA,iBAAiB;QACf,OAAO;YAAC;SAAI;QACZ,UAAU;YAAC;YAAW;YAAiB;SAAgB;QACvD,SAAS;IACX;IACA,mBAAmB;QACjB,OAAO;YAAC;SAAI;QACZ,UAAU;YAAC;YAAc;YAAa;SAAgB;QACtD,SAAS;IACX;IACA,YAAY;QACV,OAAO;YAAC;YAAK;SAAI;QACjB,UAAU;YAAC;YAAc;YAAW;SAAW;QAC/C,SAAS;IACX;IACA,iBAAiB;QACf,QAAQ;YAAC;SAAS;QAClB,UAAU;YAAC;YAAU;YAAoB;SAAiC;QAC1E,SAAS;IACX;IACA,oBAAoB;QAClB,UAAU;YAAC;YAAsB;YAAmB;SAAiB;QACrE,SAAS;IACX;IACA,aAAa;QACX,UAAU;YAAC;YAAQ;YAAU;YAAQ;YAAU;SAAY;QAC3D,SAAS;IACX;IACA,eAAe;QACb,OAAO;YAAC;YAAiB;YAAgB;SAAc;QACvD,UAAU;YAAC;YAAW;YAAc;SAAU;QAC9C,SAAS;IACX;IACA,aAAa;QACX,OAAO;YAAC;YAAK;SAAI;QACjB,UAAU;YAAC;YAAe;YAAuB;SAA0B;QAC3E,SAAS;IACX;AACF;AAGO,MAAM,uBAAuB,CAAC,OAAO,UAAU,EAAE,EAAE;IACxD,MAAM,YAAY,OAAO,UAAU,QAAQ,CAAC;IAC5C,MAAM,eAAe,CAAC,UAAU,MAAM,IAAI,UAAU,OAAO,IAAI,MAAM,OAAO,IAAI,EAAE,EAAE,WAAW;IAC/F,MAAM,aAAa,OAAO,UAAU;IAEpC,oCAAoC;IACpC,KAAK,MAAM,CAAC,SAAS,OAAO,IAAI,OAAO,OAAO,CAAC,gBAAiB;QAC9D,qBAAqB;QACrB,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,CAAC,QAAQ,CAAC,aAAa;YACrD,8CAA8C;YAC9C,IAAI,OAAO,QAAQ,IAAI,CAAC,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAA,UAAW,aAAa,QAAQ,CAAC,WAAW;gBACvF;YACF;YAEA,OAAO,oBAAoB,SAAS,OAAO,SAAS;QACtD;QAEA,gDAAgD;QAChD,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,CAAA,QAAS,SAAS,CAAC,MAAM,GAAG;YAClE,OAAO,oBAAoB,SAAS,OAAO,SAAS;QACtD;QAEA,kCAAkC;QAClC,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAA,UAAW,aAAa,QAAQ,CAAC,WAAW;YACtF,OAAO,oBAAoB,SAAS,OAAO,SAAS;QACtD;IACF;IAEA,qCAAqC;IACrC,OAAO,mBAAmB,OAAO,SAAS;AAC5C;AAEA,MAAM,sBAAsB,CAAC,SAAS,OAAO,SAAS;IACpD,MAAM,SAAS,cAAc,CAAC,QAAQ;IACtC,MAAM,cAAc,OAAO,OAAO;IAElC,IAAI,oBAAoB,CAAC,YAAY,EAAE;QACrC,OAAQ;YACN,KAAK;gBACH,qBAAqB,sBAAsB;gBAC3C;YACF,KAAK;gBACH,qBAAqB,uBAAuB;gBAC5C;YACF,KAAK;gBACH,qBAAqB,oBAAoB;gBACzC;YACF,KAAK;gBACH,MAAM,YAAY,OAAO,UAAU,QAAQ,CAAC;gBAC5C,qBAAqB,mBAAmB,CACtC,CAAC,iBAAiB,EAAE,UAAU,CAAC,GAAG,EAAE,SAAS,GAAG,IAAI,EACpD;gBAEF;YACF,KAAK;gBACH,MAAM,UAAU,OAAO,UAAU,MAAM,UACxB,OAAO,UAAU,MAAM,WACvB,CAAC,qBAAqB,EAAE,UAAU,CAAC,OAAO,EAAE,SAAS,GAAG,IAAI;gBAC3E,qBAAqB,aAAa,CAAC;gBACnC;YACF,KAAK;gBACH,qBAAqB,mBAAmB;gBACxC;YACF,KAAK;gBACH,qBAAqB,gBAAgB,CAAC;gBACtC;YACF,KAAK;gBACH,qBAAqB,0BAA0B;gBAC/C;YACF;gBACE,OAAO,mBAAmB,OAAO,SAAS;QAC9C;QACA,OAAO,MAAM,oBAAoB;IACnC;IAEA,OAAO,OAAO,oBAAoB;AACpC;AAEA,MAAM,qBAAqB,CAAC,OAAO,SAAS;IAC1C,IAAI,qBAAqB,cAAc,EAAE;QACvC,qBAAqB,cAAc,CAAC,OAAO;QAC3C,OAAO;IACT;IAEA,oBAAoB;IACpB,QAAQ,KAAK,CAAC,oBAAoB;IAClC,OAAO;AACT;AAGO,MAAM,kBAAkB;;IAC7B,MAAM,uBAAuB,CAAA,GAAA,0IAAA,CAAA,kBAAe,AAAD;IAE3C,MAAM,cAAc,CAAC,OAAO,UAAU,EAAE;QACtC,OAAO,qBAAqB,OAAO,SAAS;IAC9C;IAEA,OAAO;QAAE;IAAY;AACvB;GARa;;QACkB,0IAAA,CAAA,kBAAe;;;AAUvC,MAAM,wBAAwB,CAAC,eAAe;IACnD,cAAc,YAAY,CAAC,QAAQ,CAAC,GAAG;qCACrC,CAAC,WAAa;;qCACd,CAAC;YACC,qCAAqC;YACrC,qBAAqB,OAAO,eAAe;YAC3C,OAAO,QAAQ,MAAM,CAAC;QACxB;;AAEJ;uCAEe;IACb;IACA;IACA;IACA;AACF", "debugId": null}}, {"offset": {"line": 233, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/api/client.js"], "sourcesContent": ["import axios from 'axios';\r\nimport { setupErrorInterceptor } from './errorHandler';\r\n\r\nconst client = axios.create({\r\n  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000',\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n});\r\n\r\n// Add a request interceptor to include the auth token\r\nclient.interceptors.request.use(\r\n  (config) => {\r\n    // Get the token from localStorage\r\n    const token = localStorage.getItem('access_token');\r\n    \r\n    // If token exists, add it to the Authorization header\r\n    if (token) {\r\n      config.headers['Authorization'] = `Bearer ${token}`;\r\n    }\r\n    \r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Add a response interceptor to handle 401 errors (token expired)\r\nclient.interceptors.response.use(\r\n  (response) => response,\r\n  async (error) => {\r\n    const originalRequest = error.config;\r\n    \r\n    // If error is 401 and we haven't tried to refresh the token yet\r\n    if (error.response?.status === 401 && !originalRequest._retry) {\r\n      originalRequest._retry = true;\r\n      \r\n      try {\r\n        // Get refresh token\r\n        const refreshToken = localStorage.getItem('refresh_token');\r\n        \r\n        if (refreshToken) {\r\n          // Try to get a new token\r\n          const response = await axios.post('http://127.0.0.1:8000/api/auth/token/refresh/', {\r\n            refresh: refreshToken\r\n          });\r\n          \r\n          // Store the new tokens\r\n          localStorage.setItem('access_token', response.data.access);\r\n          \r\n          // Update the Authorization header\r\n          originalRequest.headers['Authorization'] = `Bearer ${response.data.access}`;\r\n          \r\n          // Retry the original request\r\n          return client(originalRequest);\r\n        }\r\n      } catch (refreshError) {\r\n        console.error('Error refreshing token:', refreshError);\r\n        \r\n        // If token refresh fails, redirect to login\r\n        if (typeof window !== 'undefined') {\r\n          // Clear tokens\r\n          localStorage.removeItem('access_token');\r\n          localStorage.removeItem('refresh_token');\r\n          \r\n          // Redirect to login page\r\n          window.location.href = '/login';\r\n        }\r\n      }\r\n    }\r\n    \r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default client;\r\n"], "names": [], "mappings": ";;;AAIW;AAJX;AACA;;;AAEA,MAAM,SAAS,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC1B,SAAS,6DAAmC;IAC5C,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,sDAAsD;AACtD,OAAO,YAAY,CAAC,OAAO,CAAC,GAAG,CAC7B,CAAC;IACC,kCAAkC;IAClC,MAAM,QAAQ,aAAa,OAAO,CAAC;IAEnC,sDAAsD;IACtD,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;IACrD;IAEA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,kEAAkE;AAClE,OAAO,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC9B,CAAC,WAAa,UACd,OAAO;IACL,MAAM,kBAAkB,MAAM,MAAM;IAEpC,gEAAgE;IAChE,IAAI,MAAM,QAAQ,EAAE,WAAW,OAAO,CAAC,gBAAgB,MAAM,EAAE;QAC7D,gBAAgB,MAAM,GAAG;QAEzB,IAAI;YACF,oBAAoB;YACpB,MAAM,eAAe,aAAa,OAAO,CAAC;YAE1C,IAAI,cAAc;gBAChB,yBAAyB;gBACzB,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,iDAAiD;oBACjF,SAAS;gBACX;gBAEA,uBAAuB;gBACvB,aAAa,OAAO,CAAC,gBAAgB,SAAS,IAAI,CAAC,MAAM;gBAEzD,kCAAkC;gBAClC,gBAAgB,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,SAAS,IAAI,CAAC,MAAM,EAAE;gBAE3E,6BAA6B;gBAC7B,OAAO,OAAO;YAChB;QACF,EAAE,OAAO,cAAc;YACrB,QAAQ,KAAK,CAAC,2BAA2B;YAEzC,4CAA4C;YAC5C,wCAAmC;gBACjC,eAAe;gBACf,aAAa,UAAU,CAAC;gBACxB,aAAa,UAAU,CAAC;gBAExB,yBAAyB;gBACzB,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;QACF;IACF;IAEA,OAAO,QAAQ,MAAM,CAAC;AACxB;uCAGa", "debugId": null}}, {"offset": {"line": 304, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/api/jobs.js"], "sourcesContent": ["import client from './client';\r\n\r\n// List all jobs with pagination and filtering\r\nexport function listJobs(params = {}) {\r\n  const queryParams = new URLSearchParams();\r\n  \r\n  // Add pagination parameters\r\n  if (params.page) queryParams.append('page', params.page);\r\n  if (params.per_page) queryParams.append('per_page', params.per_page);\r\n  \r\n  // Add filtering parameters\r\n  if (params.job_type && params.job_type !== 'ALL') queryParams.append('job_type', params.job_type);\r\n  if (params.location && params.location !== 'ALL') queryParams.append('location', params.location);\r\n  if (params.salary_min) queryParams.append('salary_min', params.salary_min);\r\n  if (params.search) queryParams.append('search', params.search);\r\n  \r\n  const queryString = queryParams.toString();\r\n  const url = `/api/v1/college/default-college/jobs/${queryString ? `?${queryString}` : ''}`;\r\n  \r\n  return client.get(url);\r\n}\r\n\r\n// Apply to a job\r\nexport function applyToJob(job, coverLetter, additionalFields = {}) {\r\n  // Check if any additional fields contain files\r\n  const hasFiles = Object.values(additionalFields).some(value => value instanceof File);\r\n\r\n  if (hasFiles) {\r\n    // Use FormData for file uploads\r\n    const formData = new FormData();\r\n    formData.append('cover_letter', coverLetter);\r\n\r\n    // Handle additional fields with files\r\n    Object.entries(additionalFields).forEach(([key, value]) => {\r\n      if (value instanceof File) {\r\n        formData.append(key, value);\r\n      } else {\r\n        formData.append(key, JSON.stringify(value));\r\n      }\r\n    });\r\n\r\n    return client.post(`/api/v1/college/default-college/jobs/${job}/apply/`, formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n  } else {\r\n    // Use JSON for non-file submissions\r\n    return client.post(`/api/v1/college/default-college/jobs/${job}/apply/`, {\r\n      cover_letter: coverLetter,\r\n      additional_field_responses: additionalFields\r\n    });\r\n  }\r\n}\r\n\r\n// Get job details by ID\r\nexport function getJobById(jobId) {\r\n  return client.get(`/api/v1/college/default-college/jobs/${jobId}/`);\r\n}\r\n\r\n// List jobs the current student has applied to\r\nexport function listAppliedJobs() {\r\n  return client.get('/api/v1/college/default-college/jobs/applied/');\r\n}\r\n\r\n// Admin API functions for managing jobs\r\n\r\n// Note: Company management functions moved to /api/companies.js to avoid conflicts\r\n\r\n// Create a new job posting\r\nexport function createJob(jobData) {\r\n  return client.post('/api/v1/college/default-college/jobs/create/', jobData);\r\n}\r\n\r\n// Update job posting\r\nexport function updateJob(jobId, jobData) {\r\n  return client.put(`/api/v1/college/default-college/jobs/${jobId}/`, jobData);\r\n}\r\n\r\n// Delete job posting\r\nexport function deleteJob(jobId) {\r\n  return client.delete(`/api/v1/college/default-college/jobs/${jobId}/`);\r\n}\r\n\r\n// Get job applications for admin\r\nexport function getJobApplications(jobId) {\r\n  return client.get(`/api/v1/college/default-college/jobs/${jobId}/applications/`);\r\n}\r\n\r\n// Get all applications for admin dashboard\r\nexport function getAllApplications() {\r\n  return client.get('/api/v1/college/default-college/applications/');\r\n}\r\n\r\n// Admin-specific job listing (shows all jobs including unpublished)\r\nexport function listJobsAdmin(params = {}) {\r\n  const queryParams = new URLSearchParams();\r\n  \r\n  // Add pagination parameters\r\n  if (params.page) queryParams.append('page', params.page);\r\n  if (params.per_page) queryParams.append('per_page', params.per_page);\r\n  \r\n  // Add filtering parameters\r\n  if (params.search) queryParams.append('search', params.search);\r\n  if (params.type && params.type !== 'All') queryParams.append('job_type', params.type);\r\n  if (params.minCTC) queryParams.append('salary_min', params.minCTC);\r\n  if (params.maxCTC) queryParams.append('salary_max', params.maxCTC);\r\n  if (params.minStipend) queryParams.append('stipend_min', params.minStipend);\r\n  if (params.maxStipend) queryParams.append('stipend_max', params.maxStipend);\r\n  if (params.location) queryParams.append('location', params.location);\r\n  if (params.is_published !== undefined) queryParams.append('is_published', params.is_published);\r\n  \r\n  // Add company filtering\r\n  if (params.company_id) queryParams.append('company_id', params.company_id);\r\n  if (params.company_name) queryParams.append('company_name', params.company_name);\r\n  \r\n  const queryString = queryParams.toString();\r\n  const url = `/api/v1/college/default-college/jobs/admin/${queryString ? `?${queryString}` : ''}`;\r\n  \r\n  console.log('🌐 listJobsAdmin calling URL:', url, 'with params:', params);\r\n  \r\n  return client.get(url).then(response => {\r\n    console.log('🌐 listJobsAdmin response:', {\r\n      status: response.status,\r\n      totalJobs: response.data?.pagination?.total_count || 0,\r\n      currentPage: response.data?.pagination?.current_page || 1,\r\n      totalPages: response.data?.pagination?.total_pages || 1\r\n    });\r\n    return response;\r\n  }).catch(error => {\r\n    console.error('🌐 listJobsAdmin error:', error);\r\n    console.error('🌐 listJobsAdmin error response:', error.response?.data);\r\n    throw error;\r\n  });\r\n}\r\n\r\n// Toggle job publish status\r\nexport function toggleJobPublish(jobId) {\r\n  return client.patch(`/api/v1/jobs/${jobId}/toggle-publish/`);\r\n}\r\n\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;AAGO,SAAS,SAAS,SAAS,CAAC,CAAC;IAClC,MAAM,cAAc,IAAI;IAExB,4BAA4B;IAC5B,IAAI,OAAO,IAAI,EAAE,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI;IACvD,IAAI,OAAO,QAAQ,EAAE,YAAY,MAAM,CAAC,YAAY,OAAO,QAAQ;IAEnE,2BAA2B;IAC3B,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,KAAK,OAAO,YAAY,MAAM,CAAC,YAAY,OAAO,QAAQ;IAChG,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,KAAK,OAAO,YAAY,MAAM,CAAC,YAAY,OAAO,QAAQ;IAChG,IAAI,OAAO,UAAU,EAAE,YAAY,MAAM,CAAC,cAAc,OAAO,UAAU;IACzE,IAAI,OAAO,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;IAE7D,MAAM,cAAc,YAAY,QAAQ;IACxC,MAAM,MAAM,CAAC,qCAAqC,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;IAE1F,OAAO,uHAAA,CAAA,UAAM,CAAC,GAAG,CAAC;AACpB;AAGO,SAAS,WAAW,GAAG,EAAE,WAAW,EAAE,mBAAmB,CAAC,CAAC;IAChE,+CAA+C;IAC/C,MAAM,WAAW,OAAO,MAAM,CAAC,kBAAkB,IAAI,CAAC,CAAA,QAAS,iBAAiB;IAEhF,IAAI,UAAU;QACZ,gCAAgC;QAChC,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,gBAAgB;QAEhC,sCAAsC;QACtC,OAAO,OAAO,CAAC,kBAAkB,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YACpD,IAAI,iBAAiB,MAAM;gBACzB,SAAS,MAAM,CAAC,KAAK;YACvB,OAAO;gBACL,SAAS,MAAM,CAAC,KAAK,KAAK,SAAS,CAAC;YACtC;QACF;QAEA,OAAO,uHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,CAAC,qCAAqC,EAAE,IAAI,OAAO,CAAC,EAAE,UAAU;YACjF,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF,OAAO;QACL,oCAAoC;QACpC,OAAO,uHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,CAAC,qCAAqC,EAAE,IAAI,OAAO,CAAC,EAAE;YACvE,cAAc;YACd,4BAA4B;QAC9B;IACF;AACF;AAGO,SAAS,WAAW,KAAK;IAC9B,OAAO,uHAAA,CAAA,UAAM,CAAC,GAAG,CAAC,CAAC,qCAAqC,EAAE,MAAM,CAAC,CAAC;AACpE;AAGO,SAAS;IACd,OAAO,uHAAA,CAAA,UAAM,CAAC,GAAG,CAAC;AACpB;AAOO,SAAS,UAAU,OAAO;IAC/B,OAAO,uHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,gDAAgD;AACrE;AAGO,SAAS,UAAU,KAAK,EAAE,OAAO;IACtC,OAAO,uHAAA,CAAA,UAAM,CAAC,GAAG,CAAC,CAAC,qCAAqC,EAAE,MAAM,CAAC,CAAC,EAAE;AACtE;AAGO,SAAS,UAAU,KAAK;IAC7B,OAAO,uHAAA,CAAA,UAAM,CAAC,MAAM,CAAC,CAAC,qCAAqC,EAAE,MAAM,CAAC,CAAC;AACvE;AAGO,SAAS,mBAAmB,KAAK;IACtC,OAAO,uHAAA,CAAA,UAAM,CAAC,GAAG,CAAC,CAAC,qCAAqC,EAAE,MAAM,cAAc,CAAC;AACjF;AAGO,SAAS;IACd,OAAO,uHAAA,CAAA,UAAM,CAAC,GAAG,CAAC;AACpB;AAGO,SAAS,cAAc,SAAS,CAAC,CAAC;IACvC,MAAM,cAAc,IAAI;IAExB,4BAA4B;IAC5B,IAAI,OAAO,IAAI,EAAE,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI;IACvD,IAAI,OAAO,QAAQ,EAAE,YAAY,MAAM,CAAC,YAAY,OAAO,QAAQ;IAEnE,2BAA2B;IAC3B,IAAI,OAAO,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;IAC7D,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,KAAK,OAAO,YAAY,MAAM,CAAC,YAAY,OAAO,IAAI;IACpF,IAAI,OAAO,MAAM,EAAE,YAAY,MAAM,CAAC,cAAc,OAAO,MAAM;IACjE,IAAI,OAAO,MAAM,EAAE,YAAY,MAAM,CAAC,cAAc,OAAO,MAAM;IACjE,IAAI,OAAO,UAAU,EAAE,YAAY,MAAM,CAAC,eAAe,OAAO,UAAU;IAC1E,IAAI,OAAO,UAAU,EAAE,YAAY,MAAM,CAAC,eAAe,OAAO,UAAU;IAC1E,IAAI,OAAO,QAAQ,EAAE,YAAY,MAAM,CAAC,YAAY,OAAO,QAAQ;IACnE,IAAI,OAAO,YAAY,KAAK,WAAW,YAAY,MAAM,CAAC,gBAAgB,OAAO,YAAY;IAE7F,wBAAwB;IACxB,IAAI,OAAO,UAAU,EAAE,YAAY,MAAM,CAAC,cAAc,OAAO,UAAU;IACzE,IAAI,OAAO,YAAY,EAAE,YAAY,MAAM,CAAC,gBAAgB,OAAO,YAAY;IAE/E,MAAM,cAAc,YAAY,QAAQ;IACxC,MAAM,MAAM,CAAC,2CAA2C,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;IAEhG,QAAQ,GAAG,CAAC,iCAAiC,KAAK,gBAAgB;IAElE,OAAO,uHAAA,CAAA,UAAM,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,CAAA;QAC1B,QAAQ,GAAG,CAAC,8BAA8B;YACxC,QAAQ,SAAS,MAAM;YACvB,WAAW,SAAS,IAAI,EAAE,YAAY,eAAe;YACrD,aAAa,SAAS,IAAI,EAAE,YAAY,gBAAgB;YACxD,YAAY,SAAS,IAAI,EAAE,YAAY,eAAe;QACxD;QACA,OAAO;IACT,GAAG,KAAK,CAAC,CAAA;QACP,QAAQ,KAAK,CAAC,2BAA2B;QACzC,QAAQ,KAAK,CAAC,oCAAoC,MAAM,QAAQ,EAAE;QAClE,MAAM;IACR;AACF;AAGO,SAAS,iBAAiB,KAAK;IACpC,OAAO,uHAAA,CAAA,UAAM,CAAC,KAAK,CAAC,CAAC,aAAa,EAAE,MAAM,gBAAgB,CAAC;AAC7D", "debugId": null}}, {"offset": {"line": 428, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/utils/profileValidation.js"], "sourcesContent": ["// Profile validation utilities for job applications\r\n\r\nexport const REQUIRED_PROFILE_FIELDS = {\r\n  basic: {\r\n    first_name: 'First Name',\r\n    last_name: 'Last Name',\r\n    email: 'Email Address',\r\n    phone: 'Phone Number',\r\n    date_of_birth: 'Date of Birth'\r\n  },\r\n  academic: {\r\n    student_id: 'Student ID/Roll Number',\r\n    branch: 'Department/Branch',\r\n    gpa: 'CGPA/GPA',\r\n    joining_year: 'Joining Year',\r\n    passout_year: 'Passout Year'\r\n  },\r\n  contact: {\r\n    address: 'Address',\r\n    city: 'City',\r\n    state: 'State',\r\n    pincode: 'PIN Code'\r\n  },\r\n  documents: {\r\n    resume: 'Resume',\r\n    tenth_certificate: 'Class 10 Certificate',\r\n    twelfth_certificate: 'Class 12 Certificate'\r\n  },\r\n  education: {\r\n    tenth_percentage: 'Class 10 Percentage',\r\n    twelfth_percentage: 'Class 12 Percentage',\r\n    tenth_year_of_passing: 'Class 10 Year of Passing',\r\n    twelfth_year_of_passing: 'Class 12 Year of Passing'\r\n  }\r\n};\r\n\r\nexport const CRITICAL_FIELDS = [\r\n  'first_name',\r\n  'last_name', \r\n  'email',\r\n  'phone',\r\n  'student_id',\r\n  'branch',\r\n  'gpa',\r\n  'resume'\r\n];\r\n\r\nexport const validateProfile = (profile) => {\r\n  const missing = [];\r\n  const warnings = [];\r\n  const errors = [];\r\n\r\n  if (!profile) {\r\n    return {\r\n      isValid: false,\r\n      missing: ['Profile not found'],\r\n      warnings: [],\r\n      errors: ['Please complete your profile before applying'],\r\n      score: 0\r\n    };\r\n  }\r\n\r\n  // Check critical fields\r\n  CRITICAL_FIELDS.forEach(field => {\r\n    const value = profile[field];\r\n    const fieldName = getFieldDisplayName(field);\r\n    \r\n    if (!value || (typeof value === 'string' && value.trim() === '')) {\r\n      missing.push(fieldName);\r\n    }\r\n  });\r\n\r\n  // Check GPA/CGPA format and range\r\n  if (profile.gpa) {\r\n    const gpaValue = parseFloat(profile.gpa);\r\n    if (isNaN(gpaValue) || gpaValue < 0 || gpaValue > 10) {\r\n      errors.push('CGPA must be between 0 and 10');\r\n    } else if (gpaValue < 6.0) {\r\n      warnings.push('CGPA below 6.0 may limit job opportunities');\r\n    }\r\n  }\r\n\r\n  // Check phone number format\r\n  if (profile.phone) {\r\n    const phoneRegex = /^[6-9]\\d{9}$/;\r\n    if (!phoneRegex.test(profile.phone.replace(/[^\\d]/g, ''))) {\r\n      errors.push('Phone number must be a valid 10-digit Indian mobile number');\r\n    }\r\n  }\r\n\r\n  // Check email format\r\n  if (profile.email) {\r\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n    if (!emailRegex.test(profile.email)) {\r\n      errors.push('Please provide a valid email address');\r\n    }\r\n  }\r\n\r\n  // Check resume\r\n  if (!profile.resume && !profile.resume_url) {\r\n    missing.push('Resume');\r\n    errors.push('Resume is required for job applications');\r\n  }\r\n\r\n  // Check academic details\r\n  if (profile.tenth_percentage && (profile.tenth_percentage < 60)) {\r\n    warnings.push('Class 10 percentage below 60% may limit opportunities');\r\n  }\r\n\r\n  if (profile.twelfth_percentage && (profile.twelfth_percentage < 60)) {\r\n    warnings.push('Class 12 percentage below 60% may limit opportunities');\r\n  }\r\n\r\n  // Calculate completeness score\r\n  const totalFields = Object.keys(REQUIRED_PROFILE_FIELDS).reduce(\r\n    (acc, section) => acc + Object.keys(REQUIRED_PROFILE_FIELDS[section]).length, \r\n    0\r\n  );\r\n  \r\n  const filledFields = totalFields - missing.length;\r\n  const score = Math.round((filledFields / totalFields) * 100);\r\n\r\n  // Determine if profile is valid for job applications\r\n  const isValid = missing.length === 0 && errors.length === 0;\r\n  const canApply = CRITICAL_FIELDS.every(field => {\r\n    const value = profile[field];\r\n    return value && (typeof value !== 'string' || value.trim() !== '');\r\n  });\r\n\r\n  return {\r\n    isValid,\r\n    canApply,\r\n    missing,\r\n    warnings,\r\n    errors,\r\n    score,\r\n    summary: generateSummary(score, missing.length, warnings.length, errors.length)\r\n  };\r\n};\r\n\r\nexport const validateForJobApplication = (profile, jobRequirements = {}) => {\r\n  const baseValidation = validateProfile(profile);\r\n  \r\n  // Additional job-specific validations\r\n  const jobErrors = [];\r\n  const jobWarnings = [];\r\n\r\n  if (jobRequirements.minCgpa && profile.gpa) {\r\n    const gpaValue = parseFloat(profile.gpa);\r\n    if (gpaValue < jobRequirements.minCgpa) {\r\n      jobErrors.push(`CGPA ${jobRequirements.minCgpa} or above required`);\r\n    }\r\n  }\r\n\r\n  if (jobRequirements.allowedBranches && profile.branch) {\r\n    if (!jobRequirements.allowedBranches.includes(profile.branch)) {\r\n      jobErrors.push(`This job is not open for ${profile.branch} students`);\r\n    }\r\n  }\r\n\r\n  if (jobRequirements.minTenthPercentage && profile.tenth_percentage) {\r\n    if (profile.tenth_percentage < jobRequirements.minTenthPercentage) {\r\n      jobErrors.push(`Class 10: ${jobRequirements.minTenthPercentage}% or above required`);\r\n    }\r\n  }\r\n\r\n  if (jobRequirements.minTwelfthPercentage && profile.twelfth_percentage) {\r\n    if (profile.twelfth_percentage < jobRequirements.minTwelfthPercentage) {\r\n      jobErrors.push(`Class 12: ${jobRequirements.minTwelfthPercentage}% or above required`);\r\n    }\r\n  }\r\n\r\n  return {\r\n    ...baseValidation,\r\n    jobSpecific: {\r\n      errors: jobErrors,\r\n      warnings: jobWarnings,\r\n      isEligible: jobErrors.length === 0\r\n    }\r\n  };\r\n};\r\n\r\nexport const getProfileCompletionSuggestions = (validation) => {\r\n  const suggestions = [];\r\n\r\n  if (validation.missing.includes('Resume')) {\r\n    suggestions.push({\r\n      type: 'critical',\r\n      title: 'Upload Resume',\r\n      description: 'A resume is required for all job applications',\r\n      action: 'upload_resume',\r\n      icon: 'upload'\r\n    });\r\n  }\r\n\r\n  if (validation.missing.some(field => ['First Name', 'Last Name', 'Email', 'Phone'].includes(field))) {\r\n    suggestions.push({\r\n      type: 'critical',\r\n      title: 'Complete Basic Information',\r\n      description: 'Fill in your personal details',\r\n      action: 'edit_basic_info',\r\n      icon: 'user'\r\n    });\r\n  }\r\n\r\n  if (validation.missing.some(field => ['CGPA/GPA', 'Department/Branch'].includes(field))) {\r\n    suggestions.push({\r\n      type: 'critical',\r\n      title: 'Add Academic Details',\r\n      description: 'Provide your academic information',\r\n      action: 'edit_academic_info',\r\n      icon: 'graduation-cap'\r\n    });\r\n  }\r\n\r\n  if (validation.warnings.some(w => w.includes('percentage'))) {\r\n    suggestions.push({\r\n      type: 'warning',\r\n      title: 'Review Academic Performance',\r\n      description: 'Low grades may limit job opportunities',\r\n      action: 'review_grades',\r\n      icon: 'alert-triangle'\r\n    });\r\n  }\r\n\r\n  if (validation.score < 80) {\r\n    suggestions.push({\r\n      type: 'info',\r\n      title: 'Complete Profile',\r\n      description: `Profile is ${validation.score}% complete. More complete profiles get better job matches.`,\r\n      action: 'complete_profile',\r\n      icon: 'info'\r\n    });\r\n  }\r\n\r\n  return suggestions;\r\n};\r\n\r\nconst getFieldDisplayName = (field) => {\r\n  for (const section of Object.values(REQUIRED_PROFILE_FIELDS)) {\r\n    if (section[field]) {\r\n      return section[field];\r\n    }\r\n  }\r\n  return field.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase());\r\n};\r\n\r\nconst generateSummary = (score, missingCount, warningCount, errorCount) => {\r\n  if (errorCount > 0) {\r\n    return {\r\n      status: 'error',\r\n      message: `${errorCount} error(s) need to be fixed before applying`,\r\n      color: 'red'\r\n    };\r\n  }\r\n  \r\n  if (missingCount > 0) {\r\n    return {\r\n      status: 'incomplete',\r\n      message: `${missingCount} required field(s) missing`,\r\n      color: 'yellow'\r\n    };\r\n  }\r\n  \r\n  if (warningCount > 0) {\r\n    return {\r\n      status: 'warning',\r\n      message: `Profile complete with ${warningCount} warning(s)`,\r\n      color: 'orange'\r\n    };\r\n  }\r\n  \r\n  if (score >= 90) {\r\n    return {\r\n      status: 'excellent',\r\n      message: 'Profile is excellent and ready for applications',\r\n      color: 'green'\r\n    };\r\n  }\r\n  \r\n  if (score >= 80) {\r\n    return {\r\n      status: 'good',\r\n      message: 'Profile is good for most applications',\r\n      color: 'blue'\r\n    };\r\n  }\r\n  \r\n  return {\r\n    status: 'needs_improvement',\r\n    message: 'Profile needs more information for better job matching',\r\n    color: 'yellow'\r\n  };\r\n};\r\n\r\nexport default {\r\n  validateProfile,\r\n  validateForJobApplication,\r\n  getProfileCompletionSuggestions,\r\n  REQUIRED_PROFILE_FIELDS,\r\n  CRITICAL_FIELDS\r\n}; "], "names": [], "mappings": "AAAA,oDAAoD;;;;;;;;;AAE7C,MAAM,0BAA0B;IACrC,OAAO;QACL,YAAY;QACZ,WAAW;QACX,OAAO;QACP,OAAO;QACP,eAAe;IACjB;IACA,UAAU;QACR,YAAY;QACZ,QAAQ;QACR,KAAK;QACL,cAAc;QACd,cAAc;IAChB;IACA,SAAS;QACP,SAAS;QACT,MAAM;QACN,OAAO;QACP,SAAS;IACX;IACA,WAAW;QACT,QAAQ;QACR,mBAAmB;QACnB,qBAAqB;IACvB;IACA,WAAW;QACT,kBAAkB;QAClB,oBAAoB;QACpB,uBAAuB;QACvB,yBAAyB;IAC3B;AACF;AAEO,MAAM,kBAAkB;IAC7B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,kBAAkB,CAAC;IAC9B,MAAM,UAAU,EAAE;IAClB,MAAM,WAAW,EAAE;IACnB,MAAM,SAAS,EAAE;IAEjB,IAAI,CAAC,SAAS;QACZ,OAAO;YACL,SAAS;YACT,SAAS;gBAAC;aAAoB;YAC9B,UAAU,EAAE;YACZ,QAAQ;gBAAC;aAA+C;YACxD,OAAO;QACT;IACF;IAEA,wBAAwB;IACxB,gBAAgB,OAAO,CAAC,CAAA;QACtB,MAAM,QAAQ,OAAO,CAAC,MAAM;QAC5B,MAAM,YAAY,oBAAoB;QAEtC,IAAI,CAAC,SAAU,OAAO,UAAU,YAAY,MAAM,IAAI,OAAO,IAAK;YAChE,QAAQ,IAAI,CAAC;QACf;IACF;IAEA,kCAAkC;IAClC,IAAI,QAAQ,GAAG,EAAE;QACf,MAAM,WAAW,WAAW,QAAQ,GAAG;QACvC,IAAI,MAAM,aAAa,WAAW,KAAK,WAAW,IAAI;YACpD,OAAO,IAAI,CAAC;QACd,OAAO,IAAI,WAAW,KAAK;YACzB,SAAS,IAAI,CAAC;QAChB;IACF;IAEA,4BAA4B;IAC5B,IAAI,QAAQ,KAAK,EAAE;QACjB,MAAM,aAAa;QACnB,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ,KAAK,CAAC,OAAO,CAAC,UAAU,MAAM;YACzD,OAAO,IAAI,CAAC;QACd;IACF;IAEA,qBAAqB;IACrB,IAAI,QAAQ,KAAK,EAAE;QACjB,MAAM,aAAa;QACnB,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ,KAAK,GAAG;YACnC,OAAO,IAAI,CAAC;QACd;IACF;IAEA,eAAe;IACf,IAAI,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ,UAAU,EAAE;QAC1C,QAAQ,IAAI,CAAC;QACb,OAAO,IAAI,CAAC;IACd;IAEA,yBAAyB;IACzB,IAAI,QAAQ,gBAAgB,IAAK,QAAQ,gBAAgB,GAAG,IAAK;QAC/D,SAAS,IAAI,CAAC;IAChB;IAEA,IAAI,QAAQ,kBAAkB,IAAK,QAAQ,kBAAkB,GAAG,IAAK;QACnE,SAAS,IAAI,CAAC;IAChB;IAEA,+BAA+B;IAC/B,MAAM,cAAc,OAAO,IAAI,CAAC,yBAAyB,MAAM,CAC7D,CAAC,KAAK,UAAY,MAAM,OAAO,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,MAAM,EAC5E;IAGF,MAAM,eAAe,cAAc,QAAQ,MAAM;IACjD,MAAM,QAAQ,KAAK,KAAK,CAAC,AAAC,eAAe,cAAe;IAExD,qDAAqD;IACrD,MAAM,UAAU,QAAQ,MAAM,KAAK,KAAK,OAAO,MAAM,KAAK;IAC1D,MAAM,WAAW,gBAAgB,KAAK,CAAC,CAAA;QACrC,MAAM,QAAQ,OAAO,CAAC,MAAM;QAC5B,OAAO,SAAS,CAAC,OAAO,UAAU,YAAY,MAAM,IAAI,OAAO,EAAE;IACnE;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA,SAAS,gBAAgB,OAAO,QAAQ,MAAM,EAAE,SAAS,MAAM,EAAE,OAAO,MAAM;IAChF;AACF;AAEO,MAAM,4BAA4B,CAAC,SAAS,kBAAkB,CAAC,CAAC;IACrE,MAAM,iBAAiB,gBAAgB;IAEvC,sCAAsC;IACtC,MAAM,YAAY,EAAE;IACpB,MAAM,cAAc,EAAE;IAEtB,IAAI,gBAAgB,OAAO,IAAI,QAAQ,GAAG,EAAE;QAC1C,MAAM,WAAW,WAAW,QAAQ,GAAG;QACvC,IAAI,WAAW,gBAAgB,OAAO,EAAE;YACtC,UAAU,IAAI,CAAC,CAAC,KAAK,EAAE,gBAAgB,OAAO,CAAC,kBAAkB,CAAC;QACpE;IACF;IAEA,IAAI,gBAAgB,eAAe,IAAI,QAAQ,MAAM,EAAE;QACrD,IAAI,CAAC,gBAAgB,eAAe,CAAC,QAAQ,CAAC,QAAQ,MAAM,GAAG;YAC7D,UAAU,IAAI,CAAC,CAAC,yBAAyB,EAAE,QAAQ,MAAM,CAAC,SAAS,CAAC;QACtE;IACF;IAEA,IAAI,gBAAgB,kBAAkB,IAAI,QAAQ,gBAAgB,EAAE;QAClE,IAAI,QAAQ,gBAAgB,GAAG,gBAAgB,kBAAkB,EAAE;YACjE,UAAU,IAAI,CAAC,CAAC,UAAU,EAAE,gBAAgB,kBAAkB,CAAC,mBAAmB,CAAC;QACrF;IACF;IAEA,IAAI,gBAAgB,oBAAoB,IAAI,QAAQ,kBAAkB,EAAE;QACtE,IAAI,QAAQ,kBAAkB,GAAG,gBAAgB,oBAAoB,EAAE;YACrE,UAAU,IAAI,CAAC,CAAC,UAAU,EAAE,gBAAgB,oBAAoB,CAAC,mBAAmB,CAAC;QACvF;IACF;IAEA,OAAO;QACL,GAAG,cAAc;QACjB,aAAa;YACX,QAAQ;YACR,UAAU;YACV,YAAY,UAAU,MAAM,KAAK;QACnC;IACF;AACF;AAEO,MAAM,kCAAkC,CAAC;IAC9C,MAAM,cAAc,EAAE;IAEtB,IAAI,WAAW,OAAO,CAAC,QAAQ,CAAC,WAAW;QACzC,YAAY,IAAI,CAAC;YACf,MAAM;YACN,OAAO;YACP,aAAa;YACb,QAAQ;YACR,MAAM;QACR;IACF;IAEA,IAAI,WAAW,OAAO,CAAC,IAAI,CAAC,CAAA,QAAS;YAAC;YAAc;YAAa;YAAS;SAAQ,CAAC,QAAQ,CAAC,SAAS;QACnG,YAAY,IAAI,CAAC;YACf,MAAM;YACN,OAAO;YACP,aAAa;YACb,QAAQ;YACR,MAAM;QACR;IACF;IAEA,IAAI,WAAW,OAAO,CAAC,IAAI,CAAC,CAAA,QAAS;YAAC;YAAY;SAAoB,CAAC,QAAQ,CAAC,SAAS;QACvF,YAAY,IAAI,CAAC;YACf,MAAM;YACN,OAAO;YACP,aAAa;YACb,QAAQ;YACR,MAAM;QACR;IACF;IAEA,IAAI,WAAW,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,CAAC,gBAAgB;QAC3D,YAAY,IAAI,CAAC;YACf,MAAM;YACN,OAAO;YACP,aAAa;YACb,QAAQ;YACR,MAAM;QACR;IACF;IAEA,IAAI,WAAW,KAAK,GAAG,IAAI;QACzB,YAAY,IAAI,CAAC;YACf,MAAM;YACN,OAAO;YACP,aAAa,CAAC,WAAW,EAAE,WAAW,KAAK,CAAC,0DAA0D,CAAC;YACvG,QAAQ;YACR,MAAM;QACR;IACF;IAEA,OAAO;AACT;AAEA,MAAM,sBAAsB,CAAC;IAC3B,KAAK,MAAM,WAAW,OAAO,MAAM,CAAC,yBAA0B;QAC5D,IAAI,OAAO,CAAC,MAAM,EAAE;YAClB,OAAO,OAAO,CAAC,MAAM;QACvB;IACF;IACA,OAAO,MAAM,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;AACrE;AAEA,MAAM,kBAAkB,CAAC,OAAO,cAAc,cAAc;IAC1D,IAAI,aAAa,GAAG;QAClB,OAAO;YACL,QAAQ;YACR,SAAS,GAAG,WAAW,0CAA0C,CAAC;YAClE,OAAO;QACT;IACF;IAEA,IAAI,eAAe,GAAG;QACpB,OAAO;YACL,QAAQ;YACR,SAAS,GAAG,aAAa,0BAA0B,CAAC;YACpD,OAAO;QACT;IACF;IAEA,IAAI,eAAe,GAAG;QACpB,OAAO;YACL,QAAQ;YACR,SAAS,CAAC,sBAAsB,EAAE,aAAa,WAAW,CAAC;YAC3D,OAAO;QACT;IACF;IAEA,IAAI,SAAS,IAAI;QACf,OAAO;YACL,QAAQ;YACR,SAAS;YACT,OAAO;QACT;IACF;IAEA,IAAI,SAAS,IAAI;QACf,OAAO;YACL,QAAQ;YACR,SAAS;YACT,OAAO;QACT;IACF;IAEA,OAAO;QACL,QAAQ;QACR,SAAS;QACT,OAAO;IACT;AACF;uCAEe;IACb;IACA;IACA;IACA;IACA;AACF", "debugId": null}}, {"offset": {"line": 718, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/api/auth.js"], "sourcesContent": ["import client from './client';\r\n\r\n// Register new student\r\nexport function signup(data) {\r\n  return client.post('/api/auth/register/student/', data);\r\n}\r\n\r\n// Login and get tokens\r\nexport function login(data) {\r\n  return client.post('/api/auth/login/', data);\r\n}\r\n\r\n// Upload Resume\r\nexport function uploadResume(file, accessToken) {\r\n  const formData = new FormData();\r\n  formData.append('resume', file);\r\n\r\n  return client.patch('/api/auth/profile/', formData, {\r\n    headers: {\r\n      'Authorization': `Bearer ${accessToken}`,\r\n      'Content-Type': 'multipart/form-data',\r\n    }\r\n  });\r\n}\r\n\r\nexport const getAuthToken = () => {\r\n  if (typeof window !== 'undefined') {\r\n    return localStorage.getItem('access_token');\r\n  }\r\n  return null;\r\n};\r\n\r\nexport const setAuthToken = (token) => {\r\n  if (typeof window !== 'undefined') {\r\n    localStorage.setItem('access_token', token);\r\n  }\r\n};\r\n\r\nexport const removeAuthToken = () => {\r\n  if (typeof window !== 'undefined') {\r\n    localStorage.removeItem('access_token');\r\n    localStorage.removeItem('refresh_token');\r\n  }\r\n};\r\n\r\nexport const getRefreshToken = () => {\r\n  if (typeof window !== 'undefined') {\r\n    return localStorage.getItem('refresh_token');\r\n  }\r\n  return null;\r\n};\r\n\r\nexport const setRefreshToken = (token) => {\r\n  if (typeof window !== 'undefined') {\r\n    localStorage.setItem('refresh_token', token);\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAGO,SAAS,OAAO,IAAI;IACzB,OAAO,uHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,+BAA+B;AACpD;AAGO,SAAS,MAAM,IAAI;IACxB,OAAO,uHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,oBAAoB;AACzC;AAGO,SAAS,aAAa,IAAI,EAAE,WAAW;IAC5C,MAAM,WAAW,IAAI;IACrB,SAAS,MAAM,CAAC,UAAU;IAE1B,OAAO,uHAAA,CAAA,UAAM,CAAC,KAAK,CAAC,sBAAsB,UAAU;QAClD,SAAS;YACP,iBAAiB,CAAC,OAAO,EAAE,aAAa;YACxC,gBAAgB;QAClB;IACF;AACF;AAEO,MAAM,eAAe;IAC1B,wCAAmC;QACjC,OAAO,aAAa,OAAO,CAAC;IAC9B;;AAEF;AAEO,MAAM,eAAe,CAAC;IAC3B,wCAAmC;QACjC,aAAa,OAAO,CAAC,gBAAgB;IACvC;AACF;AAEO,MAAM,kBAAkB;IAC7B,wCAAmC;QACjC,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;IAC1B;AACF;AAEO,MAAM,kBAAkB;IAC7B,wCAAmC;QACjC,OAAO,aAAa,OAAO,CAAC;IAC9B;;AAEF;AAEO,MAAM,kBAAkB,CAAC;IAC9B,wCAAmC;QACjC,aAAa,OAAO,CAAC,iBAAiB;IACxC;AACF", "debugId": null}}, {"offset": {"line": 783, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/api/students.js"], "sourcesContent": ["import axios from 'axios';\r\nimport { getAuthToken } from './auth';\r\n\r\n// Set the base URL for all API requests\r\nconst API_BASE_URL = 'http://localhost:8000';\r\n\r\nconst api = axios.create({\r\n  baseURL: API_BASE_URL,\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n});\r\n\r\n// Add request interceptor to include auth token\r\napi.interceptors.request.use(\r\n  (config) => {\r\n    const token = getAuthToken();\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Add response interceptor for error handling\r\napi.interceptors.response.use(\r\n  (response) => response,\r\n  (error) => {\r\n    if (error.response?.status === 401) {\r\n      // Token expired or invalid\r\n      localStorage.removeItem('access_token');\r\n      localStorage.removeItem('refresh_token');\r\n      window.location.href = '/login';\r\n    }\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport const studentsAPI = {\r\n  // Get all students\r\n  getStudents: async (params = {}) => {\r\n    const response = await api.get('/api/accounts/students/', { params });\r\n    return response.data;\r\n  },\r\n\r\n  // Get students with statistics\r\n  getStudentsWithStats: async (params = {}) => {\r\n    try {\r\n      // First try to get students with built-in statistics\r\n      const response = await api.get('/api/accounts/students/stats/', { params });\r\n      return response.data;\r\n    } catch (error) {\r\n      // Fallback to regular students endpoint\r\n      console.log('Stats endpoint not available, using regular endpoint');\r\n      const response = await api.get('/api/accounts/students/', { params });\r\n      \r\n      // Calculate basic statistics from the response\r\n      const students = response.data.data || response.data;\r\n      if (Array.isArray(students)) {\r\n        const stats = calculateStudentStats(students, params);\r\n        return {\r\n          ...response.data,\r\n          statistics: stats\r\n        };\r\n      }\r\n      \r\n      return response.data;\r\n    }\r\n  },\r\n\r\n  // Get single student\r\n  getStudent: async (id) => {\r\n    const response = await api.get(`/api/accounts/students/${id}/`);\r\n    return response.data;\r\n  },\r\n\r\n  // Update student\r\n  updateStudent: async (id, data) => {\r\n    console.log('updateStudent called with:', { id, data });\r\n\r\n    // Check authentication\r\n    const token = getAuthToken();\r\n    console.log('Auth token available:', !!token);\r\n    if (token) {\r\n      console.log('Token preview:', token.substring(0, 20) + '...');\r\n    }\r\n\r\n    if (!token) {\r\n      throw new Error('Authentication required to update student');\r\n    }\r\n\r\n    // Clean data to ensure proper format\r\n    const cleanedData = { ...data };\r\n    \r\n    // Ensure numeric fields are properly formatted\r\n    ['joining_year', 'passout_year'].forEach(field => {\r\n      if (cleanedData[field] !== null && cleanedData[field] !== undefined) {\r\n        const num = parseInt(cleanedData[field]);\r\n        cleanedData[field] = isNaN(num) ? null : num;\r\n      }\r\n    });\r\n\r\n    // Ensure string fields are properly formatted\r\n    const stringFields = [\r\n      'first_name', 'last_name', 'student_id', 'contact_email', 'phone', 'branch', 'gpa',\r\n      'date_of_birth', 'address', 'city', 'district', 'state', 'pincode', 'country',\r\n      'parent_contact', 'education', 'skills',\r\n      'tenth_cgpa', 'tenth_percentage', 'tenth_board', 'tenth_school', 'tenth_year_of_passing', \r\n      'tenth_location', 'tenth_specialization',\r\n      'twelfth_cgpa', 'twelfth_percentage', 'twelfth_board', 'twelfth_school', 'twelfth_year_of_passing',\r\n      'twelfth_location', 'twelfth_specialization'\r\n    ];\r\n\r\n    stringFields.forEach(field => {\r\n      if (cleanedData[field] !== null && cleanedData[field] !== undefined) {\r\n        cleanedData[field] = String(cleanedData[field]).trim();\r\n      }\r\n    });\r\n\r\n    // Remove undefined values\r\n    Object.keys(cleanedData).forEach(key => {\r\n      if (cleanedData[key] === undefined) {\r\n        delete cleanedData[key];\r\n      }\r\n    });\r\n\r\n    console.log('Cleaned data being sent:', cleanedData);\r\n\r\n    // Try the ViewSet endpoint first (more RESTful)\r\n    try {\r\n      console.log('Trying ViewSet endpoint:', `/api/accounts/profiles/${id}/`);\r\n      const response = await api.patch(`/api/accounts/profiles/${id}/`, cleanedData);\r\n      console.log('ViewSet endpoint success:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('ViewSet endpoint failed:', {\r\n        status: error.response?.status,\r\n        statusText: error.response?.statusText,\r\n        data: error.response?.data,\r\n        headers: error.response?.headers,\r\n        config: error.config\r\n      });\r\n\r\n      // If ViewSet fails, try the fallback endpoint\r\n      try {\r\n        console.log('Trying fallback endpoint:', `/api/accounts/students/${id}/update/`);\r\n        const response = await api.patch(`/api/accounts/students/${id}/update/`, cleanedData);\r\n        console.log('Fallback endpoint success:', response.data);\r\n        return response.data;\r\n      } catch (updateError) {\r\n        console.error('Failed to update student via both endpoints:', {\r\n          viewSetError: {\r\n            status: error.response?.status,\r\n            data: error.response?.data\r\n          },\r\n          updateViewError: {\r\n            status: updateError.response?.status,\r\n            data: updateError.response?.data\r\n          }\r\n        });\r\n\r\n        // Throw the more specific error\r\n        const primaryError = updateError.response?.status === 400 ? updateError : error;\r\n        throw primaryError;\r\n      }\r\n    }\r\n  },\r\n\r\n  // Get current user profile\r\n  getProfile: async () => {\r\n    const token = getAuthToken();\r\n    return api.get('/api/auth/profile/', {\r\n      headers: { Authorization: `Bearer ${token}` },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Update profile information\r\n  updateProfile: async (data) => {\r\n    const token = getAuthToken();\r\n    return api.patch('/api/auth/profile/', data, {\r\n      headers: { Authorization: `Bearer ${token}` },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Upload profile image\r\n  uploadProfileImage: async (file) => {\r\n    const token = getAuthToken();\r\n    const formData = new FormData();\r\n    formData.append('image', file);\r\n\r\n    return api.post('/api/accounts/profiles/me/upload_profile_image/', formData, {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Upload resume using new Resume model\r\n  uploadResume: async (file, name = null, isPrimary = false) => {\r\n    const token = getAuthToken();\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n    if (name) {\r\n      formData.append('name', name);\r\n    }\r\n    formData.append('is_primary', isPrimary);\r\n\r\n    return api.post('/api/accounts/profiles/me/resumes/', formData, {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Admin upload resume for specific student\r\n  adminUploadResume: async (studentId, file, name = null, isPrimary = false) => {\r\n    const token = getAuthToken();\r\n    if (!token) {\r\n      throw new Error('No authentication token found');\r\n    }\r\n\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n    if (name) {\r\n      formData.append('name', name);\r\n    }\r\n    formData.append('is_primary', isPrimary);\r\n\r\n    return api.post(`/api/accounts/profiles/${studentId}/upload_resume/`, formData, {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Admin get resumes for specific student\r\n  adminGetResumes: async (studentId) => {\r\n    const token = getAuthToken();\r\n    if (!token) {\r\n      throw new Error('No authentication token found');\r\n    }\r\n\r\n    return api.get(`/api/accounts/profiles/${studentId}/resumes/`, {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n      },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Admin upload certificate for specific student\r\n  adminUploadCertificate: async (studentId, file, type) => {\r\n    const token = getAuthToken();\r\n    if (!token) {\r\n      throw new Error('No authentication token found');\r\n    }\r\n\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n    formData.append('type', type);\r\n\r\n    return api.post(`/api/accounts/profiles/${studentId}/upload_certificate/`, formData, {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Admin upload semester marksheet for specific student\r\n  adminUploadSemesterMarksheet: async (studentId, file, semester, cgpa) => {\r\n    const token = getAuthToken();\r\n    if (!token) {\r\n      throw new Error('No authentication token found');\r\n    }\r\n\r\n    const formData = new FormData();\r\n    formData.append('marksheet_file', file);\r\n    formData.append('semester', semester);\r\n    formData.append('cgpa', cgpa);\r\n\r\n    return api.post(`/api/accounts/profiles/${studentId}/upload_semester_marksheet/`, formData, {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Legacy resume upload (for backward compatibility)\r\n  uploadResumeToProfile: async (file) => {\r\n    const token = getAuthToken();\r\n    const formData = new FormData();\r\n    formData.append('resume', file);\r\n\r\n    return api.patch('/api/auth/profile/', formData, {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Get all resumes for the student\r\n  getResumes: async () => {\r\n    try {\r\n      const token = getAuthToken();\r\n      if (!token) {\r\n        console.log('No authentication token, returning empty array');\r\n        return [];\r\n      }\r\n\r\n      // Try the new resume endpoint first\r\n      const response = await api.get('/api/accounts/profiles/me/resumes/', {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n\r\n      // Ensure we're getting a proper response\r\n      if (!response.data) {\r\n        return await studentsAPI.getResumesLegacy();\r\n      }\r\n\r\n      // Handle different response formats\r\n      if (Array.isArray(response.data)) {\r\n        return response.data;\r\n      } else if (response.data && response.data.data && Array.isArray(response.data.data)) {\r\n        return response.data.data;\r\n      } else {\r\n        console.log('Response data is not an array, trying fallback. Response:', response.data);\r\n        try {\r\n          return await studentsAPI.getResumesLegacy();\r\n        } catch (fallbackError) {\r\n          console.log('Fallback also failed, returning empty array');\r\n          return [];\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.log('Resume endpoint failed, using fallback method');\r\n      try {\r\n        return await studentsAPI.getResumesLegacy();\r\n      } catch (fallbackError) {\r\n        console.log('Fallback method also failed, returning empty array');\r\n        return [];\r\n      }\r\n    }\r\n  },\r\n\r\n  // Legacy method to get resumes from profile\r\n  getResumesLegacy: async () => {\r\n    try {\r\n      const token = getAuthToken();\r\n      if (!token) {\r\n        console.log('No auth token for legacy resume fetch');\r\n        return [];\r\n      }\r\n\r\n      const profile = await studentsAPI.getProfile();\r\n\r\n      if (profile?.resume || profile?.resume_url) {\r\n        const resumeUrl = profile.resume_url || profile.resume;\r\n        if (resumeUrl && resumeUrl.trim() !== '' && resumeUrl !== 'null' && resumeUrl !== 'undefined') {\r\n          const fileName = resumeUrl.split('/').pop() || 'Resume.pdf';\r\n          return [{\r\n            id: profile.id || 1,\r\n            name: fileName,\r\n            file_url: resumeUrl,\r\n            uploaded_at: profile.updated_at || new Date().toISOString()\r\n          }];\r\n        }\r\n      }\r\n      return [];\r\n    } catch (error) {\r\n      console.log('Legacy resume fetch error:', error.message);\r\n      return [];\r\n    }\r\n  },\r\n\r\n  // Delete a specific resume\r\n  deleteResume: async (resumeId) => {\r\n    const token = getAuthToken();\r\n    try {\r\n      console.log(`Attempting to delete resume with ID: ${resumeId}`);\r\n\r\n      // Use the new Resume model endpoint\r\n      const response = await api.delete(`/api/accounts/profiles/me/resumes/${resumeId}/`, {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n      console.log('DELETE resume successful:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error deleting resume:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Legacy delete function with fallback strategies\r\n  deleteResumeLegacy: async (resumeId) => {\r\n    const token = getAuthToken();\r\n    try {\r\n      console.log(`Attempting to delete resume with ID: ${resumeId}`);\r\n\r\n      let success = false;\r\n\r\n      // Attempt different deletion strategies\r\n      const strategies = [\r\n        // Strategy 1: Standard DELETE request\r\n        async () => {\r\n          try {\r\n            const response = await api.delete(`/api/accounts/profiles/me/resumes/${resumeId}/`, {\r\n              headers: { Authorization: `Bearer ${token}` },\r\n            });\r\n            console.log('DELETE resume successful:', response.data);\r\n            return { success: true, data: response.data };\r\n          } catch (error) {\r\n            console.log(`Strategy 1 failed: ${error.message}`);\r\n            return { success: false };\r\n          }\r\n        },\r\n        \r\n        // Strategy 2: POST to remove endpoint\r\n        async () => {\r\n          try {\r\n            const response = await api.post(`/api/accounts/profiles/me/resumes/${resumeId}/remove/`, {}, {\r\n              headers: { Authorization: `Bearer ${token}` },\r\n            });\r\n            console.log('POST remove successful:', response.data);\r\n            return { success: true, data: response.data };\r\n          } catch (error) {\r\n            console.log(`Strategy 2 failed: ${error.message}`);\r\n            return { success: false };\r\n          }\r\n        },\r\n        \r\n        // Strategy 3: Patch profile with delete_resume field\r\n        async () => {\r\n          try {\r\n            const response = await api.patch('/api/auth/profile/', {\r\n              delete_resume: resumeId\r\n            }, {\r\n              headers: { Authorization: `Bearer ${token}` },\r\n            });\r\n            console.log('PATCH profile successful:', response.data);\r\n            return { success: true, data: response.data };\r\n          } catch (error) {\r\n            console.log(`Strategy 3 failed: ${error.message}`);\r\n            return { success: false };\r\n          }\r\n        },\r\n        \r\n        // Strategy 4: Reset all resumes (extreme fallback)\r\n        async () => {\r\n          try {\r\n            const response = await api.patch('/api/auth/profile/', {\r\n              reset_resumes: true\r\n            }, {\r\n              headers: { Authorization: `Bearer ${token}` },\r\n            });\r\n            console.log('Reset resumes successful:', response.data);\r\n            return { success: true, data: response.data };\r\n          } catch (error) {\r\n            console.log(`Strategy 4 failed: ${error.message}`);\r\n            return { success: false };\r\n          }\r\n        }\r\n      ];\r\n      \r\n      // Try each strategy in sequence until one succeeds\r\n      for (const strategy of strategies) {\r\n        const result = await strategy();\r\n        if (result.success) {\r\n          success = true;\r\n          break;\r\n        }\r\n      }\r\n      \r\n      // Clear any locally cached data for this resume regardless of backend success\r\n      if (typeof window !== 'undefined') {\r\n        // Clear any resume-related data from localStorage\r\n        try {\r\n          const localStorageKeys = Object.keys(localStorage);\r\n          const resumeKeys = localStorageKeys.filter(key => \r\n            key.includes('resume') || key.includes('file') || key.includes('document')\r\n          );\r\n          \r\n          if (resumeKeys.length > 0) {\r\n            console.log('Clearing resume-related localStorage items:', resumeKeys);\r\n            resumeKeys.forEach(key => localStorage.removeItem(key));\r\n          }\r\n          \r\n          // Also try to clear specific keys that might be used for caching\r\n          localStorage.removeItem('resume_cache');\r\n          localStorage.removeItem('resume_list');\r\n          localStorage.removeItem('profile_cache');\r\n          localStorage.removeItem('resume_count');\r\n          localStorage.removeItem('last_resume_update');\r\n        } catch (e) {\r\n          console.error('Error clearing localStorage:', e);\r\n        }\r\n      }\r\n      \r\n      return { success, message: success ? \"Resume deleted successfully\" : \"Resume deleted locally but server sync failed\" };\r\n    } catch (error) {\r\n      console.error('Resume deletion failed:', error.response?.status, error.message);\r\n      // For UI purposes, return a success response even if backend fails\r\n      // This allows the UI to remove the resume entry and maintain a good user experience\r\n      return { \r\n        success: true,  // Return true for UI purposes\r\n        synced: false,  // But indicate sync status\r\n        error: error.message,\r\n        status: error.response?.status,\r\n        message: \"Resume removed from display (sync with server failed)\"\r\n      };\r\n    }\r\n  },\r\n\r\n  // Upload certificate (10th or 12th)\r\n  uploadCertificate: async (file, type) => {\r\n    const token = getAuthToken();\r\n    const formData = new FormData();\r\n    formData.append('file', file);  // Backend expects 'file', not 'certificate'\r\n    formData.append('type', type);\r\n\r\n    return api.post('/api/accounts/profiles/me/upload_certificate/', formData, {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Get all certificates for the student\r\n  getCertificates: async () => {\r\n    const token = getAuthToken();\r\n    if (!token) {\r\n      throw new Error('Authentication required to fetch certificates');\r\n    }\r\n    \r\n    try {\r\n      const response = await api.get('/api/accounts/profiles/me/certificates/', {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n      \r\n      // Ensure we're getting a proper response\r\n      if (!response.data) {\r\n        console.error('Empty response when fetching certificates');\r\n        return [];\r\n      }\r\n      \r\n      // Handle different response formats\r\n      if (Array.isArray(response.data)) {\r\n        return response.data;\r\n      } else if (response.data.data && Array.isArray(response.data.data)) {\r\n        return response.data.data;\r\n      } else {\r\n        console.error('Unexpected certificate data format:', response.data);\r\n        return [];\r\n      }\r\n    } catch (error) {\r\n      console.error('Certificate fetch error:', error.response?.status, error.message);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Delete a specific certificate\r\n  deleteCertificate: async (certificateType) => {\r\n    const token = getAuthToken();\r\n    try {\r\n      console.log(`Attempting to delete certificate: ${certificateType}`);\r\n\r\n      const response = await api.delete(`/api/accounts/profiles/me/delete_certificate/${certificateType}/`, {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n      console.log('DELETE certificate successful:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error deleting certificate:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Admin delete certificate for specific student\r\n  adminDeleteCertificate: async (studentId, certificateType) => {\r\n    const token = getAuthToken();\r\n    try {\r\n      console.log(`Admin attempting to delete certificate: ${certificateType} for student: ${studentId}`);\r\n\r\n      const response = await api.delete(`/api/accounts/profiles/${studentId}/delete_certificate/${certificateType}/`, {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n      console.log('Admin DELETE certificate successful:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error deleting certificate:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Delete a specific marksheet\r\n  deleteMarksheet: async (semester) => {\r\n    const token = getAuthToken();\r\n    try {\r\n      console.log(`Attempting to delete marksheet for semester: ${semester}`);\r\n\r\n      const response = await api.delete(`/api/accounts/profiles/me/delete_marksheet/${semester}/`, {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n      console.log('DELETE marksheet successful:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error deleting marksheet:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Admin delete marksheet for specific student\r\n  adminDeleteMarksheet: async (studentId, semester) => {\r\n    const token = getAuthToken();\r\n    try {\r\n      console.log(`Admin attempting to delete marksheet for semester: ${semester} for student: ${studentId}`);\r\n\r\n      const response = await api.delete(`/api/accounts/profiles/${studentId}/delete_marksheet/${semester}/`, {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n      console.log('Admin DELETE marksheet successful:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error deleting marksheet:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Legacy delete function (keeping for backward compatibility)\r\n  deleteCertificateLegacy: async (certificateId) => {\r\n    const token = getAuthToken();\r\n    try {\r\n      console.log(`Attempting to delete certificate with ID: ${certificateId}`);\r\n\r\n      let success = false;\r\n\r\n      // Attempt different deletion strategies\r\n      const strategies = [\r\n        // Strategy 1: Standard DELETE request\r\n        async () => {\r\n          try {\r\n            const response = await api.delete(`/api/accounts/profiles/me/certificates/${certificateId}/`, {\r\n              headers: { Authorization: `Bearer ${token}` },\r\n            });\r\n            console.log('DELETE certificate successful:', response.data);\r\n            return { success: true, data: response.data };\r\n          } catch (error) {\r\n            console.log(`Strategy 1 failed: ${error.message}`);\r\n            return { success: false };\r\n          }\r\n        },\r\n\r\n        // Strategy 2: POST to remove endpoint\r\n        async () => {\r\n          try {\r\n            const response = await api.post(`/api/accounts/profiles/me/certificates/${certificateId}/remove/`, {}, {\r\n              headers: { Authorization: `Bearer ${token}` },\r\n            });\r\n            console.log('POST remove successful:', response.data);\r\n            return { success: true, data: response.data };\r\n          } catch (error) {\r\n            console.log(`Strategy 2 failed: ${error.message}`);\r\n            return { success: false };\r\n          }\r\n        },\r\n        \r\n        // Strategy 3: Patch profile with delete_certificate field\r\n        async () => {\r\n          try {\r\n            const response = await api.patch('/api/auth/profile/', {\r\n              delete_certificate: certificateId\r\n            }, {\r\n              headers: { Authorization: `Bearer ${token}` },\r\n            });\r\n            console.log('PATCH profile successful:', response.data);\r\n            return { success: true, data: response.data };\r\n          } catch (error) {\r\n            console.log(`Strategy 3 failed: ${error.message}`);\r\n            return { success: false };\r\n          }\r\n        },\r\n        \r\n        // Strategy 4: Reset all certificates (extreme fallback)\r\n        async () => {\r\n          try {\r\n            const response = await api.patch('/api/auth/profile/', {\r\n              reset_certificates: true\r\n            }, {\r\n              headers: { Authorization: `Bearer ${token}` },\r\n            });\r\n            console.log('Reset certificates successful:', response.data);\r\n            return { success: true, data: response.data };\r\n          } catch (error) {\r\n            console.log(`Strategy 4 failed: ${error.message}`);\r\n            return { success: false };\r\n          }\r\n        }\r\n      ];\r\n      \r\n      // Try each strategy in sequence until one succeeds\r\n      for (const strategy of strategies) {\r\n        const result = await strategy();\r\n        if (result.success) {\r\n          success = true;\r\n          break;\r\n        }\r\n      }\r\n      \r\n      // Clear any locally cached data for this certificate regardless of backend success\r\n      if (typeof window !== 'undefined') {\r\n        // Clear any certificate-related data from localStorage\r\n        try {\r\n          const localStorageKeys = Object.keys(localStorage);\r\n          const certificateKeys = localStorageKeys.filter(key => \r\n            key.includes('certificate') || key.includes('document') || key.includes('cert')\r\n          );\r\n          \r\n          if (certificateKeys.length > 0) {\r\n            console.log('Clearing certificate-related localStorage items:', certificateKeys);\r\n            certificateKeys.forEach(key => localStorage.removeItem(key));\r\n          }\r\n          \r\n          // Also try to clear specific keys that might be used for caching\r\n          localStorage.removeItem('certificate_cache');\r\n          localStorage.removeItem('certificate_list');\r\n          localStorage.removeItem('profile_cache');\r\n        } catch (e) {\r\n          console.error('Error clearing localStorage:', e);\r\n        }\r\n      }\r\n      \r\n      return { success, message: success ? \"Certificate deleted successfully\" : \"Certificate deleted locally but server sync failed\" };\r\n    } catch (error) {\r\n      console.error('Certificate deletion failed:', error.response?.status, error.message);\r\n      // For UI purposes, return a success response even if backend fails\r\n      // This allows the UI to remove the certificate entry and maintain a good user experience\r\n      return { \r\n        success: true,  // Return true for UI purposes\r\n        synced: false,  // But indicate sync status\r\n        error: error.message,\r\n        status: error.response?.status,\r\n        message: \"Certificate removed from display (sync with server failed)\"\r\n      };\r\n    }\r\n  },\r\n\r\n  // Get semester marksheets\r\n  getSemesterMarksheets: async () => {\r\n    const token = getAuthToken();\r\n    return api.get('/api/accounts/profiles/me/semester_marksheets/', {\r\n      headers: { Authorization: `Bearer ${token}` },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Upload semester marksheet\r\n  uploadSemesterMarksheet: async (file, semester, cgpa) => {\r\n    const token = getAuthToken();\r\n    const formData = new FormData();\r\n    formData.append('marksheet_file', file);\r\n    formData.append('semester', semester);\r\n    formData.append('cgpa', cgpa);\r\n\r\n    return api.post('/api/accounts/profiles/me/upload_semester_marksheet/', formData, {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Get current user's freeze status and restrictions\r\n  getFreezeStatus: async () => {\r\n    const token = getAuthToken();\r\n    if (!token) {\r\n      throw new Error('Authentication required to fetch freeze status');\r\n    }\r\n\r\n    try {\r\n      const response = await api.get('/api/auth/profile/', {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n\r\n      const profile = response.data;\r\n      return {\r\n        freeze_status: profile.freeze_status || 'none',\r\n        freeze_reason: profile.freeze_reason,\r\n        freeze_date: profile.freeze_date,\r\n        min_salary_requirement: profile.min_salary_requirement,\r\n        allowed_job_tiers: profile.allowed_job_tiers || [],\r\n        allowed_job_types: profile.allowed_job_types || [],\r\n        allowed_companies: profile.allowed_companies || []\r\n      };\r\n    } catch (error) {\r\n      console.error('Freeze status fetch error:', error.response?.status, error.message);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Check if student can apply to a specific job\r\n  canApplyToJob: async (jobId) => {\r\n    const token = getAuthToken();\r\n    if (!token) {\r\n      throw new Error('Authentication required to check job application eligibility');\r\n    }\r\n\r\n    try {\r\n      const response = await api.get(`/api/v1/college/default-college/jobs/${jobId}/can-apply/`, {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Job application eligibility check error:', error.response?.status, error.message);\r\n      if (error.response?.data) {\r\n        console.error('Error details:', error.response.data);\r\n      }\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get list of jobs the student has applied to\r\n  getAppliedJobs: async () => {\r\n    const token = getAuthToken();\r\n    if (!token) {\r\n      throw new Error('Authentication required to fetch applied jobs');\r\n    }\r\n\r\n    try {\r\n      const response = await api.get('/api/v1/college/default-college/jobs/applied/', {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Applied jobs fetch error:', error.response?.status, error.message);\r\n      throw error;\r\n    }\r\n  },\r\n};\r\n\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,wCAAwC;AACxC,MAAM,eAAe;AAErB,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,gDAAgD;AAChD,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,CAC1B,CAAC;IACC,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;IACzB,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,8CAA8C;AAC9C,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC3B,CAAC,WAAa,UACd,CAAC;IACC,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAClC,2BAA2B;QAC3B,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGK,MAAM,cAAc;IACzB,mBAAmB;IACnB,aAAa,OAAO,SAAS,CAAC,CAAC;QAC7B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,2BAA2B;YAAE;QAAO;QACnE,OAAO,SAAS,IAAI;IACtB;IAEA,+BAA+B;IAC/B,sBAAsB,OAAO,SAAS,CAAC,CAAC;QACtC,IAAI;YACF,qDAAqD;YACrD,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,iCAAiC;gBAAE;YAAO;YACzE,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,wCAAwC;YACxC,QAAQ,GAAG,CAAC;YACZ,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,2BAA2B;gBAAE;YAAO;YAEnE,+CAA+C;YAC/C,MAAM,WAAW,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI;YACpD,IAAI,MAAM,OAAO,CAAC,WAAW;gBAC3B,MAAM,QAAQ,sBAAsB,UAAU;gBAC9C,OAAO;oBACL,GAAG,SAAS,IAAI;oBAChB,YAAY;gBACd;YACF;YAEA,OAAO,SAAS,IAAI;QACtB;IACF;IAEA,qBAAqB;IACrB,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,iBAAiB;IACjB,eAAe,OAAO,IAAI;QACxB,QAAQ,GAAG,CAAC,8BAA8B;YAAE;YAAI;QAAK;QAErD,uBAAuB;QACvB,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,QAAQ,GAAG,CAAC,yBAAyB,CAAC,CAAC;QACvC,IAAI,OAAO;YACT,QAAQ,GAAG,CAAC,kBAAkB,MAAM,SAAS,CAAC,GAAG,MAAM;QACzD;QAEA,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,qCAAqC;QACrC,MAAM,cAAc;YAAE,GAAG,IAAI;QAAC;QAE9B,+CAA+C;QAC/C;YAAC;YAAgB;SAAe,CAAC,OAAO,CAAC,CAAA;YACvC,IAAI,WAAW,CAAC,MAAM,KAAK,QAAQ,WAAW,CAAC,MAAM,KAAK,WAAW;gBACnE,MAAM,MAAM,SAAS,WAAW,CAAC,MAAM;gBACvC,WAAW,CAAC,MAAM,GAAG,MAAM,OAAO,OAAO;YAC3C;QACF;QAEA,8CAA8C;QAC9C,MAAM,eAAe;YACnB;YAAc;YAAa;YAAc;YAAiB;YAAS;YAAU;YAC7E;YAAiB;YAAW;YAAQ;YAAY;YAAS;YAAW;YACpE;YAAkB;YAAa;YAC/B;YAAc;YAAoB;YAAe;YAAgB;YACjE;YAAkB;YAClB;YAAgB;YAAsB;YAAiB;YAAkB;YACzE;YAAoB;SACrB;QAED,aAAa,OAAO,CAAC,CAAA;YACnB,IAAI,WAAW,CAAC,MAAM,KAAK,QAAQ,WAAW,CAAC,MAAM,KAAK,WAAW;gBACnE,WAAW,CAAC,MAAM,GAAG,OAAO,WAAW,CAAC,MAAM,EAAE,IAAI;YACtD;QACF;QAEA,0BAA0B;QAC1B,OAAO,IAAI,CAAC,aAAa,OAAO,CAAC,CAAA;YAC/B,IAAI,WAAW,CAAC,IAAI,KAAK,WAAW;gBAClC,OAAO,WAAW,CAAC,IAAI;YACzB;QACF;QAEA,QAAQ,GAAG,CAAC,4BAA4B;QAExC,gDAAgD;QAChD,IAAI;YACF,QAAQ,GAAG,CAAC,4BAA4B,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC;YACvE,MAAM,WAAW,MAAM,IAAI,KAAK,CAAC,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC,EAAE;YAClE,QAAQ,GAAG,CAAC,6BAA6B,SAAS,IAAI;YACtD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;gBACxC,QAAQ,MAAM,QAAQ,EAAE;gBACxB,YAAY,MAAM,QAAQ,EAAE;gBAC5B,MAAM,MAAM,QAAQ,EAAE;gBACtB,SAAS,MAAM,QAAQ,EAAE;gBACzB,QAAQ,MAAM,MAAM;YACtB;YAEA,8CAA8C;YAC9C,IAAI;gBACF,QAAQ,GAAG,CAAC,6BAA6B,CAAC,uBAAuB,EAAE,GAAG,QAAQ,CAAC;gBAC/E,MAAM,WAAW,MAAM,IAAI,KAAK,CAAC,CAAC,uBAAuB,EAAE,GAAG,QAAQ,CAAC,EAAE;gBACzE,QAAQ,GAAG,CAAC,8BAA8B,SAAS,IAAI;gBACvD,OAAO,SAAS,IAAI;YACtB,EAAE,OAAO,aAAa;gBACpB,QAAQ,KAAK,CAAC,gDAAgD;oBAC5D,cAAc;wBACZ,QAAQ,MAAM,QAAQ,EAAE;wBACxB,MAAM,MAAM,QAAQ,EAAE;oBACxB;oBACA,iBAAiB;wBACf,QAAQ,YAAY,QAAQ,EAAE;wBAC9B,MAAM,YAAY,QAAQ,EAAE;oBAC9B;gBACF;gBAEA,gCAAgC;gBAChC,MAAM,eAAe,YAAY,QAAQ,EAAE,WAAW,MAAM,cAAc;gBAC1E,MAAM;YACR;QACF;IACF;IAEA,2BAA2B;IAC3B,YAAY;QACV,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,OAAO,IAAI,GAAG,CAAC,sBAAsB;YACnC,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAC9C,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,6BAA6B;IAC7B,eAAe,OAAO;QACpB,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,OAAO,IAAI,KAAK,CAAC,sBAAsB,MAAM;YAC3C,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAC9C,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,uBAAuB;IACvB,oBAAoB,OAAO;QACzB,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS;QAEzB,OAAO,IAAI,IAAI,CAAC,mDAAmD,UAAU;YAC3E,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,gBAAgB;YAClB;QACF,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,uCAAuC;IACvC,cAAc,OAAO,MAAM,OAAO,IAAI,EAAE,YAAY,KAAK;QACvD,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QACxB,IAAI,MAAM;YACR,SAAS,MAAM,CAAC,QAAQ;QAC1B;QACA,SAAS,MAAM,CAAC,cAAc;QAE9B,OAAO,IAAI,IAAI,CAAC,sCAAsC,UAAU;YAC9D,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,gBAAgB;YAClB;QACF,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,2CAA2C;IAC3C,mBAAmB,OAAO,WAAW,MAAM,OAAO,IAAI,EAAE,YAAY,KAAK;QACvE,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QACxB,IAAI,MAAM;YACR,SAAS,MAAM,CAAC,QAAQ;QAC1B;QACA,SAAS,MAAM,CAAC,cAAc;QAE9B,OAAO,IAAI,IAAI,CAAC,CAAC,uBAAuB,EAAE,UAAU,eAAe,CAAC,EAAE,UAAU;YAC9E,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,gBAAgB;YAClB;QACF,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,yCAAyC;IACzC,iBAAiB,OAAO;QACtB,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,IAAI,GAAG,CAAC,CAAC,uBAAuB,EAAE,UAAU,SAAS,CAAC,EAAE;YAC7D,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;YAClC;QACF,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,gDAAgD;IAChD,wBAAwB,OAAO,WAAW,MAAM;QAC9C,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QACxB,SAAS,MAAM,CAAC,QAAQ;QAExB,OAAO,IAAI,IAAI,CAAC,CAAC,uBAAuB,EAAE,UAAU,oBAAoB,CAAC,EAAE,UAAU;YACnF,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,gBAAgB;YAClB;QACF,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,uDAAuD;IACvD,8BAA8B,OAAO,WAAW,MAAM,UAAU;QAC9D,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,kBAAkB;QAClC,SAAS,MAAM,CAAC,YAAY;QAC5B,SAAS,MAAM,CAAC,QAAQ;QAExB,OAAO,IAAI,IAAI,CAAC,CAAC,uBAAuB,EAAE,UAAU,2BAA2B,CAAC,EAAE,UAAU;YAC1F,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,gBAAgB;YAClB;QACF,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,oDAAoD;IACpD,uBAAuB,OAAO;QAC5B,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,UAAU;QAE1B,OAAO,IAAI,KAAK,CAAC,sBAAsB,UAAU;YAC/C,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,gBAAgB;YAClB;QACF,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,kCAAkC;IAClC,YAAY;QACV,IAAI;YACF,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;YACzB,IAAI,CAAC,OAAO;gBACV,QAAQ,GAAG,CAAC;gBACZ,OAAO,EAAE;YACX;YAEA,oCAAoC;YACpC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,sCAAsC;gBACnE,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YAEA,yCAAyC;YACzC,IAAI,CAAC,SAAS,IAAI,EAAE;gBAClB,OAAO,MAAM,YAAY,gBAAgB;YAC3C;YAEA,oCAAoC;YACpC,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBAChC,OAAO,SAAS,IAAI;YACtB,OAAO,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,GAAG;gBACnF,OAAO,SAAS,IAAI,CAAC,IAAI;YAC3B,OAAO;gBACL,QAAQ,GAAG,CAAC,6DAA6D,SAAS,IAAI;gBACtF,IAAI;oBACF,OAAO,MAAM,YAAY,gBAAgB;gBAC3C,EAAE,OAAO,eAAe;oBACtB,QAAQ,GAAG,CAAC;oBACZ,OAAO,EAAE;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC;YACZ,IAAI;gBACF,OAAO,MAAM,YAAY,gBAAgB;YAC3C,EAAE,OAAO,eAAe;gBACtB,QAAQ,GAAG,CAAC;gBACZ,OAAO,EAAE;YACX;QACF;IACF;IAEA,4CAA4C;IAC5C,kBAAkB;QAChB,IAAI;YACF,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;YACzB,IAAI,CAAC,OAAO;gBACV,QAAQ,GAAG,CAAC;gBACZ,OAAO,EAAE;YACX;YAEA,MAAM,UAAU,MAAM,YAAY,UAAU;YAE5C,IAAI,SAAS,UAAU,SAAS,YAAY;gBAC1C,MAAM,YAAY,QAAQ,UAAU,IAAI,QAAQ,MAAM;gBACtD,IAAI,aAAa,UAAU,IAAI,OAAO,MAAM,cAAc,UAAU,cAAc,aAAa;oBAC7F,MAAM,WAAW,UAAU,KAAK,CAAC,KAAK,GAAG,MAAM;oBAC/C,OAAO;wBAAC;4BACN,IAAI,QAAQ,EAAE,IAAI;4BAClB,MAAM;4BACN,UAAU;4BACV,aAAa,QAAQ,UAAU,IAAI,IAAI,OAAO,WAAW;wBAC3D;qBAAE;gBACJ;YACF;YACA,OAAO,EAAE;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,8BAA8B,MAAM,OAAO;YACvD,OAAO,EAAE;QACX;IACF;IAEA,2BAA2B;IAC3B,cAAc,OAAO;QACnB,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,UAAU;YAE9D,oCAAoC;YACpC,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,kCAAkC,EAAE,SAAS,CAAC,CAAC,EAAE;gBAClF,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YACA,QAAQ,GAAG,CAAC,6BAA6B,SAAS,IAAI;YACtD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA,kDAAkD;IAClD,oBAAoB,OAAO;QACzB,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,UAAU;YAE9D,IAAI,UAAU;YAEd,wCAAwC;YACxC,MAAM,aAAa;gBACjB,sCAAsC;gBACtC;oBACE,IAAI;wBACF,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,kCAAkC,EAAE,SAAS,CAAC,CAAC,EAAE;4BAClF,SAAS;gCAAE,eAAe,CAAC,OAAO,EAAE,OAAO;4BAAC;wBAC9C;wBACA,QAAQ,GAAG,CAAC,6BAA6B,SAAS,IAAI;wBACtD,OAAO;4BAAE,SAAS;4BAAM,MAAM,SAAS,IAAI;wBAAC;oBAC9C,EAAE,OAAO,OAAO;wBACd,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,OAAO,EAAE;wBACjD,OAAO;4BAAE,SAAS;wBAAM;oBAC1B;gBACF;gBAEA,sCAAsC;gBACtC;oBACE,IAAI;wBACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,kCAAkC,EAAE,SAAS,QAAQ,CAAC,EAAE,CAAC,GAAG;4BAC3F,SAAS;gCAAE,eAAe,CAAC,OAAO,EAAE,OAAO;4BAAC;wBAC9C;wBACA,QAAQ,GAAG,CAAC,2BAA2B,SAAS,IAAI;wBACpD,OAAO;4BAAE,SAAS;4BAAM,MAAM,SAAS,IAAI;wBAAC;oBAC9C,EAAE,OAAO,OAAO;wBACd,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,OAAO,EAAE;wBACjD,OAAO;4BAAE,SAAS;wBAAM;oBAC1B;gBACF;gBAEA,qDAAqD;gBACrD;oBACE,IAAI;wBACF,MAAM,WAAW,MAAM,IAAI,KAAK,CAAC,sBAAsB;4BACrD,eAAe;wBACjB,GAAG;4BACD,SAAS;gCAAE,eAAe,CAAC,OAAO,EAAE,OAAO;4BAAC;wBAC9C;wBACA,QAAQ,GAAG,CAAC,6BAA6B,SAAS,IAAI;wBACtD,OAAO;4BAAE,SAAS;4BAAM,MAAM,SAAS,IAAI;wBAAC;oBAC9C,EAAE,OAAO,OAAO;wBACd,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,OAAO,EAAE;wBACjD,OAAO;4BAAE,SAAS;wBAAM;oBAC1B;gBACF;gBAEA,mDAAmD;gBACnD;oBACE,IAAI;wBACF,MAAM,WAAW,MAAM,IAAI,KAAK,CAAC,sBAAsB;4BACrD,eAAe;wBACjB,GAAG;4BACD,SAAS;gCAAE,eAAe,CAAC,OAAO,EAAE,OAAO;4BAAC;wBAC9C;wBACA,QAAQ,GAAG,CAAC,6BAA6B,SAAS,IAAI;wBACtD,OAAO;4BAAE,SAAS;4BAAM,MAAM,SAAS,IAAI;wBAAC;oBAC9C,EAAE,OAAO,OAAO;wBACd,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,OAAO,EAAE;wBACjD,OAAO;4BAAE,SAAS;wBAAM;oBAC1B;gBACF;aACD;YAED,mDAAmD;YACnD,KAAK,MAAM,YAAY,WAAY;gBACjC,MAAM,SAAS,MAAM;gBACrB,IAAI,OAAO,OAAO,EAAE;oBAClB,UAAU;oBACV;gBACF;YACF;YAEA,8EAA8E;YAC9E,wCAAmC;gBACjC,kDAAkD;gBAClD,IAAI;oBACF,MAAM,mBAAmB,OAAO,IAAI,CAAC;oBACrC,MAAM,aAAa,iBAAiB,MAAM,CAAC,CAAA,MACzC,IAAI,QAAQ,CAAC,aAAa,IAAI,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC;oBAGjE,IAAI,WAAW,MAAM,GAAG,GAAG;wBACzB,QAAQ,GAAG,CAAC,+CAA+C;wBAC3D,WAAW,OAAO,CAAC,CAAA,MAAO,aAAa,UAAU,CAAC;oBACpD;oBAEA,iEAAiE;oBACjE,aAAa,UAAU,CAAC;oBACxB,aAAa,UAAU,CAAC;oBACxB,aAAa,UAAU,CAAC;oBACxB,aAAa,UAAU,CAAC;oBACxB,aAAa,UAAU,CAAC;gBAC1B,EAAE,OAAO,GAAG;oBACV,QAAQ,KAAK,CAAC,gCAAgC;gBAChD;YACF;YAEA,OAAO;gBAAE;gBAAS,SAAS,UAAU,gCAAgC;YAAgD;QACvH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;YAC9E,mEAAmE;YACnE,oFAAoF;YACpF,OAAO;gBACL,SAAS;gBACT,QAAQ;gBACR,OAAO,MAAM,OAAO;gBACpB,QAAQ,MAAM,QAAQ,EAAE;gBACxB,SAAS;YACX;QACF;IACF;IAEA,oCAAoC;IACpC,mBAAmB,OAAO,MAAM;QAC9B,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ,OAAQ,4CAA4C;QAC5E,SAAS,MAAM,CAAC,QAAQ;QAExB,OAAO,IAAI,IAAI,CAAC,iDAAiD,UAAU;YACzE,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,gBAAgB;YAClB;QACF,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,uCAAuC;IACvC,iBAAiB;QACf,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,2CAA2C;gBACxE,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YAEA,yCAAyC;YACzC,IAAI,CAAC,SAAS,IAAI,EAAE;gBAClB,QAAQ,KAAK,CAAC;gBACd,OAAO,EAAE;YACX;YAEA,oCAAoC;YACpC,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBAChC,OAAO,SAAS,IAAI;YACtB,OAAO,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,GAAG;gBAClE,OAAO,SAAS,IAAI,CAAC,IAAI;YAC3B,OAAO;gBACL,QAAQ,KAAK,CAAC,uCAAuC,SAAS,IAAI;gBAClE,OAAO,EAAE;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;YAC/E,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,mBAAmB,OAAO;QACxB,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,kCAAkC,EAAE,iBAAiB;YAElE,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,6CAA6C,EAAE,gBAAgB,CAAC,CAAC,EAAE;gBACpG,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YACA,QAAQ,GAAG,CAAC,kCAAkC,SAAS,IAAI;YAC3D,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR;IACF;IAEA,gDAAgD;IAChD,wBAAwB,OAAO,WAAW;QACxC,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,gBAAgB,cAAc,EAAE,WAAW;YAElG,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,uBAAuB,EAAE,UAAU,oBAAoB,EAAE,gBAAgB,CAAC,CAAC,EAAE;gBAC9G,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YACA,QAAQ,GAAG,CAAC,wCAAwC,SAAS,IAAI;YACjE,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR;IACF;IAEA,8BAA8B;IAC9B,iBAAiB,OAAO;QACtB,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,6CAA6C,EAAE,UAAU;YAEtE,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,2CAA2C,EAAE,SAAS,CAAC,CAAC,EAAE;gBAC3F,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YACA,QAAQ,GAAG,CAAC,gCAAgC,SAAS,IAAI;YACzD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,8CAA8C;IAC9C,sBAAsB,OAAO,WAAW;QACtC,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,mDAAmD,EAAE,SAAS,cAAc,EAAE,WAAW;YAEtG,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,uBAAuB,EAAE,UAAU,kBAAkB,EAAE,SAAS,CAAC,CAAC,EAAE;gBACrG,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YACA,QAAQ,GAAG,CAAC,sCAAsC,SAAS,IAAI;YAC/D,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,8DAA8D;IAC9D,yBAAyB,OAAO;QAC9B,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,0CAA0C,EAAE,eAAe;YAExE,IAAI,UAAU;YAEd,wCAAwC;YACxC,MAAM,aAAa;gBACjB,sCAAsC;gBACtC;oBACE,IAAI;wBACF,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,uCAAuC,EAAE,cAAc,CAAC,CAAC,EAAE;4BAC5F,SAAS;gCAAE,eAAe,CAAC,OAAO,EAAE,OAAO;4BAAC;wBAC9C;wBACA,QAAQ,GAAG,CAAC,kCAAkC,SAAS,IAAI;wBAC3D,OAAO;4BAAE,SAAS;4BAAM,MAAM,SAAS,IAAI;wBAAC;oBAC9C,EAAE,OAAO,OAAO;wBACd,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,OAAO,EAAE;wBACjD,OAAO;4BAAE,SAAS;wBAAM;oBAC1B;gBACF;gBAEA,sCAAsC;gBACtC;oBACE,IAAI;wBACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,uCAAuC,EAAE,cAAc,QAAQ,CAAC,EAAE,CAAC,GAAG;4BACrG,SAAS;gCAAE,eAAe,CAAC,OAAO,EAAE,OAAO;4BAAC;wBAC9C;wBACA,QAAQ,GAAG,CAAC,2BAA2B,SAAS,IAAI;wBACpD,OAAO;4BAAE,SAAS;4BAAM,MAAM,SAAS,IAAI;wBAAC;oBAC9C,EAAE,OAAO,OAAO;wBACd,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,OAAO,EAAE;wBACjD,OAAO;4BAAE,SAAS;wBAAM;oBAC1B;gBACF;gBAEA,0DAA0D;gBAC1D;oBACE,IAAI;wBACF,MAAM,WAAW,MAAM,IAAI,KAAK,CAAC,sBAAsB;4BACrD,oBAAoB;wBACtB,GAAG;4BACD,SAAS;gCAAE,eAAe,CAAC,OAAO,EAAE,OAAO;4BAAC;wBAC9C;wBACA,QAAQ,GAAG,CAAC,6BAA6B,SAAS,IAAI;wBACtD,OAAO;4BAAE,SAAS;4BAAM,MAAM,SAAS,IAAI;wBAAC;oBAC9C,EAAE,OAAO,OAAO;wBACd,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,OAAO,EAAE;wBACjD,OAAO;4BAAE,SAAS;wBAAM;oBAC1B;gBACF;gBAEA,wDAAwD;gBACxD;oBACE,IAAI;wBACF,MAAM,WAAW,MAAM,IAAI,KAAK,CAAC,sBAAsB;4BACrD,oBAAoB;wBACtB,GAAG;4BACD,SAAS;gCAAE,eAAe,CAAC,OAAO,EAAE,OAAO;4BAAC;wBAC9C;wBACA,QAAQ,GAAG,CAAC,kCAAkC,SAAS,IAAI;wBAC3D,OAAO;4BAAE,SAAS;4BAAM,MAAM,SAAS,IAAI;wBAAC;oBAC9C,EAAE,OAAO,OAAO;wBACd,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,OAAO,EAAE;wBACjD,OAAO;4BAAE,SAAS;wBAAM;oBAC1B;gBACF;aACD;YAED,mDAAmD;YACnD,KAAK,MAAM,YAAY,WAAY;gBACjC,MAAM,SAAS,MAAM;gBACrB,IAAI,OAAO,OAAO,EAAE;oBAClB,UAAU;oBACV;gBACF;YACF;YAEA,mFAAmF;YACnF,wCAAmC;gBACjC,uDAAuD;gBACvD,IAAI;oBACF,MAAM,mBAAmB,OAAO,IAAI,CAAC;oBACrC,MAAM,kBAAkB,iBAAiB,MAAM,CAAC,CAAA,MAC9C,IAAI,QAAQ,CAAC,kBAAkB,IAAI,QAAQ,CAAC,eAAe,IAAI,QAAQ,CAAC;oBAG1E,IAAI,gBAAgB,MAAM,GAAG,GAAG;wBAC9B,QAAQ,GAAG,CAAC,oDAAoD;wBAChE,gBAAgB,OAAO,CAAC,CAAA,MAAO,aAAa,UAAU,CAAC;oBACzD;oBAEA,iEAAiE;oBACjE,aAAa,UAAU,CAAC;oBACxB,aAAa,UAAU,CAAC;oBACxB,aAAa,UAAU,CAAC;gBAC1B,EAAE,OAAO,GAAG;oBACV,QAAQ,KAAK,CAAC,gCAAgC;gBAChD;YACF;YAEA,OAAO;gBAAE;gBAAS,SAAS,UAAU,qCAAqC;YAAqD;QACjI,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;YACnF,mEAAmE;YACnE,yFAAyF;YACzF,OAAO;gBACL,SAAS;gBACT,QAAQ;gBACR,OAAO,MAAM,OAAO;gBACpB,QAAQ,MAAM,QAAQ,EAAE;gBACxB,SAAS;YACX;QACF;IACF;IAEA,0BAA0B;IAC1B,uBAAuB;QACrB,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,OAAO,IAAI,GAAG,CAAC,kDAAkD;YAC/D,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAC9C,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,4BAA4B;IAC5B,yBAAyB,OAAO,MAAM,UAAU;QAC9C,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,kBAAkB;QAClC,SAAS,MAAM,CAAC,YAAY;QAC5B,SAAS,MAAM,CAAC,QAAQ;QAExB,OAAO,IAAI,IAAI,CAAC,wDAAwD,UAAU;YAChF,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,gBAAgB;YAClB;QACF,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,oDAAoD;IACpD,iBAAiB;QACf,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,sBAAsB;gBACnD,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YAEA,MAAM,UAAU,SAAS,IAAI;YAC7B,OAAO;gBACL,eAAe,QAAQ,aAAa,IAAI;gBACxC,eAAe,QAAQ,aAAa;gBACpC,aAAa,QAAQ,WAAW;gBAChC,wBAAwB,QAAQ,sBAAsB;gBACtD,mBAAmB,QAAQ,iBAAiB,IAAI,EAAE;gBAClD,mBAAmB,QAAQ,iBAAiB,IAAI,EAAE;gBAClD,mBAAmB,QAAQ,iBAAiB,IAAI,EAAE;YACpD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;YACjF,MAAM;QACR;IACF;IAEA,+CAA+C;IAC/C,eAAe,OAAO;QACpB,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,qCAAqC,EAAE,MAAM,WAAW,CAAC,EAAE;gBACzF,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;YAC/F,IAAI,MAAM,QAAQ,EAAE,MAAM;gBACxB,QAAQ,KAAK,CAAC,kBAAkB,MAAM,QAAQ,CAAC,IAAI;YACrD;YACA,MAAM;QACR;IACF;IAEA,8CAA8C;IAC9C,gBAAgB;QACd,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,iDAAiD;gBAC9E,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;YAChF,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 1665, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/app/jobpostings/%5Bid%5D/apply/page.jsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { useRouter, useParams } from 'next/navigation';\r\nimport { ArrowLeft, Clock, MapPin, Building2, Calendar, DollarSign, CheckCircle, AlertCircle, Users, FileText, Briefcase, Award } from 'lucide-react';\r\nimport { applyToJob } from '../../../../api/jobs';\r\nimport client from '../../../../api/client';\r\nimport { useNotification } from '../../../../contexts/NotificationContext';\r\nimport { validateForJobApplication } from '../../../../utils/profileValidation';\r\nimport { studentsAPI } from '../../../../api/students';\r\n\r\n// --- Reusable UI Components ---\r\n\r\n// A reusable component for each section card in the form and review pages\r\nconst SectionCard = ({ title, onEdit, children, icon }) => (\r\n  <div className=\"bg-white p-4 sm:p-6 rounded-xl shadow-sm border border-gray-200/80 mb-6\">\r\n    <div className=\"flex justify-between items-center mb-4\">\r\n      <div className=\"flex items-center\">\r\n        {icon && <span className=\"mr-2 text-lg\">{icon}</span>}\r\n        <h3 className=\"text-lg font-semibold text-gray-800\">{title}</h3>\r\n      </div>\r\n      {onEdit && (\r\n        <button onClick={onEdit} className=\"text-sm font-medium text-indigo-600 hover:text-indigo-800 transition-colors duration-200\">\r\n          Edit\r\n        </button>\r\n      )}\r\n    </div>\r\n    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4\">\r\n      {children}\r\n    </div>\r\n  </div>\r\n);\r\n\r\n// A reusable input field component\r\nconst InputField = ({ label, type = 'text', placeholder, name, value, onChange, isFullWidth = false, required = false }) => (\r\n  <div className={isFullWidth ? 'md:col-span-2' : ''}>\r\n    <label htmlFor={name} className=\"block text-sm font-medium text-gray-700 mb-1.5\">\r\n      {label} {required && <span className=\"text-red-500\">*</span>}\r\n    </label>\r\n    <input\r\n      type={type}\r\n      name={name}\r\n      id={name}\r\n      placeholder={placeholder}\r\n      value={value}\r\n      onChange={onChange}\r\n      required={required}\r\n      className=\"w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 transition-shadow duration-200\"\r\n    />\r\n  </div>\r\n);\r\n\r\n// A reusable select field component\r\nconst SelectField = ({ label, name, value, onChange, children, required = false }) => (\r\n  <div>\r\n    <label htmlFor={name} className=\"block text-sm font-medium text-gray-700 mb-1.5\">\r\n      {label} {required && <span className=\"text-red-500\">*</span>}\r\n    </label>\r\n    <select \r\n      id={name} \r\n      name={name} \r\n      value={value}\r\n      onChange={onChange}\r\n      required={required}\r\n      className=\"w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 transition-shadow duration-200\"\r\n    >\r\n      {children}\r\n    </select>\r\n  </div>\r\n);\r\n\r\n// A reusable textarea component\r\nconst TextareaField = ({ label, name, value, onChange, placeholder, rows = 6, tip, required = false }) => (\r\n  <div className=\"md:col-span-2\">\r\n    <label htmlFor={name} className=\"block text-sm font-medium text-gray-700 mb-1.5\">\r\n      {label} {required && <span className=\"text-red-500\">*</span>}\r\n    </label>\r\n    <textarea\r\n      id={name}\r\n      name={name}\r\n      rows={rows}\r\n      value={value}\r\n      onChange={onChange}\r\n      required={required}\r\n      className=\"w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 transition-shadow duration-200\"\r\n      placeholder={placeholder}\r\n    ></textarea>\r\n    {tip && <p className=\"mt-2 text-xs text-gray-500\">{tip}</p>}\r\n  </div>\r\n);\r\n\r\n// A reusable file input component with preview\r\nconst FileInput = ({ label, name, fileName, onChange, required = false }) => (\r\n  <div className=\"md:col-span-2\">\r\n    <label className=\"block text-sm font-medium text-gray-700 mb-1.5\">\r\n      {label} {required && <span className=\"text-red-500\">*</span>}\r\n    </label>\r\n    <div className=\"mt-1 flex items-center justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg hover:border-indigo-400 transition-colors duration-200\">\r\n      <div className=\"space-y-1 text-center\">\r\n        <svg className=\"mx-auto h-12 w-12 text-gray-400\" stroke=\"currentColor\" fill=\"none\" viewBox=\"0 0 48 48\" aria-hidden=\"true\">\r\n          <path d=\"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8\" strokeWidth={2} strokeLinecap=\"round\" strokeLinejoin=\"round\" />\r\n        </svg>\r\n        <div className=\"flex text-sm text-gray-600\">\r\n          <label htmlFor={name} className=\"relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500 focus-within:outline-none\">\r\n            <span>Upload a file</span>\r\n            <input id={name} name={name} type=\"file\" className=\"sr-only\" onChange={onChange} required={required} />\r\n          </label>\r\n          <p className=\"pl-1\">or drag and drop</p>\r\n        </div>\r\n        {fileName ? (\r\n          <p className=\"text-sm font-semibold text-green-600\">{fileName}</p>\r\n        ) : (\r\n          <p className=\"text-xs text-gray-500\">PDF, DOCX, PNG, JPG up to 10MB</p>\r\n        )}\r\n      </div>\r\n    </div>\r\n  </div>\r\n);\r\n\r\n// --- Job Details Preview Component ---\r\nconst JobDetailsPreview = ({ job }) => {\r\n  if (!job) return null;\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-xl shadow-sm border border-gray-200/80 p-6 mb-6\">\r\n      <div className=\"mb-6\">\r\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">{job.title}</h2>\r\n        <div className=\"flex items-center text-lg text-gray-600 mb-2\">\r\n          <Building2 className=\"w-5 h-5 mr-2\" />\r\n          <span>{job.company_name}</span>\r\n        </div>\r\n        <div className=\"flex items-center text-sm text-indigo-600 font-medium\">\r\n          <Briefcase className=\"w-4 h-4 mr-1\" />\r\n          <span>{job.job_type || 'FULL TIME'}</span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Job Summary Grid */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6 pb-6 border-b border-gray-200\">\r\n        <div className=\"flex items-center gap-3\">\r\n          <DollarSign className=\"w-5 h-5 text-indigo-600\" />\r\n          <div>\r\n            <p className=\"text-sm font-medium text-gray-900\">Salary</p>\r\n            <p className=\"text-sm text-gray-600\">\r\n              {job.salary_min && job.salary_max\r\n                ? `$${job.salary_min} - $${job.salary_max}`\r\n                : \"Competitive salary\"}\r\n            </p>\r\n          </div>\r\n        </div>\r\n        \r\n        <div className=\"flex items-center gap-3\">\r\n          <MapPin className=\"w-5 h-5 text-indigo-600\" />\r\n          <div>\r\n            <p className=\"text-sm font-medium text-gray-900\">Location</p>\r\n            <p className=\"text-sm text-gray-600\">{job.location}</p>\r\n          </div>\r\n        </div>\r\n        \r\n        <div className=\"flex items-center gap-3\">\r\n          <Calendar className=\"w-5 h-5 text-indigo-600\" />\r\n          <div>\r\n            <p className=\"text-sm font-medium text-gray-900\">Deadline</p>\r\n            <p className=\"text-sm text-gray-600\">\r\n              {job.application_deadline \r\n                ? new Date(job.application_deadline).toLocaleDateString()\r\n                : \"Not specified\"}\r\n            </p>\r\n          </div>\r\n        </div>\r\n        \r\n        <div className=\"flex items-center gap-3\">\r\n          <Clock className=\"w-5 h-5 text-indigo-600\" />\r\n          <div>\r\n            <p className=\"text-sm font-medium text-gray-900\">Duration</p>\r\n            <p className=\"text-sm text-gray-600\">{job.duration || \"Not specified\"}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Job Description */}\r\n      <div className=\"mb-6\">\r\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">Job Description</h3>\r\n        <p className=\"text-gray-700 leading-relaxed\">{job.description}</p>\r\n      </div>\r\n\r\n      {/* Requirements */}\r\n      {job.requirements && job.requirements.length > 0 && (\r\n        <div className=\"mb-6\">\r\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">Requirements</h3>\r\n          <div className=\"flex flex-wrap gap-2\">\r\n            {job.requirements.map((req, index) => (\r\n              <span key={index} className=\"bg-gray-100 border border-gray-200 rounded-lg px-3 py-1 text-sm font-medium text-gray-700\">\r\n                {req}\r\n              </span>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Additional Fields Preview */}\r\n      {job.additional_fields && job.additional_fields.length > 0 && (\r\n        <div className=\"mb-6\">\r\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-3 flex items-center\">\r\n            <FileText className=\"w-5 h-5 mr-2 text-indigo-600\" />\r\n            Additional Information Required\r\n          </h3>\r\n          <div className=\"bg-gray-50 rounded-lg p-4\">\r\n            <p className=\"text-sm text-gray-600 mb-3\">\r\n              You'll need to provide the following information when applying:\r\n            </p>\r\n            <div className=\"space-y-2\">\r\n              {job.additional_fields.map((field, index) => (\r\n                <div key={index} className=\"flex items-center gap-2\">\r\n                  <div className=\"w-2 h-2 bg-indigo-600 rounded-full\"></div>\r\n                  <span className=\"text-sm text-gray-700\">\r\n                    {field.label}\r\n                    {field.required && <span className=\"text-red-500 ml-1\">*</span>}\r\n                  </span>\r\n                  <span className=\"text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded\">\r\n                    {field.type}\r\n                  </span>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Interview Process */}\r\n      {job.interview_rounds && job.interview_rounds.length > 0 && (\r\n        <div className=\"mb-6\">\r\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-3 flex items-center\">\r\n            <Users className=\"w-5 h-5 mr-2 text-indigo-600\" />\r\n            Interview Process\r\n          </h3>\r\n          <div className=\"space-y-3\">\r\n            {job.interview_rounds.map((round, index) => (\r\n              <div key={index} className=\"flex items-center gap-4\">\r\n                <div className=\"w-8 h-8 bg-indigo-600 text-white rounded-full flex items-center justify-center text-sm font-semibold flex-shrink-0\">\r\n                  {index + 1}\r\n                </div>\r\n                <div className=\"flex-1\">\r\n                  <h4 className=\"font-medium text-gray-900\">{round.name}</h4>\r\n                  <p className=\"text-sm text-gray-600\">{round.description}</p>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Eligibility Requirements */}\r\n      {(job.min_cgpa || job.allowed_branches || job.min_tenth_percentage || job.min_twelfth_percentage) && (\r\n        <div className=\"mb-6\">\r\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-3 flex items-center\">\r\n            <Award className=\"w-5 h-5 mr-2 text-indigo-600\" />\r\n            Eligibility Requirements\r\n          </h3>\r\n          <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n              {job.min_cgpa && (\r\n                <div className=\"flex items-center gap-2\">\r\n                  <CheckCircle className=\"w-4 h-4 text-blue-600\" />\r\n                  <span className=\"text-sm text-gray-700\">Minimum CGPA: {job.min_cgpa}</span>\r\n                </div>\r\n              )}\r\n              {job.allowed_branches && job.allowed_branches.length > 0 && (\r\n                <div className=\"flex items-center gap-2\">\r\n                  <CheckCircle className=\"w-4 h-4 text-blue-600\" />\r\n                  <span className=\"text-sm text-gray-700\">Branches: {job.allowed_branches.join(', ')}</span>\r\n                </div>\r\n              )}\r\n              {job.min_tenth_percentage && (\r\n                <div className=\"flex items-center gap-2\">\r\n                  <CheckCircle className=\"w-4 h-4 text-blue-600\" />\r\n                  <span className=\"text-sm text-gray-700\">10th Percentage: {job.min_tenth_percentage}%</span>\r\n                </div>\r\n              )}\r\n              {job.min_twelfth_percentage && (\r\n                <div className=\"flex items-center gap-2\">\r\n                  <CheckCircle className=\"w-4 h-4 text-blue-600\" />\r\n                  <span className=\"text-sm text-gray-700\">12th Percentage: {job.min_twelfth_percentage}%</span>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Application Tips */}\r\n      <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\r\n        <div className=\"flex items-start gap-3\">\r\n          <AlertCircle className=\"w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0\" />\r\n          <div>\r\n            <h4 className=\"font-medium text-yellow-800 mb-1\">Application Tips</h4>\r\n            <ul className=\"text-sm text-yellow-700 space-y-1\">\r\n              <li>• Ensure your profile is complete with all required information</li>\r\n              <li>• Prepare a compelling cover letter highlighting relevant experience</li>\r\n              <li>• Have your resume and any required documents ready</li>\r\n              <li>• Review the job requirements and eligibility criteria carefully</li>\r\n            </ul>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n// --- Form View Component ---\r\nconst ApplicationForm = ({ job, formData, setFormData, setStep, canApply = true }) => {\r\n  const handleChange = (e) => {\r\n    const { name, value, type, files } = e.target;\r\n    if (type === 'file') {\r\n      setFormData(prev => ({ \r\n        ...prev, \r\n        additional_fields: {\r\n          ...prev.additional_fields,\r\n          [name]: files[0]\r\n        }\r\n      }));\r\n    } else {\r\n      setFormData(prev => ({ ...prev, [name]: value }));\r\n    }\r\n  };\r\n\r\n  const handleAdditionalFieldChange = (fieldId, value) => {\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      additional_fields: {\r\n        ...prev.additional_fields,\r\n        [fieldId]: value\r\n      }\r\n    }));\r\n  };\r\n\r\n  const renderAdditionalField = (field) => {\r\n    const fieldId = `field_${field.id}`;\r\n    const value = formData.additional_fields[fieldId] || '';\r\n\r\n    switch (field.type) {\r\n      case 'text':\r\n        return (\r\n          <InputField\r\n            key={field.id}\r\n            label={field.label}\r\n            name={fieldId}\r\n            value={value}\r\n            onChange={(e) => handleAdditionalFieldChange(fieldId, e.target.value)}\r\n            placeholder={`Enter ${field.label.toLowerCase()}`}\r\n            required={field.required}\r\n          />\r\n        );\r\n      \r\n      case 'number':\r\n        return (\r\n          <InputField\r\n            key={field.id}\r\n            label={field.label}\r\n            type=\"number\"\r\n            name={fieldId}\r\n            value={value}\r\n            onChange={(e) => handleAdditionalFieldChange(fieldId, e.target.value)}\r\n            placeholder={`Enter ${field.label.toLowerCase()}`}\r\n            required={field.required}\r\n          />\r\n        );\r\n      \r\n      case 'file':\r\n        return (\r\n          <FileInput\r\n            key={field.id}\r\n            label={field.label}\r\n            name={fieldId}\r\n            fileName={value instanceof File ? value.name : ''}\r\n            onChange={(e) => {\r\n              const file = e.target.files[0];\r\n              handleAdditionalFieldChange(fieldId, file);\r\n            }}\r\n            required={field.required}\r\n          />\r\n        );\r\n      \r\n      case 'multiple_choice':\r\n        return (\r\n          <SelectField\r\n            key={field.id}\r\n            label={field.label}\r\n            name={fieldId}\r\n            value={value}\r\n            onChange={(e) => handleAdditionalFieldChange(fieldId, e.target.value)}\r\n            required={field.required}\r\n          >\r\n            <option value=\"\">Select {field.label.toLowerCase()}</option>\r\n            {field.options?.map((option, index) => (\r\n              <option key={index} value={option}>\r\n                {option}\r\n              </option>\r\n            ))}\r\n          </SelectField>\r\n        );\r\n      \r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  const handleSubmit = (e) => {\r\n    e.preventDefault();\r\n    setStep('review');\r\n  };\r\n\r\n  return (\r\n    <form onSubmit={handleSubmit}>\r\n      <SectionCard title=\"📝 Cover Letter\" icon=\"📝\">\r\n        <TextareaField \r\n          label=\"Cover Letter\"\r\n          name=\"cover_letter\"\r\n          value={formData.cover_letter}\r\n          onChange={handleChange}\r\n          placeholder=\"Dear Hiring Manager, I am writing to express my interest in this position...\"\r\n          tip=\"Tip: Mention specific skills from the job requirements and explain how your experience aligns with the role.\"\r\n          required={true}\r\n        />\r\n      </SectionCard>\r\n\r\n      {job.additional_fields && job.additional_fields.length > 0 && (\r\n        <SectionCard title=\"📋 Additional Information\" icon=\"📋\">\r\n          <div className=\"md:col-span-2 space-y-4\">\r\n            {job.additional_fields.map(field => renderAdditionalField(field))}\r\n          </div>\r\n        </SectionCard>\r\n      )}\r\n\r\n      <div className=\"mt-8 flex justify-end\">\r\n        <button\r\n          type=\"submit\"\r\n          disabled={!canApply}\r\n          className={`w-full sm:w-auto px-6 py-3 font-semibold rounded-lg shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 ${\r\n            canApply\r\n              ? 'bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500'\r\n              : 'bg-gray-300 text-gray-500 cursor-not-allowed'\r\n          }`}\r\n        >\r\n          {canApply ? 'Review Application' : 'Application Restricted'}\r\n        </button>\r\n      </div>\r\n    </form>\r\n  );\r\n};\r\n\r\n// --- Review View Component ---\r\nconst ReviewApplication = ({ job, formData, setStep, onSubmit, isSubmitting, canApply = true }) => {\r\n  \r\n  // A reusable row for displaying a piece of data\r\n  const DataRow = ({ label, value }) => (\r\n    <div className=\"py-3 sm:grid sm:grid-cols-3 sm:gap-4\">\r\n      <dt className=\"text-sm font-medium text-gray-500\">{label}</dt>\r\n      <dd className=\"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2 break-words\">{value || 'Not provided'}</dd>\r\n    </div>\r\n  );\r\n  \r\n  const TextData = ({ label, value }) => (\r\n    <div className=\"py-3\">\r\n      <dt className=\"text-sm font-medium text-gray-500 mb-2\">{label}</dt>\r\n      <dd className=\"text-sm text-gray-800 p-4 bg-gray-50 rounded-lg whitespace-pre-wrap\">{value || 'Not provided'}</dd>\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <div>\r\n      <SectionCard title=\"📝 Cover Letter\" onEdit={() => setStep('form')} icon=\"📝\">\r\n        <div className=\"md:col-span-2\">\r\n          <TextData label=\"Cover Letter\" value={formData.cover_letter} />\r\n        </div>\r\n      </SectionCard>\r\n      \r\n      {job.additional_fields && job.additional_fields.length > 0 && (\r\n        <SectionCard title=\"📋 Additional Information\" onEdit={() => setStep('form')} icon=\"📋\">\r\n          <div className=\"md:col-span-2 divide-y divide-gray-200\">\r\n            {job.additional_fields.map(field => {\r\n              const fieldId = `field_${field.id}`;\r\n              const value = formData.additional_fields[fieldId];\r\n              return (\r\n                <DataRow \r\n                  key={field.id}\r\n                  label={field.label} \r\n                  value={field.type === 'file' && value instanceof File ? value.name : value} \r\n                />\r\n              );\r\n            })}\r\n          </div>\r\n        </SectionCard>\r\n      )}\r\n      \r\n      <div className=\"mt-8 flex justify-between\">\r\n        <button \r\n          type=\"button\"\r\n          onClick={() => setStep('form')}\r\n          className=\"px-6 py-3 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200\"\r\n        >\r\n          Back to Edit\r\n        </button>\r\n        <button\r\n          type=\"button\"\r\n          onClick={onSubmit}\r\n          disabled={isSubmitting || !canApply}\r\n          className={`px-8 py-3 font-semibold rounded-lg shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 disabled:opacity-50 ${\r\n            canApply\r\n              ? 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500'\r\n              : 'bg-gray-300 text-gray-500 cursor-not-allowed'\r\n          }`}\r\n        >\r\n          {isSubmitting ? \"Submitting...\" : canApply ? \"Submit Application\" : \"Application Restricted\"}\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n// --- Main Component ---\r\nexport default function JobApplicationPage() {\r\n  const router = useRouter();\r\n  const params = useParams();\r\n  const jobId = params.id;\r\n  const { showApplicationSubmissionError, showSuccess, handleApiError, showProfileIncompleteModal } = useNotification();\r\n\r\n  const [job, setJob] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [step, setStep] = useState('form'); // 'form' or 'review'\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [userProfile, setUserProfile] = useState(null);\r\n  const [profileValidation, setProfileValidation] = useState(null);\r\n  const [freezeStatus, setFreezeStatus] = useState(null);\r\n  const [canApply, setCanApply] = useState(true);\r\n  const [freezeRestrictions, setFreezeRestrictions] = useState([]);\r\n\r\n  // Form data state\r\n  const [formData, setFormData] = useState({\r\n    cover_letter: '',\r\n    additional_fields: {}\r\n  });\r\n\r\n  // Fetch job details\r\n  useEffect(() => {\r\n    const fetchJobDetails = async () => {\r\n      try {\r\n        setLoading(true);\r\n        const response = await client.get(`/api/v1/college/default-college/jobs/${jobId}/`);\r\n        setJob(response.data);\r\n        setError(null);\r\n      } catch (err) {\r\n        console.error('Failed to fetch job details:', err);\r\n        handleApiError(err, 'loading job details');\r\n        setError('Failed to load job details. Please try again.');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    if (jobId) {\r\n      fetchJobDetails();\r\n      fetchUserProfile();\r\n      checkJobApplicationEligibility();\r\n    }\r\n  }, [jobId]);\r\n\r\n  // Fetch user profile for validation\r\n  const fetchUserProfile = async () => {\r\n    try {\r\n      const token = localStorage.getItem('access_token');\r\n      if (!token) return;\r\n\r\n      const response = await client.get('/api/auth/profile/');\r\n      const profile = response.data;\r\n      setUserProfile(profile);\r\n\r\n      // Validate profile for job application\r\n      const jobRequirements = job ? {\r\n        minCgpa: job.min_cgpa,\r\n        allowedBranches: job.allowed_branches,\r\n        minTenthPercentage: job.min_tenth_percentage,\r\n        minTwelfthPercentage: job.min_twelfth_percentage\r\n      } : {};\r\n\r\n      const validation = validateForJobApplication(profile, jobRequirements);\r\n      setProfileValidation(validation);\r\n    } catch (err) {\r\n      console.error('Failed to fetch user profile:', err);\r\n    }\r\n  };\r\n\r\n  // Check freeze status and job application eligibility\r\n  const checkJobApplicationEligibility = async () => {\r\n    if (!jobId) return;\r\n\r\n    try {\r\n      const eligibilityResponse = await studentsAPI.canApplyToJob(jobId);\r\n      setCanApply(eligibilityResponse.can_apply);\r\n\r\n      if (!eligibilityResponse.can_apply) {\r\n        setFreezeStatus({\r\n          status: eligibilityResponse.freeze_status,\r\n          reason: eligibilityResponse.reason,\r\n          freeze_reason: eligibilityResponse.freeze_reason,\r\n          restrictions: eligibilityResponse.restrictions || []\r\n        });\r\n        setFreezeRestrictions(eligibilityResponse.restrictions || []);\r\n      }\r\n    } catch (err) {\r\n      console.error('Failed to check job application eligibility:', err);\r\n\r\n      // Handle specific error cases\r\n      if (err.response?.status === 400 && err.response?.data?.reason) {\r\n        // User doesn't have student profile or other validation error\r\n        setCanApply(false);\r\n        setFreezeStatus({\r\n          status: 'error',\r\n          reason: err.response.data.reason,\r\n          freeze_reason: '',\r\n          restrictions: []\r\n        });\r\n      } else if (err.response?.status === 500) {\r\n        // Server error - show error message but allow application\r\n        setCanApply(true);\r\n        console.error('Server error checking eligibility:', err.response?.data);\r\n      } else {\r\n        // Other errors - allow application but log the error\r\n        setCanApply(true);\r\n      }\r\n    }\r\n  };\r\n\r\n  // Re-validate when job data changes\r\n  useEffect(() => {\r\n    if (userProfile && job) {\r\n      const jobRequirements = {\r\n        minCgpa: job.min_cgpa,\r\n        allowedBranches: job.allowed_branches,\r\n        minTenthPercentage: job.min_tenth_percentage,\r\n        minTwelfthPercentage: job.min_twelfth_percentage\r\n      };\r\n\r\n      const validation = validateForJobApplication(userProfile, jobRequirements);\r\n      setProfileValidation(validation);\r\n    }\r\n  }, [userProfile, job]);\r\n\r\n  const handleSubmit = async () => {\r\n    // Check profile validation first\r\n    if (profileValidation && !profileValidation.canApply) {\r\n      const missingFields = profileValidation.missing.filter(field => \r\n        ['Resume', 'First Name', 'Last Name', 'Email', 'Student ID/Roll Number'].includes(field)\r\n      );\r\n      showProfileIncompleteModal(missingFields);\r\n      return;\r\n    }\r\n\r\n    // Check job-specific eligibility\r\n    if (profileValidation?.jobSpecific && !profileValidation.jobSpecific.isEligible) {\r\n      showApplicationSubmissionError({\r\n        response: {\r\n          data: {\r\n            eligibility: profileValidation.jobSpecific.errors\r\n          }\r\n        }\r\n      });\r\n      return;\r\n    }\r\n    \r\n    if (!formData.cover_letter.trim()) {\r\n      showApplicationSubmissionError({\r\n        response: {\r\n          data: {\r\n            cover_letter: ['Cover letter is required.']\r\n          }\r\n        }\r\n      });\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n\r\n    try {\r\n      await applyToJob(jobId, formData.cover_letter, formData.additional_fields);\r\n      \r\n      // Success - show success notification and redirect\r\n      showSuccess('Application Submitted!', 'Your job application has been submitted successfully. Good luck!');\r\n      \r\n      // Redirect after a short delay to let user see the success message\r\n      setTimeout(() => {\r\n        router.push('/jobpostings');\r\n      }, 2000);\r\n      \r\n    } catch (err) {\r\n      console.error('Failed to submit application:', err);\r\n      showApplicationSubmissionError(err);\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <div className=\"animate-spin rounded-full h-16 w-16 border-b-2 border-indigo-600 mx-auto\"></div>\r\n          <p className=\"mt-4 text-gray-600\">Loading job details...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <p className=\"text-red-600 mb-4\">{error}</p>\r\n          <button\r\n            onClick={() => router.push('/jobpostings')}\r\n            className=\"px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700\"\r\n          >\r\n            Back to Jobs\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (!job) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <p className=\"text-gray-600 mb-4\">Job not found</p>\r\n          <button\r\n            onClick={() => router.push('/jobpostings')}\r\n            className=\"px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700\"\r\n          >\r\n            Back to Jobs\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"bg-gray-50 font-sans\">\r\n      <div className=\"container mx-auto max-w-4xl p-4 sm:p-6 lg:p-8\">\r\n        \r\n        {/* Header */}\r\n        <header className=\"mb-8\">\r\n          <div className=\"flex items-center mb-4\">\r\n            <button\r\n              onClick={() => router.push('/jobpostings')}\r\n              className=\"flex items-center text-gray-600 hover:text-gray-900 mr-4\"\r\n            >\r\n              <ArrowLeft className=\"w-5 h-5 mr-2\" />\r\n              Back to Jobs\r\n            </button>\r\n          </div>\r\n          \r\n          <div className=\"bg-white rounded-xl shadow-sm border border-gray-200/80 p-6 mb-6\">\r\n            <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between mb-4\">\r\n              <div>\r\n                <h1 className=\"text-2xl sm:text-3xl font-bold text-gray-900 mb-2\">{job.title}</h1>\r\n                <div className=\"flex items-center text-lg text-gray-600 mb-2\">\r\n                  <Building2 className=\"w-5 h-5 mr-2\" />\r\n                  <span>{job.company_name}</span>\r\n                </div>\r\n              </div>\r\n              \r\n              {/* Application Status */}\r\n              <div className=\"flex items-center space-x-2 mt-4 lg:mt-0\">\r\n                {[1, 2, 3].map((stepNumber) => (\r\n                  <div key={stepNumber} className=\"flex items-center\">\r\n                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${\r\n                      (step === 'form' && stepNumber === 1) || (step === 'review' && stepNumber <= 2)\r\n                        ? 'bg-indigo-600 text-white' \r\n                        : 'bg-gray-200 text-gray-600'\r\n                    }`}>\r\n                      {stepNumber}\r\n                    </div>\r\n                    {stepNumber < 3 && (\r\n                      <div className={`w-8 h-0.5 mx-2 ${\r\n                        (step === 'review' && stepNumber < 2) ? 'bg-indigo-600' : 'bg-gray-200'\r\n                      }`} />\r\n                    )}\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n            \r\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n              <div className=\"flex items-center text-sm text-gray-600\">\r\n                <MapPin className=\"w-4 h-4 mr-2\" />\r\n                <span>{job.location}</span>\r\n              </div>\r\n              <div className=\"flex items-center text-sm text-gray-600\">\r\n                <DollarSign className=\"w-4 h-4 mr-2\" />\r\n                <span>\r\n                  {job.salary_min && job.salary_max\r\n                    ? `$${job.salary_min} - $${job.salary_max}`\r\n                    : \"Competitive salary\"}\r\n                </span>\r\n              </div>\r\n              <div className=\"flex items-center text-sm text-gray-600\">\r\n                <Calendar className=\"w-4 h-4 mr-2\" />\r\n                <span>\r\n                  Deadline: {job.application_deadline \r\n                    ? new Date(job.application_deadline).toLocaleDateString()\r\n                    : \"Not specified\"}\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </header>\r\n\r\n        {/* Job Details Preview */}\r\n        <JobDetailsPreview job={job} />\r\n\r\n        {/* Freeze Restriction Notice */}\r\n        {!canApply && freezeStatus && (\r\n          <div className=\"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\">\r\n            <div className=\"flex items-start\">\r\n              <AlertCircle className=\"w-5 h-5 text-red-600 mt-0.5 mr-3 flex-shrink-0\" />\r\n              <div className=\"flex-1\">\r\n                <h3 className=\"text-red-800 font-semibold mb-2\">\r\n                  {freezeStatus.status === 'complete' ? 'Account Completely Frozen' : 'Account Partially Restricted'}\r\n                </h3>\r\n                <p className=\"text-red-700 mb-3\">\r\n                  {freezeStatus.reason}\r\n                </p>\r\n                {freezeStatus.freeze_reason && (\r\n                  <div className=\"bg-red-100 rounded-md p-3 mb-3\">\r\n                    <p className=\"text-red-800 font-medium text-sm\">\r\n                      Admin Reason: {freezeStatus.freeze_reason}\r\n                    </p>\r\n                  </div>\r\n                )}\r\n                {freezeRestrictions.length > 0 && (\r\n                  <div>\r\n                    <p className=\"text-red-700 font-medium text-sm mb-2\">Specific restrictions for this job:</p>\r\n                    <ul className=\"list-disc list-inside text-red-600 text-sm space-y-1\">\r\n                      {freezeRestrictions.map((restriction, index) => (\r\n                        <li key={index}>{restriction}</li>\r\n                      ))}\r\n                    </ul>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Main Content: Switches between Form and Review */}\r\n        <main>\r\n          {step === 'form' ? (\r\n            <ApplicationForm\r\n              job={job}\r\n              formData={formData}\r\n              setFormData={setFormData}\r\n              setStep={setStep}\r\n              canApply={canApply}\r\n            />\r\n          ) : (\r\n            <ReviewApplication\r\n              job={job}\r\n              formData={formData}\r\n              setStep={setStep}\r\n              onSubmit={handleSubmit}\r\n              isSubmitting={isSubmitting}\r\n              canApply={canApply}\r\n            />\r\n          )}\r\n        </main>\r\n        \r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAWA,iCAAiC;AAEjC,0EAA0E;AAC1E,MAAM,cAAc,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,iBACpD,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;4BACZ,sBAAQ,6LAAC;gCAAK,WAAU;0CAAgB;;;;;;0CACzC,6LAAC;gCAAG,WAAU;0CAAuC;;;;;;;;;;;;oBAEtD,wBACC,6LAAC;wBAAO,SAAS;wBAAQ,WAAU;kCAA2F;;;;;;;;;;;;0BAKlI,6LAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;KAdD;AAmBN,mCAAmC;AACnC,MAAM,aAAa,CAAC,EAAE,KAAK,EAAE,OAAO,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,cAAc,KAAK,EAAE,WAAW,KAAK,EAAE,iBACrH,6LAAC;QAAI,WAAW,cAAc,kBAAkB;;0BAC9C,6LAAC;gBAAM,SAAS;gBAAM,WAAU;;oBAC7B;oBAAM;oBAAE,0BAAY,6LAAC;wBAAK,WAAU;kCAAe;;;;;;;;;;;;0BAEtD,6LAAC;gBACC,MAAM;gBACN,MAAM;gBACN,IAAI;gBACJ,aAAa;gBACb,OAAO;gBACP,UAAU;gBACV,UAAU;gBACV,WAAU;;;;;;;;;;;;MAbV;AAkBN,oCAAoC;AACpC,MAAM,cAAc,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,KAAK,EAAE,iBAC/E,6LAAC;;0BACC,6LAAC;gBAAM,SAAS;gBAAM,WAAU;;oBAC7B;oBAAM;oBAAE,0BAAY,6LAAC;wBAAK,WAAU;kCAAe;;;;;;;;;;;;0BAEtD,6LAAC;gBACC,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,UAAU;gBACV,UAAU;gBACV,WAAU;0BAET;;;;;;;;;;;;MAbD;AAkBN,gCAAgC;AAChC,MAAM,gBAAgB,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,CAAC,EAAE,GAAG,EAAE,WAAW,KAAK,EAAE,iBACnG,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAM,SAAS;gBAAM,WAAU;;oBAC7B;oBAAM;oBAAE,0BAAY,6LAAC;wBAAK,WAAU;kCAAe;;;;;;;;;;;;0BAEtD,6LAAC;gBACC,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP,UAAU;gBACV,UAAU;gBACV,WAAU;gBACV,aAAa;;;;;;YAEd,qBAAO,6LAAC;gBAAE,WAAU;0BAA8B;;;;;;;;;;;;MAfjD;AAmBN,+CAA+C;AAC/C,MAAM,YAAY,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,KAAK,EAAE,iBACtE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAM,WAAU;;oBACd;oBAAM;oBAAE,0BAAY,6LAAC;wBAAK,WAAU;kCAAe;;;;;;;;;;;;0BAEtD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;4BAAkC,QAAO;4BAAe,MAAK;4BAAO,SAAQ;4BAAY,eAAY;sCACjH,cAAA,6LAAC;gCAAK,GAAE;gCAA+K,aAAa;gCAAG,eAAc;gCAAQ,gBAAe;;;;;;;;;;;sCAE9O,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,SAAS;oCAAM,WAAU;;sDAC9B,6LAAC;sDAAK;;;;;;sDACN,6LAAC;4CAAM,IAAI;4CAAM,MAAM;4CAAM,MAAK;4CAAO,WAAU;4CAAU,UAAU;4CAAU,UAAU;;;;;;;;;;;;8CAE7F,6LAAC;oCAAE,WAAU;8CAAO;;;;;;;;;;;;wBAErB,yBACC,6LAAC;4BAAE,WAAU;sCAAwC;;;;;iDAErD,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;MApBzC;AA2BN,wCAAwC;AACxC,MAAM,oBAAoB,CAAC,EAAE,GAAG,EAAE;IAChC,IAAI,CAAC,KAAK,OAAO;IAEjB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyC,IAAI,KAAK;;;;;;kCAChE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,6LAAC;0CAAM,IAAI,YAAY;;;;;;;;;;;;kCAEzB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+MAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,6LAAC;0CAAM,IAAI,QAAQ,IAAI;;;;;;;;;;;;;;;;;;0BAK3B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;0CACtB,6LAAC;;kDACC,6LAAC;wCAAE,WAAU;kDAAoC;;;;;;kDACjD,6LAAC;wCAAE,WAAU;kDACV,IAAI,UAAU,IAAI,IAAI,UAAU,GAC7B,CAAC,CAAC,EAAE,IAAI,UAAU,CAAC,IAAI,EAAE,IAAI,UAAU,EAAE,GACzC;;;;;;;;;;;;;;;;;;kCAKV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;;kDACC,6LAAC;wCAAE,WAAU;kDAAoC;;;;;;kDACjD,6LAAC;wCAAE,WAAU;kDAAyB,IAAI,QAAQ;;;;;;;;;;;;;;;;;;kCAItD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;;kDACC,6LAAC;wCAAE,WAAU;kDAAoC;;;;;;kDACjD,6LAAC;wCAAE,WAAU;kDACV,IAAI,oBAAoB,GACrB,IAAI,KAAK,IAAI,oBAAoB,EAAE,kBAAkB,KACrD;;;;;;;;;;;;;;;;;;kCAKV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;;kDACC,6LAAC;wCAAE,WAAU;kDAAoC;;;;;;kDACjD,6LAAC;wCAAE,WAAU;kDAAyB,IAAI,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;;0BAM5D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAE,WAAU;kCAAiC,IAAI,WAAW;;;;;;;;;;;;YAI9D,IAAI,YAAY,IAAI,IAAI,YAAY,CAAC,MAAM,GAAG,mBAC7C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;kCACZ,IAAI,YAAY,CAAC,GAAG,CAAC,CAAC,KAAK,sBAC1B,6LAAC;gCAAiB,WAAU;0CACzB;+BADQ;;;;;;;;;;;;;;;;YASlB,IAAI,iBAAiB,IAAI,IAAI,iBAAiB,CAAC,MAAM,GAAG,mBACvD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC,iNAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAiC;;;;;;;kCAGvD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAG1C,6LAAC;gCAAI,WAAU;0CACZ,IAAI,iBAAiB,CAAC,GAAG,CAAC,CAAC,OAAO,sBACjC,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAK,WAAU;;oDACb,MAAM,KAAK;oDACX,MAAM,QAAQ,kBAAI,6LAAC;wDAAK,WAAU;kEAAoB;;;;;;;;;;;;0DAEzD,6LAAC;gDAAK,WAAU;0DACb,MAAM,IAAI;;;;;;;uCAPL;;;;;;;;;;;;;;;;;;;;;;YAiBnB,IAAI,gBAAgB,IAAI,IAAI,gBAAgB,CAAC,MAAM,GAAG,mBACrD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;4BAAiC;;;;;;;kCAGpD,6LAAC;wBAAI,WAAU;kCACZ,IAAI,gBAAgB,CAAC,GAAG,CAAC,CAAC,OAAO,sBAChC,6LAAC;gCAAgB,WAAU;;kDACzB,6LAAC;wCAAI,WAAU;kDACZ,QAAQ;;;;;;kDAEX,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA6B,MAAM,IAAI;;;;;;0DACrD,6LAAC;gDAAE,WAAU;0DAAyB,MAAM,WAAW;;;;;;;;;;;;;+BANjD;;;;;;;;;;;;;;;;YAejB,CAAC,IAAI,QAAQ,IAAI,IAAI,gBAAgB,IAAI,IAAI,oBAAoB,IAAI,IAAI,sBAAsB,mBAC9F,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;4BAAiC;;;;;;;kCAGpD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;gCACZ,IAAI,QAAQ,kBACX,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,8NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,6LAAC;4CAAK,WAAU;;gDAAwB;gDAAe,IAAI,QAAQ;;;;;;;;;;;;;gCAGtE,IAAI,gBAAgB,IAAI,IAAI,gBAAgB,CAAC,MAAM,GAAG,mBACrD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,8NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,6LAAC;4CAAK,WAAU;;gDAAwB;gDAAW,IAAI,gBAAgB,CAAC,IAAI,CAAC;;;;;;;;;;;;;gCAGhF,IAAI,oBAAoB,kBACvB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,8NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,6LAAC;4CAAK,WAAU;;gDAAwB;gDAAkB,IAAI,oBAAoB;gDAAC;;;;;;;;;;;;;gCAGtF,IAAI,sBAAsB,kBACzB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,8NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,6LAAC;4CAAK,WAAU;;gDAAwB;gDAAkB,IAAI,sBAAsB;gDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASjG,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB;MA3LM;AA6LN,8BAA8B;AAC9B,MAAM,kBAAkB,CAAC,EAAE,GAAG,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,IAAI,EAAE;IAC/E,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAC7C,IAAI,SAAS,QAAQ;YACnB,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,mBAAmB;wBACjB,GAAG,KAAK,iBAAiB;wBACzB,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE;oBAClB;gBACF,CAAC;QACH,OAAO;YACL,YAAY,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,KAAK,EAAE;gBAAM,CAAC;QACjD;IACF;IAEA,MAAM,8BAA8B,CAAC,SAAS;QAC5C,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,mBAAmB;oBACjB,GAAG,KAAK,iBAAiB;oBACzB,CAAC,QAAQ,EAAE;gBACb;YACF,CAAC;IACH;IAEA,MAAM,wBAAwB,CAAC;QAC7B,MAAM,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE;QACnC,MAAM,QAAQ,SAAS,iBAAiB,CAAC,QAAQ,IAAI;QAErD,OAAQ,MAAM,IAAI;YAChB,KAAK;gBACH,qBACE,6LAAC;oBAEC,OAAO,MAAM,KAAK;oBAClB,MAAM;oBACN,OAAO;oBACP,UAAU,CAAC,IAAM,4BAA4B,SAAS,EAAE,MAAM,CAAC,KAAK;oBACpE,aAAa,CAAC,MAAM,EAAE,MAAM,KAAK,CAAC,WAAW,IAAI;oBACjD,UAAU,MAAM,QAAQ;mBANnB,MAAM,EAAE;;;;;YAUnB,KAAK;gBACH,qBACE,6LAAC;oBAEC,OAAO,MAAM,KAAK;oBAClB,MAAK;oBACL,MAAM;oBACN,OAAO;oBACP,UAAU,CAAC,IAAM,4BAA4B,SAAS,EAAE,MAAM,CAAC,KAAK;oBACpE,aAAa,CAAC,MAAM,EAAE,MAAM,KAAK,CAAC,WAAW,IAAI;oBACjD,UAAU,MAAM,QAAQ;mBAPnB,MAAM,EAAE;;;;;YAWnB,KAAK;gBACH,qBACE,6LAAC;oBAEC,OAAO,MAAM,KAAK;oBAClB,MAAM;oBACN,UAAU,iBAAiB,OAAO,MAAM,IAAI,GAAG;oBAC/C,UAAU,CAAC;wBACT,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;wBAC9B,4BAA4B,SAAS;oBACvC;oBACA,UAAU,MAAM,QAAQ;mBARnB,MAAM,EAAE;;;;;YAYnB,KAAK;gBACH,qBACE,6LAAC;oBAEC,OAAO,MAAM,KAAK;oBAClB,MAAM;oBACN,OAAO;oBACP,UAAU,CAAC,IAAM,4BAA4B,SAAS,EAAE,MAAM,CAAC,KAAK;oBACpE,UAAU,MAAM,QAAQ;;sCAExB,6LAAC;4BAAO,OAAM;;gCAAG;gCAAQ,MAAM,KAAK,CAAC,WAAW;;;;;;;wBAC/C,MAAM,OAAO,EAAE,IAAI,CAAC,QAAQ,sBAC3B,6LAAC;gCAAmB,OAAO;0CACxB;+BADU;;;;;;mBATV,MAAM,EAAE;;;;;YAgBnB;gBACE,OAAO;QACX;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,QAAQ;IACV;IAEA,qBACE,6LAAC;QAAK,UAAU;;0BACd,6LAAC;gBAAY,OAAM;gBAAkB,MAAK;0BACxC,cAAA,6LAAC;oBACC,OAAM;oBACN,MAAK;oBACL,OAAO,SAAS,YAAY;oBAC5B,UAAU;oBACV,aAAY;oBACZ,KAAI;oBACJ,UAAU;;;;;;;;;;;YAIb,IAAI,iBAAiB,IAAI,IAAI,iBAAiB,CAAC,MAAM,GAAG,mBACvD,6LAAC;gBAAY,OAAM;gBAA4B,MAAK;0BAClD,cAAA,6LAAC;oBAAI,WAAU;8BACZ,IAAI,iBAAiB,CAAC,GAAG,CAAC,CAAA,QAAS,sBAAsB;;;;;;;;;;;0BAKhE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,MAAK;oBACL,UAAU,CAAC;oBACX,WAAW,CAAC,8IAA8I,EACxJ,WACI,uEACA,gDACJ;8BAED,WAAW,uBAAuB;;;;;;;;;;;;;;;;;AAK7C;MA3IM;AA6IN,gCAAgC;AAChC,MAAM,oBAAoB,CAAC,EAAE,GAAG,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,WAAW,IAAI,EAAE;IAE5F,gDAAgD;IAChD,MAAM,UAAU,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,iBAC/B,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAqC;;;;;;8BACnD,6LAAC;oBAAG,WAAU;8BAAgE,SAAS;;;;;;;;;;;;IAI3F,MAAM,WAAW,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,iBAChC,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAA0C;;;;;;8BACxD,6LAAC;oBAAG,WAAU;8BAAuE,SAAS;;;;;;;;;;;;IAIlG,qBACE,6LAAC;;0BACC,6LAAC;gBAAY,OAAM;gBAAkB,QAAQ,IAAM,QAAQ;gBAAS,MAAK;0BACvE,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAS,OAAM;wBAAe,OAAO,SAAS,YAAY;;;;;;;;;;;;;;;;YAI9D,IAAI,iBAAiB,IAAI,IAAI,iBAAiB,CAAC,MAAM,GAAG,mBACvD,6LAAC;gBAAY,OAAM;gBAA4B,QAAQ,IAAM,QAAQ;gBAAS,MAAK;0BACjF,cAAA,6LAAC;oBAAI,WAAU;8BACZ,IAAI,iBAAiB,CAAC,GAAG,CAAC,CAAA;wBACzB,MAAM,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE;wBACnC,MAAM,QAAQ,SAAS,iBAAiB,CAAC,QAAQ;wBACjD,qBACE,6LAAC;4BAEC,OAAO,MAAM,KAAK;4BAClB,OAAO,MAAM,IAAI,KAAK,UAAU,iBAAiB,OAAO,MAAM,IAAI,GAAG;2BAFhE,MAAM,EAAE;;;;;oBAKnB;;;;;;;;;;;0BAKN,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,MAAK;wBACL,SAAS,IAAM,QAAQ;wBACvB,WAAU;kCACX;;;;;;kCAGD,6LAAC;wBACC,MAAK;wBACL,SAAS;wBACT,UAAU,gBAAgB,CAAC;wBAC3B,WAAW,CAAC,iJAAiJ,EAC3J,WACI,oEACA,gDACJ;kCAED,eAAe,kBAAkB,WAAW,uBAAuB;;;;;;;;;;;;;;;;;;AAK9E;MAlEM;AAqES,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,QAAQ,OAAO,EAAE;IACvB,MAAM,EAAE,8BAA8B,EAAE,WAAW,EAAE,cAAc,EAAE,0BAA0B,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,kBAAe,AAAD;IAElH,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,qBAAqB;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAE/D,kBAAkB;IAClB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,cAAc;QACd,mBAAmB,CAAC;IACtB;IAEA,oBAAoB;IACpB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM;gEAAkB;oBACtB,IAAI;wBACF,WAAW;wBACX,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAM,CAAC,GAAG,CAAC,CAAC,qCAAqC,EAAE,MAAM,CAAC,CAAC;wBAClF,OAAO,SAAS,IAAI;wBACpB,SAAS;oBACX,EAAE,OAAO,KAAK;wBACZ,QAAQ,KAAK,CAAC,gCAAgC;wBAC9C,eAAe,KAAK;wBACpB,SAAS;oBACX,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA,IAAI,OAAO;gBACT;gBACA;gBACA;YACF;QACF;uCAAG;QAAC;KAAM;IAEV,oCAAoC;IACpC,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,CAAC,OAAO;YAEZ,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAM,CAAC,GAAG,CAAC;YAClC,MAAM,UAAU,SAAS,IAAI;YAC7B,eAAe;YAEf,uCAAuC;YACvC,MAAM,kBAAkB,MAAM;gBAC5B,SAAS,IAAI,QAAQ;gBACrB,iBAAiB,IAAI,gBAAgB;gBACrC,oBAAoB,IAAI,oBAAoB;gBAC5C,sBAAsB,IAAI,sBAAsB;YAClD,IAAI,CAAC;YAEL,MAAM,aAAa,CAAA,GAAA,oIAAA,CAAA,4BAAyB,AAAD,EAAE,SAAS;YACtD,qBAAqB;QACvB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,iCAAiC;QACjD;IACF;IAEA,sDAAsD;IACtD,MAAM,iCAAiC;QACrC,IAAI,CAAC,OAAO;QAEZ,IAAI;YACF,MAAM,sBAAsB,MAAM,yHAAA,CAAA,cAAW,CAAC,aAAa,CAAC;YAC5D,YAAY,oBAAoB,SAAS;YAEzC,IAAI,CAAC,oBAAoB,SAAS,EAAE;gBAClC,gBAAgB;oBACd,QAAQ,oBAAoB,aAAa;oBACzC,QAAQ,oBAAoB,MAAM;oBAClC,eAAe,oBAAoB,aAAa;oBAChD,cAAc,oBAAoB,YAAY,IAAI,EAAE;gBACtD;gBACA,sBAAsB,oBAAoB,YAAY,IAAI,EAAE;YAC9D;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,gDAAgD;YAE9D,8BAA8B;YAC9B,IAAI,IAAI,QAAQ,EAAE,WAAW,OAAO,IAAI,QAAQ,EAAE,MAAM,QAAQ;gBAC9D,8DAA8D;gBAC9D,YAAY;gBACZ,gBAAgB;oBACd,QAAQ;oBACR,QAAQ,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM;oBAChC,eAAe;oBACf,cAAc,EAAE;gBAClB;YACF,OAAO,IAAI,IAAI,QAAQ,EAAE,WAAW,KAAK;gBACvC,0DAA0D;gBAC1D,YAAY;gBACZ,QAAQ,KAAK,CAAC,sCAAsC,IAAI,QAAQ,EAAE;YACpE,OAAO;gBACL,qDAAqD;gBACrD,YAAY;YACd;QACF;IACF;IAEA,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,eAAe,KAAK;gBACtB,MAAM,kBAAkB;oBACtB,SAAS,IAAI,QAAQ;oBACrB,iBAAiB,IAAI,gBAAgB;oBACrC,oBAAoB,IAAI,oBAAoB;oBAC5C,sBAAsB,IAAI,sBAAsB;gBAClD;gBAEA,MAAM,aAAa,CAAA,GAAA,oIAAA,CAAA,4BAAyB,AAAD,EAAE,aAAa;gBAC1D,qBAAqB;YACvB;QACF;uCAAG;QAAC;QAAa;KAAI;IAErB,MAAM,eAAe;QACnB,iCAAiC;QACjC,IAAI,qBAAqB,CAAC,kBAAkB,QAAQ,EAAE;YACpD,MAAM,gBAAgB,kBAAkB,OAAO,CAAC,MAAM,CAAC,CAAA,QACrD;oBAAC;oBAAU;oBAAc;oBAAa;oBAAS;iBAAyB,CAAC,QAAQ,CAAC;YAEpF,2BAA2B;YAC3B;QACF;QAEA,iCAAiC;QACjC,IAAI,mBAAmB,eAAe,CAAC,kBAAkB,WAAW,CAAC,UAAU,EAAE;YAC/E,+BAA+B;gBAC7B,UAAU;oBACR,MAAM;wBACJ,aAAa,kBAAkB,WAAW,CAAC,MAAM;oBACnD;gBACF;YACF;YACA;QACF;QAEA,IAAI,CAAC,SAAS,YAAY,CAAC,IAAI,IAAI;YACjC,+BAA+B;gBAC7B,UAAU;oBACR,MAAM;wBACJ,cAAc;4BAAC;yBAA4B;oBAC7C;gBACF;YACF;YACA;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,MAAM,CAAA,GAAA,qHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,SAAS,YAAY,EAAE,SAAS,iBAAiB;YAEzE,mDAAmD;YACnD,YAAY,0BAA0B;YAEtC,mEAAmE;YACnE,WAAW;gBACT,OAAO,IAAI,CAAC;YACd,GAAG;QAEL,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,+BAA+B;QACjC,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,6LAAC;wBACC,SAAS,IAAM,OAAO,IAAI,CAAC;wBAC3B,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,IAAI,CAAC,KAAK;QACR,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,6LAAC;wBACC,SAAS,IAAM,OAAO,IAAI,CAAC;wBAC3B,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAGb,6LAAC;oBAAO,WAAU;;sCAChB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS,IAAM,OAAO,IAAI,CAAC;gCAC3B,WAAU;;kDAEV,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;sCAK1C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAqD,IAAI,KAAK;;;;;;8DAC5E,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,mNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;sEACrB,6LAAC;sEAAM,IAAI,YAAY;;;;;;;;;;;;;;;;;;sDAK3B,6LAAC;4CAAI,WAAU;sDACZ;gDAAC;gDAAG;gDAAG;6CAAE,CAAC,GAAG,CAAC,CAAC,2BACd,6LAAC;oDAAqB,WAAU;;sEAC9B,6LAAC;4DAAI,WAAW,CAAC,0EAA0E,EACzF,AAAC,SAAS,UAAU,eAAe,KAAO,SAAS,YAAY,cAAc,IACzE,6BACA,6BACJ;sEACC;;;;;;wDAEF,aAAa,mBACZ,6LAAC;4DAAI,WAAW,CAAC,eAAe,EAC9B,AAAC,SAAS,YAAY,aAAa,IAAK,kBAAkB,eAC1D;;;;;;;mDAXI;;;;;;;;;;;;;;;;8CAkBhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;8DAAM,IAAI,QAAQ;;;;;;;;;;;;sDAErB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,6LAAC;8DACE,IAAI,UAAU,IAAI,IAAI,UAAU,GAC7B,CAAC,CAAC,EAAE,IAAI,UAAU,CAAC,IAAI,EAAE,IAAI,UAAU,EAAE,GACzC;;;;;;;;;;;;sDAGR,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;;wDAAK;wDACO,IAAI,oBAAoB,GAC/B,IAAI,KAAK,IAAI,oBAAoB,EAAE,kBAAkB,KACrD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQd,6LAAC;oBAAkB,KAAK;;;;;;gBAGvB,CAAC,YAAY,8BACZ,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDACX,aAAa,MAAM,KAAK,aAAa,8BAA8B;;;;;;kDAEtE,6LAAC;wCAAE,WAAU;kDACV,aAAa,MAAM;;;;;;oCAErB,aAAa,aAAa,kBACzB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;;gDAAmC;gDAC/B,aAAa,aAAa;;;;;;;;;;;;oCAI9C,mBAAmB,MAAM,GAAG,mBAC3B,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAwC;;;;;;0DACrD,6LAAC;gDAAG,WAAU;0DACX,mBAAmB,GAAG,CAAC,CAAC,aAAa,sBACpC,6LAAC;kEAAgB;uDAAR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAWzB,6LAAC;8BACE,SAAS,uBACR,6LAAC;wBACC,KAAK;wBACL,UAAU;wBACV,aAAa;wBACb,SAAS;wBACT,UAAU;;;;;6CAGZ,6LAAC;wBACC,KAAK;wBACL,UAAU;wBACV,SAAS;wBACT,UAAU;wBACV,cAAc;wBACd,UAAU;;;;;;;;;;;;;;;;;;;;;;AAQxB;GAvWwB;;QACP,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;QAE4E,0IAAA,CAAA,kBAAe;;;MAJ7F", "debugId": null}}]}