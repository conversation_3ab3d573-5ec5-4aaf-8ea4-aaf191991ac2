(()=>{var e={};e.id=5328,e.ids=[5328],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35734:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\forms\\\\[id]\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\forms\\[id]\\page.jsx","default")},39646:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>d,pages:()=>c,routeModule:()=>p,tree:()=>u});var s=t(65239),a=t(48088),o=t(88170),n=t.n(o),i=t(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let u={children:["",{children:["forms",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,35734)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\forms\\[id]\\page.jsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,75535)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\layout.js"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\forms\\[id]\\page.jsx"],d={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/forms/[id]/page",pathname:"/forms/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},40828:(e,r,t)=>{Promise.resolve().then(t.bind(t,95644))},50867:(e,r,t)=>{"use strict";t.d(r,{DG:()=>o,Jy:()=>n,Uq:()=>a,i6:()=>l,wi:()=>i});var s=t(58138);function a(){return s.A.get("/api/v1/jobs/forms/")}function o(e){return s.A.post("/api/v1/jobs/forms/",e)}function n(e){return s.A.get(`/api/v1/jobs/forms/${e}/`)}function i(e,r){return s.A.patch(`/api/v1/jobs/forms/${e}/`,r)}function l(e){return s.A.post(`/api/v1/jobs/forms/${e}/delete/`)}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58138:(e,r,t)=>{"use strict";t.d(r,{A:()=>o});var s=t(51060);t(51421);let a=s.A.create({baseURL:"http://127.0.0.1:8000",headers:{"Content-Type":"application/json"}});a.interceptors.request.use(e=>{let r=localStorage.getItem("access_token");return r&&(e.headers.Authorization=`Bearer ${r}`),e},e=>Promise.reject(e)),a.interceptors.response.use(e=>e,async e=>{let r=e.config;if(e.response?.status===401&&!r._retry){r._retry=!0;try{let e=localStorage.getItem("refresh_token");if(e){let t=await s.A.post("http://127.0.0.1:8000/api/auth/token/refresh/",{refresh:e});return localStorage.setItem("access_token",t.data.access),r.headers.Authorization=`Bearer ${t.data.access}`,a(r)}}catch(e){console.error("Error refreshing token:",e)}}return Promise.reject(e)});let o=a},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},70748:(e,r,t)=>{Promise.resolve().then(t.bind(t,35734))},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},95644:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var s=t(60687),a=t(43210),o=t(16189),n=t(50867);function i(){let{id:e}=(0,o.useParams)(),r=(0,o.useRouter)(),[t,i]=(0,a.useState)(!1),[l,u]=(0,a.useState)(""),[c,d]=(0,a.useState)(null),[p,m]=(0,a.useState)({description:"",skills:"",salaryMin:"",salaryMax:"",deadline:""}),f=async()=>{try{await (0,n.wi)(e,{submitted:!0,details:p}),localStorage.removeItem(`form-access-${e}`),r.push("/thank-you")}catch(e){console.error("Error submitting form:",e),alert("Failed to submit form. Please try again.")}};return c?t?(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 p-6",children:(0,s.jsxs)("div",{className:"max-w-3xl mx-auto bg-white p-6 rounded-lg shadow",children:[(0,s.jsxs)("h2",{className:"text-2xl font-bold text-black mb-6",children:["Fill Job Details for ",c.title]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("textarea",{rows:4,placeholder:"Job Description",value:p.description,onChange:e=>m({...p,description:e.target.value}),className:"w-full border p-3 rounded focus:ring-2 text-black focus:ring-blue-500"}),(0,s.jsx)("input",{type:"text",placeholder:"Required Skills (comma-separated)",value:p.skills,onChange:e=>m({...p,skills:e.target.value}),className:"w-full border p-2 rounded focus:ring-2 text-black focus:ring-blue-500"}),(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)("input",{type:"number",placeholder:"Min Salary",value:p.salaryMin,onChange:e=>m({...p,salaryMin:e.target.value}),className:"w-1/2 border p-2 rounded focus:ring-2 text-black focus:ring-blue-500"}),(0,s.jsx)("input",{type:"number",placeholder:"Max Salary",value:p.salaryMax,onChange:e=>m({...p,salaryMax:e.target.value}),className:"w-1/2 border p-2 rounded focus:ring-2 text-black focus:ring-blue-500"})]}),(0,s.jsx)("input",{type:"date",value:p.deadline,onChange:e=>m({...p,deadline:e.target.value}),className:"w-full border p-2 rounded focus:ring-2 text-black focus:ring-blue-500"}),(0,s.jsx)("div",{className:"pt-4",children:(0,s.jsx)("button",{onClick:f,className:"w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700",children:"Submit Form"})})]})]})}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 p-6",children:(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow w-full max-w-md",children:[(0,s.jsx)("h2",{className:"text-lg font-bold mb-4 text-black",children:"Enter Access Key"}),(0,s.jsx)("input",{type:"text",value:l,onChange:e=>u(e.target.value),className:"w-full p-2 border rounded mb-4 focus:ring-2 text-black focus:ring-blue-500",placeholder:"Enter the key provided"}),(0,s.jsx)("button",{onClick:()=>{l===c?.key?(i(!0),localStorage.setItem(`form-access-${e}`,l)):alert("Invalid access key")},className:"w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700",children:"Submit Key"})]})}):(0,s.jsx)("div",{className:"p-6",children:"Form not found."})}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,681,1658,1060,2305],()=>t(39646));module.exports=s})();