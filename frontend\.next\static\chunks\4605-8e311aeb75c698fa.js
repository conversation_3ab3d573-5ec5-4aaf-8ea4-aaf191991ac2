"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1318,4605],{7045:(e,a,t)=>{t.d(a,{A:()=>c});var n=t(95155),o=t(12115),r=t(54395),l=t(22439),s=t(16701),i=t(755);function c(e){let{companies:a,onSubmit:t,onCancel:c,initialData:d={}}=e,[p,u]=(0,o.useState)({title:d.title||"",location:d.location||"",salary_min:d.salary_min||"",salary_max:d.salary_max||"",job_type:d.job_type||"FULL_TIME",description:d.description||"",required_skills:d.required_skills||"",requirements:d.requirements||[],skills:d.skills||[],benefits:d.benefits||[],application_deadline:d.application_deadline||"",duration:d.duration||"",company_id:d.company_id||"",company_name:d.company_name||""}),[m,g]=(0,o.useState)(d.interview_rounds||[{name:"",date:"",time:""}]),[b,x]=(0,o.useState)(d.additional_fields||[]),[y,h]=(0,o.useState)(!1),[f,v]=(0,o.useState)(d.company_name||""),[j,_]=(0,o.useState)(!1),[N,w]=(0,o.useState)(d.company_name||""),A=(e,a)=>{u(t=>({...t,[e]:a}))},C=e=>{let a=e.companyName||e.name;u(t=>({...t,company_id:e.id||e.companyName,company_name:a||""})),w(a),v(a),_(!1)},k=e=>{v(e),_(!0),e.trim()||(u(e=>({...e,company_id:"",company_name:""})),w(""))},S=(a||[]).filter(e=>(e.companyName||e.name||"").toLowerCase().includes(f.toLowerCase())),F=(e,a,t)=>{g(n=>n.map((n,o)=>o===e?{...n,[a]:t}:n))},T=e=>{g(a=>a.filter((a,t)=>t!==e))},E=e=>{let a={id:Date.now(),type:e,label:"",required:!1,options:"multiple_choice"===e?[""]:void 0};x(e=>[...e,a])},L=(e,a,t)=>{x(n=>n.map(n=>n.id===e?{...n,[a]:t}:n))},P=e=>{x(a=>a.filter(a=>a.id!==e))},q=e=>{x(a=>a.map(a=>a.id===e?{...a,options:[...a.options||[],""]}:a))},J=(e,a,t)=>{x(n=>n.map(n=>n.id===e?{...n,options:n.options.map((e,n)=>n===a?t:e)}:n))},I=async e=>{e.preventDefault(),h(!0);try{let e={...p,interview_rounds:m.filter(e=>e.name.trim()),additional_fields:b,salary_min:parseFloat(p.salary_min)||0,salary_max:parseFloat(p.salary_max)||0,requirements:p.requirements.filter(e=>e.trim()),skills:p.skills.filter(e=>e.trim()),benefits:p.benefits.filter(e=>e.trim())};await t(e)}catch(e){console.error("Error creating job:",e),alert("Failed to create job posting. Please try again.")}finally{h(!1)}};return(0,n.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,n.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Publish Job Posting"}),(0,n.jsx)("button",{onClick:c,className:"text-gray-500 hover:text-gray-700",children:(0,n.jsx)(r.A,{className:"w-6 h-6"})})]}),(0,n.jsxs)("form",{onSubmit:I,className:"space-y-6",children:[(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select Company *"}),(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)(l.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none w-4 h-4"}),(0,n.jsx)("input",{type:"text",value:f,onChange:e=>k(e.target.value),onFocus:()=>_(!0),placeholder:"Search for a company...",className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0}),N&&(0,n.jsx)("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2",children:(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("span",{className:"text-xs text-green-600 font-medium",children:"Selected"}),(0,n.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"})]})})]}),j&&f&&(0,n.jsx)("div",{className:"absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto",children:S.length>0?(0,n.jsx)("div",{className:"py-1",children:S.map(e=>(0,n.jsx)("button",{type:"button",onClick:()=>C(e),className:"w-full text-left px-4 py-2 hover:bg-gray-50 focus:bg-gray-50 focus:outline-none border-b border-gray-100 last:border-b-0",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"font-medium text-gray-900",children:e.companyName||e.name}),e.location&&(0,n.jsx)("div",{className:"text-sm text-gray-500",children:e.location})]}),e.totalActiveJobs>0&&(0,n.jsxs)("div",{className:"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full",children:[e.totalActiveJobs," jobs"]})]})},e.id||e.companyName))}):(0,n.jsxs)("div",{className:"px-4 py-3 text-sm text-gray-500 text-center",children:['No companies found matching "',f,'"']})}),j&&(0,n.jsx)("div",{className:"fixed inset-0 z-5",onClick:()=>_(!1)})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Job Title *"}),(0,n.jsx)("input",{type:"text",value:p.title,onChange:e=>A("title",e.target.value),placeholder:"Enter job title",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Location *"}),(0,n.jsx)("input",{type:"text",value:p.location,onChange:e=>A("location",e.target.value),placeholder:"Enter location",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Job Type *"}),(0,n.jsxs)("select",{value:p.job_type,onChange:e=>A("job_type",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,n.jsx)("option",{value:"FULL_TIME",children:"Full Time"}),(0,n.jsx)("option",{value:"PART_TIME",children:"Part Time"}),(0,n.jsx)("option",{value:"INTERNSHIP",children:"Internship"}),(0,n.jsx)("option",{value:"CONTRACT",children:"Contract"})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Min Salary"}),(0,n.jsx)("input",{type:"number",value:p.salary_min,onChange:e=>A("salary_min",e.target.value),placeholder:"Enter minimum salary",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Max Salary"}),(0,n.jsx)("input",{type:"number",value:p.salary_max,onChange:e=>A("salary_max",e.target.value),placeholder:"Enter maximum salary",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Duration"}),(0,n.jsx)("input",{type:"text",value:p.duration,onChange:e=>A("duration",e.target.value),placeholder:"e.g., 6 months, 2 years",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Application Deadline *"}),(0,n.jsx)("input",{type:"date",value:p.application_deadline,onChange:e=>A("application_deadline",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Job Description *"}),(0,n.jsx)("textarea",{value:p.description,onChange:e=>A("description",e.target.value),placeholder:"Enter job description",rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Required Skills *"}),(0,n.jsx)("textarea",{value:p.required_skills,onChange:e=>A("required_skills",e.target.value),placeholder:"Enter required skills (e.g., Python, React, SQL, etc.)",rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Interview Timeline"}),m.map((e,a)=>(0,n.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4 mb-4",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,n.jsxs)("h4",{className:"font-medium text-gray-700",children:["Round ",a+1]}),a>0&&(0,n.jsx)("button",{type:"button",onClick:()=>T(a),className:"text-red-500 hover:text-red-700",children:(0,n.jsx)(s.A,{className:"w-4 h-4"})})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3",children:[(0,n.jsx)("input",{type:"text",value:e.name,onChange:e=>F(a,"name",e.target.value),placeholder:"Enter round name",className:"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,n.jsx)("input",{type:"date",value:e.date,onChange:e=>F(a,"date",e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,n.jsx)("input",{type:"time",value:e.time,onChange:e=>F(a,"time",e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]},a)),(0,n.jsxs)("button",{type:"button",onClick:()=>{g(e=>[...e,{name:"",date:"",time:""}])},className:"flex items-center gap-2 px-4 py-2 text-blue-600 border border-blue-600 rounded-lg hover:bg-blue-50 transition-colors",children:[(0,n.jsx)(i.A,{className:"w-4 h-4"}),"Add Round"]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Additional Fields"}),b.map(e=>{var a;return(0,n.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4 mb-4",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,n.jsx)("input",{type:"text",value:e.label,onChange:a=>L(e.id,"label",a.target.value),placeholder:"Field label",className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 mr-3"}),(0,n.jsx)("button",{type:"button",onClick:()=>P(e.id),className:"text-red-500 hover:text-red-700",children:(0,n.jsx)(s.A,{className:"w-4 h-4"})})]}),"multiple_choice"===e.type&&(0,n.jsxs)("div",{className:"space-y-2",children:[null==(a=e.options)?void 0:a.map((a,t)=>(0,n.jsx)("input",{type:"text",value:a,onChange:a=>J(e.id,t,a.target.value),placeholder:"Option ".concat(t+1),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"},t)),(0,n.jsx)("button",{type:"button",onClick:()=>q(e.id),className:"text-blue-600 text-sm hover:text-blue-800",children:"+ Add Option"})]})]},e.id)}),(0,n.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,n.jsxs)("button",{type:"button",onClick:()=>E("text"),className:"flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:[(0,n.jsx)(i.A,{className:"w-4 h-4"}),"Add Text Field"]}),(0,n.jsxs)("button",{type:"button",onClick:()=>E("number"),className:"flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:[(0,n.jsx)(i.A,{className:"w-4 h-4"}),"Add Number Field"]}),(0,n.jsxs)("button",{type:"button",onClick:()=>E("file"),className:"flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:[(0,n.jsx)(i.A,{className:"w-4 h-4"}),"Add File Upload Field"]}),(0,n.jsxs)("button",{type:"button",onClick:()=>E("multiple_choice"),className:"flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:[(0,n.jsx)(i.A,{className:"w-4 h-4"}),"Add Multiple Choice Field"]})]})]}),(0,n.jsxs)("div",{className:"flex items-center justify-end gap-4 pt-6 border-t border-gray-200",children:[(0,n.jsx)("button",{type:"button",onClick:c,className:"px-6 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:"Cancel"}),(0,n.jsx)("button",{type:"submit",disabled:y,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:y?"Publishing...":"Publish Job"})]})]})]})})}t(34842)},34842:(e,a,t)=>{t.d(a,{G$:()=>s,N6:()=>o,Om:()=>d,T4:()=>p,YQ:()=>l,_S:()=>i,lh:()=>c,vr:()=>r});var n=t(37719);function o(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=new URLSearchParams;e.page&&a.append("page",e.page),e.per_page&&a.append("per_page",e.per_page),e.job_type&&"ALL"!==e.job_type&&a.append("job_type",e.job_type),e.location&&"ALL"!==e.location&&a.append("location",e.location),e.salary_min&&a.append("salary_min",e.salary_min),e.search&&a.append("search",e.search);let t=a.toString();return n.A.get("/api/v1/college/default-college/jobs/".concat(t?"?".concat(t):""))}function r(e,a){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!Object.values(t).some(e=>e instanceof File))return n.A.post("/api/v1/college/default-college/jobs/".concat(e,"/apply/"),{cover_letter:a,additional_field_responses:t});{let o=new FormData;return o.append("cover_letter",a),Object.entries(t).forEach(e=>{let[a,t]=e;t instanceof File?o.append(a,t):o.append(a,JSON.stringify(t))}),n.A.post("/api/v1/college/default-college/jobs/".concat(e,"/apply/"),o,{headers:{"Content-Type":"multipart/form-data"}})}}function l(e){return n.A.get("/api/v1/college/default-college/jobs/".concat(e,"/"))}function s(){return n.A.get("/api/v1/college/default-college/jobs/applied/")}function i(e){return n.A.post("/api/v1/college/default-college/jobs/create/",e)}function c(e,a){return n.A.put("/api/v1/college/default-college/jobs/".concat(e,"/"),a)}function d(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=new URLSearchParams;e.page&&a.append("page",e.page),e.per_page&&a.append("per_page",e.per_page),e.search&&a.append("search",e.search),e.type&&"All"!==e.type&&a.append("job_type",e.type),e.minCTC&&a.append("salary_min",e.minCTC),e.maxCTC&&a.append("salary_max",e.maxCTC),e.minStipend&&a.append("stipend_min",e.minStipend),e.maxStipend&&a.append("stipend_max",e.maxStipend),e.location&&a.append("location",e.location),void 0!==e.is_published&&a.append("is_published",e.is_published),e.company_id&&a.append("company_id",e.company_id),e.company_name&&a.append("company_name",e.company_name);let t=a.toString(),o="/api/v1/college/default-college/jobs/admin/".concat(t?"?".concat(t):"");return console.log("\uD83C\uDF10 listJobsAdmin calling URL:",o,"with params:",e),n.A.get(o).then(e=>{var a,t,n,o,r,l;return console.log("\uD83C\uDF10 listJobsAdmin response:",{status:e.status,totalJobs:(null==(t=e.data)||null==(a=t.pagination)?void 0:a.total_count)||0,currentPage:(null==(o=e.data)||null==(n=o.pagination)?void 0:n.current_page)||1,totalPages:(null==(l=e.data)||null==(r=l.pagination)?void 0:r.total_pages)||1}),e}).catch(e=>{var a;throw console.error("\uD83C\uDF10 listJobsAdmin error:",e),console.error("\uD83C\uDF10 listJobsAdmin error response:",null==(a=e.response)?void 0:a.data),e})}function p(e){return n.A.patch("/api/v1/jobs/".concat(e,"/toggle-publish/"))}},48937:(e,a,t)=>{t.d(a,{C1:()=>l,Gu:()=>b,JT:()=>i,RC:()=>c,S0:()=>y,Y_:()=>p,bl:()=>u,dl:()=>x,eK:()=>s,fetchCompanies:()=>r,getCompanyStats:()=>d,jQ:()=>m,mm:()=>o,oY:()=>g});var n=t(37719);function o(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=new URLSearchParams;e.page&&a.append("page",e.page),e.per_page&&a.append("per_page",e.per_page),e.tier&&"ALL"!==e.tier&&a.append("tier",e.tier),e.industry&&"ALL"!==e.industry&&a.append("industry",e.industry),e.campus_recruiting&&a.append("campus_recruiting",e.campus_recruiting),e.search&&a.append("search",e.search),e.sort&&a.append("sort",e.sort),a.append("_t",new Date().getTime());let t=a.toString(),o=["/api/v1/companies/".concat(t?"?".concat(t):""),"/api/v1/college/default-college/companies/".concat(t?"?".concat(t):"")];return n.A.get(o[0]).catch(e=>(console.log("Primary endpoint failed: ".concat(e.message,", trying fallback...")),n.A.get(o[1])))}async function r(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{console.log("Fetching companies from API...");let a=await o(e),t=[];if(a.data&&Array.isArray(a.data)?t=a.data:a.data&&a.data.results&&Array.isArray(a.data.results)?t=a.data.results:a.data&&a.data.data&&Array.isArray(a.data.data)&&(t=a.data.data),console.log("Retrieved ".concat(t.length," companies from API")),t.length>0){let e=await Promise.all(t.map(async e=>{try{let a=await l(e.id);return p(a.data)}catch(a){return console.log("Could not fetch details for company ".concat(e.id,":"),a),p(e)}}));return sessionStorage.setItem("companies_data",JSON.stringify(e)),sessionStorage.setItem("companies_timestamp",Date.now()),e}throw Error("No companies returned from API")}catch(a){console.error("Error fetching companies from API:",a);{let e=sessionStorage.getItem("companies_data"),a=sessionStorage.getItem("companies_timestamp");if(e&&a&&Date.now()-parseInt(a)<3e5)return console.log("Using cached company data (< 5 min old)"),JSON.parse(e)}console.log("Falling back to static company data");let{companies:e}=await t.e(1260).then(t.bind(t,21260));return e}}function l(e){let a=["/api/v1/company/".concat(e,"/"),"/api/v1/companies/".concat(e,"/"),"/api/v1/college/default-college/companies/".concat(e,"/")];return n.A.get(a[0]).catch(e=>(console.log("First company endpoint failed: ".concat(e.message,", trying second...")),n.A.get(a[1]).catch(e=>(console.log("Second company endpoint failed: ".concat(e.message,", trying third...")),n.A.get(a[2])))))}function s(e){let a=new FormData;return Object.keys(e).forEach(t=>{"logo"===t&&e[t]instanceof File?a.append(t,e[t]):null!==e[t]&&void 0!==e[t]&&a.append(t,e[t])}),n.A.post("/api/v1/companies/",a,{headers:{"Content-Type":"multipart/form-data"}})}function i(e,a){let t=new FormData;return Object.keys(a).forEach(e=>{"logo"===e&&a[e]instanceof File?t.append(e,a[e]):null!==a[e]&&void 0!==a[e]&&t.append(e,a[e])}),n.A.put("/api/v1/companies/".concat(e,"/"),t,{headers:{"Content-Type":"multipart/form-data"}})}function c(e){return n.A.delete("/api/v1/companies/".concat(e,"/"))}function d(){return n.A.get("/api/v1/companies/stats/")}function p(e){return{id:e.id,name:e.name,logo:e.logo||"https://via.placeholder.com/48x48/4285F4/FFFFFF?text=".concat(e.name.charAt(0)),description:e.description||"",industry:e.industry||"",size:e.size||"",founded:e.founded||"",location:e.location||"Location not specified",website:e.website||"",tier:e.tier||"Tier 3",campus_recruiting:e.campus_recruiting||!1,totalActiveJobs:e.total_active_jobs||0,totalApplicants:e.total_applicants||0,totalHired:e.total_hired||0,awaitedApproval:e.pending_approval||0}}function u(e){return n.A.get("/api/v1/companies/".concat(e,"/followers/count/"))}function m(e,a){return n.A.post("/api/v1/companies/".concat(e,"/followers/"),{user_id:a})}function g(e,a){return n.A.delete("/api/v1/companies/".concat(e,"/followers/"),{data:{user_id:a}})}function b(e,a){return n.A.get("/api/v1/companies/".concat(e,"/followers/status/?user_id=").concat(a))}function x(e){return n.A.get("/api/v1/users/".concat(e,"/following/"))}function y(){return n.A.get("/api/v1/companies/simple/").then(e=>{if(e.data&&e.data.success)return e.data;throw Error("Failed to fetch companies")}).catch(e=>(console.error("Error fetching companies:",e),{success:!1,data:[],count:0,error:e.message}))}}}]);