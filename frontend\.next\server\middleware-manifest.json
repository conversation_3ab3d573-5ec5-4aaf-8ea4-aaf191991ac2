{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_adbadb87._.js", "server/edge/chunks/[root-of-the-server]__e07e566a._.js", "server/edge/chunks/edge-wrapper_9cb4b359.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "4L1N1ApV6JxRkywf2zSy/shy6TmjdExKn7foFPP8JTY=", "__NEXT_PREVIEW_MODE_ID": "dc73368ec17896e069f4f2c48e9c06b7", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "06a22190f1ac8d2b21ed8c036fe3bb5515de2be53f26efc36d94507bef19567a", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "9ef8956712f6086d908cb1ed0949ceed6e7e6a1ac3843c37b27dc30c74afa3c9"}}}, "sortedMiddleware": ["/"], "functions": {}}