(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4520],{1243:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},19946:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var a=r(12115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),n=e=>{let t=o(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},i=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,a.forwardRef)((e,t)=>{let{color:r="currentColor",size:s=24,strokeWidth:o=2,absoluteStrokeWidth:n,className:d="",children:u,iconNode:h,...p}=e;return(0,a.createElement)("svg",{ref:t,...c,width:s,height:s,stroke:r,strokeWidth:n?24*Number(o)/Number(s):o,className:l("lucide",d),...!u&&!i(p)&&{"aria-hidden":"true"},...p},[...h.map(e=>{let[t,r]=e;return(0,a.createElement)(t,r)}),...Array.isArray(u)?u:[u]])}),u=(e,t)=>{let r=(0,a.forwardRef)((r,o)=>{let{className:i,...c}=r;return(0,a.createElement)(d,{ref:o,iconNode:t,className:l("lucide-".concat(s(n(e))),"lucide-".concat(e),i),...c})});return r.displayName=n(e),r}},29869:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},33325:(e,t,r)=>{Promise.resolve().then(r.bind(r,66960))},33786:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},35695:(e,t,r)=>{"use strict";var a=r(18999);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},37719:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(23464);r(73983);let s=a.A.create({baseURL:"http://127.0.0.1:8000",headers:{"Content-Type":"application/json"}});s.interceptors.request.use(e=>{let t=localStorage.getItem("access_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),s.interceptors.response.use(e=>e,async e=>{var t;let r=e.config;if((null==(t=e.response)?void 0:t.status)===401&&!r._retry){r._retry=!0;try{let e=localStorage.getItem("refresh_token");if(e){let t=await a.A.post("http://127.0.0.1:8000/api/auth/token/refresh/",{refresh:e});return localStorage.setItem("access_token",t.data.access),r.headers.Authorization="Bearer ".concat(t.data.access),s(r)}}catch(e){console.error("Error refreshing token:",e),localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),window.location.href="/login"}}return Promise.reject(e)});let o=s},40646:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},54416:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},66960:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var a=r(95155),s=r(12115),o=r(35695),n=r(6874),l=r.n(n),i=r(23464),c=r(72476),d=r(73983);function u(){let e=(0,o.useRouter)(),{showAuthError:t,showValidationError:r,showNetworkError:n,handleApiError:u}=(0,d.hN)(),[h,p]=(0,s.useState)(!1),[g,m]=(0,s.useState)(""),[y,f]=(0,s.useState)(""),[b,x]=(0,s.useState)(""),[k,v]=(0,s.useState)(""),[w,A]=(0,s.useState)(!1),[S,N]=(0,s.useState)([]),j=async e=>{e.preventDefault(),await _(y,b)},_=async(a,s)=>{var o,l,d,h,g,y,f,b,x;p(!0),m("");try{let t=await i.A.post("http://127.0.0.1:8000/api/auth/login/",{email:a,password:s});(0,c.O5)(t.data.access),(0,c.fE)(t.data.refresh);let{access:r,refresh:n,user:l}=t.data;switch(localStorage.setItem("access",r),localStorage.setItem("refresh",n),localStorage.setItem("access_token",r),localStorage.setItem("refresh_token",n),localStorage.setItem("user",JSON.stringify(l)),document.cookie="role=".concat(l.user_type,"; path=/; max-age=86400"),null==(o=l.user_type)?void 0:o.toLowerCase()){case"student":default:e.push("/");break;case"admin":e.push("/admin/dashboard");break;case"employer":e.push("/company/dashboard")}}catch(e){console.error("Login error:",e),(null==(l=e.response)?void 0:l.status)===403&&(null==(h=e.response)||null==(d=h.data)?void 0:d.freeze_status)==="complete"?(t(e.response.data.detail),m(e.response.data.detail)):(null==(g=e.response)?void 0:g.status)===401?r("Login Failed",{credentials:"Invalid email or password. Please check your credentials and try again."}):e.response?u(e,"login"):n(e),m((null==(f=e.response)||null==(y=f.data)?void 0:y.detail)||(null==(x=e.response)||null==(b=x.data)?void 0:b.message)||"Login failed. Please try again.")}finally{p(!1)}};return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-r from-[#242734] to-[#241F2A] flex items-center justify-center p-4 login-container",children:(0,a.jsxs)("form",{onSubmit:j,className:"w-full max-w-md bg-white rounded-xl shadow-2xl p-10 flex flex-col gap-6 login-form",children:[(0,a.jsx)("h1",{className:"text-center text-2xl text-gray-800 font-bold mb-2",children:"Login to Placeeasy"}),g&&(0,a.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded",children:g}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("label",{className:"mb-2 font-semibold text-gray-800",children:"Email"}),(0,a.jsx)("input",{type:"email",value:y,onChange:e=>f(e.target.value),className:"p-3 rounded-lg border border-gray-300 text-gray-800 text-base outline-none",required:!0,disabled:h})]}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("label",{className:"mb-2 font-semibold text-gray-800",children:"Password"}),(0,a.jsx)("input",{type:"password",value:b,onChange:e=>x(e.target.value),className:"p-3 rounded-lg border border-gray-300 text-gray-800 text-base outline-none",required:!0,disabled:h})]}),(0,a.jsx)("button",{type:"submit",disabled:h,className:"p-3 rounded-lg cursor-pointer text-white text-base font-medium transition-colors ".concat(h?"bg-gray-400 cursor-not-allowed":"bg-indigo-500 hover:bg-indigo-600"),children:h?"Logging in...":"Login"}),(0,a.jsx)("button",{onClick:()=>_("<EMAIL>","admin"),disabled:h,type:"button",className:"p-3 rounded-lg cursor-pointer text-white text-base font-medium transition-colors ".concat(h?"bg-gray-400 cursor-not-allowed":"bg-green-500 hover:bg-green-600"),children:h?"Logging in...":"Quick Login as Admin"}),(0,a.jsx)("button",{onClick:()=>_("<EMAIL>","student123"),disabled:h,type:"button",className:"p-3 rounded-lg cursor-pointer text-white text-base font-medium transition-colors ".concat(h?"bg-gray-400 cursor-not-allowed":"bg-blue-500 hover:bg-blue-600"),children:h?"Logging in...":"Quick Login as Student"}),(0,a.jsx)(l(),{href:"/signup",children:(0,a.jsx)("div",{className:"p-3 rounded-lg cursor-pointer text-center bg-indigo-500 text-white text-base font-medium hover:bg-indigo-600 transition-colors",children:"Signup"})})]})})}},71007:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},72476:(e,t,r)=>{"use strict";r.d(t,{O5:()=>s,c4:()=>a,fE:()=>o}),r(37719);let a=()=>localStorage.getItem("access_token"),s=e=>{localStorage.setItem("access_token",e)},o=e=>{localStorage.setItem("refresh_token",e)}},75525:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},76517:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]])},81284:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},85339:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[3464,6874,3983,8441,1684,7358],()=>t(33325)),_N_E=e.O()}]);