(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1260,1318,5085,8937],{21260:(e,t,a)=>{"use strict";a.d(t,{Ly:()=>n,_N:()=>i,companies:()=>r,zZ:()=>l});var s=a(48937);let r=[{id:1,name:"TechCorp Inc",description:"Leading technology solutions provider",industry:"Technology",size:"500-1000",founded:"2010",website:"https://techcorp.com"},{id:2,name:"DataCorp",description:"Data analytics and insights company",industry:"Data Analytics",size:"100-500",founded:"2015",website:"https://datacorp.com"}],n=[{id:1,job_id:25,title:"Software Engineer",company:"TechCorp Inc",status:"APPLIED",application_deadline:"2024-05-30T23:59:59Z"},{id:2,job_id:26,title:"Data Scientist",company:"DataCorp",status:"INTERVIEW SCHEDULED",application_deadline:"2024-06-15T23:59:59Z"}],i=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{let t={...e,_t:new Date().getTime()};console.log("Fetching companies with cache busting...");let a=await (0,s.mm)(t),n=[];a.data&&Array.isArray(a.data)?n=a.data:a.data&&a.data.results&&Array.isArray(a.data.results)?n=a.data.results:a.data&&a.data.data&&Array.isArray(a.data.data)&&(n=a.data.data);let i=n.map(s.Y_);if(console.log("Fetched ".concat(i.length," companies from API")),0===i.length)return console.warn("API returned empty companies array, using static data"),r;return i}catch(e){console.error("Error fetching companies:",e);try{console.log("Trying alternate endpoint format...");let e=await fetch("/api/v1/college/default-college/companies/");if(e.ok){let t=await e.json(),a=Array.isArray(t)?t:t.data||t.results||[];if(a.length>0)return console.log("Successfully retrieved companies from alternate endpoint"),a.map(s.Y_)}}catch(e){console.error("Alternate endpoint also failed:",e)}return r}};function l(e){return console.log("Fetching jobs for company ID: ".concat(e)),[]}},37719:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var s=a(23464);a(73983);let r=s.A.create({baseURL:"http://127.0.0.1:8000",headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>{let t=localStorage.getItem("access_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,async e=>{var t;let a=e.config;if((null==(t=e.response)?void 0:t.status)===401&&!a._retry){a._retry=!0;try{let e=localStorage.getItem("refresh_token");if(e){let t=await s.A.post("http://127.0.0.1:8000/api/auth/token/refresh/",{refresh:e});return localStorage.setItem("access_token",t.data.access),a.headers.Authorization="Bearer ".concat(t.data.access),r(a)}}catch(e){console.error("Error refreshing token:",e),localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),window.location.href="/login"}}return Promise.reject(e)});let n=r},48937:(e,t,a)=>{"use strict";a.d(t,{C1:()=>i,Gu:()=>x,JT:()=>c,RC:()=>o,S0:()=>y,Y_:()=>m,bl:()=>p,dl:()=>u,eK:()=>l,fetchCompanies:()=>n,getCompanyStats:()=>d,jQ:()=>h,mm:()=>r,oY:()=>g});var s=a(37719);function r(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.page&&t.append("page",e.page),e.per_page&&t.append("per_page",e.per_page),e.tier&&"ALL"!==e.tier&&t.append("tier",e.tier),e.industry&&"ALL"!==e.industry&&t.append("industry",e.industry),e.campus_recruiting&&t.append("campus_recruiting",e.campus_recruiting),e.search&&t.append("search",e.search),e.sort&&t.append("sort",e.sort),t.append("_t",new Date().getTime());let a=t.toString(),r=["/api/v1/companies/".concat(a?"?".concat(a):""),"/api/v1/college/default-college/companies/".concat(a?"?".concat(a):"")];return s.A.get(r[0]).catch(e=>(console.log("Primary endpoint failed: ".concat(e.message,", trying fallback...")),s.A.get(r[1])))}async function n(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{console.log("Fetching companies from API...");let t=await r(e),a=[];if(t.data&&Array.isArray(t.data)?a=t.data:t.data&&t.data.results&&Array.isArray(t.data.results)?a=t.data.results:t.data&&t.data.data&&Array.isArray(t.data.data)&&(a=t.data.data),console.log("Retrieved ".concat(a.length," companies from API")),a.length>0){let e=await Promise.all(a.map(async e=>{try{let t=await i(e.id);return m(t.data)}catch(t){return console.log("Could not fetch details for company ".concat(e.id,":"),t),m(e)}}));return sessionStorage.setItem("companies_data",JSON.stringify(e)),sessionStorage.setItem("companies_timestamp",Date.now()),e}throw Error("No companies returned from API")}catch(t){console.error("Error fetching companies from API:",t);{let e=sessionStorage.getItem("companies_data"),t=sessionStorage.getItem("companies_timestamp");if(e&&t&&Date.now()-parseInt(t)<3e5)return console.log("Using cached company data (< 5 min old)"),JSON.parse(e)}console.log("Falling back to static company data");let{companies:e}=await a.e(1260).then(a.bind(a,21260));return e}}function i(e){let t=["/api/v1/company/".concat(e,"/"),"/api/v1/companies/".concat(e,"/"),"/api/v1/college/default-college/companies/".concat(e,"/")];return s.A.get(t[0]).catch(e=>(console.log("First company endpoint failed: ".concat(e.message,", trying second...")),s.A.get(t[1]).catch(e=>(console.log("Second company endpoint failed: ".concat(e.message,", trying third...")),s.A.get(t[2])))))}function l(e){let t=new FormData;return Object.keys(e).forEach(a=>{"logo"===a&&e[a]instanceof File?t.append(a,e[a]):null!==e[a]&&void 0!==e[a]&&t.append(a,e[a])}),s.A.post("/api/v1/companies/",t,{headers:{"Content-Type":"multipart/form-data"}})}function c(e,t){let a=new FormData;return Object.keys(t).forEach(e=>{"logo"===e&&t[e]instanceof File?a.append(e,t[e]):null!==t[e]&&void 0!==t[e]&&a.append(e,t[e])}),s.A.put("/api/v1/companies/".concat(e,"/"),a,{headers:{"Content-Type":"multipart/form-data"}})}function o(e){return s.A.delete("/api/v1/companies/".concat(e,"/"))}function d(){return s.A.get("/api/v1/companies/stats/")}function m(e){return{id:e.id,name:e.name,logo:e.logo||"https://via.placeholder.com/48x48/4285F4/FFFFFF?text=".concat(e.name.charAt(0)),description:e.description||"",industry:e.industry||"",size:e.size||"",founded:e.founded||"",location:e.location||"Location not specified",website:e.website||"",tier:e.tier||"Tier 3",campus_recruiting:e.campus_recruiting||!1,totalActiveJobs:e.total_active_jobs||0,totalApplicants:e.total_applicants||0,totalHired:e.total_hired||0,awaitedApproval:e.pending_approval||0}}function p(e){return s.A.get("/api/v1/companies/".concat(e,"/followers/count/"))}function h(e,t){return s.A.post("/api/v1/companies/".concat(e,"/followers/"),{user_id:t})}function g(e,t){return s.A.delete("/api/v1/companies/".concat(e,"/followers/"),{data:{user_id:t}})}function x(e,t){return s.A.get("/api/v1/companies/".concat(e,"/followers/status/?user_id=").concat(t))}function u(e){return s.A.get("/api/v1/users/".concat(e,"/following/"))}function y(){return s.A.get("/api/v1/companies/simple/").then(e=>{if(e.data&&e.data.success)return e.data;throw Error("Failed to fetch companies")}).catch(e=>(console.error("Error fetching companies:",e),{success:!1,data:[],count:0,error:e.message}))}},68664:(e,t,a)=>{Promise.resolve().then(a.bind(a,80705))},80705:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>_});var s=a(95155),r=a(12115),n=a(85339),i=a(17580),l=a(23227),c=a(16785),o=a(23861),d=a(40646),m=a(69074),p=a(14186),h=a(47924),g=a(54653),x=a(15968),u=a(42355),y=a(13052),N=a(4516),j=a(71007),f=a(21260);let v={APPLICATION_DEADLINE:"APPLICATION_DEADLINE",INTERVIEW:"INTERVIEW",COMPANY_EVENT:"COMPANY_EVENT",CAREER_FAIR:"CAREER_FAIR",DEADLINE_REMINDER:"DEADLINE_REMINDER",OFFER_RESPONSE:"OFFER_RESPONSE"};v.COMPANY_EVENT,v.COMPANY_EVENT;let b=[{id:1,title:"Microsoft Application Deadline",type:v.APPLICATION_DEADLINE,date:"2024-05-20",time:"23:59",company:"Microsoft",description:"Final deadline for Microsoft Summer Internship applications",status:"upcoming",priority:"high",color:"#dc2626",location:"Online"},{id:2,title:"Google Technical Interview",type:v.INTERVIEW,date:"2024-05-22",time:"14:00",company:"Google",description:"Technical interview for Software Engineer position",status:"scheduled",priority:"high",color:"#059669",location:"Virtual - Google Meet",duration:"90 minutes"},{id:3,title:"Career Fair 2024",type:v.CAREER_FAIR,date:"2024-05-25",time:"10:00",company:"Multiple Companies",description:"Annual career fair with 50+ companies",status:"upcoming",priority:"medium",color:"#2563eb",location:"Student Center",duration:"6 hours"}],w=e=>b.filter(t=>t.type===e),A=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:7,t=new Date,a=new Date(t.getTime()+24*e*36e5);return b.filter(e=>{let s=new Date(e.date);return s>=t&&s<=a}).sort((e,t)=>new Date(e.date)-new Date(t.date))},E=()=>{let e=b.length,t=w(v.INTERVIEW).length,a=w(v.APPLICATION_DEADLINE).length;return{total:e,interviews:t,deadlines:a,companyEvents:w(v.COMPANY_EVENT).length,upcoming:A().length}},I=()=>{let e=[];return f.Ly.forEach(t=>{let a=new Date(new Date(t.application_deadline).getTime()-2592e5);if(a>new Date&&e.push({id:"reminder-".concat(t.id),title:"Reminder: ".concat(t.title," - Application Due Soon"),type:v.DEADLINE_REMINDER,date:a.toISOString().split("T")[0],time:"09:00",company:t.company,jobId:t.job_id,description:"Application deadline reminder for ".concat(t.title," at ").concat(t.company),status:"upcoming",priority:"medium",color:"#f59e0b"}),"INTERVIEW SCHEDULED"===t.status){let a=new Date;a.setDate(a.getDate()+Math.floor(14*Math.random())+1),e.push({id:"interview-".concat(t.id),title:"".concat(t.company," ").concat(t.title," - Interview"),type:v.INTERVIEW,date:a.toISOString().split("T")[0],time:"14:00",company:t.company,jobId:t.job_id,applicationId:t.id,description:"Interview for ".concat(t.title," position at ").concat(t.company),status:"scheduled",priority:"high",color:"#059669",location:"Virtual - Teams Meeting",interviewer:"HR Team",duration:"60 minutes"})}}),e},D=()=>[...b,...I()];function _(){var e;let[t,a]=(0,r.useState)(new Date),[f,b]=(0,r.useState)(null),[w,I]=(0,r.useState)([]),[_,C]=(0,r.useState)([]),[S,T]=(0,r.useState)("calendar"),[R,L]=(0,r.useState)("ALL"),[F,P]=(0,r.useState)(""),[k,M]=(0,r.useState)(null);(0,r.useEffect)(()=>{let e=D();I(e),C(e)},[]),(0,r.useEffect)(()=>{let e=w;"ALL"!==R&&(e=e.filter(e=>e.type===R)),F&&(e=e.filter(e=>e.title.toLowerCase().includes(F.toLowerCase())||e.company.toLowerCase().includes(F.toLowerCase())||e.description.toLowerCase().includes(F.toLowerCase()))),C(e)},[w,R,F]);let O=e=>{let s=new Date(t);s.setMonth(t.getMonth()+e),a(s)},V=e=>{if(!e)return[];let a="".concat(t.getFullYear(),"-").concat(String(t.getMonth()+1).padStart(2,"0"),"-").concat(String(e).padStart(2,"0"));return _.filter(e=>e.date===a)},Y=e=>{switch(e){case v.APPLICATION_DEADLINE:return(0,s.jsx)(n.A,{className:"w-4 h-4"});case v.INTERVIEW:return(0,s.jsx)(i.A,{className:"w-4 h-4"});case v.COMPANY_EVENT:return(0,s.jsx)(l.A,{className:"w-4 h-4"});case v.CAREER_FAIR:return(0,s.jsx)(c.A,{className:"w-4 h-4"});case v.DEADLINE_REMINDER:return(0,s.jsx)(o.A,{className:"w-4 h-4"});case v.OFFER_RESPONSE:return(0,s.jsx)(d.A,{className:"w-4 h-4"});default:return(0,s.jsx)(m.A,{className:"w-4 h-4"})}},W=e=>{switch(e){case"high":return"border-red-500 bg-red-50 text-red-700";case"medium":return"border-yellow-500 bg-yellow-50 text-yellow-700";case"low":return"border-green-500 bg-green-50 text-green-700";default:return"border-gray-500 bg-gray-50 text-gray-700"}},U=E(),z=A(7),J=e=>{M(e)},G=()=>{M(null)},H=e=>{let a=new Date;return e&&t.getFullYear()===a.getFullYear()&&t.getMonth()===a.getMonth()&&e===a.getDate()};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"My Calendar"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Track your application deadlines, interviews, and career events"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4 mb-8",children:[(0,s.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-100",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,s.jsx)(m.A,{className:"w-5 h-5 text-blue-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:U.total}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Total Events"})]})]})}),(0,s.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-100",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,s.jsx)(i.A,{className:"w-5 h-5 text-green-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:U.interviews}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Interviews"})]})]})}),(0,s.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-100",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 bg-red-100 rounded-lg",children:(0,s.jsx)(n.A,{className:"w-5 h-5 text-red-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:U.deadlines}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Deadlines"})]})]})}),(0,s.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-100",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,s.jsx)(l.A,{className:"w-5 h-5 text-purple-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:U.companyEvents}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Company Events"})]})]})}),(0,s.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-100",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 bg-amber-100 rounded-lg",children:(0,s.jsx)(p.A,{className:"w-5 h-5 text-amber-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:U.upcoming}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"This Week"})]})]})})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-100 mb-6",children:[(0,s.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex gap-4 flex-1",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(h.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,s.jsx)("input",{type:"text",placeholder:"Search events...",value:F,onChange:e=>P(e.target.value),className:"pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[300px]"})]}),(0,s.jsxs)("select",{value:R,onChange:e=>L(e.target.value),className:"px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"ALL",children:"All Events"}),(0,s.jsx)("option",{value:v.APPLICATION_DEADLINE,children:"Application Deadlines"}),(0,s.jsx)("option",{value:v.INTERVIEW,children:"Interviews"}),(0,s.jsx)("option",{value:v.COMPANY_EVENT,children:"Company Events"}),(0,s.jsx)("option",{value:v.CAREER_FAIR,children:"Career Fairs"}),(0,s.jsx)("option",{value:v.DEADLINE_REMINDER,children:"Reminders"}),(0,s.jsx)("option",{value:v.OFFER_RESPONSE,children:"Offer Responses"})]})]}),(0,s.jsxs)("div",{className:"flex bg-gray-100 rounded-lg p-1",children:[(0,s.jsx)("button",{onClick:()=>T("calendar"),className:"p-2 rounded-md transition-colors ".concat("calendar"===S?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:(0,s.jsx)(g.A,{className:"w-5 h-5"})}),(0,s.jsx)("button",{onClick:()=>T("list"),className:"p-2 rounded-md transition-colors ".concat("list"===S?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:(0,s.jsx)(x.A,{className:"w-5 h-5"})})]})]}),(0,s.jsxs)("div",{className:"mt-4 text-sm text-gray-600",children:["Showing ",_.length," of ",w.length," events"]})]})]}),"calendar"===S?(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden",children:[(0,s.jsx)("div",{className:"p-6 border-b border-gray-100",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("h2",{className:"text-xl font-semibold text-gray-900",children:[["January","February","March","April","May","June","July","August","September","October","November","December"][t.getMonth()]," ",t.getFullYear()]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)("button",{onClick:()=>O(-1),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,s.jsx)(u.A,{className:"w-5 h-5"})}),(0,s.jsx)("button",{onClick:()=>a(new Date),className:"px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors text-sm font-medium",children:"Today"}),(0,s.jsx)("button",{onClick:()=>O(1),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,s.jsx)(y.A,{className:"w-5 h-5"})})]})]})}),(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("div",{className:"grid grid-cols-7 gap-4 mb-4",children:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"].map(e=>(0,s.jsx)("div",{className:"text-center text-sm font-medium text-gray-500 py-2",children:e},e))}),(0,s.jsx)("div",{className:"grid grid-cols-7 gap-4",children:(e=>{let t=e.getFullYear(),a=e.getMonth(),s=new Date(t,a,1),r=new Date(t,a+1,0).getDate(),n=s.getDay(),i=[];for(let e=0;e<n;e++)i.push(null);for(let e=1;e<=r;e++)i.push(e);return i})(t).map((e,t)=>{let a=V(e),r=H(e);return(0,s.jsx)("div",{className:"min-h-[120px] p-2 border rounded-lg ".concat(r?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"," ").concat(e?"cursor-pointer":""),onClick:()=>e&&b(e),children:e&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"text-sm font-medium mb-2 ".concat(r?"text-blue-700":"text-gray-900"),children:e}),(0,s.jsxs)("div",{className:"space-y-1",children:[a.slice(0,3).map(e=>(0,s.jsx)("div",{onClick:t=>{t.stopPropagation(),J(e)},className:"text-xs p-1 rounded truncate cursor-pointer hover:opacity-80",style:{backgroundColor:e.color+"20",color:e.color},children:e.title},e.id)),a.length>3&&(0,s.jsxs)("div",{className:"text-xs text-gray-500 p-1",children:["+",a.length-3," more"]})]})]})},t)})})]})]}):(0,s.jsx)("div",{className:"space-y-4",children:0===_.length?(0,s.jsxs)("div",{className:"text-center py-16 bg-white rounded-lg",children:[(0,s.jsx)("div",{className:"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(m.A,{className:"w-12 h-12 text-gray-400"})}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"No events found"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Try adjusting your search or filters to find more events"})]}):_.map(e=>{var t;return(0,s.jsx)("div",{onClick:()=>J(e),className:"bg-white rounded-lg p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200 cursor-pointer",children:(0,s.jsxs)("div",{className:"flex items-start gap-4",children:[(0,s.jsx)("div",{className:"p-3 rounded-lg",style:{backgroundColor:e.color+"20",color:e.color},children:Y(e.type)}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:e.title}),(0,s.jsx)("span",{className:"px-3 py-1 rounded-full text-xs font-medium border ".concat(W(e.priority)),children:(null==(t=e.priority)?void 0:t.toUpperCase())||"MEDIUM"})]}),(0,s.jsx)("p",{className:"text-gray-600 mb-3",children:e.description}),(0,s.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-500",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(m.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:new Date(e.date).toLocaleDateString()})]}),e.time&&(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(p.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:e.time})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(l.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:e.company})]}),e.location&&(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(N.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:e.location})]})]})]})]})},e.id)})}),z.length>0&&(0,s.jsxs)("div",{className:"fixed right-6 top-24 w-80 bg-white rounded-lg shadow-lg border border-gray-100 p-4 max-h-96 overflow-y-auto",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Upcoming This Week"}),(0,s.jsx)("div",{className:"space-y-3",children:z.slice(0,5).map(e=>(0,s.jsxs)("div",{onClick:()=>J(e),className:"p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,s.jsx)("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:e.color}}),(0,s.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e.company})]}),(0,s.jsx)("p",{className:"text-sm text-gray-700 line-clamp-2",children:e.title}),(0,s.jsxs)("div",{className:"flex items-center gap-2 mt-2 text-xs text-gray-500",children:[(0,s.jsx)(m.A,{className:"w-3 h-3"}),(0,s.jsx)("span",{children:new Date(e.date).toLocaleDateString()}),e.time&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(p.A,{className:"w-3 h-3 ml-2"}),(0,s.jsx)("span",{children:e.time})]})]})]},e.id))})]}),k&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,s.jsx)("div",{className:"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-6",children:[(0,s.jsxs)("div",{className:"flex items-start gap-4",children:[(0,s.jsx)("div",{className:"p-3 rounded-lg",style:{backgroundColor:k.color+"20",color:k.color},children:Y(k.type)}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:k.title}),(0,s.jsx)("p",{className:"text-gray-600",children:k.description})]})]}),(0,s.jsx)("button",{onClick:G,className:"text-gray-400 hover:text-gray-600 p-2",children:"\xd7"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(m.A,{className:"w-5 h-5 text-gray-400"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Date"}),(0,s.jsx)("p",{className:"font-medium",children:new Date(k.date).toLocaleDateString()})]})]}),k.time&&(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(p.A,{className:"w-5 h-5 text-gray-400"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Time"}),(0,s.jsx)("p",{className:"font-medium",children:k.time})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(l.A,{className:"w-5 h-5 text-gray-400"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Company"}),(0,s.jsx)("p",{className:"font-medium",children:k.company})]})]}),k.location&&(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(N.A,{className:"w-5 h-5 text-gray-400"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Location"}),(0,s.jsx)("p",{className:"font-medium",children:k.location})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[k.interviewer&&(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(j.A,{className:"w-5 h-5 text-gray-400"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Interviewer"}),(0,s.jsx)("p",{className:"font-medium",children:k.interviewer})]})]}),k.duration&&(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(p.A,{className:"w-5 h-5 text-gray-400"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Duration"}),(0,s.jsx)("p",{className:"font-medium",children:k.duration})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(n.A,{className:"w-5 h-5 text-gray-400"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Priority"}),(0,s.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium border ".concat(W(k.priority)),children:(null==(e=k.priority)?void 0:e.toUpperCase())||"MEDIUM"})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(d.A,{className:"w-5 h-5 text-gray-400"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Status"}),(0,s.jsx)("p",{className:"font-medium capitalize",children:k.status})]})]})]})]}),(0,s.jsxs)("div",{className:"flex gap-3 mt-8",children:[(0,s.jsx)("button",{onClick:G,className:"flex-1 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:"Close"}),(0,s.jsx)("button",{className:"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Add to Google Calendar"})]})]})})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[3464,4670,3983,8441,1684,7358],()=>t(68664)),_N_E=e.O()}]);