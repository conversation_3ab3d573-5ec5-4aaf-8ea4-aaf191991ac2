"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2338],{52338:(e,t,a)=>{a.d(t,{N:()=>c});var r=a(23464),o=a(72476);let s=r.A.create({baseURL:"http://localhost:8000",headers:{"Content-Type":"application/json"}});s.interceptors.request.use(e=>{let t=(0,o.c4)();return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),s.interceptors.response.use(e=>e,e=>{var t;return(null==(t=e.response)?void 0:t.status)===401&&(localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),window.location.href="/login"),Promise.reject(e)});let c={getStudents:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(await s.get("/api/accounts/students/",{params:e})).data},getStudentsWithStats:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{return(await s.get("/api/accounts/students/stats/",{params:e})).data}catch(r){console.log("Stats endpoint not available, using regular endpoint");let t=await s.get("/api/accounts/students/",{params:e}),a=t.data.data||t.data;if(Array.isArray(a)){let r=calculateStudentStats(a,e);return{...t.data,statistics:r}}return t.data}},getStudent:async e=>(await s.get("/api/accounts/students/".concat(e,"/"))).data,updateStudent:async(e,t)=>{console.log("updateStudent called with:",{id:e,data:t});let a=(0,o.c4)();if(console.log("Auth token available:",!!a),a&&console.log("Token preview:",a.substring(0,20)+"..."),!a)throw Error("Authentication required to update student");let r={...t};["joining_year","passout_year"].forEach(e=>{if(null!==r[e]&&void 0!==r[e]){let t=parseInt(r[e]);r[e]=isNaN(t)?null:t}}),["first_name","last_name","student_id","contact_email","phone","branch","gpa","date_of_birth","address","city","district","state","pincode","country","parent_contact","education","skills","tenth_cgpa","tenth_percentage","tenth_board","tenth_school","tenth_year_of_passing","tenth_location","tenth_specialization","twelfth_cgpa","twelfth_percentage","twelfth_board","twelfth_school","twelfth_year_of_passing","twelfth_location","twelfth_specialization"].forEach(e=>{null!==r[e]&&void 0!==r[e]&&(r[e]=String(r[e]).trim())}),Object.keys(r).forEach(e=>{void 0===r[e]&&delete r[e]}),console.log("Cleaned data being sent:",r);try{console.log("Trying ViewSet endpoint:","/api/accounts/profiles/".concat(e,"/"));let t=await s.patch("/api/accounts/profiles/".concat(e,"/"),r);return console.log("ViewSet endpoint success:",t.data),t.data}catch(t){var c,n,l,i,u,d,p,h,f;console.error("ViewSet endpoint failed:",{status:null==(c=t.response)?void 0:c.status,statusText:null==(n=t.response)?void 0:n.statusText,data:null==(l=t.response)?void 0:l.data,headers:null==(i=t.response)?void 0:i.headers,config:t.config});try{console.log("Trying fallback endpoint:","/api/accounts/students/".concat(e,"/update/"));let t=await s.patch("/api/accounts/students/".concat(e,"/update/"),r);return console.log("Fallback endpoint success:",t.data),t.data}catch(e){throw console.error("Failed to update student via both endpoints:",{viewSetError:{status:null==(u=t.response)?void 0:u.status,data:null==(d=t.response)?void 0:d.data},updateViewError:{status:null==(p=e.response)?void 0:p.status,data:null==(h=e.response)?void 0:h.data}}),(null==(f=e.response)?void 0:f.status)===400?e:t}}},getProfile:async()=>{let e=(0,o.c4)();return s.get("/api/auth/profile/",{headers:{Authorization:"Bearer ".concat(e)}}).then(e=>e.data)},updateProfile:async e=>{let t=(0,o.c4)();return s.patch("/api/auth/profile/",e,{headers:{Authorization:"Bearer ".concat(t)}}).then(e=>e.data)},uploadProfileImage:async e=>{let t=(0,o.c4)(),a=new FormData;return a.append("image",e),s.post("/api/accounts/profiles/me/upload_profile_image/",a,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"multipart/form-data"}}).then(e=>e.data)},uploadResume:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,a=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=(0,o.c4)(),c=new FormData;return c.append("file",e),t&&c.append("name",t),c.append("is_primary",a),s.post("/api/accounts/profiles/me/resumes/",c,{headers:{Authorization:"Bearer ".concat(r),"Content-Type":"multipart/form-data"}}).then(e=>e.data)},adminUploadResume:async function(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],c=(0,o.c4)();if(!c)throw Error("No authentication token found");let n=new FormData;return n.append("file",t),a&&n.append("name",a),n.append("is_primary",r),s.post("/api/accounts/profiles/".concat(e,"/upload_resume/"),n,{headers:{Authorization:"Bearer ".concat(c),"Content-Type":"multipart/form-data"}}).then(e=>e.data)},adminGetResumes:async e=>{let t=(0,o.c4)();if(!t)throw Error("No authentication token found");return s.get("/api/accounts/profiles/".concat(e,"/resumes/"),{headers:{Authorization:"Bearer ".concat(t)}}).then(e=>e.data)},adminUploadCertificate:async(e,t,a)=>{let r=(0,o.c4)();if(!r)throw Error("No authentication token found");let c=new FormData;return c.append("file",t),c.append("type",a),s.post("/api/accounts/profiles/".concat(e,"/upload_certificate/"),c,{headers:{Authorization:"Bearer ".concat(r),"Content-Type":"multipart/form-data"}}).then(e=>e.data)},adminUploadSemesterMarksheet:async(e,t,a,r)=>{let c=(0,o.c4)();if(!c)throw Error("No authentication token found");let n=new FormData;return n.append("marksheet_file",t),n.append("semester",a),n.append("cgpa",r),s.post("/api/accounts/profiles/".concat(e,"/upload_semester_marksheet/"),n,{headers:{Authorization:"Bearer ".concat(c),"Content-Type":"multipart/form-data"}}).then(e=>e.data)},uploadResumeToProfile:async e=>{let t=(0,o.c4)(),a=new FormData;return a.append("resume",e),s.patch("/api/auth/profile/",a,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"multipart/form-data"}}).then(e=>e.data)},getResumes:async()=>{try{let e=(0,o.c4)();if(!e)return console.log("No authentication token, returning empty array"),[];let t=await s.get("/api/accounts/profiles/me/resumes/",{headers:{Authorization:"Bearer ".concat(e)}});if(!t.data)return await c.getResumesLegacy();if(Array.isArray(t.data))return t.data;if(t.data&&t.data.data&&Array.isArray(t.data.data))return t.data.data;console.log("Response data is not an array, trying fallback. Response:",t.data);try{return await c.getResumesLegacy()}catch(e){return console.log("Fallback also failed, returning empty array"),[]}}catch(e){console.log("Resume endpoint failed, using fallback method");try{return await c.getResumesLegacy()}catch(e){return console.log("Fallback method also failed, returning empty array"),[]}}},getResumesLegacy:async()=>{try{if(!(0,o.c4)())return console.log("No auth token for legacy resume fetch"),[];let e=await c.getProfile();if((null==e?void 0:e.resume)||(null==e?void 0:e.resume_url)){let t=e.resume_url||e.resume;if(t&&""!==t.trim()&&"null"!==t&&"undefined"!==t){let a=t.split("/").pop()||"Resume.pdf";return[{id:e.id||1,name:a,file_url:t,uploaded_at:e.updated_at||new Date().toISOString()}]}}return[]}catch(e){return console.log("Legacy resume fetch error:",e.message),[]}},deleteResume:async e=>{let t=(0,o.c4)();try{console.log("Attempting to delete resume with ID: ".concat(e));let a=await s.delete("/api/accounts/profiles/me/resumes/".concat(e,"/"),{headers:{Authorization:"Bearer ".concat(t)}});return console.log("DELETE resume successful:",a.data),a.data}catch(e){throw console.error("Error deleting resume:",e),e}},deleteResumeLegacy:async e=>{let t=(0,o.c4)();try{console.log("Attempting to delete resume with ID: ".concat(e));let a=!1;for(let r of[async()=>{try{let a=await s.delete("/api/accounts/profiles/me/resumes/".concat(e,"/"),{headers:{Authorization:"Bearer ".concat(t)}});return console.log("DELETE resume successful:",a.data),{success:!0,data:a.data}}catch(e){return console.log("Strategy 1 failed: ".concat(e.message)),{success:!1}}},async()=>{try{let a=await s.post("/api/accounts/profiles/me/resumes/".concat(e,"/remove/"),{},{headers:{Authorization:"Bearer ".concat(t)}});return console.log("POST remove successful:",a.data),{success:!0,data:a.data}}catch(e){return console.log("Strategy 2 failed: ".concat(e.message)),{success:!1}}},async()=>{try{let a=await s.patch("/api/auth/profile/",{delete_resume:e},{headers:{Authorization:"Bearer ".concat(t)}});return console.log("PATCH profile successful:",a.data),{success:!0,data:a.data}}catch(e){return console.log("Strategy 3 failed: ".concat(e.message)),{success:!1}}},async()=>{try{let e=await s.patch("/api/auth/profile/",{reset_resumes:!0},{headers:{Authorization:"Bearer ".concat(t)}});return console.log("Reset resumes successful:",e.data),{success:!0,data:e.data}}catch(e){return console.log("Strategy 4 failed: ".concat(e.message)),{success:!1}}}])if((await r()).success){a=!0;break}try{let e=Object.keys(localStorage).filter(e=>e.includes("resume")||e.includes("file")||e.includes("document"));e.length>0&&(console.log("Clearing resume-related localStorage items:",e),e.forEach(e=>localStorage.removeItem(e))),localStorage.removeItem("resume_cache"),localStorage.removeItem("resume_list"),localStorage.removeItem("profile_cache"),localStorage.removeItem("resume_count"),localStorage.removeItem("last_resume_update")}catch(e){console.error("Error clearing localStorage:",e)}return{success:a,message:a?"Resume deleted successfully":"Resume deleted locally but server sync failed"}}catch(e){var a,r;return console.error("Resume deletion failed:",null==(a=e.response)?void 0:a.status,e.message),{success:!0,synced:!1,error:e.message,status:null==(r=e.response)?void 0:r.status,message:"Resume removed from display (sync with server failed)"}}},uploadCertificate:async(e,t)=>{let a=(0,o.c4)(),r=new FormData;return r.append("file",e),r.append("type",t),s.post("/api/accounts/profiles/me/upload_certificate/",r,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"multipart/form-data"}}).then(e=>e.data)},getCertificates:async()=>{let e=(0,o.c4)();if(!e)throw Error("Authentication required to fetch certificates");try{let t=await s.get("/api/accounts/profiles/me/certificates/",{headers:{Authorization:"Bearer ".concat(e)}});if(!t.data)return console.error("Empty response when fetching certificates"),[];if(Array.isArray(t.data))return t.data;if(t.data.data&&Array.isArray(t.data.data))return t.data.data;return console.error("Unexpected certificate data format:",t.data),[]}catch(e){var t;throw console.error("Certificate fetch error:",null==(t=e.response)?void 0:t.status,e.message),e}},deleteCertificate:async e=>{let t=(0,o.c4)();try{console.log("Attempting to delete certificate: ".concat(e));let a=await s.delete("/api/accounts/profiles/me/delete_certificate/".concat(e,"/"),{headers:{Authorization:"Bearer ".concat(t)}});return console.log("DELETE certificate successful:",a.data),a.data}catch(e){throw console.error("Error deleting certificate:",e),e}},adminDeleteCertificate:async(e,t)=>{let a=(0,o.c4)();try{console.log("Admin attempting to delete certificate: ".concat(t," for student: ").concat(e));let r=await s.delete("/api/accounts/profiles/".concat(e,"/delete_certificate/").concat(t,"/"),{headers:{Authorization:"Bearer ".concat(a)}});return console.log("Admin DELETE certificate successful:",r.data),r.data}catch(e){throw console.error("Error deleting certificate:",e),e}},deleteMarksheet:async e=>{let t=(0,o.c4)();try{console.log("Attempting to delete marksheet for semester: ".concat(e));let a=await s.delete("/api/accounts/profiles/me/delete_marksheet/".concat(e,"/"),{headers:{Authorization:"Bearer ".concat(t)}});return console.log("DELETE marksheet successful:",a.data),a.data}catch(e){throw console.error("Error deleting marksheet:",e),e}},adminDeleteMarksheet:async(e,t)=>{let a=(0,o.c4)();try{console.log("Admin attempting to delete marksheet for semester: ".concat(t," for student: ").concat(e));let r=await s.delete("/api/accounts/profiles/".concat(e,"/delete_marksheet/").concat(t,"/"),{headers:{Authorization:"Bearer ".concat(a)}});return console.log("Admin DELETE marksheet successful:",r.data),r.data}catch(e){throw console.error("Error deleting marksheet:",e),e}},deleteCertificateLegacy:async e=>{let t=(0,o.c4)();try{console.log("Attempting to delete certificate with ID: ".concat(e));let a=!1;for(let r of[async()=>{try{let a=await s.delete("/api/accounts/profiles/me/certificates/".concat(e,"/"),{headers:{Authorization:"Bearer ".concat(t)}});return console.log("DELETE certificate successful:",a.data),{success:!0,data:a.data}}catch(e){return console.log("Strategy 1 failed: ".concat(e.message)),{success:!1}}},async()=>{try{let a=await s.post("/api/accounts/profiles/me/certificates/".concat(e,"/remove/"),{},{headers:{Authorization:"Bearer ".concat(t)}});return console.log("POST remove successful:",a.data),{success:!0,data:a.data}}catch(e){return console.log("Strategy 2 failed: ".concat(e.message)),{success:!1}}},async()=>{try{let a=await s.patch("/api/auth/profile/",{delete_certificate:e},{headers:{Authorization:"Bearer ".concat(t)}});return console.log("PATCH profile successful:",a.data),{success:!0,data:a.data}}catch(e){return console.log("Strategy 3 failed: ".concat(e.message)),{success:!1}}},async()=>{try{let e=await s.patch("/api/auth/profile/",{reset_certificates:!0},{headers:{Authorization:"Bearer ".concat(t)}});return console.log("Reset certificates successful:",e.data),{success:!0,data:e.data}}catch(e){return console.log("Strategy 4 failed: ".concat(e.message)),{success:!1}}}])if((await r()).success){a=!0;break}try{let e=Object.keys(localStorage).filter(e=>e.includes("certificate")||e.includes("document")||e.includes("cert"));e.length>0&&(console.log("Clearing certificate-related localStorage items:",e),e.forEach(e=>localStorage.removeItem(e))),localStorage.removeItem("certificate_cache"),localStorage.removeItem("certificate_list"),localStorage.removeItem("profile_cache")}catch(e){console.error("Error clearing localStorage:",e)}return{success:a,message:a?"Certificate deleted successfully":"Certificate deleted locally but server sync failed"}}catch(e){var a,r;return console.error("Certificate deletion failed:",null==(a=e.response)?void 0:a.status,e.message),{success:!0,synced:!1,error:e.message,status:null==(r=e.response)?void 0:r.status,message:"Certificate removed from display (sync with server failed)"}}},getSemesterMarksheets:async()=>{let e=(0,o.c4)();return s.get("/api/accounts/profiles/me/semester_marksheets/",{headers:{Authorization:"Bearer ".concat(e)}}).then(e=>e.data)},uploadSemesterMarksheet:async(e,t,a)=>{let r=(0,o.c4)(),c=new FormData;return c.append("marksheet_file",e),c.append("semester",t),c.append("cgpa",a),s.post("/api/accounts/profiles/me/upload_semester_marksheet/",c,{headers:{Authorization:"Bearer ".concat(r),"Content-Type":"multipart/form-data"}}).then(e=>e.data)},getFreezeStatus:async()=>{let e=(0,o.c4)();if(!e)throw Error("Authentication required to fetch freeze status");try{let t=(await s.get("/api/auth/profile/",{headers:{Authorization:"Bearer ".concat(e)}})).data;return{freeze_status:t.freeze_status||"none",freeze_reason:t.freeze_reason,freeze_date:t.freeze_date,min_salary_requirement:t.min_salary_requirement,allowed_job_tiers:t.allowed_job_tiers||[],allowed_job_types:t.allowed_job_types||[],allowed_companies:t.allowed_companies||[]}}catch(e){var t;throw console.error("Freeze status fetch error:",null==(t=e.response)?void 0:t.status,e.message),e}},canApplyToJob:async e=>{let t=(0,o.c4)();if(!t)throw Error("Authentication required to check job application eligibility");try{return(await s.get("/api/v1/college/default-college/jobs/".concat(e,"/can-apply/"),{headers:{Authorization:"Bearer ".concat(t)}})).data}catch(e){var a;throw console.error("Job application eligibility check error:",null==(a=e.response)?void 0:a.status,e.message),e}}}},72476:(e,t,a)=>{a.d(t,{O5:()=>o,c4:()=>r,fE:()=>s}),a(37719);let r=()=>localStorage.getItem("access_token"),o=e=>{localStorage.setItem("access_token",e)},s=e=>{localStorage.setItem("refresh_token",e)}}}]);