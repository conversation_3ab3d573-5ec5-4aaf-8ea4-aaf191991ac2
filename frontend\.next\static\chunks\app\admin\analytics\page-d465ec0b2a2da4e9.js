(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1093],{1243:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},5040:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},17580:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},19946:(e,t,s)=>{"use strict";s.d(t,{A:()=>h});var a=s(12115);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),d=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return t.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim()},n=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,a.forwardRef)((e,t)=>{let{color:s="currentColor",size:r=24,strokeWidth:l=2,absoluteStrokeWidth:d,className:o="",children:h,iconNode:m,...u}=e;return(0,a.createElement)("svg",{ref:t,...c,width:r,height:r,stroke:s,strokeWidth:d?24*Number(l)/Number(r):l,className:i("lucide",o),...!h&&!n(u)&&{"aria-hidden":"true"},...u},[...m.map(e=>{let[t,s]=e;return(0,a.createElement)(t,s)}),...Array.isArray(h)?h:[h]])}),h=(e,t)=>{let s=(0,a.forwardRef)((s,l)=>{let{className:n,...c}=s;return(0,a.createElement)(o,{ref:l,iconNode:t,className:i("lucide-".concat(r(d(e))),"lucide-".concat(e),n),...c})});return s.displayName=d(e),s}},20677:(e,t,s)=>{Promise.resolve().then(s.bind(s,89672))},29869:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},33109:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},33786:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},40646:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},53904:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},54416:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},69037:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},69074:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},71007:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},72713:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},75525:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},76517:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]])},81284:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},85339:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},87949:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("graduation-cap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]])},89672:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>g});var a=s(95155),r=s(12115),l=s(17580),d=s(5040),i=s(69037),n=s(87949),c=s(53904),o=s(33109),h=s(72713),m=s(69074);let u=(0,s(19946).A)("chart-pie",[["path",{d:"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z",key:"pzmjnu"}],["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}]]);var p=s(69327);let x={formatGPA:e=>e?parseFloat(e).toFixed(2):"0.00",getPerformanceCategoryColor:e=>({high_performers:"#10B981",good_performers:"#3B82F6",average_performers:"#F59E0B",poor_performers:"#EF4444"})[e]||"#6B7280",sortDepartmentsByCount:e=>[...e].sort((e,t)=>t.total_students-e.total_students),sortYearsChronologically:e=>[...e].sort((e,t)=>(e.passout_year||0)-(t.passout_year||0))},y=()=>{var e,t,s,y;let[g,v]=(0,r.useState)(!0),[f,j]=(0,r.useState)(null),[k,N]=(0,r.useState)(!1),[b,w]=(0,r.useState)({enhanced:null,departments:null,years:null,performance:null}),A=async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{v(!0),j(null);let t=await p.n0.getAllStudentAnalytics(e);t.success?(w(t.data),t.data.errors&&t.data.errors.length>0&&console.warn("Some metrics failed to load:",t.data.errors)):j(t.error||"Failed to load student analytics")}catch(e){console.error("Error fetching student analytics:",e),j("Failed to load student analytics. Please try again.")}finally{v(!1)}},_=async()=>{N(!0);try{let e=await p.n0.refreshAllMetrics();e.success?await A(!0):j(e.error||"Failed to refresh metrics")}catch(e){console.error("Error refreshing metrics:",e),j("Failed to refresh metrics. Please try again.")}finally{N(!1)}};if((0,r.useEffect)(()=>{A()},[]),g)return(0,a.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,a.jsx)("span",{className:"ml-2 text-gray-600",children:"Loading student analytics..."})]});if(f)return(0,a.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,a.jsx)("p",{className:"text-red-600",children:f}),(0,a.jsx)("button",{onClick:()=>A(),className:"mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700",children:"Try Again"})]});let{enhanced:M,departments:C,years:L,performance:P}=b,z=(null==M?void 0:M.overview)||{},S=(null==C?void 0:C.departments)||[],E=(null==L?void 0:L.years)||[],F=[{title:"Total Students",value:(null==(e=z.total_students)?void 0:e.toLocaleString())||"0",icon:(0,a.jsx)(l.A,{className:"w-6 h-6"}),color:"bg-blue-500",description:"Registered students",trend:z.high_performer_percentage>20?"up":"down"},{title:"Departments",value:z.active_departments||"0",icon:(0,a.jsx)(d.A,{className:"w-6 h-6"}),color:"bg-green-500",description:"Active departments",trend:"up"},{title:"High Performers",value:(null==(t=z.high_performers)?void 0:t.toLocaleString())||"0",icon:(0,a.jsx)(i.A,{className:"w-6 h-6"}),color:"bg-purple-500",description:"".concat((null==(s=z.high_performer_percentage)?void 0:s.toFixed(1))||0,"% of students"),trend:z.high_performer_percentage>25?"up":"down"},{title:"Placement Ready",value:(null==(y=z.placement_ready)?void 0:y.toLocaleString())||"0",icon:(0,a.jsx)(n.A,{className:"w-6 h-6"}),color:"bg-orange-500",description:"Current year eligible",trend:"up"}],q=x.sortDepartmentsByCount(S).slice(0,5),D=x.sortYearsChronologically(E).slice(-5);return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Student Analytics"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Comprehensive overview of student data and performance"})]}),(0,a.jsxs)("button",{onClick:_,disabled:k,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50",children:[(0,a.jsx)(c.A,{className:"w-4 h-4 mr-2 ".concat(k?"animate-spin":"")}),k?"Refreshing...":"Refresh Data"]})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:F.map((e,t)=>(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e.title}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.value}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:e.description})]}),(0,a.jsx)("div",{className:"".concat(e.color," p-3 rounded-lg text-white"),children:e.icon})]}),(0,a.jsxs)("div",{className:"mt-4 flex items-center",children:["up"===e.trend?(0,a.jsx)(o.A,{className:"w-4 h-4 text-green-500 mr-1"}):(0,a.jsx)(o.A,{className:"w-4 h-4 text-red-500 mr-1 transform rotate-180"}),(0,a.jsx)("span",{className:"text-sm ".concat("up"===e.trend?"text-green-600":"text-red-600"),children:"Performance indicator"})]})]},t))}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Top Departments"}),(0,a.jsx)(h.A,{className:"w-5 h-5 text-gray-400"})]}),(0,a.jsx)("div",{className:"space-y-4",children:q.map((e,t)=>{var s;return(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e.branch||"Unknown Department"}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:[e.total_students," students"]})]}),(0,a.jsx)("div",{className:"mt-1 bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-500 h-2 rounded-full",style:{width:"".concat(e.total_students/((null==(s=q[0])?void 0:s.total_students)||1)*100,"%")}})}),(0,a.jsxs)("div",{className:"mt-1 flex justify-between text-xs text-gray-500",children:[(0,a.jsxs)("span",{children:["Avg GPA: ",x.formatGPA(e.avg_gpa)]}),(0,a.jsxs)("span",{children:["High Performers: ",e.high_performers||0]})]})]})},t)})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Year-wise Distribution"}),(0,a.jsx)(m.A,{className:"w-5 h-5 text-gray-400"})]}),(0,a.jsx)("div",{className:"space-y-4",children:D.map((e,t)=>{var s,r;return(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e.passout_year||"Unknown Year"}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:[e.total_students," students"]})]}),(0,a.jsx)("div",{className:"mt-1 bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-green-500 h-2 rounded-full",style:{width:"".concat(e.total_students/((null==(s=D[0])?void 0:s.total_students)||1)*100,"%")}})}),(0,a.jsxs)("div",{className:"mt-1 flex justify-between text-xs text-gray-500",children:[(0,a.jsxs)("span",{children:["Avg GPA: ",x.formatGPA(e.avg_gpa)]}),(0,a.jsxs)("span",{children:["Placement Rate: ",(null==(r=e.placement_rate)?void 0:r.toFixed(1))||0,"%"]})]})]})},t)})})]})]}),P&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Performance Analytics"}),(0,a.jsx)(u,{className:"w-5 h-5 text-gray-400"})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:Object.entries(P.performance_categories||{}).map((e,t)=>{var s;let[r,l]=e;return(0,a.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:l}),(0,a.jsx)("div",{className:"text-sm text-gray-600 capitalize",children:r.replace("_"," ")}),(0,a.jsx)("div",{className:"mt-2 h-2 bg-gray-200 rounded",children:(0,a.jsx)("div",{className:"h-2 rounded",style:{width:"".concat(l/((null==(s=P.overall_performance)?void 0:s.total_students)||1)*100,"%"),backgroundColor:x.getPerformanceCategoryColor(r)}})})]},r)})})]}),(0,a.jsxs)("div",{className:"text-center text-sm text-gray-500",children:["Last updated: ",(null==M?void 0:M.last_updated)?new Date(M.last_updated).toLocaleString():"Unknown"]})]})};function g(){return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsx)(y,{})})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[3464,3983,9327,8441,1684,7358],()=>t(20677)),_N_E=e.O()}]);