(()=>{var e={};e.id=2758,e.ids=[2758,4335],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},14335:(e,t,r)=>{"use strict";r.d(t,{C1:()=>n,Gu:()=>h,JT:()=>o,RC:()=>c,S0:()=>f,Y_:()=>p,bl:()=>u,dl:()=>g,eK:()=>l,fetchCompanies:()=>i,getCompanyStats:()=>d,jQ:()=>m,mm:()=>a,oY:()=>x});var s=r(58138);function a(e={}){let t=new URLSearchParams;e.page&&t.append("page",e.page),e.per_page&&t.append("per_page",e.per_page),e.tier&&"ALL"!==e.tier&&t.append("tier",e.tier),e.industry&&"ALL"!==e.industry&&t.append("industry",e.industry),e.campus_recruiting&&t.append("campus_recruiting",e.campus_recruiting),e.search&&t.append("search",e.search),e.sort&&t.append("sort",e.sort),t.append("_t",new Date().getTime());let r=t.toString(),i=[`/api/v1/companies/${r?`?${r}`:""}`,`/api/v1/college/default-college/companies/${r?`?${r}`:""}`];return s.A.get(i[0]).catch(e=>(console.log(`Primary endpoint failed: ${e.message}, trying fallback...`),s.A.get(i[1])))}async function i(e={}){try{console.log("Fetching companies from API...");let t=await a(e),r=[];if(t.data&&Array.isArray(t.data)?r=t.data:t.data&&t.data.results&&Array.isArray(t.data.results)?r=t.data.results:t.data&&t.data.data&&Array.isArray(t.data.data)&&(r=t.data.data),console.log(`Retrieved ${r.length} companies from API`),r.length>0)return await Promise.all(r.map(async e=>{try{let t=await n(e.id);return p(t.data)}catch(t){return console.log(`Could not fetch details for company ${e.id}:`,t),p(e)}}));throw Error("No companies returned from API")}catch(t){console.error("Error fetching companies from API:",t),console.log("Falling back to static company data");let{companies:e}=await r.e(1286).then(r.bind(r,61286));return e}}function n(e){let t=[`/api/v1/company/${e}/`,`/api/v1/companies/${e}/`,`/api/v1/college/default-college/companies/${e}/`];return s.A.get(t[0]).catch(e=>(console.log(`First company endpoint failed: ${e.message}, trying second...`),s.A.get(t[1]).catch(e=>(console.log(`Second company endpoint failed: ${e.message}, trying third...`),s.A.get(t[2])))))}function l(e){let t=new FormData;return Object.keys(e).forEach(r=>{"logo"===r&&e[r]instanceof File?t.append(r,e[r]):null!==e[r]&&void 0!==e[r]&&t.append(r,e[r])}),s.A.post("/api/v1/companies/",t,{headers:{"Content-Type":"multipart/form-data"}})}function o(e,t){let r=new FormData;return Object.keys(t).forEach(e=>{"logo"===e&&t[e]instanceof File?r.append(e,t[e]):null!==t[e]&&void 0!==t[e]&&r.append(e,t[e])}),s.A.put(`/api/v1/companies/${e}/`,r,{headers:{"Content-Type":"multipart/form-data"}})}function c(e){return s.A.delete(`/api/v1/companies/${e}/`)}function d(){return s.A.get("/api/v1/companies/stats/")}function p(e){return{id:e.id,name:e.name,logo:e.logo||`https://via.placeholder.com/48x48/4285F4/FFFFFF?text=${e.name.charAt(0)}`,description:e.description||"",industry:e.industry||"",size:e.size||"",founded:e.founded||"",location:e.location||"Location not specified",website:e.website||"",tier:e.tier||"Tier 3",campus_recruiting:e.campus_recruiting||!1,totalActiveJobs:e.total_active_jobs||0,totalApplicants:e.total_applicants||0,totalHired:e.total_hired||0,awaitedApproval:e.pending_approval||0}}function u(e){return s.A.get(`/api/v1/companies/${e}/followers/count/`)}function m(e,t){return s.A.post(`/api/v1/companies/${e}/followers/`,{user_id:t})}function x(e,t){return s.A.delete(`/api/v1/companies/${e}/followers/`,{data:{user_id:t}})}function h(e,t){return s.A.get(`/api/v1/companies/${e}/followers/status/?user_id=${t}`)}function g(e){return s.A.get(`/api/v1/users/${e}/following/`)}function f(){return s.A.get("/api/v1/companies/simple/").then(e=>{if(e.data&&e.data.success)return e.data;throw Error("Failed to fetch companies")}).catch(e=>(console.error("Error fetching companies:",e),{success:!1,data:[],count:0,error:e.message}))}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28484:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>c});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let c={children:["",{children:["companies",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,64486)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\companies\\page.jsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,75535)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\layout.js"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\companies\\page.jsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/companies/page",pathname:"/companies",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33255:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("bookmark-check",[["path",{d:"m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2Z",key:"169p4p"}],["path",{d:"m9 10 2 2 4-4",key:"1gnqz4"}]])},33873:e=>{"use strict";e.exports=require("path")},40228:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41312:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},49014:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("bookmark",[["path",{d:"m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z",key:"1fy3hk"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58138:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(51060);r(51421);let a=s.A.create({baseURL:"http://127.0.0.1:8000",headers:{"Content-Type":"application/json"}});a.interceptors.request.use(e=>{let t=localStorage.getItem("access_token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e)),a.interceptors.response.use(e=>e,async e=>{let t=e.config;if(e.response?.status===401&&!t._retry){t._retry=!0;try{let e=localStorage.getItem("refresh_token");if(e){let r=await s.A.post("http://127.0.0.1:8000/api/auth/token/refresh/",{refresh:e});return localStorage.setItem("access_token",r.data.access),t.headers.Authorization=`Bearer ${r.data.access}`,a(t)}}catch(e){console.error("Error refreshing token:",e)}}return Promise.reject(e)});let i=a},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63787:(e,t,r)=>{"use strict";function s(){try{let e=localStorage.getItem("access");if(!e)return null;let t=localStorage.getItem("user");if(t){let e=JSON.parse(t);return e.id||e.user_id||null}if(e)try{let t=e.split(".")[1],r=JSON.parse(atob(t));return r.user_id||r.id||r.sub||null}catch(e){console.error("Error decoding JWT token:",e)}return null}catch(e){return console.error("Error getting user ID:",e),null}}function a(){return localStorage.getItem("access")}function i(){try{let e=localStorage.getItem("user");return e?JSON.parse(e):null}catch(e){return console.error("Error getting user data:",e),null}}r.d(t,{F6:()=>s,c4:()=>a,gL:()=>i})},64398:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},64486:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\companies\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\companies\\page.jsx","default")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},76800:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f});var s=r(60687),a=r(43210),i=r(16189),n=r(93613),l=r(79410),o=r(64398),c=r(49014),d=r(99270),p=r(33255),u=r(97992),m=r(41312),x=r(40228),h=r(14335),g=r(63787);function f(){let e=(0,i.useRouter)(),[t,r]=(0,a.useState)([]),[f,y]=(0,a.useState)(""),[b,v]=(0,a.useState)("ALL"),[j,N]=(0,a.useState)("ALL"),[w,A]=(0,a.useState)("name"),[k,C]=(0,a.useState)(new Set),[_,S]=(0,a.useState)(!0),[L,P]=(0,a.useState)(null),[$,M]=(0,a.useState)({total:0,tier1:0,tier2:0,tier3:0,campus_recruiting:0}),[q,T]=(0,a.useState)(1),E=[...new Set(t.map(e=>e.industry))],F=t.filter(e=>{let t=e.name.toLowerCase().includes(f.toLowerCase())||e.industry.toLowerCase().includes(f.toLowerCase())||e.description.toLowerCase().includes(f.toLowerCase()),r="ALL"===b||e.tier===b,s="ALL"===j||e.industry===j,a="bookmarks"!==w||k.has(e.id);return t&&r&&s&&a}).sort((e,t)=>{if("name"===w)return e.name.localeCompare(t.name);if("jobs"===w)return t.totalActiveJobs-e.totalActiveJobs;if("applicants"===w)return t.totalApplicants-e.totalApplicants;if("tier"===w)return e.tier.localeCompare(t.tier);if("bookmarks"===w)return e.name.localeCompare(t.name);return 0}),z=Math.ceil(F.length/8),D=8*q,I=F.slice(D-8,D),J=e=>T(e),O=async(e,t)=>{e.stopPropagation();try{let e=(0,g.F6)();if(!e)return void alert("Please log in to follow companies");let r=new Set(k);r.has(t)?(await (0,h.oY)(t,e),r.delete(t)):(await (0,h.jQ)(t,e),r.add(t)),C(r)}catch(e){console.error("Error updating follow status:",e),alert("Failed to update follow status. Please try again.")}},R=e=>{switch(e){case"Tier 1":return"bg-emerald-100 text-emerald-800 border-emerald-200";case"Tier 2":return"bg-blue-100 text-blue-800 border-blue-200";case"Tier 3":return"bg-purple-100 text-purple-800 border-purple-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}},G=t=>{e.push(`/company/${t}`)};return _?(0,s.jsxs)("div",{className:"h-full flex flex-col items-center justify-center",children:[(0,s.jsxs)("div",{className:"relative w-16 h-16 mb-8",children:[(0,s.jsx)("div",{className:"absolute inset-0 border-t-4 border-blue-500 rounded-full animate-spin"}),(0,s.jsx)("div",{className:"absolute inset-2 border-r-4 border-transparent rounded-full"})]}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:"Loading companies..."})]}):L?(0,s.jsx)("div",{className:"h-full flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center max-w-md",children:[(0,s.jsx)("div",{className:"text-red-500 mb-4",children:(0,s.jsx)(n.A,{className:"w-16 h-16 mx-auto"})}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Error Loading Companies"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:L}),(0,s.jsx)("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Retry"})]})}):(0,s.jsx)("div",{className:"h-full overflow-y-auto",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Explore Companies"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Discover top companies and exciting career opportunities"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4 mb-8",children:[(0,s.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-100",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,s.jsx)(l.A,{className:"w-5 h-5 text-blue-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:$.total}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Total Companies"})]})]})}),(0,s.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-100",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 bg-emerald-100 rounded-lg",children:(0,s.jsx)(o.A,{className:"w-5 h-5 text-emerald-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:$.tier1}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Tier 1"})]})]})}),(0,s.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-100",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,s.jsx)(o.A,{className:"w-5 h-5 text-blue-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:$.tier2}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Tier 2"})]})]})}),(0,s.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-100",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 bg-amber-100 rounded-lg",children:(0,s.jsx)(o.A,{className:"w-5 h-5 text-amber-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:$.tier3}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Tier 3"})]})]})}),(0,s.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-100",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,s.jsx)(c.A,{className:"w-5 h-5 text-purple-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:k.size}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Bookmarks"})]})]})})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-100",children:[(0,s.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,s.jsxs)("div",{className:"flex-1 relative",children:[(0,s.jsx)(d.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,s.jsx)("input",{type:"text",placeholder:"Search companies, industries...",value:f,onChange:e=>y(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsxs)("select",{value:b,onChange:e=>v(e.target.value),className:"px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[120px]",children:[(0,s.jsx)("option",{value:"ALL",children:"All Tiers"}),(0,s.jsx)("option",{value:"Tier 1",children:"Tier 1"}),(0,s.jsx)("option",{value:"Tier 2",children:"Tier 2"}),(0,s.jsx)("option",{value:"Tier 3",children:"Tier 3"})]}),(0,s.jsxs)("select",{value:j,onChange:e=>N(e.target.value),className:"px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[150px]",children:[(0,s.jsx)("option",{value:"ALL",children:"All Industries"}),E.map(e=>(0,s.jsx)("option",{value:e,children:e},e))]}),(0,s.jsxs)("select",{value:w,onChange:e=>A(e.target.value),className:"px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[130px]",children:[(0,s.jsx)("option",{value:"name",children:"Company A-Z"}),(0,s.jsx)("option",{value:"jobs",children:"Most Jobs"}),(0,s.jsx)("option",{value:"applicants",children:"Most Popular"}),(0,s.jsx)("option",{value:"bookmarks",children:"Bookmarked"}),(0,s.jsx)("option",{value:"tier",children:"Tier"})]})]})]}),(0,s.jsxs)("div",{className:"mt-4 text-sm text-gray-600",children:["Showing ",F.length," of ",t.length," companies"]})]})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:I.length>0?I.map(e=>(0,s.jsxs)("div",{onClick:()=>G(e.id),className:"bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-lg transition-all duration-200 cursor-pointer group",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-white font-bold text-lg",children:e.name.charAt(0)})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-gray-900 group-hover:text-blue-600 transition-colors",children:e.name}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:e.industry})]})]}),(0,s.jsx)("button",{onClick:t=>O(t,e.id),className:"p-2 rounded-lg transition-colors hover:bg-gray-100",children:k.has(e.id)?(0,s.jsx)(p.A,{className:"w-5 h-5 text-blue-600"}):(0,s.jsx)(c.A,{className:"w-5 h-5 text-gray-500"})})]}),(0,s.jsxs)("div",{className:"space-y-3 mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[(0,s.jsx)(u.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:e.location})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[(0,s.jsx)(m.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:e.size})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[(0,s.jsx)(x.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:e.founded?`Founded ${e.founded}`:"Founded year not specified"})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,s.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium border ${R(e.tier)}`,children:e.tier}),e.campus_recruiting&&(0,s.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-700 border border-green-200",children:"Campus Recruiting"})]}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mb-4 line-clamp-3",children:e.description}),(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-4 pt-4 border-t border-gray-100",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-lg font-bold text-gray-900",children:e.totalActiveJobs}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Active Jobs"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-lg font-bold text-gray-900",children:e.totalApplicants}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Applicants"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-lg font-bold text-gray-900",children:e.totalHired}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Hired"})]})]})]},e.id)):(0,s.jsxs)("div",{className:"col-span-full flex flex-col items-center justify-center py-16",children:[(0,s.jsx)("div",{className:"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4",children:(0,s.jsx)(l.A,{className:"w-12 h-12 text-gray-400"})}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"No companies found"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"Try adjusting your search or filters"}),(0,s.jsx)("button",{onClick:()=>{y(""),v("ALL"),N("ALL"),A("name")},className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Clear Filters"})]})}),F.length>0&&(0,s.jsx)("div",{className:"mt-8 flex justify-center",children:(0,s.jsxs)("nav",{className:"flex items-center space-x-2","aria-label":"Pagination",children:[(0,s.jsx)("button",{onClick:()=>T(e=>Math.max(e-1,1)),disabled:1===q,className:`px-4 py-2 rounded-md border ${1===q?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 hover:bg-gray-100"}`,children:"Previous"}),[...Array(Math.min(5,z))].map((e,t)=>{let r;return r=z<=5||q<=3?t+1:q>=z-2?z-4+t:q-2+t,(0,s.jsx)("button",{onClick:()=>J(r),className:`w-10 h-10 flex items-center justify-center rounded-md ${q===r?"bg-blue-500 text-white":"text-gray-700 hover:bg-gray-100"}`,"aria-current":q===r?"page":void 0,children:r},r)}),(0,s.jsx)("button",{onClick:()=>T(e=>Math.min(e+1,z)),disabled:q===z,className:`px-4 py-2 rounded-md border ${q===z?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 hover:bg-gray-100"}`,children:"Next"})]})})]})})}},77297:(e,t,r)=>{Promise.resolve().then(r.bind(r,76800))},77969:(e,t,r)=>{Promise.resolve().then(r.bind(r,64486))},79410:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},97992:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},99270:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,681,1658,1060,2305],()=>r(28484));module.exports=s})();