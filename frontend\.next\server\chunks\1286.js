"use strict";exports.id=1286,exports.ids=[1286],exports.modules={61286:(t,a,e)=>{e.d(a,{Ly:()=>n,_N:()=>i,companies:()=>r,zZ:()=>s});var o=e(14335);let r=[{id:1,name:"TechCorp Inc",description:"Leading technology solutions provider",industry:"Technology",size:"500-1000",founded:"2010",website:"https://techcorp.com"},{id:2,name:"DataCorp",description:"Data analytics and insights company",industry:"Data Analytics",size:"100-500",founded:"2015",website:"https://datacorp.com"}],n=[{id:1,job_id:25,title:"Software Engineer",company:"TechCorp Inc",status:"APPLIED",application_deadline:"2024-05-30T23:59:59Z"},{id:2,job_id:26,title:"Data Scientist",company:"DataCorp",status:"INTERVIEW SCHEDULED",application_deadline:"2024-06-15T23:59:59Z"}],i=async(t={})=>{try{let a={...t,_t:new Date().getTime()};console.log("Fetching companies with cache busting...");let e=await (0,o.mm)(a),n=[];e.data&&Array.isArray(e.data)?n=e.data:e.data&&e.data.results&&Array.isArray(e.data.results)?n=e.data.results:e.data&&e.data.data&&Array.isArray(e.data.data)&&(n=e.data.data);let i=n.map(o.Y_);if(console.log(`Fetched ${i.length} companies from API`),0===i.length)return console.warn("API returned empty companies array, using static data"),r;return i}catch(t){console.error("Error fetching companies:",t);try{console.log("Trying alternate endpoint format...");let t=await fetch("/api/v1/college/default-college/companies/");if(t.ok){let a=await t.json(),e=Array.isArray(a)?a:a.data||a.results||[];if(e.length>0)return console.log("Successfully retrieved companies from alternate endpoint"),e.map(o.Y_)}}catch(t){console.error("Alternate endpoint also failed:",t)}return r}};function s(t){return console.log(`Fetching jobs for company ID: ${t}`),[]}}};