{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/utils/cn.ts"], "sourcesContent": ["import { clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: any) {\n  return twMerge(clsx(inputs));\n}"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAW;IAC/B,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/components/ui/Sidebar.jsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useRef, useEffect, createContext } from 'react';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport { useRouter } from 'next/navigation';\r\nimport Link from 'next/link';\r\nimport { IconUser, IconMenu2, IconX } from '@tabler/icons-react';\r\nimport { cn } from '../../utils/cn';\r\n\r\nconst SidebarContext = createContext();\r\n\r\nexport default function Sidebar({\r\n  sections = [],\r\n  bottomItems = [],\r\n  defaultExpanded = false,\r\n  navbarHeight = '4rem',\r\n  className,\r\n}) {\r\n  const [expanded, setExpanded] = useState(defaultExpanded);\r\n  const [isMobile, setIsMobile] = useState(false);\r\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\r\n\r\n  // Check if mobile screen\r\n  useEffect(() => {\r\n    const checkMobile = () => {\r\n      setIsMobile(window.innerWidth < 768);\r\n    };\r\n    \r\n    checkMobile();\r\n    window.addEventListener('resize', checkMobile);\r\n    return () => window.removeEventListener('resize', checkMobile);\r\n  }, []);\r\n\r\n  // Helper function to render icons consistently\r\n  const renderIcon = (icon) => {\r\n    if (React.isValidElement(icon)) {\r\n      // Clone the icon and ensure consistent sizing\r\n      return React.cloneElement(icon, {\r\n        className: 'w-7 h-7',\r\n        size: undefined // Remove size prop if it exists\r\n      });\r\n    }\r\n    return icon;\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {/* Hamburger Menu Button - Mobile only */}\r\n      {isMobile && (\r\n        <button\r\n          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\r\n          className=\"fixed top-4 left-4 z-50 p-2 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow md:hidden\"\r\n          style={{ marginTop: navbarHeight }}\r\n        >\r\n          {mobileMenuOpen ? <IconX size={24} /> : <IconMenu2 size={24} />}\r\n        </button>\r\n      )}\r\n\r\n      <SidebarContext.Provider value={{ expanded }}>\r\n        <motion.div\r\n          animate={{ width: expanded ? '300px' : '80px' }}\r\n          onMouseEnter={() => setExpanded(true)}\r\n          onMouseLeave={() => setExpanded(false)}\r\n          className={cn(\r\n            'fixed top-0 left-0 h-screen bg-white px-4 py-4 shadow-lg rounded-r-3xl flex flex-col justify-between sidebar',\r\n            // Hide on mobile unless menu is open\r\n            isMobile && !mobileMenuOpen ? 'hidden' : '',\r\n            className\r\n          )}\r\n          style={{ marginTop: navbarHeight }}\r\n        >\r\n        <nav className=\"flex flex-col gap-8\">\r\n          {/* Logo */}\r\n          <Link\r\n            href=\"/\"\r\n            className={cn(\r\n              'flex items-center gap-4 p-3 text-black',\r\n              !expanded && 'justify-center'\r\n            )}\r\n          >\r\n            <div className=\"flex-shrink-0 text-2xl font-bold\">\r\n              {!expanded ? 'P' : null}\r\n            </div>\r\n            {expanded && (\r\n              <motion.span\r\n                initial={{ opacity: 0 }}\r\n                animate={{ opacity: 1 }}\r\n                exit={{ opacity: 0 }}\r\n                className=\"text-2xl font-bold whitespace-nowrap\"\r\n              >\r\n                Placeeasy.in\r\n              </motion.span>\r\n            )}\r\n          </Link>\r\n\r\n          {/* Render top sections */}\r\n          {sections.map((section, sectionIndex) => (\r\n            <div key={sectionIndex} className=\"bg-gray-50 rounded-xl p-2\">\r\n              <AnimatePresence>\r\n                {section.items.map((item) => (\r\n                  <Link\r\n                    key={item.title}\r\n                    href={item.href}\r\n                    className={cn(\r\n                      'flex items-center gap-4 p-3 text-gray-600 hover:bg-blue-50 hover:text-blue-600 rounded-xl transition-colors',\r\n                      !expanded && 'justify-center'\r\n                    )}\r\n                  >\r\n                    <div className=\"flex-shrink-0 flex items-center justify-center\">\r\n                      {renderIcon(item.icon)}\r\n                    </div>\r\n                    {expanded && (\r\n                      <motion.span\r\n                        initial={{ opacity: 0 }}\r\n                        animate={{ opacity: 1 }}\r\n                        exit={{ opacity: 0 }}\r\n                        className=\"text-base font-bold whitespace-nowrap\"\r\n                      >\r\n                        {item.title}\r\n                      </motion.span>\r\n                    )}\r\n                  </Link>\r\n                ))}\r\n              </AnimatePresence>\r\n            </div>\r\n          ))}\r\n        </nav>\r\n\r\n        {/* Bottom Items */}\r\n        {bottomItems.length > 0 && (\r\n          <div className=\"mt-6\">\r\n            {bottomItems.map((item) => (\r\n              <Link\r\n                key={item.title}\r\n                href={item.href}\r\n                className={cn(\r\n                  'flex items-center gap-4 p-3 text-gray-600 hover:bg-blue-50 hover:text-blue-600 rounded-xl transition-colors mb-2',\r\n                  !expanded && 'justify-center'\r\n                )}\r\n              >\r\n                <div className=\"flex-shrink-0 flex items-center justify-center\">\r\n                  {renderIcon(item.icon)}\r\n                </div>\r\n                {expanded && (\r\n                  <motion.span\r\n                    initial={{ opacity: 0 }}\r\n                    animate={{ opacity: 1 }}\r\n                    exit={{ opacity: 0 }}\r\n                    className=\"text-base font-bold whitespace-nowrap\"\r\n                  >\r\n                    {item.title}\r\n                  </motion.span>\r\n                )}\r\n              </Link>\r\n            ))}\r\n          </div>\r\n        )}\r\n        </motion.div>\r\n      </SidebarContext.Provider>\r\n    </>\r\n  );\r\n}\r\n\r\nexport function MenuBar({ menuItems = [], onItemClick }) {\r\n  const [selectedItem, setSelectedItem] = useState(menuItems[0]?.label);\r\n\r\n  const handleClick = (item) => {\r\n    setSelectedItem(item.label);\r\n    onItemClick?.(item);\r\n  };\r\n\r\n  return (\r\n    <div className=\"w-48 bg-white h-full rounded-xl overflow-auto border-r\">\r\n      <ul className=\"space-y-6 p-6\">\r\n        {menuItems.map((item, index) => (\r\n          <li\r\n            key={index}\r\n            className=\"cursor-pointer text-gray-700 text-lg \r\n                       hover:text-blue-500 hover:scale-105 \r\n                       transition-transform duration-200\"\r\n            onClick={() => onItemClick?.(item)}\r\n          >\r\n            {item.label}\r\n          </li>\r\n        ))}\r\n      </ul>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAPA;;;;;;;;AASA,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD;AAEpB,SAAS,QAAQ,EAC9B,WAAW,EAAE,EACb,cAAc,EAAE,EAChB,kBAAkB,KAAK,EACvB,eAAe,MAAM,EACrB,SAAS,EACV;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc;YAClB,YAAY,OAAO,UAAU,GAAG;QAClC;QAEA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,+CAA+C;IAC/C,MAAM,aAAa,CAAC;QAClB,kBAAI,qMAAA,CAAA,UAAK,CAAC,cAAc,CAAC,OAAO;YAC9B,8CAA8C;YAC9C,qBAAO,qMAAA,CAAA,UAAK,CAAC,YAAY,CAAC,MAAM;gBAC9B,WAAW;gBACX,MAAM,UAAU,gCAAgC;YAClD;QACF;QACA,OAAO;IACT;IAEA,qBACE;;YAEG,0BACC,8OAAC;gBACC,SAAS,IAAM,kBAAkB,CAAC;gBAClC,WAAU;gBACV,OAAO;oBAAE,WAAW;gBAAa;0BAEhC,+BAAiB,8OAAC,gNAAA,CAAA,QAAK;oBAAC,MAAM;;;;;yCAAS,8OAAC,wNAAA,CAAA,YAAS;oBAAC,MAAM;;;;;;;;;;;0BAI7D,8OAAC,eAAe,QAAQ;gBAAC,OAAO;oBAAE;gBAAS;0BACzC,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,OAAO,WAAW,UAAU;oBAAO;oBAC9C,cAAc,IAAM,YAAY;oBAChC,cAAc,IAAM,YAAY;oBAChC,WAAW,CAAA,GAAA,kHAAA,CAAA,KAAE,AAAD,EACV,gHACA,qCAAqC;oBACrC,YAAY,CAAC,iBAAiB,WAAW,IACzC;oBAEF,OAAO;wBAAE,WAAW;oBAAa;;sCAEnC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAW,CAAA,GAAA,kHAAA,CAAA,KAAE,AAAD,EACV,0CACA,CAAC,YAAY;;sDAGf,8OAAC;4CAAI,WAAU;sDACZ,CAAC,WAAW,MAAM;;;;;;wCAEpB,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;4CACV,SAAS;gDAAE,SAAS;4CAAE;4CACtB,SAAS;gDAAE,SAAS;4CAAE;4CACtB,MAAM;gDAAE,SAAS;4CAAE;4CACnB,WAAU;sDACX;;;;;;;;;;;;gCAOJ,SAAS,GAAG,CAAC,CAAC,SAAS,6BACtB,8OAAC;wCAAuB,WAAU;kDAChC,cAAA,8OAAC,yLAAA,CAAA,kBAAe;sDACb,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,8OAAC,4JAAA,CAAA,UAAI;oDAEH,MAAM,KAAK,IAAI;oDACf,WAAW,CAAA,GAAA,kHAAA,CAAA,KAAE,AAAD,EACV,+GACA,CAAC,YAAY;;sEAGf,8OAAC;4DAAI,WAAU;sEACZ,WAAW,KAAK,IAAI;;;;;;wDAEtB,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;4DACV,SAAS;gEAAE,SAAS;4DAAE;4DACtB,SAAS;gEAAE,SAAS;4DAAE;4DACtB,MAAM;gEAAE,SAAS;4DAAE;4DACnB,WAAU;sEAET,KAAK,KAAK;;;;;;;mDAjBV,KAAK,KAAK;;;;;;;;;;uCAJb;;;;;;;;;;;wBAgCb,YAAY,MAAM,GAAG,mBACpB,8OAAC;4BAAI,WAAU;sCACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAA,GAAA,kHAAA,CAAA,KAAE,AAAD,EACV,oHACA,CAAC,YAAY;;sDAGf,8OAAC;4CAAI,WAAU;sDACZ,WAAW,KAAK,IAAI;;;;;;wCAEtB,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;4CACV,SAAS;gDAAE,SAAS;4CAAE;4CACtB,SAAS;gDAAE,SAAS;4CAAE;4CACtB,MAAM;gDAAE,SAAS;4CAAE;4CACnB,WAAU;sDAET,KAAK,KAAK;;;;;;;mCAjBV,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;AA4B/B;AAEO,SAAS,QAAQ,EAAE,YAAY,EAAE,EAAE,WAAW,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,CAAC,EAAE,EAAE;IAE/D,MAAM,cAAc,CAAC;QACnB,gBAAgB,KAAK,KAAK;QAC1B,cAAc;IAChB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAG,WAAU;sBACX,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC;oBAEC,WAAU;oBAGV,SAAS,IAAM,cAAc;8BAE5B,KAAK,KAAK;mBANN;;;;;;;;;;;;;;;AAYjB", "debugId": null}}, {"offset": {"line": 327, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/contexts/ThemeContext.jsx"], "sourcesContent": ["'use client';\r\n\r\nimport { createContext, useContext, useEffect, useState } from 'react';\r\n\r\nconst ThemeContext = createContext();\r\n\r\nexport const useTheme = () => {\r\n  const context = useContext(ThemeContext);\r\n  if (!context) {\r\n    throw new Error('useTheme must be used within a ThemeProvider');\r\n  }\r\n  return context;\r\n};\r\n\r\nexport const ThemeProvider = ({ children }) => {\r\n  const [theme, setTheme] = useState('system');\r\n  const [resolvedTheme, setResolvedTheme] = useState('light');\r\n\r\n  // Load theme from localStorage on mount\r\n  useEffect(() => {\r\n    if (typeof window !== 'undefined') {\r\n      const savedTheme = localStorage.getItem('userTheme') || 'system';\r\n      setTheme(savedTheme);\r\n    }\r\n  }, []);\r\n\r\n  // Apply theme to document and resolve system theme\r\n  useEffect(() => {\r\n    if (typeof window !== 'undefined') {\r\n      const applyTheme = (themeToApply) => {\r\n        const root = document.documentElement;\r\n        const body = document.body;\r\n        \r\n        // Remove existing theme classes\r\n        body.classList.remove('dark-mode', 'light-mode');\r\n        root.removeAttribute('data-theme');\r\n        \r\n        let actualTheme = themeToApply;\r\n        \r\n        // Handle system theme\r\n        if (themeToApply === 'system') {\r\n          const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\r\n          actualTheme = prefersDark ? 'dark' : 'light';\r\n        }\r\n        \r\n        // Apply theme\r\n        if (actualTheme === 'dark') {\r\n          body.classList.add('dark-mode');\r\n          root.setAttribute('data-theme', 'dark');\r\n        } else {\r\n          body.classList.add('light-mode');\r\n          root.setAttribute('data-theme', 'light');\r\n        }\r\n        \r\n        setResolvedTheme(actualTheme);\r\n      };\r\n\r\n      applyTheme(theme);\r\n\r\n      // Listen for system theme changes when using system theme\r\n      if (theme === 'system') {\r\n        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\r\n        const handleChange = (e) => {\r\n          applyTheme('system');\r\n        };\r\n        \r\n        mediaQuery.addEventListener('change', handleChange);\r\n        return () => mediaQuery.removeEventListener('change', handleChange);\r\n      }\r\n    }\r\n  }, [theme]);\r\n\r\n  const changeTheme = (newTheme) => {\r\n    setTheme(newTheme);\r\n    if (typeof window !== 'undefined') {\r\n      localStorage.setItem('userTheme', newTheme);\r\n    }\r\n  };\r\n\r\n  const value = {\r\n    theme,\r\n    resolvedTheme,\r\n    changeTheme,\r\n    isDark: resolvedTheme === 'dark',\r\n    isLight: resolvedTheme === 'light'\r\n  };\r\n\r\n  return (\r\n    <ThemeContext.Provider value={value}>\r\n      {children}\r\n    </ThemeContext.Provider>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAIA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD;AAE1B,MAAM,WAAW;IACtB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,MAAM,gBAAgB,CAAC,EAAE,QAAQ,EAAE;IACxC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAAmC;;QAGnC;IACF,GAAG,EAAE;IAEL,mDAAmD;IACnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAAmC;;QAyCnC;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,cAAc,CAAC;QACnB,SAAS;QACT,uCAAmC;;QAEnC;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA,QAAQ,kBAAkB;QAC1B,SAAS,kBAAkB;IAC7B;IAEA,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;kBAC3B;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 389, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/components/ui/ThemeToggle.jsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useTheme } from '../../contexts/ThemeContext';\r\nimport { Sun, Moon, Monitor } from 'lucide-react';\r\nimport { useState, useRef, useEffect } from 'react';\r\n\r\nconst ThemeToggle = () => {\r\n  const { theme, changeTheme, isDark } = useTheme();\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const dropdownRef = useRef(null);\r\n\r\n  const themes = [\r\n    { value: 'light', label: 'Light', icon: Sun },\r\n    { value: 'dark', label: 'Dark', icon: Moon },\r\n    { value: 'system', label: 'System', icon: Monitor }\r\n  ];\r\n\r\n  const currentTheme = themes.find(t => t.value === theme);\r\n\r\n  // Close dropdown when clicking outside\r\n  useEffect(() => {\r\n    const handleClickOutside = (event) => {\r\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\r\n        setIsOpen(false);\r\n      }\r\n    };\r\n\r\n    document.addEventListener('mousedown', handleClickOutside);\r\n    return () => document.removeEventListener('mousedown', handleClickOutside);\r\n  }, []);\r\n\r\n  const handleThemeChange = (newTheme) => {\r\n    changeTheme(newTheme);\r\n    setIsOpen(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"relative\" ref={dropdownRef}>\r\n      <button\r\n        onClick={() => setIsOpen(!isOpen)}\r\n        className={`\r\n          p-2 rounded-lg border transition-colors duration-200\r\n          ${isDark \r\n            ? 'bg-gray-800 border-gray-600 text-gray-200 hover:bg-gray-700' \r\n            : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'\r\n          }\r\n          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1\r\n        `}\r\n        title={`Current theme: ${currentTheme?.label}`}\r\n      >\r\n        {currentTheme && (\r\n          <currentTheme.icon className=\"w-5 h-5\" />\r\n        )}\r\n      </button>\r\n\r\n      {isOpen && (\r\n        <div className={`\r\n          absolute right-0 mt-2 w-32 rounded-lg shadow-lg border z-50\r\n          ${isDark \r\n            ? 'bg-gray-800 border-gray-600' \r\n            : 'bg-white border-gray-200'\r\n          }\r\n        `}>\r\n          <div className=\"py-1\">\r\n            {themes.map((themeOption) => {\r\n              const Icon = themeOption.icon;\r\n              const isSelected = theme === themeOption.value;\r\n              \r\n              return (\r\n                <button\r\n                  key={themeOption.value}\r\n                  onClick={() => handleThemeChange(themeOption.value)}\r\n                  className={`\r\n                    w-full px-3 py-2 text-left text-sm flex items-center gap-2 transition-colors duration-150\r\n                    ${isSelected\r\n                      ? (isDark ? 'bg-gray-700 text-blue-400' : 'bg-blue-50 text-blue-600')\r\n                      : (isDark ? 'text-gray-200 hover:bg-gray-700' : 'text-gray-700 hover:bg-gray-50')\r\n                    }\r\n                  `}\r\n                >\r\n                  <Icon className=\"w-4 h-4\" />\r\n                  <span>{themeOption.label}</span>\r\n                  {isSelected && (\r\n                    <span className=\"ml-auto text-xs\">✓</span>\r\n                  )}\r\n                </button>\r\n              );\r\n            })}\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ThemeToggle; "], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAJA;;;;;AAMA,MAAM,cAAc;IAClB,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAC9C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE3B,MAAM,SAAS;QACb;YAAE,OAAO;YAAS,OAAO;YAAS,MAAM,gMAAA,CAAA,MAAG;QAAC;QAC5C;YAAE,OAAO;YAAQ,OAAO;YAAQ,MAAM,kMAAA,CAAA,OAAI;QAAC;QAC3C;YAAE,OAAO;YAAU,OAAO;YAAU,MAAM,wMAAA,CAAA,UAAO;QAAC;KACnD;IAED,MAAM,eAAe,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;IAElD,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAG;gBACtE,UAAU;YACZ;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG,EAAE;IAEL,MAAM,oBAAoB,CAAC;QACzB,YAAY;QACZ,UAAU;IACZ;IAEA,qBACE,8OAAC;QAAI,WAAU;QAAW,KAAK;;0BAC7B,8OAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAW,CAAC;;UAEV,EAAE,SACE,gEACA,0DACH;;QAEH,CAAC;gBACD,OAAO,CAAC,eAAe,EAAE,cAAc,OAAO;0BAE7C,8BACC,8OAAC,aAAa,IAAI;oBAAC,WAAU;;;;;;;;;;;YAIhC,wBACC,8OAAC;gBAAI,WAAW,CAAC;;UAEf,EAAE,SACE,gCACA,2BACH;QACH,CAAC;0BACC,cAAA,8OAAC;oBAAI,WAAU;8BACZ,OAAO,GAAG,CAAC,CAAC;wBACX,MAAM,OAAO,YAAY,IAAI;wBAC7B,MAAM,aAAa,UAAU,YAAY,KAAK;wBAE9C,qBACE,8OAAC;4BAEC,SAAS,IAAM,kBAAkB,YAAY,KAAK;4BAClD,WAAW,CAAC;;oBAEV,EAAE,aACG,SAAS,8BAA8B,6BACvC,SAAS,oCAAoC,iCACjD;kBACH,CAAC;;8CAED,8OAAC;oCAAK,WAAU;;;;;;8CAChB,8OAAC;8CAAM,YAAY,KAAK;;;;;;gCACvB,4BACC,8OAAC;oCAAK,WAAU;8CAAkB;;;;;;;2BAb/B,YAAY,KAAK;;;;;oBAiB5B;;;;;;;;;;;;;;;;;AAMZ;uCAEe", "debugId": null}}, {"offset": {"line": 533, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/components/ui/DropdownMenu.jsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useRef, useEffect } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { IconUser, IconChevronRight, IconSettings, IconUser as IconProfile } from '@tabler/icons-react';\r\nimport ThemeToggle from './ThemeToggle';\r\n\r\nexport default function DropdownMenu() {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [activeSubmenu, setActiveSubmenu] = useState(null);\r\n  const dropdownRef = useRef(null);\r\n  const router = useRouter();\r\n\r\n  // Get user role from localStorage\r\n  const [userRole, setUserRole] = useState(null);\r\n  \r\n  useEffect(() => {\r\n    const role = localStorage.getItem('role');\r\n    setUserRole(role);\r\n  }, []);\r\n\r\n  const menuItems = [\r\n    {\r\n      title: 'Profile',\r\n      icon: IconProfile,\r\n      hasSubmenu: true,\r\n      submenu: [\r\n        { title: 'My Profile', href: userRole === 'ADMIN' ? '/admin/profile' : '/profile' },\r\n        { title: 'Edit Profile', href: '/profile/edit' },\r\n        { title: 'Account Settings', href: '/profile/settings' },\r\n        { title: 'Privacy Settings', href: '/profile/privacy' }\r\n      ]\r\n    },\r\n    {\r\n      title: 'Settings',\r\n      icon: IconSettings,\r\n      hasSubmenu: true,\r\n      submenu: [\r\n        { title: 'Theme', component: 'theme' },\r\n        { title: 'Notification Preferences', href: '/settings/notifications' },\r\n        { title: 'Language', href: '/settings/language' },\r\n        { title: 'Data & Privacy', href: '/settings/privacy' }\r\n      ]\r\n    },\r\n    { title: 'Help Center', href: '/help' },\r\n    { title: 'Terms of Service', href: '/terms' }\r\n  ];\r\n\r\n  const handleLogout = () => {\r\n    localStorage.removeItem('userEmail');\r\n    localStorage.removeItem('collegeName');\r\n    localStorage.removeItem('role');\r\n    document.cookie = 'role=; path=/; max-age=0';\r\n    router.push('/login');\r\n  };\r\n\r\n  useEffect(() => {\r\n    const handleClickOutside = (event) => {\r\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\r\n        setIsOpen(false);\r\n        setActiveSubmenu(null);\r\n      }\r\n    };\r\n\r\n    document.addEventListener('mousedown', handleClickOutside);\r\n    return () => document.removeEventListener('mousedown', handleClickOutside);\r\n  }, []);\r\n\r\n  const handleSubmenuToggle = (index) => {\r\n    setActiveSubmenu(activeSubmenu === index ? null : index);\r\n  };\r\n\r\n  return (\r\n    <div className=\"relative flex items-center gap-3\" ref={dropdownRef}>\r\n      <span className=\"text-black font-medium\">Student Career Center</span>\r\n      <button\r\n        onClick={() => setIsOpen(!isOpen)}\r\n        className=\"text-blue-600 hover:text-blue-700 p-2 rounded-full hover:bg-blue-50 transition-colors\"\r\n      >\r\n        <IconUser size={24} />\r\n      </button>\r\n\r\n      {isOpen && (\r\n        <div className=\"absolute right-0 top-full mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50\">\r\n          {menuItems.map((item, index) => (\r\n            <div key={index} className=\"relative\">\r\n              {item.hasSubmenu ? (\r\n                <div>\r\n                  <button\r\n                    onClick={() => handleSubmenuToggle(index)}\r\n                    className=\"w-full flex items-center justify-between px-4 py-2 text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200\"\r\n                  >\r\n                    <div className=\"flex items-center gap-3\">\r\n                      {item.icon && <item.icon size={18} />}\r\n                      <span>{item.title}</span>\r\n                    </div>\r\n                    <IconChevronRight \r\n                      size={16} \r\n                      className={`transform transition-transform duration-200 ${\r\n                        activeSubmenu === index ? 'rotate-90' : ''\r\n                      }`}\r\n                    />\r\n                  </button>\r\n                  \r\n                  {activeSubmenu === index && (\r\n                    <div className=\"ml-4 border-l border-gray-200 pl-4 py-2\">\r\n                      {item.submenu.map((subItem, subIndex) => (\r\n                        <div key={subIndex}>\r\n                          {subItem.component === 'theme' ? (\r\n                            <div className=\"px-2 py-2\">\r\n                              <div className=\"flex items-center justify-between\">\r\n                                <span className=\"text-sm text-gray-600\">Theme</span>\r\n                                <ThemeToggle />\r\n                              </div>\r\n                            </div>\r\n                          ) : (\r\n                            <a\r\n                              href={subItem.href}\r\n                              className=\"block px-2 py-2 text-sm text-gray-600 hover:text-blue-600 hover:bg-blue-25 rounded transition-all duration-200\"\r\n                            >\r\n                              {subItem.title}\r\n                            </a>\r\n                          )}\r\n                        </div>\r\n                      ))}\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              ) : (\r\n                <a\r\n                  href={item.href}\r\n                  className=\"block px-4 py-2 text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200\"\r\n                >\r\n                  {item.title}\r\n                </a>\r\n              )}\r\n            </div>\r\n          ))}\r\n          \r\n          <hr className=\"my-2 border-gray-200\" />\r\n          \r\n          <button\r\n            onClick={handleLogout}\r\n            className=\"w-full text-left px-4 py-2 text-red-600 hover:bg-red-50 transition-all duration-200\"\r\n          >\r\n            Logout\r\n          </button>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,kCAAkC;IAClC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,OAAO,aAAa,OAAO,CAAC;QAClC,YAAY;IACd,GAAG,EAAE;IAEL,MAAM,YAAY;QAChB;YACE,OAAO;YACP,MAAM,sNAAA,CAAA,WAAW;YACjB,YAAY;YACZ,SAAS;gBACP;oBAAE,OAAO;oBAAc,MAAM,aAAa,UAAU,mBAAmB;gBAAW;gBAClF;oBAAE,OAAO;oBAAgB,MAAM;gBAAgB;gBAC/C;oBAAE,OAAO;oBAAoB,MAAM;gBAAoB;gBACvD;oBAAE,OAAO;oBAAoB,MAAM;gBAAmB;aACvD;QACH;QACA;YACE,OAAO;YACP,MAAM,8NAAA,CAAA,eAAY;YAClB,YAAY;YACZ,SAAS;gBACP;oBAAE,OAAO;oBAAS,WAAW;gBAAQ;gBACrC;oBAAE,OAAO;oBAA4B,MAAM;gBAA0B;gBACrE;oBAAE,OAAO;oBAAY,MAAM;gBAAqB;gBAChD;oBAAE,OAAO;oBAAkB,MAAM;gBAAoB;aACtD;QACH;QACA;YAAE,OAAO;YAAe,MAAM;QAAQ;QACtC;YAAE,OAAO;YAAoB,MAAM;QAAS;KAC7C;IAED,MAAM,eAAe;QACnB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,SAAS,MAAM,GAAG;QAClB,OAAO,IAAI,CAAC;IACd;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAG;gBACtE,UAAU;gBACV,iBAAiB;YACnB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG,EAAE;IAEL,MAAM,sBAAsB,CAAC;QAC3B,iBAAiB,kBAAkB,QAAQ,OAAO;IACpD;IAEA,qBACE,8OAAC;QAAI,WAAU;QAAmC,KAAK;;0BACrD,8OAAC;gBAAK,WAAU;0BAAyB;;;;;;0BACzC,8OAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;0BAEV,cAAA,8OAAC,sNAAA,CAAA,WAAQ;oBAAC,MAAM;;;;;;;;;;;YAGjB,wBACC,8OAAC;gBAAI,WAAU;;oBACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC;4BAAgB,WAAU;sCACxB,KAAK,UAAU,iBACd,8OAAC;;kDACC,8OAAC;wCACC,SAAS,IAAM,oBAAoB;wCACnC,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;;oDACZ,KAAK,IAAI,kBAAI,8OAAC,KAAK,IAAI;wDAAC,MAAM;;;;;;kEAC/B,8OAAC;kEAAM,KAAK,KAAK;;;;;;;;;;;;0DAEnB,8OAAC,sOAAA,CAAA,mBAAgB;gDACf,MAAM;gDACN,WAAW,CAAC,4CAA4C,EACtD,kBAAkB,QAAQ,cAAc,IACxC;;;;;;;;;;;;oCAIL,kBAAkB,uBACjB,8OAAC;wCAAI,WAAU;kDACZ,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,SAAS,yBAC1B,8OAAC;0DACE,QAAQ,SAAS,KAAK,wBACrB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAwB;;;;;;0EACxC,8OAAC,uIAAA,CAAA,UAAW;;;;;;;;;;;;;;;yEAIhB,8OAAC;oDACC,MAAM,QAAQ,IAAI;oDAClB,WAAU;8DAET,QAAQ,KAAK;;;;;;+CAbV;;;;;;;;;;;;;;;qDAsBlB,8OAAC;gCACC,MAAM,KAAK,IAAI;gCACf,WAAU;0CAET,KAAK,KAAK;;;;;;2BAhDP;;;;;kCAsDZ,8OAAC;wBAAG,WAAU;;;;;;kCAEd,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 815, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/utils/navigation.js"], "sourcesContent": ["import { \r\n  IconHome, \r\n  IconBriefcase, \r\n  IconCompass, \r\n  IconBuilding, \r\n  IconUsers, \r\n  IconCalendarEvent, \r\n  IconMail,\r\n  IconSettings,\r\n  IconUser\r\n} from '@tabler/icons-react';\r\n\r\n// Shared navigation configuration for all student pages\r\nexport const navigationLinks = [\r\n  {\r\n    items: [\r\n      { title: 'My Campus', href: '/', icon: <IconHome /> },\r\n      { title: 'My Jobs', href: '/myjobs', icon: <IconBriefcase /> },\r\n      { title: 'Explore', href: '/explore', icon: <IconCompass /> },\r\n      { title: 'Inbox', href: '/inbox', icon: <IconMail /> }\r\n    ]\r\n  },\r\n  {\r\n    items: [\r\n      { title: 'Job Postings', href: '/jobpostings', icon: <IconBuilding /> },\r\n      { title: 'Companies', href: '/companies', icon: <IconUsers /> },\r\n      { title: 'Events', href: '/events', icon: <IconCalendarEvent /> },\r\n      { title: 'Calendar', href: '/calendar', icon: <IconCalendarEvent /> },\r\n      { title: 'My Profile', href: '/profile', icon: <IconUser /> },\r\n      { title: 'Settings', href: '/settings', icon: <IconSettings /> }\r\n    ]\r\n  }\r\n];\r\n\r\n// Function to get the correct settings URL based on user role\r\nexport const getSettingsUrl = (userType) => {\r\n  return userType === 'ADMIN' ? '/admin/settings' : '/settings';\r\n};\r\n\r\n// Function to get navigation links with dynamic settings URL\r\nexport const getNavigationLinks = (userType) => {\r\n  return navigationLinks.map(group => ({\r\n    ...group,\r\n    items: group.items.map(item => {\r\n      if (item.title === 'Settings') {\r\n        return {\r\n          ...item,\r\n          href: getSettingsUrl(userType)\r\n        };\r\n      }\r\n      return item;\r\n    })\r\n  }));\r\n};"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAaO,MAAM,kBAAkB;IAC7B;QACE,OAAO;YACL;gBAAE,OAAO;gBAAa,MAAM;gBAAK,oBAAM,8OAAC,sNAAA,CAAA,WAAQ;;;;;YAAI;YACpD;gBAAE,OAAO;gBAAW,MAAM;gBAAW,oBAAM,8OAAC,gOAAA,CAAA,gBAAa;;;;;YAAI;YAC7D;gBAAE,OAAO;gBAAW,MAAM;gBAAY,oBAAM,8OAAC,4NAAA,CAAA,cAAW;;;;;YAAI;YAC5D;gBAAE,OAAO;gBAAS,MAAM;gBAAU,oBAAM,8OAAC,sNAAA,CAAA,WAAQ;;;;;YAAI;SACtD;IACH;IACA;QACE,OAAO;YACL;gBAAE,OAAO;gBAAgB,MAAM;gBAAgB,oBAAM,8OAAC,8NAAA,CAAA,eAAY;;;;;YAAI;YACtE;gBAAE,OAAO;gBAAa,MAAM;gBAAc,oBAAM,8OAAC,wNAAA,CAAA,YAAS;;;;;YAAI;YAC9D;gBAAE,OAAO;gBAAU,MAAM;gBAAW,oBAAM,8OAAC,wOAAA,CAAA,oBAAiB;;;;;YAAI;YAChE;gBAAE,OAAO;gBAAY,MAAM;gBAAa,oBAAM,8OAAC,wOAAA,CAAA,oBAAiB;;;;;YAAI;YACpE;gBAAE,OAAO;gBAAc,MAAM;gBAAY,oBAAM,8OAAC,sNAAA,CAAA,WAAQ;;;;;YAAI;YAC5D;gBAAE,OAAO;gBAAY,MAAM;gBAAa,oBAAM,8OAAC,8NAAA,CAAA,eAAY;;;;;YAAI;SAChE;IACH;CACD;AAGM,MAAM,iBAAiB,CAAC;IAC7B,OAAO,aAAa,UAAU,oBAAoB;AACpD;AAGO,MAAM,qBAAqB,CAAC;IACjC,OAAO,gBAAgB,GAAG,CAAC,CAAA,QAAS,CAAC;YACnC,GAAG,KAAK;YACR,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA;gBACrB,IAAI,KAAK,KAAK,KAAK,YAAY;oBAC7B,OAAO;wBACL,GAAG,IAAI;wBACP,MAAM,eAAe;oBACvB;gBACF;gBACA,OAAO;YACT;QACF,CAAC;AACH", "debugId": null}}, {"offset": {"line": 955, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/components/ui/ErrorModal.jsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { \r\n  X, \r\n  AlertTriangle, \r\n  AlertCircle, \r\n  CheckCircle, \r\n  Info, \r\n  Upload,\r\n  User,\r\n  Wifi,\r\n  FileText,\r\n  Shield,\r\n  Clock,\r\n  ExternalLink\r\n} from 'lucide-react';\r\n\r\nconst ErrorModal = ({ \r\n  isOpen, \r\n  onClose, \r\n  type = 'error', \r\n  title, \r\n  message, \r\n  details,\r\n  actions = [],\r\n  dismissible = true,\r\n  autoClose = false,\r\n  autoCloseDelay = 5000,\r\n  showIcon = true\r\n}) => {\r\n  React.useEffect(() => {\r\n    if (autoClose && isOpen) {\r\n      const timer = setTimeout(() => {\r\n        onClose();\r\n      }, autoCloseDelay);\r\n      return () => clearTimeout(timer);\r\n    }\r\n  }, [autoClose, isOpen, autoCloseDelay, onClose]);\r\n\r\n  // Prevent background scroll when modal is open\r\n  React.useEffect(() => {\r\n    if (isOpen) {\r\n      document.body.style.overflow = 'hidden';\r\n    } else {\r\n      document.body.style.overflow = 'unset';\r\n    }\r\n    return () => {\r\n      document.body.style.overflow = 'unset';\r\n    };\r\n  }, [isOpen]);\r\n\r\n  if (!isOpen) return null;\r\n\r\n  const getIconAndColors = () => {\r\n    switch (type) {\r\n      case 'success':\r\n        return {\r\n          icon: CheckCircle,\r\n          iconColor: 'text-green-500',\r\n          bgColor: 'bg-green-50',\r\n          borderColor: 'border-green-200',\r\n          buttonColor: 'bg-green-600 hover:bg-green-700'\r\n        };\r\n      case 'warning':\r\n        return {\r\n          icon: AlertTriangle,\r\n          iconColor: 'text-yellow-500',\r\n          bgColor: 'bg-yellow-50',\r\n          borderColor: 'border-yellow-200',\r\n          buttonColor: 'bg-yellow-600 hover:bg-yellow-700'\r\n        };\r\n      case 'info':\r\n        return {\r\n          icon: Info,\r\n          iconColor: 'text-blue-500',\r\n          bgColor: 'bg-blue-50',\r\n          borderColor: 'border-blue-200',\r\n          buttonColor: 'bg-blue-600 hover:bg-blue-700'\r\n        };\r\n      case 'network':\r\n        return {\r\n          icon: Wifi,\r\n          iconColor: 'text-red-500',\r\n          bgColor: 'bg-red-50',\r\n          borderColor: 'border-red-200',\r\n          buttonColor: 'bg-red-600 hover:bg-red-700'\r\n        };\r\n      case 'auth':\r\n        return {\r\n          icon: Shield,\r\n          iconColor: 'text-red-500',\r\n          bgColor: 'bg-red-50',\r\n          borderColor: 'border-red-200',\r\n          buttonColor: 'bg-red-600 hover:bg-red-700'\r\n        };\r\n      case 'validation':\r\n        return {\r\n          icon: AlertCircle,\r\n          iconColor: 'text-orange-500',\r\n          bgColor: 'bg-orange-50',\r\n          borderColor: 'border-orange-200',\r\n          buttonColor: 'bg-orange-600 hover:bg-orange-700'\r\n        };\r\n      default: // error\r\n        return {\r\n          icon: AlertCircle,\r\n          iconColor: 'text-red-500',\r\n          bgColor: 'bg-red-50',\r\n          borderColor: 'border-red-200',\r\n          buttonColor: 'bg-red-600 hover:bg-red-700'\r\n        };\r\n    }\r\n  };\r\n\r\n  const { icon: IconComponent, iconColor, bgColor, borderColor, buttonColor } = getIconAndColors();\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n      <div className={`bg-white rounded-lg shadow-xl max-w-md w-full mx-4 border-2 ${borderColor}`}>\r\n        {/* Header */}\r\n        <div className={`${bgColor} px-6 py-4 rounded-t-lg border-b ${borderColor} flex items-center justify-between`}>\r\n          <div className=\"flex items-center\">\r\n            {showIcon && (\r\n              <IconComponent className={`w-6 h-6 ${iconColor} mr-3`} />\r\n            )}\r\n            <h3 className=\"text-lg font-semibold text-gray-900\">\r\n              {title}\r\n            </h3>\r\n          </div>\r\n          {dismissible && (\r\n            <button\r\n              onClick={onClose}\r\n              className=\"text-gray-400 hover:text-gray-600 transition-colors\"\r\n            >\r\n              <X className=\"w-5 h-5\" />\r\n            </button>\r\n          )}\r\n        </div>\r\n\r\n        {/* Content */}\r\n        <div className=\"px-6 py-4\">\r\n          <p className=\"text-gray-700 mb-4\">\r\n            {message}\r\n          </p>\r\n          \r\n          {details && (\r\n            <div className=\"bg-gray-50 rounded-lg p-4 mb-4\">\r\n              <h4 className=\"text-sm font-medium text-gray-800 mb-2\">Details:</h4>\r\n              {typeof details === 'string' ? (\r\n                <p className=\"text-sm text-gray-600\">{details}</p>\r\n              ) : Array.isArray(details) ? (\r\n                <ul className=\"text-sm text-gray-600 space-y-1\">\r\n                  {details.map((detail, index) => (\r\n                    <li key={index} className=\"flex items-start\">\r\n                      <span className=\"w-2 h-2 bg-gray-400 rounded-full mt-2 mr-2 flex-shrink-0\"></span>\r\n                      {detail}\r\n                    </li>\r\n                  ))}\r\n                </ul>\r\n              ) : (\r\n                <div className=\"text-sm text-gray-600\">\r\n                  {Object.entries(details).map(([key, value]) => (\r\n                    <div key={key} className=\"mb-1\">\r\n                      <span className=\"font-medium\">{key}:</span> {Array.isArray(value) ? value.join(', ') : value}\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Actions */}\r\n        <div className=\"px-6 py-4 bg-gray-50 rounded-b-lg flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-3 sm:justify-end\">\r\n          {actions.length > 0 ? (\r\n            actions.map((action, index) => (\r\n              <button\r\n                key={index}\r\n                onClick={action.onClick}\r\n                className={`px-4 py-2 rounded-lg font-medium transition-colors ${\r\n                  action.variant === 'secondary'\r\n                    ? 'bg-gray-200 text-gray-800 hover:bg-gray-300'\r\n                    : action.variant === 'danger'\r\n                    ? 'bg-red-600 text-white hover:bg-red-700'\r\n                    : buttonColor + ' text-white'\r\n                }`}\r\n                disabled={action.disabled}\r\n              >\r\n                {action.loading && (\r\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 inline-block\"></div>\r\n                )}\r\n                {action.icon && <action.icon className=\"w-4 h-4 mr-2 inline-block\" />}\r\n                {action.label}\r\n              </button>\r\n            ))\r\n          ) : (\r\n            <button\r\n              onClick={onClose}\r\n              className={`px-4 py-2 rounded-lg font-medium text-white transition-colors ${buttonColor}`}\r\n            >\r\n              OK\r\n            </button>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ErrorModal; "], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAkBA,MAAM,aAAa,CAAC,EAClB,MAAM,EACN,OAAO,EACP,OAAO,OAAO,EACd,KAAK,EACL,OAAO,EACP,OAAO,EACP,UAAU,EAAE,EACZ,cAAc,IAAI,EAClB,YAAY,KAAK,EACjB,iBAAiB,IAAI,EACrB,WAAW,IAAI,EAChB;IACC,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,aAAa,QAAQ;YACvB,MAAM,QAAQ,WAAW;gBACvB;YACF,GAAG;YACH,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;QAAW;QAAQ;QAAgB;KAAQ;IAE/C,+CAA+C;IAC/C,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,QAAQ;YACV,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QACA,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;KAAO;IAEX,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,MAAM,2NAAA,CAAA,cAAW;oBACjB,WAAW;oBACX,SAAS;oBACT,aAAa;oBACb,aAAa;gBACf;YACF,KAAK;gBACH,OAAO;oBACL,MAAM,wNAAA,CAAA,gBAAa;oBACnB,WAAW;oBACX,SAAS;oBACT,aAAa;oBACb,aAAa;gBACf;YACF,KAAK;gBACH,OAAO;oBACL,MAAM,kMAAA,CAAA,OAAI;oBACV,WAAW;oBACX,SAAS;oBACT,aAAa;oBACb,aAAa;gBACf;YACF,KAAK;gBACH,OAAO;oBACL,MAAM,kMAAA,CAAA,OAAI;oBACV,WAAW;oBACX,SAAS;oBACT,aAAa;oBACb,aAAa;gBACf;YACF,KAAK;gBACH,OAAO;oBACL,MAAM,sMAAA,CAAA,SAAM;oBACZ,WAAW;oBACX,SAAS;oBACT,aAAa;oBACb,aAAa;gBACf;YACF,KAAK;gBACH,OAAO;oBACL,MAAM,oNAAA,CAAA,cAAW;oBACjB,WAAW;oBACX,SAAS;oBACT,aAAa;oBACb,aAAa;gBACf;YACF;gBACE,OAAO;oBACL,MAAM,oNAAA,CAAA,cAAW;oBACjB,WAAW;oBACX,SAAS;oBACT,aAAa;oBACb,aAAa;gBACf;QACJ;IACF;IAEA,MAAM,EAAE,MAAM,aAAa,EAAE,SAAS,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG;IAE9E,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAW,CAAC,4DAA4D,EAAE,aAAa;;8BAE1F,8OAAC;oBAAI,WAAW,GAAG,QAAQ,iCAAiC,EAAE,YAAY,kCAAkC,CAAC;;sCAC3G,8OAAC;4BAAI,WAAU;;gCACZ,0BACC,8OAAC;oCAAc,WAAW,CAAC,QAAQ,EAAE,UAAU,KAAK,CAAC;;;;;;8CAEvD,8OAAC;oCAAG,WAAU;8CACX;;;;;;;;;;;;wBAGJ,6BACC,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAMnB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCACV;;;;;;wBAGF,yBACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;gCACtD,OAAO,YAAY,yBAClB,8OAAC;oCAAE,WAAU;8CAAyB;;;;;2CACpC,MAAM,OAAO,CAAC,yBAChB,8OAAC;oCAAG,WAAU;8CACX,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC;4CAAe,WAAU;;8DACxB,8OAAC;oDAAK,WAAU;;;;;;gDACf;;2CAFM;;;;;;;;;yDAOb,8OAAC;oCAAI,WAAU;8CACZ,OAAO,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBACxC,8OAAC;4CAAc,WAAU;;8DACvB,8OAAC;oDAAK,WAAU;;wDAAe;wDAAI;;;;;;;gDAAQ;gDAAE,MAAM,OAAO,CAAC,SAAS,MAAM,IAAI,CAAC,QAAQ;;2CAD/E;;;;;;;;;;;;;;;;;;;;;;8BAWtB,8OAAC;oBAAI,WAAU;8BACZ,QAAQ,MAAM,GAAG,IAChB,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACnB,8OAAC;4BAEC,SAAS,OAAO,OAAO;4BACvB,WAAW,CAAC,mDAAmD,EAC7D,OAAO,OAAO,KAAK,cACf,gDACA,OAAO,OAAO,KAAK,WACnB,2CACA,cAAc,eAClB;4BACF,UAAU,OAAO,QAAQ;;gCAExB,OAAO,OAAO,kBACb,8OAAC;oCAAI,WAAU;;;;;;gCAEhB,OAAO,IAAI,kBAAI,8OAAC,OAAO,IAAI;oCAAC,WAAU;;;;;;gCACtC,OAAO,KAAK;;2BAfR;;;;kDAmBT,8OAAC;wBACC,SAAS;wBACT,WAAW,CAAC,8DAA8D,EAAE,aAAa;kCAC1F;;;;;;;;;;;;;;;;;;;;;;AAQb;uCAEe", "debugId": null}}, {"offset": {"line": 1266, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/contexts/NotificationContext.jsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { createContext, useContext, useState, useCallback } from 'react';\r\nimport ErrorModal from '../components/ui/ErrorModal';\r\nimport { \r\n  Upload, \r\n  User, \r\n  Wifi, \r\n  FileText, \r\n  Shield, \r\n  ExternalLink \r\n} from 'lucide-react';\r\n\r\nconst NotificationContext = createContext();\r\n\r\nexport const useNotification = () => {\r\n  const context = useContext(NotificationContext);\r\n  if (!context) {\r\n    throw new Error('useNotification must be used within a NotificationProvider');\r\n  }\r\n  return context;\r\n};\r\n\r\nexport const NotificationProvider = ({ children }) => {\r\n  const [notification, setNotification] = useState(null);\r\n\r\n  const showNotification = useCallback((config) => {\r\n    setNotification({\r\n      id: Date.now(),\r\n      ...config,\r\n    });\r\n  }, []);\r\n\r\n  const hideNotification = useCallback(() => {\r\n    setNotification(null);\r\n  }, []);\r\n\r\n  // Specific notification types for common errors\r\n  const showError = useCallback((title, message, details = null, actions = []) => {\r\n    showNotification({\r\n      type: 'error',\r\n      title,\r\n      message,\r\n      details,\r\n      actions,\r\n    });\r\n  }, [showNotification]);\r\n\r\n  const showSuccess = useCallback((title, message, autoClose = true) => {\r\n    showNotification({\r\n      type: 'success',\r\n      title,\r\n      message,\r\n      autoClose,\r\n      autoCloseDelay: 3000,\r\n    });\r\n  }, [showNotification]);\r\n\r\n  const showWarning = useCallback((title, message, details = null, actions = []) => {\r\n    showNotification({\r\n      type: 'warning',\r\n      title,\r\n      message,\r\n      details,\r\n      actions,\r\n    });\r\n  }, [showNotification]);\r\n\r\n  const showInfo = useCallback((title, message, autoClose = false) => {\r\n    showNotification({\r\n      type: 'info',\r\n      title,\r\n      message,\r\n      autoClose,\r\n    });\r\n  }, [showNotification]);\r\n\r\n  // Specific error handlers for common scenarios\r\n  const showMissingResumeModal = useCallback(() => {\r\n    showNotification({\r\n      type: 'warning',\r\n      title: 'Resume Required',\r\n      message: 'You need to upload a resume before applying to this job.',\r\n      details: [\r\n        'Please upload your resume to your profile first',\r\n        'You can upload it from the Profile section',\r\n        'Accepted formats: PDF, DOC, DOCX (max 5MB)'\r\n      ],\r\n      showIcon: true,\r\n      actions: [\r\n        {\r\n          text: 'Go to Profile',\r\n          style: 'primary',\r\n          icon: User,\r\n          action: () => {\r\n            window.location.href = '/profile';\r\n          }\r\n        },\r\n        {\r\n          text: 'Upload Resume',\r\n          style: 'secondary',\r\n          icon: Upload,\r\n          action: () => {\r\n            // Trigger a file upload dialog\r\n            const input = document.createElement('input');\r\n            input.type = 'file';\r\n            input.accept = '.pdf,.doc,.docx';\r\n            input.onchange = (e) => {\r\n              const file = e.target.files[0];\r\n              if (file) {\r\n                // Handle resume upload (this would need to be passed as a prop)\r\n                console.log('Resume file selected:', file);\r\n              }\r\n            };\r\n            input.click();\r\n          }\r\n        }\r\n      ],\r\n      dismissible: true\r\n    });\r\n  }, [showNotification]);\r\n\r\n  const showApplicationSubmissionError = useCallback((error) => {\r\n    const errorData = error?.response?.data || {};\r\n    \r\n    // Check for specific validation errors\r\n    if (errorData.resume) {\r\n      showMissingResumeModal();\r\n      return;\r\n    }\r\n\r\n    // Check for other validation errors\r\n    const validationErrors = {};\r\n    let hasValidationErrors = false;\r\n\r\n    Object.keys(errorData).forEach(field => {\r\n      if (Array.isArray(errorData[field]) && errorData[field].length > 0) {\r\n        validationErrors[field] = errorData[field];\r\n        hasValidationErrors = true;\r\n      }\r\n    });\r\n\r\n    if (hasValidationErrors) {\r\n      // Use inline validation error notification instead of undefined function\r\n      showNotification({\r\n        type: 'error',\r\n        title: 'Application Submission Failed',\r\n        message: 'Please fix the following validation errors:',\r\n        details: Object.entries(validationErrors).map(([field, errors]) => \r\n          `${field}: ${Array.isArray(errors) ? errors.join(', ') : errors}`\r\n        ),\r\n        showIcon: true,\r\n        dismissible: true\r\n      });\r\n      return;\r\n    }\r\n\r\n    // Generic application error\r\n    const message = errorData.detail || \r\n                   errorData.message || \r\n                   error?.message || \r\n                   'Failed to submit your job application.';\r\n\r\n    showNotification({\r\n      type: 'error',\r\n      title: 'Application Failed',\r\n      message: message,\r\n      details: [\r\n        'Please check your application details',\r\n        'Ensure all required fields are filled',\r\n        'Try again in a few moments'\r\n      ],\r\n      showIcon: true,\r\n      actions: [\r\n        {\r\n          text: 'Try Again',\r\n          style: 'primary',\r\n          action: () => {\r\n            // This would need to be passed as a callback\r\n            window.location.reload();\r\n          }\r\n        }\r\n      ],\r\n      dismissible: true\r\n    });\r\n  }, [showNotification, showMissingResumeModal]);\r\n\r\n  const showProfileIncompleteModal = useCallback((missingFields = []) => {\r\n    showNotification({\r\n      type: 'warning',\r\n      title: 'Profile Incomplete',\r\n      message: 'Please complete your profile before applying to jobs.',\r\n      details: missingFields.length > 0 \r\n        ? [`Missing: ${missingFields.join(', ')}`]\r\n        : [\r\n          'Some required profile information is missing',\r\n          'Please update your profile with all necessary details',\r\n          'This ensures better job matching and application processing'\r\n        ],\r\n      showIcon: true,\r\n      actions: [\r\n        {\r\n          text: 'Complete Profile',\r\n          style: 'primary',\r\n          icon: User,\r\n          action: () => {\r\n            window.location.href = '/profile';\r\n          }\r\n        }\r\n      ],\r\n      dismissible: true\r\n    });\r\n  }, [showNotification]);\r\n\r\n  const showSessionExpiredModal = useCallback(() => {\r\n    showNotification({\r\n      type: 'warning',\r\n      title: 'Session Expired',\r\n      message: 'Your login session has expired. Please login again to continue.',\r\n      details: [\r\n        'For security reasons, sessions expire after a period of inactivity',\r\n        'Please login again to access your account',\r\n        'Your data is safe and will be available after login'\r\n      ],\r\n      showIcon: true,\r\n      actions: [\r\n        {\r\n          text: 'Login',\r\n          style: 'primary',\r\n          icon: Shield,\r\n          action: () => {\r\n            window.location.href = '/login';\r\n          }\r\n        }\r\n      ],\r\n      dismissible: false,\r\n      autoClose: false\r\n    });\r\n  }, [showNotification]);\r\n\r\n  const showMaintenanceModal = useCallback(() => {\r\n    showNotification({\r\n      type: 'info',\r\n      title: 'System Maintenance',\r\n      message: 'The system is currently under maintenance. Please try again later.',\r\n      details: [\r\n        'We are working to improve your experience',\r\n        'Maintenance should be completed shortly',\r\n        'Thank you for your patience'\r\n      ],\r\n      showIcon: true,\r\n      actions: [\r\n        {\r\n          text: 'Check Status',\r\n          style: 'secondary',\r\n          icon: ExternalLink,\r\n          action: () => {\r\n            window.open('/status', '_blank');\r\n          }\r\n        }\r\n      ],\r\n      dismissible: true,\r\n      autoClose: false\r\n    });\r\n  }, [showNotification]);\r\n\r\n  // Specific error handlers for common scenarios\r\n  const showAuthError = useCallback((message = 'Authentication failed. Please log in again.') => {\r\n    showNotification({\r\n      type: 'auth',\r\n      title: 'Authentication Required',\r\n      message,\r\n      actions: [\r\n        {\r\n          label: 'Go to Login',\r\n          onClick: () => {\r\n            hideNotification();\r\n            window.location.href = '/login';\r\n          },\r\n          icon: User,\r\n        },\r\n      ],\r\n    });\r\n  }, [showNotification, hideNotification]);\r\n\r\n  const showNetworkError = useCallback((error = null) => {\r\n    showNotification({\r\n      type: 'network',\r\n      title: 'Connection Error',\r\n      message: 'Unable to connect to the server. Please check your internet connection and try again.',\r\n      details: error ? [\r\n        'Make sure you\\'re connected to the internet',\r\n        'Try refreshing the page',\r\n        'Check if the server is running',\r\n        error.message || 'Unknown network error'\r\n      ] : [\r\n        'Make sure you\\'re connected to the internet',\r\n        'Try refreshing the page',\r\n        'Check if the server is running'\r\n      ],\r\n      actions: [\r\n        {\r\n          label: 'Retry',\r\n          onClick: () => {\r\n            hideNotification();\r\n            window.location.reload();\r\n          },\r\n          icon: Wifi,\r\n        },\r\n        {\r\n          label: 'Cancel',\r\n          onClick: hideNotification,\r\n          variant: 'secondary',\r\n        },\r\n      ],\r\n    });\r\n  }, [showNotification, hideNotification]);\r\n\r\n  const showResumeRequiredError = useCallback(() => {\r\n    showNotification({\r\n      type: 'validation',\r\n      title: 'Resume Required',\r\n      message: 'You need to upload a resume before applying to this job. Please update your profile first.',\r\n      details: [\r\n        'Go to your profile page',\r\n        'Upload your resume in the \"Resume\" section',\r\n        'Come back and apply for the job'\r\n      ],\r\n      actions: [\r\n        {\r\n          label: 'Go to Profile',\r\n          onClick: () => {\r\n            hideNotification();\r\n            window.location.href = '/profile';\r\n          },\r\n          icon: User,\r\n        },\r\n        {\r\n          label: 'Cancel',\r\n          onClick: hideNotification,\r\n          variant: 'secondary',\r\n        },\r\n      ],\r\n    });\r\n  }, [showNotification, hideNotification]);\r\n\r\n  const showFileUploadError = useCallback((details = null) => {\r\n    showNotification({\r\n      type: 'error',\r\n      title: 'File Upload Failed',\r\n      message: 'There was a problem uploading your file. Please try again with a different file.',\r\n      details: details || [\r\n        'Check that your file is not too large (max 10MB)',\r\n        'Supported formats: PDF, DOC, DOCX',\r\n        'Make sure the file is not corrupted',\r\n        'Try uploading a different file'\r\n      ],\r\n      actions: [\r\n        {\r\n          label: 'Try Again',\r\n          onClick: hideNotification,\r\n          icon: Upload,\r\n        },\r\n      ],\r\n    });\r\n  }, [showNotification, hideNotification]);\r\n\r\n  const showValidationError = useCallback((title, errors) => {\r\n    let details;\r\n    let message;\r\n\r\n    if (typeof errors === 'object' && errors !== null) {\r\n      // Handle backend validation errors\r\n      details = {};\r\n      Object.entries(errors).forEach(([field, messages]) => {\r\n        if (Array.isArray(messages)) {\r\n          details[field] = messages.join(', ');\r\n        } else {\r\n          details[field] = messages;\r\n        }\r\n      });\r\n      message = 'Please fix the following errors and try again:';\r\n    } else if (Array.isArray(errors)) {\r\n      details = errors;\r\n      message = 'Please fix the following issues:';\r\n    } else {\r\n      message = errors || 'Please check your input and try again.';\r\n      details = null;\r\n    }\r\n\r\n    showNotification({\r\n      type: 'validation',\r\n      title: title || 'Validation Error',\r\n      message,\r\n      details,\r\n    });\r\n  }, [showNotification]);\r\n\r\n  const showPermissionError = useCallback((message = 'You don\\'t have permission to perform this action.') => {\r\n    showNotification({\r\n      type: 'auth',\r\n      title: 'Permission Denied',\r\n      message,\r\n      details: [\r\n        'Contact your administrator if you believe this is an error',\r\n        'Make sure you\\'re logged in with the correct account',\r\n        'Check if your session has expired'\r\n      ],\r\n      actions: [\r\n        {\r\n          label: 'Go to Login',\r\n          onClick: () => {\r\n            hideNotification();\r\n            window.location.href = '/login';\r\n          },\r\n          icon: Shield,\r\n        },\r\n        {\r\n          label: 'Contact Support',\r\n          onClick: () => {\r\n            hideNotification();\r\n            window.location.href = '/admin/helpandsupport';\r\n          },\r\n          icon: ExternalLink,\r\n          variant: 'secondary',\r\n        },\r\n      ],\r\n    });\r\n  }, [showNotification, hideNotification]);\r\n\r\n  const showProfileIncompleteError = useCallback((missingFields = []) => {\r\n    showNotification({\r\n      type: 'warning',\r\n      title: 'Profile Incomplete',\r\n      message: 'Your profile needs to be completed before you can apply for jobs.',\r\n      details: missingFields.length > 0 \r\n        ? [`Please fill in the following fields: ${missingFields.join(', ')}`]\r\n        : [\r\n            'Make sure your personal information is complete',\r\n            'Upload your resume',\r\n            'Verify your contact information',\r\n            'Add your academic details'\r\n          ],\r\n      actions: [\r\n        {\r\n          label: 'Complete Profile',\r\n          onClick: () => {\r\n            hideNotification();\r\n            window.location.href = '/profile';\r\n          },\r\n          icon: User,\r\n        },\r\n        {\r\n          label: 'Later',\r\n          onClick: hideNotification,\r\n          variant: 'secondary',\r\n        },\r\n      ],\r\n    });\r\n  }, [showNotification, hideNotification]);\r\n\r\n  // Generic API error handler\r\n  const handleApiError = useCallback((error, context = 'operation') => {\r\n    if (error?.response?.status === 401) {\r\n      showAuthError();\r\n    } else if (error?.response?.status === 403) {\r\n      showPermissionError();\r\n    } else if (error?.response?.status >= 500) {\r\n      showError(\r\n        'Server Error',\r\n        `A server error occurred while performing the ${context}. Please try again later.`,\r\n        'If the problem persists, please contact support.'\r\n      );\r\n    } else if (!error?.response) {\r\n      showNetworkError(error);\r\n    } else {\r\n      // Generic error\r\n      const message = error?.response?.data?.detail || \r\n                     error?.response?.data?.message || \r\n                     error?.message || \r\n                     `Failed to complete ${context}. Please try again.`;\r\n      \r\n      showError('Error', message);\r\n    }\r\n  }, [showError, showAuthError, showPermissionError, showNetworkError]);\r\n\r\n  const value = {\r\n    // Core functions\r\n    showNotification,\r\n    hideNotification,\r\n    \r\n    // Basic types\r\n    showError,\r\n    showSuccess,\r\n    showWarning,\r\n    showInfo,\r\n    \r\n    // Specific error types\r\n    showAuthError,\r\n    showNetworkError,\r\n    showResumeRequiredError,\r\n    showFileUploadError,\r\n    showValidationError,\r\n    showPermissionError,\r\n    showProfileIncompleteError,\r\n    showApplicationSubmissionError,\r\n    showMissingResumeModal,\r\n    showProfileIncompleteModal,\r\n    showSessionExpiredModal,\r\n    showMaintenanceModal,\r\n    \r\n    // Generic handler\r\n    handleApiError,\r\n  };\r\n\r\n  return (\r\n    <NotificationContext.Provider value={value}>\r\n      {children}\r\n      {notification && (\r\n        <ErrorModal\r\n          isOpen={true}\r\n          onClose={hideNotification}\r\n          type={notification.type}\r\n          title={notification.title}\r\n          message={notification.message}\r\n          details={notification.details}\r\n          actions={notification.actions}\r\n          dismissible={notification.dismissible !== false}\r\n          autoClose={notification.autoClose}\r\n          autoCloseDelay={notification.autoCloseDelay}\r\n          showIcon={notification.showIcon !== false}\r\n        />\r\n      )}\r\n    </NotificationContext.Provider>\r\n  );\r\n};\r\n\r\nexport default NotificationProvider; "], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAaA,MAAM,oCAAsB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD;AAEjC,MAAM,kBAAkB;IAC7B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,MAAM,uBAAuB,CAAC,EAAE,QAAQ,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,gBAAgB;YACd,IAAI,KAAK,GAAG;YACZ,GAAG,MAAM;QACX;IACF,GAAG,EAAE;IAEL,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACnC,gBAAgB;IAClB,GAAG,EAAE;IAEL,gDAAgD;IAChD,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAO,SAAS,UAAU,IAAI,EAAE,UAAU,EAAE;QACzE,iBAAiB;YACf,MAAM;YACN;YACA;YACA;YACA;QACF;IACF,GAAG;QAAC;KAAiB;IAErB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAO,SAAS,YAAY,IAAI;QAC/D,iBAAiB;YACf,MAAM;YACN;YACA;YACA;YACA,gBAAgB;QAClB;IACF,GAAG;QAAC;KAAiB;IAErB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAO,SAAS,UAAU,IAAI,EAAE,UAAU,EAAE;QAC3E,iBAAiB;YACf,MAAM;YACN;YACA;YACA;YACA;QACF;IACF,GAAG;QAAC;KAAiB;IAErB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAO,SAAS,YAAY,KAAK;QAC7D,iBAAiB;YACf,MAAM;YACN;YACA;YACA;QACF;IACF,GAAG;QAAC;KAAiB;IAErB,+CAA+C;IAC/C,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACzC,iBAAiB;YACf,MAAM;YACN,OAAO;YACP,SAAS;YACT,SAAS;gBACP;gBACA;gBACA;aACD;YACD,UAAU;YACV,SAAS;gBACP;oBACE,MAAM;oBACN,OAAO;oBACP,MAAM,kMAAA,CAAA,OAAI;oBACV,QAAQ;wBACN,OAAO,QAAQ,CAAC,IAAI,GAAG;oBACzB;gBACF;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,MAAM,sMAAA,CAAA,SAAM;oBACZ,QAAQ;wBACN,+BAA+B;wBAC/B,MAAM,QAAQ,SAAS,aAAa,CAAC;wBACrC,MAAM,IAAI,GAAG;wBACb,MAAM,MAAM,GAAG;wBACf,MAAM,QAAQ,GAAG,CAAC;4BAChB,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;4BAC9B,IAAI,MAAM;gCACR,gEAAgE;gCAChE,QAAQ,GAAG,CAAC,yBAAyB;4BACvC;wBACF;wBACA,MAAM,KAAK;oBACb;gBACF;aACD;YACD,aAAa;QACf;IACF,GAAG;QAAC;KAAiB;IAErB,MAAM,iCAAiC,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClD,MAAM,YAAY,OAAO,UAAU,QAAQ,CAAC;QAE5C,uCAAuC;QACvC,IAAI,UAAU,MAAM,EAAE;YACpB;YACA;QACF;QAEA,oCAAoC;QACpC,MAAM,mBAAmB,CAAC;QAC1B,IAAI,sBAAsB;QAE1B,OAAO,IAAI,CAAC,WAAW,OAAO,CAAC,CAAA;YAC7B,IAAI,MAAM,OAAO,CAAC,SAAS,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG;gBAClE,gBAAgB,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;gBAC1C,sBAAsB;YACxB;QACF;QAEA,IAAI,qBAAqB;YACvB,yEAAyE;YACzE,iBAAiB;gBACf,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,SAAS,OAAO,OAAO,CAAC,kBAAkB,GAAG,CAAC,CAAC,CAAC,OAAO,OAAO,GAC5D,GAAG,MAAM,EAAE,EAAE,MAAM,OAAO,CAAC,UAAU,OAAO,IAAI,CAAC,QAAQ,QAAQ;gBAEnE,UAAU;gBACV,aAAa;YACf;YACA;QACF;QAEA,4BAA4B;QAC5B,MAAM,UAAU,UAAU,MAAM,IACjB,UAAU,OAAO,IACjB,OAAO,WACP;QAEf,iBAAiB;YACf,MAAM;YACN,OAAO;YACP,SAAS;YACT,SAAS;gBACP;gBACA;gBACA;aACD;YACD,UAAU;YACV,SAAS;gBACP;oBACE,MAAM;oBACN,OAAO;oBACP,QAAQ;wBACN,6CAA6C;wBAC7C,OAAO,QAAQ,CAAC,MAAM;oBACxB;gBACF;aACD;YACD,aAAa;QACf;IACF,GAAG;QAAC;QAAkB;KAAuB;IAE7C,MAAM,6BAA6B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,gBAAgB,EAAE;QAChE,iBAAiB;YACf,MAAM;YACN,OAAO;YACP,SAAS;YACT,SAAS,cAAc,MAAM,GAAG,IAC5B;gBAAC,CAAC,SAAS,EAAE,cAAc,IAAI,CAAC,OAAO;aAAC,GACxC;gBACA;gBACA;gBACA;aACD;YACH,UAAU;YACV,SAAS;gBACP;oBACE,MAAM;oBACN,OAAO;oBACP,MAAM,kMAAA,CAAA,OAAI;oBACV,QAAQ;wBACN,OAAO,QAAQ,CAAC,IAAI,GAAG;oBACzB;gBACF;aACD;YACD,aAAa;QACf;IACF,GAAG;QAAC;KAAiB;IAErB,MAAM,0BAA0B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC1C,iBAAiB;YACf,MAAM;YACN,OAAO;YACP,SAAS;YACT,SAAS;gBACP;gBACA;gBACA;aACD;YACD,UAAU;YACV,SAAS;gBACP;oBACE,MAAM;oBACN,OAAO;oBACP,MAAM,sMAAA,CAAA,SAAM;oBACZ,QAAQ;wBACN,OAAO,QAAQ,CAAC,IAAI,GAAG;oBACzB;gBACF;aACD;YACD,aAAa;YACb,WAAW;QACb;IACF,GAAG;QAAC;KAAiB;IAErB,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvC,iBAAiB;YACf,MAAM;YACN,OAAO;YACP,SAAS;YACT,SAAS;gBACP;gBACA;gBACA;aACD;YACD,UAAU;YACV,SAAS;gBACP;oBACE,MAAM;oBACN,OAAO;oBACP,MAAM,sNAAA,CAAA,eAAY;oBAClB,QAAQ;wBACN,OAAO,IAAI,CAAC,WAAW;oBACzB;gBACF;aACD;YACD,aAAa;YACb,WAAW;QACb;IACF,GAAG;QAAC;KAAiB;IAErB,+CAA+C;IAC/C,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,UAAU,6CAA6C;QACxF,iBAAiB;YACf,MAAM;YACN,OAAO;YACP;YACA,SAAS;gBACP;oBACE,OAAO;oBACP,SAAS;wBACP;wBACA,OAAO,QAAQ,CAAC,IAAI,GAAG;oBACzB;oBACA,MAAM,kMAAA,CAAA,OAAI;gBACZ;aACD;QACH;IACF,GAAG;QAAC;QAAkB;KAAiB;IAEvC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAQ,IAAI;QAChD,iBAAiB;YACf,MAAM;YACN,OAAO;YACP,SAAS;YACT,SAAS,QAAQ;gBACf;gBACA;gBACA;gBACA,MAAM,OAAO,IAAI;aAClB,GAAG;gBACF;gBACA;gBACA;aACD;YACD,SAAS;gBACP;oBACE,OAAO;oBACP,SAAS;wBACP;wBACA,OAAO,QAAQ,CAAC,MAAM;oBACxB;oBACA,MAAM,kMAAA,CAAA,OAAI;gBACZ;gBACA;oBACE,OAAO;oBACP,SAAS;oBACT,SAAS;gBACX;aACD;QACH;IACF,GAAG;QAAC;QAAkB;KAAiB;IAEvC,MAAM,0BAA0B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC1C,iBAAiB;YACf,MAAM;YACN,OAAO;YACP,SAAS;YACT,SAAS;gBACP;gBACA;gBACA;aACD;YACD,SAAS;gBACP;oBACE,OAAO;oBACP,SAAS;wBACP;wBACA,OAAO,QAAQ,CAAC,IAAI,GAAG;oBACzB;oBACA,MAAM,kMAAA,CAAA,OAAI;gBACZ;gBACA;oBACE,OAAO;oBACP,SAAS;oBACT,SAAS;gBACX;aACD;QACH;IACF,GAAG;QAAC;QAAkB;KAAiB;IAEvC,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,UAAU,IAAI;QACrD,iBAAiB;YACf,MAAM;YACN,OAAO;YACP,SAAS;YACT,SAAS,WAAW;gBAClB;gBACA;gBACA;gBACA;aACD;YACD,SAAS;gBACP;oBACE,OAAO;oBACP,SAAS;oBACT,MAAM,sMAAA,CAAA,SAAM;gBACd;aACD;QACH;IACF,GAAG;QAAC;QAAkB;KAAiB;IAEvC,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAO;QAC9C,IAAI;QACJ,IAAI;QAEJ,IAAI,OAAO,WAAW,YAAY,WAAW,MAAM;YACjD,mCAAmC;YACnC,UAAU,CAAC;YACX,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,OAAO,SAAS;gBAC/C,IAAI,MAAM,OAAO,CAAC,WAAW;oBAC3B,OAAO,CAAC,MAAM,GAAG,SAAS,IAAI,CAAC;gBACjC,OAAO;oBACL,OAAO,CAAC,MAAM,GAAG;gBACnB;YACF;YACA,UAAU;QACZ,OAAO,IAAI,MAAM,OAAO,CAAC,SAAS;YAChC,UAAU;YACV,UAAU;QACZ,OAAO;YACL,UAAU,UAAU;YACpB,UAAU;QACZ;QAEA,iBAAiB;YACf,MAAM;YACN,OAAO,SAAS;YAChB;YACA;QACF;IACF,GAAG;QAAC;KAAiB;IAErB,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,UAAU,oDAAoD;QACrG,iBAAiB;YACf,MAAM;YACN,OAAO;YACP;YACA,SAAS;gBACP;gBACA;gBACA;aACD;YACD,SAAS;gBACP;oBACE,OAAO;oBACP,SAAS;wBACP;wBACA,OAAO,QAAQ,CAAC,IAAI,GAAG;oBACzB;oBACA,MAAM,sMAAA,CAAA,SAAM;gBACd;gBACA;oBACE,OAAO;oBACP,SAAS;wBACP;wBACA,OAAO,QAAQ,CAAC,IAAI,GAAG;oBACzB;oBACA,MAAM,sNAAA,CAAA,eAAY;oBAClB,SAAS;gBACX;aACD;QACH;IACF,GAAG;QAAC;QAAkB;KAAiB;IAEvC,MAAM,6BAA6B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,gBAAgB,EAAE;QAChE,iBAAiB;YACf,MAAM;YACN,OAAO;YACP,SAAS;YACT,SAAS,cAAc,MAAM,GAAG,IAC5B;gBAAC,CAAC,qCAAqC,EAAE,cAAc,IAAI,CAAC,OAAO;aAAC,GACpE;gBACE;gBACA;gBACA;gBACA;aACD;YACL,SAAS;gBACP;oBACE,OAAO;oBACP,SAAS;wBACP;wBACA,OAAO,QAAQ,CAAC,IAAI,GAAG;oBACzB;oBACA,MAAM,kMAAA,CAAA,OAAI;gBACZ;gBACA;oBACE,OAAO;oBACP,SAAS;oBACT,SAAS;gBACX;aACD;QACH;IACF,GAAG;QAAC;QAAkB;KAAiB;IAEvC,4BAA4B;IAC5B,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAO,UAAU,WAAW;QAC9D,IAAI,OAAO,UAAU,WAAW,KAAK;YACnC;QACF,OAAO,IAAI,OAAO,UAAU,WAAW,KAAK;YAC1C;QACF,OAAO,IAAI,OAAO,UAAU,UAAU,KAAK;YACzC,UACE,gBACA,CAAC,6CAA6C,EAAE,QAAQ,yBAAyB,CAAC,EAClF;QAEJ,OAAO,IAAI,CAAC,OAAO,UAAU;YAC3B,iBAAiB;QACnB,OAAO;YACL,gBAAgB;YAChB,MAAM,UAAU,OAAO,UAAU,MAAM,UACxB,OAAO,UAAU,MAAM,WACvB,OAAO,WACP,CAAC,mBAAmB,EAAE,QAAQ,mBAAmB,CAAC;YAEjE,UAAU,SAAS;QACrB;IACF,GAAG;QAAC;QAAW;QAAe;QAAqB;KAAiB;IAEpE,MAAM,QAAQ;QACZ,iBAAiB;QACjB;QACA;QAEA,cAAc;QACd;QACA;QACA;QACA;QAEA,uBAAuB;QACvB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA,kBAAkB;QAClB;IACF;IAEA,qBACE,8OAAC,oBAAoB,QAAQ;QAAC,OAAO;;YAClC;YACA,8BACC,8OAAC,sIAAA,CAAA,UAAU;gBACT,QAAQ;gBACR,SAAS;gBACT,MAAM,aAAa,IAAI;gBACvB,OAAO,aAAa,KAAK;gBACzB,SAAS,aAAa,OAAO;gBAC7B,SAAS,aAAa,OAAO;gBAC7B,SAAS,aAAa,OAAO;gBAC7B,aAAa,aAAa,WAAW,KAAK;gBAC1C,WAAW,aAAa,SAAS;gBACjC,gBAAgB,aAAa,cAAc;gBAC3C,UAAU,aAAa,QAAQ,KAAK;;;;;;;;;;;;AAK9C;uCAEe", "debugId": null}}, {"offset": {"line": 1818, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/app/layout.js"], "sourcesContent": ["'use client';\r\n\r\nimport './globals.css';\r\nimport { useEffect, useState } from 'react';\r\nimport { usePathname } from 'next/navigation';\r\nimport Sidebar from '../components/ui/Sidebar';\r\nimport DropdownMenu from '../components/ui/DropdownMenu';\r\nimport { navigationLinks } from '../utils/navigation';\r\nimport { ThemeProvider } from '../contexts/ThemeContext';\r\nimport { NotificationProvider } from '../contexts/NotificationContext';\r\nimport ThemeToggle from '../components/ui/ThemeToggle';\r\n\r\n// Define page titles for different routes\r\nconst getPageTitle = (pathname) => {\r\n  const titleMap = {\r\n    '/': 'My Campus',\r\n    '/myjobs': 'My Jobs',\r\n    '/explore': 'Explore',\r\n    '/inbox': 'Inbox',\r\n    '/jobpostings': 'Job Postings',\r\n    '/companies': 'Companies',\r\n    '/events': 'Events',\r\n    '/calendar': 'Calendar',\r\n    '/admin': 'Admin Dashboard',\r\n    '/admin/posts': 'Admin Posts'\r\n  };\r\n  \r\n  // Handle dynamic routes like /company/[id]\r\n  if (pathname.startsWith('/company/')) {\r\n    return 'Company Profile';\r\n  }\r\n  \r\n  if (pathname.startsWith('/admin/')) {\r\n    return 'Admin Dashboard';\r\n  }\r\n  \r\n  return titleMap[pathname] || 'PlaceEasy';\r\n};\r\n\r\n// Check if current route should use admin layout\r\nconst isAdminRoute = (pathname) => {\r\n  return pathname.startsWith('/admin');\r\n};\r\n\r\n// Check if current route should hide the sidebar (like login, signup, etc.)\r\nconst shouldHideSidebar = (pathname) => {\r\n  const hiddenRoutes = ['/login', '/signup', '/onboarding'];\r\n  return hiddenRoutes.includes(pathname);\r\n};\r\n\r\nexport default function RootLayout({ children }) {\r\n  const pathname = usePathname();\r\n  const [college, setCollege] = useState('');\r\n  const [isClient, setIsClient] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setIsClient(true);\r\n    const storedCollege = localStorage.getItem('collegeName');\r\n    if (storedCollege) {\r\n      setCollege(storedCollege);\r\n    }\r\n  }, []);\r\n\r\n  // Don't render the layout structure until client-side hydration\r\n  if (!isClient) {\r\n    return (\r\n      <html lang=\"en\">\r\n        <head>\r\n          <title>PlaceEasy - Campus Placement Platform</title>\r\n          <meta name=\"description\" content=\"Your comprehensive campus placement and career management platform\" />\r\n        </head>\r\n        <body className=\"h-screen overflow-hidden\">\r\n          <ThemeProvider>\r\n            <NotificationProvider>\r\n              <main className=\"h-full bg-white text-black\">\r\n                {children}\r\n              </main>\r\n            </NotificationProvider>\r\n          </ThemeProvider>\r\n        </body>\r\n      </html>\r\n    );\r\n  }\r\n\r\n  const pageTitle = getPageTitle(pathname);\r\n  const hideLayout = shouldHideSidebar(pathname);\r\n  const isAdmin = isAdminRoute(pathname);\r\n\r\n  // If it's a route that should hide the sidebar (like login), just render the children\r\n  if (hideLayout) {\r\n    return (\r\n      <html lang=\"en\">\r\n        <head>\r\n          <title>PlaceEasy - Campus Placement Platform</title>\r\n          <meta name=\"description\" content=\"Your comprehensive campus placement and career management platform\" />\r\n        </head>\r\n        <body className=\"h-screen overflow-hidden\">\r\n          <ThemeProvider>\r\n            <NotificationProvider>\r\n              <main className=\"h-full bg-white text-black\">\r\n                {children}\r\n              </main>\r\n            </NotificationProvider>\r\n          </ThemeProvider>\r\n        </body>\r\n      </html>\r\n    );\r\n  }\r\n\r\n  // Admin routes use their own layout completely\r\n  if (isAdmin) {\r\n    return (\r\n      <html lang=\"en\">\r\n        <head>\r\n          <title>PlaceEasy - Campus Placement Platform</title>\r\n          <meta name=\"description\" content=\"Your comprehensive campus placement and career management platform\" />\r\n        </head>\r\n        <body className=\"h-screen overflow-hidden\">\r\n          <ThemeProvider>\r\n            <NotificationProvider>\r\n              <div className=\"h-screen bg-gray-50\">\r\n                {/* Fixed Header for Admin */}\r\n                <div className=\"fixed w-full flex justify-between items-center py-4 bg-white shadow-sm z-10\">\r\n                  <span className=\"text-gray-700 font-medium text-xl ml-28\">\r\n                    {pageTitle}\r\n                  </span>\r\n                  <div className=\"flex items-center gap-4 mr-6\">\r\n                    <ThemeToggle />\r\n                    <DropdownMenu />\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Admin Content - Takes full remaining height */}\r\n                <div className=\"pt-16 h-[calc(100vh-4rem)]\">\r\n                  {children}\r\n                </div>\r\n              </div>\r\n            </NotificationProvider>\r\n          </ThemeProvider>\r\n        </body>\r\n      </html>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <html lang=\"en\">\r\n      <head>\r\n        <title>PlaceEasy - Campus Placement Platform</title>\r\n        <meta name=\"description\" content=\"Your comprehensive campus placement and career management platform\" />\r\n      </head>\r\n      <body className=\"h-screen overflow-hidden\">\r\n        <ThemeProvider>\r\n          <NotificationProvider>\r\n            <div className=\"h-screen bg-gray-50\">\r\n              {/* Fixed Header */}\r\n              <div className=\"fixed w-full flex justify-between items-center py-4 bg-white shadow-sm z-10\">\r\n                <span className=\"text-gray-700 font-medium text-xl ml-16 sm:ml-28\">\r\n                  {college || 'AVV Chennai'}\r\n                </span>\r\n                <div className=\"flex items-center gap-4 mr-6\">\r\n                  <ThemeToggle />\r\n                  <DropdownMenu />\r\n                </div>\r\n              </div>\r\n\r\n              {/* Sidebar */}\r\n              <Sidebar \r\n                sections={navigationLinks} \r\n                defaultExpanded={false} \r\n                navbarHeight=\"4rem\" \r\n                className=\"z-20\" \r\n              />\r\n\r\n              {/* Main Layout with Sidebar */}\r\n              <div className=\"flex pt-16 h-[calc(100vh-4rem)]\">\r\n                {/* Content Area */}\r\n                <div className=\"flex-1 p-6 ml-0 md:ml-20 h-full overflow-auto\">\r\n                  <div className=\"bg-white rounded-xl shadow-md p-6 min-h-[800px] text-black content-card\">\r\n                    {children}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </NotificationProvider>\r\n        </ThemeProvider>\r\n      </body>\r\n    </html>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;AAYA,0CAA0C;AAC1C,MAAM,eAAe,CAAC;IACpB,MAAM,WAAW;QACf,KAAK;QACL,WAAW;QACX,YAAY;QACZ,UAAU;QACV,gBAAgB;QAChB,cAAc;QACd,WAAW;QACX,aAAa;QACb,UAAU;QACV,gBAAgB;IAClB;IAEA,2CAA2C;IAC3C,IAAI,SAAS,UAAU,CAAC,cAAc;QACpC,OAAO;IACT;IAEA,IAAI,SAAS,UAAU,CAAC,YAAY;QAClC,OAAO;IACT;IAEA,OAAO,QAAQ,CAAC,SAAS,IAAI;AAC/B;AAEA,iDAAiD;AACjD,MAAM,eAAe,CAAC;IACpB,OAAO,SAAS,UAAU,CAAC;AAC7B;AAEA,4EAA4E;AAC5E,MAAM,oBAAoB,CAAC;IACzB,MAAM,eAAe;QAAC;QAAU;QAAW;KAAc;IACzD,OAAO,aAAa,QAAQ,CAAC;AAC/B;AAEe,SAAS,WAAW,EAAE,QAAQ,EAAE;IAC7C,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;QACZ,MAAM,gBAAgB,aAAa,OAAO,CAAC;QAC3C,IAAI,eAAe;YACjB,WAAW;QACb;IACF,GAAG,EAAE;IAEL,gEAAgE;IAChE,IAAI,CAAC,UAAU;QACb,qBACE,8OAAC;YAAK,MAAK;;8BACT,8OAAC;;sCACC,8OAAC;sCAAM;;;;;;sCACP,8OAAC;4BAAK,MAAK;4BAAc,SAAQ;;;;;;;;;;;;8BAEnC,8OAAC;oBAAK,WAAU;8BACd,cAAA,8OAAC,gIAAA,CAAA,gBAAa;kCACZ,cAAA,8OAAC,uIAAA,CAAA,uBAAoB;sCACnB,cAAA,8OAAC;gCAAK,WAAU;0CACb;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOf;IAEA,MAAM,YAAY,aAAa;IAC/B,MAAM,aAAa,kBAAkB;IACrC,MAAM,UAAU,aAAa;IAE7B,sFAAsF;IACtF,IAAI,YAAY;QACd,qBACE,8OAAC;YAAK,MAAK;;8BACT,8OAAC;;sCACC,8OAAC;sCAAM;;;;;;sCACP,8OAAC;4BAAK,MAAK;4BAAc,SAAQ;;;;;;;;;;;;8BAEnC,8OAAC;oBAAK,WAAU;8BACd,cAAA,8OAAC,gIAAA,CAAA,gBAAa;kCACZ,cAAA,8OAAC,uIAAA,CAAA,uBAAoB;sCACnB,cAAA,8OAAC;gCAAK,WAAU;0CACb;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOf;IAEA,+CAA+C;IAC/C,IAAI,SAAS;QACX,qBACE,8OAAC;YAAK,MAAK;;8BACT,8OAAC;;sCACC,8OAAC;sCAAM;;;;;;sCACP,8OAAC;4BAAK,MAAK;4BAAc,SAAQ;;;;;;;;;;;;8BAEnC,8OAAC;oBAAK,WAAU;8BACd,cAAA,8OAAC,gIAAA,CAAA,gBAAa;kCACZ,cAAA,8OAAC,uIAAA,CAAA,uBAAoB;sCACnB,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DACb;;;;;;0DAEH,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,uIAAA,CAAA,UAAW;;;;;kEACZ,8OAAC,wIAAA,CAAA,UAAY;;;;;;;;;;;;;;;;;kDAKjB,8OAAC;wCAAI,WAAU;kDACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQjB;IAEA,qBACE,8OAAC;QAAK,MAAK;;0BACT,8OAAC;;kCACC,8OAAC;kCAAM;;;;;;kCACP,8OAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;;;;;;;0BAEnC,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,gBAAa;8BACZ,cAAA,8OAAC,uIAAA,CAAA,uBAAoB;kCACnB,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDACb,WAAW;;;;;;sDAEd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,uIAAA,CAAA,UAAW;;;;;8DACZ,8OAAC,wIAAA,CAAA,UAAY;;;;;;;;;;;;;;;;;8CAKjB,8OAAC,mIAAA,CAAA,UAAO;oCACN,UAAU,0HAAA,CAAA,kBAAe;oCACzB,iBAAiB;oCACjB,cAAa;oCACb,WAAU;;;;;;8CAIZ,8OAAC;oCAAI,WAAU;8CAEb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrB", "debugId": null}}]}