{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_adbadb87._.js", "server/edge/chunks/[root-of-the-server]__e07e566a._.js", "server/edge/chunks/edge-wrapper_9cb4b359.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "4L1N1ApV6JxRkywf2zSy/shy6TmjdExKn7foFPP8JTY=", "__NEXT_PREVIEW_MODE_ID": "b1c2f9f2b452d705b5621c268cacf48d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "1368d5f883b4eb7adeb69800bb8abed8ef9a7a941ac11c3ff69765b4acbbba84", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "2eef7e10e21e51579a493d39532d5bdc75e1f77f8f61bb0fbc043b79397c5e8d"}}}, "instrumentation": null, "functions": {}}