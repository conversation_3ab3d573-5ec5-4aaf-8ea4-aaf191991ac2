(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/api/errorHandler.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ERROR_PATTERNS": (()=>ERROR_PATTERNS),
    "default": (()=>__TURBOPACK__default__export__),
    "detectAndHandleError": (()=>detectAndHandleError),
    "setupErrorInterceptor": (()=>setupErrorInterceptor),
    "useErrorHandler": (()=>useErrorHandler)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$NotificationContext$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/NotificationContext.jsx [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
;
const ERROR_PATTERNS = {
    AUTHENTICATION: {
        codes: [
            401
        ],
        keywords: [
            'unauthorized',
            'authentication',
            'token',
            'login'
        ],
        handler: 'showAuthError'
    },
    SESSION_EXPIRED: {
        codes: [
            401
        ],
        keywords: [
            'expired',
            'invalid token',
            'token expired'
        ],
        handler: 'showSessionExpiredModal'
    },
    PERMISSION_DENIED: {
        codes: [
            403
        ],
        keywords: [
            'permission',
            'forbidden',
            'access denied'
        ],
        handler: 'showAuthError'
    },
    VALIDATION: {
        codes: [
            400,
            422
        ],
        keywords: [
            'validation',
            'invalid',
            'required'
        ],
        handler: 'showValidationError'
    },
    RESUME_REQUIRED: {
        fields: [
            'resume'
        ],
        keywords: [
            'resume',
            'must be uploaded',
            'present in the student profile'
        ],
        handler: 'showMissingResumeModal'
    },
    PROFILE_INCOMPLETE: {
        keywords: [
            'profile incomplete',
            'missing profile',
            'update profile'
        ],
        handler: 'showProfileIncompleteModal'
    },
    FILE_UPLOAD: {
        keywords: [
            'file',
            'upload',
            'size',
            'format',
            'extension'
        ],
        handler: 'showFileUploadError'
    },
    NETWORK_ERROR: {
        codes: [
            'NETWORK_ERROR',
            'ECONNREFUSED',
            'ERR_NETWORK'
        ],
        keywords: [
            'network',
            'connection',
            'timeout'
        ],
        handler: 'showNetworkError'
    },
    MAINTENANCE: {
        codes: [
            503,
            502
        ],
        keywords: [
            'maintenance',
            'service unavailable',
            'temporarily unavailable'
        ],
        handler: 'showMaintenanceModal'
    }
};
const detectAndHandleError = (error, context = '', notificationHandlers)=>{
    const errorData = error?.response?.data || {};
    const errorMessage = (errorData.detail || errorData.message || error.message || '').toLowerCase();
    const statusCode = error?.response?.status;
    // Check for specific error patterns
    for (const [pattern, config] of Object.entries(ERROR_PATTERNS)){
        // Check status codes
        if (config.codes && config.codes.includes(statusCode)) {
            // Additional keyword check for more precision
            if (config.keywords && !config.keywords.some((keyword)=>errorMessage.includes(keyword))) {
                continue;
            }
            return handleSpecificError(pattern, error, context, notificationHandlers);
        }
        // Check for field-specific errors (like resume)
        if (config.fields && config.fields.some((field)=>errorData[field])) {
            return handleSpecificError(pattern, error, context, notificationHandlers);
        }
        // Check keywords in error message
        if (config.keywords && config.keywords.some((keyword)=>errorMessage.includes(keyword))) {
            return handleSpecificError(pattern, error, context, notificationHandlers);
        }
    }
    // Fallback to generic error handling
    return handleGenericError(error, context, notificationHandlers);
};
const handleSpecificError = (pattern, error, context, notificationHandlers)=>{
    const config = ERROR_PATTERNS[pattern];
    const handlerName = config.handler;
    if (notificationHandlers[handlerName]) {
        switch(handlerName){
            case 'showMissingResumeModal':
                notificationHandlers.showMissingResumeModal();
                break;
            case 'showSessionExpiredModal':
                notificationHandlers.showSessionExpiredModal();
                break;
            case 'showMaintenanceModal':
                notificationHandlers.showMaintenanceModal();
                break;
            case 'showValidationError':
                const errorData = error?.response?.data || {};
                notificationHandlers.showValidationError(`Validation Error ${context ? `in ${context}` : ''}`, errorData);
                break;
            case 'showAuthError':
                const message = error?.response?.data?.detail || error?.response?.data?.message || `Authentication failed${context ? ` while ${context}` : ''}`;
                notificationHandlers.showAuthError(message);
                break;
            case 'showFileUploadError':
                notificationHandlers.showFileUploadError();
                break;
            case 'showNetworkError':
                notificationHandlers.showNetworkError(error);
                break;
            case 'showProfileIncompleteModal':
                notificationHandlers.showProfileIncompleteModal();
                break;
            default:
                return handleGenericError(error, context, notificationHandlers);
        }
        return true; // Error was handled
    }
    return false; // Error not handled
};
const handleGenericError = (error, context, notificationHandlers)=>{
    if (notificationHandlers.handleApiError) {
        notificationHandlers.handleApiError(error, context);
        return true;
    }
    // Ultimate fallback
    console.error('Unhandled error:', error);
    return false;
};
const useErrorHandler = ()=>{
    _s();
    const notificationHandlers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$NotificationContext$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useNotification"])();
    const handleError = (error, context = '')=>{
        return detectAndHandleError(error, context, notificationHandlers);
    };
    return {
        handleError
    };
};
_s(useErrorHandler, "2+3vdp+Gj38tyC21Sdt5jR3pRwQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$NotificationContext$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useNotification"]
    ];
});
const setupErrorInterceptor = (axiosInstance, notificationHandlers)=>{
    axiosInstance.interceptors.response.use({
        "setupErrorInterceptor.use": (response)=>response
    }["setupErrorInterceptor.use"], {
        "setupErrorInterceptor.use": (error)=>{
            // Automatically handle common errors
            detectAndHandleError(error, 'API request', notificationHandlers);
            return Promise.reject(error);
        }
    }["setupErrorInterceptor.use"]);
};
const __TURBOPACK__default__export__ = {
    detectAndHandleError,
    useErrorHandler,
    setupErrorInterceptor,
    ERROR_PATTERNS
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/api/client.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$errorHandler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/api/errorHandler.js [app-client] (ecmascript)");
;
;
const client = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: ("TURBOPACK compile-time value", "http://127.0.0.1:8000") || 'http://127.0.0.1:8000',
    headers: {
        'Content-Type': 'application/json'
    }
});
// Add a request interceptor to include the auth token
client.interceptors.request.use((config)=>{
    // Get the token from localStorage
    const token = localStorage.getItem('access_token');
    // If token exists, add it to the Authorization header
    if (token) {
        config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
}, (error)=>{
    return Promise.reject(error);
});
// Add a response interceptor to handle 401 errors (token expired)
client.interceptors.response.use((response)=>response, async (error)=>{
    const originalRequest = error.config;
    // If error is 401 and we haven't tried to refresh the token yet
    if (error.response?.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true;
        try {
            // Get refresh token
            const refreshToken = localStorage.getItem('refresh_token');
            if (refreshToken) {
                // Try to get a new token
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post('http://127.0.0.1:8000/api/auth/token/refresh/', {
                    refresh: refreshToken
                });
                // Store the new tokens
                localStorage.setItem('access_token', response.data.access);
                // Update the Authorization header
                originalRequest.headers['Authorization'] = `Bearer ${response.data.access}`;
                // Retry the original request
                return client(originalRequest);
            }
        } catch (refreshError) {
            console.error('Error refreshing token:', refreshError);
            // If token refresh fails, redirect to login
            if ("TURBOPACK compile-time truthy", 1) {
                // Clear tokens
                localStorage.removeItem('access_token');
                localStorage.removeItem('refresh_token');
                // Redirect to login page
                window.location.href = '/login';
            }
        }
    }
    return Promise.reject(error);
});
const __TURBOPACK__default__export__ = client;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/api/jobs.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "applyToJob": (()=>applyToJob),
    "createJob": (()=>createJob),
    "deleteJob": (()=>deleteJob),
    "getAllApplications": (()=>getAllApplications),
    "getJobApplications": (()=>getJobApplications),
    "getJobById": (()=>getJobById),
    "listAppliedJobs": (()=>listAppliedJobs),
    "listJobs": (()=>listJobs),
    "listJobsAdmin": (()=>listJobsAdmin),
    "toggleJobPublish": (()=>toggleJobPublish),
    "updateJob": (()=>updateJob)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/api/client.js [app-client] (ecmascript)");
;
function listJobs(params = {}) {
    const queryParams = new URLSearchParams();
    // Add pagination parameters
    if (params.page) queryParams.append('page', params.page);
    if (params.per_page) queryParams.append('per_page', params.per_page);
    // Add filtering parameters
    if (params.job_type && params.job_type !== 'ALL') queryParams.append('job_type', params.job_type);
    if (params.location && params.location !== 'ALL') queryParams.append('location', params.location);
    if (params.salary_min) queryParams.append('salary_min', params.salary_min);
    if (params.search) queryParams.append('search', params.search);
    const queryString = queryParams.toString();
    const url = `/api/v1/college/default-college/jobs/${queryString ? `?${queryString}` : ''}`;
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(url);
}
function applyToJob(job, coverLetter, additionalFields = {}) {
    // Check if any additional fields contain files
    const hasFiles = Object.values(additionalFields).some((value)=>value instanceof File);
    if (hasFiles) {
        // Use FormData for file uploads
        const formData = new FormData();
        formData.append('cover_letter', coverLetter);
        // Handle additional fields with files
        Object.entries(additionalFields).forEach(([key, value])=>{
            if (value instanceof File) {
                formData.append(key, value);
            } else {
                formData.append(key, JSON.stringify(value));
            }
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/college/default-college/jobs/${job}/apply/`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });
    } else {
        // Use JSON for non-file submissions
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/college/default-college/jobs/${job}/apply/`, {
            cover_letter: coverLetter,
            additional_field_responses: additionalFields
        });
    }
}
function getJobById(jobId) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/college/default-college/jobs/${jobId}/`);
}
function listAppliedJobs() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get('/api/v1/college/default-college/jobs/applied/');
}
function createJob(jobData) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post('/api/v1/college/default-college/jobs/create/', jobData);
}
function updateJob(jobId, jobData) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].put(`/api/v1/college/default-college/jobs/${jobId}/`, jobData);
}
function deleteJob(jobId) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(`/api/v1/college/default-college/jobs/${jobId}/`);
}
function getJobApplications(jobId) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/college/default-college/jobs/${jobId}/applications/`);
}
function getAllApplications() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get('/api/v1/college/default-college/applications/');
}
function listJobsAdmin(params = {}) {
    const queryParams = new URLSearchParams();
    // Add pagination parameters
    if (params.page) queryParams.append('page', params.page);
    if (params.per_page) queryParams.append('per_page', params.per_page);
    // Add filtering parameters
    if (params.search) queryParams.append('search', params.search);
    if (params.type && params.type !== 'All') queryParams.append('job_type', params.type);
    if (params.minCTC) queryParams.append('salary_min', params.minCTC);
    if (params.maxCTC) queryParams.append('salary_max', params.maxCTC);
    if (params.minStipend) queryParams.append('stipend_min', params.minStipend);
    if (params.maxStipend) queryParams.append('stipend_max', params.maxStipend);
    if (params.location) queryParams.append('location', params.location);
    if (params.is_published !== undefined) queryParams.append('is_published', params.is_published);
    // Add company filtering
    if (params.company_id) queryParams.append('company_id', params.company_id);
    if (params.company_name) queryParams.append('company_name', params.company_name);
    const queryString = queryParams.toString();
    const url = `/api/v1/college/default-college/jobs/admin/${queryString ? `?${queryString}` : ''}`;
    console.log('🌐 listJobsAdmin calling URL:', url, 'with params:', params);
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(url).then((response)=>{
        console.log('🌐 listJobsAdmin response:', {
            status: response.status,
            totalJobs: response.data?.pagination?.total_count || 0,
            currentPage: response.data?.pagination?.current_page || 1,
            totalPages: response.data?.pagination?.total_pages || 1
        });
        return response;
    }).catch((error)=>{
        console.error('🌐 listJobsAdmin error:', error);
        console.error('🌐 listJobsAdmin error response:', error.response?.data);
        throw error;
    });
}
function toggleJobPublish(jobId) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].patch(`/api/v1/jobs/${jobId}/toggle-publish/`);
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/utils/profileValidation.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Profile validation utilities for job applications
__turbopack_context__.s({
    "CRITICAL_FIELDS": (()=>CRITICAL_FIELDS),
    "REQUIRED_PROFILE_FIELDS": (()=>REQUIRED_PROFILE_FIELDS),
    "default": (()=>__TURBOPACK__default__export__),
    "getProfileCompletionSuggestions": (()=>getProfileCompletionSuggestions),
    "validateForJobApplication": (()=>validateForJobApplication),
    "validateProfile": (()=>validateProfile)
});
const REQUIRED_PROFILE_FIELDS = {
    basic: {
        first_name: 'First Name',
        last_name: 'Last Name',
        email: 'Email Address',
        phone: 'Phone Number',
        date_of_birth: 'Date of Birth'
    },
    academic: {
        student_id: 'Student ID/Roll Number',
        branch: 'Department/Branch',
        gpa: 'CGPA/GPA',
        joining_year: 'Joining Year',
        passout_year: 'Passout Year'
    },
    contact: {
        address: 'Address',
        city: 'City',
        state: 'State',
        pincode: 'PIN Code'
    },
    documents: {
        resume: 'Resume',
        tenth_certificate: 'Class 10 Certificate',
        twelfth_certificate: 'Class 12 Certificate'
    },
    education: {
        tenth_percentage: 'Class 10 Percentage',
        twelfth_percentage: 'Class 12 Percentage',
        tenth_year_of_passing: 'Class 10 Year of Passing',
        twelfth_year_of_passing: 'Class 12 Year of Passing'
    }
};
const CRITICAL_FIELDS = [
    'first_name',
    'last_name',
    'email',
    'phone',
    'student_id',
    'branch',
    'gpa',
    'resume'
];
const validateProfile = (profile)=>{
    const missing = [];
    const warnings = [];
    const errors = [];
    if (!profile) {
        return {
            isValid: false,
            missing: [
                'Profile not found'
            ],
            warnings: [],
            errors: [
                'Please complete your profile before applying'
            ],
            score: 0
        };
    }
    // Check critical fields
    CRITICAL_FIELDS.forEach((field)=>{
        const value = profile[field];
        const fieldName = getFieldDisplayName(field);
        if (!value || typeof value === 'string' && value.trim() === '') {
            missing.push(fieldName);
        }
    });
    // Check GPA/CGPA format and range
    if (profile.gpa) {
        const gpaValue = parseFloat(profile.gpa);
        if (isNaN(gpaValue) || gpaValue < 0 || gpaValue > 10) {
            errors.push('CGPA must be between 0 and 10');
        } else if (gpaValue < 6.0) {
            warnings.push('CGPA below 6.0 may limit job opportunities');
        }
    }
    // Check phone number format
    if (profile.phone) {
        const phoneRegex = /^[6-9]\d{9}$/;
        if (!phoneRegex.test(profile.phone.replace(/[^\d]/g, ''))) {
            errors.push('Phone number must be a valid 10-digit Indian mobile number');
        }
    }
    // Check email format
    if (profile.email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(profile.email)) {
            errors.push('Please provide a valid email address');
        }
    }
    // Check resume
    if (!profile.resume && !profile.resume_url) {
        missing.push('Resume');
        errors.push('Resume is required for job applications');
    }
    // Check academic details
    if (profile.tenth_percentage && profile.tenth_percentage < 60) {
        warnings.push('Class 10 percentage below 60% may limit opportunities');
    }
    if (profile.twelfth_percentage && profile.twelfth_percentage < 60) {
        warnings.push('Class 12 percentage below 60% may limit opportunities');
    }
    // Calculate completeness score
    const totalFields = Object.keys(REQUIRED_PROFILE_FIELDS).reduce((acc, section)=>acc + Object.keys(REQUIRED_PROFILE_FIELDS[section]).length, 0);
    const filledFields = totalFields - missing.length;
    const score = Math.round(filledFields / totalFields * 100);
    // Determine if profile is valid for job applications
    const isValid = missing.length === 0 && errors.length === 0;
    const canApply = CRITICAL_FIELDS.every((field)=>{
        const value = profile[field];
        return value && (typeof value !== 'string' || value.trim() !== '');
    });
    return {
        isValid,
        canApply,
        missing,
        warnings,
        errors,
        score,
        summary: generateSummary(score, missing.length, warnings.length, errors.length)
    };
};
const validateForJobApplication = (profile, jobRequirements = {})=>{
    const baseValidation = validateProfile(profile);
    // Additional job-specific validations
    const jobErrors = [];
    const jobWarnings = [];
    if (jobRequirements.minCgpa && profile.gpa) {
        const gpaValue = parseFloat(profile.gpa);
        if (gpaValue < jobRequirements.minCgpa) {
            jobErrors.push(`CGPA ${jobRequirements.minCgpa} or above required`);
        }
    }
    if (jobRequirements.allowedBranches && profile.branch) {
        if (!jobRequirements.allowedBranches.includes(profile.branch)) {
            jobErrors.push(`This job is not open for ${profile.branch} students`);
        }
    }
    if (jobRequirements.minTenthPercentage && profile.tenth_percentage) {
        if (profile.tenth_percentage < jobRequirements.minTenthPercentage) {
            jobErrors.push(`Class 10: ${jobRequirements.minTenthPercentage}% or above required`);
        }
    }
    if (jobRequirements.minTwelfthPercentage && profile.twelfth_percentage) {
        if (profile.twelfth_percentage < jobRequirements.minTwelfthPercentage) {
            jobErrors.push(`Class 12: ${jobRequirements.minTwelfthPercentage}% or above required`);
        }
    }
    return {
        ...baseValidation,
        jobSpecific: {
            errors: jobErrors,
            warnings: jobWarnings,
            isEligible: jobErrors.length === 0
        }
    };
};
const getProfileCompletionSuggestions = (validation)=>{
    const suggestions = [];
    if (validation.missing.includes('Resume')) {
        suggestions.push({
            type: 'critical',
            title: 'Upload Resume',
            description: 'A resume is required for all job applications',
            action: 'upload_resume',
            icon: 'upload'
        });
    }
    if (validation.missing.some((field)=>[
            'First Name',
            'Last Name',
            'Email',
            'Phone'
        ].includes(field))) {
        suggestions.push({
            type: 'critical',
            title: 'Complete Basic Information',
            description: 'Fill in your personal details',
            action: 'edit_basic_info',
            icon: 'user'
        });
    }
    if (validation.missing.some((field)=>[
            'CGPA/GPA',
            'Department/Branch'
        ].includes(field))) {
        suggestions.push({
            type: 'critical',
            title: 'Add Academic Details',
            description: 'Provide your academic information',
            action: 'edit_academic_info',
            icon: 'graduation-cap'
        });
    }
    if (validation.warnings.some((w)=>w.includes('percentage'))) {
        suggestions.push({
            type: 'warning',
            title: 'Review Academic Performance',
            description: 'Low grades may limit job opportunities',
            action: 'review_grades',
            icon: 'alert-triangle'
        });
    }
    if (validation.score < 80) {
        suggestions.push({
            type: 'info',
            title: 'Complete Profile',
            description: `Profile is ${validation.score}% complete. More complete profiles get better job matches.`,
            action: 'complete_profile',
            icon: 'info'
        });
    }
    return suggestions;
};
const getFieldDisplayName = (field)=>{
    for (const section of Object.values(REQUIRED_PROFILE_FIELDS)){
        if (section[field]) {
            return section[field];
        }
    }
    return field.replace(/_/g, ' ').replace(/\b\w/g, (l)=>l.toUpperCase());
};
const generateSummary = (score, missingCount, warningCount, errorCount)=>{
    if (errorCount > 0) {
        return {
            status: 'error',
            message: `${errorCount} error(s) need to be fixed before applying`,
            color: 'red'
        };
    }
    if (missingCount > 0) {
        return {
            status: 'incomplete',
            message: `${missingCount} required field(s) missing`,
            color: 'yellow'
        };
    }
    if (warningCount > 0) {
        return {
            status: 'warning',
            message: `Profile complete with ${warningCount} warning(s)`,
            color: 'orange'
        };
    }
    if (score >= 90) {
        return {
            status: 'excellent',
            message: 'Profile is excellent and ready for applications',
            color: 'green'
        };
    }
    if (score >= 80) {
        return {
            status: 'good',
            message: 'Profile is good for most applications',
            color: 'blue'
        };
    }
    return {
        status: 'needs_improvement',
        message: 'Profile needs more information for better job matching',
        color: 'yellow'
    };
};
const __TURBOPACK__default__export__ = {
    validateProfile,
    validateForJobApplication,
    getProfileCompletionSuggestions,
    REQUIRED_PROFILE_FIELDS,
    CRITICAL_FIELDS
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/jobpostings/[id]/apply/page.jsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>JobApplicationPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$left$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowLeft$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/arrow-left.js [app-client] (ecmascript) <export default as ArrowLeft>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-client] (ecmascript) <export default as Clock>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$map$2d$pin$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MapPin$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/map-pin.js [app-client] (ecmascript) <export default as MapPin>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$building$2d$2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Building2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/building-2.js [app-client] (ecmascript) <export default as Building2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/calendar.js [app-client] (ecmascript) <export default as Calendar>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$dollar$2d$sign$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__DollarSign$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/dollar-sign.js [app-client] (ecmascript) <export default as DollarSign>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-check-big.js [app-client] (ecmascript) <export default as CheckCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-alert.js [app-client] (ecmascript) <export default as AlertCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/users.js [app-client] (ecmascript) <export default as Users>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/file-text.js [app-client] (ecmascript) <export default as FileText>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$briefcase$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Briefcase$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/briefcase.js [app-client] (ecmascript) <export default as Briefcase>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$award$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Award$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/award.js [app-client] (ecmascript) <export default as Award>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$jobs$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/api/jobs.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/api/client.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$NotificationContext$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/NotificationContext.jsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$profileValidation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/profileValidation.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
// --- Reusable UI Components ---
// A reusable component for each section card in the form and review pages
const SectionCard = ({ title, onEdit, children, icon })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "bg-white p-4 sm:p-6 rounded-xl shadow-sm border border-gray-200/80 mb-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex justify-between items-center mb-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center",
                        children: [
                            icon && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "mr-2 text-lg",
                                children: icon
                            }, void 0, false, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 18,
                                columnNumber: 18
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-lg font-semibold text-gray-800",
                                children: title
                            }, void 0, false, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 19,
                                columnNumber: 9
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 17,
                        columnNumber: 7
                    }, this),
                    onEdit && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: onEdit,
                        className: "text-sm font-medium text-indigo-600 hover:text-indigo-800 transition-colors duration-200",
                        children: "Edit"
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 22,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 16,
                columnNumber: 5
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4",
                children: children
            }, void 0, false, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 27,
                columnNumber: 5
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
        lineNumber: 15,
        columnNumber: 3
    }, this);
_c = SectionCard;
// A reusable input field component
const InputField = ({ label, type = 'text', placeholder, name, value, onChange, isFullWidth = false, required = false })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: isFullWidth ? 'md:col-span-2' : '',
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                htmlFor: name,
                className: "block text-sm font-medium text-gray-700 mb-1.5",
                children: [
                    label,
                    " ",
                    required && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "text-red-500",
                        children: "*"
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 37,
                        columnNumber: 28
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 36,
                columnNumber: 5
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                type: type,
                name: name,
                id: name,
                placeholder: placeholder,
                value: value,
                onChange: onChange,
                required: required,
                className: "w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 transition-shadow duration-200"
            }, void 0, false, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 39,
                columnNumber: 5
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
        lineNumber: 35,
        columnNumber: 3
    }, this);
_c1 = InputField;
// A reusable select field component
const SelectField = ({ label, name, value, onChange, children, required = false })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                htmlFor: name,
                className: "block text-sm font-medium text-gray-700 mb-1.5",
                children: [
                    label,
                    " ",
                    required && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "text-red-500",
                        children: "*"
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 56,
                        columnNumber: 28
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 55,
                columnNumber: 5
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                id: name,
                name: name,
                value: value,
                onChange: onChange,
                required: required,
                className: "w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 transition-shadow duration-200",
                children: children
            }, void 0, false, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 58,
                columnNumber: 5
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
        lineNumber: 54,
        columnNumber: 3
    }, this);
_c2 = SelectField;
// A reusable textarea component
const TextareaField = ({ label, name, value, onChange, placeholder, rows = 6, tip, required = false })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "md:col-span-2",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                htmlFor: name,
                className: "block text-sm font-medium text-gray-700 mb-1.5",
                children: [
                    label,
                    " ",
                    required && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "text-red-500",
                        children: "*"
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 75,
                        columnNumber: 28
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 74,
                columnNumber: 5
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                id: name,
                name: name,
                rows: rows,
                value: value,
                onChange: onChange,
                required: required,
                className: "w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 transition-shadow duration-200",
                placeholder: placeholder
            }, void 0, false, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 77,
                columnNumber: 5
            }, this),
            tip && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "mt-2 text-xs text-gray-500",
                children: tip
            }, void 0, false, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 87,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
        lineNumber: 73,
        columnNumber: 3
    }, this);
_c3 = TextareaField;
// A reusable file input component with preview
const FileInput = ({ label, name, fileName, onChange, required = false })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "md:col-span-2",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                className: "block text-sm font-medium text-gray-700 mb-1.5",
                children: [
                    label,
                    " ",
                    required && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "text-red-500",
                        children: "*"
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 95,
                        columnNumber: 28
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 94,
                columnNumber: 5
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mt-1 flex items-center justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg hover:border-indigo-400 transition-colors duration-200",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-1 text-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                            className: "mx-auto h-12 w-12 text-gray-400",
                            stroke: "currentColor",
                            fill: "none",
                            viewBox: "0 0 48 48",
                            "aria-hidden": "true",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                d: "M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8",
                                strokeWidth: 2,
                                strokeLinecap: "round",
                                strokeLinejoin: "round"
                            }, void 0, false, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 100,
                                columnNumber: 11
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                            lineNumber: 99,
                            columnNumber: 9
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex text-sm text-gray-600",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    htmlFor: name,
                                    className: "relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500 focus-within:outline-none",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            children: "Upload a file"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 104,
                                            columnNumber: 13
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                            id: name,
                                            name: name,
                                            type: "file",
                                            className: "sr-only",
                                            onChange: onChange,
                                            required: required
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 105,
                                            columnNumber: 13
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                    lineNumber: 103,
                                    columnNumber: 11
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "pl-1",
                                    children: "or drag and drop"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                    lineNumber: 107,
                                    columnNumber: 11
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                            lineNumber: 102,
                            columnNumber: 9
                        }, this),
                        fileName ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-sm font-semibold text-green-600",
                            children: fileName
                        }, void 0, false, {
                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                            lineNumber: 110,
                            columnNumber: 11
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-xs text-gray-500",
                            children: "PDF, DOCX, PNG, JPG up to 10MB"
                        }, void 0, false, {
                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                            lineNumber: 112,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                    lineNumber: 98,
                    columnNumber: 7
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 97,
                columnNumber: 5
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
        lineNumber: 93,
        columnNumber: 3
    }, this);
_c4 = FileInput;
// --- Job Details Preview Component ---
const JobDetailsPreview = ({ job })=>{
    if (!job) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "bg-white rounded-xl shadow-sm border border-gray-200/80 p-6 mb-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        className: "text-2xl font-bold text-gray-900 mb-2",
                        children: job.title
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 126,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center text-lg text-gray-600 mb-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$building$2d$2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Building2$3e$__["Building2"], {
                                className: "w-5 h-5 mr-2"
                            }, void 0, false, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 128,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: job.company_name
                            }, void 0, false, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 129,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 127,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center text-sm text-indigo-600 font-medium",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$briefcase$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Briefcase$3e$__["Briefcase"], {
                                className: "w-4 h-4 mr-1"
                            }, void 0, false, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 132,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: job.job_type || 'FULL TIME'
                            }, void 0, false, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 133,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 131,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 125,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6 pb-6 border-b border-gray-200",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$dollar$2d$sign$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__DollarSign$3e$__["DollarSign"], {
                                className: "w-5 h-5 text-indigo-600"
                            }, void 0, false, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 140,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-sm font-medium text-gray-900",
                                        children: "Salary"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                        lineNumber: 142,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-sm text-gray-600",
                                        children: job.salary_min && job.salary_max ? `$${job.salary_min} - $${job.salary_max}` : "Competitive salary"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                        lineNumber: 143,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 141,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 139,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$map$2d$pin$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MapPin$3e$__["MapPin"], {
                                className: "w-5 h-5 text-indigo-600"
                            }, void 0, false, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 152,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-sm font-medium text-gray-900",
                                        children: "Location"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                        lineNumber: 154,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-sm text-gray-600",
                                        children: job.location
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                        lineNumber: 155,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 153,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 151,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__["Calendar"], {
                                className: "w-5 h-5 text-indigo-600"
                            }, void 0, false, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 160,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-sm font-medium text-gray-900",
                                        children: "Deadline"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                        lineNumber: 162,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-sm text-gray-600",
                                        children: job.application_deadline ? new Date(job.application_deadline).toLocaleDateString() : "Not specified"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                        lineNumber: 163,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 161,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 159,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"], {
                                className: "w-5 h-5 text-indigo-600"
                            }, void 0, false, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 172,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-sm font-medium text-gray-900",
                                        children: "Duration"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                        lineNumber: 174,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-sm text-gray-600",
                                        children: job.duration || "Not specified"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                        lineNumber: 175,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 173,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 171,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 138,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-lg font-semibold text-gray-900 mb-3",
                        children: "Job Description"
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 182,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-gray-700 leading-relaxed",
                        children: job.description
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 183,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 181,
                columnNumber: 7
            }, this),
            job.requirements && job.requirements.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-lg font-semibold text-gray-900 mb-3",
                        children: "Requirements"
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 189,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-wrap gap-2",
                        children: job.requirements.map((req, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "bg-gray-100 border border-gray-200 rounded-lg px-3 py-1 text-sm font-medium text-gray-700",
                                children: req
                            }, index, false, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 192,
                                columnNumber: 15
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 190,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 188,
                columnNumber: 9
            }, this),
            job.additional_fields && job.additional_fields.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-lg font-semibold text-gray-900 mb-3 flex items-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__["FileText"], {
                                className: "w-5 h-5 mr-2 text-indigo-600"
                            }, void 0, false, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 204,
                                columnNumber: 13
                            }, this),
                            "Additional Information Required"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 203,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-gray-50 rounded-lg p-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-sm text-gray-600 mb-3",
                                children: "You'll need to provide the following information when applying:"
                            }, void 0, false, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 208,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "space-y-2",
                                children: job.additional_fields.map((field, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center gap-2",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "w-2 h-2 bg-indigo-600 rounded-full"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                lineNumber: 214,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-sm text-gray-700",
                                                children: [
                                                    field.label,
                                                    field.required && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-red-500 ml-1",
                                                        children: "*"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                        lineNumber: 217,
                                                        columnNumber: 40
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                lineNumber: 215,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded",
                                                children: field.type
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                lineNumber: 219,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, index, true, {
                                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                        lineNumber: 213,
                                        columnNumber: 17
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 211,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 207,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 202,
                columnNumber: 9
            }, this),
            job.interview_rounds && job.interview_rounds.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-lg font-semibold text-gray-900 mb-3 flex items-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__["Users"], {
                                className: "w-5 h-5 mr-2 text-indigo-600"
                            }, void 0, false, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 233,
                                columnNumber: 13
                            }, this),
                            "Interview Process"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 232,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-3",
                        children: job.interview_rounds.map((round, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center gap-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "w-8 h-8 bg-indigo-600 text-white rounded-full flex items-center justify-center text-sm font-semibold flex-shrink-0",
                                        children: index + 1
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                        lineNumber: 239,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex-1",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                className: "font-medium text-gray-900",
                                                children: round.name
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                lineNumber: 243,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-sm text-gray-600",
                                                children: round.description
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                lineNumber: 244,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                        lineNumber: 242,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, index, true, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 238,
                                columnNumber: 15
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 236,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 231,
                columnNumber: 9
            }, this),
            (job.min_cgpa || job.allowed_branches || job.min_tenth_percentage || job.min_twelfth_percentage) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-lg font-semibold text-gray-900 mb-3 flex items-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$award$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Award$3e$__["Award"], {
                                className: "w-5 h-5 mr-2 text-indigo-600"
                            }, void 0, false, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 256,
                                columnNumber: 13
                            }, this),
                            "Eligibility Requirements"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 255,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-blue-50 border border-blue-200 rounded-lg p-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid grid-cols-1 md:grid-cols-2 gap-4",
                            children: [
                                job.min_cgpa && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center gap-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__["CheckCircle"], {
                                            className: "w-4 h-4 text-blue-600"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 263,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-sm text-gray-700",
                                            children: [
                                                "Minimum CGPA: ",
                                                job.min_cgpa
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 264,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                    lineNumber: 262,
                                    columnNumber: 17
                                }, this),
                                job.allowed_branches && job.allowed_branches.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center gap-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__["CheckCircle"], {
                                            className: "w-4 h-4 text-blue-600"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 269,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-sm text-gray-700",
                                            children: [
                                                "Branches: ",
                                                job.allowed_branches.join(', ')
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 270,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                    lineNumber: 268,
                                    columnNumber: 17
                                }, this),
                                job.min_tenth_percentage && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center gap-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__["CheckCircle"], {
                                            className: "w-4 h-4 text-blue-600"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 275,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-sm text-gray-700",
                                            children: [
                                                "10th Percentage: ",
                                                job.min_tenth_percentage,
                                                "%"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 276,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                    lineNumber: 274,
                                    columnNumber: 17
                                }, this),
                                job.min_twelfth_percentage && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center gap-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__["CheckCircle"], {
                                            className: "w-4 h-4 text-blue-600"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 281,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-sm text-gray-700",
                                            children: [
                                                "12th Percentage: ",
                                                job.min_twelfth_percentage,
                                                "%"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 282,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                    lineNumber: 280,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                            lineNumber: 260,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 259,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 254,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-yellow-50 border border-yellow-200 rounded-lg p-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-start gap-3",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__["AlertCircle"], {
                            className: "w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0"
                        }, void 0, false, {
                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                            lineNumber: 293,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                    className: "font-medium text-yellow-800 mb-1",
                                    children: "Application Tips"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                    lineNumber: 295,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                    className: "text-sm text-yellow-700 space-y-1",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: "• Ensure your profile is complete with all required information"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 297,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: "• Prepare a compelling cover letter highlighting relevant experience"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 298,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: "• Have your resume and any required documents ready"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 299,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: "• Review the job requirements and eligibility criteria carefully"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 300,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                    lineNumber: 296,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                            lineNumber: 294,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                    lineNumber: 292,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 291,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
        lineNumber: 124,
        columnNumber: 5
    }, this);
};
_c5 = JobDetailsPreview;
// --- Form View Component ---
const ApplicationForm = ({ job, formData, setFormData, setStep })=>{
    const handleChange = (e)=>{
        const { name, value, type, files } = e.target;
        if (type === 'file') {
            setFormData((prev)=>({
                    ...prev,
                    additional_fields: {
                        ...prev.additional_fields,
                        [name]: files[0]
                    }
                }));
        } else {
            setFormData((prev)=>({
                    ...prev,
                    [name]: value
                }));
        }
    };
    const handleAdditionalFieldChange = (fieldId, value)=>{
        setFormData((prev)=>({
                ...prev,
                additional_fields: {
                    ...prev.additional_fields,
                    [fieldId]: value
                }
            }));
    };
    const renderAdditionalField = (field)=>{
        const fieldId = `field_${field.id}`;
        const value = formData.additional_fields[fieldId] || '';
        switch(field.type){
            case 'text':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(InputField, {
                    label: field.label,
                    name: fieldId,
                    value: value,
                    onChange: (e)=>handleAdditionalFieldChange(fieldId, e.target.value),
                    placeholder: `Enter ${field.label.toLowerCase()}`,
                    required: field.required
                }, field.id, false, {
                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                    lineNumber: 343,
                    columnNumber: 11
                }, this);
            case 'number':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(InputField, {
                    label: field.label,
                    type: "number",
                    name: fieldId,
                    value: value,
                    onChange: (e)=>handleAdditionalFieldChange(fieldId, e.target.value),
                    placeholder: `Enter ${field.label.toLowerCase()}`,
                    required: field.required
                }, field.id, false, {
                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                    lineNumber: 356,
                    columnNumber: 11
                }, this);
            case 'file':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(FileInput, {
                    label: field.label,
                    name: fieldId,
                    fileName: value instanceof File ? value.name : '',
                    onChange: (e)=>{
                        const file = e.target.files[0];
                        handleAdditionalFieldChange(fieldId, file);
                    },
                    required: field.required
                }, field.id, false, {
                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                    lineNumber: 370,
                    columnNumber: 11
                }, this);
            case 'multiple_choice':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(SelectField, {
                    label: field.label,
                    name: fieldId,
                    value: value,
                    onChange: (e)=>handleAdditionalFieldChange(fieldId, e.target.value),
                    required: field.required,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                            value: "",
                            children: [
                                "Select ",
                                field.label.toLowerCase()
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                            lineNumber: 393,
                            columnNumber: 13
                        }, this),
                        field.options?.map((option, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                value: option,
                                children: option
                            }, index, false, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 395,
                                columnNumber: 15
                            }, this))
                    ]
                }, field.id, true, {
                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                    lineNumber: 385,
                    columnNumber: 11
                }, this);
            default:
                return null;
        }
    };
    const handleSubmit = (e)=>{
        e.preventDefault();
        setStep('review');
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
        onSubmit: handleSubmit,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(SectionCard, {
                title: "📝 Cover Letter",
                icon: "📝",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(TextareaField, {
                    label: "Cover Letter",
                    name: "cover_letter",
                    value: formData.cover_letter,
                    onChange: handleChange,
                    placeholder: "Dear Hiring Manager, I am writing to express my interest in this position...",
                    tip: "Tip: Mention specific skills from the job requirements and explain how your experience aligns with the role.",
                    required: true
                }, void 0, false, {
                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                    lineNumber: 415,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 414,
                columnNumber: 7
            }, this),
            job.additional_fields && job.additional_fields.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(SectionCard, {
                title: "📋 Additional Information",
                icon: "📋",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "md:col-span-2 space-y-4",
                    children: job.additional_fields.map((field)=>renderAdditionalField(field))
                }, void 0, false, {
                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                    lineNumber: 428,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 427,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mt-8 flex justify-end",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    type: "submit",
                    className: "w-full sm:w-auto px-6 py-3 bg-indigo-600 text-white font-semibold rounded-lg shadow-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200",
                    children: "Review Application"
                }, void 0, false, {
                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                    lineNumber: 435,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 434,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
        lineNumber: 413,
        columnNumber: 5
    }, this);
};
_c6 = ApplicationForm;
// --- Review View Component ---
const ReviewApplication = ({ job, formData, setStep, onSubmit, isSubmitting })=>{
    // A reusable row for displaying a piece of data
    const DataRow = ({ label, value })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "py-3 sm:grid sm:grid-cols-3 sm:gap-4",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("dt", {
                    className: "text-sm font-medium text-gray-500",
                    children: label
                }, void 0, false, {
                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                    lineNumber: 452,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("dd", {
                    className: "mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2 break-words",
                    children: value || 'Not provided'
                }, void 0, false, {
                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                    lineNumber: 453,
                    columnNumber: 7
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
            lineNumber: 451,
            columnNumber: 5
        }, this);
    const TextData = ({ label, value })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "py-3",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("dt", {
                    className: "text-sm font-medium text-gray-500 mb-2",
                    children: label
                }, void 0, false, {
                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                    lineNumber: 459,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("dd", {
                    className: "text-sm text-gray-800 p-4 bg-gray-50 rounded-lg whitespace-pre-wrap",
                    children: value || 'Not provided'
                }, void 0, false, {
                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                    lineNumber: 460,
                    columnNumber: 7
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
            lineNumber: 458,
            columnNumber: 5
        }, this);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(SectionCard, {
                title: "📝 Cover Letter",
                onEdit: ()=>setStep('form'),
                icon: "📝",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "md:col-span-2",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(TextData, {
                        label: "Cover Letter",
                        value: formData.cover_letter
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 468,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                    lineNumber: 467,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 466,
                columnNumber: 7
            }, this),
            job.additional_fields && job.additional_fields.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(SectionCard, {
                title: "📋 Additional Information",
                onEdit: ()=>setStep('form'),
                icon: "📋",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "md:col-span-2 divide-y divide-gray-200",
                    children: job.additional_fields.map((field)=>{
                        const fieldId = `field_${field.id}`;
                        const value = formData.additional_fields[fieldId];
                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(DataRow, {
                            label: field.label,
                            value: field.type === 'file' && value instanceof File ? value.name : value
                        }, field.id, false, {
                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                            lineNumber: 479,
                            columnNumber: 17
                        }, this);
                    })
                }, void 0, false, {
                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                    lineNumber: 474,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 473,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mt-8 flex justify-between",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        type: "button",
                        onClick: ()=>setStep('form'),
                        className: "px-6 py-3 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200",
                        children: "Back to Edit"
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 491,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        type: "button",
                        onClick: onSubmit,
                        disabled: isSubmitting,
                        className: "px-8 py-3 bg-green-600 text-white font-semibold rounded-lg shadow-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 disabled:opacity-50",
                        children: isSubmitting ? "Submitting..." : "Submit Application"
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 498,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 490,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
        lineNumber: 465,
        columnNumber: 5
    }, this);
};
_c7 = ReviewApplication;
function JobApplicationPage() {
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const params = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useParams"])();
    const jobId = params.id;
    const { showApplicationSubmissionError, showSuccess, handleApiError, showProfileIncompleteModal } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$NotificationContext$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useNotification"])();
    const [job, setJob] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [step, setStep] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('form'); // 'form' or 'review'
    const [isSubmitting, setIsSubmitting] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [userProfile, setUserProfile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [profileValidation, setProfileValidation] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Form data state
    const [formData, setFormData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        cover_letter: '',
        additional_fields: {}
    });
    // Fetch job details
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "JobApplicationPage.useEffect": ()=>{
            const fetchJobDetails = {
                "JobApplicationPage.useEffect.fetchJobDetails": async ()=>{
                    try {
                        setLoading(true);
                        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/college/default-college/jobs/${jobId}/`);
                        setJob(response.data);
                        setError(null);
                    } catch (err) {
                        console.error('Failed to fetch job details:', err);
                        handleApiError(err, 'loading job details');
                        setError('Failed to load job details. Please try again.');
                    } finally{
                        setLoading(false);
                    }
                }
            }["JobApplicationPage.useEffect.fetchJobDetails"];
            if (jobId) {
                fetchJobDetails();
                fetchUserProfile();
            }
        }
    }["JobApplicationPage.useEffect"], [
        jobId
    ]);
    // Fetch user profile for validation
    const fetchUserProfile = async ()=>{
        try {
            const token = localStorage.getItem('access_token');
            if (!token) return;
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get('/api/auth/profile/');
            const profile = response.data;
            setUserProfile(profile);
            // Validate profile for job application
            const jobRequirements = job ? {
                minCgpa: job.min_cgpa,
                allowedBranches: job.allowed_branches,
                minTenthPercentage: job.min_tenth_percentage,
                minTwelfthPercentage: job.min_twelfth_percentage
            } : {};
            const validation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$profileValidation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["validateForJobApplication"])(profile, jobRequirements);
            setProfileValidation(validation);
        } catch (err) {
            console.error('Failed to fetch user profile:', err);
        }
    };
    // Re-validate when job data changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "JobApplicationPage.useEffect": ()=>{
            if (userProfile && job) {
                const jobRequirements = {
                    minCgpa: job.min_cgpa,
                    allowedBranches: job.allowed_branches,
                    minTenthPercentage: job.min_tenth_percentage,
                    minTwelfthPercentage: job.min_twelfth_percentage
                };
                const validation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$profileValidation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["validateForJobApplication"])(userProfile, jobRequirements);
                setProfileValidation(validation);
            }
        }
    }["JobApplicationPage.useEffect"], [
        userProfile,
        job
    ]);
    const handleSubmit = async ()=>{
        // Check profile validation first
        if (profileValidation && !profileValidation.canApply) {
            const missingFields = profileValidation.missing.filter((field)=>[
                    'Resume',
                    'First Name',
                    'Last Name',
                    'Email',
                    'Student ID/Roll Number'
                ].includes(field));
            showProfileIncompleteModal(missingFields);
            return;
        }
        // Check job-specific eligibility
        if (profileValidation?.jobSpecific && !profileValidation.jobSpecific.isEligible) {
            showApplicationSubmissionError({
                response: {
                    data: {
                        eligibility: profileValidation.jobSpecific.errors
                    }
                }
            });
            return;
        }
        if (!formData.cover_letter.trim()) {
            showApplicationSubmissionError({
                response: {
                    data: {
                        cover_letter: [
                            'Cover letter is required.'
                        ]
                    }
                }
            });
            return;
        }
        setIsSubmitting(true);
        try {
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$jobs$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["applyToJob"])(jobId, formData.cover_letter, formData.additional_fields);
            // Success - show success notification and redirect
            showSuccess('Application Submitted!', 'Your job application has been submitted successfully. Good luck!');
            // Redirect after a short delay to let user see the success message
            setTimeout(()=>{
                router.push('/jobpostings');
            }, 2000);
        } catch (err) {
            console.error('Failed to submit application:', err);
            showApplicationSubmissionError(err);
        } finally{
            setIsSubmitting(false);
        }
    };
    if (loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen bg-gray-50 flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "animate-spin rounded-full h-16 w-16 border-b-2 border-indigo-600 mx-auto"
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 653,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "mt-4 text-gray-600",
                        children: "Loading job details..."
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 654,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 652,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
            lineNumber: 651,
            columnNumber: 7
        }, this);
    }
    if (error) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen bg-gray-50 flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-red-600 mb-4",
                        children: error
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 664,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>router.push('/jobpostings'),
                        className: "px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700",
                        children: "Back to Jobs"
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 665,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 663,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
            lineNumber: 662,
            columnNumber: 7
        }, this);
    }
    if (!job) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen bg-gray-50 flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-gray-600 mb-4",
                        children: "Job not found"
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 680,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>router.push('/jobpostings'),
                        className: "px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700",
                        children: "Back to Jobs"
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 681,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                lineNumber: 679,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
            lineNumber: 678,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "bg-gray-50 font-sans",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "container mx-auto max-w-4xl p-4 sm:p-6 lg:p-8",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
                    className: "mb-8",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center mb-4",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>router.push('/jobpostings'),
                                className: "flex items-center text-gray-600 hover:text-gray-900 mr-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$left$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowLeft$3e$__["ArrowLeft"], {
                                        className: "w-5 h-5 mr-2"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                        lineNumber: 703,
                                        columnNumber: 15
                                    }, this),
                                    "Back to Jobs"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                lineNumber: 699,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                            lineNumber: 698,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-white rounded-xl shadow-sm border border-gray-200/80 p-6 mb-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex flex-col lg:flex-row lg:items-center lg:justify-between mb-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                                    className: "text-2xl sm:text-3xl font-bold text-gray-900 mb-2",
                                                    children: job.title
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                    lineNumber: 711,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center text-lg text-gray-600 mb-2",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$building$2d$2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Building2$3e$__["Building2"], {
                                                            className: "w-5 h-5 mr-2"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                            lineNumber: 713,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            children: job.company_name
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                            lineNumber: 714,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                    lineNumber: 712,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 710,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center space-x-2 mt-4 lg:mt-0",
                                            children: [
                                                1,
                                                2,
                                                3
                                            ].map((stepNumber)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: `w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${step === 'form' && stepNumber === 1 || step === 'review' && stepNumber <= 2 ? 'bg-indigo-600 text-white' : 'bg-gray-200 text-gray-600'}`,
                                                            children: stepNumber
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                            lineNumber: 722,
                                                            columnNumber: 21
                                                        }, this),
                                                        stepNumber < 3 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: `w-8 h-0.5 mx-2 ${step === 'review' && stepNumber < 2 ? 'bg-indigo-600' : 'bg-gray-200'}`
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                            lineNumber: 730,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, stepNumber, true, {
                                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                    lineNumber: 721,
                                                    columnNumber: 19
                                                }, this))
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 719,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                    lineNumber: 709,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "grid grid-cols-1 md:grid-cols-3 gap-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center text-sm text-gray-600",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$map$2d$pin$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MapPin$3e$__["MapPin"], {
                                                    className: "w-4 h-4 mr-2"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                    lineNumber: 741,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: job.location
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                    lineNumber: 742,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 740,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center text-sm text-gray-600",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$dollar$2d$sign$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__DollarSign$3e$__["DollarSign"], {
                                                    className: "w-4 h-4 mr-2"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                    lineNumber: 745,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: job.salary_min && job.salary_max ? `$${job.salary_min} - $${job.salary_max}` : "Competitive salary"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                    lineNumber: 746,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 744,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center text-sm text-gray-600",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__["Calendar"], {
                                                    className: "w-4 h-4 mr-2"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                    lineNumber: 753,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: [
                                                        "Deadline: ",
                                                        job.application_deadline ? new Date(job.application_deadline).toLocaleDateString() : "Not specified"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                                    lineNumber: 754,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                            lineNumber: 752,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                                    lineNumber: 739,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                            lineNumber: 708,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                    lineNumber: 697,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(JobDetailsPreview, {
                    job: job
                }, void 0, false, {
                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                    lineNumber: 765,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
                    children: step === 'form' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ApplicationForm, {
                        job: job,
                        formData: formData,
                        setFormData: setFormData,
                        setStep: setStep
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 770,
                        columnNumber: 13
                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ReviewApplication, {
                        job: job,
                        formData: formData,
                        setStep: setStep,
                        onSubmit: handleSubmit,
                        isSubmitting: isSubmitting
                    }, void 0, false, {
                        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                        lineNumber: 777,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
                    lineNumber: 768,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
            lineNumber: 694,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/jobpostings/[id]/apply/page.jsx",
        lineNumber: 693,
        columnNumber: 5
    }, this);
}
_s(JobApplicationPage, "fqmXvgjo3DxfOKsPwq19Zg2UF/g=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useParams"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$NotificationContext$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useNotification"]
    ];
});
_c8 = JobApplicationPage;
var _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8;
__turbopack_context__.k.register(_c, "SectionCard");
__turbopack_context__.k.register(_c1, "InputField");
__turbopack_context__.k.register(_c2, "SelectField");
__turbopack_context__.k.register(_c3, "TextareaField");
__turbopack_context__.k.register(_c4, "FileInput");
__turbopack_context__.k.register(_c5, "JobDetailsPreview");
__turbopack_context__.k.register(_c6, "ApplicationForm");
__turbopack_context__.k.register(_c7, "ReviewApplication");
__turbopack_context__.k.register(_c8, "JobApplicationPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_1623c2c6._.js.map