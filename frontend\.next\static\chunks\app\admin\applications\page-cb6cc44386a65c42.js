(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5940],{9022:(e,t,s)=>{Promise.resolve().then(s.bind(s,45026))},37719:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(23464);s(73983);let l=a.A.create({baseURL:"http://127.0.0.1:8000",headers:{"Content-Type":"application/json"}});l.interceptors.request.use(e=>{let t=localStorage.getItem("access_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),l.interceptors.response.use(e=>e,async e=>{var t;let s=e.config;if((null==(t=e.response)?void 0:t.status)===401&&!s._retry){s._retry=!0;try{let e=localStorage.getItem("refresh_token");if(e){let t=await a.A.post("http://127.0.0.1:8000/api/auth/token/refresh/",{refresh:e});return localStorage.setItem("access_token",t.data.access),s.headers.Authorization="Bearer ".concat(t.data.access),l(s)}}catch(e){console.error("Error refreshing token:",e),localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),window.location.href="/login"}}return Promise.reject(e)});let r=l},45026:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>I});var a=s(95155),l=s(12115),r=s(35695),n=s(14186),i=s(92657),d=s(40646),o=s(54861),c=s(66932),m=s(91788),x=s(53904),u=s(57434),p=s(69074),h=s(47924),g=s(37719),b=s(54416);function y(e){let{filters:t,onFilterChange:s,onClose:r}=e,[n,i]=(0,l.useState)(t),d=(e,t)=>{i({...n,[e]:t})};return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Filter Applications"}),(0,a.jsx)("button",{onClick:r,className:"p-2 hover:bg-gray-100 rounded-full",children:(0,a.jsx)(b.A,{className:"w-5 h-5"})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Status"}),(0,a.jsx)("select",{value:n.status,onChange:e=>d("status",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[{value:"ALL",label:"All Statuses"},{value:"APPLIED",label:"Applied"},{value:"UNDER_REVIEW",label:"Under Review"},{value:"SHORTLISTED",label:"Shortlisted"},{value:"REJECTED",label:"Rejected"},{value:"HIRED",label:"Hired"}].map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Company"}),(0,a.jsx)("input",{type:"text",placeholder:"Enter company name",value:n.company,onChange:e=>d("company",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Job Title"}),(0,a.jsx)("input",{type:"text",placeholder:"Enter job title",value:n.job_title,onChange:e=>d("job_title",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Student Name"}),(0,a.jsx)("input",{type:"text",placeholder:"Enter student name",value:n.student_name,onChange:e=>d("student_name",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Applied From"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:"date",value:n.date_from,onChange:e=>d("date_from",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,a.jsx)(p.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Applied To"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:"date",value:n.date_to,onChange:e=>d("date_to",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,a.jsx)(p.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none"})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("button",{onClick:()=>{let e={status:"ALL",company:"",job_title:"",date_from:"",date_to:"",student_name:""};i(e),s(e)},className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"Clear All"}),(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsx)("button",{onClick:r,className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"Cancel"}),(0,a.jsx)("button",{onClick:()=>{s(n),r()},className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Apply Filters"})]})]})]})}var j=s(62525),f=s(69037);function N(e){let{status:t}=e,s=(()=>{switch(t){case"APPLIED":return{color:"bg-blue-100 text-blue-800 border-blue-200",icon:(0,a.jsx)(n.A,{className:"w-3 h-3"}),label:"Applied"};case"UNDER_REVIEW":return{color:"bg-yellow-100 text-yellow-800 border-yellow-200",icon:(0,a.jsx)(i.A,{className:"w-3 h-3"}),label:"Under Review"};case"SHORTLISTED":return{color:"bg-green-100 text-green-800 border-green-200",icon:(0,a.jsx)(d.A,{className:"w-3 h-3"}),label:"Shortlisted"};case"REJECTED":return{color:"bg-red-100 text-red-800 border-red-200",icon:(0,a.jsx)(o.A,{className:"w-3 h-3"}),label:"Rejected"};case"HIRED":return{color:"bg-emerald-100 text-emerald-800 border-emerald-200",icon:(0,a.jsx)(f.A,{className:"w-3 h-3"}),label:"Hired"};default:return{color:"bg-gray-100 text-gray-800 border-gray-200",icon:(0,a.jsx)(n.A,{className:"w-3 h-3"}),label:t||"Unknown"}}})();return(0,a.jsxs)("span",{className:"inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ".concat(s.color),children:[s.icon,s.label]})}function v(e){let{applications:t,onViewApplication:s,onDeleteApplication:r}=e,[n,d]=(0,l.useState)(new Set),[o,c]=(0,l.useState)(!1),m=Array.isArray(t)?t:[],x=e=>{let t=new Set(n);t.has(e)?t.delete(e):t.add(e),d(t),c(t.size>0)},u=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return(0,a.jsxs)("div",{className:"overflow-x-auto",children:[o&&(0,a.jsxs)("div",{className:"flex items-center justify-between bg-blue-50 border-b border-blue-200 px-6 py-3",children:[(0,a.jsxs)("span",{className:"text-sm text-blue-700",children:[n.size," application",1!==n.size?"s":""," selected"]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{className:"px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700",children:"Bulk Status Update"}),(0,a.jsx)("button",{className:"px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700",children:"Bulk Delete"})]})]}),(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left",children:(0,a.jsx)("input",{type:"checkbox",checked:n.size===m.length&&m.length>0,onChange:()=>{n.size===m.length?(d(new Set),c(!1)):(d(new Set(m.map(e=>e.id))),c(!0))},className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"})}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Student"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Job Title"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Company"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Applied Date"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:m.map(e=>{var t;return(0,a.jsxs)("tr",{className:"hover:bg-gray-50 ".concat(n.has(e.id)?"bg-blue-50":""),children:[(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsx)("input",{type:"checkbox",checked:n.has(e.id),onChange:()=>x(e.id),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 h-10 w-10",children:(0,a.jsx)("div",{className:"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:(null==(t=e.student_name)?void 0:t.charAt(0))||"U"})})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.student_name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.student_email}),(0,a.jsxs)("div",{className:"text-xs text-gray-400",children:["ID: ",e.student_id]})]})]})}),(0,a.jsxs)("td",{className:"px-6 py-4",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.job_title}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.job_location})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.company_name})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)(N,{status:e.status})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:u(e.applied_at)}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("button",{onClick:()=>s(e),className:"inline-flex items-center px-2 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,a.jsx)(i.A,{className:"w-3 h-3 mr-1"}),"View"]}),(0,a.jsxs)("button",{onClick:()=>r(e.id),className:"inline-flex items-center px-2 py-1 border border-red-300 text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:[(0,a.jsx)(j.A,{className:"w-3 h-3 mr-1"}),"Delete"]})]})})]},e.id)})})]})]})}var _=s(73983);function w(e){let{onClose:t,filters:s}=e,{showSuccess:r,showError:n,handleApiError:i}=(0,_.hN)(),[d,o]=(0,l.useState)("csv"),[c,u]=(0,l.useState)(!1),[p,h]=(0,l.useState)(!0),[y,j]=(0,l.useState)([]),[f,N]=(0,l.useState)(["student_name","student_email","job_title","company_name","status","applied_at"]);(0,l.useEffect)(()=>{(async()=>{try{h(!0);let e=null==s?void 0:s.job_id,t=await g.A.get("/api/v1/jobs/applications/export/".concat(e?"?job_id=".concat(e):""));if(t.data&&t.data.all&&(j(t.data.all),6===f.length)){let e=t.data.standard.filter(e=>["student_name","student_email","job_title","company_name","status","applied_at"].includes(e.key)).map(e=>e.key);N(e)}}catch(e){console.error("Failed to fetch available columns:",e),j([{key:"student_name",label:"Student Name",category:"student"},{key:"student_email",label:"Email",category:"student"},{key:"student_id",label:"Student ID",category:"student"},{key:"job_title",label:"Job Title",category:"job"},{key:"company_name",label:"Company",category:"job"},{key:"status",label:"Status",category:"application"},{key:"applied_at",label:"Applied Date",category:"application"},{key:"current_cgpa",label:"CGPA",category:"academic"},{key:"branch",label:"Branch",category:"student"}])}finally{h(!1)}})()},[null==s?void 0:s.job_id]);let v=async()=>{if(0===f.length)return void n("Export Error","Please select at least one column to export.");u(!0);try{var e;let a={format:d,columns:f};s&&(s.job_id&&(a.job_id=parseInt(s.job_id)),s.status&&"ALL"!==s.status&&(a.status=[s.status]),s.date_from&&(a.date_from=s.date_from),s.date_to&&(a.date_to=s.date_to)),console.log("Export request payload:",a);let l=await g.A.post("/api/v1/jobs/applications/export/",a,{responseType:"blob"}),n=l.data,i=l.headers["content-disposition"],o=i?null==(e=i.split("filename=")[1])?void 0:e.replace(/"/g,""):"applications_export.".concat(d),c=window.URL.createObjectURL(n),m=document.createElement("a");m.href=c,m.download=o,document.body.appendChild(m),m.click(),document.body.removeChild(m),window.URL.revokeObjectURL(c),r("Export Successful","Your ".concat(d.toUpperCase()," file has been downloaded.")),t()}catch(e){console.error("Export error:",e),i(e,"export")}finally{u(!1)}};return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-screen overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Export Applications"}),(0,a.jsx)("button",{onClick:t,className:"p-2 hover:bg-gray-100 rounded-full",children:(0,a.jsx)(b.A,{className:"w-5 h-5"})})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Export Format"}),(0,a.jsx)("div",{className:"flex gap-3",children:["csv","excel","pdf"].map(e=>(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"radio",name:"format",value:e,checked:d===e,onChange:e=>o(e.target.value),className:"mr-2"}),e.toUpperCase()]},e))})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Select Columns to Export"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Choose which fields to include in your export file."}),p?(0,a.jsxs)("div",{className:"flex items-center justify-center py-8 border rounded-lg",children:[(0,a.jsx)(x.A,{className:"w-5 h-5 text-gray-400 animate-spin mr-2"}),(0,a.jsx)("span",{className:"text-gray-600",children:"Loading available columns..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"space-y-4 max-h-64 overflow-y-auto border rounded-lg p-3",children:["student","job","application","academic","contact","additional"].map(e=>{let t=y.filter(t=>t.category===e);return 0===t.length?null:(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-800 mb-2 border-b border-gray-200 pb-1",children:{student:"Student Information",job:"Job Details",application:"Application Data",academic:"Academic Information",contact:"Contact Information",additional:"Additional Fields"}[e]}),(0,a.jsx)("div",{className:"space-y-1",children:t.map(e=>(0,a.jsxs)("label",{className:"flex items-center space-x-3 p-1 hover:bg-gray-50 rounded cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:f.includes(e.key),onChange:t=>{t.target.checked?N(t=>[...t,e.key]):N(t=>t.filter(t=>t!==e.key))},className:"h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),(0,a.jsx)("span",{className:"text-sm text-gray-700 flex-1",children:e.label}),e.type&&(0,a.jsx)("span",{className:"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded",children:e.type})]},e.key))})]},e)})}),(0,a.jsxs)("div",{className:"mt-3 flex justify-between text-sm text-gray-600",children:[(0,a.jsxs)("span",{children:[f.length," columns selected"]}),(0,a.jsxs)("div",{className:"space-x-2",children:[(0,a.jsx)("button",{type:"button",onClick:()=>N([]),className:"text-gray-600 hover:text-gray-700",children:"Clear All"}),(0,a.jsx)("button",{type:"button",onClick:()=>N(y.map(e=>e.key)),className:"text-blue-600 hover:text-blue-700",children:"Select All"})]})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end gap-3",children:[(0,a.jsx)("button",{onClick:t,className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"Cancel"}),(0,a.jsxs)("button",{onClick:v,disabled:0===f.length||c,className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"w-4 h-4"}),c?"Exporting...":"Export"]})]})]})]})})}var A=s(4229),S=s(71007),k=s(17576),C=s(81191);function E(e){let{application:t,onClose:s,onUpdate:r}=e,[n,i]=(0,l.useState)(!1),[d,o]=(0,l.useState)({status:(null==t?void 0:t.status)||"APPLIED",admin_notes:(null==t?void 0:t.admin_notes)||""}),[c,x]=(0,l.useState)(null),[h,y]=(0,l.useState)(!0);if((0,l.useEffect)(()=>{(async()=>{if(null==t?void 0:t.id)try{var e;y(!0);let s=await (e=t.id,g.A.get("/api/v1/jobs/applications/".concat(e,"/")));x(s.data)}catch(e){console.error("Failed to fetch detailed application:",e),x(t)}finally{y(!1)}})()},[null==t?void 0:t.id]),!t)return null;let j=c||t,f=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),v=(e,t)=>(0,a.jsxs)("div",{className:"flex justify-between items-start text-sm py-2 border-b border-gray-100 last:border-b-0",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("span",{className:"font-medium text-gray-700",children:[e.label,e.required&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),e.type&&(0,a.jsxs)("span",{className:"text-xs text-gray-500 ml-2",children:["(",e.type,")"]})]}),(0,a.jsx)("div",{className:"flex-1 text-right",children:(()=>{if(!t&&0!==t)return"Not provided";switch(e.type){case"file":if("string"==typeof t&&t.startsWith("/media/"))return(0,a.jsxs)("a",{href:t,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-800 flex items-center gap-1",children:[(0,a.jsx)(m.A,{className:"w-4 h-4"}),"View File"]});return t||"No file uploaded";case"multiple_choice":return t||"Not selected";default:return t||"Not provided"}})()})]},e.id||e.label);return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-screen overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,a.jsxs)("h2",{className:"text-xl font-semibold text-gray-900",children:["Application Details - ",j.student_name]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[n?(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{onClick:()=>i(!1),className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"Cancel"}),(0,a.jsxs)("button",{onClick:()=>{console.log("Saving application:",d),alert("Save functionality will be implemented in the next phase"),i(!1),r&&r()},className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center gap-2",children:[(0,a.jsx)(A.A,{className:"w-4 h-4"}),"Save"]})]}):(0,a.jsx)("button",{onClick:()=>i(!0),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Edit"}),(0,a.jsx)("button",{onClick:s,className:"p-2 hover:bg-gray-100 rounded-full",children:(0,a.jsx)(b.A,{className:"w-5 h-5"})})]})]}),(0,a.jsx)("div",{className:"p-6",children:h?(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading application details..."})]})}):(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,a.jsx)(S.A,{className:"w-5 h-5 text-gray-600"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Student Information"})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Name"}),(0,a.jsx)("p",{className:"text-gray-900",children:j.student_name})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Email"}),(0,a.jsx)("p",{className:"text-gray-900",children:j.student_email})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Student ID"}),(0,a.jsx)("p",{className:"text-gray-900",children:j.student_id})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Branch"}),(0,a.jsx)("p",{className:"text-gray-900",children:j.branch})]})]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,a.jsx)(k.A,{className:"w-5 h-5 text-gray-600"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Job Information"})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Job Title"}),(0,a.jsx)("p",{className:"text-gray-900",children:j.job_title})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Company"}),(0,a.jsx)("p",{className:"text-gray-900",children:j.company_name})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Location"}),(0,a.jsx)("p",{className:"text-gray-900",children:j.job_location})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,a.jsx)(p.A,{className:"w-5 h-5 text-gray-600"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Application Details"})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Applied Date"}),(0,a.jsx)("p",{className:"text-gray-900",children:f(j.applied_at)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Status"}),n?(0,a.jsxs)("select",{value:d.status,onChange:e=>o({...d,status:e.target.value}),className:"mt-1 w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"APPLIED",children:"Applied"}),(0,a.jsx)("option",{value:"UNDER_REVIEW",children:"Under Review"}),(0,a.jsx)("option",{value:"SHORTLISTED",children:"Shortlisted"}),(0,a.jsx)("option",{value:"REJECTED",children:"Rejected"}),(0,a.jsx)("option",{value:"HIRED",children:"Hired"})]}):(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)(N,{status:j.status})})]}),j.cover_letter&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Cover Letter"}),(0,a.jsx)("p",{className:"text-gray-900 text-sm bg-white p-3 rounded border",children:j.cover_letter})]})]})]}),j.job_additional_fields&&j.job_additional_fields.length>0&&j.custom_responses&&(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,a.jsx)(C.A,{className:"w-5 h-5 text-gray-600"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Additional Information"})]}),(0,a.jsx)("div",{className:"space-y-1",children:j.job_additional_fields.map(e=>{let t="field_".concat(e.id),s=j.custom_responses[t]||j.custom_responses[e.id]||j.custom_responses[e.label];return v(e,s)})})]}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,a.jsx)(u.A,{className:"w-5 h-5 text-gray-600"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Admin Notes"})]}),n?(0,a.jsx)("textarea",{value:d.admin_notes,onChange:e=>o({...d,admin_notes:e.target.value}),placeholder:"Add internal notes about this application...",rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}):(0,a.jsx)("p",{className:"text-gray-900",children:j.admin_notes||"No admin notes yet."})]}),j.status_history&&j.status_history.length>0&&(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Status History"}),(0,a.jsx)("div",{className:"space-y-2",children:j.status_history.map((e,t)=>(0,a.jsxs)("div",{className:"text-sm bg-white p-2 rounded",children:[(0,a.jsx)("span",{className:"font-medium",children:e.from_status})," → ",(0,a.jsx)("span",{className:"font-medium",children:e.to_status}),(0,a.jsx)("span",{className:"text-gray-500 ml-2",children:f(e.changed_at)})]},t))})]})]})]})})]})})}function D(){var e,t,s,i;let b=(0,r.useSearchParams)().get("job_id"),[j,f]=(0,l.useState)([]),[N,_]=(0,l.useState)([]),[A,S]=(0,l.useState)({total:0,by_status:[],recent:0}),[k,C]=(0,l.useState)(!0),[D,I]=(0,l.useState)(null),[L,R]=(0,l.useState)(null),[T,P]=(0,l.useState)(!1),[U,F]=(0,l.useState)(!1),[J,H]=(0,l.useState)(!1),[z,O]=(0,l.useState)(""),[V,M]=(0,l.useState)({status:"ALL",company:"",job_title:"",date_from:"",date_to:"",student_name:""}),[W,B]=(0,l.useState)(1),[q,Z]=(0,l.useState)(1),[G]=(0,l.useState)(20);(0,l.useEffect)(()=>{X()},[W,V,b]),(0,l.useEffect)(()=>{z.trim()||_(j)},[j]),(0,l.useEffect)(()=>{let e=setTimeout(()=>{Y()},300);return()=>clearTimeout(e)},[z,j]);let X=async()=>{try{C(!0);try{let s=await function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.page&&t.append("page",e.page),e.page_size&&t.append("page_size",e.page_size),e.status&&"ALL"!==e.status&&t.append("status",e.status),e.job_id&&t.append("job_id",e.job_id),e.company&&t.append("company_name__icontains",e.company),e.job_title&&t.append("job__title__icontains",e.job_title),e.student_name&&t.append("student_name__icontains",e.student_name),e.date_from&&t.append("applied_at__gte",e.date_from),e.date_to&&t.append("applied_at__lte",e.date_to);let s=t.toString();return g.A.get("/api/v1/jobs/applications/".concat(s?"?".concat(s):""))}({page:W,page_size:G,...b&&{job_id:b},...V});if(console.log("Real API Response:",s),s&&s.data){var e,t;s.data.results&&s.data.results.results?(f(s.data.results.results||[]),S(s.data.results.stats||{total:0,by_status:[],recent:0})):(f(s.data.results||[]),S(s.data.stats||{total:0,by_status:[],recent:0}));let a=s.data.count||(null==(t=s.data.results)||null==(e=t.stats)?void 0:e.total)||0;Z(Math.ceil(a/G));return}}catch(e){console.log("API Error, falling back to mock data:",e)}let s=[{id:1,job_id:25,student_name:"John Doe",student_email:"<EMAIL>",student_id:"CS2021001",branch:"Computer Science",job_title:"Software Engineer",company_name:"TechCorp Inc",job_location:"San Francisco, CA",status:"APPLIED",applied_at:"2024-01-15T10:30:00Z",cover_letter:"I am very interested in this position...",admin_notes:"",status_history:[]},{id:2,job_id:25,student_name:"Jane Smith",student_email:"<EMAIL>",student_id:"CS2021002",branch:"Computer Science",job_title:"Software Engineer",company_name:"TechCorp Inc",job_location:"San Francisco, CA",status:"UNDER_REVIEW",applied_at:"2024-01-14T14:20:00Z",cover_letter:"My experience in machine learning...",admin_notes:"Strong candidate",status_history:[]},{id:3,job_id:26,student_name:"Mike Johnson",student_email:"<EMAIL>",student_id:"CS2021003",branch:"Computer Science",job_title:"Frontend Developer",company_name:"WebSolutions",job_location:"Austin, TX",status:"SHORTLISTED",applied_at:"2024-01-13T09:15:00Z",cover_letter:"I have extensive experience in React...",admin_notes:"Excellent portfolio",status_history:[]}];b&&(s=s.filter(e=>e.job_id===parseInt(b)));let a={total:s.length,by_status:[{status:"APPLIED",count:s.filter(e=>"APPLIED"===e.status).length},{status:"UNDER_REVIEW",count:s.filter(e=>"UNDER_REVIEW"===e.status).length},{status:"SHORTLISTED",count:s.filter(e=>"SHORTLISTED"===e.status).length}],recent:s.length};console.log("Using Mock Applications:",s),console.log("Mock Stats:",a),console.log("Job ID from URL:",b),f(s),S(a),Z(1)}catch(e){console.error("Failed to load applications:",e),I("Failed to load applications")}finally{C(!1)}},Y=()=>{let e=Array.isArray(j)?j:[];if(!z.trim())return void _(e);_(e.filter(e=>{var t,s,a,l,r;return(null==(t=e.student_name)?void 0:t.toLowerCase().includes(z.toLowerCase()))||(null==(s=e.student_email)?void 0:s.toLowerCase().includes(z.toLowerCase()))||(null==(a=e.job_title)?void 0:a.toLowerCase().includes(z.toLowerCase()))||(null==(l=e.company_name)?void 0:l.toLowerCase().includes(z.toLowerCase()))||(null==(r=e.student_id)?void 0:r.toLowerCase().includes(z.toLowerCase()))}))},K=async e=>{if(confirm("Are you sure you want to delete this application? This action cannot be undone."))try{f(t=>t.filter(t=>t.id!==e)),alert("Application deleted successfully (mock action)")}catch(e){console.error("Failed to delete application:",e),alert("Failed to delete application")}},Q=z?Array.isArray(N)?N:[]:Array.isArray(j)?j:[];return console.log("Current state:",{searchTerm:z,applications:(null==j?void 0:j.length)||0,filteredApplications:(null==N?void 0:N.length)||0,displayApplications:(null==Q?void 0:Q.length)||0,stats:A}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Applications Management"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:b?"Applications for Job ID: ".concat(b):"Track and manage all job applications from students"}),b&&(0,a.jsx)("div",{className:"mt-2",children:(0,a.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:["Filtered by Job ID: ",b]})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)("button",{onClick:()=>F(!U),className:"flex items-center gap-2 px-4 py-2 rounded-lg border transition-colors ".concat(U?"bg-blue-50 border-blue-200 text-blue-700":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50"),children:[(0,a.jsx)(c.A,{className:"w-4 h-4"}),"Filters"]}),(0,a.jsxs)("button",{onClick:()=>P(!0),className:"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,a.jsx)(m.A,{className:"w-4 h-4"}),"Export"]}),(0,a.jsxs)("button",{onClick:X,disabled:k,className:"flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors disabled:opacity-50",children:[(0,a.jsx)(x.A,{className:"w-4 h-4 ".concat(k?"animate-spin":"")}),"Refresh"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Applications"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:A.total})]}),(0,a.jsx)("div",{className:"p-3 bg-blue-100 rounded-full",children:(0,a.jsx)(u.A,{className:"w-6 h-6 text-blue-600"})})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Pending Review"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:(null==(t=A.by_status)||null==(e=t.find(e=>"APPLIED"===e.status))?void 0:e.count)||0})]}),(0,a.jsx)("div",{className:"p-3 bg-yellow-100 rounded-full",children:(0,a.jsx)(n.A,{className:"w-6 h-6 text-yellow-600"})})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Shortlisted"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:(null==(i=A.by_status)||null==(s=i.find(e=>"SHORTLISTED"===e.status))?void 0:s.count)||0})]}),(0,a.jsx)("div",{className:"p-3 bg-green-100 rounded-full",children:(0,a.jsx)(d.A,{className:"w-6 h-6 text-green-600"})})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Recent (7 days)"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:A.recent})]}),(0,a.jsx)("div",{className:"p-3 bg-purple-100 rounded-full",children:(0,a.jsx)(p.A,{className:"w-6 h-6 text-purple-600"})})]})})]}),U&&(0,a.jsx)(y,{filters:V,onFilterChange:e=>{M(e),B(1)},onClose:()=>F(!1)}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-4",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(h.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,a.jsx)("input",{type:"text",placeholder:"Search by student name, email, job title, company, or student ID...",value:z,onChange:e=>O(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[k?(0,a.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,a.jsx)(x.A,{className:"w-8 h-8 text-gray-400 animate-spin"}),(0,a.jsx)("span",{className:"ml-3 text-gray-600",children:"Loading applications..."})]}):D?(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(o.A,{className:"w-12 h-12 text-red-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-900 font-medium",children:"Error loading applications"}),(0,a.jsx)("p",{className:"text-gray-600",children:D}),(0,a.jsx)("button",{onClick:X,className:"mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Try Again"})]})}):0===Q.length?(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(u.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-900 font-medium",children:"No applications found"}),(0,a.jsx)("p",{className:"text-gray-600",children:z?"Try adjusting your search criteria":"No applications have been submitted yet"})]})}):(0,a.jsx)(v,{applications:Q,onViewApplication:e=>{R(e),H(!0)},onDeleteApplication:K}),q>1&&(0,a.jsxs)("div",{className:"flex items-center justify-between px-6 py-4 border-t border-gray-200",children:[(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["Page ",W," of ",q]})}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("button",{onClick:()=>B(Math.max(1,W-1)),disabled:1===W,className:"px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,a.jsx)("button",{onClick:()=>B(Math.min(q,W+1)),disabled:W===q,className:"px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]})]})]}),T&&(0,a.jsx)(w,{onClose:()=>P(!1),filters:V}),J&&L&&(0,a.jsx)(E,{application:L,onClose:()=>{H(!1),R(null)},onUpdate:X})]})}function I(){return(0,a.jsx)(l.Suspense,{fallback:(0,a.jsx)("div",{children:"Loading..."}),children:(0,a.jsx)(D,{})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[3464,6737,3983,8441,1684,7358],()=>t(9022)),_N_E=e.O()}]);