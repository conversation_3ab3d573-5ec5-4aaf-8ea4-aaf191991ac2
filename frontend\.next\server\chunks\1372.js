exports.id=1372,exports.ids=[1372,4335],exports.modules={8819:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},9005:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},10722:(e,t,a)=>{Promise.resolve().then(a.bind(a,11527))},11527:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>f});var r=a(60687),s=a(43210),i=a(24664),n=a(98848),o=a(9535),c=a(37325),d=a(81080),l=a(95994),p=a(80556),h=a(90910),u=a(81172),m=a(20798),y=a(58869),g=a(53411);function f({children:e}){let[t,a]=(0,s.useState)(""),f=[{items:[{title:"Dashboard",href:"/admin/dashboard",icon:(0,r.jsx)(n.A,{})},{title:"Jobs",href:"/admin/jobs",icon:(0,r.jsx)(o.A,{})},{title:"Applications",href:"/admin/applications",icon:(0,r.jsx)(c.A,{})},{title:"Posts",href:"/admin/posts",icon:(0,r.jsx)(m.A,{className:"w-5 h-5"})},{title:"Student Management",href:"/admin/student-management",icon:(0,r.jsx)(y.A,{className:"w-5 h-5"})},{title:"Student Analytics",href:"/admin/analytics",icon:(0,r.jsx)(g.A,{className:"w-5 h-5"})},{title:"Company Management",href:"/admin/companymanagement",icon:(0,r.jsx)(d.A,{})},{title:"Forms",href:"/admin/form",icon:(0,r.jsx)(l.A,{})}]}],v=[{title:"My Profile",href:"/admin/profile",icon:(0,r.jsx)(p.A,{})},{title:"Settings",href:"../settings",icon:(0,r.jsx)(h.A,{})},{title:"Contact Support",href:"/admin/helpandsupport",icon:(0,r.jsx)(u.A,{})}];return(0,r.jsx)("div",{className:"h-screen bg-gray-50 overflow-hidden",children:(0,r.jsxs)("div",{className:"flex h-full",children:[(0,r.jsx)(i.A,{sections:f,bottomItems:v,defaultExpanded:!1,navbarHeight:"0",className:"z-20"}),(0,r.jsx)("div",{className:"flex-1 p-6 ml-20 overflow-y-auto",children:(0,r.jsx)("div",{className:"bg-white rounded-xl shadow-md p-6 min-h-full text-black",children:e})})]})})}},14335:(e,t,a)=>{"use strict";a.d(t,{C1:()=>n,Gu:()=>y,JT:()=>c,RC:()=>d,S0:()=>f,Y_:()=>p,bl:()=>h,dl:()=>g,eK:()=>o,fetchCompanies:()=>i,getCompanyStats:()=>l,jQ:()=>u,mm:()=>s,oY:()=>m});var r=a(58138);function s(e={}){let t=new URLSearchParams;e.page&&t.append("page",e.page),e.per_page&&t.append("per_page",e.per_page),e.tier&&"ALL"!==e.tier&&t.append("tier",e.tier),e.industry&&"ALL"!==e.industry&&t.append("industry",e.industry),e.campus_recruiting&&t.append("campus_recruiting",e.campus_recruiting),e.search&&t.append("search",e.search),e.sort&&t.append("sort",e.sort),t.append("_t",new Date().getTime());let a=t.toString(),i=[`/api/v1/companies/${a?`?${a}`:""}`,`/api/v1/college/default-college/companies/${a?`?${a}`:""}`];return r.A.get(i[0]).catch(e=>(console.log(`Primary endpoint failed: ${e.message}, trying fallback...`),r.A.get(i[1])))}async function i(e={}){try{console.log("Fetching companies from API...");let t=await s(e),a=[];if(t.data&&Array.isArray(t.data)?a=t.data:t.data&&t.data.results&&Array.isArray(t.data.results)?a=t.data.results:t.data&&t.data.data&&Array.isArray(t.data.data)&&(a=t.data.data),console.log(`Retrieved ${a.length} companies from API`),a.length>0)return await Promise.all(a.map(async e=>{try{let t=await n(e.id);return p(t.data)}catch(t){return console.log(`Could not fetch details for company ${e.id}:`,t),p(e)}}));throw Error("No companies returned from API")}catch(t){console.error("Error fetching companies from API:",t),console.log("Falling back to static company data");let{companies:e}=await a.e(1286).then(a.bind(a,61286));return e}}function n(e){let t=[`/api/v1/company/${e}/`,`/api/v1/companies/${e}/`,`/api/v1/college/default-college/companies/${e}/`];return r.A.get(t[0]).catch(e=>(console.log(`First company endpoint failed: ${e.message}, trying second...`),r.A.get(t[1]).catch(e=>(console.log(`Second company endpoint failed: ${e.message}, trying third...`),r.A.get(t[2])))))}function o(e){let t=new FormData;return Object.keys(e).forEach(a=>{"logo"===a&&e[a]instanceof File?t.append(a,e[a]):null!==e[a]&&void 0!==e[a]&&t.append(a,e[a])}),r.A.post("/api/v1/companies/",t,{headers:{"Content-Type":"multipart/form-data"}})}function c(e,t){let a=new FormData;return Object.keys(t).forEach(e=>{"logo"===e&&t[e]instanceof File?a.append(e,t[e]):null!==t[e]&&void 0!==t[e]&&a.append(e,t[e])}),r.A.put(`/api/v1/companies/${e}/`,a,{headers:{"Content-Type":"multipart/form-data"}})}function d(e){return r.A.delete(`/api/v1/companies/${e}/`)}function l(){return r.A.get("/api/v1/companies/stats/")}function p(e){return{id:e.id,name:e.name,logo:e.logo||`https://via.placeholder.com/48x48/4285F4/FFFFFF?text=${e.name.charAt(0)}`,description:e.description||"",industry:e.industry||"",size:e.size||"",founded:e.founded||"",location:e.location||"Location not specified",website:e.website||"",tier:e.tier||"Tier 3",campus_recruiting:e.campus_recruiting||!1,totalActiveJobs:e.total_active_jobs||0,totalApplicants:e.total_applicants||0,totalHired:e.total_hired||0,awaitedApproval:e.pending_approval||0}}function h(e){return r.A.get(`/api/v1/companies/${e}/followers/count/`)}function u(e,t){return r.A.post(`/api/v1/companies/${e}/followers/`,{user_id:t})}function m(e,t){return r.A.delete(`/api/v1/companies/${e}/followers/`,{data:{user_id:t}})}function y(e,t){return r.A.get(`/api/v1/companies/${e}/followers/status/?user_id=${t}`)}function g(e){return r.A.get(`/api/v1/users/${e}/following/`)}function f(){return r.A.get("/api/v1/companies/simple/").then(e=>{if(e.data&&e.data.success)return e.data;throw Error("Failed to fetch companies")}).catch(e=>(console.error("Error fetching companies:",e),{success:!1,data:[],count:0,error:e.message}))}},20798:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("megaphone",[["path",{d:"m3 11 18-5v12L3 14v-3z",key:"n962bs"}],["path",{d:"M11.6 16.8a3 3 0 1 1-5.8-1.6",key:"1yl0tm"}]])},23697:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\layout.jsx","default")},28559:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},35071:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},37325:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var r=(0,a(6445).A)("outline","clipboard-list","IconClipboardList",[["path",{d:"M9 5h-2a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-12a2 2 0 0 0 -2 -2h-2",key:"svg-0"}],["path",{d:"M9 3m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z",key:"svg-1"}],["path",{d:"M9 12l.01 0",key:"svg-2"}],["path",{d:"M13 12l2 0",key:"svg-3"}],["path",{d:"M9 16l.01 0",key:"svg-4"}],["path",{d:"M13 16l2 0",key:"svg-5"}]])},40228:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41312:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},43125:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("loader",[["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m16.2 7.8 2.9-2.9",key:"r700ao"}],["path",{d:"M18 12h4",key:"wj9ykh"}],["path",{d:"m16.2 16.2 2.9 2.9",key:"1bxg5t"}],["path",{d:"M12 18v4",key:"jadmvz"}],["path",{d:"m4.9 19.1 2.9-2.9",key:"bwix9q"}],["path",{d:"M2 12h4",key:"j09sii"}],["path",{d:"m4.9 4.9 2.9 2.9",key:"giyufr"}]])},47342:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},48173:(e,t,a)=>{Promise.resolve().then(a.bind(a,23697))},53411:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},58138:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var r=a(51060);a(51421);let s=r.A.create({baseURL:"http://127.0.0.1:8000",headers:{"Content-Type":"application/json"}});s.interceptors.request.use(e=>{let t=localStorage.getItem("access_token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e)),s.interceptors.response.use(e=>e,async e=>{let t=e.config;if(e.response?.status===401&&!t._retry){t._retry=!0;try{let e=localStorage.getItem("refresh_token");if(e){let a=await r.A.post("http://127.0.0.1:8000/api/auth/token/refresh/",{refresh:e});return localStorage.setItem("access_token",a.data.access),t.headers.Authorization=`Bearer ${a.data.access}`,s(t)}}catch(e){console.error("Error refreshing token:",e)}}return Promise.reject(e)});let i=s},64398:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},70440:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});var r=a(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79410:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},81172:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var r=(0,a(6445).A)("outline","help","IconHelp",[["path",{d:"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0",key:"svg-0"}],["path",{d:"M12 17l0 .01",key:"svg-1"}],["path",{d:"M12 13.5a1.5 1.5 0 0 1 1 -1.5a2.6 2.6 0 1 0 -3 -4",key:"svg-2"}]])},95994:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var r=(0,a(6445).A)("outline","forms","IconForms",[["path",{d:"M12 3a3 3 0 0 0 -3 3v12a3 3 0 0 0 3 3",key:"svg-0"}],["path",{d:"M6 3a3 3 0 0 1 3 3v12a3 3 0 0 1 -3 3",key:"svg-1"}],["path",{d:"M13 7h7a1 1 0 0 1 1 1v8a1 1 0 0 1 -1 1h-7",key:"svg-2"}],["path",{d:"M5 7h-1a1 1 0 0 0 -1 1v8a1 1 0 0 0 1 1h1",key:"svg-3"}],["path",{d:"M17 12h.01",key:"svg-4"}],["path",{d:"M13 12h.01",key:"svg-5"}]])},97992:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};