module.exports = {

"[project]/src/api/companies.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_data_jobsData_7b6b6532.js",
  "server/chunks/ssr/src_api_companies_c3c60fc1.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/api/companies.js [app-ssr] (ecmascript)");
    });
});
}}),

};