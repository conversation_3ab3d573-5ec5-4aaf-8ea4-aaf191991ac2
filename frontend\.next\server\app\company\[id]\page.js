(()=>{var e={};e.id=6208,e.ids=[1286,4335,6208],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11437:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},12412:e=>{"use strict";e.exports=require("assert")},14335:(e,t,s)=>{"use strict";s.d(t,{C1:()=>n,Gu:()=>u,JT:()=>o,RC:()=>c,S0:()=>y,Y_:()=>m,bl:()=>p,dl:()=>g,eK:()=>l,fetchCompanies:()=>i,getCompanyStats:()=>d,jQ:()=>x,mm:()=>r,oY:()=>h});var a=s(58138);function r(e={}){let t=new URLSearchParams;e.page&&t.append("page",e.page),e.per_page&&t.append("per_page",e.per_page),e.tier&&"ALL"!==e.tier&&t.append("tier",e.tier),e.industry&&"ALL"!==e.industry&&t.append("industry",e.industry),e.campus_recruiting&&t.append("campus_recruiting",e.campus_recruiting),e.search&&t.append("search",e.search),e.sort&&t.append("sort",e.sort),t.append("_t",new Date().getTime());let s=t.toString(),i=[`/api/v1/companies/${s?`?${s}`:""}`,`/api/v1/college/default-college/companies/${s?`?${s}`:""}`];return a.A.get(i[0]).catch(e=>(console.log(`Primary endpoint failed: ${e.message}, trying fallback...`),a.A.get(i[1])))}async function i(e={}){try{console.log("Fetching companies from API...");let t=await r(e),s=[];if(t.data&&Array.isArray(t.data)?s=t.data:t.data&&t.data.results&&Array.isArray(t.data.results)?s=t.data.results:t.data&&t.data.data&&Array.isArray(t.data.data)&&(s=t.data.data),console.log(`Retrieved ${s.length} companies from API`),s.length>0)return await Promise.all(s.map(async e=>{try{let t=await n(e.id);return m(t.data)}catch(t){return console.log(`Could not fetch details for company ${e.id}:`,t),m(e)}}));throw Error("No companies returned from API")}catch(t){console.error("Error fetching companies from API:",t),console.log("Falling back to static company data");let{companies:e}=await s.e(1286).then(s.bind(s,61286));return e}}function n(e){let t=[`/api/v1/company/${e}/`,`/api/v1/companies/${e}/`,`/api/v1/college/default-college/companies/${e}/`];return a.A.get(t[0]).catch(e=>(console.log(`First company endpoint failed: ${e.message}, trying second...`),a.A.get(t[1]).catch(e=>(console.log(`Second company endpoint failed: ${e.message}, trying third...`),a.A.get(t[2])))))}function l(e){let t=new FormData;return Object.keys(e).forEach(s=>{"logo"===s&&e[s]instanceof File?t.append(s,e[s]):null!==e[s]&&void 0!==e[s]&&t.append(s,e[s])}),a.A.post("/api/v1/companies/",t,{headers:{"Content-Type":"multipart/form-data"}})}function o(e,t){let s=new FormData;return Object.keys(t).forEach(e=>{"logo"===e&&t[e]instanceof File?s.append(e,t[e]):null!==t[e]&&void 0!==t[e]&&s.append(e,t[e])}),a.A.put(`/api/v1/companies/${e}/`,s,{headers:{"Content-Type":"multipart/form-data"}})}function c(e){return a.A.delete(`/api/v1/companies/${e}/`)}function d(){return a.A.get("/api/v1/companies/stats/")}function m(e){return{id:e.id,name:e.name,logo:e.logo||`https://via.placeholder.com/48x48/4285F4/FFFFFF?text=${e.name.charAt(0)}`,description:e.description||"",industry:e.industry||"",size:e.size||"",founded:e.founded||"",location:e.location||"Location not specified",website:e.website||"",tier:e.tier||"Tier 3",campus_recruiting:e.campus_recruiting||!1,totalActiveJobs:e.total_active_jobs||0,totalApplicants:e.total_applicants||0,totalHired:e.total_hired||0,awaitedApproval:e.pending_approval||0}}function p(e){return a.A.get(`/api/v1/companies/${e}/followers/count/`)}function x(e,t){return a.A.post(`/api/v1/companies/${e}/followers/`,{user_id:t})}function h(e,t){return a.A.delete(`/api/v1/companies/${e}/followers/`,{data:{user_id:t}})}function u(e,t){return a.A.get(`/api/v1/companies/${e}/followers/status/?user_id=${t}`)}function g(e){return a.A.get(`/api/v1/users/${e}/following/`)}function y(){return a.A.get("/api/v1/companies/simple/").then(e=>{if(e.data&&e.data.success)return e.data;throw Error("Failed to fetch companies")}).catch(e=>(console.error("Error fetching companies:",e),{success:!1,data:[],count:0,error:e.message}))}},17313:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23928:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},25541:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33823:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>S});var a=s(60687),r=s(43210),i=s(16189),n=s(43125),l=s(93613),o=s(28559),c=s(17313),d=s(62688);let m=(0,d.A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]);var p=s(97992),x=s(41312),h=s(40228),u=s(11437),g=s(25334),y=s(64398),b=s(57800),f=s(25541),j=s(48730),v=s(5336),N=s(23928),w=s(67760);let A=(0,d.A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]);var k=s(14335);s(61286);var _=s(63787),C=s(65222);function S(){let e=(0,i.useRouter)(),t=parseInt((0,i.useParams)().id),[s,d]=(0,r.useState)(null),[S,P]=(0,r.useState)([]),[$,M]=(0,r.useState)("overview"),[q,E]=(0,r.useState)(!1),[F,T]=(0,r.useState)(new Set),[I,D]=(0,r.useState)(0),[L,z]=(0,r.useState)(!0),[O,H]=(0,r.useState)(null),R=S.filter(e=>e.is_active),J=async()=>{try{let e=(0,_.F6)();if(!e)return void alert("Please log in to follow companies");q?(await (0,k.oY)(t,e),D(e=>Math.max(0,e-1))):(await (0,k.jQ)(t,e),D(e=>e+1)),E(!q)}catch(e){console.error("Error toggling follow status:",e),alert("Failed to update follow status. Please try again.")}};return L?(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(n.A,{className:"h-12 w-12 animate-spin text-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading company details..."})]})}):O?(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsxs)("div",{className:"text-center max-w-md",children:[(0,a.jsx)("div",{className:"text-red-500 mb-4",children:(0,a.jsx)(l.A,{className:"w-16 h-16 mx-auto"})}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Error Loading Company"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:O}),(0,a.jsx)("button",{onClick:()=>e.push("/companies"),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Return to Companies"})]})}):s?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsxs)("button",{onClick:()=>e.back(),className:"flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors",children:[(0,a.jsx)(o.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Back to Companies"})]})}),(0,a.jsxs)("div",{className:"relative mb-6",children:[(0,a.jsxs)("div",{className:"h-48 bg-gradient-to-r from-blue-600 to-blue-800 relative overflow-hidden rounded-xl",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black opacity-20"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"}),(0,a.jsx)("div",{className:"absolute bottom-0 left-0 right-0 p-6",children:(0,a.jsxs)("div",{className:"flex items-end gap-6",children:[(0,a.jsxs)("div",{className:"w-24 h-24 bg-white rounded-xl shadow-lg flex items-center justify-center overflow-hidden border-4 border-white",children:[(0,a.jsx)("img",{src:s.logo,alt:s.name,className:"w-16 h-16 rounded-lg object-cover",onError:e=>{e.target.style.display="none",e.target.nextSibling.style.display="flex"}}),(0,a.jsx)(c.A,{className:"w-12 h-12 text-gray-600 hidden"})]}),(0,a.jsxs)("div",{className:"flex-1 text-white pb-2",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold mb-2",children:s.name}),(0,a.jsxs)("div",{className:"flex items-center gap-4 text-white/90",children:[(0,a.jsx)("span",{children:s.industry}),(0,a.jsx)("span",{children:"•"}),(0,a.jsxs)("span",{children:[I.toLocaleString()," followers"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3 pb-2",children:[(0,a.jsx)("button",{onClick:J,className:`px-6 py-2 rounded-lg font-medium transition-colors ${q?"bg-white/20 text-white border border-white/30 hover:bg-white/30":"bg-blue-600 text-white hover:bg-blue-700"}`,children:q?"Following":"Follow"}),(0,a.jsx)("button",{className:"p-2 rounded-lg bg-white/20 text-white border border-white/30 hover:bg-white/30 transition-colors",children:(0,a.jsx)(m,{className:"w-5 h-5"})})]})]})})]}),(0,a.jsx)("div",{className:"bg-white shadow-sm border border-gray-200 rounded-lg mt-4",children:(0,a.jsx)("div",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-6 text-sm text-gray-600",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(p.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:s.location})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(x.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:s.size})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"w-4 h-4"}),(0,a.jsxs)("span",{children:["Founded ",s.founded]})]}),(0,a.jsxs)("a",{href:s.website,target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-2 text-blue-600 hover:text-blue-800",children:[(0,a.jsx)(u.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Website"}),(0,a.jsx)(g.A,{className:"w-3 h-3"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("div",{className:`inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium border ${(e=>{switch(e){case"Tier 1":return"bg-emerald-100 text-emerald-800 border-emerald-200";case"Tier 2":return"bg-blue-100 text-blue-800 border-blue-200";case"Tier 3":return"bg-purple-100 text-purple-800 border-purple-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}})(s.tier)}`,children:[(0,a.jsx)(y.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:s.tier})]}),s.campus_recruiting&&(0,a.jsx)("span",{className:"px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium",children:"Campus Recruiting"})]})]})})})]}),(0,a.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg mb-6",children:(0,a.jsx)("div",{className:"px-6",children:(0,a.jsx)("nav",{className:"flex space-x-8",children:[{id:"overview",label:"Overview"},{id:"jobs",label:"Jobs"},{id:"posts",label:"Posts"}].map(e=>(0,a.jsx)("button",{onClick:()=>M(e.id),className:`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${$===e.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:e.label},e.id))})})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-6",children:["overview"===$&&(0,a.jsx)("div",{className:"max-w-4xl",children:(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:["About ",s.name]}),(0,a.jsx)("p",{className:"text-gray-700 leading-relaxed text-lg",children:s.description})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Company Metrics"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6",children:[(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-6 text-center",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-3",children:[(0,a.jsx)(b.A,{className:"w-6 h-6 text-blue-600"}),(0,a.jsx)("h4",{className:"font-semibold text-blue-900",children:"Active Jobs"})]}),(0,a.jsx)("p",{className:"text-3xl font-bold text-blue-900",children:s.totalActiveJobs})]}),(0,a.jsxs)("div",{className:"bg-green-50 rounded-lg p-6 text-center",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-3",children:[(0,a.jsx)(x.A,{className:"w-6 h-6 text-green-600"}),(0,a.jsx)("h4",{className:"font-semibold text-green-900",children:"Total Applicants"})]}),(0,a.jsx)("p",{className:"text-3xl font-bold text-green-900",children:s.totalApplicants})]}),(0,a.jsxs)("div",{className:"bg-purple-50 rounded-lg p-6 text-center",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-3",children:[(0,a.jsx)(f.A,{className:"w-6 h-6 text-purple-600"}),(0,a.jsx)("h4",{className:"font-semibold text-purple-900",children:"Hired"})]}),(0,a.jsx)("p",{className:"text-3xl font-bold text-purple-900",children:s.totalHired})]}),(0,a.jsxs)("div",{className:"bg-amber-50 rounded-lg p-6 text-center",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-3",children:[(0,a.jsx)(j.A,{className:"w-6 h-6 text-amber-600"}),(0,a.jsx)("h4",{className:"font-semibold text-amber-900",children:"Pending"})]}),(0,a.jsx)("p",{className:"text-3xl font-bold text-amber-900",children:s.awaitedApproval})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Work Life"}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,a.jsx)("p",{className:"text-gray-700 leading-relaxed mb-4",children:"Tier 1"===s.tier?`${s.name} offers a dynamic work environment with cutting-edge technology, comprehensive benefits, and opportunities for professional growth. Our culture emphasizes innovation, collaboration, and work-life balance.`:"Tier 2"===s.tier?`At ${s.name}, we foster a collaborative environment where innovation thrives. We offer competitive benefits, flexible work arrangements, and continuous learning opportunities for our employees.`:`${s.name} provides a supportive workplace focused on growth and development. We believe in empowering our employees with the tools and opportunities they need to succeed.`}),(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:[{label:"Remote Work",available:!0},{label:"Health Insurance",available:!0},{label:"Stock Options",available:"Tier 1"===s.tier||"Tier 2"===s.tier},{label:"Learning Budget",available:!0},{label:"Flexible Hours",available:!0},{label:"Gym Membership",available:"Tier 1"===s.tier}].map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(v.A,{className:`w-4 h-4 ${e.available?"text-green-600":"text-gray-400"}`}),(0,a.jsx)("span",{className:e.available?"text-gray-900":"text-gray-500",children:e.label})]},t))})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Company Details"}),(0,a.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Industry"}),(0,a.jsx)("p",{className:"text-gray-700",children:s.industry})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Company Size"}),(0,a.jsx)("p",{className:"text-gray-700",children:s.size})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Founded"}),(0,a.jsx)("p",{className:"text-gray-700",children:s.founded})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Headquarters"}),(0,a.jsx)("p",{className:"text-gray-700",children:s.location})]})]})})]})]})}),"jobs"===$&&(0,a.jsxs)("div",{className:"max-w-4xl",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Job Openings"}),(0,a.jsxs)("p",{className:"text-gray-600",children:[R.length," active position",1!==R.length?"s":""," available"]})]}),R.length>0?(0,a.jsx)("div",{className:"space-y-6",children:R.map(e=>(0,a.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:e.title}),(0,a.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${"INTERNSHIP"===e.type?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800"}`,children:"INTERNSHIP"===e.type?"Internship":"Full-time"}),e.is_featured&&(0,a.jsx)("span",{className:"px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium",children:"Featured"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-6 text-sm text-gray-600 mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(p.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:e.location})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(N.A,{className:"w-4 h-4"}),(0,a.jsxs)("span",{children:["$",e.salary_min,"k - $",e.salary_max,"k / ",e.per]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(j.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:e.duration})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(h.A,{className:"w-4 h-4"}),(0,a.jsxs)("span",{children:["Apply by ",new Date(e.deadline).toLocaleDateString()]})]})]}),(0,a.jsx)(C.G_,{description:e.description,className:"mb-4"}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2 mb-4",children:[e.skills.slice(0,5).map((e,t)=>(0,a.jsx)("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium",children:e},t)),e.skills.length>5&&(0,a.jsxs)("span",{className:"px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-xs",children:["+",e.skills.length-5," more"]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-2 ml-6",children:[(0,a.jsx)("button",{className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium",children:"Apply Now"}),(0,a.jsx)("button",{className:"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",children:"Save Job"})]})]})},e.id))}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(b.A,{className:"w-8 h-8 text-gray-400"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No Active Job Openings"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"This company doesn't have any active job postings right now."}),(0,a.jsx)("button",{onClick:J,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Follow Company for Updates"})]})]}),"posts"===$&&(0,a.jsxs)("div",{className:"max-w-4xl",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Company Posts"}),(0,a.jsxs)("p",{className:"text-gray-600",children:["Latest updates and news from ",s.name]})]}),(0,a.jsx)("div",{className:"space-y-6",children:[{id:1,title:"We're excited to announce our new internship program!",content:"Applications are now open for our Summer 2024 internship program. Join our team and work on cutting-edge projects while learning from industry experts.",date:"2024-01-15",likes:42,comments:8},{id:2,title:"Company Culture Spotlight",content:"Take a behind-the-scenes look at our innovative workspace and learn about our commitment to employee well-being and professional development.",date:"2024-01-10",likes:28,comments:5}].map(e=>(0,a.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-6",children:(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[(0,a.jsxs)("div",{className:"w-12 h-12 bg-white rounded-lg flex items-center justify-center shadow-sm",children:[(0,a.jsx)("img",{src:s.logo,alt:s.name,className:"w-8 h-8 rounded object-cover",onError:e=>{e.target.style.display="none",e.target.nextSibling.style.display="flex"}}),(0,a.jsx)(c.A,{className:"w-6 h-6 text-gray-600 hidden"})]}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900",children:s.name}),(0,a.jsx)("span",{className:"text-gray-500",children:"•"}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:new Date(e.date).toLocaleDateString()})]}),(0,a.jsx)("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:e.title}),(0,a.jsx)("p",{className:"text-gray-700 mb-4",children:e.content}),(0,a.jsxs)("div",{className:"flex items-center gap-6 text-sm text-gray-500",children:[(0,a.jsxs)("button",{className:"flex items-center gap-2 hover:text-blue-600 transition-colors",children:[(0,a.jsx)(w.A,{className:"w-4 h-4"}),(0,a.jsxs)("span",{children:[e.likes," likes"]})]}),(0,a.jsxs)("button",{className:"flex items-center gap-2 hover:text-blue-600 transition-colors",children:[(0,a.jsx)(A,{className:"w-4 h-4"}),(0,a.jsxs)("span",{children:[e.comments," comments"]})]}),(0,a.jsxs)("button",{className:"flex items-center gap-2 hover:text-blue-600 transition-colors",children:[(0,a.jsx)(m,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Share"})]})]})]})]})},e.id))}),(0,a.jsxs)("div",{className:"text-center py-12 mt-8",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(A,{className:"w-8 h-8 text-gray-400"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"That's all for now"}),(0,a.jsxs)("p",{className:"text-gray-600",children:["Follow ",s.name," to get notified about new posts and updates."]})]})]})]})]}):(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-gray-600",children:"No company data available"}),(0,a.jsx)("button",{onClick:()=>e.push("/companies"),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors mt-4",children:"Return to Companies"})]})})}},33873:e=>{"use strict";e.exports=require("path")},40228:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41312:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},43125:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("loader",[["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m16.2 7.8 2.9-2.9",key:"r700ao"}],["path",{d:"M18 12h4",key:"wj9ykh"}],["path",{d:"m16.2 16.2 2.9 2.9",key:"1bxg5t"}],["path",{d:"M12 18v4",key:"jadmvz"}],["path",{d:"m4.9 19.1 2.9-2.9",key:"bwix9q"}],["path",{d:"M2 12h4",key:"j09sii"}],["path",{d:"m4.9 4.9 2.9 2.9",key:"giyufr"}]])},45596:(e,t,s)=>{Promise.resolve().then(s.bind(s,33823))},48730:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57800:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},58138:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(51060);s(51421);let r=a.A.create({baseURL:"http://127.0.0.1:8000",headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>{let t=localStorage.getItem("access_token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,async e=>{let t=e.config;if(e.response?.status===401&&!t._retry){t._retry=!0;try{let e=localStorage.getItem("refresh_token");if(e){let s=await a.A.post("http://127.0.0.1:8000/api/auth/token/refresh/",{refresh:e});return localStorage.setItem("access_token",s.data.access),t.headers.Authorization=`Bearer ${s.data.access}`,r(t)}}catch(e){console.error("Error refreshing token:",e)}}return Promise.reject(e)});let i=r},61286:(e,t,s)=>{"use strict";s.d(t,{Ly:()=>i,_N:()=>n,companies:()=>r,zZ:()=>l});var a=s(14335);let r=[{id:1,name:"TechCorp Inc",description:"Leading technology solutions provider",industry:"Technology",size:"500-1000",founded:"2010",website:"https://techcorp.com"},{id:2,name:"DataCorp",description:"Data analytics and insights company",industry:"Data Analytics",size:"100-500",founded:"2015",website:"https://datacorp.com"}],i=[{id:1,job_id:25,title:"Software Engineer",company:"TechCorp Inc",status:"APPLIED",application_deadline:"2024-05-30T23:59:59Z"},{id:2,job_id:26,title:"Data Scientist",company:"DataCorp",status:"INTERVIEW SCHEDULED",application_deadline:"2024-06-15T23:59:59Z"}],n=async(e={})=>{try{let t={...e,_t:new Date().getTime()};console.log("Fetching companies with cache busting...");let s=await (0,a.mm)(t),i=[];s.data&&Array.isArray(s.data)?i=s.data:s.data&&s.data.results&&Array.isArray(s.data.results)?i=s.data.results:s.data&&s.data.data&&Array.isArray(s.data.data)&&(i=s.data.data);let n=i.map(a.Y_);if(console.log(`Fetched ${n.length} companies from API`),0===n.length)return console.warn("API returned empty companies array, using static data"),r;return n}catch(e){console.error("Error fetching companies:",e);try{console.log("Trying alternate endpoint format...");let e=await fetch("/api/v1/college/default-college/companies/");if(e.ok){let t=await e.json(),s=Array.isArray(t)?t:t.data||t.results||[];if(s.length>0)return console.log("Successfully retrieved companies from alternate endpoint"),s.map(a.Y_)}}catch(e){console.error("Alternate endpoint also failed:",e)}return r}};function l(e){return console.log(`Fetching jobs for company ID: ${e}`),[]}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63787:(e,t,s)=>{"use strict";function a(){try{let e=localStorage.getItem("access");if(!e)return null;let t=localStorage.getItem("user");if(t){let e=JSON.parse(t);return e.id||e.user_id||null}if(e)try{let t=e.split(".")[1],s=JSON.parse(atob(t));return s.user_id||s.id||s.sub||null}catch(e){console.error("Error decoding JWT token:",e)}return null}catch(e){return console.error("Error getting user ID:",e),null}}function r(){return localStorage.getItem("access")}function i(){try{let e=localStorage.getItem("user");return e?JSON.parse(e):null}catch(e){return console.error("Error getting user data:",e),null}}s.d(t,{F6:()=>a,c4:()=>r,gL:()=>i})},64398:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},65222:(e,t,s)=>{"use strict";s.d(t,{G_:()=>r});var a=s(60687);function r({description:e,className:t=""}){let s=e?e.replace(/\n/g,"<br />").replace(/•/g,"•").replace(/\*\s/g,"• ").replace(/-\s/g,"• ").trim():"No description provided.";return(0,a.jsx)("div",{className:`text-gray-700 leading-relaxed ${t}`,dangerouslySetInnerHTML:{__html:s}})}},66194:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\company\\\\[id]\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\company\\[id]\\page.jsx","default")},67760:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},87452:(e,t,s)=>{Promise.resolve().then(s.bind(s,66194))},94735:e=>{"use strict";e.exports=require("events")},97992:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},99310:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>c});var a=s(65239),r=s(48088),i=s(88170),n=s.n(i),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c={children:["",{children:["company",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,66194)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\company\\[id]\\page.jsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,75535)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\layout.js"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\company\\[id]\\page.jsx"],m={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/company/[id]/page",pathname:"/company/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4447,681,1658,1060,2305],()=>s(99310));module.exports=a})();