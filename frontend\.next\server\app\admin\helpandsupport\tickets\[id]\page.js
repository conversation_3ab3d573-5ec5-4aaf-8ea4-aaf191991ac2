(()=>{var e={};e.id=592,e.ids=[592],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5216:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>K});var a=r(60687),n=r(43210),s=r(85814),i=r.n(s),o=r(16189),l=r(97299),c=r(76717),d=r(47033),u=r(93613),m=r(96882),h=r(11860),x=r(62688);let p=(0,x.A)("file",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]]),f=(0,x.A)("paperclip",[["path",{d:"m16 6-8.414 8.586a2 2 0 0 0 2.829 2.829l8.414-8.586a4 4 0 1 0-5.657-5.657l-8.379 8.551a6 6 0 1 0 8.485 8.485l8.379-8.551",key:"1miecu"}]]);var g=r(27900),y=r(28885),b=r(64722),w=r(78872),j=r(31504),v=r(29789),N=r(58505),k=r(23711);function C(e,t){let r=(0,k.a)(e,t?.in);return r.setHours(0,0,0,0),r}function _(e,t){let r=(0,w.q)(),a=t?.weekStartsOn??t?.locale?.options?.weekStartsOn??r.weekStartsOn??r.locale?.options?.weekStartsOn??0,n=(0,k.a)(e,t?.in),s=n.getDay();return n.setDate(n.getDate()-(7*(s<a)+s-a)),n.setHours(0,0,0,0),n}function S(e,t){return _(e,{...t,weekStartsOn:1})}var D=r(87981);function M(e,t){let r=(0,k.a)(e,t?.in),a=r.getFullYear(),n=(0,D.w)(r,0);n.setFullYear(a+1,0,4),n.setHours(0,0,0,0);let s=S(n),i=(0,D.w)(r,0);i.setFullYear(a,0,4),i.setHours(0,0,0,0);let o=S(i);return r.getTime()>=s.getTime()?a+1:r.getTime()>=o.getTime()?a:a-1}function A(e,t){let r=(0,k.a)(e,t?.in),a=r.getFullYear(),n=(0,w.q)(),s=t?.firstWeekContainsDate??t?.locale?.options?.firstWeekContainsDate??n.firstWeekContainsDate??n.locale?.options?.firstWeekContainsDate??1,i=(0,D.w)(t?.in||e,0);i.setFullYear(a+1,0,s),i.setHours(0,0,0,0);let o=_(i,t),l=(0,D.w)(t?.in||e,0);l.setFullYear(a,0,s),l.setHours(0,0,0,0);let c=_(l,t);return+r>=+o?a+1:+r>=+c?a:a-1}function P(e,t){let r=Math.abs(e).toString().padStart(t,"0");return(e<0?"-":"")+r}let T={y(e,t){let r=e.getFullYear(),a=r>0?r:1-r;return P("yy"===t?a%100:a,t.length)},M(e,t){let r=e.getMonth();return"M"===t?String(r+1):P(r+1,2)},d:(e,t)=>P(e.getDate(),t.length),a(e,t){let r=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return r.toUpperCase();case"aaa":return r;case"aaaaa":return r[0];default:return"am"===r?"a.m.":"p.m."}},h:(e,t)=>P(e.getHours()%12||12,t.length),H:(e,t)=>P(e.getHours(),t.length),m:(e,t)=>P(e.getMinutes(),t.length),s:(e,t)=>P(e.getSeconds(),t.length),S(e,t){let r=t.length;return P(Math.trunc(e.getMilliseconds()*Math.pow(10,r-3)),t.length)}},q={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},E={G:function(e,t,r){let a=+(e.getFullYear()>0);switch(t){case"G":case"GG":case"GGG":return r.era(a,{width:"abbreviated"});case"GGGGG":return r.era(a,{width:"narrow"});default:return r.era(a,{width:"wide"})}},y:function(e,t,r){if("yo"===t){let t=e.getFullYear();return r.ordinalNumber(t>0?t:1-t,{unit:"year"})}return T.y(e,t)},Y:function(e,t,r,a){let n=A(e,a),s=n>0?n:1-n;return"YY"===t?P(s%100,2):"Yo"===t?r.ordinalNumber(s,{unit:"year"}):P(s,t.length)},R:function(e,t){return P(M(e),t.length)},u:function(e,t){return P(e.getFullYear(),t.length)},Q:function(e,t,r){let a=Math.ceil((e.getMonth()+1)/3);switch(t){case"Q":return String(a);case"QQ":return P(a,2);case"Qo":return r.ordinalNumber(a,{unit:"quarter"});case"QQQ":return r.quarter(a,{width:"abbreviated",context:"formatting"});case"QQQQQ":return r.quarter(a,{width:"narrow",context:"formatting"});default:return r.quarter(a,{width:"wide",context:"formatting"})}},q:function(e,t,r){let a=Math.ceil((e.getMonth()+1)/3);switch(t){case"q":return String(a);case"qq":return P(a,2);case"qo":return r.ordinalNumber(a,{unit:"quarter"});case"qqq":return r.quarter(a,{width:"abbreviated",context:"standalone"});case"qqqqq":return r.quarter(a,{width:"narrow",context:"standalone"});default:return r.quarter(a,{width:"wide",context:"standalone"})}},M:function(e,t,r){let a=e.getMonth();switch(t){case"M":case"MM":return T.M(e,t);case"Mo":return r.ordinalNumber(a+1,{unit:"month"});case"MMM":return r.month(a,{width:"abbreviated",context:"formatting"});case"MMMMM":return r.month(a,{width:"narrow",context:"formatting"});default:return r.month(a,{width:"wide",context:"formatting"})}},L:function(e,t,r){let a=e.getMonth();switch(t){case"L":return String(a+1);case"LL":return P(a+1,2);case"Lo":return r.ordinalNumber(a+1,{unit:"month"});case"LLL":return r.month(a,{width:"abbreviated",context:"standalone"});case"LLLLL":return r.month(a,{width:"narrow",context:"standalone"});default:return r.month(a,{width:"wide",context:"standalone"})}},w:function(e,t,r,a){let n=function(e,t){let r=(0,k.a)(e,t?.in);return Math.round((_(r,t)-function(e,t){let r=(0,w.q)(),a=t?.firstWeekContainsDate??t?.locale?.options?.firstWeekContainsDate??r.firstWeekContainsDate??r.locale?.options?.firstWeekContainsDate??1,n=A(e,t),s=(0,D.w)(t?.in||e,0);return s.setFullYear(n,0,a),s.setHours(0,0,0,0),_(s,t)}(r,t))/N.my)+1}(e,a);return"wo"===t?r.ordinalNumber(n,{unit:"week"}):P(n,t.length)},I:function(e,t,r){let a=function(e,t){let r=(0,k.a)(e,void 0);return Math.round((S(r)-function(e,t){let r=M(e,void 0),a=(0,D.w)(e,0);return a.setFullYear(r,0,4),a.setHours(0,0,0,0),S(a)}(r))/N.my)+1}(e);return"Io"===t?r.ordinalNumber(a,{unit:"week"}):P(a,t.length)},d:function(e,t,r){return"do"===t?r.ordinalNumber(e.getDate(),{unit:"date"}):T.d(e,t)},D:function(e,t,r){let a=function(e,t){let r=(0,k.a)(e,void 0);return function(e,t,r){let[a,n]=(0,v.x)(void 0,e,t),s=C(a),i=C(n);return Math.round((s-(0,j.G)(s)-(i-(0,j.G)(i)))/N.w4)}(r,function(e,t){let r=(0,k.a)(e,void 0);return r.setFullYear(r.getFullYear(),0,1),r.setHours(0,0,0,0),r}(r))+1}(e);return"Do"===t?r.ordinalNumber(a,{unit:"dayOfYear"}):P(a,t.length)},E:function(e,t,r){let a=e.getDay();switch(t){case"E":case"EE":case"EEE":return r.day(a,{width:"abbreviated",context:"formatting"});case"EEEEE":return r.day(a,{width:"narrow",context:"formatting"});case"EEEEEE":return r.day(a,{width:"short",context:"formatting"});default:return r.day(a,{width:"wide",context:"formatting"})}},e:function(e,t,r,a){let n=e.getDay(),s=(n-a.weekStartsOn+8)%7||7;switch(t){case"e":return String(s);case"ee":return P(s,2);case"eo":return r.ordinalNumber(s,{unit:"day"});case"eee":return r.day(n,{width:"abbreviated",context:"formatting"});case"eeeee":return r.day(n,{width:"narrow",context:"formatting"});case"eeeeee":return r.day(n,{width:"short",context:"formatting"});default:return r.day(n,{width:"wide",context:"formatting"})}},c:function(e,t,r,a){let n=e.getDay(),s=(n-a.weekStartsOn+8)%7||7;switch(t){case"c":return String(s);case"cc":return P(s,t.length);case"co":return r.ordinalNumber(s,{unit:"day"});case"ccc":return r.day(n,{width:"abbreviated",context:"standalone"});case"ccccc":return r.day(n,{width:"narrow",context:"standalone"});case"cccccc":return r.day(n,{width:"short",context:"standalone"});default:return r.day(n,{width:"wide",context:"standalone"})}},i:function(e,t,r){let a=e.getDay(),n=0===a?7:a;switch(t){case"i":return String(n);case"ii":return P(n,t.length);case"io":return r.ordinalNumber(n,{unit:"day"});case"iii":return r.day(a,{width:"abbreviated",context:"formatting"});case"iiiii":return r.day(a,{width:"narrow",context:"formatting"});case"iiiiii":return r.day(a,{width:"short",context:"formatting"});default:return r.day(a,{width:"wide",context:"formatting"})}},a:function(e,t,r){let a=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return r.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"aaa":return r.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return r.dayPeriod(a,{width:"narrow",context:"formatting"});default:return r.dayPeriod(a,{width:"wide",context:"formatting"})}},b:function(e,t,r){let a,n=e.getHours();switch(a=12===n?q.noon:0===n?q.midnight:n/12>=1?"pm":"am",t){case"b":case"bb":return r.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"bbb":return r.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return r.dayPeriod(a,{width:"narrow",context:"formatting"});default:return r.dayPeriod(a,{width:"wide",context:"formatting"})}},B:function(e,t,r){let a,n=e.getHours();switch(a=n>=17?q.evening:n>=12?q.afternoon:n>=4?q.morning:q.night,t){case"B":case"BB":case"BBB":return r.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"BBBBB":return r.dayPeriod(a,{width:"narrow",context:"formatting"});default:return r.dayPeriod(a,{width:"wide",context:"formatting"})}},h:function(e,t,r){if("ho"===t){let t=e.getHours()%12;return 0===t&&(t=12),r.ordinalNumber(t,{unit:"hour"})}return T.h(e,t)},H:function(e,t,r){return"Ho"===t?r.ordinalNumber(e.getHours(),{unit:"hour"}):T.H(e,t)},K:function(e,t,r){let a=e.getHours()%12;return"Ko"===t?r.ordinalNumber(a,{unit:"hour"}):P(a,t.length)},k:function(e,t,r){let a=e.getHours();return(0===a&&(a=24),"ko"===t)?r.ordinalNumber(a,{unit:"hour"}):P(a,t.length)},m:function(e,t,r){return"mo"===t?r.ordinalNumber(e.getMinutes(),{unit:"minute"}):T.m(e,t)},s:function(e,t,r){return"so"===t?r.ordinalNumber(e.getSeconds(),{unit:"second"}):T.s(e,t)},S:function(e,t){return T.S(e,t)},X:function(e,t,r){let a=e.getTimezoneOffset();if(0===a)return"Z";switch(t){case"X":return O(a);case"XXXX":case"XX":return z(a);default:return z(a,":")}},x:function(e,t,r){let a=e.getTimezoneOffset();switch(t){case"x":return O(a);case"xxxx":case"xx":return z(a);default:return z(a,":")}},O:function(e,t,r){let a=e.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+$(a,":");default:return"GMT"+z(a,":")}},z:function(e,t,r){let a=e.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+$(a,":");default:return"GMT"+z(a,":")}},t:function(e,t,r){return P(Math.trunc(e/1e3),t.length)},T:function(e,t,r){return P(+e,t.length)}};function $(e,t=""){let r=e>0?"-":"+",a=Math.abs(e),n=Math.trunc(a/60),s=a%60;return 0===s?r+String(n):r+String(n)+t+P(s,2)}function O(e,t){return e%60==0?(e>0?"-":"+")+P(Math.abs(e)/60,2):z(e,t)}function z(e,t=""){let r=Math.abs(e);return(e>0?"-":"+")+P(Math.trunc(r/60),2)+t+P(r%60,2)}let L=(e,t)=>{switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});default:return t.date({width:"full"})}},Y=(e,t)=>{switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});default:return t.time({width:"full"})}},F={p:Y,P:(e,t)=>{let r,a=e.match(/(P+)(p+)?/)||[],n=a[1],s=a[2];if(!s)return L(e,t);switch(n){case"P":r=t.dateTime({width:"short"});break;case"PP":r=t.dateTime({width:"medium"});break;case"PPP":r=t.dateTime({width:"long"});break;default:r=t.dateTime({width:"full"})}return r.replace("{{date}}",L(n,t)).replace("{{time}}",Y(s,t))}},H=/^D+$/,G=/^Y+$/,I=["D","DD","YY","YYYY"],B=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,R=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,W=/^'([^]*?)'?$/,Q=/''/g,X=/[a-zA-Z]/;function U(e,t,r){let a=(0,w.q)(),n=r?.locale??a.locale??b.c,s=r?.firstWeekContainsDate??r?.locale?.options?.firstWeekContainsDate??a.firstWeekContainsDate??a.locale?.options?.firstWeekContainsDate??1,i=r?.weekStartsOn??r?.locale?.options?.weekStartsOn??a.weekStartsOn??a.locale?.options?.weekStartsOn??0,o=(0,k.a)(e,r?.in);if(!(o instanceof Date||"object"==typeof o&&"[object Date]"===Object.prototype.toString.call(o))&&"number"!=typeof o||isNaN(+(0,k.a)(o)))throw RangeError("Invalid time value");let l=t.match(R).map(e=>{let t=e[0];return"p"===t||"P"===t?(0,F[t])(e,n.formatLong):e}).join("").match(B).map(e=>{if("''"===e)return{isToken:!1,value:"'"};let t=e[0];if("'"===t)return{isToken:!1,value:function(e){let t=e.match(W);return t?t[1].replace(Q,"'"):e}(e)};if(E[t])return{isToken:!0,value:e};if(t.match(X))throw RangeError("Format string contains an unescaped latin alphabet character `"+t+"`");return{isToken:!1,value:e}});n.localize.preprocessor&&(l=n.localize.preprocessor(o,l));let c={firstWeekContainsDate:s,weekStartsOn:i,locale:n};return l.map(a=>{if(!a.isToken)return a.value;let s=a.value;return(!r?.useAdditionalWeekYearTokens&&G.test(s)||!r?.useAdditionalDayOfYearTokens&&H.test(s))&&function(e,t,r){let a=function(e,t,r){let a="Y"===e[0]?"years":"days of the month";return`Use \`${e.toLowerCase()}\` instead of \`${e}\` (in \`${t}\`) for formatting ${a} to the input \`${r}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}(e,t,r);if(console.warn(a),I.includes(e))throw RangeError(a)}(s,t,String(e)),(0,E[s[0]])(o,s,n.localize,c)}).join("")}let V=[1,2,3,4,5];function Z({open:e,onClose:t,onSubmit:r,loading:s}){let[i,o]=(0,n.useState)(0),[l,c]=(0,n.useState)(0),[d,u]=(0,n.useState)("");return e?(0,a.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-opacity-30",children:(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-xl max-w-lg w-full p-8 relative",children:[(0,a.jsx)("button",{className:"absolute top-4 right-4 text-gray-400 hover:text-gray-600 text-2xl",onClick:t,"aria-label":"Close",children:"\xd7"}),(0,a.jsx)("h2",{className:"text-2xl font-bold mb-2",children:"Support Ticket Feedback"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Please rate your support experience and provide any additional comments"}),(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),0!==i&&r({rating:i,comment:d})},children:[(0,a.jsx)("div",{className:"flex justify-center mb-6",children:V.map(e=>(0,a.jsx)("button",{type:"button",className:"focus:outline-none",onClick:()=>o(e),onMouseEnter:()=>c(e),onMouseLeave:()=>c(0),"aria-label":`Rate ${e} star${e>1?"s":""}`,children:(0,a.jsx)("svg",{className:`w-10 h-10 mx-1 ${e<=(l||i)?"text-yellow-400":"text-gray-300"}`,fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.286 3.967a1 1 0 00.95.69h4.178c.969 0 1.371 1.24.588 1.81l-3.385 2.46a1 1 0 00-.364 1.118l1.287 3.966c.3.922-.755 1.688-1.54 1.118l-3.385-2.46a1 1 0 00-1.175 0l-3.385 2.46c-.784.57-1.838-.196-1.54-1.118l1.287-3.966a1 1 0 00-.364-1.118L2.045 9.394c-.783-.57-.38-1.81.588-1.81h4.178a1 1 0 00.95-.69l1.286-3.967z"})})},e))}),(0,a.jsx)("textarea",{className:"w-full border border-gray-300 rounded-md p-3 mb-6 focus:outline-none focus:ring-2 focus:ring-blue-500",rows:4,placeholder:"Share your experience with our support team...",value:d,onChange:e=>u(e.target.value)}),(0,a.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,a.jsx)("button",{type:"button",className:"px-4 py-2 rounded-md border border-gray-300 bg-white text-gray-700 hover:bg-gray-100",onClick:t,disabled:s,children:"Cancel"}),(0,a.jsx)("button",{type:"submit",className:"px-4 py-2 rounded-md bg-blue-600 text-white font-semibold hover:bg-blue-700 disabled:opacity-60",disabled:s||0===i,children:s?"Submitting...":"Submit Feedback"})]})]})]})}):null}let K=()=>{let e=(0,o.useParams)().id,[t,r]=(0,n.useState)(null),[s,x]=(0,n.useState)(!0),[b,w]=(0,n.useState)([]),[j,v]=(0,n.useState)(!1),[N,k]=(0,n.useState)("comments"),[C,_]=(0,n.useState)(""),[S,D]=(0,n.useState)(null),[M,A]=(0,n.useState)(null),[P,T]=(0,n.useState)(!0),[q,E]=(0,n.useState)(!1),[$,O]=(0,n.useState)(!1),[z,L]=(0,n.useState)(null),[Y,F]=(0,n.useState)(!1),[H,G]=(0,n.useState)(!1),[I,B]=(0,n.useState)(!1),R=(0,n.useRef)(null),W=(0,n.useRef)(null),Q=e=>{if(!e)return"";try{let t=new Date(e);return U(t,"MMMM d, yyyy 'at' h:mm a")}catch(t){return console.error("Error formatting date:",t),e}},X=e=>{if(!e)return"";try{let t=new Date(e);return U(t,"h:mm a")}catch(e){return console.error("Error formatting time:",e),""}};(0,n.useEffect)(()=>{(async()=>{try{x(!0),L(null),await y.M$.ensureLoggedIn();let t=await y.yp.getTicket(e);r(t),V(!1)}catch(e){console.error("Error fetching ticket:",e),L("Failed to load ticket. Please try again later.")}finally{x(!1)}})()},[e]);let V=async(t=!1)=>{try{v(!0);let r=await y.yp.getComments(e);console.log("Raw API response:",r);let a=[];Array.isArray(r)?a=r:r&&"object"==typeof r&&(Array.isArray(r.data)?a=r.data:r.comments&&Array.isArray(r.comments)?a=r.comments:r.results&&Array.isArray(r.results)&&(a=r.results));let n=localStorage.getItem("user")||sessionStorage.getItem("user"),s=null;if(n)try{s=JSON.parse(n)}catch(e){console.error("Error parsing current user data:",e)}console.log("Current user data for comment processing:",s),t&&(a=[...b.filter(e=>e._isLocalComment).map(e=>e._originalData),...a]);let i=a.map(e=>{let t=!1;if(console.log("Processing comment for direction:",e),"sent"===e.direction||"sent"===e.type)t=!0,console.log("Comment marked as sent by direction/type");else if("received"===e.direction||"received"===e.type)t=!1,console.log("Comment marked as received by direction/type");else if("admin"===e.sender_type||"admin"===e.from||"admin"===e.sender)t=!0,console.log("Comment marked as sent by sender_type/from/sender = admin");else if("user"===e.sender_type||"user"===e.from||"user"===e.sender)t=!1,console.log("Comment marked as received by sender_type/from/sender = user");else if("admin"===e.created_by||"admin"===e.user_type)t=!0,console.log("Comment marked as sent by created_by/user_type = admin");else if("user"===e.created_by||"user"===e.user_type)t=!1,console.log("Comment marked as received by created_by/user_type = user");else if(console.log("Current user data for this comment:",s),s)e.created_by===s.id||e.user_id===s.id?(t=!0,console.log("Comment marked as sent by current user ID match")):"admin"===s.user_type||"admin"===s.role?(t=!1,console.log("Comment marked as received (admin interface, not from current admin)")):(t=!1,console.log("Comment marked as received (user interface)"));else{let r=["admin@","admin1@","support@","help@"];["Thank you for your feedback","Thank you for your patience","Ticket status changed to","Attached file:"].some(t=>e.content&&e.content.includes(t))||r.some(t=>e.created_by_email&&e.created_by_email.includes(t))||r.some(t=>e.user_email&&e.user_email.includes(t))?(t=!0,console.log("Comment marked as sent by admin pattern detection")):(t=!1,console.log("Comment marked as received (default fallback)"))}let r={id:e.id||e._id||`comment-${Date.now()}-${Math.random()}`,content:e.content||e.message||e.text||"",is_sent:t,created_at:e.created_at||e.createdAt||e.timestamp||new Date().toISOString(),attachment:e.attachment||e.file||null,_originalData:e,_isLocalComment:e._isLocalComment||!1};return console.log("Final normalized comment:",r),r});console.log("Normalized comments:",i),w(i)}catch(e){console.error("Error fetching comments:",e)}finally{v(!1)}};(0,n.useEffect)(()=>{W.current&&W.current.scrollIntoView({behavior:"smooth"})},[b]);let K=()=>{D(null),A(null),R.current&&(R.current.value="")},J=async()=>{if((C.trim()||S)&&!$)try{O(!0);let t=null;if(C.trim()){t=await y.yp.addComment(e,C.trim()),console.log("New comment created:",t);let r={id:t.id||t._id||Date.now(),content:C.trim(),is_sent:!0,created_at:new Date().toISOString(),_originalData:t,_isLocalComment:!0};w(e=>[...e,r])}if(S){let t=await y.yp.addAttachment(e,S);if(console.log("File uploaded:",t),!C.trim()){let r=await y.yp.addComment(e,`Attached file: ${S.name}`),a={id:r.id||Date.now(),content:`Attached file: ${S.name}`,is_sent:!0,created_at:new Date().toISOString(),attachment:{url:t.url||"#",filename:S.name,type:S.type,size:S.size},_originalData:r,_isLocalComment:!0};w(e=>[...e,a])}}_(""),K(),setTimeout(()=>{V(!0)},1e3)}catch(e){console.error("Error sending message:",e),alert("Failed to send message. Please try again.")}finally{O(!1)}},ee=async t=>{try{await y.yp.updateTicket(e,{status:t}),r(e=>({...e,status:t})),await y.yp.addComment(e,`Ticket status changed to ${t}`),V(!1)}catch(e){console.error("Error updating ticket status:",e),alert("Failed to update ticket status. Please try again.")}},et=async()=>{alert("Reassign functionality would open a user selection modal")},er=async(e,t)=>{let r=`ticket_feedback_${e}_${t}`;if(localStorage.getItem(r))return!0;try{let a=await y.yp.checkFeedback(e);if(a&&a.user_id===t)return localStorage.setItem(r,"1"),!0}catch(e){}return!1};(0,n.useEffect)(()=>{if(t&&"resolved"===t.status){let e=localStorage.getItem("user"),r=null;if(e)try{r=JSON.parse(e).id}catch{}r&&t.createdBy?.id===r&&er(t.id,r).then(e=>{B(e),F(!e)})}else F(!1)},[t]);let ea=async({rating:e,comment:r})=>{G(!0);let a=localStorage.getItem("user"),n=null;if(a)try{n=JSON.parse(a).id}catch{}try{await y.yp.submitFeedback(t.id,{rating:e,comment:r}),n&&localStorage.setItem(`ticket_feedback_${t.id}_${n}`,"1"),B(!0),F(!1)}catch(e){alert("Failed to submit feedback. Please try again.")}finally{G(!1)}};return((0,n.useEffect)(()=>{let t=setInterval(async()=>{try{let t=await y.yp.getComments(e),r=[];Array.isArray(t)?r=t:t&&"object"==typeof t&&(Array.isArray(t.data)?r=t.data:t.comments&&Array.isArray(t.comments)?r=t.comments:t.results&&Array.isArray(t.results)&&(r=t.results));let a=r.map(e=>{let t=!1;return"sent"===e.direction||"sent"===e.type?t=!0:"received"===e.direction||"received"===e.type?t=!1:"admin"===e.sender_type||"admin"===e.from||"admin"===e.sender?t=!0:"user"===e.sender_type||"user"===e.from||"user"===e.sender?t=!1:"admin"===e.created_by||"admin"===e.user_type?t=!0:("user"===e.created_by||"user"===e.user_type)&&(t=!1),{id:e.id||e._id||`comment-${Date.now()}-${Math.random()}`,content:e.content||e.message||e.text||"",is_sent:t,created_at:e.created_at||e.createdAt||e.timestamp||new Date().toISOString(),attachment:e.attachment||e.file||null,_originalData:e,_isLocalComment:e._isLocalComment||!1}});w(e=>{let t=new Set(e.map(e=>e.id)),r=a.filter(e=>!t.has(e.id));return 0===r.length?e:[...e,...r].sort((e,t)=>new Date(e.created_at)-new Date(t.created_at))})}catch(e){}},3e3);return()=>clearInterval(t)},[e]),s)?(0,a.jsx)("div",{className:"space-y-6 pb-8",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(c.$,{variant:"outline",size:"icon",asChild:!0,children:(0,a.jsx)(i(),{href:"/admin/helpandsupport/tickets",children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})}),(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Loading..."})]})}):z?(0,a.jsxs)("div",{className:"space-y-6 pb-8",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(c.$,{variant:"outline",size:"icon",asChild:!0,children:(0,a.jsx)(i(),{href:"/admin/helpandsupport/tickets",children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})}),(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Error"})]}),(0,a.jsx)(l.Zp,{children:(0,a.jsxs)(l.Wu,{className:"p-6 text-center",children:[(0,a.jsx)(u.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-500",children:z}),(0,a.jsx)(c.$,{className:"mt-4",asChild:!0,children:(0,a.jsx)(i(),{href:"/admin/helpandsupport/tickets",children:"Back to Tickets"})})]})})]}):t?(0,a.jsxs)("div",{className:"space-y-6 pb-8 mt-16",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between sm:items-center gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(c.$,{variant:"outline",size:"icon",asChild:!0,children:(0,a.jsx)(i(),{href:"/admin/helpandsupport",children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})}),(0,a.jsxs)("h1",{className:"text-2xl font-bold tracking-tight truncate",children:["Ticket #",t.id]})]}),(0,a.jsx)("span",{className:"px-2 py-1 text-sm bg-blue-100 text-blue-800 rounded-md font-medium",children:t.status})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,a.jsx)("div",{className:"lg:col-span-3 space-y-6",children:(0,a.jsx)(l.Zp,{className:"shadow-md",children:(0,a.jsxs)("div",{className:"flex flex-col h-[680px]",children:[(0,a.jsx)("div",{className:"p-4",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("h3",{className:"font-medium text-lg",children:"Conversation"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)(c.$,{variant:"outline",size:"sm",className:"flex items-center gap-1 text-xs",onClick:()=>E(!q),children:[(0,a.jsx)(m.A,{className:"h-4 w-4"}),"Ticket Details"]}),q&&(0,a.jsxs)("div",{className:"absolute top-full left-0 mt-2 z-50 w-80 bg-white rounded-lg shadow-lg border border-gray-200 p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsx)("h4",{className:"font-bold text-lg",children:t.title}),(0,a.jsx)("button",{onClick:()=>E(!1),className:"text-gray-400 hover:text-gray-600",children:(0,a.jsx)(h.A,{className:"h-4 w-4"})})]}),(0,a.jsxs)("div",{className:"mb-3 flex gap-2",children:[(0,a.jsx)("span",{className:"px-2 py-0.5 text-xs bg-blue-100 text-blue-800 rounded-full font-medium",children:t.priority}),(0,a.jsx)("span",{className:"px-2 py-0.5 text-xs bg-purple-100 text-purple-800 rounded-full font-medium",children:t.category})]}),(0,a.jsx)("p",{className:"text-sm text-gray-700 mb-3 whitespace-pre-line",children:t.description}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 border-t pt-2 mt-2",children:[(0,a.jsxs)("p",{children:["Created: ",Q(t.createdAt)]}),(0,a.jsxs)("p",{children:["Assigned to: ",t.assignedTo?.name||"admin"]})]})]})]})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{className:"text-xs bg-gray-100 px-3 py-1.5 rounded-full text-gray-600 hover:bg-gray-200 transition",onClick:()=>{_("Thank you for your feedback")},children:"Thank you for your feedback"}),(0,a.jsx)("button",{className:"text-xs bg-blue-100 text-blue-700 px-3 py-1.5 rounded-full hover:bg-blue-200 transition",onClick:()=>{_("Thank you for your patience")},children:"Thank you for your patience"})]})]})}),(0,a.jsxs)("div",{className:"flex-1 p-6 space-y-6 overflow-y-auto ",children:[(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsxs)("div",{className:"bg-gray-100 text-gray-700 px-4 py-2 rounded-lg text-sm max-w-md text-center",children:[(0,a.jsxs)("p",{children:["Ticket #",t.id," opened - ",t.title]}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:'Click on "Ticket Details" to view more information'})]})}),j?(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)("div",{className:"bg-gray-100 text-gray-700 px-4 py-2 rounded-lg text-sm",children:"Loading comments..."})}):b&&b.length>0?[...b].sort((e,t)=>new Date(e.created_at)-new Date(t.created_at)).map((e,t)=>{console.log(`Rendering comment ${t}:`,e),console.log(`Original data for comment ${t}:`,e._originalData);let r=e.is_sent?"justify-end":"justify-start";return console.log(`Comment ${t} direction:`,r,"is_sent:",e.is_sent),(0,a.jsx)("div",{className:`flex ${r}`,children:(0,a.jsxs)("div",{className:`${e.is_sent?"bg-blue-500 text-white":"bg-white border border-gray-200 shadow-sm"} px-4 py-3 rounded-lg max-w-md`,children:[(0,a.jsx)("p",{children:e.content}),(0,a.jsx)("div",{className:`text-xs ${e.is_sent?"text-blue-100":"text-gray-400"} mt-1 text-right`,children:X(e.created_at||e.createdAt)}),e.attachment&&(0,a.jsx)("div",{className:`mt-2 p-1 rounded ${e.is_sent?"bg-white":"bg-gray-100"}`,children:e.attachment.type?.startsWith("image/")?(0,a.jsx)("img",{src:e.attachment.url,alt:"Attachment",className:"rounded max-w-xs w-full h-auto"}):(0,a.jsxs)("div",{className:"flex items-center p-2 rounded bg-gray-100",children:[(0,a.jsx)(p,{className:"h-6 w-6 text-blue-600 mr-2"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{className:"text-sm font-medium",children:e.attachment.filename}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[e.attachment.type," • ",Math.round(e.attachment.size/1024)," KB"]})]}),(0,a.jsx)("a",{href:e.attachment.url,target:"_blank",rel:"noopener noreferrer",className:"text-xs text-blue-600 hover:text-blue-800",children:"Download"})]})})]})},e.id||t)}):(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)("div",{className:"bg-gray-100 text-gray-700 px-4 py-2 rounded-lg text-sm",children:"No comments yet. Start the conversation!"})}),(0,a.jsx)("div",{ref:W})]}),S&&(0,a.jsx)("div",{className:"p-4 bg-blue-50 border-t border-blue-100",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[M?(0,a.jsx)("div",{className:"w-16 h-16 rounded border overflow-hidden mr-3 bg-white flex items-center justify-center",children:(0,a.jsx)("img",{src:M,alt:"Preview",className:"max-w-full max-h-full"})}):(0,a.jsx)("div",{className:"w-16 h-16 rounded border bg-gray-100 flex items-center justify-center mr-3",children:(0,a.jsx)(p,{className:"h-8 w-8 text-gray-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium truncate",children:S.name}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[(S.size/1024/1024).toFixed(2)," MB"]})]})]}),(0,a.jsx)("button",{onClick:K,className:"p-1 hover:bg-blue-100 rounded-full",children:(0,a.jsx)(h.A,{className:"h-5 w-5 text-blue-600"})})]})}),(0,a.jsxs)("div",{className:"p-4 bg-white",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>R.current.click(),className:"p-2 hover:bg-gray-100 rounded-full text-gray-500 hover:text-blue-600 transition-colors",title:"Attach file",children:(0,a.jsx)(f,{className:"w-5 h-5"})}),(0,a.jsx)("input",{type:"text",value:C,onChange:e=>_(e.target.value),placeholder:"Type your message...",className:"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),J())},disabled:$}),(0,a.jsx)("button",{className:`${$?"bg-blue-300":"bg-blue-500 hover:bg-blue-600"} text-white p-2 rounded-lg transition-colors flex items-center justify-center`,onClick:J,disabled:$,children:(0,a.jsx)(g.A,{className:"w-5 h-5"})}),(0,a.jsx)("input",{ref:R,type:"file",className:"hidden",onChange:e=>{let t=e.target.files[0];if(t)if(D(t),t.type.startsWith("image/")){let e=new FileReader;e.onloadend=()=>{A(e.result)},e.readAsDataURL(t)}else A(null)},accept:"image/*,.pdf,.doc,.docx,.xls,.xlsx,.txt"})]}),(0,a.jsx)("div",{className:"mt-2 text-xs text-gray-500 pl-2",children:"Supported formats: Images, PDF, DOC, XLSX, TXT (Max: 10MB)"})]})]})})}),(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsxs)(l.Zp,{className:"sticky top-6",children:[(0,a.jsx)(l.aR,{className:"pb-2",children:(0,a.jsx)(l.ZB,{className:"text-lg font-medium",children:"Ticket Information"})}),(0,a.jsxs)(l.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 mb-1",children:"Submitted By"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"h-6 w-6 rounded-full bg-gray-200 flex items-center justify-center text-xs",children:t.createdBy?.name?.charAt(0)||"a"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:t.createdBy?.name||"admin1"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 mb-1",children:"Assigned To"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"h-6 w-6 rounded-full bg-gray-200 flex items-center justify-center text-xs",children:t.assignedTo?.name?.charAt(0)||"a"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:t.assignedTo?.name||"admin"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 mb-1",children:"Created At"}),(0,a.jsx)("p",{className:"text-sm",children:Q(t.createdAt)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 mb-1",children:"Last Updated"}),(0,a.jsx)("p",{className:"text-sm",children:Q(t.updatedAt)})]}),(0,a.jsxs)("div",{className:"pt-2",children:[(0,a.jsx)(c.$,{className:"w-full",onClick:()=>ee("open"===t.status?"resolved":"open"),children:"open"===t.status?"Mark as Resolved":"Reopen Ticket"}),(0,a.jsx)(c.$,{variant:"outline",className:"w-full mt-2",onClick:et,children:"Reassign Ticket"})]})]})]})})]}),(0,a.jsx)(Z,{open:Y,onClose:()=>F(!1),onSubmit:ea,loading:H})]}):(0,a.jsxs)("div",{className:"space-y-6 pb-8",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(c.$,{variant:"outline",size:"icon",asChild:!0,children:(0,a.jsx)(i(),{href:"/admin/helpandsupport/tickets",children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})}),(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Ticket Not Found"})]}),(0,a.jsx)(l.Zp,{children:(0,a.jsxs)(l.Wu,{className:"p-6 text-center",children:[(0,a.jsx)("p",{className:"text-gray-500",children:"The requested ticket could not be found."}),(0,a.jsx)(c.$,{className:"mt-4",asChild:!0,children:(0,a.jsx)(i(),{href:"/admin/helpandsupport/tickets",children:"Back to Tickets"})})]})})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11072:(e,t,r)=>{Promise.resolve().then(r.bind(r,5216))},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27900:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},47033:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},47520:(e,t,r)=>{Promise.resolve().then(r.bind(r,53952))},53952:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\helpandsupport\\\\tickets\\\\[id]\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\helpandsupport\\tickets\\[id]\\page.jsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},99924:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var a=r(65239),n=r(48088),s=r(88170),i=r.n(s),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["admin",{children:["helpandsupport",{children:["tickets",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,53952)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\helpandsupport\\tickets\\[id]\\page.jsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,23697)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\layout.jsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,75535)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\layout.js"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\helpandsupport\\tickets\\[id]\\page.jsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/admin/helpandsupport/tickets/[id]/page",pathname:"/admin/helpandsupport/tickets/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,681,1658,1060,4403,2305,6728],()=>r(99924));module.exports=a})();