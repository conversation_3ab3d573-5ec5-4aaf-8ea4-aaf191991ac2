"use strict";exports.id=3322,exports.ids=[3322],exports.modules={33322:(e,t,a)=>{a.d(t,{N:()=>n});var r=a(51060),s=a(97632);let o=r.A.create({baseURL:"http://localhost:8000",headers:{"Content-Type":"application/json"}});o.interceptors.request.use(e=>{let t=(0,s.c4)();return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e)),o.interceptors.response.use(e=>e,e=>(e.response?.status===401&&(localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),window.location.href="/login"),Promise.reject(e)));let n={getStudents:async(e={})=>(await o.get("/api/accounts/students/",{params:e})).data,getStudentsWithStats:async(e={})=>{try{return(await o.get("/api/accounts/students/stats/",{params:e})).data}catch(r){console.log("Stats endpoint not available, using regular endpoint");let t=await o.get("/api/accounts/students/",{params:e}),a=t.data.data||t.data;if(Array.isArray(a)){let r=calculateStudentStats(a,e);return{...t.data,statistics:r}}return t.data}},getStudent:async e=>(await o.get(`/api/accounts/students/${e}/`)).data,updateStudent:async(e,t)=>{console.log("updateStudent called with:",{id:e,data:t});let a=(0,s.c4)();if(console.log("Auth token available:",!!a),a&&console.log("Token preview:",a.substring(0,20)+"..."),!a)throw Error("Authentication required to update student");let r={...t};["joining_year","passout_year"].forEach(e=>{if(null!==r[e]&&void 0!==r[e]){let t=parseInt(r[e]);r[e]=isNaN(t)?null:t}}),["first_name","last_name","student_id","contact_email","phone","branch","gpa","date_of_birth","address","city","district","state","pincode","country","parent_contact","education","skills","tenth_cgpa","tenth_percentage","tenth_board","tenth_school","tenth_year_of_passing","tenth_location","tenth_specialization","twelfth_cgpa","twelfth_percentage","twelfth_board","twelfth_school","twelfth_year_of_passing","twelfth_location","twelfth_specialization"].forEach(e=>{null!==r[e]&&void 0!==r[e]&&(r[e]=String(r[e]).trim())}),Object.keys(r).forEach(e=>{void 0===r[e]&&delete r[e]}),console.log("Cleaned data being sent:",r);try{console.log("Trying ViewSet endpoint:",`/api/accounts/profiles/${e}/`);let t=await o.patch(`/api/accounts/profiles/${e}/`,r);return console.log("ViewSet endpoint success:",t.data),t.data}catch(t){console.error("ViewSet endpoint failed:",{status:t.response?.status,statusText:t.response?.statusText,data:t.response?.data,headers:t.response?.headers,config:t.config});try{console.log("Trying fallback endpoint:",`/api/accounts/students/${e}/update/`);let t=await o.patch(`/api/accounts/students/${e}/update/`,r);return console.log("Fallback endpoint success:",t.data),t.data}catch(e){throw console.error("Failed to update student via both endpoints:",{viewSetError:{status:t.response?.status,data:t.response?.data},updateViewError:{status:e.response?.status,data:e.response?.data}}),e.response?.status===400?e:t}}},getProfile:async()=>{let e=(0,s.c4)();return o.get("/api/auth/profile/",{headers:{Authorization:`Bearer ${e}`}}).then(e=>e.data)},updateProfile:async e=>{let t=(0,s.c4)();return o.patch("/api/auth/profile/",e,{headers:{Authorization:`Bearer ${t}`}}).then(e=>e.data)},uploadProfileImage:async e=>{let t=(0,s.c4)(),a=new FormData;return a.append("image",e),o.post("/api/accounts/profiles/me/upload_profile_image/",a,{headers:{Authorization:`Bearer ${t}`,"Content-Type":"multipart/form-data"}}).then(e=>e.data)},uploadResume:async(e,t=null,a=!1)=>{let r=(0,s.c4)(),n=new FormData;return n.append("file",e),t&&n.append("name",t),n.append("is_primary",a),o.post("/api/accounts/profiles/me/resumes/",n,{headers:{Authorization:`Bearer ${r}`,"Content-Type":"multipart/form-data"}}).then(e=>e.data)},adminUploadResume:async(e,t,a=null,r=!1)=>{let n=(0,s.c4)();if(!n)throw Error("No authentication token found");let c=new FormData;return c.append("file",t),a&&c.append("name",a),c.append("is_primary",r),o.post(`/api/accounts/profiles/${e}/upload_resume/`,c,{headers:{Authorization:`Bearer ${n}`,"Content-Type":"multipart/form-data"}}).then(e=>e.data)},adminGetResumes:async e=>{let t=(0,s.c4)();if(!t)throw Error("No authentication token found");return o.get(`/api/accounts/profiles/${e}/resumes/`,{headers:{Authorization:`Bearer ${t}`}}).then(e=>e.data)},adminUploadCertificate:async(e,t,a)=>{let r=(0,s.c4)();if(!r)throw Error("No authentication token found");let n=new FormData;return n.append("file",t),n.append("type",a),o.post(`/api/accounts/profiles/${e}/upload_certificate/`,n,{headers:{Authorization:`Bearer ${r}`,"Content-Type":"multipart/form-data"}}).then(e=>e.data)},adminUploadSemesterMarksheet:async(e,t,a,r)=>{let n=(0,s.c4)();if(!n)throw Error("No authentication token found");let c=new FormData;return c.append("marksheet_file",t),c.append("semester",a),c.append("cgpa",r),o.post(`/api/accounts/profiles/${e}/upload_semester_marksheet/`,c,{headers:{Authorization:`Bearer ${n}`,"Content-Type":"multipart/form-data"}}).then(e=>e.data)},uploadResumeToProfile:async e=>{let t=(0,s.c4)(),a=new FormData;return a.append("resume",e),o.patch("/api/auth/profile/",a,{headers:{Authorization:`Bearer ${t}`,"Content-Type":"multipart/form-data"}}).then(e=>e.data)},getResumes:async()=>{try{let e=(0,s.c4)();if(!e)return console.log("No authentication token, returning empty array"),[];let t=await o.get("/api/accounts/profiles/me/resumes/",{headers:{Authorization:`Bearer ${e}`}});if(!t.data)return await n.getResumesLegacy();if(Array.isArray(t.data))return t.data;if(t.data&&t.data.data&&Array.isArray(t.data.data))return t.data.data;console.log("Response data is not an array, trying fallback. Response:",t.data);try{return await n.getResumesLegacy()}catch(e){return console.log("Fallback also failed, returning empty array"),[]}}catch(e){console.log("Resume endpoint failed, using fallback method");try{return await n.getResumesLegacy()}catch(e){return console.log("Fallback method also failed, returning empty array"),[]}}},getResumesLegacy:async()=>{try{if(!(0,s.c4)())return console.log("No auth token for legacy resume fetch"),[];let e=await n.getProfile();if(e?.resume||e?.resume_url){let t=e.resume_url||e.resume;if(t&&""!==t.trim()&&"null"!==t&&"undefined"!==t){let a=t.split("/").pop()||"Resume.pdf";return[{id:e.id||1,name:a,file_url:t,uploaded_at:e.updated_at||new Date().toISOString()}]}}return[]}catch(e){return console.log("Legacy resume fetch error:",e.message),[]}},deleteResume:async e=>{let t=(0,s.c4)();try{console.log(`Attempting to delete resume with ID: ${e}`);let a=await o.delete(`/api/accounts/profiles/me/resumes/${e}/`,{headers:{Authorization:`Bearer ${t}`}});return console.log("DELETE resume successful:",a.data),a.data}catch(e){throw console.error("Error deleting resume:",e),e}},deleteResumeLegacy:async e=>{let t=(0,s.c4)();try{console.log(`Attempting to delete resume with ID: ${e}`);let a=!1;for(let r of[async()=>{try{let a=await o.delete(`/api/accounts/profiles/me/resumes/${e}/`,{headers:{Authorization:`Bearer ${t}`}});return console.log("DELETE resume successful:",a.data),{success:!0,data:a.data}}catch(e){return console.log(`Strategy 1 failed: ${e.message}`),{success:!1}}},async()=>{try{let a=await o.post(`/api/accounts/profiles/me/resumes/${e}/remove/`,{},{headers:{Authorization:`Bearer ${t}`}});return console.log("POST remove successful:",a.data),{success:!0,data:a.data}}catch(e){return console.log(`Strategy 2 failed: ${e.message}`),{success:!1}}},async()=>{try{let a=await o.patch("/api/auth/profile/",{delete_resume:e},{headers:{Authorization:`Bearer ${t}`}});return console.log("PATCH profile successful:",a.data),{success:!0,data:a.data}}catch(e){return console.log(`Strategy 3 failed: ${e.message}`),{success:!1}}},async()=>{try{let e=await o.patch("/api/auth/profile/",{reset_resumes:!0},{headers:{Authorization:`Bearer ${t}`}});return console.log("Reset resumes successful:",e.data),{success:!0,data:e.data}}catch(e){return console.log(`Strategy 4 failed: ${e.message}`),{success:!1}}}])if((await r()).success){a=!0;break}return{success:a,message:a?"Resume deleted successfully":"Resume deleted locally but server sync failed"}}catch(e){return console.error("Resume deletion failed:",e.response?.status,e.message),{success:!0,synced:!1,error:e.message,status:e.response?.status,message:"Resume removed from display (sync with server failed)"}}},uploadCertificate:async(e,t)=>{let a=(0,s.c4)(),r=new FormData;return r.append("file",e),r.append("type",t),o.post("/api/accounts/profiles/me/upload_certificate/",r,{headers:{Authorization:`Bearer ${a}`,"Content-Type":"multipart/form-data"}}).then(e=>e.data)},getCertificates:async()=>{let e=(0,s.c4)();if(!e)throw Error("Authentication required to fetch certificates");try{let t=await o.get("/api/accounts/profiles/me/certificates/",{headers:{Authorization:`Bearer ${e}`}});if(!t.data)return console.error("Empty response when fetching certificates"),[];if(Array.isArray(t.data))return t.data;if(t.data.data&&Array.isArray(t.data.data))return t.data.data;return console.error("Unexpected certificate data format:",t.data),[]}catch(e){throw console.error("Certificate fetch error:",e.response?.status,e.message),e}},deleteCertificate:async e=>{let t=(0,s.c4)();try{console.log(`Attempting to delete certificate: ${e}`);let a=await o.delete(`/api/accounts/profiles/me/delete_certificate/${e}/`,{headers:{Authorization:`Bearer ${t}`}});return console.log("DELETE certificate successful:",a.data),a.data}catch(e){throw console.error("Error deleting certificate:",e),e}},adminDeleteCertificate:async(e,t)=>{let a=(0,s.c4)();try{console.log(`Admin attempting to delete certificate: ${t} for student: ${e}`);let r=await o.delete(`/api/accounts/profiles/${e}/delete_certificate/${t}/`,{headers:{Authorization:`Bearer ${a}`}});return console.log("Admin DELETE certificate successful:",r.data),r.data}catch(e){throw console.error("Error deleting certificate:",e),e}},deleteMarksheet:async e=>{let t=(0,s.c4)();try{console.log(`Attempting to delete marksheet for semester: ${e}`);let a=await o.delete(`/api/accounts/profiles/me/delete_marksheet/${e}/`,{headers:{Authorization:`Bearer ${t}`}});return console.log("DELETE marksheet successful:",a.data),a.data}catch(e){throw console.error("Error deleting marksheet:",e),e}},adminDeleteMarksheet:async(e,t)=>{let a=(0,s.c4)();try{console.log(`Admin attempting to delete marksheet for semester: ${t} for student: ${e}`);let r=await o.delete(`/api/accounts/profiles/${e}/delete_marksheet/${t}/`,{headers:{Authorization:`Bearer ${a}`}});return console.log("Admin DELETE marksheet successful:",r.data),r.data}catch(e){throw console.error("Error deleting marksheet:",e),e}},deleteCertificateLegacy:async e=>{let t=(0,s.c4)();try{console.log(`Attempting to delete certificate with ID: ${e}`);let a=!1;for(let r of[async()=>{try{let a=await o.delete(`/api/accounts/profiles/me/certificates/${e}/`,{headers:{Authorization:`Bearer ${t}`}});return console.log("DELETE certificate successful:",a.data),{success:!0,data:a.data}}catch(e){return console.log(`Strategy 1 failed: ${e.message}`),{success:!1}}},async()=>{try{let a=await o.post(`/api/accounts/profiles/me/certificates/${e}/remove/`,{},{headers:{Authorization:`Bearer ${t}`}});return console.log("POST remove successful:",a.data),{success:!0,data:a.data}}catch(e){return console.log(`Strategy 2 failed: ${e.message}`),{success:!1}}},async()=>{try{let a=await o.patch("/api/auth/profile/",{delete_certificate:e},{headers:{Authorization:`Bearer ${t}`}});return console.log("PATCH profile successful:",a.data),{success:!0,data:a.data}}catch(e){return console.log(`Strategy 3 failed: ${e.message}`),{success:!1}}},async()=>{try{let e=await o.patch("/api/auth/profile/",{reset_certificates:!0},{headers:{Authorization:`Bearer ${t}`}});return console.log("Reset certificates successful:",e.data),{success:!0,data:e.data}}catch(e){return console.log(`Strategy 4 failed: ${e.message}`),{success:!1}}}])if((await r()).success){a=!0;break}return{success:a,message:a?"Certificate deleted successfully":"Certificate deleted locally but server sync failed"}}catch(e){return console.error("Certificate deletion failed:",e.response?.status,e.message),{success:!0,synced:!1,error:e.message,status:e.response?.status,message:"Certificate removed from display (sync with server failed)"}}},getSemesterMarksheets:async()=>{let e=(0,s.c4)();return o.get("/api/accounts/profiles/me/semester_marksheets/",{headers:{Authorization:`Bearer ${e}`}}).then(e=>e.data)},uploadSemesterMarksheet:async(e,t,a)=>{let r=(0,s.c4)(),n=new FormData;return n.append("marksheet_file",e),n.append("semester",t),n.append("cgpa",a),o.post("/api/accounts/profiles/me/upload_semester_marksheet/",n,{headers:{Authorization:`Bearer ${r}`,"Content-Type":"multipart/form-data"}}).then(e=>e.data)},getFreezeStatus:async()=>{let e=(0,s.c4)();if(!e)throw Error("Authentication required to fetch freeze status");try{let t=(await o.get("/api/auth/profile/",{headers:{Authorization:`Bearer ${e}`}})).data;return{freeze_status:t.freeze_status||"none",freeze_reason:t.freeze_reason,freeze_date:t.freeze_date,min_salary_requirement:t.min_salary_requirement,allowed_job_tiers:t.allowed_job_tiers||[],allowed_job_types:t.allowed_job_types||[],allowed_companies:t.allowed_companies||[]}}catch(e){throw console.error("Freeze status fetch error:",e.response?.status,e.message),e}},canApplyToJob:async e=>{let t=(0,s.c4)();if(!t)throw Error("Authentication required to check job application eligibility");try{return(await o.get(`/api/v1/college/default-college/jobs/${e}/can-apply/`,{headers:{Authorization:`Bearer ${t}`}})).data}catch(e){throw console.error("Job application eligibility check error:",e.response?.status,e.message),e}}}},97632:(e,t,a)=>{a.d(t,{O5:()=>s,c4:()=>r,fE:()=>o}),a(58138);let r=()=>null,s=e=>{},o=e=>{}}};