(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6636],{26155:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h});var a=s(95155),l=s(12115),r=s(66766),n=s(29911),i=s(52338);function c(e){let{isOpen:t,onClose:s,resume:r,onUpload:c,onDelete:o}=e,[d,m]=(0,l.useState)([]),[x,u]=(0,l.useState)(!1),[h,p]=(0,l.useState)(!1),g=(0,l.useRef)(null);(0,l.useEffect)(()=>{t&&f()},[t]);let f=async()=>{try{if(p(!0),!localStorage.getItem("access_token")){console.error("No authentication token found"),m([]),p(!1);return}let e=[];try{if(console.log("Fetching user-specific resumes..."),e=await i.N.getResumes(),console.log("Resumes fetched:",e),!Array.isArray(e))throw console.error("Invalid resume data format:",e),Error("Invalid resume data format")}catch(t){console.log("New resumes API not available, falling back to profile data:",t);try{let t=await i.N.getProfile();if(console.log("User profile fetched for resume fallback:",null==t?void 0:t.id),(null==t?void 0:t.resume)||(null==t?void 0:t.resume_url)){let s=t.resume_url||t.resume,a=s.split("/").pop()||"Resume.pdf";e=[{id:t.id||1,name:a,resume_url:s,uploaded_at:t.updated_at||new Date().toISOString()}]}}catch(e){console.error("Error fetching profile for resume:",e)}}let t=e.map((e,t)=>{var s;return{id:e.id||t+1,name:e.name||e.file_name||(null==(s=e.resume_url)?void 0:s.split("/").pop())||"Resume ".concat(t+1),date:e.uploaded_at?new Date(e.uploaded_at).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"}):new Date().toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"}),url:e.resume_url||e.file_url||e.url,status:"success"}});console.log("Displaying ".concat(t.length," resumes for current user")),m(t)}catch(e){if(console.error("Error fetching resumes:",e),r){let e=[];if("string"==typeof r&&""!==r.trim()){let t=r.split("/"),s=t[t.length-1];e.push({id:1,name:s||"Resume",date:new Date().toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"}),url:r,status:"success"})}m(e)}else m([])}finally{p(!1)}},j=async e=>{let t=e.target.files[0];if(t)try{if(u(!0),t.size>5242880){alert("File size exceeds 5MB limit. Please select a smaller file."),u(!1);return}let e={id:Date.now(),name:t.name,date:new Date().toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"}),file:t,url:URL.createObjectURL(t),status:"uploading",progress:0};m(t=>[...t,e]);let s=setInterval(()=>{m(t=>t.map(t=>t.id===e.id?{...t,progress:Math.min(t.progress+25,99)}:t))},500);try{await i.N.uploadResume(t,t.name,!1),clearInterval(s),f().catch(e=>console.log("Resume refresh failed after upload:",e))}catch(t){throw clearInterval(s),m(t=>t.map(t=>t.id===e.id?{...t,status:"error",progress:0}:t)),t}u(!1)}catch(e){console.error("Error uploading resume:",e),u(!1),alert("Failed to upload resume. Please try again.")}},y=e=>{if(!e)return void alert("Resume URL is not available");if(e.startsWith("blob:"))window.open(e,"_blank");else if(e.startsWith("http://")||e.startsWith("https://"))window.open(e,"_blank");else{let t="".concat(window.location.origin).concat(e.startsWith("/")?"":"/").concat(e);window.open(t,"_blank")}},N=async e=>{try{if(e.url){let t=document.createElement("a");t.href=e.url,t.download=e.name||"resume.pdf",document.body.appendChild(t),t.click(),document.body.removeChild(t)}else alert("Resume file is not available for download")}catch(e){console.error("Error downloading resume:",e),alert("Failed to download resume. Please try again.")}},b=async e=>{try{let t=d.find(t=>t.id===e);if(m(t=>t.filter(t=>t.id!==e)),t&&t.id&&"number"==typeof t.id)try{let e=await i.N.deleteResume(t.id);console.log("Resume deletion response:",e);try{let e=Object.keys(localStorage).filter(e=>e.includes("resume")||e.includes("file")||e.includes("document"));e.length>0&&(console.log("Clearing resume-related localStorage items:",e),e.forEach(e=>localStorage.removeItem(e))),localStorage.removeItem("resume_count"),localStorage.removeItem("last_resume_update");let t=localStorage.getItem("user_profile");if(t)try{let e=JSON.parse(t);e&&e.resume&&(e.resume=null,localStorage.setItem("user_profile",JSON.stringify(e)))}catch(e){}}catch(e){console.error("Error clearing localStorage:",e)}}catch(e){console.error("Backend delete failed, but UI is updated:",e)}if("function"==typeof o)try{await o(t)}catch(e){console.error("onDelete callback error:",e)}f().catch(e=>console.log("Resume refresh failed after delete:",e))}catch(e){console.error("Error in delete process:",e),f().catch(e=>console.log("Resume refresh failed after delete error:",e))}},v=e=>"uploading"===e.status?(0,a.jsx)("div",{className:"ml-2 text-blue-500",children:(0,a.jsx)(n.hW,{className:"animate-spin"})}):"success"===e.status?(0,a.jsx)("div",{className:"ml-2 text-green-500",children:(0,a.jsx)(n.A7C,{})}):"error"===e.status?(0,a.jsx)("div",{className:"ml-2 text-red-500",children:(0,a.jsx)(n.TNq,{})}):null;return t?(0,a.jsx)("div",{className:"fixed inset-0 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg w-full max-w-2xl shadow-xl",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center p-6 border-b",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-800",children:"My Resumes"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Upload multiple resumes for different job types"})]}),(0,a.jsx)("button",{onClick:s,className:"text-gray-500 hover:text-gray-700",children:(0,a.jsx)(n._Hm,{size:24})})]}),(0,a.jsx)("div",{className:"p-6 max-h-96 overflow-y-auto",children:h?(0,a.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,a.jsx)(n.hW,{className:"animate-spin text-blue-500 text-2xl mr-3"}),(0,a.jsx)("span",{className:"text-gray-600",children:"Loading resumes..."})]}):0===d.length?(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)("p",{children:"No resumes uploaded yet"}),(0,a.jsx)("p",{className:"text-sm mt-2",children:"You can upload multiple resumes for different job applications"})]}):(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsxs)("h3",{className:"text-md font-medium text-gray-700",children:["Your Resumes (",d.length,")"]}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:d.length>1?"You can use different resumes for different applications":""})]}),(0,a.jsx)("div",{className:"space-y-4",children:d.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between bg-gray-50 p-4 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center flex-grow cursor-pointer hover:bg-gray-100 p-2 rounded",onClick:()=>"uploading"!==e.status?y(e.url):null,children:[(0,a.jsx)("div",{className:"bg-blue-100 p-3 rounded-full mr-4",children:(0,a.jsx)(n.t69,{className:"text-blue-600"})}),(0,a.jsxs)("div",{className:"flex-grow",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("h3",{className:"font-medium text-gray-800 mr-2",children:e.name}),v(e),"uploading"!==e.status&&(0,a.jsx)(n.EQc,{className:"text-gray-500 text-xs ml-2"})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Uploaded on ",e.date]}),"uploading"===e.status&&(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5 mt-2",children:(0,a.jsx)("div",{className:"bg-blue-600 h-2.5 rounded-full transition-all duration-300",style:{width:"".concat(e.progress,"%")}})})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>N(e),className:"p-2 text-green-500 hover:text-green-700 hover:bg-green-50 rounded-full","aria-label":"Download resume",disabled:"uploading"===e.status,title:"Download resume",children:(0,a.jsx)(n.dIn,{className:"uploading"===e.status?"opacity-50":""})}),(0,a.jsx)("button",{onClick:()=>b(e.id),className:"p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full","aria-label":"Delete resume",disabled:"uploading"===e.status,title:"Delete resume",children:(0,a.jsx)(n.qbC,{className:"uploading"===e.status?"opacity-50":""})})]})]},e.id))})]})}),(0,a.jsxs)("div",{className:"border-t p-6 flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Supported formats: PDF, DOCX (max 5MB)"}),(0,a.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"You can upload multiple resumes tailored to different positions"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("input",{type:"file",accept:".pdf,.docx",className:"hidden",ref:g,onChange:j,disabled:x}),(0,a.jsx)("button",{onClick:()=>g.current.click(),className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center ".concat(x?"opacity-70 cursor-not-allowed":""),disabled:x,children:x?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.hW,{className:"mr-2 animate-spin"})," Uploading..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.HVe,{className:"mr-2"})," Add Resume"]})})]})]})]})}):null}function o(e){let{isOpen:t,onClose:s,documents:r={},onUploadCertificate:i,onUploadMarksheet:c,onDeleteCertificate:o,onDeleteMarksheet:d}=e,[m,x]=(0,l.useState)("tenth"),[u,h]=(0,l.useState)(!1),[p,g]=(0,l.useState)({tenth:[],twelfth:[],semesterwise:[]}),f=(0,l.useRef)(null),j=(0,l.useRef)(null),y=(0,l.useRef)(null),N=e=>e?e&&!e.startsWith("http")?"http://localhost:8000".concat(e):e:null;(0,l.useEffect)(()=>{let e={tenth:[],twelfth:[],semesterwise:[]};if(r.tenth&&""!==r.tenth.trim()&&"null"!==r.tenth&&"undefined"!==r.tenth){let t="string"==typeof r.tenth?r.tenth.split("/"):["10th Certificate"],s=t[t.length-1];s&&""!==s&&"null"!==s&&(e.tenth=[{id:1,name:s,date:new Date().toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"}),url:r.tenth}])}if(r.twelfth&&""!==r.twelfth.trim()&&"null"!==r.twelfth&&"undefined"!==r.twelfth){let t="string"==typeof r.twelfth?r.twelfth.split("/"):["12th Certificate"],s=t[t.length-1];s&&""!==s&&"null"!==s&&(e.twelfth=[{id:1,name:s,date:new Date().toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"}),url:r.twelfth}])}r.semesterMarksheets&&Array.isArray(r.semesterMarksheets)&&r.semesterMarksheets.length>0&&(e.semesterwise=r.semesterMarksheets.filter(e=>e&&e.marksheet_url&&""!==e.marksheet_url.trim()).map(e=>({id:e.id,name:"Semester ".concat(e.semester," Marksheet (CGPA: ").concat(e.cgpa,")"),date:e.upload_date?new Date(e.upload_date).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"}):"Unknown date",url:e.marksheet_url||e.marksheet_file,semester:e.semester,cgpa:e.cgpa}))),g(e)},[r]);let b=async e=>{let t=e.target.files[0];if(t)try{if(h(!0),"tenth"===m){await i(t,"tenth");let e={id:Date.now(),name:t.name,date:new Date().toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"}),url:URL.createObjectURL(t)};g(t=>({...t,tenth:[e]}))}else if("twelfth"===m){await i(t,"twelfth");let e={id:Date.now(),name:t.name,date:new Date().toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"}),url:URL.createObjectURL(t)};g(t=>({...t,twelfth:[e]}))}else if("semesterwise"===m){if(!j.current||!y.current){alert("Semester input fields are not available"),h(!1);return}let e=j.current.value,s=y.current.value;if(!e||!s){alert("Please enter semester number and CGPA"),h(!1);return}await c(t,e,s);let a={id:Date.now(),name:"Semester ".concat(e," Marksheet (CGPA: ").concat(s,")"),date:new Date().toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"}),url:URL.createObjectURL(t),semester:e,cgpa:s};g(e=>({...e,semesterwise:[...e.semesterwise,a]}))}h(!1)}catch(e){console.error("Error uploading document:",e),h(!1),alert("Failed to upload document. Please try again.")}},v=e=>{if(!e)return void alert("Document URL is not available");let t=N(e);window.open(t,"_blank")},w=e=>{try{if(e.url){let t=N(e.url),s=e.createElement("a");s.href=t,s.download=e.name||"document.pdf",e.body.appendChild(s),s.click(),e.body.removeChild(s)}else alert("Document file is not available for download")}catch(e){console.error("Error downloading document:",e),alert("Failed to download document. Please try again.")}},_=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{"tenth"===m||"twelfth"===m?(await o("tenth"===m?"10th":"12th"),g(e=>({...e,[m]:[]}))):"semesterwise"===m&&t?(await d(t.semester),g(e=>({...e,semesterwise:e.semesterwise.filter(e=>e.semester!==t.semester)}))):g(t=>({...t,[m]:t[m].filter(t=>t.id!==e)}))}catch(e){console.error("Error deleting document:",e),alert("Failed to delete document. Please try again.")}};return t?(0,a.jsx)("div",{className:"fixed inset-0 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg w-full max-w-2xl shadow-xl",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center p-6 border-b",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-800",children:"My Documents"}),(0,a.jsx)("button",{onClick:s,className:"text-gray-500 hover:text-gray-700",children:(0,a.jsx)(n._Hm,{size:24})})]}),(0,a.jsxs)("div",{className:"flex border-b",children:[(0,a.jsx)("button",{className:"px-6 py-3 text-sm font-medium ".concat("tenth"===m?"text-blue-600 border-b-2 border-blue-600":"text-gray-500 hover:text-gray-700"),onClick:()=>x("tenth"),children:"10th Certificate"}),(0,a.jsx)("button",{className:"px-6 py-3 text-sm font-medium ".concat("twelfth"===m?"text-blue-600 border-b-2 border-blue-600":"text-gray-500 hover:text-gray-700"),onClick:()=>x("twelfth"),children:"12th Certificate"}),(0,a.jsx)("button",{className:"px-6 py-3 text-sm font-medium ".concat("semesterwise"===m?"text-blue-600 border-b-2 border-blue-600":"text-gray-500 hover:text-gray-700"),onClick:()=>x("semesterwise"),children:"Semester Grades"})]}),(0,a.jsx)("div",{className:"p-6 max-h-96 overflow-y-auto",children:0===p[m].length?(0,a.jsx)("div",{className:"text-center py-8 text-gray-500",children:"No documents uploaded for this category yet"}):(0,a.jsx)("div",{className:"space-y-4",children:p[m].map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center justify-between bg-gray-50 p-4 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center flex-grow cursor-pointer hover:bg-gray-100 p-2 rounded",onClick:()=>v(e.url),children:[(0,a.jsx)("div",{className:"bg-blue-100 p-3 rounded-full mr-4",children:(0,a.jsx)(n.t69,{className:"text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("h3",{className:"font-medium text-gray-800 mr-2",children:e.name}),(0,a.jsx)(n.EQc,{className:"text-gray-500 text-xs"})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Uploaded on ",e.date]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>w(e),className:"p-2 text-green-500 hover:text-green-700 hover:bg-green-50 rounded-full","aria-label":"Download document",title:"Download document",children:(0,a.jsx)(n.dIn,{})}),(0,a.jsx)("button",{onClick:()=>_(e.id,e),className:"p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full","aria-label":"Delete document",title:"Delete document",children:(0,a.jsx)(n.qbC,{})})]})]},e.id||"".concat(m,"-").concat(t)))})}),(0,a.jsxs)("div",{className:"border-t p-6",children:["semesterwise"===m&&(0,a.jsxs)("div",{className:"mb-4 grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Semester Number"}),(0,a.jsx)("input",{type:"number",min:"1",max:"8",ref:j,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"e.g., 1"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"CGPA"}),(0,a.jsx)("input",{type:"number",step:"0.01",min:"0",max:"10",ref:y,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"e.g., 8.5"})]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Supported formats: PDF, JPG, PNG (max 5MB)"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("input",{type:"file",accept:".pdf,.jpg,.jpeg,.png",className:"hidden",ref:f,onChange:b,disabled:u}),(0,a.jsx)("button",{onClick:()=>f.current.click(),className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center ".concat(u?"opacity-70 cursor-not-allowed":""),disabled:u,children:u?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.hW,{className:"mr-2 animate-spin"})," Uploading..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.HVe,{className:"mr-2"})," Upload Document"]})})]})]})]})]})}):null}var d=s(73983);let m={resume:{maxSize:5242880,allowedTypes:["application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],allowedExtensions:[".pdf",".doc",".docx"],displayName:"Resume"},profile_image:{maxSize:2097152,allowedTypes:["image/jpeg","image/png","image/gif","image/webp"],allowedExtensions:[".jpg",".jpeg",".png",".gif",".webp"],displayName:"Profile Image"},certificate:{maxSize:5242880,allowedTypes:["application/pdf","image/jpeg","image/png"],allowedExtensions:[".pdf",".jpg",".jpeg",".png"],displayName:"Certificate"},marksheet:{maxSize:5242880,allowedTypes:["application/pdf","image/jpeg","image/png"],allowedExtensions:[".pdf",".jpg",".jpeg",".png"],displayName:"Marksheet"},cover_letter:{maxSize:2097152,allowedTypes:["application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],allowedExtensions:[".pdf",".doc",".docx"],displayName:"Cover Letter"},generic_document:{maxSize:0xa00000,allowedTypes:["application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document","image/jpeg","image/png"],allowedExtensions:[".pdf",".doc",".docx",".jpg",".jpeg",".png"],displayName:"Document"}},x=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"generic_document",s=m[t],a=[],l=[];if(!e)return{isValid:!1,errors:["No file selected"],warnings:[],file:null};if(e.size>s.maxSize){let t=Math.round(s.maxSize/1048576),l=Math.round(e.size/1048576*10)/10;a.push("File size (".concat(l,"MB) exceeds maximum allowed size of ").concat(t,"MB"))}if(!s.allowedTypes.includes(e.type)){let e=s.allowedExtensions.join(", ");a.push("File type not supported. Allowed formats: ".concat(e))}let r="."+e.name.split(".").pop().toLowerCase();if(!s.allowedExtensions.includes(r)){let e=s.allowedExtensions.join(", ");a.push("File extension not allowed. Supported extensions: ".concat(e))}switch(t){case"resume":e.size<51200&&l.push("Resume file seems very small. Please ensure it contains adequate content."),e.name.length>100&&l.push("File name is very long. Consider using a shorter name.");break;case"profile_image":e.size<10240&&l.push("Image file seems very small. Please ensure it's a clear photo.");break;case"certificate":case"marksheet":e.size<102400&&l.push("Document file seems small. Please ensure it's clearly readable.")}return(e.name.includes("..")||e.name.includes("/")||e.name.includes("\\"))&&a.push("File name contains invalid characters"),e.name.length>255&&a.push("File name is too long (maximum 255 characters)"),{isValid:0===a.length,errors:a,warnings:l,file:e,size:e.size,sizeDisplay:u(e.size),type:e.type,name:e.name}},u=e=>{if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(1))+" "+["Bytes","KB","MB","GB"][t]};function h(){var e,t,m,u;let{handleApiError:h,showFileUploadError:p}=(0,d.hN)(),[g,f]=(0,l.useState)(!1),[j,y]=(0,l.useState)(!1),[N,b]=(0,l.useState)(null),[v,w]=(0,l.useState)(!0),[_,S]=(0,l.useState)(null),[k,C]=(0,l.useState)([]),[D,E]=(0,l.useState)(0),[R,L]=(0,l.useState)(null),[P,U]=(0,l.useState)({totalListings:0,eligibleJobs:0,loading:!0});(0,l.useEffect)(()=>{!async function(){try{w(!0);let e=await i.N.getProfile();b(e);let t=await i.N.getSemesterMarksheets();C(t),await M(),z(),w(!1)}catch(e){console.error("Error fetching profile:",e),h(e,"loading profile"),S("Failed to load profile data. Please try again later."),w(!1)}}()},[]);let M=async()=>{try{let e=await i.N.getResumes();if(E(e.length),e.length>0){let t=e.reduce((e,t)=>{let s=new Date(t.uploaded_at||t.created_at),a=new Date(e);return s>a?t.uploaded_at||t.created_at:e},e[0].uploaded_at||e[0].created_at);L(t)}}catch(e){console.error("Error fetching resumes:",e),(null==N?void 0:N.resume)&&(E(1),L(N.updated_at))}},z=async()=>{try{let e,{getCompanyStats:t,fetchCompanies:a}=await s.e(1318).then(s.bind(s,48937));try{let s=await t();e=s.data||s}catch(s){console.log("Could not fetch company stats, calculating from companies data");let t=await a();e={total:t.length,active_jobs:t.reduce((e,t)=>e+(t.totalActiveJobs||0),0)}}let l=I(e,N);U({totalListings:e.total||0,eligibleJobs:l,loading:!1})}catch(e){console.error("Error fetching company stats:",e),U({totalListings:0,eligibleJobs:0,loading:!1})}},I=(e,t)=>{if(!e||!t)return 0;let s=parseFloat(B());t.branch;let a=0;return e.forEach(e=>{let t=e.totalActiveJobs||0,l=0;a+=Math.floor(t*(s>=8.5?.9:s>=7.5?.75:s>=6.5?.5:.25))}),a},F=async e=>{let t=x(e,"resume");if(!t.isValid)return void p(t.errors);try{await i.N.uploadResume(e,e.name,!1),await M()}catch(e){console.error("Error uploading resume:",e),p(["Failed to upload resume","Please check the file format and size","Supported formats: PDF, DOC, DOCX (max 5MB)"])}},A=async e=>{try{if((null==e?void 0:e.id)&&(localStorage.removeItem("resume_".concat(e.id)),localStorage.removeItem("resume_count"),localStorage.removeItem("last_resume_update")),await M(),(null==e?void 0:e.url)===(null==N?void 0:N.resume)){let e={...N,resume:null};b(e)}}catch(e){console.error("Error handling resume deletion:",e)}},B=()=>(null==N?void 0:N.gpa)||"0.00",O=e=>e&&"string"==typeof e&&""!==e.trim()&&"null"!==e&&"undefined"!==e,W=e=>N&&N["semester".concat(e,"_cgpa")]||"-";return v?(0,a.jsxs)("div",{className:"flex items-center justify-center min-h-screen bg-gray-50",children:[(0,a.jsx)(n.hW,{className:"animate-spin text-blue-500 text-4xl mr-3"}),(0,a.jsx)("span",{className:"text-xl text-gray-700",children:"Loading profile..."})]}):_?(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen bg-gray-50",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-red-500 text-xl mb-4",children:_}),(0,a.jsx)("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600",children:"Retry"})]})}):(0,a.jsxs)("div",{className:"bg-gray-50 min-h-screen p-6",children:[(0,a.jsxs)("div",{className:"max-w-8xl mx-auto grid grid-cols-1 lg:grid-cols-12 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-3 bg-white rounded-lg p-5 shadow-sm h-fit content-card profile-container",children:[(0,a.jsx)("div",{className:"flex justify-between",children:v?(0,a.jsx)("div",{className:"w-50 h-50 bg-blue-100 flex items-center justify-center rounded-lg mb-4",children:(0,a.jsx)(n.hW,{className:"animate-spin text-blue-500 text-2xl"})}):(null==N?void 0:N.profile_image_url)?(0,a.jsx)("div",{className:"w-50 h-50 bg-blue-100 object-center text-center rounded-lg mb-4 relative mx-auto",children:(0,a.jsx)(r.default,{src:N.profile_image_url,alt:"".concat(N.first_name," ").concat(N.last_name),fill:!0,className:"rounded-lg object-cover"})}):(0,a.jsx)("div",{className:"w-50 h-50 bg-blue-500 text-white flex items-center justify-center rounded-lg mb-4 mx-auto",children:(0,a.jsx)("span",{className:"text-3xl font-bold",children:(null==N?void 0:N.initial)||((null==N?void 0:N.first_name)?N.first_name[0].toUpperCase():"S")})})}),(0,a.jsx)("h1",{className:"text-xl font-bold text-center mt-2 text-gray-800",children:(null==N?void 0:N.first_name)&&(null==N?void 0:N.last_name)?"".concat(N.first_name," ").concat(N.last_name):"-"}),(0,a.jsxs)("div",{className:"mt-4 space-y-3 text-md",children:[(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"Student ID"}),(0,a.jsxs)("p",{className:"font-medium text-gray-800",children:[": ",(null==N?void 0:N.student_id)||"-"]})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"Major"}),(0,a.jsxs)("p",{className:"font-medium text-gray-800",children:[": ",(null==N?void 0:N.branch)||"-"]})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"Passed Out"}),(0,a.jsxs)("p",{className:"font-medium text-gray-800",children:[": ",(null==N?void 0:N.passout_year)||"-"]})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"Gender"}),(0,a.jsxs)("p",{className:"font-medium text-gray-800",children:[": ",(null==N?void 0:N.gender)||"-"]})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"Birthday"}),(0,a.jsxs)("p",{className:"font-medium text-gray-800",children:[": ",(null==N?void 0:N.date_of_birth)||"-"]})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"Phone"}),(0,a.jsxs)("p",{className:"font-medium text-gray-800",children:[": ",(null==N?void 0:N.phone)||"-"]})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"Email"}),(0,a.jsxs)("p",{className:"font-medium text-gray-800",children:[": ",(null==N?void 0:N.contact_email)||(null==N||null==(e=N.user)?void 0:e.email)||"-"]})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"Campus"}),(0,a.jsxs)("p",{className:"font-medium text-gray-800",children:[": ",(null==N?void 0:N.college_name)||"-"]})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"Placement"}),(0,a.jsxs)("p",{className:"font-medium text-gray-800",children:[": ",(null==N?void 0:N.joining_year)&&(null==N?void 0:N.passout_year)?"".concat(N.joining_year,"-").concat(N.passout_year):"-"]})]})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-3 text-gray-800",children:"Skills"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:(()=>{var e;let t=(null==N?void 0:N.skills)||(null==(e=selectedStudent)?void 0:e.skills);return Array.isArray(t)&&t.length>0?t.map((e,t)=>(0,a.jsx)("span",{className:"bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm",children:e},t)):"string"==typeof t&&t.trim()?t.split(",").map((e,t)=>(0,a.jsx)("span",{className:"bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm",children:e.trim()},t)):(0,a.jsx)("p",{className:"text-gray-600",children:"No skills listed"})})()})]})]}),(0,a.jsxs)("div",{className:"lg:col-span-6 space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg p-5 shadow-sm content-card",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-6 text-gray-800",children:"Academic"}),(0,a.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"Semester Wise score"}),(0,a.jsxs)("div",{className:"bg-blue-50 inline-flex items-center px-3 py-1 rounded-full ml-3",children:[(0,a.jsx)("span",{className:"text-blue-600 font-medium",children:B()}),(0,a.jsx)("span",{className:"text-sm text-gray-500 ml-1",children:"CGPA"}),(0,a.jsx)("span",{className:"text-blue-600 ml-2",children:(t=B())&&"-"!==t?(9.5*parseFloat(t)).toFixed(2)+"%":"-"})]})]}),(0,a.jsx)("div",{className:"text-lg font-semibold text-gray-800",children:(m=null==N?void 0:N.joining_year,u=null==N?void 0:N.passout_year,m&&u?"".concat(m," - ").concat(u):"-")})]}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full border-collapse border border-gray-300",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"bg-gray-50",children:[(0,a.jsx)("th",{className:"border border-gray-300 px-4 py-3 text-sm font-semibold text-gray-700",children:"Sem"}),[1,2,3,4,5,6,7,8].map(e=>(0,a.jsx)("th",{className:"border border-gray-300 px-4 py-3 text-sm font-semibold text-gray-700",children:e},e))]})}),(0,a.jsx)("tbody",{children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"border border-gray-300 px-4 py-3 text-sm font-medium text-gray-700",children:"Cgpa"}),[1,2,3,4,5,6,7,8].map(e=>(0,a.jsx)("td",{className:"border border-gray-300 px-4 py-3 text-sm text-gray-700",children:W(e)},e))]})})]})})}),(0,a.jsx)("hr",{className:"my-6"}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"Class XII"}),(0,a.jsxs)("div",{className:"bg-blue-50 inline-flex items-center px-3 py-1 rounded-full ml-3",children:[(0,a.jsx)("span",{className:"text-blue-600 font-medium",children:(null==N?void 0:N.twelfth_cgpa)||"-"}),(0,a.jsx)("span",{className:"text-sm text-gray-500 ml-1",children:"CGPA"}),(0,a.jsx)("span",{className:"text-blue-600 ml-2",children:(null==N?void 0:N.twelfth_percentage)||"-"})]})]}),(0,a.jsx)("div",{className:"text-lg font-semibold text-gray-800",children:(null==N?void 0:N.twelfth_year_of_passing)?"".concat(parseInt(N.twelfth_year_of_passing)-2," - ").concat(N.twelfth_year_of_passing):"-"})]}),(0,a.jsx)("div",{className:"flex justify-between items-start mb-2",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-6 w-full",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 text-sm w-[120px]",children:"College :"}),(0,a.jsx)("p",{className:"text-gray-700 font-medium",children:(null==N?void 0:N.twelfth_school)||"-"})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 text-sm w-[120px]",children:"Board :"}),(0,a.jsx)("p",{className:"text-gray-700 font-medium",children:(null==N?void 0:N.twelfth_board)||"-"})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 text-sm w-[120px]",children:"Location :"}),(0,a.jsx)("p",{className:"text-gray-700 font-medium",children:(null==N?void 0:N.city)||"-"})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 text-sm w-[120px]",children:"Specialization :"}),(0,a.jsx)("p",{className:"text-gray-700 font-medium",children:(null==N?void 0:N.twelfth_specialization)||"-"})]})]})]})})]}),(0,a.jsx)("hr",{className:"my-6"}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"Class X"}),(0,a.jsxs)("div",{className:"bg-blue-50 inline-flex items-center px-3 py-1 rounded-full ml-3",children:[(0,a.jsx)("span",{className:"text-blue-600 font-medium",children:(null==N?void 0:N.tenth_cgpa)||"-"}),(0,a.jsx)("span",{className:"text-sm text-gray-500 ml-1",children:"CGPA"}),(0,a.jsx)("span",{className:"text-blue-600 ml-2",children:(null==N?void 0:N.tenth_percentage)||"-"})]})]}),(0,a.jsx)("div",{className:"text-lg font-semibold text-gray-800",children:(null==N?void 0:N.tenth_year_of_passing)?"".concat(parseInt(N.tenth_year_of_passing)-1," - ").concat(N.tenth_year_of_passing):"-"})]}),(0,a.jsx)("div",{className:"flex justify-between items-start mb-2",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-6 w-full",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 text-sm w-[120px]",children:"School :"}),(0,a.jsx)("p",{className:"text-gray-700 font-medium",children:(null==N?void 0:N.tenth_school)||"-"})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 text-sm w-[120px]",children:"Board :"}),(0,a.jsx)("p",{className:"text-gray-700 font-medium",children:(null==N?void 0:N.tenth_board)||"-"})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 text-sm w-[120px]",children:"Location :"}),(0,a.jsx)("p",{className:"text-gray-700 font-medium",children:(null==N?void 0:N.city)||"-"})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 text-sm w-[120px]",children:"Specialization :"}),(0,a.jsx)("p",{className:"text-gray-700 font-medium",children:"-"})]})]})]})})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-5 shadow-sm content-card",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4 text-gray-800",children:"Companies"}),P.loading?(0,a.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,a.jsx)(n.hW,{className:"animate-spin text-blue-500 text-xl mr-2"}),(0,a.jsx)("span",{className:"text-gray-600",children:"Loading company data..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("p",{className:"text-gray-500 text-sm",children:"Total Listings"}),(0,a.jsx)("p",{className:"text-lg font-semibold text-gray-700",children:P.totalListings})]})}),(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("p",{className:"text-gray-500 text-sm",children:"Eligible Jobs"}),(0,a.jsx)("p",{className:"text-lg font-semibold text-gray-700",children:P.eligibleJobs})]})})]})]})]}),(0,a.jsxs)("div",{className:"lg:col-span-3 space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg p-5 shadow-sm content-card",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4 text-gray-800",children:"My Files"}),(0,a.jsxs)("div",{className:"flex items-center p-2 mb-3 cursor-pointer hover:bg-gray-50 rounded-lg transition-colors",onClick:()=>f(!0),children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-3",children:(0,a.jsx)("div",{className:"text-blue-600",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"})})})}),(0,a.jsxs)("div",{className:"flex-grow",children:[(0,a.jsx)("h3",{className:"font-medium text-gray-700",children:"Resumes"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:D>0?"".concat(D," resume").concat(D>1?"s":""," uploaded")+(R?" • Last updated ".concat(new Date(R).toLocaleDateString()):""):"No resumes uploaded"})]}),(0,a.jsx)("div",{className:"bg-green-50 px-3 py-1 rounded-full",children:(0,a.jsx)("span",{className:"text-green-600 font-medium",children:D})})]}),(0,a.jsxs)("div",{className:"flex items-center p-2 cursor-pointer hover:bg-gray-50 rounded-lg transition-colors",onClick:()=>y(!0),children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-3",children:(0,a.jsx)("div",{className:"text-blue-600",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})})}),(0,a.jsxs)("div",{className:"flex-grow",children:[(0,a.jsx)("h3",{className:"font-medium text-gray-700",children:"Documents"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Academic certificates and marksheets"})]}),(0,a.jsx)("div",{className:"bg-green-50 px-3 py-1 rounded-full",children:(0,a.jsx)("span",{className:"text-green-600 font-medium",children:(()=>{let e=+!!O(null==N?void 0:N.tenth_certificate);return e+ +!!O(null==N?void 0:N.twelfth_certificate)+(k?k.filter(e=>e&&O(e.marksheet_url)).length:0)})()})})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-5 shadow-sm content-card",children:[(0,a.jsx)("div",{className:"flex justify-between items-center mb-4",children:(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-800",children:"CURRENT ADDRESS"})}),(0,a.jsxs)("div",{className:"space-y-3 text-sm",children:[(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"City"}),(0,a.jsxs)("p",{className:"font-medium text-gray-700",children:[": ",(null==N?void 0:N.city)||"-"]})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"District"}),(0,a.jsxs)("p",{className:"font-medium text-gray-700",children:[": ",(null==N?void 0:N.district)||"-"]})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"State"}),(0,a.jsxs)("p",{className:"font-medium text-gray-700",children:[": ",(null==N?void 0:N.state)||"-"]})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"Pin Code"}),(0,a.jsxs)("p",{className:"font-medium text-gray-700",children:[": ",(null==N?void 0:N.pincode)||"-"]})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"Country"}),(0,a.jsxs)("p",{className:"font-medium text-gray-700",children:[": ",(null==N?void 0:N.country)||"-"]})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"Address"}),(0,a.jsxs)("p",{className:"font-medium text-gray-700",children:[": ",(null==N?void 0:N.address)||"-"]})]})]})]})]})]}),(0,a.jsx)(c,{isOpen:g,onClose:()=>f(!1),resume:(null==N?void 0:N.resume_url)||(null==N?void 0:N.resume),onUpload:F,onDelete:A}),(0,a.jsx)(o,{isOpen:j,onClose:()=>y(!1),documents:{tenth:(null==N?void 0:N.tenth_certificate_url)||(null==N?void 0:N.tenth_certificate),twelfth:(null==N?void 0:N.twelfth_certificate_url)||(null==N?void 0:N.twelfth_certificate),semesterMarksheets:k},onUploadCertificate:i.N.uploadCertificate,onUploadMarksheet:i.N.uploadSemesterMarksheet,onDeleteCertificate:i.N.deleteCertificate,onDeleteMarksheet:i.N.deleteMarksheet})]})}},34577:(e,t,s)=>{Promise.resolve().then(s.bind(s,26155))},37719:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(23464);s(73983);let l=a.A.create({baseURL:"http://127.0.0.1:8000",headers:{"Content-Type":"application/json"}});l.interceptors.request.use(e=>{let t=localStorage.getItem("access_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),l.interceptors.response.use(e=>e,async e=>{var t;let s=e.config;if((null==(t=e.response)?void 0:t.status)===401&&!s._retry){s._retry=!0;try{let e=localStorage.getItem("refresh_token");if(e){let t=await a.A.post("http://127.0.0.1:8000/api/auth/token/refresh/",{refresh:e});return localStorage.setItem("access_token",t.data.access),s.headers.Authorization="Bearer ".concat(t.data.access),l(s)}}catch(e){console.error("Error refreshing token:",e),localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),window.location.href="/login"}}return Promise.reject(e)});let r=l}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,3464,485,3983,2338,8441,1684,7358],()=>t(34577)),_N_E=e.O()}]);