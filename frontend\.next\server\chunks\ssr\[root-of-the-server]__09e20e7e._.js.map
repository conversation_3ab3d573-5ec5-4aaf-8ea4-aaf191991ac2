{"version": 3, "sources": [], "sections": [{"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/api/errorHandler.js"], "sourcesContent": ["import { useNotification } from '../contexts/NotificationContext';\r\n\r\n// Error codes and their corresponding handlers\r\nexport const ERROR_PATTERNS = {\r\n  AUTHENTICATION: {\r\n    codes: [401],\r\n    keywords: ['unauthorized', 'authentication', 'token', 'login'],\r\n    handler: 'showAuthError'\r\n  },\r\n  SESSION_EXPIRED: {\r\n    codes: [401],\r\n    keywords: ['expired', 'invalid token', 'token expired'],\r\n    handler: 'showSessionExpiredModal'\r\n  },\r\n  PERMISSION_DENIED: {\r\n    codes: [403],\r\n    keywords: ['permission', 'forbidden', 'access denied'],\r\n    handler: 'showAuthError'\r\n  },\r\n  VALIDATION: {\r\n    codes: [400, 422],\r\n    keywords: ['validation', 'invalid', 'required'],\r\n    handler: 'showValidationError'\r\n  },\r\n  RESUME_REQUIRED: {\r\n    fields: ['resume'],\r\n    keywords: ['resume', 'must be uploaded', 'present in the student profile'],\r\n    handler: 'showMissingResumeModal'\r\n  },\r\n  PROFILE_INCOMPLETE: {\r\n    keywords: ['profile incomplete', 'missing profile', 'update profile'],\r\n    handler: 'showProfileIncompleteModal'\r\n  },\r\n  FILE_UPLOAD: {\r\n    keywords: ['file', 'upload', 'size', 'format', 'extension'],\r\n    handler: 'showFileUploadError'\r\n  },\r\n  NETWORK_ERROR: {\r\n    codes: ['NETWORK_ERROR', 'ECONNREFUSED', 'ERR_NETWORK'],\r\n    keywords: ['network', 'connection', 'timeout'],\r\n    handler: 'showNetworkError'\r\n  },\r\n  MAINTENANCE: {\r\n    codes: [503, 502],\r\n    keywords: ['maintenance', 'service unavailable', 'temporarily unavailable'],\r\n    handler: 'showMaintenanceModal'\r\n  }\r\n};\r\n\r\n// Smart error detection and handling\r\nexport const detectAndHandleError = (error, context = '', notificationHandlers) => {\r\n  const errorData = error?.response?.data || {};\r\n  const errorMessage = (errorData.detail || errorData.message || error.message || '').toLowerCase();\r\n  const statusCode = error?.response?.status;\r\n\r\n  // Check for specific error patterns\r\n  for (const [pattern, config] of Object.entries(ERROR_PATTERNS)) {\r\n    // Check status codes\r\n    if (config.codes && config.codes.includes(statusCode)) {\r\n      // Additional keyword check for more precision\r\n      if (config.keywords && !config.keywords.some(keyword => errorMessage.includes(keyword))) {\r\n        continue;\r\n      }\r\n      \r\n      return handleSpecificError(pattern, error, context, notificationHandlers);\r\n    }\r\n\r\n    // Check for field-specific errors (like resume)\r\n    if (config.fields && config.fields.some(field => errorData[field])) {\r\n      return handleSpecificError(pattern, error, context, notificationHandlers);\r\n    }\r\n\r\n    // Check keywords in error message\r\n    if (config.keywords && config.keywords.some(keyword => errorMessage.includes(keyword))) {\r\n      return handleSpecificError(pattern, error, context, notificationHandlers);\r\n    }\r\n  }\r\n\r\n  // Fallback to generic error handling\r\n  return handleGenericError(error, context, notificationHandlers);\r\n};\r\n\r\nconst handleSpecificError = (pattern, error, context, notificationHandlers) => {\r\n  const config = ERROR_PATTERNS[pattern];\r\n  const handlerName = config.handler;\r\n  \r\n  if (notificationHandlers[handlerName]) {\r\n    switch (handlerName) {\r\n      case 'showMissingResumeModal':\r\n        notificationHandlers.showMissingResumeModal();\r\n        break;\r\n      case 'showSessionExpiredModal':\r\n        notificationHandlers.showSessionExpiredModal();\r\n        break;\r\n      case 'showMaintenanceModal':\r\n        notificationHandlers.showMaintenanceModal();\r\n        break;\r\n      case 'showValidationError':\r\n        const errorData = error?.response?.data || {};\r\n        notificationHandlers.showValidationError(\r\n          `Validation Error ${context ? `in ${context}` : ''}`, \r\n          errorData\r\n        );\r\n        break;\r\n      case 'showAuthError':\r\n        const message = error?.response?.data?.detail || \r\n                       error?.response?.data?.message || \r\n                       `Authentication failed${context ? ` while ${context}` : ''}`;\r\n        notificationHandlers.showAuthError(message);\r\n        break;\r\n      case 'showFileUploadError':\r\n        notificationHandlers.showFileUploadError();\r\n        break;\r\n      case 'showNetworkError':\r\n        notificationHandlers.showNetworkError(error);\r\n        break;\r\n      case 'showProfileIncompleteModal':\r\n        notificationHandlers.showProfileIncompleteModal();\r\n        break;\r\n      default:\r\n        return handleGenericError(error, context, notificationHandlers);\r\n    }\r\n    return true; // Error was handled\r\n  }\r\n  \r\n  return false; // Error not handled\r\n};\r\n\r\nconst handleGenericError = (error, context, notificationHandlers) => {\r\n  if (notificationHandlers.handleApiError) {\r\n    notificationHandlers.handleApiError(error, context);\r\n    return true;\r\n  }\r\n  \r\n  // Ultimate fallback\r\n  console.error('Unhandled error:', error);\r\n  return false;\r\n};\r\n\r\n// Hook for easy error handling in components\r\nexport const useErrorHandler = () => {\r\n  const notificationHandlers = useNotification();\r\n  \r\n  const handleError = (error, context = '') => {\r\n    return detectAndHandleError(error, context, notificationHandlers);\r\n  };\r\n\r\n  return { handleError };\r\n};\r\n\r\n// Axios interceptor setup\r\nexport const setupErrorInterceptor = (axiosInstance, notificationHandlers) => {\r\n  axiosInstance.interceptors.response.use(\r\n    (response) => response,\r\n    (error) => {\r\n      // Automatically handle common errors\r\n      detectAndHandleError(error, 'API request', notificationHandlers);\r\n      return Promise.reject(error);\r\n    }\r\n  );\r\n};\r\n\r\nexport default {\r\n  detectAndHandleError,\r\n  useErrorHandler,\r\n  setupErrorInterceptor,\r\n  ERROR_PATTERNS\r\n}; "], "names": [], "mappings": ";;;;;;;AAAA;;AAGO,MAAM,iBAAiB;IAC5B,gBAAgB;QACd,OAAO;YAAC;SAAI;QACZ,UAAU;YAAC;YAAgB;YAAkB;YAAS;SAAQ;QAC9D,SAAS;IACX;IACA,iBAAiB;QACf,OAAO;YAAC;SAAI;QACZ,UAAU;YAAC;YAAW;YAAiB;SAAgB;QACvD,SAAS;IACX;IACA,mBAAmB;QACjB,OAAO;YAAC;SAAI;QACZ,UAAU;YAAC;YAAc;YAAa;SAAgB;QACtD,SAAS;IACX;IACA,YAAY;QACV,OAAO;YAAC;YAAK;SAAI;QACjB,UAAU;YAAC;YAAc;YAAW;SAAW;QAC/C,SAAS;IACX;IACA,iBAAiB;QACf,QAAQ;YAAC;SAAS;QAClB,UAAU;YAAC;YAAU;YAAoB;SAAiC;QAC1E,SAAS;IACX;IACA,oBAAoB;QAClB,UAAU;YAAC;YAAsB;YAAmB;SAAiB;QACrE,SAAS;IACX;IACA,aAAa;QACX,UAAU;YAAC;YAAQ;YAAU;YAAQ;YAAU;SAAY;QAC3D,SAAS;IACX;IACA,eAAe;QACb,OAAO;YAAC;YAAiB;YAAgB;SAAc;QACvD,UAAU;YAAC;YAAW;YAAc;SAAU;QAC9C,SAAS;IACX;IACA,aAAa;QACX,OAAO;YAAC;YAAK;SAAI;QACjB,UAAU;YAAC;YAAe;YAAuB;SAA0B;QAC3E,SAAS;IACX;AACF;AAGO,MAAM,uBAAuB,CAAC,OAAO,UAAU,EAAE,EAAE;IACxD,MAAM,YAAY,OAAO,UAAU,QAAQ,CAAC;IAC5C,MAAM,eAAe,CAAC,UAAU,MAAM,IAAI,UAAU,OAAO,IAAI,MAAM,OAAO,IAAI,EAAE,EAAE,WAAW;IAC/F,MAAM,aAAa,OAAO,UAAU;IAEpC,oCAAoC;IACpC,KAAK,MAAM,CAAC,SAAS,OAAO,IAAI,OAAO,OAAO,CAAC,gBAAiB;QAC9D,qBAAqB;QACrB,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,CAAC,QAAQ,CAAC,aAAa;YACrD,8CAA8C;YAC9C,IAAI,OAAO,QAAQ,IAAI,CAAC,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAA,UAAW,aAAa,QAAQ,CAAC,WAAW;gBACvF;YACF;YAEA,OAAO,oBAAoB,SAAS,OAAO,SAAS;QACtD;QAEA,gDAAgD;QAChD,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,CAAA,QAAS,SAAS,CAAC,MAAM,GAAG;YAClE,OAAO,oBAAoB,SAAS,OAAO,SAAS;QACtD;QAEA,kCAAkC;QAClC,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAA,UAAW,aAAa,QAAQ,CAAC,WAAW;YACtF,OAAO,oBAAoB,SAAS,OAAO,SAAS;QACtD;IACF;IAEA,qCAAqC;IACrC,OAAO,mBAAmB,OAAO,SAAS;AAC5C;AAEA,MAAM,sBAAsB,CAAC,SAAS,OAAO,SAAS;IACpD,MAAM,SAAS,cAAc,CAAC,QAAQ;IACtC,MAAM,cAAc,OAAO,OAAO;IAElC,IAAI,oBAAoB,CAAC,YAAY,EAAE;QACrC,OAAQ;YACN,KAAK;gBACH,qBAAqB,sBAAsB;gBAC3C;YACF,KAAK;gBACH,qBAAqB,uBAAuB;gBAC5C;YACF,KAAK;gBACH,qBAAqB,oBAAoB;gBACzC;YACF,KAAK;gBACH,MAAM,YAAY,OAAO,UAAU,QAAQ,CAAC;gBAC5C,qBAAqB,mBAAmB,CACtC,CAAC,iBAAiB,EAAE,UAAU,CAAC,GAAG,EAAE,SAAS,GAAG,IAAI,EACpD;gBAEF;YACF,KAAK;gBACH,MAAM,UAAU,OAAO,UAAU,MAAM,UACxB,OAAO,UAAU,MAAM,WACvB,CAAC,qBAAqB,EAAE,UAAU,CAAC,OAAO,EAAE,SAAS,GAAG,IAAI;gBAC3E,qBAAqB,aAAa,CAAC;gBACnC;YACF,KAAK;gBACH,qBAAqB,mBAAmB;gBACxC;YACF,KAAK;gBACH,qBAAqB,gBAAgB,CAAC;gBACtC;YACF,KAAK;gBACH,qBAAqB,0BAA0B;gBAC/C;YACF;gBACE,OAAO,mBAAmB,OAAO,SAAS;QAC9C;QACA,OAAO,MAAM,oBAAoB;IACnC;IAEA,OAAO,OAAO,oBAAoB;AACpC;AAEA,MAAM,qBAAqB,CAAC,OAAO,SAAS;IAC1C,IAAI,qBAAqB,cAAc,EAAE;QACvC,qBAAqB,cAAc,CAAC,OAAO;QAC3C,OAAO;IACT;IAEA,oBAAoB;IACpB,QAAQ,KAAK,CAAC,oBAAoB;IAClC,OAAO;AACT;AAGO,MAAM,kBAAkB;IAC7B,MAAM,uBAAuB,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAE3C,MAAM,cAAc,CAAC,OAAO,UAAU,EAAE;QACtC,OAAO,qBAAqB,OAAO,SAAS;IAC9C;IAEA,OAAO;QAAE;IAAY;AACvB;AAGO,MAAM,wBAAwB,CAAC,eAAe;IACnD,cAAc,YAAY,CAAC,QAAQ,CAAC,GAAG,CACrC,CAAC,WAAa,UACd,CAAC;QACC,qCAAqC;QACrC,qBAAqB,OAAO,eAAe;QAC3C,OAAO,QAAQ,MAAM,CAAC;IACxB;AAEJ;uCAEe;IACb;IACA;IACA;IACA;AACF", "debugId": null}}, {"offset": {"line": 323, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/api/client.js"], "sourcesContent": ["import axios from 'axios';\r\nimport { setupErrorInterceptor } from './errorHandler';\r\n\r\nconst client = axios.create({\r\n  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000',\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n});\r\n\r\n// Add a request interceptor to include the auth token\r\nclient.interceptors.request.use(\r\n  (config) => {\r\n    // Get the token from localStorage\r\n    const token = localStorage.getItem('access_token');\r\n    \r\n    // If token exists, add it to the Authorization header\r\n    if (token) {\r\n      config.headers['Authorization'] = `Bearer ${token}`;\r\n    }\r\n    \r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Add a response interceptor to handle 401 errors (token expired)\r\nclient.interceptors.response.use(\r\n  (response) => response,\r\n  async (error) => {\r\n    const originalRequest = error.config;\r\n    \r\n    // If error is 401 and we haven't tried to refresh the token yet\r\n    if (error.response?.status === 401 && !originalRequest._retry) {\r\n      originalRequest._retry = true;\r\n      \r\n      try {\r\n        // Get refresh token\r\n        const refreshToken = localStorage.getItem('refresh_token');\r\n        \r\n        if (refreshToken) {\r\n          // Try to get a new token\r\n          const response = await axios.post('http://127.0.0.1:8000/api/auth/token/refresh/', {\r\n            refresh: refreshToken\r\n          });\r\n          \r\n          // Store the new tokens\r\n          localStorage.setItem('access_token', response.data.access);\r\n          \r\n          // Update the Authorization header\r\n          originalRequest.headers['Authorization'] = `Bearer ${response.data.access}`;\r\n          \r\n          // Retry the original request\r\n          return client(originalRequest);\r\n        }\r\n      } catch (refreshError) {\r\n        console.error('Error refreshing token:', refreshError);\r\n        \r\n        // If token refresh fails, redirect to login\r\n        if (typeof window !== 'undefined') {\r\n          // Clear tokens\r\n          localStorage.removeItem('access_token');\r\n          localStorage.removeItem('refresh_token');\r\n          \r\n          // Redirect to login page\r\n          window.location.href = '/login';\r\n        }\r\n      }\r\n    }\r\n    \r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default client;\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,SAAS,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC1B,SAAS,6DAAmC;IAC5C,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,sDAAsD;AACtD,OAAO,YAAY,CAAC,OAAO,CAAC,GAAG,CAC7B,CAAC;IACC,kCAAkC;IAClC,MAAM,QAAQ,aAAa,OAAO,CAAC;IAEnC,sDAAsD;IACtD,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;IACrD;IAEA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,kEAAkE;AAClE,OAAO,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC9B,CAAC,WAAa,UACd,OAAO;IACL,MAAM,kBAAkB,MAAM,MAAM;IAEpC,gEAAgE;IAChE,IAAI,MAAM,QAAQ,EAAE,WAAW,OAAO,CAAC,gBAAgB,MAAM,EAAE;QAC7D,gBAAgB,MAAM,GAAG;QAEzB,IAAI;YACF,oBAAoB;YACpB,MAAM,eAAe,aAAa,OAAO,CAAC;YAE1C,IAAI,cAAc;gBAChB,yBAAyB;gBACzB,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,iDAAiD;oBACjF,SAAS;gBACX;gBAEA,uBAAuB;gBACvB,aAAa,OAAO,CAAC,gBAAgB,SAAS,IAAI,CAAC,MAAM;gBAEzD,kCAAkC;gBAClC,gBAAgB,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,SAAS,IAAI,CAAC,MAAM,EAAE;gBAE3E,6BAA6B;gBAC7B,OAAO,OAAO;YAChB;QACF,EAAE,OAAO,cAAc;YACrB,QAAQ,KAAK,CAAC,2BAA2B;YAEzC,4CAA4C;YAC5C,uCAAmC;;YAOnC;QACF;IACF;IAEA,OAAO,QAAQ,MAAM,CAAC;AACxB;uCAGa", "debugId": null}}, {"offset": {"line": 386, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/api/jobs.js"], "sourcesContent": ["import client from './client';\r\n\r\n// List all jobs with pagination and filtering\r\nexport function listJobs(params = {}) {\r\n  const queryParams = new URLSearchParams();\r\n  \r\n  // Add pagination parameters\r\n  if (params.page) queryParams.append('page', params.page);\r\n  if (params.per_page) queryParams.append('per_page', params.per_page);\r\n  \r\n  // Add filtering parameters\r\n  if (params.job_type && params.job_type !== 'ALL') queryParams.append('job_type', params.job_type);\r\n  if (params.location && params.location !== 'ALL') queryParams.append('location', params.location);\r\n  if (params.salary_min) queryParams.append('salary_min', params.salary_min);\r\n  if (params.search) queryParams.append('search', params.search);\r\n  \r\n  const queryString = queryParams.toString();\r\n  const url = `/api/v1/college/default-college/jobs/${queryString ? `?${queryString}` : ''}`;\r\n  \r\n  return client.get(url);\r\n}\r\n\r\n// Apply to a job\r\nexport function applyToJob(job, coverLetter, additionalFields = {}) {\r\n  // Check if any additional fields contain files\r\n  const hasFiles = Object.values(additionalFields).some(value => value instanceof File);\r\n\r\n  if (hasFiles) {\r\n    // Use FormData for file uploads\r\n    const formData = new FormData();\r\n    formData.append('cover_letter', coverLetter);\r\n\r\n    // Handle additional fields with files\r\n    Object.entries(additionalFields).forEach(([key, value]) => {\r\n      if (value instanceof File) {\r\n        formData.append(key, value);\r\n      } else {\r\n        formData.append(key, JSON.stringify(value));\r\n      }\r\n    });\r\n\r\n    return client.post(`/api/v1/college/default-college/jobs/${job}/apply/`, formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n  } else {\r\n    // Use JSON for non-file submissions\r\n    return client.post(`/api/v1/college/default-college/jobs/${job}/apply/`, {\r\n      cover_letter: coverLetter,\r\n      additional_field_responses: additionalFields\r\n    });\r\n  }\r\n}\r\n\r\n// Get job details by ID\r\nexport function getJobById(jobId) {\r\n  return client.get(`/api/v1/college/default-college/jobs/${jobId}/`);\r\n}\r\n\r\n// List jobs the current student has applied to\r\nexport function listAppliedJobs() {\r\n  return client.get('/api/v1/college/default-college/jobs/applied/');\r\n}\r\n\r\n// Admin API functions for managing jobs\r\n\r\n// Note: Company management functions moved to /api/companies.js to avoid conflicts\r\n\r\n// Create a new job posting\r\nexport function createJob(jobData) {\r\n  return client.post('/api/v1/college/default-college/jobs/create/', jobData);\r\n}\r\n\r\n// Update job posting\r\nexport function updateJob(jobId, jobData) {\r\n  return client.put(`/api/v1/college/default-college/jobs/${jobId}/`, jobData);\r\n}\r\n\r\n// Delete job posting\r\nexport function deleteJob(jobId) {\r\n  return client.delete(`/api/v1/college/default-college/jobs/${jobId}/`);\r\n}\r\n\r\n// Get job applications for admin\r\nexport function getJobApplications(jobId) {\r\n  return client.get(`/api/v1/college/default-college/jobs/${jobId}/applications/`);\r\n}\r\n\r\n// Get all applications for admin dashboard\r\nexport function getAllApplications() {\r\n  return client.get('/api/v1/college/default-college/applications/');\r\n}\r\n\r\n// Admin-specific job listing (shows all jobs including unpublished)\r\nexport function listJobsAdmin(params = {}) {\r\n  const queryParams = new URLSearchParams();\r\n  \r\n  // Add pagination parameters\r\n  if (params.page) queryParams.append('page', params.page);\r\n  if (params.per_page) queryParams.append('per_page', params.per_page);\r\n  \r\n  // Add filtering parameters\r\n  if (params.search) queryParams.append('search', params.search);\r\n  if (params.type && params.type !== 'All') queryParams.append('job_type', params.type);\r\n  if (params.minCTC) queryParams.append('salary_min', params.minCTC);\r\n  if (params.maxCTC) queryParams.append('salary_max', params.maxCTC);\r\n  if (params.minStipend) queryParams.append('stipend_min', params.minStipend);\r\n  if (params.maxStipend) queryParams.append('stipend_max', params.maxStipend);\r\n  if (params.location) queryParams.append('location', params.location);\r\n  if (params.is_published !== undefined) queryParams.append('is_published', params.is_published);\r\n  \r\n  // Add company filtering\r\n  if (params.company_id) queryParams.append('company_id', params.company_id);\r\n  if (params.company_name) queryParams.append('company_name', params.company_name);\r\n  \r\n  const queryString = queryParams.toString();\r\n  const url = `/api/v1/college/default-college/jobs/admin/${queryString ? `?${queryString}` : ''}`;\r\n  \r\n  console.log('🌐 listJobsAdmin calling URL:', url, 'with params:', params);\r\n  \r\n  return client.get(url).then(response => {\r\n    console.log('🌐 listJobsAdmin response:', {\r\n      status: response.status,\r\n      totalJobs: response.data?.pagination?.total_count || 0,\r\n      currentPage: response.data?.pagination?.current_page || 1,\r\n      totalPages: response.data?.pagination?.total_pages || 1\r\n    });\r\n    return response;\r\n  }).catch(error => {\r\n    console.error('🌐 listJobsAdmin error:', error);\r\n    console.error('🌐 listJobsAdmin error response:', error.response?.data);\r\n    throw error;\r\n  });\r\n}\r\n\r\n// Toggle job publish status\r\nexport function toggleJobPublish(jobId) {\r\n  return client.patch(`/api/v1/jobs/${jobId}/toggle-publish/`);\r\n}\r\n\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;AAGO,SAAS,SAAS,SAAS,CAAC,CAAC;IAClC,MAAM,cAAc,IAAI;IAExB,4BAA4B;IAC5B,IAAI,OAAO,IAAI,EAAE,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI;IACvD,IAAI,OAAO,QAAQ,EAAE,YAAY,MAAM,CAAC,YAAY,OAAO,QAAQ;IAEnE,2BAA2B;IAC3B,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,KAAK,OAAO,YAAY,MAAM,CAAC,YAAY,OAAO,QAAQ;IAChG,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,KAAK,OAAO,YAAY,MAAM,CAAC,YAAY,OAAO,QAAQ;IAChG,IAAI,OAAO,UAAU,EAAE,YAAY,MAAM,CAAC,cAAc,OAAO,UAAU;IACzE,IAAI,OAAO,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;IAE7D,MAAM,cAAc,YAAY,QAAQ;IACxC,MAAM,MAAM,CAAC,qCAAqC,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;IAE1F,OAAO,oHAAA,CAAA,UAAM,CAAC,GAAG,CAAC;AACpB;AAGO,SAAS,WAAW,GAAG,EAAE,WAAW,EAAE,mBAAmB,CAAC,CAAC;IAChE,+CAA+C;IAC/C,MAAM,WAAW,OAAO,MAAM,CAAC,kBAAkB,IAAI,CAAC,CAAA,QAAS,iBAAiB;IAEhF,IAAI,UAAU;QACZ,gCAAgC;QAChC,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,gBAAgB;QAEhC,sCAAsC;QACtC,OAAO,OAAO,CAAC,kBAAkB,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YACpD,IAAI,iBAAiB,MAAM;gBACzB,SAAS,MAAM,CAAC,KAAK;YACvB,OAAO;gBACL,SAAS,MAAM,CAAC,KAAK,KAAK,SAAS,CAAC;YACtC;QACF;QAEA,OAAO,oHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,CAAC,qCAAqC,EAAE,IAAI,OAAO,CAAC,EAAE,UAAU;YACjF,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF,OAAO;QACL,oCAAoC;QACpC,OAAO,oHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,CAAC,qCAAqC,EAAE,IAAI,OAAO,CAAC,EAAE;YACvE,cAAc;YACd,4BAA4B;QAC9B;IACF;AACF;AAGO,SAAS,WAAW,KAAK;IAC9B,OAAO,oHAAA,CAAA,UAAM,CAAC,GAAG,CAAC,CAAC,qCAAqC,EAAE,MAAM,CAAC,CAAC;AACpE;AAGO,SAAS;IACd,OAAO,oHAAA,CAAA,UAAM,CAAC,GAAG,CAAC;AACpB;AAOO,SAAS,UAAU,OAAO;IAC/B,OAAO,oHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,gDAAgD;AACrE;AAGO,SAAS,UAAU,KAAK,EAAE,OAAO;IACtC,OAAO,oHAAA,CAAA,UAAM,CAAC,GAAG,CAAC,CAAC,qCAAqC,EAAE,MAAM,CAAC,CAAC,EAAE;AACtE;AAGO,SAAS,UAAU,KAAK;IAC7B,OAAO,oHAAA,CAAA,UAAM,CAAC,MAAM,CAAC,CAAC,qCAAqC,EAAE,MAAM,CAAC,CAAC;AACvE;AAGO,SAAS,mBAAmB,KAAK;IACtC,OAAO,oHAAA,CAAA,UAAM,CAAC,GAAG,CAAC,CAAC,qCAAqC,EAAE,MAAM,cAAc,CAAC;AACjF;AAGO,SAAS;IACd,OAAO,oHAAA,CAAA,UAAM,CAAC,GAAG,CAAC;AACpB;AAGO,SAAS,cAAc,SAAS,CAAC,CAAC;IACvC,MAAM,cAAc,IAAI;IAExB,4BAA4B;IAC5B,IAAI,OAAO,IAAI,EAAE,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI;IACvD,IAAI,OAAO,QAAQ,EAAE,YAAY,MAAM,CAAC,YAAY,OAAO,QAAQ;IAEnE,2BAA2B;IAC3B,IAAI,OAAO,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;IAC7D,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,KAAK,OAAO,YAAY,MAAM,CAAC,YAAY,OAAO,IAAI;IACpF,IAAI,OAAO,MAAM,EAAE,YAAY,MAAM,CAAC,cAAc,OAAO,MAAM;IACjE,IAAI,OAAO,MAAM,EAAE,YAAY,MAAM,CAAC,cAAc,OAAO,MAAM;IACjE,IAAI,OAAO,UAAU,EAAE,YAAY,MAAM,CAAC,eAAe,OAAO,UAAU;IAC1E,IAAI,OAAO,UAAU,EAAE,YAAY,MAAM,CAAC,eAAe,OAAO,UAAU;IAC1E,IAAI,OAAO,QAAQ,EAAE,YAAY,MAAM,CAAC,YAAY,OAAO,QAAQ;IACnE,IAAI,OAAO,YAAY,KAAK,WAAW,YAAY,MAAM,CAAC,gBAAgB,OAAO,YAAY;IAE7F,wBAAwB;IACxB,IAAI,OAAO,UAAU,EAAE,YAAY,MAAM,CAAC,cAAc,OAAO,UAAU;IACzE,IAAI,OAAO,YAAY,EAAE,YAAY,MAAM,CAAC,gBAAgB,OAAO,YAAY;IAE/E,MAAM,cAAc,YAAY,QAAQ;IACxC,MAAM,MAAM,CAAC,2CAA2C,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;IAEhG,QAAQ,GAAG,CAAC,iCAAiC,KAAK,gBAAgB;IAElE,OAAO,oHAAA,CAAA,UAAM,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,CAAA;QAC1B,QAAQ,GAAG,CAAC,8BAA8B;YACxC,QAAQ,SAAS,MAAM;YACvB,WAAW,SAAS,IAAI,EAAE,YAAY,eAAe;YACrD,aAAa,SAAS,IAAI,EAAE,YAAY,gBAAgB;YACxD,YAAY,SAAS,IAAI,EAAE,YAAY,eAAe;QACxD;QACA,OAAO;IACT,GAAG,KAAK,CAAC,CAAA;QACP,QAAQ,KAAK,CAAC,2BAA2B;QACzC,QAAQ,KAAK,CAAC,oCAAoC,MAAM,QAAQ,EAAE;QAClE,MAAM;IACR;AACF;AAGO,SAAS,iBAAiB,KAAK;IACpC,OAAO,oHAAA,CAAA,UAAM,CAAC,KAAK,CAAC,CAAC,aAAa,EAAE,MAAM,gBAAgB,CAAC;AAC7D", "debugId": null}}, {"offset": {"line": 507, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/lib/utils.js"], "sourcesContent": ["import { clsx } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\n// Utility function to format job descriptions with proper line breaks and formatting\r\nexport function formatJobDescription(description) {\r\n  if (!description) return \"No description provided.\";\r\n  \r\n  return description\r\n    .replace(/\\n/g, '<br />') // Convert newlines to HTML breaks\r\n    .replace(/•/g, '•') // Ensure bullet points are preserved\r\n    .replace(/\\*\\s/g, '• ') // Convert asterisk bullets to bullet symbols\r\n    .replace(/-\\s/g, '• ') // Convert dash bullets to bullet symbols\r\n    .trim();\r\n}\r\n\r\n// Component for rendering formatted job descriptions\r\nexport function FormattedJobDescription({ description, className = \"\" }) {\r\n  const formattedDescription = formatJobDescription(description);\r\n  \r\n  return (\r\n    <div \r\n      className={`text-gray-700 leading-relaxed ${className}`}\r\n      dangerouslySetInnerHTML={{ __html: formattedDescription }}\r\n    />\r\n  );\r\n}\r\n\r\n// Utility function to standardize field names in API responses\r\nexport function standardizeFieldNames(data, entityType) {\r\n  if (!data) return data;\r\n  \r\n  // Define standard field name mappings for different entity types\r\n  const fieldMappings = {\r\n    company: {\r\n      totalActiveJobs: 'total_active_jobs',\r\n      totalApplicants: 'total_applicants',\r\n      totalHired: 'total_hired',\r\n      awaitedApproval: 'pending_approval',\r\n      companyName: 'name',\r\n      companySize: 'size',\r\n      companyIndustry: 'industry',\r\n      companyLocation: 'location'\r\n    },\r\n    student: {\r\n      firstName: 'first_name',\r\n      lastName: 'last_name',\r\n      contactEmail: 'email',\r\n      studentId: 'student_id',\r\n      joiningYear: 'joining_year',\r\n      passoutYear: 'passout_year',\r\n      tenthCertificate: 'tenth_certificate',\r\n      twelfthCertificate: 'twelfth_certificate'\r\n    },\r\n    job: {\r\n      jobTitle: 'title',\r\n      jobType: 'job_type',\r\n      jobLocation: 'location',\r\n      salaryMin: 'salary_min',\r\n      salaryMax: 'salary_max',\r\n      requiredSkills: 'required_skills',\r\n      applicationDeadline: 'application_deadline',\r\n      isActive: 'is_active',\r\n      companyName: 'company_name'\r\n    }\r\n  };\r\n  \r\n  // Return original data if entity type is not supported\r\n  if (!fieldMappings[entityType]) return data;\r\n  \r\n  // If it's an array, standardize each item\r\n  if (Array.isArray(data)) {\r\n    return data.map(item => standardizeFieldNames(item, entityType));\r\n  }\r\n  \r\n  // For single objects, standardize fields\r\n  const standardized = { ...data };\r\n  const mapping = fieldMappings[entityType];\r\n  \r\n  // Apply field name standardization\r\n  Object.keys(mapping).forEach(nonStandardField => {\r\n    const standardField = mapping[nonStandardField];\r\n    \r\n    // If non-standard field exists in the data, copy it to the standard field\r\n    if (standardized[nonStandardField] !== undefined && standardized[standardField] === undefined) {\r\n      standardized[standardField] = standardized[nonStandardField];\r\n      delete standardized[nonStandardField];\r\n    }\r\n  });\r\n  \r\n  return standardized;\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;;AAEO,SAAS,GAAG,GAAG,MAAM;IAC1B,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,qBAAqB,WAAW;IAC9C,IAAI,CAAC,aAAa,OAAO;IAEzB,OAAO,YACJ,OAAO,CAAC,OAAO,UAAU,kCAAkC;KAC3D,OAAO,CAAC,MAAM,KAAK,qCAAqC;KACxD,OAAO,CAAC,SAAS,MAAM,6CAA6C;KACpE,OAAO,CAAC,QAAQ,MAAM,yCAAyC;KAC/D,IAAI;AACT;AAGO,SAAS,wBAAwB,EAAE,WAAW,EAAE,YAAY,EAAE,EAAE;IACrE,MAAM,uBAAuB,qBAAqB;IAElD,qBACE,8OAAC;QACC,WAAW,CAAC,8BAA8B,EAAE,WAAW;QACvD,yBAAyB;YAAE,QAAQ;QAAqB;;;;;;AAG9D;AAGO,SAAS,sBAAsB,IAAI,EAAE,UAAU;IACpD,IAAI,CAAC,MAAM,OAAO;IAElB,iEAAiE;IACjE,MAAM,gBAAgB;QACpB,SAAS;YACP,iBAAiB;YACjB,iBAAiB;YACjB,YAAY;YACZ,iBAAiB;YACjB,aAAa;YACb,aAAa;YACb,iBAAiB;YACjB,iBAAiB;QACnB;QACA,SAAS;YACP,WAAW;YACX,UAAU;YACV,cAAc;YACd,WAAW;YACX,aAAa;YACb,aAAa;YACb,kBAAkB;YAClB,oBAAoB;QACtB;QACA,KAAK;YACH,UAAU;YACV,SAAS;YACT,aAAa;YACb,WAAW;YACX,WAAW;YACX,gBAAgB;YAChB,qBAAqB;YACrB,UAAU;YACV,aAAa;QACf;IACF;IAEA,uDAAuD;IACvD,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,OAAO;IAEvC,0CAA0C;IAC1C,IAAI,MAAM,OAAO,CAAC,OAAO;QACvB,OAAO,KAAK,GAAG,CAAC,CAAA,OAAQ,sBAAsB,MAAM;IACtD;IAEA,yCAAyC;IACzC,MAAM,eAAe;QAAE,GAAG,IAAI;IAAC;IAC/B,MAAM,UAAU,aAAa,CAAC,WAAW;IAEzC,mCAAmC;IACnC,OAAO,IAAI,CAAC,SAAS,OAAO,CAAC,CAAA;QAC3B,MAAM,gBAAgB,OAAO,CAAC,iBAAiB;QAE/C,0EAA0E;QAC1E,IAAI,YAAY,CAAC,iBAAiB,KAAK,aAAa,YAAY,CAAC,cAAc,KAAK,WAAW;YAC7F,YAAY,CAAC,cAAc,GAAG,YAAY,CAAC,iBAAiB;YAC5D,OAAO,YAAY,CAAC,iBAAiB;QACvC;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 607, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/api/auth.js"], "sourcesContent": ["import client from './client';\r\n\r\n// Register new student\r\nexport function signup(data) {\r\n  return client.post('/api/auth/register/student/', data);\r\n}\r\n\r\n// Login and get tokens\r\nexport function login(data) {\r\n  return client.post('/api/auth/login/', data);\r\n}\r\n\r\n// Upload Resume\r\nexport function uploadResume(file, accessToken) {\r\n  const formData = new FormData();\r\n  formData.append('resume', file);\r\n\r\n  return client.patch('/api/auth/profile/', formData, {\r\n    headers: {\r\n      'Authorization': `Bearer ${accessToken}`,\r\n      'Content-Type': 'multipart/form-data',\r\n    }\r\n  });\r\n}\r\n\r\nexport const getAuthToken = () => {\r\n  if (typeof window !== 'undefined') {\r\n    return localStorage.getItem('access_token');\r\n  }\r\n  return null;\r\n};\r\n\r\nexport const setAuthToken = (token) => {\r\n  if (typeof window !== 'undefined') {\r\n    localStorage.setItem('access_token', token);\r\n  }\r\n};\r\n\r\nexport const removeAuthToken = () => {\r\n  if (typeof window !== 'undefined') {\r\n    localStorage.removeItem('access_token');\r\n    localStorage.removeItem('refresh_token');\r\n  }\r\n};\r\n\r\nexport const getRefreshToken = () => {\r\n  if (typeof window !== 'undefined') {\r\n    return localStorage.getItem('refresh_token');\r\n  }\r\n  return null;\r\n};\r\n\r\nexport const setRefreshToken = (token) => {\r\n  if (typeof window !== 'undefined') {\r\n    localStorage.setItem('refresh_token', token);\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAGO,SAAS,OAAO,IAAI;IACzB,OAAO,oHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,+BAA+B;AACpD;AAGO,SAAS,MAAM,IAAI;IACxB,OAAO,oHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,oBAAoB;AACzC;AAGO,SAAS,aAAa,IAAI,EAAE,WAAW;IAC5C,MAAM,WAAW,IAAI;IACrB,SAAS,MAAM,CAAC,UAAU;IAE1B,OAAO,oHAAA,CAAA,UAAM,CAAC,KAAK,CAAC,sBAAsB,UAAU;QAClD,SAAS;YACP,iBAAiB,CAAC,OAAO,EAAE,aAAa;YACxC,gBAAgB;QAClB;IACF;AACF;AAEO,MAAM,eAAe;IAC1B,uCAAmC;;IAEnC;IACA,OAAO;AACT;AAEO,MAAM,eAAe,CAAC;IAC3B,uCAAmC;;IAEnC;AACF;AAEO,MAAM,kBAAkB;IAC7B,uCAAmC;;IAGnC;AACF;AAEO,MAAM,kBAAkB;IAC7B,uCAAmC;;IAEnC;IACA,OAAO;AACT;AAEO,MAAM,kBAAkB,CAAC;IAC9B,uCAAmC;;IAEnC;AACF", "debugId": null}}, {"offset": {"line": 668, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/api/students.js"], "sourcesContent": ["import axios from 'axios';\r\nimport { getAuthToken } from './auth';\r\n\r\n// Set the base URL for all API requests\r\nconst API_BASE_URL = 'http://localhost:8000';\r\n\r\nconst api = axios.create({\r\n  baseURL: API_BASE_URL,\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n});\r\n\r\n// Add request interceptor to include auth token\r\napi.interceptors.request.use(\r\n  (config) => {\r\n    const token = getAuthToken();\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Add response interceptor for error handling\r\napi.interceptors.response.use(\r\n  (response) => response,\r\n  (error) => {\r\n    if (error.response?.status === 401) {\r\n      // Token expired or invalid\r\n      localStorage.removeItem('access_token');\r\n      localStorage.removeItem('refresh_token');\r\n      window.location.href = '/login';\r\n    }\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport const studentsAPI = {\r\n  // Get all students\r\n  getStudents: async (params = {}) => {\r\n    const response = await api.get('/api/accounts/students/', { params });\r\n    return response.data;\r\n  },\r\n\r\n  // Get students with statistics\r\n  getStudentsWithStats: async (params = {}) => {\r\n    try {\r\n      // First try to get students with built-in statistics\r\n      const response = await api.get('/api/accounts/students/stats/', { params });\r\n      return response.data;\r\n    } catch (error) {\r\n      // Fallback to regular students endpoint\r\n      console.log('Stats endpoint not available, using regular endpoint');\r\n      const response = await api.get('/api/accounts/students/', { params });\r\n      \r\n      // Calculate basic statistics from the response\r\n      const students = response.data.data || response.data;\r\n      if (Array.isArray(students)) {\r\n        const stats = calculateStudentStats(students, params);\r\n        return {\r\n          ...response.data,\r\n          statistics: stats\r\n        };\r\n      }\r\n      \r\n      return response.data;\r\n    }\r\n  },\r\n\r\n  // Get single student\r\n  getStudent: async (id) => {\r\n    const response = await api.get(`/api/accounts/students/${id}/`);\r\n    return response.data;\r\n  },\r\n\r\n  // Update student\r\n  updateStudent: async (id, data) => {\r\n    console.log('updateStudent called with:', { id, data });\r\n\r\n    // Check authentication\r\n    const token = getAuthToken();\r\n    console.log('Auth token available:', !!token);\r\n    if (token) {\r\n      console.log('Token preview:', token.substring(0, 20) + '...');\r\n    }\r\n\r\n    if (!token) {\r\n      throw new Error('Authentication required to update student');\r\n    }\r\n\r\n    // Clean data to ensure proper format\r\n    const cleanedData = { ...data };\r\n    \r\n    // Ensure numeric fields are properly formatted\r\n    ['joining_year', 'passout_year'].forEach(field => {\r\n      if (cleanedData[field] !== null && cleanedData[field] !== undefined) {\r\n        const num = parseInt(cleanedData[field]);\r\n        cleanedData[field] = isNaN(num) ? null : num;\r\n      }\r\n    });\r\n\r\n    // Ensure string fields are properly formatted\r\n    const stringFields = [\r\n      'first_name', 'last_name', 'student_id', 'contact_email', 'phone', 'branch', 'gpa',\r\n      'date_of_birth', 'address', 'city', 'district', 'state', 'pincode', 'country',\r\n      'parent_contact', 'education', 'skills',\r\n      'tenth_cgpa', 'tenth_percentage', 'tenth_board', 'tenth_school', 'tenth_year_of_passing', \r\n      'tenth_location', 'tenth_specialization',\r\n      'twelfth_cgpa', 'twelfth_percentage', 'twelfth_board', 'twelfth_school', 'twelfth_year_of_passing',\r\n      'twelfth_location', 'twelfth_specialization'\r\n    ];\r\n\r\n    stringFields.forEach(field => {\r\n      if (cleanedData[field] !== null && cleanedData[field] !== undefined) {\r\n        cleanedData[field] = String(cleanedData[field]).trim();\r\n      }\r\n    });\r\n\r\n    // Remove undefined values\r\n    Object.keys(cleanedData).forEach(key => {\r\n      if (cleanedData[key] === undefined) {\r\n        delete cleanedData[key];\r\n      }\r\n    });\r\n\r\n    console.log('Cleaned data being sent:', cleanedData);\r\n\r\n    // Try the ViewSet endpoint first (more RESTful)\r\n    try {\r\n      console.log('Trying ViewSet endpoint:', `/api/accounts/profiles/${id}/`);\r\n      const response = await api.patch(`/api/accounts/profiles/${id}/`, cleanedData);\r\n      console.log('ViewSet endpoint success:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('ViewSet endpoint failed:', {\r\n        status: error.response?.status,\r\n        statusText: error.response?.statusText,\r\n        data: error.response?.data,\r\n        headers: error.response?.headers,\r\n        config: error.config\r\n      });\r\n\r\n      // If ViewSet fails, try the fallback endpoint\r\n      try {\r\n        console.log('Trying fallback endpoint:', `/api/accounts/students/${id}/update/`);\r\n        const response = await api.patch(`/api/accounts/students/${id}/update/`, cleanedData);\r\n        console.log('Fallback endpoint success:', response.data);\r\n        return response.data;\r\n      } catch (updateError) {\r\n        console.error('Failed to update student via both endpoints:', {\r\n          viewSetError: {\r\n            status: error.response?.status,\r\n            data: error.response?.data\r\n          },\r\n          updateViewError: {\r\n            status: updateError.response?.status,\r\n            data: updateError.response?.data\r\n          }\r\n        });\r\n\r\n        // Throw the more specific error\r\n        const primaryError = updateError.response?.status === 400 ? updateError : error;\r\n        throw primaryError;\r\n      }\r\n    }\r\n  },\r\n\r\n  // Get current user profile\r\n  getProfile: async () => {\r\n    const token = getAuthToken();\r\n    return api.get('/api/auth/profile/', {\r\n      headers: { Authorization: `Bearer ${token}` },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Update profile information\r\n  updateProfile: async (data) => {\r\n    const token = getAuthToken();\r\n    return api.patch('/api/auth/profile/', data, {\r\n      headers: { Authorization: `Bearer ${token}` },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Upload profile image\r\n  uploadProfileImage: async (file) => {\r\n    const token = getAuthToken();\r\n    const formData = new FormData();\r\n    formData.append('image', file);\r\n\r\n    return api.post('/api/accounts/profiles/me/upload_profile_image/', formData, {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Upload resume using new Resume model\r\n  uploadResume: async (file, name = null, isPrimary = false) => {\r\n    const token = getAuthToken();\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n    if (name) {\r\n      formData.append('name', name);\r\n    }\r\n    formData.append('is_primary', isPrimary);\r\n\r\n    return api.post('/api/accounts/profiles/me/resumes/', formData, {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Admin upload resume for specific student\r\n  adminUploadResume: async (studentId, file, name = null, isPrimary = false) => {\r\n    const token = getAuthToken();\r\n    if (!token) {\r\n      throw new Error('No authentication token found');\r\n    }\r\n\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n    if (name) {\r\n      formData.append('name', name);\r\n    }\r\n    formData.append('is_primary', isPrimary);\r\n\r\n    return api.post(`/api/accounts/profiles/${studentId}/upload_resume/`, formData, {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Admin get resumes for specific student\r\n  adminGetResumes: async (studentId) => {\r\n    const token = getAuthToken();\r\n    if (!token) {\r\n      throw new Error('No authentication token found');\r\n    }\r\n\r\n    return api.get(`/api/accounts/profiles/${studentId}/resumes/`, {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n      },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Admin upload certificate for specific student\r\n  adminUploadCertificate: async (studentId, file, type) => {\r\n    const token = getAuthToken();\r\n    if (!token) {\r\n      throw new Error('No authentication token found');\r\n    }\r\n\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n    formData.append('type', type);\r\n\r\n    return api.post(`/api/accounts/profiles/${studentId}/upload_certificate/`, formData, {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Admin upload semester marksheet for specific student\r\n  adminUploadSemesterMarksheet: async (studentId, file, semester, cgpa) => {\r\n    const token = getAuthToken();\r\n    if (!token) {\r\n      throw new Error('No authentication token found');\r\n    }\r\n\r\n    const formData = new FormData();\r\n    formData.append('marksheet_file', file);\r\n    formData.append('semester', semester);\r\n    formData.append('cgpa', cgpa);\r\n\r\n    return api.post(`/api/accounts/profiles/${studentId}/upload_semester_marksheet/`, formData, {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Legacy resume upload (for backward compatibility)\r\n  uploadResumeToProfile: async (file) => {\r\n    const token = getAuthToken();\r\n    const formData = new FormData();\r\n    formData.append('resume', file);\r\n\r\n    return api.patch('/api/auth/profile/', formData, {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Get all resumes for the student\r\n  getResumes: async () => {\r\n    try {\r\n      const token = getAuthToken();\r\n      if (!token) {\r\n        console.log('No authentication token, returning empty array');\r\n        return [];\r\n      }\r\n\r\n      // Try the new resume endpoint first\r\n      const response = await api.get('/api/accounts/profiles/me/resumes/', {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n\r\n      // Ensure we're getting a proper response\r\n      if (!response.data) {\r\n        return await studentsAPI.getResumesLegacy();\r\n      }\r\n\r\n      // Handle different response formats\r\n      if (Array.isArray(response.data)) {\r\n        return response.data;\r\n      } else if (response.data && response.data.data && Array.isArray(response.data.data)) {\r\n        return response.data.data;\r\n      } else {\r\n        console.log('Response data is not an array, trying fallback. Response:', response.data);\r\n        try {\r\n          return await studentsAPI.getResumesLegacy();\r\n        } catch (fallbackError) {\r\n          console.log('Fallback also failed, returning empty array');\r\n          return [];\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.log('Resume endpoint failed, using fallback method');\r\n      try {\r\n        return await studentsAPI.getResumesLegacy();\r\n      } catch (fallbackError) {\r\n        console.log('Fallback method also failed, returning empty array');\r\n        return [];\r\n      }\r\n    }\r\n  },\r\n\r\n  // Legacy method to get resumes from profile\r\n  getResumesLegacy: async () => {\r\n    try {\r\n      const token = getAuthToken();\r\n      if (!token) {\r\n        console.log('No auth token for legacy resume fetch');\r\n        return [];\r\n      }\r\n\r\n      const profile = await studentsAPI.getProfile();\r\n\r\n      if (profile?.resume || profile?.resume_url) {\r\n        const resumeUrl = profile.resume_url || profile.resume;\r\n        if (resumeUrl && resumeUrl.trim() !== '' && resumeUrl !== 'null' && resumeUrl !== 'undefined') {\r\n          const fileName = resumeUrl.split('/').pop() || 'Resume.pdf';\r\n          return [{\r\n            id: profile.id || 1,\r\n            name: fileName,\r\n            file_url: resumeUrl,\r\n            uploaded_at: profile.updated_at || new Date().toISOString()\r\n          }];\r\n        }\r\n      }\r\n      return [];\r\n    } catch (error) {\r\n      console.log('Legacy resume fetch error:', error.message);\r\n      return [];\r\n    }\r\n  },\r\n\r\n  // Delete a specific resume\r\n  deleteResume: async (resumeId) => {\r\n    const token = getAuthToken();\r\n    try {\r\n      console.log(`Attempting to delete resume with ID: ${resumeId}`);\r\n\r\n      // Use the new Resume model endpoint\r\n      const response = await api.delete(`/api/accounts/profiles/me/resumes/${resumeId}/`, {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n      console.log('DELETE resume successful:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error deleting resume:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Legacy delete function with fallback strategies\r\n  deleteResumeLegacy: async (resumeId) => {\r\n    const token = getAuthToken();\r\n    try {\r\n      console.log(`Attempting to delete resume with ID: ${resumeId}`);\r\n\r\n      let success = false;\r\n\r\n      // Attempt different deletion strategies\r\n      const strategies = [\r\n        // Strategy 1: Standard DELETE request\r\n        async () => {\r\n          try {\r\n            const response = await api.delete(`/api/accounts/profiles/me/resumes/${resumeId}/`, {\r\n              headers: { Authorization: `Bearer ${token}` },\r\n            });\r\n            console.log('DELETE resume successful:', response.data);\r\n            return { success: true, data: response.data };\r\n          } catch (error) {\r\n            console.log(`Strategy 1 failed: ${error.message}`);\r\n            return { success: false };\r\n          }\r\n        },\r\n        \r\n        // Strategy 2: POST to remove endpoint\r\n        async () => {\r\n          try {\r\n            const response = await api.post(`/api/accounts/profiles/me/resumes/${resumeId}/remove/`, {}, {\r\n              headers: { Authorization: `Bearer ${token}` },\r\n            });\r\n            console.log('POST remove successful:', response.data);\r\n            return { success: true, data: response.data };\r\n          } catch (error) {\r\n            console.log(`Strategy 2 failed: ${error.message}`);\r\n            return { success: false };\r\n          }\r\n        },\r\n        \r\n        // Strategy 3: Patch profile with delete_resume field\r\n        async () => {\r\n          try {\r\n            const response = await api.patch('/api/auth/profile/', {\r\n              delete_resume: resumeId\r\n            }, {\r\n              headers: { Authorization: `Bearer ${token}` },\r\n            });\r\n            console.log('PATCH profile successful:', response.data);\r\n            return { success: true, data: response.data };\r\n          } catch (error) {\r\n            console.log(`Strategy 3 failed: ${error.message}`);\r\n            return { success: false };\r\n          }\r\n        },\r\n        \r\n        // Strategy 4: Reset all resumes (extreme fallback)\r\n        async () => {\r\n          try {\r\n            const response = await api.patch('/api/auth/profile/', {\r\n              reset_resumes: true\r\n            }, {\r\n              headers: { Authorization: `Bearer ${token}` },\r\n            });\r\n            console.log('Reset resumes successful:', response.data);\r\n            return { success: true, data: response.data };\r\n          } catch (error) {\r\n            console.log(`Strategy 4 failed: ${error.message}`);\r\n            return { success: false };\r\n          }\r\n        }\r\n      ];\r\n      \r\n      // Try each strategy in sequence until one succeeds\r\n      for (const strategy of strategies) {\r\n        const result = await strategy();\r\n        if (result.success) {\r\n          success = true;\r\n          break;\r\n        }\r\n      }\r\n      \r\n      // Clear any locally cached data for this resume regardless of backend success\r\n      if (typeof window !== 'undefined') {\r\n        // Clear any resume-related data from localStorage\r\n        try {\r\n          const localStorageKeys = Object.keys(localStorage);\r\n          const resumeKeys = localStorageKeys.filter(key => \r\n            key.includes('resume') || key.includes('file') || key.includes('document')\r\n          );\r\n          \r\n          if (resumeKeys.length > 0) {\r\n            console.log('Clearing resume-related localStorage items:', resumeKeys);\r\n            resumeKeys.forEach(key => localStorage.removeItem(key));\r\n          }\r\n          \r\n          // Also try to clear specific keys that might be used for caching\r\n          localStorage.removeItem('resume_cache');\r\n          localStorage.removeItem('resume_list');\r\n          localStorage.removeItem('profile_cache');\r\n          localStorage.removeItem('resume_count');\r\n          localStorage.removeItem('last_resume_update');\r\n        } catch (e) {\r\n          console.error('Error clearing localStorage:', e);\r\n        }\r\n      }\r\n      \r\n      return { success, message: success ? \"Resume deleted successfully\" : \"Resume deleted locally but server sync failed\" };\r\n    } catch (error) {\r\n      console.error('Resume deletion failed:', error.response?.status, error.message);\r\n      // For UI purposes, return a success response even if backend fails\r\n      // This allows the UI to remove the resume entry and maintain a good user experience\r\n      return { \r\n        success: true,  // Return true for UI purposes\r\n        synced: false,  // But indicate sync status\r\n        error: error.message,\r\n        status: error.response?.status,\r\n        message: \"Resume removed from display (sync with server failed)\"\r\n      };\r\n    }\r\n  },\r\n\r\n  // Upload certificate (10th or 12th)\r\n  uploadCertificate: async (file, type) => {\r\n    const token = getAuthToken();\r\n    const formData = new FormData();\r\n    formData.append('file', file);  // Backend expects 'file', not 'certificate'\r\n    formData.append('type', type);\r\n\r\n    return api.post('/api/accounts/profiles/me/upload_certificate/', formData, {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Get all certificates for the student\r\n  getCertificates: async () => {\r\n    const token = getAuthToken();\r\n    if (!token) {\r\n      throw new Error('Authentication required to fetch certificates');\r\n    }\r\n    \r\n    try {\r\n      const response = await api.get('/api/accounts/profiles/me/certificates/', {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n      \r\n      // Ensure we're getting a proper response\r\n      if (!response.data) {\r\n        console.error('Empty response when fetching certificates');\r\n        return [];\r\n      }\r\n      \r\n      // Handle different response formats\r\n      if (Array.isArray(response.data)) {\r\n        return response.data;\r\n      } else if (response.data.data && Array.isArray(response.data.data)) {\r\n        return response.data.data;\r\n      } else {\r\n        console.error('Unexpected certificate data format:', response.data);\r\n        return [];\r\n      }\r\n    } catch (error) {\r\n      console.error('Certificate fetch error:', error.response?.status, error.message);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Delete a specific certificate\r\n  deleteCertificate: async (certificateType) => {\r\n    const token = getAuthToken();\r\n    try {\r\n      console.log(`Attempting to delete certificate: ${certificateType}`);\r\n\r\n      const response = await api.delete(`/api/accounts/profiles/me/delete_certificate/${certificateType}/`, {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n      console.log('DELETE certificate successful:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error deleting certificate:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Admin delete certificate for specific student\r\n  adminDeleteCertificate: async (studentId, certificateType) => {\r\n    const token = getAuthToken();\r\n    try {\r\n      console.log(`Admin attempting to delete certificate: ${certificateType} for student: ${studentId}`);\r\n\r\n      const response = await api.delete(`/api/accounts/profiles/${studentId}/delete_certificate/${certificateType}/`, {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n      console.log('Admin DELETE certificate successful:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error deleting certificate:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Delete a specific marksheet\r\n  deleteMarksheet: async (semester) => {\r\n    const token = getAuthToken();\r\n    try {\r\n      console.log(`Attempting to delete marksheet for semester: ${semester}`);\r\n\r\n      const response = await api.delete(`/api/accounts/profiles/me/delete_marksheet/${semester}/`, {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n      console.log('DELETE marksheet successful:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error deleting marksheet:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Admin delete marksheet for specific student\r\n  adminDeleteMarksheet: async (studentId, semester) => {\r\n    const token = getAuthToken();\r\n    try {\r\n      console.log(`Admin attempting to delete marksheet for semester: ${semester} for student: ${studentId}`);\r\n\r\n      const response = await api.delete(`/api/accounts/profiles/${studentId}/delete_marksheet/${semester}/`, {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n      console.log('Admin DELETE marksheet successful:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error deleting marksheet:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Legacy delete function (keeping for backward compatibility)\r\n  deleteCertificateLegacy: async (certificateId) => {\r\n    const token = getAuthToken();\r\n    try {\r\n      console.log(`Attempting to delete certificate with ID: ${certificateId}`);\r\n\r\n      let success = false;\r\n\r\n      // Attempt different deletion strategies\r\n      const strategies = [\r\n        // Strategy 1: Standard DELETE request\r\n        async () => {\r\n          try {\r\n            const response = await api.delete(`/api/accounts/profiles/me/certificates/${certificateId}/`, {\r\n              headers: { Authorization: `Bearer ${token}` },\r\n            });\r\n            console.log('DELETE certificate successful:', response.data);\r\n            return { success: true, data: response.data };\r\n          } catch (error) {\r\n            console.log(`Strategy 1 failed: ${error.message}`);\r\n            return { success: false };\r\n          }\r\n        },\r\n\r\n        // Strategy 2: POST to remove endpoint\r\n        async () => {\r\n          try {\r\n            const response = await api.post(`/api/accounts/profiles/me/certificates/${certificateId}/remove/`, {}, {\r\n              headers: { Authorization: `Bearer ${token}` },\r\n            });\r\n            console.log('POST remove successful:', response.data);\r\n            return { success: true, data: response.data };\r\n          } catch (error) {\r\n            console.log(`Strategy 2 failed: ${error.message}`);\r\n            return { success: false };\r\n          }\r\n        },\r\n        \r\n        // Strategy 3: Patch profile with delete_certificate field\r\n        async () => {\r\n          try {\r\n            const response = await api.patch('/api/auth/profile/', {\r\n              delete_certificate: certificateId\r\n            }, {\r\n              headers: { Authorization: `Bearer ${token}` },\r\n            });\r\n            console.log('PATCH profile successful:', response.data);\r\n            return { success: true, data: response.data };\r\n          } catch (error) {\r\n            console.log(`Strategy 3 failed: ${error.message}`);\r\n            return { success: false };\r\n          }\r\n        },\r\n        \r\n        // Strategy 4: Reset all certificates (extreme fallback)\r\n        async () => {\r\n          try {\r\n            const response = await api.patch('/api/auth/profile/', {\r\n              reset_certificates: true\r\n            }, {\r\n              headers: { Authorization: `Bearer ${token}` },\r\n            });\r\n            console.log('Reset certificates successful:', response.data);\r\n            return { success: true, data: response.data };\r\n          } catch (error) {\r\n            console.log(`Strategy 4 failed: ${error.message}`);\r\n            return { success: false };\r\n          }\r\n        }\r\n      ];\r\n      \r\n      // Try each strategy in sequence until one succeeds\r\n      for (const strategy of strategies) {\r\n        const result = await strategy();\r\n        if (result.success) {\r\n          success = true;\r\n          break;\r\n        }\r\n      }\r\n      \r\n      // Clear any locally cached data for this certificate regardless of backend success\r\n      if (typeof window !== 'undefined') {\r\n        // Clear any certificate-related data from localStorage\r\n        try {\r\n          const localStorageKeys = Object.keys(localStorage);\r\n          const certificateKeys = localStorageKeys.filter(key => \r\n            key.includes('certificate') || key.includes('document') || key.includes('cert')\r\n          );\r\n          \r\n          if (certificateKeys.length > 0) {\r\n            console.log('Clearing certificate-related localStorage items:', certificateKeys);\r\n            certificateKeys.forEach(key => localStorage.removeItem(key));\r\n          }\r\n          \r\n          // Also try to clear specific keys that might be used for caching\r\n          localStorage.removeItem('certificate_cache');\r\n          localStorage.removeItem('certificate_list');\r\n          localStorage.removeItem('profile_cache');\r\n        } catch (e) {\r\n          console.error('Error clearing localStorage:', e);\r\n        }\r\n      }\r\n      \r\n      return { success, message: success ? \"Certificate deleted successfully\" : \"Certificate deleted locally but server sync failed\" };\r\n    } catch (error) {\r\n      console.error('Certificate deletion failed:', error.response?.status, error.message);\r\n      // For UI purposes, return a success response even if backend fails\r\n      // This allows the UI to remove the certificate entry and maintain a good user experience\r\n      return { \r\n        success: true,  // Return true for UI purposes\r\n        synced: false,  // But indicate sync status\r\n        error: error.message,\r\n        status: error.response?.status,\r\n        message: \"Certificate removed from display (sync with server failed)\"\r\n      };\r\n    }\r\n  },\r\n\r\n  // Get semester marksheets\r\n  getSemesterMarksheets: async () => {\r\n    const token = getAuthToken();\r\n    return api.get('/api/accounts/profiles/me/semester_marksheets/', {\r\n      headers: { Authorization: `Bearer ${token}` },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Upload semester marksheet\r\n  uploadSemesterMarksheet: async (file, semester, cgpa) => {\r\n    const token = getAuthToken();\r\n    const formData = new FormData();\r\n    formData.append('marksheet_file', file);\r\n    formData.append('semester', semester);\r\n    formData.append('cgpa', cgpa);\r\n\r\n    return api.post('/api/accounts/profiles/me/upload_semester_marksheet/', formData, {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Get current user's freeze status and restrictions\r\n  getFreezeStatus: async () => {\r\n    const token = getAuthToken();\r\n    if (!token) {\r\n      throw new Error('Authentication required to fetch freeze status');\r\n    }\r\n\r\n    try {\r\n      const response = await api.get('/api/auth/profile/', {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n\r\n      const profile = response.data;\r\n      return {\r\n        freeze_status: profile.freeze_status || 'none',\r\n        freeze_reason: profile.freeze_reason,\r\n        freeze_date: profile.freeze_date,\r\n        min_salary_requirement: profile.min_salary_requirement,\r\n        allowed_job_tiers: profile.allowed_job_tiers || [],\r\n        allowed_job_types: profile.allowed_job_types || [],\r\n        allowed_companies: profile.allowed_companies || []\r\n      };\r\n    } catch (error) {\r\n      console.error('Freeze status fetch error:', error.response?.status, error.message);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Check if student can apply to a specific job\r\n  canApplyToJob: async (jobId) => {\r\n    const token = getAuthToken();\r\n    if (!token) {\r\n      throw new Error('Authentication required to check job application eligibility');\r\n    }\r\n\r\n    try {\r\n      const response = await api.get(`/api/v1/college/default-college/jobs/${jobId}/can-apply/`, {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Job application eligibility check error:', error.response?.status, error.message);\r\n      if (error.response?.data) {\r\n        console.error('Error details:', error.response.data);\r\n      }\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get list of jobs the student has applied to\r\n  getAppliedJobs: async () => {\r\n    const token = getAuthToken();\r\n    if (!token) {\r\n      throw new Error('Authentication required to fetch applied jobs');\r\n    }\r\n\r\n    try {\r\n      const response = await api.get('/api/v1/college/default-college/jobs/applied/', {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Applied jobs fetch error:', error.response?.status, error.message);\r\n      throw error;\r\n    }\r\n  },\r\n};\r\n\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,wCAAwC;AACxC,MAAM,eAAe;AAErB,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,gDAAgD;AAChD,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,CAC1B,CAAC;IACC,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;IACzB,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,8CAA8C;AAC9C,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC3B,CAAC,WAAa,UACd,CAAC;IACC,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAClC,2BAA2B;QAC3B,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGK,MAAM,cAAc;IACzB,mBAAmB;IACnB,aAAa,OAAO,SAAS,CAAC,CAAC;QAC7B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,2BAA2B;YAAE;QAAO;QACnE,OAAO,SAAS,IAAI;IACtB;IAEA,+BAA+B;IAC/B,sBAAsB,OAAO,SAAS,CAAC,CAAC;QACtC,IAAI;YACF,qDAAqD;YACrD,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,iCAAiC;gBAAE;YAAO;YACzE,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,wCAAwC;YACxC,QAAQ,GAAG,CAAC;YACZ,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,2BAA2B;gBAAE;YAAO;YAEnE,+CAA+C;YAC/C,MAAM,WAAW,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI;YACpD,IAAI,MAAM,OAAO,CAAC,WAAW;gBAC3B,MAAM,QAAQ,sBAAsB,UAAU;gBAC9C,OAAO;oBACL,GAAG,SAAS,IAAI;oBAChB,YAAY;gBACd;YACF;YAEA,OAAO,SAAS,IAAI;QACtB;IACF;IAEA,qBAAqB;IACrB,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,iBAAiB;IACjB,eAAe,OAAO,IAAI;QACxB,QAAQ,GAAG,CAAC,8BAA8B;YAAE;YAAI;QAAK;QAErD,uBAAuB;QACvB,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,QAAQ,GAAG,CAAC,yBAAyB,CAAC,CAAC;QACvC,IAAI,OAAO;YACT,QAAQ,GAAG,CAAC,kBAAkB,MAAM,SAAS,CAAC,GAAG,MAAM;QACzD;QAEA,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,qCAAqC;QACrC,MAAM,cAAc;YAAE,GAAG,IAAI;QAAC;QAE9B,+CAA+C;QAC/C;YAAC;YAAgB;SAAe,CAAC,OAAO,CAAC,CAAA;YACvC,IAAI,WAAW,CAAC,MAAM,KAAK,QAAQ,WAAW,CAAC,MAAM,KAAK,WAAW;gBACnE,MAAM,MAAM,SAAS,WAAW,CAAC,MAAM;gBACvC,WAAW,CAAC,MAAM,GAAG,MAAM,OAAO,OAAO;YAC3C;QACF;QAEA,8CAA8C;QAC9C,MAAM,eAAe;YACnB;YAAc;YAAa;YAAc;YAAiB;YAAS;YAAU;YAC7E;YAAiB;YAAW;YAAQ;YAAY;YAAS;YAAW;YACpE;YAAkB;YAAa;YAC/B;YAAc;YAAoB;YAAe;YAAgB;YACjE;YAAkB;YAClB;YAAgB;YAAsB;YAAiB;YAAkB;YACzE;YAAoB;SACrB;QAED,aAAa,OAAO,CAAC,CAAA;YACnB,IAAI,WAAW,CAAC,MAAM,KAAK,QAAQ,WAAW,CAAC,MAAM,KAAK,WAAW;gBACnE,WAAW,CAAC,MAAM,GAAG,OAAO,WAAW,CAAC,MAAM,EAAE,IAAI;YACtD;QACF;QAEA,0BAA0B;QAC1B,OAAO,IAAI,CAAC,aAAa,OAAO,CAAC,CAAA;YAC/B,IAAI,WAAW,CAAC,IAAI,KAAK,WAAW;gBAClC,OAAO,WAAW,CAAC,IAAI;YACzB;QACF;QAEA,QAAQ,GAAG,CAAC,4BAA4B;QAExC,gDAAgD;QAChD,IAAI;YACF,QAAQ,GAAG,CAAC,4BAA4B,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC;YACvE,MAAM,WAAW,MAAM,IAAI,KAAK,CAAC,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC,EAAE;YAClE,QAAQ,GAAG,CAAC,6BAA6B,SAAS,IAAI;YACtD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;gBACxC,QAAQ,MAAM,QAAQ,EAAE;gBACxB,YAAY,MAAM,QAAQ,EAAE;gBAC5B,MAAM,MAAM,QAAQ,EAAE;gBACtB,SAAS,MAAM,QAAQ,EAAE;gBACzB,QAAQ,MAAM,MAAM;YACtB;YAEA,8CAA8C;YAC9C,IAAI;gBACF,QAAQ,GAAG,CAAC,6BAA6B,CAAC,uBAAuB,EAAE,GAAG,QAAQ,CAAC;gBAC/E,MAAM,WAAW,MAAM,IAAI,KAAK,CAAC,CAAC,uBAAuB,EAAE,GAAG,QAAQ,CAAC,EAAE;gBACzE,QAAQ,GAAG,CAAC,8BAA8B,SAAS,IAAI;gBACvD,OAAO,SAAS,IAAI;YACtB,EAAE,OAAO,aAAa;gBACpB,QAAQ,KAAK,CAAC,gDAAgD;oBAC5D,cAAc;wBACZ,QAAQ,MAAM,QAAQ,EAAE;wBACxB,MAAM,MAAM,QAAQ,EAAE;oBACxB;oBACA,iBAAiB;wBACf,QAAQ,YAAY,QAAQ,EAAE;wBAC9B,MAAM,YAAY,QAAQ,EAAE;oBAC9B;gBACF;gBAEA,gCAAgC;gBAChC,MAAM,eAAe,YAAY,QAAQ,EAAE,WAAW,MAAM,cAAc;gBAC1E,MAAM;YACR;QACF;IACF;IAEA,2BAA2B;IAC3B,YAAY;QACV,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,OAAO,IAAI,GAAG,CAAC,sBAAsB;YACnC,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAC9C,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,6BAA6B;IAC7B,eAAe,OAAO;QACpB,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,OAAO,IAAI,KAAK,CAAC,sBAAsB,MAAM;YAC3C,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAC9C,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,uBAAuB;IACvB,oBAAoB,OAAO;QACzB,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS;QAEzB,OAAO,IAAI,IAAI,CAAC,mDAAmD,UAAU;YAC3E,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,gBAAgB;YAClB;QACF,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,uCAAuC;IACvC,cAAc,OAAO,MAAM,OAAO,IAAI,EAAE,YAAY,KAAK;QACvD,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QACxB,IAAI,MAAM;YACR,SAAS,MAAM,CAAC,QAAQ;QAC1B;QACA,SAAS,MAAM,CAAC,cAAc;QAE9B,OAAO,IAAI,IAAI,CAAC,sCAAsC,UAAU;YAC9D,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,gBAAgB;YAClB;QACF,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,2CAA2C;IAC3C,mBAAmB,OAAO,WAAW,MAAM,OAAO,IAAI,EAAE,YAAY,KAAK;QACvE,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QACxB,IAAI,MAAM;YACR,SAAS,MAAM,CAAC,QAAQ;QAC1B;QACA,SAAS,MAAM,CAAC,cAAc;QAE9B,OAAO,IAAI,IAAI,CAAC,CAAC,uBAAuB,EAAE,UAAU,eAAe,CAAC,EAAE,UAAU;YAC9E,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,gBAAgB;YAClB;QACF,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,yCAAyC;IACzC,iBAAiB,OAAO;QACtB,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,IAAI,GAAG,CAAC,CAAC,uBAAuB,EAAE,UAAU,SAAS,CAAC,EAAE;YAC7D,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;YAClC;QACF,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,gDAAgD;IAChD,wBAAwB,OAAO,WAAW,MAAM;QAC9C,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QACxB,SAAS,MAAM,CAAC,QAAQ;QAExB,OAAO,IAAI,IAAI,CAAC,CAAC,uBAAuB,EAAE,UAAU,oBAAoB,CAAC,EAAE,UAAU;YACnF,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,gBAAgB;YAClB;QACF,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,uDAAuD;IACvD,8BAA8B,OAAO,WAAW,MAAM,UAAU;QAC9D,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,kBAAkB;QAClC,SAAS,MAAM,CAAC,YAAY;QAC5B,SAAS,MAAM,CAAC,QAAQ;QAExB,OAAO,IAAI,IAAI,CAAC,CAAC,uBAAuB,EAAE,UAAU,2BAA2B,CAAC,EAAE,UAAU;YAC1F,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,gBAAgB;YAClB;QACF,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,oDAAoD;IACpD,uBAAuB,OAAO;QAC5B,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,UAAU;QAE1B,OAAO,IAAI,KAAK,CAAC,sBAAsB,UAAU;YAC/C,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,gBAAgB;YAClB;QACF,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,kCAAkC;IAClC,YAAY;QACV,IAAI;YACF,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;YACzB,IAAI,CAAC,OAAO;gBACV,QAAQ,GAAG,CAAC;gBACZ,OAAO,EAAE;YACX;YAEA,oCAAoC;YACpC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,sCAAsC;gBACnE,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YAEA,yCAAyC;YACzC,IAAI,CAAC,SAAS,IAAI,EAAE;gBAClB,OAAO,MAAM,YAAY,gBAAgB;YAC3C;YAEA,oCAAoC;YACpC,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBAChC,OAAO,SAAS,IAAI;YACtB,OAAO,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,GAAG;gBACnF,OAAO,SAAS,IAAI,CAAC,IAAI;YAC3B,OAAO;gBACL,QAAQ,GAAG,CAAC,6DAA6D,SAAS,IAAI;gBACtF,IAAI;oBACF,OAAO,MAAM,YAAY,gBAAgB;gBAC3C,EAAE,OAAO,eAAe;oBACtB,QAAQ,GAAG,CAAC;oBACZ,OAAO,EAAE;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC;YACZ,IAAI;gBACF,OAAO,MAAM,YAAY,gBAAgB;YAC3C,EAAE,OAAO,eAAe;gBACtB,QAAQ,GAAG,CAAC;gBACZ,OAAO,EAAE;YACX;QACF;IACF;IAEA,4CAA4C;IAC5C,kBAAkB;QAChB,IAAI;YACF,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;YACzB,IAAI,CAAC,OAAO;gBACV,QAAQ,GAAG,CAAC;gBACZ,OAAO,EAAE;YACX;YAEA,MAAM,UAAU,MAAM,YAAY,UAAU;YAE5C,IAAI,SAAS,UAAU,SAAS,YAAY;gBAC1C,MAAM,YAAY,QAAQ,UAAU,IAAI,QAAQ,MAAM;gBACtD,IAAI,aAAa,UAAU,IAAI,OAAO,MAAM,cAAc,UAAU,cAAc,aAAa;oBAC7F,MAAM,WAAW,UAAU,KAAK,CAAC,KAAK,GAAG,MAAM;oBAC/C,OAAO;wBAAC;4BACN,IAAI,QAAQ,EAAE,IAAI;4BAClB,MAAM;4BACN,UAAU;4BACV,aAAa,QAAQ,UAAU,IAAI,IAAI,OAAO,WAAW;wBAC3D;qBAAE;gBACJ;YACF;YACA,OAAO,EAAE;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,8BAA8B,MAAM,OAAO;YACvD,OAAO,EAAE;QACX;IACF;IAEA,2BAA2B;IAC3B,cAAc,OAAO;QACnB,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,UAAU;YAE9D,oCAAoC;YACpC,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,kCAAkC,EAAE,SAAS,CAAC,CAAC,EAAE;gBAClF,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YACA,QAAQ,GAAG,CAAC,6BAA6B,SAAS,IAAI;YACtD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA,kDAAkD;IAClD,oBAAoB,OAAO;QACzB,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,UAAU;YAE9D,IAAI,UAAU;YAEd,wCAAwC;YACxC,MAAM,aAAa;gBACjB,sCAAsC;gBACtC;oBACE,IAAI;wBACF,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,kCAAkC,EAAE,SAAS,CAAC,CAAC,EAAE;4BAClF,SAAS;gCAAE,eAAe,CAAC,OAAO,EAAE,OAAO;4BAAC;wBAC9C;wBACA,QAAQ,GAAG,CAAC,6BAA6B,SAAS,IAAI;wBACtD,OAAO;4BAAE,SAAS;4BAAM,MAAM,SAAS,IAAI;wBAAC;oBAC9C,EAAE,OAAO,OAAO;wBACd,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,OAAO,EAAE;wBACjD,OAAO;4BAAE,SAAS;wBAAM;oBAC1B;gBACF;gBAEA,sCAAsC;gBACtC;oBACE,IAAI;wBACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,kCAAkC,EAAE,SAAS,QAAQ,CAAC,EAAE,CAAC,GAAG;4BAC3F,SAAS;gCAAE,eAAe,CAAC,OAAO,EAAE,OAAO;4BAAC;wBAC9C;wBACA,QAAQ,GAAG,CAAC,2BAA2B,SAAS,IAAI;wBACpD,OAAO;4BAAE,SAAS;4BAAM,MAAM,SAAS,IAAI;wBAAC;oBAC9C,EAAE,OAAO,OAAO;wBACd,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,OAAO,EAAE;wBACjD,OAAO;4BAAE,SAAS;wBAAM;oBAC1B;gBACF;gBAEA,qDAAqD;gBACrD;oBACE,IAAI;wBACF,MAAM,WAAW,MAAM,IAAI,KAAK,CAAC,sBAAsB;4BACrD,eAAe;wBACjB,GAAG;4BACD,SAAS;gCAAE,eAAe,CAAC,OAAO,EAAE,OAAO;4BAAC;wBAC9C;wBACA,QAAQ,GAAG,CAAC,6BAA6B,SAAS,IAAI;wBACtD,OAAO;4BAAE,SAAS;4BAAM,MAAM,SAAS,IAAI;wBAAC;oBAC9C,EAAE,OAAO,OAAO;wBACd,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,OAAO,EAAE;wBACjD,OAAO;4BAAE,SAAS;wBAAM;oBAC1B;gBACF;gBAEA,mDAAmD;gBACnD;oBACE,IAAI;wBACF,MAAM,WAAW,MAAM,IAAI,KAAK,CAAC,sBAAsB;4BACrD,eAAe;wBACjB,GAAG;4BACD,SAAS;gCAAE,eAAe,CAAC,OAAO,EAAE,OAAO;4BAAC;wBAC9C;wBACA,QAAQ,GAAG,CAAC,6BAA6B,SAAS,IAAI;wBACtD,OAAO;4BAAE,SAAS;4BAAM,MAAM,SAAS,IAAI;wBAAC;oBAC9C,EAAE,OAAO,OAAO;wBACd,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,OAAO,EAAE;wBACjD,OAAO;4BAAE,SAAS;wBAAM;oBAC1B;gBACF;aACD;YAED,mDAAmD;YACnD,KAAK,MAAM,YAAY,WAAY;gBACjC,MAAM,SAAS,MAAM;gBACrB,IAAI,OAAO,OAAO,EAAE;oBAClB,UAAU;oBACV;gBACF;YACF;YAEA,8EAA8E;YAC9E,uCAAmC;;YAsBnC;YAEA,OAAO;gBAAE;gBAAS,SAAS,UAAU,gCAAgC;YAAgD;QACvH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;YAC9E,mEAAmE;YACnE,oFAAoF;YACpF,OAAO;gBACL,SAAS;gBACT,QAAQ;gBACR,OAAO,MAAM,OAAO;gBACpB,QAAQ,MAAM,QAAQ,EAAE;gBACxB,SAAS;YACX;QACF;IACF;IAEA,oCAAoC;IACpC,mBAAmB,OAAO,MAAM;QAC9B,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ,OAAQ,4CAA4C;QAC5E,SAAS,MAAM,CAAC,QAAQ;QAExB,OAAO,IAAI,IAAI,CAAC,iDAAiD,UAAU;YACzE,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,gBAAgB;YAClB;QACF,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,uCAAuC;IACvC,iBAAiB;QACf,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,2CAA2C;gBACxE,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YAEA,yCAAyC;YACzC,IAAI,CAAC,SAAS,IAAI,EAAE;gBAClB,QAAQ,KAAK,CAAC;gBACd,OAAO,EAAE;YACX;YAEA,oCAAoC;YACpC,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBAChC,OAAO,SAAS,IAAI;YACtB,OAAO,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,GAAG;gBAClE,OAAO,SAAS,IAAI,CAAC,IAAI;YAC3B,OAAO;gBACL,QAAQ,KAAK,CAAC,uCAAuC,SAAS,IAAI;gBAClE,OAAO,EAAE;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;YAC/E,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,mBAAmB,OAAO;QACxB,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,kCAAkC,EAAE,iBAAiB;YAElE,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,6CAA6C,EAAE,gBAAgB,CAAC,CAAC,EAAE;gBACpG,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YACA,QAAQ,GAAG,CAAC,kCAAkC,SAAS,IAAI;YAC3D,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR;IACF;IAEA,gDAAgD;IAChD,wBAAwB,OAAO,WAAW;QACxC,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,gBAAgB,cAAc,EAAE,WAAW;YAElG,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,uBAAuB,EAAE,UAAU,oBAAoB,EAAE,gBAAgB,CAAC,CAAC,EAAE;gBAC9G,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YACA,QAAQ,GAAG,CAAC,wCAAwC,SAAS,IAAI;YACjE,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR;IACF;IAEA,8BAA8B;IAC9B,iBAAiB,OAAO;QACtB,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,6CAA6C,EAAE,UAAU;YAEtE,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,2CAA2C,EAAE,SAAS,CAAC,CAAC,EAAE;gBAC3F,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YACA,QAAQ,GAAG,CAAC,gCAAgC,SAAS,IAAI;YACzD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,8CAA8C;IAC9C,sBAAsB,OAAO,WAAW;QACtC,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,mDAAmD,EAAE,SAAS,cAAc,EAAE,WAAW;YAEtG,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,uBAAuB,EAAE,UAAU,kBAAkB,EAAE,SAAS,CAAC,CAAC,EAAE;gBACrG,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YACA,QAAQ,GAAG,CAAC,sCAAsC,SAAS,IAAI;YAC/D,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,8DAA8D;IAC9D,yBAAyB,OAAO;QAC9B,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,0CAA0C,EAAE,eAAe;YAExE,IAAI,UAAU;YAEd,wCAAwC;YACxC,MAAM,aAAa;gBACjB,sCAAsC;gBACtC;oBACE,IAAI;wBACF,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,uCAAuC,EAAE,cAAc,CAAC,CAAC,EAAE;4BAC5F,SAAS;gCAAE,eAAe,CAAC,OAAO,EAAE,OAAO;4BAAC;wBAC9C;wBACA,QAAQ,GAAG,CAAC,kCAAkC,SAAS,IAAI;wBAC3D,OAAO;4BAAE,SAAS;4BAAM,MAAM,SAAS,IAAI;wBAAC;oBAC9C,EAAE,OAAO,OAAO;wBACd,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,OAAO,EAAE;wBACjD,OAAO;4BAAE,SAAS;wBAAM;oBAC1B;gBACF;gBAEA,sCAAsC;gBACtC;oBACE,IAAI;wBACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,uCAAuC,EAAE,cAAc,QAAQ,CAAC,EAAE,CAAC,GAAG;4BACrG,SAAS;gCAAE,eAAe,CAAC,OAAO,EAAE,OAAO;4BAAC;wBAC9C;wBACA,QAAQ,GAAG,CAAC,2BAA2B,SAAS,IAAI;wBACpD,OAAO;4BAAE,SAAS;4BAAM,MAAM,SAAS,IAAI;wBAAC;oBAC9C,EAAE,OAAO,OAAO;wBACd,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,OAAO,EAAE;wBACjD,OAAO;4BAAE,SAAS;wBAAM;oBAC1B;gBACF;gBAEA,0DAA0D;gBAC1D;oBACE,IAAI;wBACF,MAAM,WAAW,MAAM,IAAI,KAAK,CAAC,sBAAsB;4BACrD,oBAAoB;wBACtB,GAAG;4BACD,SAAS;gCAAE,eAAe,CAAC,OAAO,EAAE,OAAO;4BAAC;wBAC9C;wBACA,QAAQ,GAAG,CAAC,6BAA6B,SAAS,IAAI;wBACtD,OAAO;4BAAE,SAAS;4BAAM,MAAM,SAAS,IAAI;wBAAC;oBAC9C,EAAE,OAAO,OAAO;wBACd,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,OAAO,EAAE;wBACjD,OAAO;4BAAE,SAAS;wBAAM;oBAC1B;gBACF;gBAEA,wDAAwD;gBACxD;oBACE,IAAI;wBACF,MAAM,WAAW,MAAM,IAAI,KAAK,CAAC,sBAAsB;4BACrD,oBAAoB;wBACtB,GAAG;4BACD,SAAS;gCAAE,eAAe,CAAC,OAAO,EAAE,OAAO;4BAAC;wBAC9C;wBACA,QAAQ,GAAG,CAAC,kCAAkC,SAAS,IAAI;wBAC3D,OAAO;4BAAE,SAAS;4BAAM,MAAM,SAAS,IAAI;wBAAC;oBAC9C,EAAE,OAAO,OAAO;wBACd,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,OAAO,EAAE;wBACjD,OAAO;4BAAE,SAAS;wBAAM;oBAC1B;gBACF;aACD;YAED,mDAAmD;YACnD,KAAK,MAAM,YAAY,WAAY;gBACjC,MAAM,SAAS,MAAM;gBACrB,IAAI,OAAO,OAAO,EAAE;oBAClB,UAAU;oBACV;gBACF;YACF;YAEA,mFAAmF;YACnF,uCAAmC;;YAoBnC;YAEA,OAAO;gBAAE;gBAAS,SAAS,UAAU,qCAAqC;YAAqD;QACjI,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;YACnF,mEAAmE;YACnE,yFAAyF;YACzF,OAAO;gBACL,SAAS;gBACT,QAAQ;gBACR,OAAO,MAAM,OAAO;gBACpB,QAAQ,MAAM,QAAQ,EAAE;gBACxB,SAAS;YACX;QACF;IACF;IAEA,0BAA0B;IAC1B,uBAAuB;QACrB,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,OAAO,IAAI,GAAG,CAAC,kDAAkD;YAC/D,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAC9C,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,4BAA4B;IAC5B,yBAAyB,OAAO,MAAM,UAAU;QAC9C,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,kBAAkB;QAClC,SAAS,MAAM,CAAC,YAAY;QAC5B,SAAS,MAAM,CAAC,QAAQ;QAExB,OAAO,IAAI,IAAI,CAAC,wDAAwD,UAAU;YAChF,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,gBAAgB;YAClB;QACF,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,oDAAoD;IACpD,iBAAiB;QACf,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,sBAAsB;gBACnD,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YAEA,MAAM,UAAU,SAAS,IAAI;YAC7B,OAAO;gBACL,eAAe,QAAQ,aAAa,IAAI;gBACxC,eAAe,QAAQ,aAAa;gBACpC,aAAa,QAAQ,WAAW;gBAChC,wBAAwB,QAAQ,sBAAsB;gBACtD,mBAAmB,QAAQ,iBAAiB,IAAI,EAAE;gBAClD,mBAAmB,QAAQ,iBAAiB,IAAI,EAAE;gBAClD,mBAAmB,QAAQ,iBAAiB,IAAI,EAAE;YACpD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;YACjF,MAAM;QACR;IACF;IAEA,+CAA+C;IAC/C,eAAe,OAAO;QACpB,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,qCAAqC,EAAE,MAAM,WAAW,CAAC,EAAE;gBACzF,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;YAC/F,IAAI,MAAM,QAAQ,EAAE,MAAM;gBACxB,QAAQ,KAAK,CAAC,kBAAkB,MAAM,QAAQ,CAAC,IAAI;YACrD;YACA,MAAM;QACR;IACF;IAEA,8CAA8C;IAC9C,gBAAgB;QACd,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,iDAAiD;gBAC9E,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;YAChF,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 1517, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/app/jobpostings/page.jsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useState } from 'react';\r\nimport {\r\n  Search,\r\n  MapPin,\r\n  Clock,\r\n  DollarSign,\r\n  Calendar,\r\n  Bookmark,\r\n  BookmarkCheck,\r\n  Building2,\r\n  Users,\r\n  Briefcase,\r\n  GraduationCap,\r\n  Globe,\r\n  Heart,\r\n  ExternalLink,\r\n  CheckCircle,\r\n  AlertCircle,\r\n} from \"lucide-react\";\r\nimport { applyToJob, listJobs } from '../../api/jobs.js';\r\nimport { FormattedJobDescription } from '../../lib/utils';\r\nimport { studentsAPI } from '../../api/students';\r\n\r\nexport default function JobPostings() {\r\n  const [jobs, setJobs] = useState([]);\r\n  const [selectedJob, setSelectedJob] = useState(null);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [jobTypeFilter, setJobTypeFilter] = useState('ALL');\r\n  const [locationFilter, setLocationFilter] = useState('ALL');\r\n\r\n  const [savedJobs, setSavedJobs] = useState(new Set());\r\n  const [isApplying, setIsApplying] = useState(false);\r\n  const [freezeStatus, setFreezeStatus] = useState(null);\r\n  const [jobEligibility, setJobEligibility] = useState(new Map());\r\n  const [appliedJobs, setAppliedJobs] = useState(new Set());\r\n  \r\n  // Mobile view state\r\n  const [isMobileView, setIsMobileView] = useState(false);\r\n  const [showJobDetails, setShowJobDetails] = useState(false);\r\n  \r\n  // Pagination state\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [pagination, setPagination] = useState({\r\n    current_page: 1,\r\n    total_pages: 1,\r\n    total_count: 0,\r\n    per_page: 10,\r\n    has_next: false,\r\n    has_previous: false\r\n  });\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  // Function to fetch jobs with current filters and pagination\r\n  const fetchJobs = async (page = 1) => {\r\n    setIsLoading(true);\r\n    try {\r\n      const params = {\r\n        page: page,\r\n        per_page: 10,\r\n        search: searchTerm,\r\n        job_type: jobTypeFilter,\r\n        location: locationFilter\r\n      };\r\n\r\n      const res = await listJobs(params);\r\n      \r\n      // Extract jobs from paginated response structure\r\n      let jobsData = [];\r\n      if (res.data && res.data.data && Array.isArray(res.data.data)) {\r\n        jobsData = res.data.data;\r\n        // Backend sometimes returns incorrect total_pages, so we calculate it correctly\r\n        const originalPagination = res.data.pagination;\r\n        const correctedTotalPages = Math.ceil(originalPagination.total_count / originalPagination.per_page);\r\n        \r\n        const correctedPagination = {\r\n          ...originalPagination,\r\n          total_pages: correctedTotalPages,\r\n          has_next: originalPagination.current_page < correctedTotalPages,\r\n          has_previous: originalPagination.current_page > 1\r\n        };\r\n        setPagination(correctedPagination);\r\n      } else if (Array.isArray(res.data)) {\r\n        jobsData = res.data;\r\n        console.log('No pagination data in response');\r\n      }\r\n      \r\n      setJobs(jobsData);\r\n      setCurrentPage(page);\r\n      \r\n      // Auto-select first job if available and no job is currently selected\r\n      if (jobsData.length > 0 && !selectedJob) {\r\n        console.log('First job data:', jobsData[0]);\r\n        console.log('Requirements field:', {\r\n          value: jobsData[0].requirements,\r\n          type: typeof jobsData[0].requirements,\r\n          isArray: Array.isArray(jobsData[0].requirements)\r\n        });\r\n        console.log('Additional fields:', jobsData[0].additional_fields);\r\n        console.log('Interview rounds:', jobsData[0].interview_rounds);\r\n        setSelectedJob(jobsData[0]);\r\n      }\r\n      \r\n    } catch (err) {\r\n      console.error('Failed to load jobs:', err);\r\n      setJobs([]);\r\n      setPagination({\r\n        current_page: 1,\r\n        total_pages: 1,\r\n        total_count: 0,\r\n        per_page: 10,\r\n        has_next: false,\r\n        has_previous: false\r\n      });\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n// Removed modal application form state - now using dedicated page\r\n\r\n  // Check freeze status and job eligibility\r\n  const checkFreezeStatus = async () => {\r\n    try {\r\n      const status = await studentsAPI.getFreezeStatus();\r\n      setFreezeStatus(status);\r\n    } catch (err) {\r\n      console.error('Failed to fetch freeze status:', err);\r\n      // If user doesn't have student profile, set a default status\r\n      if (err.response?.status === 400 || err.response?.status === 404) {\r\n        setFreezeStatus({\r\n          freeze_status: 'none',\r\n          freeze_reason: null\r\n        });\r\n      }\r\n    }\r\n  };\r\n\r\n  // Fetch applied jobs\r\n  const fetchAppliedJobs = async () => {\r\n    try {\r\n      const appliedJobsData = await studentsAPI.getAppliedJobs();\r\n      // Extract job IDs from the applied jobs data\r\n      const appliedJobIds = new Set();\r\n      if (appliedJobsData && appliedJobsData.results) {\r\n        appliedJobsData.results.forEach(application => {\r\n          if (application.job && application.job.id) {\r\n            appliedJobIds.add(application.job.id);\r\n          }\r\n        });\r\n      }\r\n      setAppliedJobs(appliedJobIds);\r\n    } catch (err) {\r\n      console.error('Failed to fetch applied jobs:', err);\r\n      // If error, assume no applied jobs\r\n      setAppliedJobs(new Set());\r\n    }\r\n  };\r\n\r\n  const checkJobEligibility = async (jobId) => {\r\n    try {\r\n      const eligibility = await studentsAPI.canApplyToJob(jobId);\r\n      setJobEligibility(prev => new Map(prev.set(jobId, eligibility)));\r\n      return eligibility;\r\n    } catch (err) {\r\n      console.error('Failed to check job eligibility:', err);\r\n\r\n      // Handle specific error cases\r\n      if (err.response?.status === 400 && err.response?.data?.reason) {\r\n        // User doesn't have student profile - return error state\r\n        const errorEligibility = {\r\n          can_apply: false,\r\n          reason: err.response.data.reason\r\n        };\r\n        setJobEligibility(prev => new Map(prev.set(jobId, errorEligibility)));\r\n        return errorEligibility;\r\n      }\r\n\r\n      // For other errors, default to allowing application\r\n      return { can_apply: true };\r\n    }\r\n  };\r\n\r\n  // Helper function to check if a job has restrictions\r\n  const hasJobRestrictions = (jobId) => {\r\n    const eligibility = jobEligibility.get(jobId);\r\n    return eligibility && !eligibility.can_apply;\r\n  };\r\n\r\n  // Helper function to get restriction message\r\n  const getRestrictionMessage = (jobId) => {\r\n    const eligibility = jobEligibility.get(jobId);\r\n    if (eligibility && !eligibility.can_apply) {\r\n      return eligibility.reason || 'You have restrictions for this job';\r\n    }\r\n    return null;\r\n  };\r\n\r\n  // Helper function to check if already applied to a job\r\n  const hasAppliedToJob = (jobId) => {\r\n    return appliedJobs.has(jobId);\r\n  };\r\n\r\n  // Helper function to get apply button text and state\r\n  const getApplyButtonState = (jobId) => {\r\n    if (hasAppliedToJob(jobId)) {\r\n      return {\r\n        text: 'Already Applied',\r\n        disabled: true,\r\n        className: 'px-6 py-2 bg-gray-400 text-white rounded-lg cursor-not-allowed',\r\n        reason: 'You have already applied to this job'\r\n      };\r\n    }\r\n\r\n    const eligibility = jobEligibility.get(jobId);\r\n    if (eligibility && !eligibility.can_apply) {\r\n      return {\r\n        text: 'Cannot Apply',\r\n        disabled: true,\r\n        className: 'px-6 py-2 bg-red-400 text-white rounded-lg cursor-not-allowed',\r\n        reason: eligibility.reason\r\n      };\r\n    }\r\n\r\n    return {\r\n      text: isApplying ? 'Applying...' : 'Apply Now',\r\n      disabled: isApplying,\r\n      className: 'px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium',\r\n      reason: null\r\n    };\r\n  };\r\n\r\n  const applyJob = async () => {\r\n    // If no job is selected, return early\r\n    if (!selectedJob) return;\r\n\r\n    // Check if already applied\r\n    if (hasAppliedToJob(selectedJob.id)) {\r\n      alert('You have already applied to this job.');\r\n      return;\r\n    }\r\n\r\n    // Check eligibility before navigating\r\n    const eligibility = await checkJobEligibility(selectedJob.id);\r\n\r\n    if (!eligibility.can_apply) {\r\n      // Show a brief message and still navigate to let the application page handle the detailed error\r\n      alert('You have restrictions that may prevent you from applying to this job. Please check the application page for details.');\r\n    }\r\n\r\n    // Navigate to the dedicated application page\r\n    window.location.href = `/jobpostings/${selectedJob.id}/apply`;\r\n  };\r\n\r\n// Removed modal application form handlers - now using dedicated page\r\n\r\n\r\n  // Initial load\r\n  useEffect(() => {\r\n    fetchJobs(1);\r\n    checkFreezeStatus();\r\n    fetchAppliedJobs();\r\n\r\n    // Load saved jobs from localStorage\r\n    const saved = localStorage.getItem('savedJobs');\r\n    if (saved) {\r\n      setSavedJobs(new Set(JSON.parse(saved)));\r\n    }\r\n  }, []);\r\n\r\n  // Refresh applied jobs when page becomes visible (e.g., returning from application page)\r\n  useEffect(() => {\r\n    const handleVisibilityChange = () => {\r\n      if (!document.hidden) {\r\n        fetchAppliedJobs();\r\n      }\r\n    };\r\n\r\n    document.addEventListener('visibilitychange', handleVisibilityChange);\r\n    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);\r\n  }, []);\r\n\r\n  // Debounced search effect\r\n  useEffect(() => {\r\n    const timeoutId = setTimeout(() => {\r\n      if (currentPage === 1) {\r\n        fetchJobs(1);\r\n      } else {\r\n        setCurrentPage(1);\r\n        fetchJobs(1);\r\n      }\r\n    }, 500); // 500ms debounce\r\n\r\n    return () => clearTimeout(timeoutId);\r\n  }, [searchTerm, jobTypeFilter, locationFilter]);\r\n\r\n  // Check for mobile view on mount and resize\r\n  useEffect(() => {\r\n    const checkMobileView = () => {\r\n      setIsMobileView(window.innerWidth < 768);\r\n    };\r\n    \r\n    checkMobileView();\r\n    window.addEventListener('resize', checkMobileView);\r\n    \r\n    return () => window.removeEventListener('resize', checkMobileView);\r\n  }, []);\r\n\r\n  // Stats based on pagination data\r\n  const jobsArray = Array.isArray(jobs) ? jobs : [];\r\n  \r\n  // Pagination is working correctly now\r\n  \r\n  const stats = {\r\n    total: pagination.total_count,\r\n    internships: jobsArray.filter(job => job.job_type === 'INTERNSHIP').length,\r\n    fullTime: jobsArray.filter(job => job.job_type === 'FULL_TIME').length,\r\n    remote: jobsArray.filter(job => (job.location || '').toLowerCase().includes('remote')).length,\r\n  };\r\n\r\n  // Use jobs directly since filtering is done server-side\r\n  const filteredJobs = jobsArray;\r\n\r\n  const toggleSaveJob = (jobId) => {\r\n    const newSavedJobs = new Set(savedJobs);\r\n    if (newSavedJobs.has(jobId)) {\r\n      newSavedJobs.delete(jobId);\r\n    } else {\r\n      newSavedJobs.add(jobId);\r\n    }\r\n    setSavedJobs(newSavedJobs);\r\n    localStorage.setItem('savedJobs', JSON.stringify([...newSavedJobs]));\r\n  };\r\n\r\n  // Mobile-specific functions\r\n  const handleJobSelect = (job) => {\r\n    setSelectedJob(job);\r\n    if (isMobileView) {\r\n      setShowJobDetails(true);\r\n    }\r\n  };\r\n\r\n  const handleBackToList = () => {\r\n    setShowJobDetails(false);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {/* Header with stats */}\r\n      <div className=\"mb-6\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <div>\r\n            <h1 className=\"text-2xl font-bold text-gray-900\">Discover Jobs</h1>\r\n            <p className=\"text-sm text-gray-600\">Find your next opportunity from top companies</p>\r\n          </div>\r\n          <div className=\"hidden md:flex items-center gap-6\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <div className=\"p-2 bg-blue-100 rounded-lg\">\r\n                <Briefcase className=\"w-4 h-4 text-blue-600\" />\r\n              </div>\r\n              <div>\r\n                <p className=\"text-lg font-bold text-gray-900\">{stats.total}</p>\r\n                <p className=\"text-xs text-gray-500\">Total Jobs</p>\r\n              </div>\r\n            </div>\r\n            <div className=\"flex items-center gap-2\">\r\n              <div className=\"p-2 bg-green-100 rounded-lg\">\r\n                <GraduationCap className=\"w-4 h-4 text-green-600\" />\r\n              </div>\r\n              <div>\r\n                <p className=\"text-lg font-bold text-gray-900\">{stats.internships}</p>\r\n                <p className=\"text-xs text-gray-500\">Internships</p>\r\n              </div>\r\n            </div>\r\n            <div className=\"flex items-center gap-2\">\r\n              <div className=\"p-2 bg-purple-100 rounded-lg\">\r\n                <Globe className=\"w-4 h-4 text-purple-600\" />\r\n              </div>\r\n              <div>\r\n                <p className=\"text-lg font-bold text-gray-900\">{stats.remote}</p>\r\n                <p className=\"text-xs text-gray-500\">Remote</p>\r\n              </div>\r\n            </div>\r\n            <div className=\"flex items-center gap-2\">\r\n              <div className=\"p-2 bg-amber-100 rounded-lg\">\r\n                <Heart className=\"w-4 h-4 text-amber-600\" />\r\n              </div>\r\n              <div>\r\n                <p className=\"text-lg font-bold text-gray-900\">{savedJobs.size}</p>\r\n                <p className=\"text-xs text-gray-500\">Saved</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Main Content */}\r\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-100 h-[calc(100vh-240px)]\">\r\n        {/* Mobile Layout */}\r\n        {isMobileView ? (\r\n          <div className=\"h-full\">\r\n            {!showJobDetails ? (\r\n              // Mobile Job List View\r\n              <div className=\"h-full flex flex-col\">\r\n                {/* Mobile Header */}\r\n                <div className=\"p-4 border-b border-gray-200\">\r\n                  <div className=\"flex items-center justify-between mb-4\">\r\n                    <h2 className=\"text-lg font-semibold text-gray-900\">Jobs</h2>\r\n                    <span className=\"text-sm text-gray-500\">\r\n                      {pagination.total_count > 0 \r\n                        ? `${filteredJobs.length} of ${pagination.total_count} jobs` \r\n                        : `${filteredJobs.length} positions`}\r\n                    </span>\r\n                  </div>\r\n                  \r\n                  {/* Mobile Search and Filters */}\r\n                  <div className=\"space-y-3\">\r\n                    <div className=\"relative\">\r\n                      <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" />\r\n                      <input\r\n                        type=\"text\"\r\n                        placeholder=\"Search jobs, companies, skills...\"\r\n                        value={searchTerm}\r\n                        onChange={(e) => setSearchTerm(e.target.value)}\r\n                        className=\"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\"\r\n                      />\r\n                    </div>\r\n                    <div className=\"grid grid-cols-2 gap-3\">\r\n                      <select\r\n                        value={jobTypeFilter}\r\n                        onChange={(e) => setJobTypeFilter(e.target.value)}\r\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm\"\r\n                      >\r\n                        <option value=\"ALL\">All Types</option>\r\n                        <option value=\"INTERNSHIP\">Internships</option>\r\n                        <option value=\"FULL_TIME\">Full-time</option>\r\n                        <option value=\"PART_TIME\">Part-time</option>\r\n                      </select>\r\n                      <select\r\n                        value={locationFilter}\r\n                        onChange={(e) => setLocationFilter(e.target.value)}\r\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm\"\r\n                      >\r\n                        <option value=\"ALL\">All Locations</option>\r\n                        <option value=\"Remote\">Remote</option>\r\n                      </select>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                \r\n                {/* Mobile Jobs List */}\r\n                <div className=\"flex-1 overflow-y-auto\">\r\n                  {isLoading ? (\r\n                    <div className=\"flex items-center justify-center p-8\">\r\n                      <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\r\n                    </div>\r\n                  ) : filteredJobs.length > 0 ? (\r\n                    <div className=\"divide-y divide-gray-100\">\r\n                      {filteredJobs.map((job) => {\r\n                        const isSaved = savedJobs.has(job.id);\r\n                        return (\r\n                          <div\r\n                            key={job.id}\r\n                            onClick={() => handleJobSelect(job)}\r\n                            className=\"p-4 cursor-pointer transition-all duration-200 hover:bg-gray-50\"\r\n                          >\r\n                            <div className=\"space-y-3\">\r\n                              <div className=\"flex items-start justify-between\">\r\n                                <div className=\"flex-1\">\r\n                                  <span className={`px-2 py-1 rounded text-xs font-medium ${\r\n                                    job.job_type === 'INTERNSHIP' \r\n                                      ? 'bg-green-100 text-green-800' \r\n                                      : 'bg-blue-100 text-blue-800'\r\n                                  }`}>\r\n                                    {job.job_type || \"Type not specified\"}\r\n                                  </span>\r\n                                  <h3 className=\"font-semibold text-base leading-tight mt-2\">\r\n                                    {job.title || \"Title not available\"}\r\n                                  </h3>\r\n                                </div>\r\n                                <button\r\n                                  onClick={(e) => {\r\n                                    e.stopPropagation();\r\n                                    toggleSaveJob(job.id);\r\n                                  }}\r\n                                  className=\"p-2 rounded-full hover:bg-gray-200 transition-colors\"\r\n                                >\r\n                                  {isSaved ? (\r\n                                    <BookmarkCheck className=\"w-5 h-5 text-blue-600\" />\r\n                                  ) : (\r\n                                    <Bookmark className=\"w-5 h-5 text-gray-400\" />\r\n                                  )}\r\n                                </button>\r\n                              </div>\r\n                              <div className=\"flex items-center gap-2 text-sm text-gray-600\">\r\n                                <Building2 className=\"w-4 h-4\" />\r\n                                <span className=\"font-medium\">{job.company_name || \"N/A\"}</span>\r\n                              </div>\r\n                              <div className=\"flex items-center justify-between text-sm text-gray-500\">\r\n                                <div className=\"flex items-center gap-1\">\r\n                                  <MapPin className=\"w-4 h-4\" />\r\n                                  <span>{job.location || \"N/A\"}</span>\r\n                                </div>\r\n                                <div className=\"flex items-center gap-1\">\r\n                                  <DollarSign className=\"w-4 h-4\" />\r\n                                  <span>\r\n                                    {job.salary_min && job.salary_max\r\n                                      ? `$${job.salary_min} - $${job.salary_max}`\r\n                                      : \"Salary not specified\"}\r\n                                  </span>\r\n                                </div>\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                        );\r\n                      })}\r\n                    </div>\r\n                  ) : (\r\n                    <div className=\"flex flex-col items-center justify-center h-full text-gray-500 p-8\">\r\n                      <Search className=\"w-12 h-12 mb-4 text-gray-300\" />\r\n                      <p className=\"text-lg font-medium\">No jobs found</p>\r\n                      <p className=\"text-sm text-center\">Try adjusting your search or filters</p>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n                \r\n                {/* Mobile Pagination */}\r\n                {pagination.total_pages > 1 && (\r\n                  <div className=\"p-4 border-t border-gray-200 bg-white\">\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <div className=\"text-sm text-gray-600\">\r\n                        Showing {((currentPage - 1) * pagination.per_page) + 1} to {Math.min(currentPage * pagination.per_page, pagination.total_count)} of {pagination.total_count} jobs\r\n                      </div>\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <button\r\n                          onClick={() => fetchJobs(currentPage - 1)}\r\n                          disabled={!pagination.has_previous || isLoading}\r\n                          className=\"px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                        >\r\n                          Previous\r\n                        </button>\r\n                        <span className=\"text-sm text-gray-600\">\r\n                          {currentPage} of {pagination.total_pages}\r\n                        </span>\r\n                        <button\r\n                          onClick={() => fetchJobs(currentPage + 1)}\r\n                          disabled={!pagination.has_next || isLoading}\r\n                          className=\"px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                        >\r\n                          Next\r\n                        </button>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            ) : (\r\n              // Mobile Job Details View\r\n              <div className=\"h-full flex flex-col\">\r\n                {/* Mobile Job Details Header */}\r\n                <div className=\"p-4 border-b border-gray-200 bg-white\">\r\n                  <div className=\"flex items-center justify-between mb-4\">\r\n                    <button\r\n                      onClick={handleBackToList}\r\n                      className=\"flex items-center gap-2 text-blue-600 hover:text-blue-700 font-medium\"\r\n                    >\r\n                      <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\r\n                      </svg>\r\n                      Back to Jobs\r\n                    </button>\r\n                    <button\r\n                      onClick={() => toggleSaveJob(selectedJob.id)}\r\n                      className={`px-4 py-2 rounded-lg border transition-colors ${\r\n                        savedJobs.has(selectedJob.id)\r\n                          ? 'border-blue-500 bg-blue-50 text-blue-700'\r\n                          : 'border-gray-300 hover:bg-gray-50'\r\n                      }`}\r\n                    >\r\n                      {savedJobs.has(selectedJob.id) ? 'Saved' : 'Save'}\r\n                    </button>\r\n                  </div>\r\n                  \r\n                  <div className=\"mb-4\">\r\n                    <h1 className=\"text-xl font-bold text-gray-900 mb-2\">\r\n                      {selectedJob.title || \"Title not available\"}\r\n                    </h1>\r\n                    <div className=\"flex items-center gap-3 text-gray-600 mb-3\">\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <Building2 className=\"w-4 h-4\" />\r\n                        <span className=\"font-semibold\">{selectedJob.company_name || \"Company not available\"}</span>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"flex items-center gap-2\">\r\n                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${\r\n                        selectedJob.job_type === 'INTERNSHIP' \r\n                          ? 'bg-green-100 text-green-800' \r\n                          : 'bg-blue-100 text-blue-800'\r\n                      }`}>\r\n                        {selectedJob.job_type || \"Type not specified\"}\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                  \r\n                  <div className=\"space-y-2\">\r\n                    {selectedJob && (() => {\r\n                      const buttonState = getApplyButtonState(selectedJob.id);\r\n                      const showWarning = (freezeStatus && freezeStatus.freeze_status !== 'none') ||\r\n                                         hasAppliedToJob(selectedJob.id) ||\r\n                                         (buttonState.reason && !hasAppliedToJob(selectedJob.id));\r\n\r\n                      return (\r\n                        <>\r\n                          {showWarning && (\r\n                            <div className={`flex items-center gap-2 text-sm p-2 rounded-lg ${\r\n                              hasAppliedToJob(selectedJob.id)\r\n                                ? 'text-green-600 bg-green-50'\r\n                                : buttonState.reason\r\n                                  ? 'text-red-600 bg-red-50'\r\n                                  : 'text-amber-600 bg-amber-50'\r\n                            }`}>\r\n                              <AlertCircle className=\"w-4 h-4\" />\r\n                              <span>\r\n                                {hasAppliedToJob(selectedJob.id)\r\n                                  ? 'You have already applied to this job'\r\n                                  : buttonState.reason || 'Account restrictions may apply'\r\n                                }\r\n                              </span>\r\n                            </div>\r\n                          )}\r\n                          <button\r\n                            onClick={applyJob}\r\n                            className={`w-full px-6 py-3 rounded-lg transition-colors font-medium ${\r\n                              buttonState.className.replace('px-6 py-2', 'px-6 py-3')\r\n                            }`}\r\n                            disabled={buttonState.disabled}\r\n                          >\r\n                            {buttonState.text}\r\n                          </button>\r\n                        </>\r\n                      );\r\n                    })()}\r\n                  </div>\r\n                </div>\r\n                \r\n                {/* Mobile Job Details Content */}\r\n                <div className=\"flex-1 overflow-y-auto p-4\">\r\n                  <div className=\"space-y-6\">\r\n                    {/* Key Information Grid */}\r\n                    <div className=\"grid grid-cols-2 gap-3\">\r\n                      <div className=\"bg-gray-50 rounded-lg p-3\">\r\n                        <div className=\"flex items-center gap-2 mb-1\">\r\n                          <DollarSign className=\"w-3 h-3 text-green-600\" />\r\n                          <h3 className=\"font-semibold text-gray-900 text-sm\">Salary</h3>\r\n                        </div>\r\n                        <p className=\"text-sm font-bold text-gray-900\">\r\n                          {selectedJob.salary_min && selectedJob.salary_max\r\n                            ? `$${selectedJob.salary_min} - $${selectedJob.salary_max}`\r\n                            : \"Salary not specified\"}\r\n                        </p>\r\n                        <p className=\"text-xs text-gray-500\">per {selectedJob.per_unit || \"N/A\"}</p>\r\n                      </div>\r\n                      <div className=\"bg-gray-50 rounded-lg p-3\">\r\n                        <div className=\"flex items-center gap-2 mb-1\">\r\n                          <MapPin className=\"w-3 h-3 text-red-600\" />\r\n                          <h3 className=\"font-semibold text-gray-900 text-sm\">Location</h3>\r\n                        </div>\r\n                        <p className=\"text-sm font-bold text-gray-900\">\r\n                          {selectedJob.location || \"Not specified\"}\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                    \r\n                    {/* Job Description */}\r\n                    <div>\r\n                      <h3 className=\"text-base font-semibold text-gray-900 mb-3\">Job Description</h3>\r\n                      <FormattedJobDescription \r\n                        description={selectedJob.description} \r\n                        className=\"text-sm\"\r\n                      />\r\n                    </div>\r\n                    \r\n                    {/* Requirements */}\r\n                    <div>\r\n                      <h3 className=\"text-base font-semibold text-gray-900 mb-3\">Requirements</h3>\r\n                      <div className=\"text-sm text-gray-700\">\r\n                        {selectedJob.requirements ? (\r\n                          typeof selectedJob.requirements === 'string' ? (\r\n                            <ul className=\"space-y-2\">\r\n                              {selectedJob.requirements.split(',').map((req, index) => (\r\n                                <li key={index} className=\"flex items-start gap-2\">\r\n                                  <CheckCircle className=\"w-4 h-4 text-green-600 mt-0.5 flex-shrink-0\" />\r\n                                  <span>{req.trim()}</span>\r\n                                </li>\r\n                              ))}\r\n                            </ul>\r\n                          ) : (\r\n                            <p>{String(selectedJob.requirements)}</p>\r\n                          )\r\n                        ) : (\r\n                          <p>No requirements specified.</p>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                    \r\n                    {/* Skills */}\r\n                    <div>\r\n                      <h3 className=\"text-base font-semibold text-gray-900 mb-3\">Required Skills</h3>\r\n                      <div className=\"flex flex-wrap gap-2\">\r\n                        {selectedJob.requirements && typeof selectedJob.requirements === 'string' ? (\r\n                          selectedJob.requirements.split(',').map((skill, index) => (\r\n                            <span\r\n                              key={index}\r\n                              className=\"px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium\"\r\n                            >\r\n                              {skill.trim()}\r\n                            </span>\r\n                          ))\r\n                        ) : (\r\n                          <span className=\"text-sm text-gray-500\">No skills specified.</span>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Interview Process */}\r\n                    {selectedJob.interview_rounds && selectedJob.interview_rounds.length > 0 && (\r\n                      <div>\r\n                        <h3 className=\"text-base font-semibold text-gray-900 mb-3\">Interview Process</h3>\r\n                        <div className=\"bg-gray-50 rounded-lg p-4\">\r\n                          <div className=\"space-y-3\">\r\n                            {selectedJob.interview_rounds.map((round, index) => (\r\n                              <div key={index} className=\"flex items-center justify-between bg-white rounded-lg p-3 border border-gray-200\">\r\n                                <div className=\"flex items-center gap-3\">\r\n                                  <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\r\n                                    <span className=\"text-sm font-semibold text-blue-600\">{index + 1}</span>\r\n                                  </div>\r\n                                  <div>\r\n                                    <p className=\"font-medium text-gray-900\">{round.name}</p>\r\n                                    {round.date && (\r\n                                      <p className=\"text-sm text-gray-600\">\r\n                                        {new Date(round.date).toLocaleDateString()}\r\n                                        {round.time && ` at ${round.time}`}\r\n                                      </p>\r\n                                    )}\r\n                                  </div>\r\n                                </div>\r\n                                <div className=\"text-xs text-gray-500\">\r\n                                  Round {index + 1}\r\n                                </div>\r\n                              </div>\r\n                            ))}\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n\r\n                    {/* Additional Requirements - Preview Form */}\r\n                    {selectedJob.additional_fields && selectedJob.additional_fields.length > 0 && (\r\n                      <div>\r\n                        <h3 className=\"text-base font-semibold text-gray-900 mb-3\">Additional Application Fields</h3>\r\n                        <p className=\"text-sm text-gray-600 mb-4\">These fields will be required when applying for this position.</p>\r\n                        <div className=\"bg-white rounded-xl border border-gray-200 p-4\">\r\n                          <div className=\"space-y-4\">\r\n                            {selectedJob.additional_fields.map((field, index) => {\r\n                              const renderField = () => {\r\n                                switch (field.type) {\r\n                                  case 'text':\r\n                                    return (\r\n                                      <div className=\"space-y-2\">\r\n                                        <label className=\"block text-sm font-semibold text-gray-900\">\r\n                                          {field.label} {field.required && <span className=\"text-red-500\">*</span>}\r\n                                        </label>\r\n                                        <input\r\n                                          type=\"text\"\r\n                                          placeholder={`Enter ${field.label.toLowerCase()}`}\r\n                                          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm\"\r\n                                          disabled\r\n                                        />\r\n                                      </div>\r\n                                    );\r\n                                  \r\n                                  case 'number':\r\n                                    return (\r\n                                      <div className=\"space-y-2\">\r\n                                        <label className=\"block text-sm font-semibold text-gray-900\">\r\n                                          {field.label} {field.required && <span className=\"text-red-500\">*</span>}\r\n                                        </label>\r\n                                        <input\r\n                                          type=\"number\"\r\n                                          placeholder=\"e.g., 5\"\r\n                                          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm\"\r\n                                          disabled\r\n                                        />\r\n                                      </div>\r\n                                    );\r\n                                  \r\n                                  case 'file':\r\n                                    return (\r\n                                      <div className=\"space-y-2\">\r\n                                        <label className=\"block text-sm font-semibold text-gray-900\">\r\n                                          {field.label} {field.required && <span className=\"text-red-500\">*</span>}\r\n                                        </label>\r\n                                        <div className=\"w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 border-dashed\">\r\n                                          <div className=\"flex items-center justify-center text-gray-500\">\r\n                                            <svg className=\"w-5 h-5 mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\" />\r\n                                            </svg>\r\n                                            <span className=\"text-sm font-medium\">Upload {field.label.toLowerCase()}</span>\r\n                                          </div>\r\n                                          <div className=\"text-center mt-2\">\r\n                                            <span className=\"text-xs text-gray-400\">No file chosen</span>\r\n                                          </div>\r\n                                        </div>\r\n                                      </div>\r\n                                    );\r\n                                  \r\n                                  case 'multiple_choice':\r\n                                    return (\r\n                                      <div className=\"space-y-2\">\r\n                                        <label className=\"block text-sm font-semibold text-gray-900\">\r\n                                          {field.label} {field.required && <span className=\"text-red-500\">*</span>}\r\n                                        </label>\r\n                                        <select\r\n                                          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm\"\r\n                                          disabled\r\n                                        >\r\n                                          <option value=\"\">Select {field.label.toLowerCase()}</option>\r\n                                          {field.options?.map((option, optIndex) => (\r\n                                            <option key={optIndex} value={option}>\r\n                                              {option}\r\n                                            </option>\r\n                                          ))}\r\n                                        </select>\r\n                                      </div>\r\n                                    );\r\n                                  \r\n                                  case 'textarea':\r\n                                    return (\r\n                                      <div className=\"space-y-2\">\r\n                                        <label className=\"block text-sm font-semibold text-gray-900\">\r\n                                          {field.label} {field.required && <span className=\"text-red-500\">*</span>}\r\n                                        </label>\r\n                                        <textarea\r\n                                          rows={4}\r\n                                          placeholder={`Enter ${field.label.toLowerCase()}`}\r\n                                          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm resize-none\"\r\n                                          disabled\r\n                                        />\r\n                                      </div>\r\n                                    );\r\n                                  \r\n                                  default:\r\n                                    return (\r\n                                      <div className=\"space-y-2\">\r\n                                        <label className=\"block text-sm font-semibold text-gray-900\">\r\n                                          {field.label} {field.required && <span className=\"text-red-500\">*</span>}\r\n                                        </label>\r\n                                        <input\r\n                                          type=\"text\"\r\n                                          placeholder=\"Unknown field type\"\r\n                                          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-100 opacity-50 cursor-not-allowed text-sm\"\r\n                                          disabled\r\n                                        />\r\n                                      </div>\r\n                                    );\r\n                                }\r\n                              };\r\n\r\n                              return (\r\n                                <div key={field.id || index} className=\"bg-gray-50 rounded-lg p-4\">\r\n                                  {renderField()}\r\n                                </div>\r\n                              );\r\n                            })}\r\n                          </div>\r\n                          <div className=\"mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200\">\r\n                            <div className=\"flex items-start gap-3\">\r\n                              <div className=\"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5\">\r\n                                <svg className=\"w-4 h-4 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                                </svg>\r\n                              </div>\r\n                              <div>\r\n                                <p className=\"text-sm font-semibold text-blue-900 mb-1\">Preview Mode</p>\r\n                                <p className=\"text-sm text-blue-800 leading-relaxed\">These are preview fields. You'll be able to fill them out when you apply for this position.</p>\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        ) : (\r\n          // Desktop Layout (existing code)\r\n          <div className=\"flex h-full\">\r\n            {/* Jobs List */}\r\n            <div className=\"w-2/5 border-r border-gray-200 flex flex-col\">\r\n              {/* Search and Filter Header */}\r\n              <div className=\"p-3 border-b border-gray-200 space-y-3\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <h2 className=\"text-lg font-semibold text-gray-900\">Jobs</h2>\r\n                  <span className=\"text-sm text-gray-500\">\r\n                    {pagination.total_count > 0 \r\n                      ? `${filteredJobs.length} of ${pagination.total_count} jobs` \r\n                      : `${filteredJobs.length} positions`}\r\n                  </span>\r\n                </div>\r\n                {/* Search Bar */}\r\n                <div className=\"relative\">\r\n                  <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" />\r\n                  <input\r\n                    type=\"text\"\r\n                    placeholder=\"Search jobs, companies, skills...\"\r\n                    value={searchTerm}\r\n                    onChange={(e) => setSearchTerm(e.target.value)}\r\n                    className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\"\r\n                  />\r\n                </div>\r\n                {/* Filters */}\r\n                <div className=\"flex gap-2\">\r\n                  <select\r\n                    value={jobTypeFilter}\r\n                    onChange={(e) => setJobTypeFilter(e.target.value)}\r\n                    className=\"flex-1 px-3 py-1.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm\"\r\n                  >\r\n                    <option value=\"ALL\">All Types</option>\r\n                    <option value=\"INTERNSHIP\">Internships</option>\r\n                    <option value=\"FULL_TIME\">Full-time</option>\r\n                    <option value=\"PART_TIME\">Part-time</option>\r\n                  </select>\r\n                  <select\r\n                    value={locationFilter}\r\n                    onChange={(e) => setLocationFilter(e.target.value)}\r\n                    className=\"flex-1 px-3 py-1.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm\"\r\n                  >\r\n                    <option value=\"ALL\">All Locations</option>\r\n                    <option value=\"Remote\">Remote</option>\r\n                  </select>\r\n                </div>\r\n              </div>\r\n              {/* Jobs List */}\r\n              <div className=\"flex-1 overflow-y-auto\">\r\n                {isLoading ? (\r\n                  <div className=\"flex items-center justify-center p-8\">\r\n                    <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\r\n                  </div>\r\n                ) : filteredJobs.length > 0 ? (\r\n                  <div className=\"divide-y divide-gray-100\">\r\n                    {filteredJobs.map((job) => {\r\n                      const isSelected = job.id === selectedJob?.id;\r\n                      const isSaved = savedJobs.has(job.id);\r\n                      return (\r\n                        <div\r\n                          key={job.id}\r\n                          onClick={() => handleJobSelect(job)}\r\n                          className={`p-3 cursor-pointer transition-all duration-200 hover:bg-gray-50 ${\r\n                            isSelected ? 'bg-blue-50 border-r-2 border-blue-500' : ''\r\n                          }`}\r\n                        >\r\n                          <div className=\"space-y-2\">\r\n                            <div className=\"flex items-start justify-between\">\r\n                              <div className=\"flex-1\">\r\n                                <span className={`px-2 py-0.5 rounded text-xs font-medium ${\r\n                                  job.job_type === 'INTERNSHIP' \r\n                                    ? 'bg-green-100 text-green-800' \r\n                                    : 'bg-blue-100 text-blue-800'\r\n                                }`}>\r\n                                  {job.job_type || \"Type not specified\"}\r\n                                </span>\r\n                                <h3 className={`font-semibold text-sm leading-tight ${\r\n                                  isSelected ? 'text-blue-900' : 'text-gray-900'\r\n                                }`}>\r\n                                  {job.title || \"Title not available\"}\r\n                                </h3>\r\n                              </div>\r\n                              <button\r\n                                onClick={(e) => {\r\n                                  e.stopPropagation();\r\n                                  toggleSaveJob(job.id);\r\n                                }}\r\n                                className=\"p-1 rounded-full hover:bg-gray-200 transition-colors\"\r\n                              >\r\n                                {isSaved ? (\r\n                                  <BookmarkCheck className=\"w-4 h-4 text-blue-600\" />\r\n                                ) : (\r\n                                  <Bookmark className=\"w-4 h-4 text-gray-400\" />\r\n                                )}\r\n                              </button>\r\n                            </div>\r\n                            <div className=\"flex items-center gap-2 text-sm text-gray-600\">\r\n                              <Building2 className=\"w-3 h-3\" />\r\n                              <span className=\"font-medium\">{job.company_name || \"N/A\"}</span>\r\n                            </div>\r\n                            <div className=\"flex items-center justify-between text-xs text-gray-500\">\r\n                              <div className=\"flex items-center gap-4\">\r\n                                <div className=\"flex items-center gap-1\">\r\n                                  <MapPin className=\"w-3 h-3\" />\r\n                                  <span>{job.location || \"N/A\"}</span>\r\n                                </div>\r\n                                <div className=\"flex items-center gap-1\">\r\n                                  <DollarSign className=\"w-3 h-3\" />\r\n                                  <span>\r\n                                    {job.salary_min && job.salary_max\r\n                                      ? `$${job.salary_min} - $${job.salary_max}`\r\n                                      : \"Salary not specified\"}\r\n                                  </span>\r\n                                </div>\r\n                              </div>\r\n                              <div className=\"flex items-center gap-1\">\r\n                                <Clock className=\"w-3 h-3\" />\r\n                                <span>—</span>\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      );\r\n                    })}\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"flex flex-col items-center justify-center h-full text-gray-500\">\r\n                    <Search className=\"w-12 h-12 mb-4 text-gray-300\" />\r\n                    <p className=\"text-lg font-medium\">No jobs found</p>\r\n                    <p className=\"text-sm\">Try adjusting your search or filters</p>\r\n                  </div>\r\n                )}\r\n              </div>\r\n              \r\n              {/* Pagination Controls */}\r\n              {pagination.total_pages > 1 && (\r\n                <div className=\"p-4 border-t border-gray-200 bg-white shadow-sm\">\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <div className=\"text-sm text-gray-600\">\r\n                      Showing {((currentPage - 1) * pagination.per_page) + 1} to {Math.min(currentPage * pagination.per_page, pagination.total_count)} of {pagination.total_count} jobs\r\n                    </div>\r\n                    <div className=\"flex items-center gap-2\">\r\n                      <button\r\n                        onClick={() => fetchJobs(currentPage - 1)}\r\n                        disabled={!pagination.has_previous || isLoading}\r\n                        className=\"px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                      >\r\n                        Previous\r\n                      </button>\r\n                      \r\n                      <div className=\"flex items-center gap-1\">\r\n                        {/* Show page numbers */}\r\n                        {Array.from({ length: Math.min(pagination.total_pages, 5) }, (_, i) => {\r\n                          let pageNum;\r\n                          if (pagination.total_pages <= 5) {\r\n                            pageNum = i + 1;\r\n                          } else if (currentPage <= 3) {\r\n                            pageNum = i + 1;\r\n                          } else if (currentPage >= pagination.total_pages - 2) {\r\n                            pageNum = pagination.total_pages - 4 + i;\r\n                          } else {\r\n                            pageNum = currentPage - 2 + i;\r\n                          }\r\n                          \r\n                          return (\r\n                            <button\r\n                              key={pageNum}\r\n                              onClick={() => fetchJobs(pageNum)}\r\n                              disabled={isLoading}\r\n                              className={`px-3 py-1 text-sm border rounded-md ${\r\n                                pageNum === currentPage\r\n                                  ? 'border-blue-500 bg-blue-50 text-blue-700'\r\n                                  : 'border-gray-300 hover:bg-gray-100'\r\n                              } disabled:opacity-50 disabled:cursor-not-allowed`}\r\n                            >\r\n                              {pageNum}\r\n                            </button>\r\n                          );\r\n                        })}\r\n                      </div>\r\n                      \r\n                      <button\r\n                        onClick={() => fetchJobs(currentPage + 1)}\r\n                        disabled={!pagination.has_next || isLoading}\r\n                        className=\"px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                      >\r\n                        Next\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Job Details */}\r\n            <div className=\"flex-1 flex flex-col\">\r\n              {selectedJob ? (\r\n                <>\r\n                  {/* Job Header */}\r\n                  <div className=\"p-4 border-b border-gray-200\">\r\n                    <div className=\"flex items-start justify-between mb-4\">\r\n                      <div className=\"flex-1\">\r\n                        <h1 className=\"text-xl font-bold text-gray-900 mb-1\">\r\n                          {selectedJob.title || \"Title not available\"}\r\n                        </h1>\r\n                        <div className=\"flex items-center gap-4 text-gray-600 mb-2\">\r\n                          <div className=\"flex items-center gap-2\">\r\n                            <Building2 className=\"w-4 h-4\" />\r\n                            <span className=\"font-semibold\">{selectedJob.company_name || \"Company not available\"}</span>\r\n                          </div>\r\n                          <div className=\"flex items-center gap-2\">\r\n                            <MapPin className=\"w-4 h-4\" />\r\n                            <span>{selectedJob.location || \"Location not available\"}</span>\r\n                          </div>\r\n                        </div>\r\n                        <div className=\"flex items-center gap-2 mt-2\">\r\n                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${\r\n                            selectedJob.job_type === 'INTERNSHIP' \r\n                              ? 'bg-green-100 text-green-800' \r\n                              : 'bg-blue-100 text-blue-800'\r\n                          }`}>\r\n                            {selectedJob.job_type || \"Type not specified\"}\r\n                          </span>\r\n                          {/* Placeholders for more tags */}\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"space-y-2\">\r\n                        {selectedJob && (() => {\r\n                          const buttonState = getApplyButtonState(selectedJob.id);\r\n                          const showWarning = (freezeStatus && freezeStatus.freeze_status !== 'none') ||\r\n                                             hasAppliedToJob(selectedJob.id) ||\r\n                                             (buttonState.reason && !hasAppliedToJob(selectedJob.id));\r\n\r\n                          return (\r\n                            <>\r\n                              {showWarning && (\r\n                                <div className={`flex items-center gap-2 text-sm p-2 rounded-lg ${\r\n                                  hasAppliedToJob(selectedJob.id)\r\n                                    ? 'text-green-600 bg-green-50'\r\n                                    : buttonState.reason\r\n                                      ? 'text-red-600 bg-red-50'\r\n                                      : 'text-amber-600 bg-amber-50'\r\n                                }`}>\r\n                                  <AlertCircle className=\"w-4 h-4\" />\r\n                                  <span>\r\n                                    {hasAppliedToJob(selectedJob.id)\r\n                                      ? 'You have already applied to this job'\r\n                                      : buttonState.reason || 'Account restrictions may apply'\r\n                                    }\r\n                                  </span>\r\n                                </div>\r\n                              )}\r\n                              <div className=\"flex gap-2\">\r\n                                <button\r\n                                  onClick={() => toggleSaveJob(selectedJob.id)}\r\n                                  className={`px-4 py-2 rounded-lg border transition-colors ${\r\n                                    savedJobs.has(selectedJob.id)\r\n                                      ? 'border-blue-500 bg-blue-50 text-blue-700'\r\n                                      : 'border-gray-300 hover:bg-gray-50'\r\n                                  }`}\r\n                                >\r\n                                  {savedJobs.has(selectedJob.id) ? 'Saved' : 'Save'}\r\n                                </button>\r\n                                <button\r\n                                  onClick={applyJob}\r\n                                  className={buttonState.className}\r\n                                  disabled={buttonState.disabled}\r\n                                >\r\n                                  {buttonState.text}\r\n                                </button>\r\n                              </div>\r\n                            </>\r\n                          );\r\n                        })()}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Job Content */}\r\n                  <div className=\"flex-1 overflow-y-auto p-4\">\r\n                    <div className=\"space-y-4\">\r\n                      {/* Key Information Grid */}\r\n                      <div className=\"grid grid-cols-2 md:grid-cols-4 gap-3\">\r\n                        <div className=\"bg-gray-50 rounded-lg p-3\">\r\n                          <div className=\"flex items-center gap-2 mb-1\">\r\n                            <DollarSign className=\"w-3 h-3 text-green-600\" />\r\n                            <h3 className=\"font-semibold text-gray-900 text-sm\">Salary</h3>\r\n                          </div>\r\n                          <p className=\"text-sm font-bold text-gray-900\">\r\n                            {selectedJob.salary_min && selectedJob.salary_max\r\n                              ? `$${selectedJob.salary_min} - $${selectedJob.salary_max}`\r\n                              : \"Salary not specified\"}\r\n                          </p>\r\n                          <p className=\"text-xs text-gray-500\">per {selectedJob.per_unit || \"N/A\"}</p>\r\n                        </div>\r\n                        <div className=\"bg-gray-50 rounded-lg p-3\">\r\n                          <div className=\"flex items-center gap-2 mb-1\">\r\n                            <Calendar className=\"w-3 h-3 text-red-600\" />\r\n                            <h3 className=\"font-semibold text-gray-900 text-sm\">Deadline</h3>\r\n                          </div>\r\n                          <p className=\"text-sm font-bold text-gray-900\">\r\n                            {selectedJob.application_deadline\r\n                              ? new Date(selectedJob.application_deadline).toLocaleDateString()\r\n                              : \"No deadline specified\"}\r\n                          </p>\r\n                        </div>\r\n                        <div className=\"bg-gray-50 rounded-lg p-3\">\r\n                          <div className=\"flex items-center gap-2 mb-1\">\r\n                            <Clock className=\"w-3 h-3 text-blue-600\" />\r\n                            <h3 className=\"font-semibold text-gray-900 text-sm\">Duration</h3>\r\n                          </div>\r\n                          <p className=\"text-sm font-bold text-gray-900\">\r\n                            {selectedJob.duration || \"Not specified\"}\r\n                          </p>\r\n                        </div>\r\n                        <div className=\"bg-gray-50 rounded-lg p-3\">\r\n                          <div className=\"flex items-center gap-2 mb-1\">\r\n                            <Users className=\"w-3 h-3 text-purple-600\" />\r\n                            <h3 className=\"font-semibold text-gray-900 text-sm\">Company Size</h3>\r\n                          </div>\r\n                          <p className=\"text-sm font-bold text-gray-900\">\r\n                            {\"N/A\"}\r\n                          </p>\r\n                        </div>\r\n                      </div>\r\n                      {/* Job Description */}\r\n                      <div>\r\n                        <h3 className=\"text-base font-semibold text-gray-900 mb-2\">Job Description</h3>\r\n                        <FormattedJobDescription \r\n                          description={selectedJob.description} \r\n                          className=\"text-sm\"\r\n                        />\r\n                      </div>\r\n                      {/* Requirements */}\r\n                      <div>\r\n                        <h3 className=\"text-base font-semibold text-gray-900 mb-2\">Requirements</h3>\r\n                        <div className=\"text-sm text-gray-700\">\r\n                          {selectedJob.requirements ? (\r\n                            typeof selectedJob.requirements === 'string' ? (\r\n                              <ul className=\"space-y-1\">\r\n                                {selectedJob.requirements.split(',').map((req, index) => (\r\n                                  <li key={index} className=\"flex items-start gap-2\">\r\n                                    <CheckCircle className=\"w-4 h-4 text-green-600 mt-0.5 flex-shrink-0\" />\r\n                                    <span>{req.trim()}</span>\r\n                                  </li>\r\n                                ))}\r\n                              </ul>\r\n                            ) : (\r\n                              <p>{String(selectedJob.requirements)}</p>\r\n                            )\r\n                          ) : (\r\n                            <p>No requirements specified.</p>\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n                      {/* Skills */}\r\n                      <div>\r\n                        <h3 className=\"text-base font-semibold text-gray-900 mb-2\">Required Skills</h3>\r\n                        <div className=\"flex flex-wrap gap-2\">\r\n                          {selectedJob.requirements && typeof selectedJob.requirements === 'string' ? (\r\n                            selectedJob.requirements.split(',').map((skill, index) => (\r\n                              <span\r\n                                key={index}\r\n                                className=\"px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium\"\r\n                              >\r\n                                {skill.trim()}\r\n                              </span>\r\n                            ))\r\n                          ) : (\r\n                            <span className=\"text-sm text-gray-500\">No skills specified.</span>\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* Interview Process */}\r\n                      {selectedJob.interview_rounds && selectedJob.interview_rounds.length > 0 && (\r\n                        <div>\r\n                          <h3 className=\"text-base font-semibold text-gray-900 mb-2\">Interview Process</h3>\r\n                          <div className=\"bg-gray-50 rounded-lg p-4\">\r\n                            <div className=\"space-y-3\">\r\n                              {selectedJob.interview_rounds.map((round, index) => (\r\n                                <div key={index} className=\"flex items-center justify-between bg-white rounded-lg p-3 border border-gray-200\">\r\n                                  <div className=\"flex items-center gap-3\">\r\n                                    <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\r\n                                      <span className=\"text-sm font-semibold text-blue-600\">{index + 1}</span>\r\n                                    </div>\r\n                                    <div>\r\n                                      <p className=\"font-medium text-gray-900\">{round.name}</p>\r\n                                      {round.date && (\r\n                                        <p className=\"text-sm text-gray-600\">\r\n                                          {new Date(round.date).toLocaleDateString()}\r\n                                          {round.time && ` at ${round.time}`}\r\n                                        </p>\r\n                                      )}\r\n                                    </div>\r\n                                  </div>\r\n                                  <div className=\"text-xs text-gray-500\">\r\n                                    Round {index + 1}\r\n                                  </div>\r\n                                </div>\r\n                              ))}\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* Additional Requirements - Preview Form */}\r\n                      {selectedJob.additional_fields && selectedJob.additional_fields.length > 0 && (\r\n                        <div>\r\n                          <h3 className=\"text-base font-semibold text-gray-900 mb-2\">Additional Application Fields</h3>\r\n                          <p className=\"text-sm text-gray-600 mb-6\">These fields will be required when applying for this position.</p>\r\n                          <div className=\"bg-white rounded-xl border border-gray-200 p-6\">\r\n                            <div className=\"space-y-6\">\r\n                              {selectedJob.additional_fields.map((field, index) => {\r\n                                const renderField = () => {\r\n                                  switch (field.type) {\r\n                                    case 'text':\r\n                                      return (\r\n                                        <div className=\"space-y-2\">\r\n                                          <label className=\"block text-sm font-semibold text-gray-900\">\r\n                                            {field.label} {field.required && <span className=\"text-red-500\">*</span>}\r\n                                          </label>\r\n                                          <input\r\n                                            type=\"text\"\r\n                                            placeholder={`Enter ${field.label.toLowerCase()}`}\r\n                                            className=\"w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm\"\r\n                                          />\r\n                                        </div>\r\n                                      );\r\n                                    \r\n                                    case 'number':\r\n                                      return (\r\n                                        <div className=\"space-y-2\">\r\n                                          <label className=\"block text-sm font-semibold text-gray-900\">\r\n                                            {field.label} {field.required && <span className=\"text-red-500\">*</span>}\r\n                                          </label>\r\n                                          <input\r\n                                            type=\"number\"\r\n                                            placeholder=\"e.g., 5\"\r\n                                            className=\"w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm\"\r\n                                          />\r\n                                        </div>\r\n                                      );\r\n                                    \r\n                                    case 'file':\r\n                                      return (\r\n                                        <div className=\"space-y-2\">\r\n                                          <label className=\"block text-sm font-semibold text-gray-900\">\r\n                                            {field.label} {field.required && <span className=\"text-red-500\">*</span>}\r\n                                          </label>\r\n                                          <div className=\"w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 border-dashed\">\r\n                                            <div className=\"flex items-center justify-center text-gray-500\">\r\n                                              <svg className=\"w-5 h-5 mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\" />\r\n                                              </svg>\r\n                                              <span className=\"text-sm font-medium\">Upload {field.label.toLowerCase()}</span>\r\n                                            </div>\r\n                                            <div className=\"text-center mt-2\">\r\n                                              <span className=\"text-xs text-gray-400\">No file chosen</span>\r\n                                            </div>\r\n                                          </div>\r\n                                        </div>\r\n                                      );\r\n                                    \r\n                                    case 'multiple_choice':\r\n                                      return (\r\n                                        <div className=\"space-y-2\">\r\n                                          <label className=\"block text-sm font-semibold text-gray-900\">\r\n                                            {field.label} {field.required && <span className=\"text-red-500\">*</span>}\r\n                                          </label>\r\n                                          <select\r\n                                            className=\"w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm\"\r\n                                          >\r\n                                            <option value=\"\">Select {field.label.toLowerCase()}</option>\r\n                                            {field.options?.map((option, optIndex) => (\r\n                                              <option key={optIndex} value={option}>\r\n                                                {option}\r\n                                              </option>\r\n                                            ))}\r\n                                          </select>\r\n                                        </div>\r\n                                      );\r\n                                    \r\n                                    case 'textarea':\r\n                                      return (\r\n                                        <div className=\"space-y-2\">\r\n                                          <label className=\"block text-sm font-semibold text-gray-900\">\r\n                                            {field.label} {field.required && <span className=\"text-red-500\">*</span>}\r\n                                          </label>\r\n                                          <textarea\r\n                                            rows={4}\r\n                                            placeholder={`Enter ${field.label.toLowerCase()}`}\r\n                                            className=\"w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm resize-none\"\r\n                                          />\r\n                                        </div>\r\n                                      );\r\n                                    \r\n                                    default:\r\n                                      return (\r\n                                        <div className=\"space-y-2\">\r\n                                          <label className=\"block text-sm font-semibold text-gray-900\">\r\n                                            {field.label} {field.required && <span className=\"text-red-500\">*</span>}\r\n                                          </label>\r\n                                          <input\r\n                                            type=\"text\"\r\n                                            placeholder=\"Unknown field type\"\r\n                                            className=\"w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-100 opacity-50 cursor-not-allowed text-sm\"\r\n                                          />\r\n                                        </div>\r\n                                      );\r\n                                  }\r\n                                };\r\n\r\n                                return (\r\n                                  <div key={field.id || index} className=\"bg-gray-50 rounded-lg p-4\">\r\n                                    {renderField()}\r\n                                  </div>\r\n                                );\r\n                              })}\r\n                            </div>\r\n                            <div className=\"mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200\">\r\n                              <div className=\"flex items-start gap-3\">\r\n                                <div className=\"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5\">\r\n                                  <svg className=\"w-4 h-4 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                                  </svg>\r\n                                </div>\r\n                                <div>\r\n                                  <p className=\"text-sm font-semibold text-blue-900 mb-1\">Preview Mode</p>\r\n                                  <p className=\"text-sm text-blue-800 leading-relaxed\">These are preview fields. You'll be able to fill them out when you apply for this position.</p>\r\n                                </div>\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n\r\n                    </div>\r\n                  </div>\r\n                </>\r\n              ) : (\r\n                <div className=\"flex-1 flex items-center justify-center\">\r\n                  <div className=\"text-center\">\r\n                    <div className=\"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n                      <Briefcase className=\"w-8 h-8 text-gray-400\" />\r\n                    </div>\r\n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Select a Job</h3>\r\n                    <p className=\"text-gray-600\">Choose a position from the list to view details</p>\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBA;AACA;AACA;AAvBA;;;;;;;AAyBe,SAAS;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IAEnD,oBAAoB;IACpB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,mBAAmB;IACnB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,cAAc;QACd,aAAa;QACb,aAAa;QACb,UAAU;QACV,UAAU;QACV,cAAc;IAChB;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,6DAA6D;IAC7D,MAAM,YAAY,OAAO,OAAO,CAAC;QAC/B,aAAa;QACb,IAAI;YACF,MAAM,SAAS;gBACb,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,UAAU;gBACV,UAAU;YACZ;YAEA,MAAM,MAAM,MAAM,CAAA,GAAA,kHAAA,CAAA,WAAQ,AAAD,EAAE;YAE3B,iDAAiD;YACjD,IAAI,WAAW,EAAE;YACjB,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,MAAM,OAAO,CAAC,IAAI,IAAI,CAAC,IAAI,GAAG;gBAC7D,WAAW,IAAI,IAAI,CAAC,IAAI;gBACxB,gFAAgF;gBAChF,MAAM,qBAAqB,IAAI,IAAI,CAAC,UAAU;gBAC9C,MAAM,sBAAsB,KAAK,IAAI,CAAC,mBAAmB,WAAW,GAAG,mBAAmB,QAAQ;gBAElG,MAAM,sBAAsB;oBAC1B,GAAG,kBAAkB;oBACrB,aAAa;oBACb,UAAU,mBAAmB,YAAY,GAAG;oBAC5C,cAAc,mBAAmB,YAAY,GAAG;gBAClD;gBACA,cAAc;YAChB,OAAO,IAAI,MAAM,OAAO,CAAC,IAAI,IAAI,GAAG;gBAClC,WAAW,IAAI,IAAI;gBACnB,QAAQ,GAAG,CAAC;YACd;YAEA,QAAQ;YACR,eAAe;YAEf,sEAAsE;YACtE,IAAI,SAAS,MAAM,GAAG,KAAK,CAAC,aAAa;gBACvC,QAAQ,GAAG,CAAC,mBAAmB,QAAQ,CAAC,EAAE;gBAC1C,QAAQ,GAAG,CAAC,uBAAuB;oBACjC,OAAO,QAAQ,CAAC,EAAE,CAAC,YAAY;oBAC/B,MAAM,OAAO,QAAQ,CAAC,EAAE,CAAC,YAAY;oBACrC,SAAS,MAAM,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,YAAY;gBACjD;gBACA,QAAQ,GAAG,CAAC,sBAAsB,QAAQ,CAAC,EAAE,CAAC,iBAAiB;gBAC/D,QAAQ,GAAG,CAAC,qBAAqB,QAAQ,CAAC,EAAE,CAAC,gBAAgB;gBAC7D,eAAe,QAAQ,CAAC,EAAE;YAC5B;QAEF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,wBAAwB;YACtC,QAAQ,EAAE;YACV,cAAc;gBACZ,cAAc;gBACd,aAAa;gBACb,aAAa;gBACb,UAAU;gBACV,UAAU;gBACV,cAAc;YAChB;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEF,kEAAkE;IAEhE,0CAA0C;IAC1C,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,SAAS,MAAM,sHAAA,CAAA,cAAW,CAAC,eAAe;YAChD,gBAAgB;QAClB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,kCAAkC;YAChD,6DAA6D;YAC7D,IAAI,IAAI,QAAQ,EAAE,WAAW,OAAO,IAAI,QAAQ,EAAE,WAAW,KAAK;gBAChE,gBAAgB;oBACd,eAAe;oBACf,eAAe;gBACjB;YACF;QACF;IACF;IAEA,qBAAqB;IACrB,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,kBAAkB,MAAM,sHAAA,CAAA,cAAW,CAAC,cAAc;YACxD,6CAA6C;YAC7C,MAAM,gBAAgB,IAAI;YAC1B,IAAI,mBAAmB,gBAAgB,OAAO,EAAE;gBAC9C,gBAAgB,OAAO,CAAC,OAAO,CAAC,CAAA;oBAC9B,IAAI,YAAY,GAAG,IAAI,YAAY,GAAG,CAAC,EAAE,EAAE;wBACzC,cAAc,GAAG,CAAC,YAAY,GAAG,CAAC,EAAE;oBACtC;gBACF;YACF;YACA,eAAe;QACjB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,mCAAmC;YACnC,eAAe,IAAI;QACrB;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,IAAI;YACF,MAAM,cAAc,MAAM,sHAAA,CAAA,cAAW,CAAC,aAAa,CAAC;YACpD,kBAAkB,CAAA,OAAQ,IAAI,IAAI,KAAK,GAAG,CAAC,OAAO;YAClD,OAAO;QACT,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,oCAAoC;YAElD,8BAA8B;YAC9B,IAAI,IAAI,QAAQ,EAAE,WAAW,OAAO,IAAI,QAAQ,EAAE,MAAM,QAAQ;gBAC9D,yDAAyD;gBACzD,MAAM,mBAAmB;oBACvB,WAAW;oBACX,QAAQ,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM;gBAClC;gBACA,kBAAkB,CAAA,OAAQ,IAAI,IAAI,KAAK,GAAG,CAAC,OAAO;gBAClD,OAAO;YACT;YAEA,oDAAoD;YACpD,OAAO;gBAAE,WAAW;YAAK;QAC3B;IACF;IAEA,qDAAqD;IACrD,MAAM,qBAAqB,CAAC;QAC1B,MAAM,cAAc,eAAe,GAAG,CAAC;QACvC,OAAO,eAAe,CAAC,YAAY,SAAS;IAC9C;IAEA,6CAA6C;IAC7C,MAAM,wBAAwB,CAAC;QAC7B,MAAM,cAAc,eAAe,GAAG,CAAC;QACvC,IAAI,eAAe,CAAC,YAAY,SAAS,EAAE;YACzC,OAAO,YAAY,MAAM,IAAI;QAC/B;QACA,OAAO;IACT;IAEA,uDAAuD;IACvD,MAAM,kBAAkB,CAAC;QACvB,OAAO,YAAY,GAAG,CAAC;IACzB;IAEA,qDAAqD;IACrD,MAAM,sBAAsB,CAAC;QAC3B,IAAI,gBAAgB,QAAQ;YAC1B,OAAO;gBACL,MAAM;gBACN,UAAU;gBACV,WAAW;gBACX,QAAQ;YACV;QACF;QAEA,MAAM,cAAc,eAAe,GAAG,CAAC;QACvC,IAAI,eAAe,CAAC,YAAY,SAAS,EAAE;YACzC,OAAO;gBACL,MAAM;gBACN,UAAU;gBACV,WAAW;gBACX,QAAQ,YAAY,MAAM;YAC5B;QACF;QAEA,OAAO;YACL,MAAM,aAAa,gBAAgB;YACnC,UAAU;YACV,WAAW;YACX,QAAQ;QACV;IACF;IAEA,MAAM,WAAW;QACf,sCAAsC;QACtC,IAAI,CAAC,aAAa;QAElB,2BAA2B;QAC3B,IAAI,gBAAgB,YAAY,EAAE,GAAG;YACnC,MAAM;YACN;QACF;QAEA,sCAAsC;QACtC,MAAM,cAAc,MAAM,oBAAoB,YAAY,EAAE;QAE5D,IAAI,CAAC,YAAY,SAAS,EAAE;YAC1B,gGAAgG;YAChG,MAAM;QACR;QAEA,6CAA6C;QAC7C,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,aAAa,EAAE,YAAY,EAAE,CAAC,MAAM,CAAC;IAC/D;IAEF,qEAAqE;IAGnE,eAAe;IACf,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,UAAU;QACV;QACA;QAEA,oCAAoC;QACpC,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,OAAO;YACT,aAAa,IAAI,IAAI,KAAK,KAAK,CAAC;QAClC;IACF,GAAG,EAAE;IAEL,yFAAyF;IACzF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,yBAAyB;YAC7B,IAAI,CAAC,SAAS,MAAM,EAAE;gBACpB;YACF;QACF;QAEA,SAAS,gBAAgB,CAAC,oBAAoB;QAC9C,OAAO,IAAM,SAAS,mBAAmB,CAAC,oBAAoB;IAChE,GAAG,EAAE;IAEL,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,WAAW;YAC3B,IAAI,gBAAgB,GAAG;gBACrB,UAAU;YACZ,OAAO;gBACL,eAAe;gBACf,UAAU;YACZ;QACF,GAAG,MAAM,iBAAiB;QAE1B,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAY;QAAe;KAAe;IAE9C,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB;YACtB,gBAAgB,OAAO,UAAU,GAAG;QACtC;QAEA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAElC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,iCAAiC;IACjC,MAAM,YAAY,MAAM,OAAO,CAAC,QAAQ,OAAO,EAAE;IAEjD,sCAAsC;IAEtC,MAAM,QAAQ;QACZ,OAAO,WAAW,WAAW;QAC7B,aAAa,UAAU,MAAM,CAAC,CAAA,MAAO,IAAI,QAAQ,KAAK,cAAc,MAAM;QAC1E,UAAU,UAAU,MAAM,CAAC,CAAA,MAAO,IAAI,QAAQ,KAAK,aAAa,MAAM;QACtE,QAAQ,UAAU,MAAM,CAAC,CAAA,MAAO,CAAC,IAAI,QAAQ,IAAI,EAAE,EAAE,WAAW,GAAG,QAAQ,CAAC,WAAW,MAAM;IAC/F;IAEA,wDAAwD;IACxD,MAAM,eAAe;IAErB,MAAM,gBAAgB,CAAC;QACrB,MAAM,eAAe,IAAI,IAAI;QAC7B,IAAI,aAAa,GAAG,CAAC,QAAQ;YAC3B,aAAa,MAAM,CAAC;QACtB,OAAO;YACL,aAAa,GAAG,CAAC;QACnB;QACA,aAAa;QACb,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;eAAI;SAAa;IACpE;IAEA,4BAA4B;IAC5B,MAAM,kBAAkB,CAAC;QACvB,eAAe;QACf,IAAI,cAAc;YAChB,kBAAkB;QACpB;IACF;IAEA,MAAM,mBAAmB;QACvB,kBAAkB;IACpB;IAEA,qBACE;;0BAEE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAEvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,4MAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAEvB,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAmC,MAAM,KAAK;;;;;;8DAC3D,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAGzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;sDAE3B,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAmC,MAAM,WAAW;;;;;;8DACjE,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAGzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAmC,MAAM,MAAM;;;;;;8DAC5D,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAGzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAmC,UAAU,IAAI;;;;;;8DAC9D,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ/C,8OAAC;gBAAI,WAAU;0BAEZ,6BACC,8OAAC;oBAAI,WAAU;8BACZ,CAAC,iBACA,uBAAuB;kCACvB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAsC;;;;;;0DACpD,8OAAC;gDAAK,WAAU;0DACb,WAAW,WAAW,GAAG,IACtB,GAAG,aAAa,MAAM,CAAC,IAAI,EAAE,WAAW,WAAW,CAAC,KAAK,CAAC,GAC1D,GAAG,aAAa,MAAM,CAAC,UAAU,CAAC;;;;;;;;;;;;kDAK1C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;wDACC,MAAK;wDACL,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC7C,WAAU;;;;;;;;;;;;0DAGd,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,OAAO;wDACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;wDAChD,WAAU;;0EAEV,8OAAC;gEAAO,OAAM;0EAAM;;;;;;0EACpB,8OAAC;gEAAO,OAAM;0EAAa;;;;;;0EAC3B,8OAAC;gEAAO,OAAM;0EAAY;;;;;;0EAC1B,8OAAC;gEAAO,OAAM;0EAAY;;;;;;;;;;;;kEAE5B,8OAAC;wDACC,OAAO;wDACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wDACjD,WAAU;;0EAEV,8OAAC;gEAAO,OAAM;0EAAM;;;;;;0EACpB,8OAAC;gEAAO,OAAM;0EAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO/B,8OAAC;gCAAI,WAAU;0CACZ,0BACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;;;;;;;;;2CAEf,aAAa,MAAM,GAAG,kBACxB,8OAAC;oCAAI,WAAU;8CACZ,aAAa,GAAG,CAAC,CAAC;wCACjB,MAAM,UAAU,UAAU,GAAG,CAAC,IAAI,EAAE;wCACpC,qBACE,8OAAC;4CAEC,SAAS,IAAM,gBAAgB;4CAC/B,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAW,CAAC,sCAAsC,EACtD,IAAI,QAAQ,KAAK,eACb,gCACA,6BACJ;kFACC,IAAI,QAAQ,IAAI;;;;;;kFAEnB,8OAAC;wEAAG,WAAU;kFACX,IAAI,KAAK,IAAI;;;;;;;;;;;;0EAGlB,8OAAC;gEACC,SAAS,CAAC;oEACR,EAAE,eAAe;oEACjB,cAAc,IAAI,EAAE;gEACtB;gEACA,WAAU;0EAET,wBACC,8OAAC,wNAAA,CAAA,gBAAa;oEAAC,WAAU;;;;;yFAEzB,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;;;;;;;;;;;;kEAI1B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,gNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;0EACrB,8OAAC;gEAAK,WAAU;0EAAe,IAAI,YAAY,IAAI;;;;;;;;;;;;kEAErD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,0MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;kFAClB,8OAAC;kFAAM,IAAI,QAAQ,IAAI;;;;;;;;;;;;0EAEzB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kNAAA,CAAA,aAAU;wEAAC,WAAU;;;;;;kFACtB,8OAAC;kFACE,IAAI,UAAU,IAAI,IAAI,UAAU,GAC7B,CAAC,CAAC,EAAE,IAAI,UAAU,CAAC,IAAI,EAAE,IAAI,UAAU,EAAE,GACzC;;;;;;;;;;;;;;;;;;;;;;;;2CA9CP,IAAI,EAAE;;;;;oCAqDjB;;;;;yDAGF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CAAE,WAAU;sDAAsB;;;;;;sDACnC,8OAAC;4CAAE,WAAU;sDAAsB;;;;;;;;;;;;;;;;;4BAMxC,WAAW,WAAW,GAAG,mBACxB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;gDAAwB;gDAC3B,CAAC,cAAc,CAAC,IAAI,WAAW,QAAQ,GAAI;gDAAE;gDAAK,KAAK,GAAG,CAAC,cAAc,WAAW,QAAQ,EAAE,WAAW,WAAW;gDAAE;gDAAK,WAAW,WAAW;gDAAC;;;;;;;sDAE9J,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,UAAU,cAAc;oDACvC,UAAU,CAAC,WAAW,YAAY,IAAI;oDACtC,WAAU;8DACX;;;;;;8DAGD,8OAAC;oDAAK,WAAU;;wDACb;wDAAY;wDAAK,WAAW,WAAW;;;;;;;8DAE1C,8OAAC;oDACC,SAAS,IAAM,UAAU,cAAc;oDACvC,UAAU,CAAC,WAAW,QAAQ,IAAI;oDAClC,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BASX,0BAA0B;kCAC1B,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACjE,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;oDACjE;;;;;;;0DAGR,8OAAC;gDACC,SAAS,IAAM,cAAc,YAAY,EAAE;gDAC3C,WAAW,CAAC,8CAA8C,EACxD,UAAU,GAAG,CAAC,YAAY,EAAE,IACxB,6CACA,oCACJ;0DAED,UAAU,GAAG,CAAC,YAAY,EAAE,IAAI,UAAU;;;;;;;;;;;;kDAI/C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DACX,YAAY,KAAK,IAAI;;;;;;0DAExB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,gNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;sEACrB,8OAAC;4DAAK,WAAU;sEAAiB,YAAY,YAAY,IAAI;;;;;;;;;;;;;;;;;0DAGjE,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAW,CAAC,2CAA2C,EAC3D,YAAY,QAAQ,KAAK,eACrB,gCACA,6BACJ;8DACC,YAAY,QAAQ,IAAI;;;;;;;;;;;;;;;;;kDAK/B,8OAAC;wCAAI,WAAU;kDACZ,eAAe,CAAC;4CACf,MAAM,cAAc,oBAAoB,YAAY,EAAE;4CACtD,MAAM,cAAc,AAAC,gBAAgB,aAAa,aAAa,KAAK,UACjD,gBAAgB,YAAY,EAAE,KAC7B,YAAY,MAAM,IAAI,CAAC,gBAAgB,YAAY,EAAE;4CAEzE,qBACE;;oDACG,6BACC,8OAAC;wDAAI,WAAW,CAAC,+CAA+C,EAC9D,gBAAgB,YAAY,EAAE,IAC1B,+BACA,YAAY,MAAM,GAChB,2BACA,8BACN;;0EACA,8OAAC,oNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;0EACE,gBAAgB,YAAY,EAAE,IAC3B,yCACA,YAAY,MAAM,IAAI;;;;;;;;;;;;kEAKhC,8OAAC;wDACC,SAAS;wDACT,WAAW,CAAC,0DAA0D,EACpE,YAAY,SAAS,CAAC,OAAO,CAAC,aAAa,cAC3C;wDACF,UAAU,YAAY,QAAQ;kEAE7B,YAAY,IAAI;;;;;;;;wCAIzB,CAAC;;;;;;;;;;;;0CAKL,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;8EACtB,8OAAC;oEAAG,WAAU;8EAAsC;;;;;;;;;;;;sEAEtD,8OAAC;4DAAE,WAAU;sEACV,YAAY,UAAU,IAAI,YAAY,UAAU,GAC7C,CAAC,CAAC,EAAE,YAAY,UAAU,CAAC,IAAI,EAAE,YAAY,UAAU,EAAE,GACzD;;;;;;sEAEN,8OAAC;4DAAE,WAAU;;gEAAwB;gEAAK,YAAY,QAAQ,IAAI;;;;;;;;;;;;;8DAEpE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,8OAAC;oEAAG,WAAU;8EAAsC;;;;;;;;;;;;sEAEtD,8OAAC;4DAAE,WAAU;sEACV,YAAY,QAAQ,IAAI;;;;;;;;;;;;;;;;;;sDAM/B,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAC3D,8OAAC,mHAAA,CAAA,0BAAuB;oDACtB,aAAa,YAAY,WAAW;oDACpC,WAAU;;;;;;;;;;;;sDAKd,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAC3D,8OAAC;oDAAI,WAAU;8DACZ,YAAY,YAAY,GACvB,OAAO,YAAY,YAAY,KAAK,yBAClC,8OAAC;wDAAG,WAAU;kEACX,YAAY,YAAY,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,sBAC7C,8OAAC;gEAAe,WAAU;;kFACxB,8OAAC,2NAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;kFACvB,8OAAC;kFAAM,IAAI,IAAI;;;;;;;+DAFR;;;;;;;;;6EAOb,8OAAC;kEAAG,OAAO,YAAY,YAAY;;;;;6EAGrC,8OAAC;kEAAE;;;;;;;;;;;;;;;;;sDAMT,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAC3D,8OAAC;oDAAI,WAAU;8DACZ,YAAY,YAAY,IAAI,OAAO,YAAY,YAAY,KAAK,WAC/D,YAAY,YAAY,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,OAAO,sBAC9C,8OAAC;4DAEC,WAAU;sEAET,MAAM,IAAI;2DAHN;;;;kFAOT,8OAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;;;;;;;wCAM7C,YAAY,gBAAgB,IAAI,YAAY,gBAAgB,CAAC,MAAM,GAAG,mBACrE,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAC3D,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACZ,YAAY,gBAAgB,CAAC,GAAG,CAAC,CAAC,OAAO,sBACxC,8OAAC;gEAAgB,WAAU;;kFACzB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;0FACb,cAAA,8OAAC;oFAAK,WAAU;8FAAuC,QAAQ;;;;;;;;;;;0FAEjE,8OAAC;;kGACC,8OAAC;wFAAE,WAAU;kGAA6B,MAAM,IAAI;;;;;;oFACnD,MAAM,IAAI,kBACT,8OAAC;wFAAE,WAAU;;4FACV,IAAI,KAAK,MAAM,IAAI,EAAE,kBAAkB;4FACvC,MAAM,IAAI,IAAI,CAAC,IAAI,EAAE,MAAM,IAAI,EAAE;;;;;;;;;;;;;;;;;;;kFAK1C,8OAAC;wEAAI,WAAU;;4EAAwB;4EAC9B,QAAQ;;;;;;;;+DAhBT;;;;;;;;;;;;;;;;;;;;;wCA0BnB,YAAY,iBAAiB,IAAI,YAAY,iBAAiB,CAAC,MAAM,GAAG,mBACvE,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAC3D,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;8DAC1C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACZ,YAAY,iBAAiB,CAAC,GAAG,CAAC,CAAC,OAAO;gEACzC,MAAM,cAAc;oEAClB,OAAQ,MAAM,IAAI;wEAChB,KAAK;4EACH,qBACE,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAM,WAAU;;4FACd,MAAM,KAAK;4FAAC;4FAAE,MAAM,QAAQ,kBAAI,8OAAC;gGAAK,WAAU;0GAAe;;;;;;;;;;;;kGAElE,8OAAC;wFACC,MAAK;wFACL,aAAa,CAAC,MAAM,EAAE,MAAM,KAAK,CAAC,WAAW,IAAI;wFACjD,WAAU;wFACV,QAAQ;;;;;;;;;;;;wEAKhB,KAAK;4EACH,qBACE,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAM,WAAU;;4FACd,MAAM,KAAK;4FAAC;4FAAE,MAAM,QAAQ,kBAAI,8OAAC;gGAAK,WAAU;0GAAe;;;;;;;;;;;;kGAElE,8OAAC;wFACC,MAAK;wFACL,aAAY;wFACZ,WAAU;wFACV,QAAQ;;;;;;;;;;;;wEAKhB,KAAK;4EACH,qBACE,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAM,WAAU;;4FACd,MAAM,KAAK;4FAAC;4FAAE,MAAM,QAAQ,kBAAI,8OAAC;gGAAK,WAAU;0GAAe;;;;;;;;;;;;kGAElE,8OAAC;wFAAI,WAAU;;0GACb,8OAAC;gGAAI,WAAU;;kHACb,8OAAC;wGAAI,WAAU;wGAAe,MAAK;wGAAO,QAAO;wGAAe,SAAQ;kHACtE,cAAA,8OAAC;4GAAK,eAAc;4GAAQ,gBAAe;4GAAQ,aAAa;4GAAG,GAAE;;;;;;;;;;;kHAEvE,8OAAC;wGAAK,WAAU;;4GAAsB;4GAAQ,MAAM,KAAK,CAAC,WAAW;;;;;;;;;;;;;0GAEvE,8OAAC;gGAAI,WAAU;0GACb,cAAA,8OAAC;oGAAK,WAAU;8GAAwB;;;;;;;;;;;;;;;;;;;;;;;wEAMlD,KAAK;4EACH,qBACE,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAM,WAAU;;4FACd,MAAM,KAAK;4FAAC;4FAAE,MAAM,QAAQ,kBAAI,8OAAC;gGAAK,WAAU;0GAAe;;;;;;;;;;;;kGAElE,8OAAC;wFACC,WAAU;wFACV,QAAQ;;0GAER,8OAAC;gGAAO,OAAM;;oGAAG;oGAAQ,MAAM,KAAK,CAAC,WAAW;;;;;;;4FAC/C,MAAM,OAAO,EAAE,IAAI,CAAC,QAAQ,yBAC3B,8OAAC;oGAAsB,OAAO;8GAC3B;mGADU;;;;;;;;;;;;;;;;;wEAQvB,KAAK;4EACH,qBACE,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAM,WAAU;;4FACd,MAAM,KAAK;4FAAC;4FAAE,MAAM,QAAQ,kBAAI,8OAAC;gGAAK,WAAU;0GAAe;;;;;;;;;;;;kGAElE,8OAAC;wFACC,MAAM;wFACN,aAAa,CAAC,MAAM,EAAE,MAAM,KAAK,CAAC,WAAW,IAAI;wFACjD,WAAU;wFACV,QAAQ;;;;;;;;;;;;wEAKhB;4EACE,qBACE,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAM,WAAU;;4FACd,MAAM,KAAK;4FAAC;4FAAE,MAAM,QAAQ,kBAAI,8OAAC;gGAAK,WAAU;0GAAe;;;;;;;;;;;;kGAElE,8OAAC;wFACC,MAAK;wFACL,aAAY;wFACZ,WAAU;wFACV,QAAQ;;;;;;;;;;;;oEAIlB;gEACF;gEAEA,qBACE,8OAAC;oEAA4B,WAAU;8EACpC;mEADO,MAAM,EAAE,IAAI;;;;;4DAI1B;;;;;;sEAEF,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAI,WAAU;4EAAwB,MAAK;4EAAO,QAAO;4EAAe,SAAQ;sFAC/E,cAAA,8OAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAa;gFAAG,GAAE;;;;;;;;;;;;;;;;kFAGzE,8OAAC;;0FACC,8OAAC;gFAAE,WAAU;0FAA2C;;;;;;0FACxD,8OAAC;gFAAE,WAAU;0FAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAa3E,iCAAiC;8BACjC,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAsC;;;;;;8DACpD,8OAAC;oDAAK,WAAU;8DACb,WAAW,WAAW,GAAG,IACtB,GAAG,aAAa,MAAM,CAAC,IAAI,EAAE,WAAW,WAAW,CAAC,KAAK,CAAC,GAC1D,GAAG,aAAa,MAAM,CAAC,UAAU,CAAC;;;;;;;;;;;;sDAI1C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,WAAU;;;;;;;;;;;;sDAId,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,OAAO;oDACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;oDAChD,WAAU;;sEAEV,8OAAC;4DAAO,OAAM;sEAAM;;;;;;sEACpB,8OAAC;4DAAO,OAAM;sEAAa;;;;;;sEAC3B,8OAAC;4DAAO,OAAM;sEAAY;;;;;;sEAC1B,8OAAC;4DAAO,OAAM;sEAAY;;;;;;;;;;;;8DAE5B,8OAAC;oDACC,OAAO;oDACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;oDACjD,WAAU;;sEAEV,8OAAC;4DAAO,OAAM;sEAAM;;;;;;sEACpB,8OAAC;4DAAO,OAAM;sEAAS;;;;;;;;;;;;;;;;;;;;;;;;8CAK7B,8OAAC;oCAAI,WAAU;8CACZ,0BACC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;;;;;;;;;+CAEf,aAAa,MAAM,GAAG,kBACxB,8OAAC;wCAAI,WAAU;kDACZ,aAAa,GAAG,CAAC,CAAC;4CACjB,MAAM,aAAa,IAAI,EAAE,KAAK,aAAa;4CAC3C,MAAM,UAAU,UAAU,GAAG,CAAC,IAAI,EAAE;4CACpC,qBACE,8OAAC;gDAEC,SAAS,IAAM,gBAAgB;gDAC/B,WAAW,CAAC,gEAAgE,EAC1E,aAAa,0CAA0C,IACvD;0DAEF,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAW,CAAC,wCAAwC,EACxD,IAAI,QAAQ,KAAK,eACb,gCACA,6BACJ;sFACC,IAAI,QAAQ,IAAI;;;;;;sFAEnB,8OAAC;4EAAG,WAAW,CAAC,oCAAoC,EAClD,aAAa,kBAAkB,iBAC/B;sFACC,IAAI,KAAK,IAAI;;;;;;;;;;;;8EAGlB,8OAAC;oEACC,SAAS,CAAC;wEACR,EAAE,eAAe;wEACjB,cAAc,IAAI,EAAE;oEACtB;oEACA,WAAU;8EAET,wBACC,8OAAC,wNAAA,CAAA,gBAAa;wEAAC,WAAU;;;;;6FAEzB,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;;;;;;;;;;;;sEAI1B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,gNAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;8EACrB,8OAAC;oEAAK,WAAU;8EAAe,IAAI,YAAY,IAAI;;;;;;;;;;;;sEAErD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,0MAAA,CAAA,SAAM;oFAAC,WAAU;;;;;;8FAClB,8OAAC;8FAAM,IAAI,QAAQ,IAAI;;;;;;;;;;;;sFAEzB,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,kNAAA,CAAA,aAAU;oFAAC,WAAU;;;;;;8FACtB,8OAAC;8FACE,IAAI,UAAU,IAAI,IAAI,UAAU,GAC7B,CAAC,CAAC,EAAE,IAAI,UAAU,CAAC,IAAI,EAAE,IAAI,UAAU,EAAE,GACzC;;;;;;;;;;;;;;;;;;8EAIV,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;sFACjB,8OAAC;sFAAK;;;;;;;;;;;;;;;;;;;;;;;;+CAzDP,IAAI,EAAE;;;;;wCA+DjB;;;;;6DAGF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAE,WAAU;0DAAsB;;;;;;0DACnC,8OAAC;gDAAE,WAAU;0DAAU;;;;;;;;;;;;;;;;;gCAM5B,WAAW,WAAW,GAAG,mBACxB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;oDAAwB;oDAC3B,CAAC,cAAc,CAAC,IAAI,WAAW,QAAQ,GAAI;oDAAE;oDAAK,KAAK,GAAG,CAAC,cAAc,WAAW,QAAQ,EAAE,WAAW,WAAW;oDAAE;oDAAK,WAAW,WAAW;oDAAC;;;;;;;0DAE9J,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,SAAS,IAAM,UAAU,cAAc;wDACvC,UAAU,CAAC,WAAW,YAAY,IAAI;wDACtC,WAAU;kEACX;;;;;;kEAID,8OAAC;wDAAI,WAAU;kEAEZ,MAAM,IAAI,CAAC;4DAAE,QAAQ,KAAK,GAAG,CAAC,WAAW,WAAW,EAAE;wDAAG,GAAG,CAAC,GAAG;4DAC/D,IAAI;4DACJ,IAAI,WAAW,WAAW,IAAI,GAAG;gEAC/B,UAAU,IAAI;4DAChB,OAAO,IAAI,eAAe,GAAG;gEAC3B,UAAU,IAAI;4DAChB,OAAO,IAAI,eAAe,WAAW,WAAW,GAAG,GAAG;gEACpD,UAAU,WAAW,WAAW,GAAG,IAAI;4DACzC,OAAO;gEACL,UAAU,cAAc,IAAI;4DAC9B;4DAEA,qBACE,8OAAC;gEAEC,SAAS,IAAM,UAAU;gEACzB,UAAU;gEACV,WAAW,CAAC,oCAAoC,EAC9C,YAAY,cACR,6CACA,oCACL,gDAAgD,CAAC;0EAEjD;+DATI;;;;;wDAYX;;;;;;kEAGF,8OAAC;wDACC,SAAS,IAAM,UAAU,cAAc;wDACvC,UAAU,CAAC,WAAW,QAAQ,IAAI;wDAClC,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAUX,8OAAC;4BAAI,WAAU;sCACZ,4BACC;;kDAEE,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEACX,YAAY,KAAK,IAAI;;;;;;sEAExB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,gNAAA,CAAA,YAAS;4EAAC,WAAU;;;;;;sFACrB,8OAAC;4EAAK,WAAU;sFAAiB,YAAY,YAAY,IAAI;;;;;;;;;;;;8EAE/D,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,0MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;sFAClB,8OAAC;sFAAM,YAAY,QAAQ,IAAI;;;;;;;;;;;;;;;;;;sEAGnC,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAW,CAAC,2CAA2C,EAC3D,YAAY,QAAQ,KAAK,eACrB,gCACA,6BACJ;0EACC,YAAY,QAAQ,IAAI;;;;;;;;;;;;;;;;;8DAK/B,8OAAC;oDAAI,WAAU;8DACZ,eAAe,CAAC;wDACf,MAAM,cAAc,oBAAoB,YAAY,EAAE;wDACtD,MAAM,cAAc,AAAC,gBAAgB,aAAa,aAAa,KAAK,UACjD,gBAAgB,YAAY,EAAE,KAC7B,YAAY,MAAM,IAAI,CAAC,gBAAgB,YAAY,EAAE;wDAEzE,qBACE;;gEACG,6BACC,8OAAC;oEAAI,WAAW,CAAC,+CAA+C,EAC9D,gBAAgB,YAAY,EAAE,IAC1B,+BACA,YAAY,MAAM,GAChB,2BACA,8BACN;;sFACA,8OAAC,oNAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;sFACvB,8OAAC;sFACE,gBAAgB,YAAY,EAAE,IAC3B,yCACA,YAAY,MAAM,IAAI;;;;;;;;;;;;8EAKhC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EACC,SAAS,IAAM,cAAc,YAAY,EAAE;4EAC3C,WAAW,CAAC,8CAA8C,EACxD,UAAU,GAAG,CAAC,YAAY,EAAE,IACxB,6CACA,oCACJ;sFAED,UAAU,GAAG,CAAC,YAAY,EAAE,IAAI,UAAU;;;;;;sFAE7C,8OAAC;4EACC,SAAS;4EACT,WAAW,YAAY,SAAS;4EAChC,UAAU,YAAY,QAAQ;sFAE7B,YAAY,IAAI;;;;;;;;;;;;;;oDAK3B,CAAC;;;;;;;;;;;;;;;;;kDAMP,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,kNAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;sFACtB,8OAAC;4EAAG,WAAU;sFAAsC;;;;;;;;;;;;8EAEtD,8OAAC;oEAAE,WAAU;8EACV,YAAY,UAAU,IAAI,YAAY,UAAU,GAC7C,CAAC,CAAC,EAAE,YAAY,UAAU,CAAC,IAAI,EAAE,YAAY,UAAU,EAAE,GACzD;;;;;;8EAEN,8OAAC;oEAAE,WAAU;;wEAAwB;wEAAK,YAAY,QAAQ,IAAI;;;;;;;;;;;;;sEAEpE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,0MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;sFACpB,8OAAC;4EAAG,WAAU;sFAAsC;;;;;;;;;;;;8EAEtD,8OAAC;oEAAE,WAAU;8EACV,YAAY,oBAAoB,GAC7B,IAAI,KAAK,YAAY,oBAAoB,EAAE,kBAAkB,KAC7D;;;;;;;;;;;;sEAGR,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;sFACjB,8OAAC;4EAAG,WAAU;sFAAsC;;;;;;;;;;;;8EAEtD,8OAAC;oEAAE,WAAU;8EACV,YAAY,QAAQ,IAAI;;;;;;;;;;;;sEAG7B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;sFACjB,8OAAC;4EAAG,WAAU;sFAAsC;;;;;;;;;;;;8EAEtD,8OAAC;oEAAE,WAAU;8EACV;;;;;;;;;;;;;;;;;;8DAKP,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA6C;;;;;;sEAC3D,8OAAC,mHAAA,CAAA,0BAAuB;4DACtB,aAAa,YAAY,WAAW;4DACpC,WAAU;;;;;;;;;;;;8DAId,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA6C;;;;;;sEAC3D,8OAAC;4DAAI,WAAU;sEACZ,YAAY,YAAY,GACvB,OAAO,YAAY,YAAY,KAAK,yBAClC,8OAAC;gEAAG,WAAU;0EACX,YAAY,YAAY,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,sBAC7C,8OAAC;wEAAe,WAAU;;0FACxB,8OAAC,2NAAA,CAAA,cAAW;gFAAC,WAAU;;;;;;0FACvB,8OAAC;0FAAM,IAAI,IAAI;;;;;;;uEAFR;;;;;;;;;qFAOb,8OAAC;0EAAG,OAAO,YAAY,YAAY;;;;;qFAGrC,8OAAC;0EAAE;;;;;;;;;;;;;;;;;8DAKT,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA6C;;;;;;sEAC3D,8OAAC;4DAAI,WAAU;sEACZ,YAAY,YAAY,IAAI,OAAO,YAAY,YAAY,KAAK,WAC/D,YAAY,YAAY,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,OAAO,sBAC9C,8OAAC;oEAEC,WAAU;8EAET,MAAM,IAAI;mEAHN;;;;0FAOT,8OAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;;;;;;gDAM7C,YAAY,gBAAgB,IAAI,YAAY,gBAAgB,CAAC,MAAM,GAAG,mBACrE,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA6C;;;;;;sEAC3D,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;0EACZ,YAAY,gBAAgB,CAAC,GAAG,CAAC,CAAC,OAAO,sBACxC,8OAAC;wEAAgB,WAAU;;0FACzB,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAI,WAAU;kGACb,cAAA,8OAAC;4FAAK,WAAU;sGAAuC,QAAQ;;;;;;;;;;;kGAEjE,8OAAC;;0GACC,8OAAC;gGAAE,WAAU;0GAA6B,MAAM,IAAI;;;;;;4FACnD,MAAM,IAAI,kBACT,8OAAC;gGAAE,WAAU;;oGACV,IAAI,KAAK,MAAM,IAAI,EAAE,kBAAkB;oGACvC,MAAM,IAAI,IAAI,CAAC,IAAI,EAAE,MAAM,IAAI,EAAE;;;;;;;;;;;;;;;;;;;0FAK1C,8OAAC;gFAAI,WAAU;;oFAAwB;oFAC9B,QAAQ;;;;;;;;uEAhBT;;;;;;;;;;;;;;;;;;;;;gDA0BnB,YAAY,iBAAiB,IAAI,YAAY,iBAAiB,CAAC,MAAM,GAAG,mBACvE,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA6C;;;;;;sEAC3D,8OAAC;4DAAE,WAAU;sEAA6B;;;;;;sEAC1C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACZ,YAAY,iBAAiB,CAAC,GAAG,CAAC,CAAC,OAAO;wEACzC,MAAM,cAAc;4EAClB,OAAQ,MAAM,IAAI;gFAChB,KAAK;oFACH,qBACE,8OAAC;wFAAI,WAAU;;0GACb,8OAAC;gGAAM,WAAU;;oGACd,MAAM,KAAK;oGAAC;oGAAE,MAAM,QAAQ,kBAAI,8OAAC;wGAAK,WAAU;kHAAe;;;;;;;;;;;;0GAElE,8OAAC;gGACC,MAAK;gGACL,aAAa,CAAC,MAAM,EAAE,MAAM,KAAK,CAAC,WAAW,IAAI;gGACjD,WAAU;;;;;;;;;;;;gFAKlB,KAAK;oFACH,qBACE,8OAAC;wFAAI,WAAU;;0GACb,8OAAC;gGAAM,WAAU;;oGACd,MAAM,KAAK;oGAAC;oGAAE,MAAM,QAAQ,kBAAI,8OAAC;wGAAK,WAAU;kHAAe;;;;;;;;;;;;0GAElE,8OAAC;gGACC,MAAK;gGACL,aAAY;gGACZ,WAAU;;;;;;;;;;;;gFAKlB,KAAK;oFACH,qBACE,8OAAC;wFAAI,WAAU;;0GACb,8OAAC;gGAAM,WAAU;;oGACd,MAAM,KAAK;oGAAC;oGAAE,MAAM,QAAQ,kBAAI,8OAAC;wGAAK,WAAU;kHAAe;;;;;;;;;;;;0GAElE,8OAAC;gGAAI,WAAU;;kHACb,8OAAC;wGAAI,WAAU;;0HACb,8OAAC;gHAAI,WAAU;gHAAe,MAAK;gHAAO,QAAO;gHAAe,SAAQ;0HACtE,cAAA,8OAAC;oHAAK,eAAc;oHAAQ,gBAAe;oHAAQ,aAAa;oHAAG,GAAE;;;;;;;;;;;0HAEvE,8OAAC;gHAAK,WAAU;;oHAAsB;oHAAQ,MAAM,KAAK,CAAC,WAAW;;;;;;;;;;;;;kHAEvE,8OAAC;wGAAI,WAAU;kHACb,cAAA,8OAAC;4GAAK,WAAU;sHAAwB;;;;;;;;;;;;;;;;;;;;;;;gFAMlD,KAAK;oFACH,qBACE,8OAAC;wFAAI,WAAU;;0GACb,8OAAC;gGAAM,WAAU;;oGACd,MAAM,KAAK;oGAAC;oGAAE,MAAM,QAAQ,kBAAI,8OAAC;wGAAK,WAAU;kHAAe;;;;;;;;;;;;0GAElE,8OAAC;gGACC,WAAU;;kHAEV,8OAAC;wGAAO,OAAM;;4GAAG;4GAAQ,MAAM,KAAK,CAAC,WAAW;;;;;;;oGAC/C,MAAM,OAAO,EAAE,IAAI,CAAC,QAAQ,yBAC3B,8OAAC;4GAAsB,OAAO;sHAC3B;2GADU;;;;;;;;;;;;;;;;;gFAQvB,KAAK;oFACH,qBACE,8OAAC;wFAAI,WAAU;;0GACb,8OAAC;gGAAM,WAAU;;oGACd,MAAM,KAAK;oGAAC;oGAAE,MAAM,QAAQ,kBAAI,8OAAC;wGAAK,WAAU;kHAAe;;;;;;;;;;;;0GAElE,8OAAC;gGACC,MAAM;gGACN,aAAa,CAAC,MAAM,EAAE,MAAM,KAAK,CAAC,WAAW,IAAI;gGACjD,WAAU;;;;;;;;;;;;gFAKlB;oFACE,qBACE,8OAAC;wFAAI,WAAU;;0GACb,8OAAC;gGAAM,WAAU;;oGACd,MAAM,KAAK;oGAAC;oGAAE,MAAM,QAAQ,kBAAI,8OAAC;wGAAK,WAAU;kHAAe;;;;;;;;;;;;0GAElE,8OAAC;gGACC,MAAK;gGACL,aAAY;gGACZ,WAAU;;;;;;;;;;;;4EAIpB;wEACF;wEAEA,qBACE,8OAAC;4EAA4B,WAAU;sFACpC;2EADO,MAAM,EAAE,IAAI;;;;;oEAI1B;;;;;;8EAEF,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;0FACb,cAAA,8OAAC;oFAAI,WAAU;oFAAwB,MAAK;oFAAO,QAAO;oFAAe,SAAQ;8FAC/E,cAAA,8OAAC;wFAAK,eAAc;wFAAQ,gBAAe;wFAAQ,aAAa;wFAAG,GAAE;;;;;;;;;;;;;;;;0FAGzE,8OAAC;;kGACC,8OAAC;wFAAE,WAAU;kGAA2C;;;;;;kGACxD,8OAAC;wFAAE,WAAU;kGAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6DAYvE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,4MAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAEvB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjD", "debugId": null}}]}