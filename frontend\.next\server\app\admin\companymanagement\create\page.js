(()=>{var e={};e.id=3998,e.ids=[3998],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},18768:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\create\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\companymanagement\\create\\page.jsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},47490:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>v});var s=r(60687),a=r(43210),n=r(16189),i=r(5336),l=r(28559),o=r(35071),d=r(9005),c=r(16023),m=r(79410),u=r(97992),p=r(41312),x=r(40228),h=r(64398),g=r(47342),b=r(43125),y=r(8819),f=r(96882),j=r(14335);function v(){let e=(0,n.useRouter)(),[t,r]=(0,a.useState)(!1),[v,N]=(0,a.useState)({}),[w,C]=(0,a.useState)(null),[_,k]=(0,a.useState)(""),[q,A]=(0,a.useState)(!1),P=(0,a.useRef)(null),[S,D]=(0,a.useState)({name:"",industry:"",location:"",size:"",founded:"",website:"",tier:"Tier 3",description:"",campus_recruiting:!1,total_active_jobs:0,total_applicants:0,total_hired:0,awaited_approval:0,logo:null}),E=e=>{let{name:t,value:r,type:s,checked:a}=e.target;D({...S,[t]:"checkbox"===s?a:r})},R=()=>{let e={};return S.name.trim()||(e.name="Company name is required"),S.industry.trim()||(e.industry="Industry is required"),S.location.trim()||(e.location="Location is required"),S.size.trim()||(e.size="Company size is required"),S.founded.trim()||(e.founded="Founded year is required"),S.description.trim()||(e.description="Description is required"),S.website.trim()?z(S.website)||(e.website="Please enter a valid URL (include http:// or https://)"):e.website="Website URL is required",N(e),0===Object.keys(e).length},z=e=>{try{return new URL(e),!0}catch(e){return!1}},T=async t=>{if(t.preventDefault(),R()){A(!0);try{let t={...S,total_active_jobs:Number(S.total_active_jobs),total_applicants:Number(S.total_applicants),total_hired:Number(S.total_hired),awaited_approval:Number(S.awaited_approval),campus_recruiting:!!S.campus_recruiting,logo:w};console.log("Submitting company data:",t);let s=await (0,j.eK)(t);console.log("API response:",s),r(!0),setTimeout(()=>{e.push("/admin/companymanagement")},1500)}catch(e){console.error("Error creating company:",e),e.response?.data?(console.log("API error details:",e.response.data),N(t=>({...t,...e.response.data,...e.response.data.non_field_errors?{api_error:e.response.data.non_field_errors.join(", ")}:{}}))):N(e=>({...e,api_error:"Failed to create company. Please try again."}))}finally{A(!1)}}},U=()=>{e.push("/admin/companymanagement")};return t?(0,s.jsx)("div",{className:"h-full flex items-center justify-center bg-gray-50",children:(0,s.jsx)("div",{className:"bg-white p-8 rounded-xl shadow-sm border border-gray-200 max-w-md w-full",children:(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4",children:(0,s.jsx)(i.A,{className:"w-8 h-8 text-green-600"})}),(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Company Created!"}),(0,s.jsx)("p",{className:"text-gray-600 text-center mb-6",children:"The company profile has been successfully created."}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Redirecting back to company management..."})]})})}):(0,s.jsx)("div",{className:"h-full overflow-y-auto bg-gray-50",children:(0,s.jsxs)("div",{className:"container mx-auto py-8 px-4 md:px-6",children:[(0,s.jsxs)("div",{className:"flex items-center mb-6",children:[(0,s.jsx)("button",{onClick:U,className:"mr-4 p-2 rounded-full hover:bg-gray-200 transition-colors",children:(0,s.jsx)(l.A,{className:"w-5 h-5 text-gray-600"})}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Create New Company"})]}),(0,s.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 md:p-8",children:[v.api_error&&(0,s.jsxs)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg text-red-700 flex items-start gap-3",children:[(0,s.jsx)(o.A,{className:"w-5 h-5 text-red-500 flex-shrink-0 mt-0.5"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium",children:"Error"}),(0,s.jsx)("p",{children:v.api_error})]})]}),(0,s.jsxs)("form",{onSubmit:T,children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"md:col-span-2",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Company Logo"}),(0,s.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,s.jsx)("div",{className:`w-24 h-24 rounded-lg flex items-center justify-center overflow-hidden ${_?"border border-gray-200":"bg-gradient-to-br from-blue-500 to-purple-600"}`,children:_?(0,s.jsx)("img",{src:_,alt:"Company logo preview",className:"w-full h-full object-cover"}):S.name?(0,s.jsx)("span",{className:"text-white font-bold text-2xl",children:S.name.charAt(0)}):(0,s.jsx)(d.A,{className:"w-10 h-10 text-white opacity-70"})}),(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsxs)("button",{type:"button",onClick:()=>{P.current.click()},className:"px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg flex items-center gap-2",children:[(0,s.jsx)(c.A,{className:"w-4 h-4"}),_?"Change Logo":"Upload Logo"]}),_&&(0,s.jsx)("button",{type:"button",onClick:()=>{C(null),k(""),D({...S,logo:""}),P.current&&(P.current.value="")},className:"px-4 py-2 bg-red-50 hover:bg-red-100 text-red-600 rounded-lg",children:"Remove"}),(0,s.jsx)("input",{ref:P,type:"file",accept:"image/*",onChange:e=>{let t=e.target.files[0];if(t){C(t);let e=new FileReader;e.onloadend=()=>{k(e.result),D({...S,logo:e.result})},e.readAsDataURL(t)}},className:"hidden"})]}),(0,s.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"Recommended: Square image, at least 200x200px (.jpg, .png)"}),(0,s.jsx)("p",{className:"text-xs text-gray-400",children:"If no logo is uploaded, the first letter of the company name will be used."})]})]})]}),(0,s.jsxs)("div",{className:"md:col-span-2",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Company Name*"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(m.A,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{type:"text",name:"name",value:S.name,onChange:E,className:`pl-10 w-full rounded-lg border ${v.name?"border-red-300":"border-gray-300"} py-3 px-4 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500`,placeholder:"Enter company name"})]}),v.name&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:v.name})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Industry*"}),(0,s.jsxs)("select",{name:"industry",value:S.industry,onChange:E,className:`w-full rounded-lg border ${v.industry?"border-red-300":"border-gray-300"} py-3 px-4 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500`,children:[(0,s.jsx)("option",{value:"",children:"Select Industry"}),(0,s.jsx)("option",{value:"Technology",children:"Technology"}),(0,s.jsx)("option",{value:"Finance",children:"Finance"}),(0,s.jsx)("option",{value:"Healthcare",children:"Healthcare"}),(0,s.jsx)("option",{value:"Education",children:"Education"}),(0,s.jsx)("option",{value:"Manufacturing",children:"Manufacturing"}),(0,s.jsx)("option",{value:"Retail",children:"Retail"}),(0,s.jsx)("option",{value:"Consulting",children:"Consulting"}),(0,s.jsx)("option",{value:"Media",children:"Media"}),(0,s.jsx)("option",{value:"Other",children:"Other"})]}),v.industry&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:v.industry})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Location*"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(u.A,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{type:"text",name:"location",value:S.location,onChange:E,className:`pl-10 w-full rounded-lg border ${v.location?"border-red-300":"border-gray-300"} py-3 px-4 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500`,placeholder:"e.g., New York, NY"})]}),v.location&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:v.location})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Company Size*"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(p.A,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsxs)("select",{name:"size",value:S.size,onChange:E,className:`pl-10 w-full rounded-lg border ${v.size?"border-red-300":"border-gray-300"} py-3 px-4 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500`,children:[(0,s.jsx)("option",{value:"",children:"Select Size"}),(0,s.jsx)("option",{value:"1-10 employees",children:"1-10 employees"}),(0,s.jsx)("option",{value:"11-50 employees",children:"11-50 employees"}),(0,s.jsx)("option",{value:"51-200 employees",children:"51-200 employees"}),(0,s.jsx)("option",{value:"201-500 employees",children:"201-500 employees"}),(0,s.jsx)("option",{value:"501-1000 employees",children:"501-1000 employees"}),(0,s.jsx)("option",{value:"1001-5000 employees",children:"1001-5000 employees"}),(0,s.jsx)("option",{value:"5001+ employees",children:"5001+ employees"})]})]}),v.size&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:v.size})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Founded Year*"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(x.A,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{type:"number",name:"founded",min:"1800",max:new Date().getFullYear(),value:S.founded,onChange:E,className:`pl-10 w-full rounded-lg border ${v.founded?"border-red-300":"border-gray-300"} py-3 px-4 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500`,placeholder:"e.g., 2010"})]}),v.founded&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:v.founded})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Tier"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(h.A,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsxs)("select",{name:"tier",value:S.tier,onChange:E,className:"pl-10 w-full rounded-lg border border-gray-300 py-3 px-4 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"Tier 1",children:"Tier 1"}),(0,s.jsx)("option",{value:"Tier 2",children:"Tier 2"}),(0,s.jsx)("option",{value:"Tier 3",children:"Tier 3"})]})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("input",{type:"checkbox",id:"campus_recruiting",name:"campus_recruiting",checked:S.campus_recruiting,onChange:E,className:"h-5 w-5 text-blue-600 rounded border-gray-300 focus:ring-blue-500"}),(0,s.jsx)("label",{htmlFor:"campus_recruiting",className:"text-sm font-medium text-gray-700",children:"Campus Recruiting Program"})]}),(0,s.jsxs)("div",{className:"md:col-span-2",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Description*"}),(0,s.jsx)("textarea",{name:"description",value:S.description,onChange:E,rows:"4",className:`w-full rounded-lg border ${v.description?"border-red-300":"border-gray-300"} py-3 px-4 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500`,placeholder:"Enter a description of the company..."}),v.description&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:v.description})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Website URL*"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(g.A,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{type:"url",name:"website",value:S.website,onChange:E,className:`pl-10 w-full rounded-lg border ${v.website?"border-red-300":"border-gray-300"} py-3 px-4 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500`,placeholder:"https://example.com"})]}),v.website&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:v.website})]})]}),(0,s.jsxs)("div",{className:"mt-8 flex flex-col md:flex-row gap-4 md:gap-3 justify-end",children:[(0,s.jsx)("button",{type:"button",onClick:U,className:"px-6 py-3 border border-gray-300 rounded-lg text-gray-700 bg-white hover:bg-gray-50 font-medium",disabled:q,children:"Cancel"}),(0,s.jsx)("button",{type:"submit",className:"px-6 py-3 bg-blue-600 rounded-lg text-white hover:bg-blue-700 font-medium flex items-center justify-center gap-2",disabled:q,children:q?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(b.A,{className:"w-5 h-5 animate-spin"}),"Creating..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(y.A,{className:"w-5 h-5"}),"Create Company"]})})]})]})]}),(0,s.jsxs)("div",{className:"mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4 flex items-start gap-3",children:[(0,s.jsx)(f.A,{className:"w-5 h-5 text-blue-500 flex-shrink-0 mt-0.5"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-blue-800",children:"Important Note"}),(0,s.jsx)("p",{className:"text-sm text-blue-600",children:"In a production environment, this would submit to an API endpoint. Currently, this demo adds the company to the local data store."})]})]})]})})}},47901:(e,t,r)=>{Promise.resolve().then(r.bind(r,18768))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66461:(e,t,r)=>{Promise.resolve().then(r.bind(r,47490))},72738:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=r(65239),a=r(48088),n=r(88170),i=r.n(n),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let d={children:["",{children:["admin",{children:["companymanagement",{children:["create",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,18768)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\companymanagement\\create\\page.jsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,23697)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\layout.jsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,75535)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\layout.js"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\companymanagement\\create\\page.jsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/companymanagement/create/page",pathname:"/admin/companymanagement/create",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,681,1658,1060,2305,1372],()=>r(72738));module.exports=s})();