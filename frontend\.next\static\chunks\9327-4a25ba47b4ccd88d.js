"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9327],{37719:(t,e,a)=>{a.d(e,{A:()=>s});var r=a(23464);a(73983);let n=r.A.create({baseURL:"http://127.0.0.1:8000",headers:{"Content-Type":"application/json"}});n.interceptors.request.use(t=>{let e=localStorage.getItem("access_token");return e&&(t.headers.Authorization="Bearer ".concat(e)),t},t=>Promise.reject(t)),n.interceptors.response.use(t=>t,async t=>{var e;let a=t.config;if((null==(e=t.response)?void 0:e.status)===401&&!a._retry){a._retry=!0;try{let t=localStorage.getItem("refresh_token");if(t){let e=await r.<PERSON>.post("http://127.0.0.1:8000/api/auth/token/refresh/",{refresh:t});return localStorage.setItem("access_token",e.data.access),a.headers.Authorization="Bearer ".concat(e.data.access),n(a)}}catch(t){console.error("Error refreshing token:",t),localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),window.location.href="/login"}}return Promise.reject(t)});let s=n},69327:(t,e,a)=>{a.d(e,{Er:()=>c,Nt:()=>o,SY:()=>s,n0:()=>i});var r=a(37719);async function n(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let a,n,s,o=new URLSearchParams;e.page&&o.append("page",e.page),e.page_size&&o.append("per_page",e.page_size),Object.keys(e).forEach(t=>{"page"!==t&&"page_size"!==t&&e[t]&&o.append(t,e[t])});let c="".concat(t,"?").concat(o.toString()),i=await r.A.get(c);if(i.data.data&&i.data.pagination)a=i.data.data,n={current_page:i.data.pagination.current_page,total_pages:i.data.pagination.total_pages,total_count:i.data.pagination.total_count,per_page:i.data.pagination.per_page,has_next:i.data.pagination.has_next,has_previous:i.data.pagination.has_previous},s=i.data.metadata||{};else if(void 0!==i.data.results){if(a=i.data.results,i.data.pagination)n={current_page:i.data.pagination.page||e.page||1,total_pages:i.data.pagination.total_pages||1,total_count:i.data.pagination.total_count||0,per_page:i.data.pagination.page_size||e.page_size||10,has_next:i.data.pagination.has_next||!1,has_previous:i.data.pagination.has_previous||!1};else{let t=i.data.count||0,a=e.page_size||10,r=e.page||1,s=Math.ceil(t/a);n={current_page:r,total_pages:s,total_count:t,per_page:a,has_next:!!i.data.next,has_previous:!!i.data.previous}}s=i.data.metadata||{}}else a=i.data,n={current_page:e.page||1,total_pages:1,total_count:Array.isArray(a)?a.length:0,per_page:e.page_size||10,has_next:!1,has_previous:!1},s={};return{success:!0,data:a||[],pagination:n,metadata:s}}catch(e){throw console.error("Error fetching paginated data from ".concat(t,":"),e),e}}let s={async getCompanies(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{return await n("/api/v1/companies/optimized/",t)}catch(e){console.log("Optimized endpoint failed, trying fallback endpoints...");try{let{fetchCompanies:e}=await a.e(1318).then(a.bind(a,48937)),r=await e();if(t.search){let e=t.search.toLowerCase();r=r.filter(t=>t.name.toLowerCase().includes(e)||t.industry.toLowerCase().includes(e)||t.description.toLowerCase().includes(e))}t.tier&&"ALL"!==t.tier&&(r=r.filter(e=>e.tier===t.tier)),t.industry&&"ALL"!==t.industry&&(r=r.filter(e=>e.industry===t.industry)),t.ordering&&r.sort((e,a)=>{switch(t.ordering){case"name":return e.name.localeCompare(a.name);case"-total_active_jobs":return(a.totalActiveJobs||0)-(e.totalActiveJobs||0);case"-total_applicants":return(a.totalApplicants||0)-(e.totalApplicants||0);case"tier":return e.tier.localeCompare(a.tier);default:return 0}});let n=t.page||1,s=t.page_size||10,o=(n-1)*s,c=r.slice(o,o+s),i=r.length,l=Math.ceil(i/s);return{success:!0,data:c,pagination:{current_page:n,total_pages:l,total_count:i,per_page:s,has_next:n<l,has_previous:n>1},metadata:{}}}catch(t){throw console.error("All company endpoints failed:",t),t}}},async getCompanyStats(){arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{return(await r.A.get("/api/v1/companies/stats/")).data}catch(t){for(let e of(console.error("Error fetching company stats from primary endpoint:",t),["/api/v1/stats/companies/","/api/v1/jobs/stats/"]))try{let t=await r.A.get(e);if(t.data)return console.log("Successfully fetched stats from ".concat(e)),t.data}catch(t){console.log("Failed to fetch from ".concat(e,": ").concat(t.message))}return console.log("Falling back to calculated stats from companies data"),this.calculateStatsFromCompanies()}},async calculateStatsFromCompanies(){try{let{fetchCompanies:t}=await a.e(1318).then(a.bind(a,48937)),e=await t();return{total:e.length,active_jobs:e.reduce((t,e)=>t+(e.totalActiveJobs||0),0),campus_recruiting:e.filter(t=>t.campus_recruiting).length,tier1:e.filter(t=>"Tier 1"===t.tier).length,tier2:e.filter(t=>"Tier 2"===t.tier).length,tier3:e.filter(t=>"Tier 3"===t.tier).length}}catch(t){return console.error("Failed to calculate stats from companies:",t),{total:0,active_jobs:0,campus_recruiting:0,tier1:0,tier2:0,tier3:0}}},async searchCompanies(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:20,n={search:t,page:a,page_size:r,...e};return this.getCompanies(n)}},o={async getStudents(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{var e,a,r,s,o,c,i;let l={...t},d=!0===t.count_only;d&&(l.per_page=1e3),t.page_size&&(l.per_page=t.page_size,delete l.page_size),l.count_only&&delete l.count_only;let u=await n("/api/accounts/students/optimized/",l);if(d&&!(null==(e=u.metadata)?void 0:e.year_counts)&&u.data){let t={};u.data.forEach(e=>{let a=e.passout_year||e.graduation_year;a&&(t[a]=(t[a]||0)+1)}),u.metadata||(u.metadata={}),u.metadata.year_counts=t}return{success:!0,data:u.data||[],pagination:{current_page:(null==(a=u.pagination)?void 0:a.current_page)||t.page||1,total_pages:(null==(r=u.pagination)?void 0:r.total_pages)||1,total_count:(null==(s=u.pagination)?void 0:s.total_count)||0,per_page:(null==(o=u.pagination)?void 0:o.per_page)||t.page_size||10,has_next:(null==(c=u.pagination)?void 0:c.has_next)||!1,has_previous:(null==(i=u.pagination)?void 0:i.has_previous)||!1},metadata:u.metadata||{}}}catch(e){console.error("Error in studentsAPI.getStudents:",e);try{return await n("/api/accounts/students/",t)}catch(t){throw console.error("Fallback endpoint also failed:",t),t}}},async getStudentStats(){arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{return(await r.A.get("/api/accounts/students/stats/")).data}catch(t){return console.error("Error fetching student stats:",t),{total:0,active:0,graduated:0,placed:0}}},async getDepartmentStats(){arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{return(await r.A.get("/api/accounts/departments/stats/")).data}catch(t){return console.error("Error fetching department stats:",t),{total:0,departments:[]}}},async searchStudents(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:20,n={search:t,page:a,page_size:r,...e};return this.getStudents(n)},async getStudent(t){try{return(await r.A.get("/api/accounts/students/".concat(t,"/"))).data}catch(t){throw console.error("Error fetching student:",t),t}},async updateStudent(t,e){try{console.log("updateStudent called with:",{id:t,data:e});let u={...e};["joining_year","passout_year"].forEach(t=>{if(null!==u[t]&&void 0!==u[t]){let e=parseInt(u[t]);u[t]=isNaN(e)?null:e}}),["first_name","last_name","student_id","contact_email","phone","branch","gpa","date_of_birth","address","city","district","state","pincode","country","parent_contact","education","skills","tenth_cgpa","tenth_percentage","tenth_board","tenth_school","tenth_year_of_passing","tenth_location","tenth_specialization","twelfth_cgpa","twelfth_percentage","twelfth_board","twelfth_school","twelfth_year_of_passing","twelfth_location","twelfth_specialization"].forEach(t=>{null!==u[t]&&void 0!==u[t]&&(u[t]=String(u[t]).trim())}),Object.keys(u).forEach(t=>{void 0===u[t]&&delete u[t]}),console.log("Cleaned data being sent:",u);try{console.log("Trying ViewSet endpoint:","/api/accounts/profiles/".concat(t,"/"));let e=await r.A.patch("/api/accounts/profiles/".concat(t,"/"),u);return console.log("ViewSet endpoint success:",e.data),e.data}catch(e){var a,n,s,o,c,i,l,d;console.error("ViewSet endpoint failed:",{status:null==(a=e.response)?void 0:a.status,statusText:null==(n=e.response)?void 0:n.statusText,data:null==(s=e.response)?void 0:s.data});try{console.log("Trying fallback endpoint:","/api/accounts/students/".concat(t,"/update/"));let e=await r.A.patch("/api/accounts/students/".concat(t,"/update/"),u);return console.log("Fallback endpoint success:",e.data),e.data}catch(t){throw console.error("Failed to update student via both endpoints:",{viewSetError:{status:null==(o=e.response)?void 0:o.status,data:null==(c=e.response)?void 0:c.data},updateViewError:{status:null==(i=t.response)?void 0:i.status,data:null==(l=t.response)?void 0:l.data}}),(null==(d=t.response)?void 0:d.status)===400?t:e}}}catch(t){throw console.error("Error updating student:",t),t}},async uploadResume(t){try{let e=new FormData;return e.append("resume",t),(await r.A.patch("/api/auth/profile/",e,{headers:{"Content-Type":"multipart/form-data"}})).data}catch(t){throw console.error("Error uploading resume:",t),t}},async deleteResume(t){try{return(await r.A.delete("/api/accounts/resumes/".concat(t,"/"))).data}catch(t){throw console.error("Error deleting resume:",t),t}},async uploadCertificate(t,e){try{let a=new FormData;return a.append("".concat(e,"_certificate"),t),(await r.A.patch("/api/auth/profile/",a,{headers:{"Content-Type":"multipart/form-data"}})).data}catch(t){throw console.error("Error uploading certificate:",t),t}},async uploadSemesterMarksheet(t,e){try{let a=new FormData;return a.append("marksheet",t),a.append("semester",e),(await r.A.post("/api/accounts/semester-marksheets/",a,{headers:{"Content-Type":"multipart/form-data"}})).data}catch(t){throw console.error("Error uploading semester marksheet:",t),t}},async getUserData(){try{return(await r.A.get("/api/auth/profile/")).data}catch(t){throw console.error("Error fetching user data:",t),t}},async updateUserProfile(t){try{return(await r.A.patch("/api/auth/profile/",t)).data}catch(t){throw console.error("Error updating user profile:",t),t}},async updateUserPassword(t){try{return(await r.A.post("/api/auth/change-password/",t)).data}catch(t){throw console.error("Password update error:",t),t}}},c={async updateSystemSettings(t){try{return(await r.A.post("/api/admin/system-settings/",t)).data}catch(t){throw console.error("Error updating system settings:",t),t}},async getSystemSettings(){try{return(await r.A.get("/api/admin/system-settings/")).data}catch(t){throw console.error("Error fetching system settings:",t),t}}},i={getEnhancedStudentMetrics:async function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"enhanced_student_stats",e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{let a={type:t};e&&(a.refresh="true");let n=await r.A.get("/api/v1/metrics/students/enhanced/",{params:a});return{success:!0,data:n.data}}catch(t){var a,n;return console.error("Error fetching enhanced student metrics:",t),{success:!1,error:(null==(n=t.response)||null==(a=n.data)?void 0:a.message)||"Failed to fetch enhanced student metrics"}}},getDepartmentStats:async function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{let a={};t&&(a.department=t),e&&(a.refresh="true");let n=await r.A.get("/api/v1/metrics/students/departments/",{params:a});return{success:!0,data:n.data}}catch(t){var a,n;return console.error("Error fetching department stats:",t),{success:!1,error:(null==(n=t.response)||null==(a=n.data)?void 0:a.message)||"Failed to fetch department statistics"}}},getYearStats:async function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;try{let n={};t&&(n.year=t),a&&(n.department=a),e&&(n.refresh="true");let s=await r.A.get("/api/v1/metrics/students/years/",{params:n});return{success:!0,data:s.data}}catch(e){var n,s,o;console.error("Error fetching year stats:",e);let t={years:[],current_year:new Date().getFullYear(),department_filter:a||null,last_updated:new Date().toISOString(),error_fallback:!0};if((null==(n=e.response)?void 0:n.status)===500)return console.warn("Server error for year stats, using fallback data"),{success:!0,data:t,fallback:!0};return{success:!1,error:(null==(o=e.response)||null==(s=o.data)?void 0:s.message)||"Failed to fetch year statistics",fallbackData:t}}},getPerformanceAnalytics:async()=>{try{let t=await r.A.get("/api/v1/metrics/students/performance/");return{success:!0,data:t.data}}catch(a){var t,e;return console.error("Error fetching performance analytics:",a),{success:!1,error:(null==(e=a.response)||null==(t=e.data)?void 0:t.message)||"Failed to fetch performance analytics"}}},getAllStudentAnalytics:async function(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{let[e,a,r,n]=await Promise.allSettled([i.getEnhancedStudentMetrics("enhanced_student_stats",t),i.getDepartmentStats(null,t),i.getYearStats(null,t),i.getPerformanceAnalytics()]),s={enhanced:"fulfilled"===e.status?e.value.data:null,departments:"fulfilled"===a.status?a.value.data:null,years:"fulfilled"===r.status?r.value.data:null,performance:"fulfilled"===n.status?n.value.data:null,errors:[]};return[e,a,r,n].forEach((t,e)=>{var a;let r=["enhanced","departments","years","performance"][e];"rejected"===t.status?s.errors.push("".concat(r,": ").concat(t.reason.message||"Unknown error")):(null==(a=t.value)?void 0:a.fallback)?s.errors.push("".concat(r,": Using fallback data due to server error")):t.value&&!t.value.success&&t.value.fallbackData&&(s[r]=t.value.fallbackData,s.errors.push("".concat(r,": ").concat(t.value.error||"API error",", using fallback")))}),s.enhanced||(s.enhanced={overview:{},last_updated:new Date().toISOString()}),s.departments||(s.departments={departments:[],last_updated:new Date().toISOString()}),s.years||(s.years={years:[],current_year:new Date().getFullYear(),last_updated:new Date().toISOString()}),s.performance||(s.performance={performance_categories:{},last_updated:new Date().toISOString()}),{success:!0,data:s,last_updated:new Date().toISOString(),has_errors:s.errors.length>0}}catch(t){return console.error("Error fetching all student analytics:",t),{success:!0,data:{enhanced:{overview:{},last_updated:new Date().toISOString()},departments:{departments:[],last_updated:new Date().toISOString()},years:{years:[],current_year:new Date().getFullYear(),last_updated:new Date().toISOString()},performance:{performance_categories:{},last_updated:new Date().toISOString()},errors:["Failed to fetch student analytics data - using fallback"]},last_updated:new Date().toISOString(),fallback:!0,has_errors:!0}}},refreshAllMetrics:async()=>{try{let t=[i.getEnhancedStudentMetrics("enhanced_student_stats",!0),i.getDepartmentStats(null,!0),i.getYearStats(null,!0)];return await Promise.all(t),{success:!0,message:"All student metrics refreshed successfully"}}catch(t){return console.error("Error refreshing student metrics:",t),{success:!1,error:"Failed to refresh student metrics"}}}}}}]);