(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2190],{4229:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},5040:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},13717:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},15717:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>I});var a=s(95155),l=s(53904),r=s(71007),n=s(47924),i=s(29869),c=s(54416),o=s(12115),d=s(35695),m=s(69327),u=s(28835),x=s(73983);s(66766);var h=s(35169),p=s(4229),g=s(13717),f=s(32919),y=s(19946);let j=(0,y.A)("lock-open",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 9.9-1",key:"1mm8w8"}]]);var v=s(29911),b=s(52338);function N(e){let{isOpen:t,onClose:s,documents:l={},onUploadCertificate:r,onUploadMarksheet:n,onUploadSuccess:i,onDeleteCertificate:c,onDeleteMarksheet:d}=e,[m,u]=(0,o.useState)("tenth"),[x,h]=(0,o.useState)(!1),[p,g]=(0,o.useState)({tenth:[],twelfth:[],semesterwise:[]}),f=(0,o.useRef)(null),y=(0,o.useRef)(null),j=(0,o.useRef)(null),b=e=>e?e&&!e.startsWith("http")?"http://localhost:8000".concat(e):e:null,N=e=>e&&"string"==typeof e&&""!==e.trim()&&"null"!==e&&"undefined"!==e;(0,o.useEffect)(()=>{let e={tenth:[],twelfth:[],semesterwise:[]};if(N(l.tenth)){let t="string"==typeof l.tenth?l.tenth.split("/"):["10th Certificate"],s=t[t.length-1];s&&""!==s&&"null"!==s&&(e.tenth=[{id:1,name:s,date:new Date().toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"}),url:l.tenth}])}if(N(l.twelfth)){let t="string"==typeof l.twelfth?l.twelfth.split("/"):["12th Certificate"],s=t[t.length-1];s&&""!==s&&"null"!==s&&(e.twelfth=[{id:1,name:s,date:new Date().toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"}),url:l.twelfth}])}l.semesterMarksheets&&Array.isArray(l.semesterMarksheets)&&l.semesterMarksheets.length>0&&(e.semesterwise=l.semesterMarksheets.filter(e=>e&&N(e.marksheet_url)).map(e=>({id:e.id,name:"Semester ".concat(e.semester," Marksheet (CGPA: ").concat(e.cgpa,")"),date:e.upload_date?new Date(e.upload_date).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"}):"Unknown date",url:e.marksheet_url||e.marksheet_file,semester:e.semester,cgpa:e.cgpa}))),g(e)},[l]);let w=async e=>{let t=e.target.files[0];if(t)try{if(h(!0),"tenth"===m){await r(t,"10th");let e={id:Date.now(),name:t.name,date:new Date().toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"}),url:URL.createObjectURL(t)};g(t=>({...t,tenth:[e]})),i&&i()}else if("twelfth"===m){await r(t,"12th");let e={id:Date.now(),name:t.name,date:new Date().toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"}),url:URL.createObjectURL(t)};g(t=>({...t,twelfth:[e]})),i&&i()}else if("semesterwise"===m){if(!y.current||!j.current){alert("Semester input fields are not available"),h(!1);return}let e=y.current.value,s=j.current.value;if(!e||!s){alert("Please enter semester number and CGPA"),h(!1);return}await n(t,e,s);let a={id:Date.now(),name:"Semester ".concat(e," Marksheet (CGPA: ").concat(s,")"),date:new Date().toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"}),url:URL.createObjectURL(t),semester:e,cgpa:s};g(e=>({...e,semesterwise:[...e.semesterwise,a]})),i&&i()}h(!1)}catch(e){console.error("Error uploading document:",e),h(!1),alert("Failed to upload document. Please try again.")}},_=e=>{if(!e)return void alert("Document URL is not available");let t=b(e);window.open(t,"_blank")},S=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{"tenth"===m||"twelfth"===m?(await c("tenth"===m?"10th":"12th"),g(e=>({...e,[m]:[]})),i&&i()):"semesterwise"===m&&t?(await d(t.semester),g(e=>({...e,semesterwise:e.semesterwise.filter(e=>e.semester!==t.semester)})),i&&i()):g(t=>({...t,[m]:t[m].filter(t=>t.id!==e)}))}catch(e){console.error("Error deleting document:",e),alert("Failed to delete document. Please try again.")}};return t?(0,a.jsx)("div",{className:"fixed inset-0 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg w-full max-w-2xl shadow-xl",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center p-6 border-b",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-800",children:"My Documents"}),(0,a.jsx)("button",{onClick:s,className:"text-gray-500 hover:text-gray-700",children:(0,a.jsx)(v._Hm,{size:24})})]}),(0,a.jsxs)("div",{className:"flex border-b",children:[(0,a.jsx)("button",{className:"px-6 py-3 text-sm font-medium ".concat("tenth"===m?"text-blue-600 border-b-2 border-blue-600":"text-gray-500 hover:text-gray-700"),onClick:()=>u("tenth"),children:"10th Certificate"}),(0,a.jsx)("button",{className:"px-6 py-3 text-sm font-medium ".concat("twelfth"===m?"text-blue-600 border-b-2 border-blue-600":"text-gray-500 hover:text-gray-700"),onClick:()=>u("twelfth"),children:"12th Certificate"}),(0,a.jsx)("button",{className:"px-6 py-3 text-sm font-medium ".concat("semesterwise"===m?"text-blue-600 border-b-2 border-blue-600":"text-gray-500 hover:text-gray-700"),onClick:()=>u("semesterwise"),children:"Semester Grades"})]}),(0,a.jsx)("div",{className:"p-6 max-h-96 overflow-y-auto",children:0===p[m].length?(0,a.jsx)("div",{className:"text-center py-8 text-gray-500",children:"No documents uploaded for this category yet"}):(0,a.jsx)("div",{className:"space-y-4",children:p[m].map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center justify-between bg-gray-50 p-4 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center flex-grow cursor-pointer hover:bg-gray-100 p-2 rounded",onClick:()=>_(e.url),children:[(0,a.jsx)("div",{className:"bg-blue-100 p-3 rounded-full mr-4",children:(0,a.jsx)(v.t69,{className:"text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("h3",{className:"font-medium text-gray-800 mr-2",children:e.name}),(0,a.jsx)(v.EQc,{className:"text-gray-500 text-xs"})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Uploaded on ",e.date]})]})]}),(0,a.jsx)("button",{onClick:()=>S(e.id,e),className:"p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full ml-2","aria-label":"Delete document",children:(0,a.jsx)(v.qbC,{})})]},e.id||"".concat(m,"-").concat(t)))})}),(0,a.jsxs)("div",{className:"border-t p-6",children:["semesterwise"===m&&(0,a.jsxs)("div",{className:"mb-4 grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Semester Number"}),(0,a.jsx)("input",{type:"number",min:"1",max:"8",ref:y,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"e.g., 1"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"CGPA"}),(0,a.jsx)("input",{type:"number",step:"0.01",min:"0",max:"10",ref:j,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"e.g., 8.5"})]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Supported formats: PDF, JPG, PNG (max 5MB)"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("input",{type:"file",accept:".pdf,.jpg,.jpeg,.png",className:"hidden",ref:f,onChange:w,disabled:x}),(0,a.jsx)("button",{onClick:()=>f.current.click(),className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center ".concat(x?"opacity-70 cursor-not-allowed":""),disabled:x,children:x?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(v.hW,{className:"mr-2 animate-spin"})," Uploading..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(v.HVe,{className:"mr-2"})," Upload Document"]})})]})]})]})]})}):null}function w(e){let{isOpen:t,onClose:s,resume:l,onUpload:r,onDelete:n,studentId:i,isAdminMode:c=!1}=e,[d,m]=(0,o.useState)([]),[u,x]=(0,o.useState)(!1),[h,p]=(0,o.useState)(!1),g=(0,o.useRef)(null);(0,o.useEffect)(()=>{t&&f()},[t,i]);let f=async()=>{try{if(p(!0),!localStorage.getItem("access_token")){console.error("No authentication token found"),m([]),p(!1);return}let e=[];if(c&&i)try{console.log("Admin mode: Fetching resumes for student ID:",i),e=await b.N.adminGetResumes(i),console.log("Admin resumes fetched:",e),Array.isArray(e)||(console.error("Invalid admin resume data format:",e),e=[])}catch(t){console.error("Failed to fetch admin resumes:",t),e=[]}else try{if(console.log("Student mode: Fetching user-specific resumes..."),e=await b.N.getResumes(),console.log("Student resumes fetched:",e),!Array.isArray(e))throw console.error("Invalid resume data format:",e),Error("Invalid resume data format")}catch(t){console.log("New resumes API not available, falling back to profile data:",t);try{let t=await b.N.getProfile();if(console.log("User profile fetched for resume fallback:",null==t?void 0:t.id),(null==t?void 0:t.resume)||(null==t?void 0:t.resume_url)){let s=t.resume_url||t.resume,a=s.split("/").pop()||"Resume.pdf";e=[{id:t.id||1,name:a,resume_url:s,uploaded_at:t.updated_at||new Date().toISOString()}]}}catch(e){console.error("Error fetching profile for resume:",e)}}let t=e.map((e,t)=>{var s;return{id:e.id||t+1,name:e.name||e.file_name||(null==(s=e.resume_url)?void 0:s.split("/").pop())||"Resume ".concat(t+1),date:e.uploaded_at?new Date(e.uploaded_at).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"}):new Date().toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"}),url:e.resume_url||e.file_url||e.url,status:"success"}});console.log("Displaying ".concat(t.length," resumes for current user")),m(t)}catch(e){if(console.error("Error fetching resumes:",e),l){let e=[];if("string"==typeof l&&""!==l.trim()){let t=l.split("/"),s=t[t.length-1];e.push({id:1,name:s||"Resume",date:new Date().toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"}),url:l,status:"success"})}m(e)}else m([])}finally{p(!1)}},y=async e=>{let t=e.target.files[0];if(t)try{if(x(!0),t.size>5242880){alert("File size exceeds 5MB limit. Please select a smaller file."),x(!1);return}let e={id:Date.now(),name:t.name,date:new Date().toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"}),file:t,url:URL.createObjectURL(t),status:"uploading",progress:0};m(t=>[...t,e]);let s=setInterval(()=>{m(t=>t.map(t=>t.id===e.id?{...t,progress:Math.min(t.progress+25,99)}:t))},500);try{await r(t),clearInterval(s),await f()}catch(t){throw clearInterval(s),m(t=>t.map(t=>t.id===e.id?{...t,status:"error",progress:0}:t)),t}x(!1)}catch(e){console.error("Error uploading resume:",e),x(!1),alert("Failed to upload resume. Please try again.")}},j=e=>{if(!e)return void alert("Resume URL is not available");if(e.startsWith("blob:"))window.open(e,"_blank");else if(e.startsWith("http://")||e.startsWith("https://"))window.open(e,"_blank");else{let t="".concat(window.location.origin).concat(e.startsWith("/")?"":"/").concat(e);window.open(t,"_blank")}},N=async e=>{try{let t=d.find(t=>t.id===e);if(m(t=>t.filter(t=>t.id!==e)),t&&t.id&&"number"==typeof t.id)try{let e=await b.N.deleteResume(t.id);console.log("Resume deletion response:",e);try{let e=Object.keys(localStorage).filter(e=>e.includes("resume")||e.includes("file")||e.includes("document"));e.length>0&&(console.log("Clearing resume-related localStorage items:",e),e.forEach(e=>localStorage.removeItem(e))),localStorage.removeItem("resume_count"),localStorage.removeItem("last_resume_update");let t=localStorage.getItem("user_profile");if(t)try{let e=JSON.parse(t);e&&e.resume&&(e.resume=null,localStorage.setItem("user_profile",JSON.stringify(e)))}catch(e){}}catch(e){console.error("Error clearing localStorage:",e)}}catch(e){console.error("Backend delete failed, but UI is updated:",e)}if("function"==typeof n)try{await n(t)}catch(e){console.error("onDelete callback error:",e)}await f()}catch(e){console.error("Error in delete process:",e),await f()}},w=e=>"uploading"===e.status?(0,a.jsx)("div",{className:"ml-2 text-blue-500",children:(0,a.jsx)(v.hW,{className:"animate-spin"})}):"success"===e.status?(0,a.jsx)("div",{className:"ml-2 text-green-500",children:(0,a.jsx)(v.A7C,{})}):"error"===e.status?(0,a.jsx)("div",{className:"ml-2 text-red-500",children:(0,a.jsx)(v.TNq,{})}):null;return t?(0,a.jsx)("div",{className:"fixed inset-0 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg w-full max-w-2xl shadow-xl",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center p-6 border-b",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-800",children:"My Resumes"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Upload multiple resumes for different job types"})]}),(0,a.jsx)("button",{onClick:s,className:"text-gray-500 hover:text-gray-700",children:(0,a.jsx)(v._Hm,{size:24})})]}),(0,a.jsx)("div",{className:"p-6 max-h-96 overflow-y-auto",children:h?(0,a.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,a.jsx)(v.hW,{className:"animate-spin text-blue-500 text-2xl mr-3"}),(0,a.jsx)("span",{className:"text-gray-600",children:"Loading resumes..."})]}):0===d.length?(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)("p",{children:"No resumes uploaded yet"}),(0,a.jsx)("p",{className:"text-sm mt-2",children:"You can upload multiple resumes for different job applications"})]}):(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsxs)("h3",{className:"text-md font-medium text-gray-700",children:["Your Resumes (",d.length,")"]}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:d.length>1?"You can use different resumes for different applications":""})]}),(0,a.jsx)("div",{className:"space-y-4",children:d.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between bg-gray-50 p-4 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center flex-grow cursor-pointer hover:bg-gray-100 p-2 rounded",onClick:()=>"uploading"!==e.status?j(e.url):null,children:[(0,a.jsx)("div",{className:"bg-blue-100 p-3 rounded-full mr-4",children:(0,a.jsx)(v.t69,{className:"text-blue-600"})}),(0,a.jsxs)("div",{className:"flex-grow",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("h3",{className:"font-medium text-gray-800 mr-2",children:e.name}),w(e),"uploading"!==e.status&&(0,a.jsx)(v.EQc,{className:"text-gray-500 text-xs ml-2"})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Uploaded on ",e.date]}),"uploading"===e.status&&(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5 mt-2",children:(0,a.jsx)("div",{className:"bg-blue-600 h-2.5 rounded-full transition-all duration-300",style:{width:"".concat(e.progress,"%")}})})]})]}),(0,a.jsx)("button",{onClick:()=>N(e.id),className:"p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full ml-2","aria-label":"Delete resume",disabled:"uploading"===e.status,children:(0,a.jsx)(v.qbC,{className:"uploading"===e.status?"opacity-50":""})})]},e.id))})]})}),(0,a.jsxs)("div",{className:"border-t p-6 flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Supported formats: PDF, DOCX (max 5MB)"}),(0,a.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"You can upload multiple resumes tailored to different positions"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("input",{type:"file",accept:".pdf,.docx",className:"hidden",ref:g,onChange:y,disabled:u}),(0,a.jsx)("button",{onClick:()=>g.current.click(),className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center ".concat(u?"opacity-70 cursor-not-allowed":""),disabled:u,children:u?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(v.hW,{className:"mr-2 animate-spin"})," Uploading..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(v.HVe,{className:"mr-2"})," Add Resume"]})})]})]})]})}):null}var _=s(1243);function S(e){var t,s;let{isOpen:l,onClose:r,onFreeze:n,onUnfreeze:i,studentName:d,currentFreezeStatus:m="none",currentFreezeData:u=null}=e,[x,h]=(0,o.useState)("complete"),[p,g]=(0,o.useState)(""),[y,v]=(0,o.useState)(!1),[b,N]=(0,o.useState)(""),[w,S]=(0,o.useState)([]),[k,C]=(0,o.useState)([]),[A,z]=(0,o.useState)([]),P=async()=>{if(!p.trim())return void alert("Please provide a reason for freezing the account.");v(!0);try{let e={freeze_type:x,reason:p.trim()};"partial"===x&&(e.min_salary_requirement=b?parseFloat(b):null,e.allowed_job_tiers=w,e.allowed_job_types=k,e.allowed_companies=A),await n(e),r()}catch(e){console.error("Error freezing account:",e),alert("Failed to freeze account. Please try again.")}finally{v(!1)}},E=async()=>{v(!0);try{await i(),r()}catch(e){console.error("Error unfreezing account:",e),alert("Failed to unfreeze account. Please try again.")}finally{v(!1)}},L=e=>{S(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},F=e=>{C(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])};return l?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800",children:"none"===m?"Freeze Account":"Manage Account Freeze"}),(0,a.jsx)("button",{onClick:r,className:"text-gray-500 hover:text-gray-700",children:(0,a.jsx)(c.A,{size:24})})]}),"none"===m?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(_.A,{className:"text-blue-600",size:20}),(0,a.jsxs)("span",{className:"font-semibold text-blue-800",children:["Freeze Account: ",d]})]}),(0,a.jsx)("p",{className:"text-blue-700 text-sm",children:"Choose how you want to restrict this student's account access."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Freeze Type *"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("label",{className:"flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50",children:[(0,a.jsx)("input",{type:"radio",name:"freezeType",value:"complete",checked:"complete"===x,onChange:e=>h(e.target.value),className:"mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-800",children:"Complete Freeze"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Student cannot login to the system at all"})]})]}),(0,a.jsxs)("label",{className:"flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50",children:[(0,a.jsx)("input",{type:"radio",name:"freezeType",value:"partial",checked:"partial"===x,onChange:e=>h(e.target.value),className:"mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-800",children:"Partial Freeze"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Student can login but with restricted job application access"})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Reason for Freeze *"}),(0,a.jsx)("textarea",{value:p,onChange:e=>g(e.target.value),className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",rows:3,placeholder:"Enter the reason for freezing this account...",required:!0})]}),"partial"===x&&(0,a.jsxs)("div",{className:"space-y-6 border-t pt-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"Partial Freeze Restrictions"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Minimum Salary Requirement (in Lakhs)"}),(0,a.jsx)("input",{type:"number",value:b,onChange:e=>N(e.target.value),className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"e.g., 10 (for 10 LPA minimum)",min:"0",step:"0.1"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Allowed Job Tiers"}),(0,a.jsx)("div",{className:"space-y-2",children:[{value:"tier1",label:"Tier 1 (Top Companies)"},{value:"tier2",label:"Tier 2 (Mid-level Companies)"},{value:"tier3",label:"Tier 3 (Startups/Small Companies)"}].map(e=>(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:w.includes(e.value),onChange:()=>L(e.value),className:"mr-2"}),e.label]},e.value))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Allowed Job Types"}),(0,a.jsx)("div",{className:"space-y-2",children:[{value:"fulltime",label:"Full Time"},{value:"internship",label:"Internship"},{value:"contract",label:"Contract"},{value:"parttime",label:"Part Time"}].map(e=>(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:k.includes(e.value),onChange:()=>F(e.value),className:"mr-2"}),e.label]},e.value))})]})]}),(0,a.jsxs)("div",{className:"flex justify-end gap-3 pt-6 border-t",children:[(0,a.jsx)("button",{onClick:r,className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors",disabled:y,children:"Cancel"}),(0,a.jsx)("button",{onClick:P,disabled:y,className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50",children:y?"Freezing...":"Freeze Account"})]})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(f.A,{className:"text-yellow-600",size:20}),(0,a.jsxs)("span",{className:"font-semibold text-yellow-800",children:["Account Currently Frozen: ","complete"===m?"Complete Freeze":"Partial Freeze"]})]}),(0,a.jsx)("p",{className:"text-yellow-700 text-sm",children:(null==u?void 0:u.freeze_reason)||"No reason provided"}),(null==u?void 0:u.freeze_date)&&(0,a.jsxs)("p",{className:"text-yellow-700 text-sm mt-1",children:["Frozen on: ",new Date(u.freeze_date).toLocaleDateString()]})]}),"partial"===m&&(null==u?void 0:u.freeze_restrictions)&&(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-800 mb-3",children:"Current Restrictions:"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[u.freeze_restrictions.min_salary_requirement&&(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Min Salary:"})," ",u.freeze_restrictions.min_salary_requirement," LPA"]}),(null==(t=u.freeze_restrictions.allowed_job_tiers)?void 0:t.length)>0&&(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Allowed Tiers:"})," ",u.freeze_restrictions.allowed_job_tiers.join(", ")]}),(null==(s=u.freeze_restrictions.allowed_job_types)?void 0:s.length)>0&&(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Allowed Types:"})," ",u.freeze_restrictions.allowed_job_types.join(", ")]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end gap-3 pt-6 border-t",children:[(0,a.jsx)("button",{onClick:r,className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors",disabled:y,children:"Close"}),(0,a.jsxs)("button",{onClick:E,disabled:y,className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 flex items-center gap-2",children:[(0,a.jsx)(j,{size:16}),y?"Unfreezing...":"Unfreeze Account"]})]})]})]})}):null}function k(e){var t,s;let{selectedStudent:l,editedStudent:r,isEditing:n,handleBackToList:i,handleEdit:d,handleSave:m,handleCancel:u,handleInputChange:x,departmentOptions:y}=e,[_,k]=(0,o.useState)(null),[C,A]=(0,o.useState)(!1),[z,P]=(0,o.useState)(null),[E,L]=(0,o.useState)([]),[F,D]=(0,o.useState)(!1),[M,R]=(0,o.useState)(!1),[U,I]=(0,o.useState)(!1),[O,T]=(0,o.useState)({loading:!1,totalListings:0,eligibleJobs:0}),[G,B]=(0,o.useState)(0),[Y,V]=(0,o.useState)(null),[J,W]=(0,o.useState)("none"),[H,q]=(0,o.useState)(null),K=e=>e&&"string"==typeof e&&""!==e.trim()&&"null"!==e&&"undefined"!==e;(0,o.useEffect)(()=>{l&&(k(l),X(l.id),Q(l.id),Z(l.id),$(l.id))},[l]);let X=async e=>{if(e)try{A(!0);let t=await b.N.getStudent(e);t&&(k(t),t.semester_marksheets&&L(t.semester_marksheets)),A(!1)}catch(e){console.error("Error fetching student details:",e),A(!1)}},Q=async e=>{if(e)try{T(e=>({...e,loading:!0})),T({loading:!1,totalListings:25,eligibleJobs:18})}catch(e){console.error("Error fetching company stats:",e),T({loading:!1,totalListings:0,eligibleJobs:0})}},Z=async e=>{if(e)try{let t=await b.N.adminGetResumes(e);if(Array.isArray(t))if(B(t.length),t.length>0){let e=t.reduce((e,t)=>{let s=new Date(t.uploaded_at),a=new Date(e.uploaded_at);return s>a?t:e});V(e.uploaded_at)}else V(null);else B(0),V(null)}catch(e){console.error("Error fetching resume info:",e),B(0),V(null)}},$=async e=>{if(e)try{let t=await fetch("http://127.0.0.1:8000/api/accounts/students/".concat(e,"/freeze/"),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("access")),"Content-Type":"application/json"}});if(t.ok){let e=await t.json();W(e.freeze_status),q(e)}else W("none"),q(null)}catch(e){console.error("Error fetching freeze status:",e),W("none"),q(null)}},ee=()=>(null==r?void 0:r.gpa)||(null==_?void 0:_.gpa)||"N/A",et=e=>{if(!e||"N/A"===e)return"";let t=parseFloat(e);return isNaN(t)?"":(9.5*t).toFixed(2)+"%"},es=e=>{if(E&&E.length>0){var t;return(null==(t=E[e-1])?void 0:t.cgpa)||"-"}if((null==r?void 0:r.semester_cgpas)&&r.semester_cgpas.length>0){let t=r.semester_cgpas.find(t=>t.semester===e);return(null==t?void 0:t.cgpa)||"-"}if((null==_?void 0:_.semester_cgpas)&&_.semester_cgpas.length>0){let t=_.semester_cgpas.find(t=>t.semester===e);return(null==t?void 0:t.cgpa)||"-"}return"-"},ea=(e,t)=>{let s=[...(null==r?void 0:r.semester_cgpas)||[]],a=s.findIndex(t=>t.semester===e);a>=0?s[a]={...s[a],cgpa:t}:s.push({semester:e,cgpa:t}),s.sort((e,t)=>e.semester-t.semester),x("semester_cgpas",s)},el=async e=>{try{if(!l)return void alert("No student selected");await b.N.adminUploadResume(l.id,e,e.name,!1),alert("Resume uploaded successfully!"),Z(l.id)}catch(e){var t,s;console.error("Error uploading resume:",e),alert("Failed to upload resume: ".concat((null==(s=e.response)||null==(t=s.data)?void 0:t.error)||e.message||"Unknown error"))}},er=async e=>{try{await b.N.deleteResume(e),l&&Z(l.id)}catch(e){console.error("Error deleting resume:",e),alert("Failed to delete resume. Please try again.")}},en=async e=>{try{let t=await fetch("http://127.0.0.1:8000/api/accounts/students/".concat(l.id,"/freeze/"),{method:"POST",headers:{Authorization:"Bearer ".concat(localStorage.getItem("access")),"Content-Type":"application/json"},body:JSON.stringify(e)});if(t.ok){let e=await t.json();alert(e.message),$(l.id)}else{let e=await t.json();throw Error(e.detail||"Failed to freeze account")}}catch(e){throw console.error("Error freezing account:",e),e}},ei=async()=>{try{let e=await fetch("http://127.0.0.1:8000/api/accounts/students/".concat(l.id,"/freeze/"),{method:"DELETE",headers:{Authorization:"Bearer ".concat(localStorage.getItem("access")),"Content-Type":"application/json"}});if(e.ok){let t=await e.json();alert(t.message),$(l.id)}else{let t=await e.json();throw Error(t.detail||"Failed to unfreeze account")}}catch(e){throw console.error("Error unfreezing account:",e),e}};return C?(0,a.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,a.jsx)(v.hW,{className:"animate-spin text-blue-500 text-xl mr-2"}),(0,a.jsx)("span",{children:"Loading details..."})]}):l||r?(0,a.jsxs)("div",{className:"bg-gray-50 min-h-screen p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsxs)("button",{onClick:i,className:"flex items-center gap-2 text-blue-600 hover:text-blue-800 transition-colors",children:[(0,a.jsx)(h.A,{size:16}),(0,a.jsx)("span",{children:"Back to List"})]}),(0,a.jsx)("div",{className:"flex gap-2",children:n?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{onClick:m,className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(p.A,{size:16}),(0,a.jsx)("span",{children:"Save"})]})}),(0,a.jsx)("button",{onClick:u,className:"px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(c.A,{size:16}),(0,a.jsx)("span",{children:"Cancel"})]})})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{onClick:d,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(g.A,{size:16}),(0,a.jsx)("span",{children:"Edit"})]})}),(0,a.jsx)("button",{onClick:()=>I(!0),className:"px-4 py-2 rounded-lg transition-colors flex items-center gap-2 ".concat("none"===J?"bg-red-600 text-white hover:bg-red-700":"complete"===J?"bg-orange-600 text-white hover:bg-orange-700":"bg-yellow-600 text-white hover:bg-yellow-700"),children:"none"===J?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f.A,{size:16}),(0,a.jsx)("span",{children:"Freeze"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(j,{size:16}),(0,a.jsx)("span",{children:"Manage Freeze"})]})})]})})]}),(0,a.jsxs)("div",{className:"max-w-8xl mx-auto grid grid-cols-1 lg:grid-cols-12 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-3 bg-white rounded-lg p-5 shadow-sm h-fit",children:[(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)("div",{className:"w-50 h-50 bg-blue-500 text-white flex items-center justify-center rounded-lg mb-4",children:(0,a.jsx)("span",{className:"text-3xl font-bold",children:(null==r?void 0:r.name)?r.name[0].toUpperCase():"S"})})}),(0,a.jsx)("h1",{className:"text-xl font-bold text-center mt-2 text-gray-800",children:n?(0,a.jsx)("input",{type:"text",value:(null==r?void 0:r.name)||"",onChange:e=>x("name",e.target.value),className:"w-full p-1 border rounded text-center"}):(null==r?void 0:r.name)||"Unknown Student"}),(0,a.jsxs)("div",{className:"mt-4 space-y-3 text-md",children:[(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"Student ID"}),(0,a.jsxs)("p",{className:"font-medium text-gray-800",children:[": ",n?(0,a.jsx)("input",{type:"text",value:(null==r?void 0:r.rollNumber)||"",onChange:e=>x("rollNumber",e.target.value),className:"p-1 border rounded"}):(null==r?void 0:r.rollNumber)||"-"]})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"Major"}),(0,a.jsxs)("p",{className:"font-medium text-gray-800",children:[": ",n?(0,a.jsxs)("select",{value:(null==r?void 0:r.department)||"",onChange:e=>x("department",e.target.value),className:"p-1 border rounded",children:[(0,a.jsx)("option",{value:"",children:"Select Department"}),y.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))]}):(null==r?void 0:r.department)||"-"]})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"Passed Out"}),(0,a.jsxs)("p",{className:"font-medium text-gray-800",children:[": ",n?(0,a.jsx)("input",{type:"text",value:(null==r?void 0:r.passout_year)||"",onChange:e=>x("passout_year",e.target.value),className:"p-1 border rounded"}):(null==r?void 0:r.passout_year)||"-"]})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"Gender"}),(0,a.jsxs)("p",{className:"font-medium text-gray-800",children:[": ",n?(0,a.jsxs)("select",{value:(null==r?void 0:r.gender)||"",onChange:e=>x("gender",e.target.value),className:"p-1 border rounded",children:[(0,a.jsx)("option",{value:"",children:"Select Gender"}),(0,a.jsx)("option",{value:"Male",children:"Male"}),(0,a.jsx)("option",{value:"Female",children:"Female"}),(0,a.jsx)("option",{value:"Other",children:"Other"})]}):(null==r?void 0:r.gender)||"-"]})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"Birthday"}),(0,a.jsxs)("p",{className:"font-medium text-gray-800",children:[": ",n?(0,a.jsx)("input",{type:"date",value:(null==r?void 0:r.dateOfBirth)||"",onChange:e=>x("dateOfBirth",e.target.value),className:"p-1 border rounded"}):(null==r?void 0:r.dateOfBirth)||"-"]})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"Phone"}),(0,a.jsxs)("p",{className:"font-medium text-gray-800",children:[": ",n?(0,a.jsx)("input",{type:"tel",value:(null==r?void 0:r.phone)||"",onChange:e=>x("phone",e.target.value),className:"p-1 border rounded"}):(null==r?void 0:r.phone)||"-"]})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"Email"}),(0,a.jsxs)("p",{className:"font-medium text-gray-800",children:[": ",n?(0,a.jsx)("input",{type:"email",value:(null==r?void 0:r.email)||"",onChange:e=>x("email",e.target.value),className:"p-1 border rounded"}):(null==r?void 0:r.email)||"-"]})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"Campus"}),(0,a.jsxs)("p",{className:"font-medium text-gray-800",children:[": ",n?(0,a.jsx)("input",{type:"text",value:(null==r?void 0:r.college_name)||"",onChange:e=>x("college_name",e.target.value),className:"p-1 border rounded"}):(null==r?void 0:r.college_name)||"-"]})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"Placement"}),(0,a.jsxs)("p",{className:"font-medium text-gray-800",children:[": ",((null==_?void 0:_.joining_year)&&(null==_?void 0:_.passout_year)?"".concat(_.joining_year," - ").concat(_.passout_year):(null==r?void 0:r.joining_year)&&(null==r?void 0:r.passout_year)?"".concat(r.joining_year," - ").concat(r.passout_year):(null==_?void 0:_.year)||(null==r?void 0:r.year)?(null==_?void 0:_.year)||(null==r?void 0:r.year):null)||"-"]})]})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-3 text-gray-800",children:"Skills"}),n?(0,a.jsx)("textarea",{value:Array.isArray(null==r?void 0:r.skills)?r.skills.join(", "):(null==r?void 0:r.skills)||"",onChange:e=>x("skills",e.target.value.split(",").map(e=>e.trim())),className:"w-full p-2 border rounded-lg",rows:3,placeholder:"Enter skills separated by commas"}):(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:(()=>{let e=(null==r?void 0:r.skills)||(null==_?void 0:_.skills)||(null==l?void 0:l.skills);return Array.isArray(e)&&e.length>0?e.map((e,t)=>(0,a.jsx)("span",{className:"bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm",children:e},t)):"string"==typeof e&&e.trim()?e.split(",").map((e,t)=>(0,a.jsx)("span",{className:"bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm",children:e.trim()},t)):(0,a.jsx)("p",{className:"text-gray-600",children:"No skills listed"})})()})]})]}),(0,a.jsxs)("div",{className:"lg:col-span-6 space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg p-5 shadow-sm",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-6 text-gray-800",children:"Academic"}),(0,a.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"Semester Wise score"}),(0,a.jsx)("div",{className:"bg-blue-50 inline-flex items-center px-3 py-1 rounded-full ml-3",children:n?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("input",{type:"text",value:(null==r?void 0:r.gpa)||"",onChange:e=>x("gpa",e.target.value),className:"w-16 p-1 border rounded text-sm text-blue-600 bg-transparent",placeholder:"0.00"}),(0,a.jsx)("span",{className:"text-sm text-gray-500 ml-1",children:"CGPA"}),(0,a.jsx)("span",{className:"text-blue-600 ml-2",children:et(null==r?void 0:r.gpa)})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"text-blue-600 font-medium",children:ee()}),(0,a.jsx)("span",{className:"text-sm text-gray-500 ml-1",children:"CGPA"}),(0,a.jsx)("span",{className:"text-blue-600 ml-2",children:et(ee())})]})})]}),(0,a.jsx)("div",{className:"text-lg font-semibold text-gray-800",children:n?(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("input",{type:"text",value:(null==r?void 0:r.joining_year)||"",onChange:e=>x("joining_year",e.target.value),className:"w-20 p-1 border rounded text-sm",placeholder:"2020"}),(0,a.jsx)("span",{children:"-"}),(0,a.jsx)("input",{type:"text",value:(null==r?void 0:r.passout_year)||"",onChange:e=>x("passout_year",e.target.value),className:"w-20 p-1 border rounded text-sm",placeholder:"2024"})]}):(t=null==r?void 0:r.joining_year,s=null==r?void 0:r.passout_year,t&&s?"".concat(t," - ").concat(s):"N/A")})]}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full border-collapse border border-gray-300",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"bg-gray-50",children:[(0,a.jsx)("th",{className:"border border-gray-300 px-4 py-3 text-sm font-semibold text-gray-700",children:"Sem"}),[1,2,3,4,5,6,7,8].map(e=>(0,a.jsx)("th",{className:"border border-gray-300 px-4 py-3 text-sm font-semibold text-gray-700",children:e},e))]})}),(0,a.jsx)("tbody",{children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"border border-gray-300 px-4 py-3 text-sm font-medium text-gray-700",children:"Cgpa"}),[1,2,3,4,5,6,7,8].map(e=>(0,a.jsx)("td",{className:"border border-gray-300 px-4 py-3 text-sm text-gray-700",children:n?(0,a.jsx)("input",{type:"text",value:"-"!==es(e)?es(e):"",onChange:t=>ea(e,t.target.value),className:"w-full p-1 border rounded text-sm text-center",placeholder:"0.0"}):es(e)},e))]})})]})})}),(0,a.jsx)("hr",{className:"my-6"}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"Class XII"}),(0,a.jsx)("div",{className:"bg-blue-50 inline-flex items-center px-3 py-1 rounded-full ml-3",children:n?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("input",{type:"text",value:(null==r?void 0:r.twelfth_cgpa)||"",onChange:e=>x("twelfth_cgpa",e.target.value),className:"w-16 p-1 border rounded text-sm text-blue-600 bg-transparent",placeholder:"9.5"}),(0,a.jsx)("span",{className:"text-sm text-gray-500 ml-1",children:"CGPA"}),(0,a.jsx)("input",{type:"text",value:(null==r?void 0:r.twelfth_percentage)||"",onChange:e=>x("twelfth_percentage",e.target.value),className:"w-16 p-1 border rounded text-sm text-blue-600 bg-transparent ml-2",placeholder:"95%"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"text-blue-600 font-medium",children:(null==r?void 0:r.twelfth_cgpa)||"-"}),(0,a.jsx)("span",{className:"text-sm text-gray-500 ml-1",children:"CGPA"}),(0,a.jsx)("span",{className:"text-blue-600 ml-2",children:(null==r?void 0:r.twelfth_percentage)||"-"})]})})]}),(0,a.jsx)("div",{className:"text-lg font-semibold text-gray-800",children:n?(0,a.jsx)("input",{type:"text",value:(null==r?void 0:r.twelfth_year_of_passing)||"",onChange:e=>x("twelfth_year_of_passing",e.target.value),className:"w-20 p-1 border rounded text-sm",placeholder:"2020"}):(null==r?void 0:r.twelfth_year_of_passing)?"".concat(parseInt(r.twelfth_year_of_passing)-2," - ").concat(r.twelfth_year_of_passing):"-"})]}),(0,a.jsx)("div",{className:"flex justify-between items-start mb-2",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-6 w-full",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 text-sm w-[120px]",children:"College :"}),n?(0,a.jsx)("input",{type:"text",value:(null==r?void 0:r.twelfth_school)||"",onChange:e=>x("twelfth_school",e.target.value),className:"flex-1 p-1 border rounded text-sm",placeholder:"School/College name"}):(0,a.jsx)("p",{className:"text-gray-700 font-medium",children:(null==r?void 0:r.twelfth_school)||"-"})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 text-sm w-[120px]",children:"Board :"}),n?(0,a.jsx)("input",{type:"text",value:(null==r?void 0:r.twelfth_board)||"",onChange:e=>x("twelfth_board",e.target.value),className:"flex-1 p-1 border rounded text-sm",placeholder:"Board name"}):(0,a.jsx)("p",{className:"text-gray-700 font-medium",children:(null==r?void 0:r.twelfth_board)||"-"})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 text-sm w-[120px]",children:"Location :"}),n?(0,a.jsx)("input",{type:"text",value:(null==r?void 0:r.twelfth_location)||"",onChange:e=>x("twelfth_location",e.target.value),className:"flex-1 p-1 border rounded text-sm",placeholder:"City, State"}):(0,a.jsx)("p",{className:"text-gray-700 font-medium",children:(null==r?void 0:r.twelfth_location)||"-"})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 text-sm w-[120px]",children:"Specialization :"}),n?(0,a.jsx)("input",{type:"text",value:(null==r?void 0:r.twelfth_specialization)||"",onChange:e=>x("twelfth_specialization",e.target.value),className:"flex-1 p-1 border rounded text-sm",placeholder:"Science/Commerce/Arts"}):(0,a.jsx)("p",{className:"text-gray-700 font-medium",children:(null==r?void 0:r.twelfth_specialization)||"-"})]})]})]})})]}),(0,a.jsx)("hr",{className:"my-6"}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"Class X"}),(0,a.jsx)("div",{className:"bg-blue-50 inline-flex items-center px-3 py-1 rounded-full ml-3",children:n?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("input",{type:"text",value:(null==r?void 0:r.tenth_cgpa)||"",onChange:e=>x("tenth_cgpa",e.target.value),className:"w-16 p-1 border rounded text-sm text-blue-600 bg-transparent",placeholder:"9.5"}),(0,a.jsx)("span",{className:"text-sm text-gray-500 ml-1",children:"CGPA"}),(0,a.jsx)("input",{type:"text",value:(null==r?void 0:r.tenth_percentage)||"",onChange:e=>x("tenth_percentage",e.target.value),className:"w-16 p-1 border rounded text-sm text-blue-600 bg-transparent ml-2",placeholder:"95%"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"text-blue-600 font-medium",children:(null==r?void 0:r.tenth_cgpa)||"-"}),(0,a.jsx)("span",{className:"text-sm text-gray-500 ml-1",children:"CGPA"}),(0,a.jsx)("span",{className:"text-blue-600 ml-2",children:(null==r?void 0:r.tenth_percentage)||"-"})]})})]}),(0,a.jsx)("div",{className:"text-lg font-semibold text-gray-800",children:n?(0,a.jsx)("input",{type:"text",value:(null==r?void 0:r.tenth_year_of_passing)||"",onChange:e=>x("tenth_year_of_passing",e.target.value),className:"w-20 p-1 border rounded text-sm",placeholder:"2018"}):(null==r?void 0:r.tenth_year_of_passing)?"".concat(parseInt(r.tenth_year_of_passing)-1," - ").concat(r.tenth_year_of_passing):"-"})]}),(0,a.jsx)("div",{className:"flex justify-between items-start mb-2",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-6 w-full",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 text-sm w-[120px]",children:"School :"}),n?(0,a.jsx)("input",{type:"text",value:(null==r?void 0:r.tenth_school)||"",onChange:e=>x("tenth_school",e.target.value),className:"flex-1 p-1 border rounded text-sm",placeholder:"School name"}):(0,a.jsx)("p",{className:"text-gray-700 font-medium",children:(null==r?void 0:r.tenth_school)||"-"})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 text-sm w-[120px]",children:"Board :"}),n?(0,a.jsx)("input",{type:"text",value:(null==r?void 0:r.tenth_board)||"",onChange:e=>x("tenth_board",e.target.value),className:"flex-1 p-1 border rounded text-sm",placeholder:"Board name"}):(0,a.jsx)("p",{className:"text-gray-700 font-medium",children:(null==r?void 0:r.tenth_board)||"-"})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 text-sm w-[120px]",children:"Location :"}),n?(0,a.jsx)("input",{type:"text",value:(null==r?void 0:r.tenth_location)||"",onChange:e=>x("tenth_location",e.target.value),className:"flex-1 p-1 border rounded text-sm",placeholder:"City, State"}):(0,a.jsx)("p",{className:"text-gray-700 font-medium",children:(null==r?void 0:r.tenth_location)||"-"})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 text-sm w-[120px]",children:"Specialization :"}),n?(0,a.jsx)("input",{type:"text",value:(null==r?void 0:r.tenth_specialization)||"",onChange:e=>x("tenth_specialization",e.target.value),className:"flex-1 p-1 border rounded text-sm",placeholder:"General/Other"}):(0,a.jsx)("p",{className:"text-gray-700 font-medium",children:(null==r?void 0:r.tenth_specialization)||"-"})]})]})]})})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-5 shadow-sm",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4 text-gray-800",children:"Companies"}),O.loading?(0,a.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,a.jsx)(v.hW,{className:"animate-spin text-blue-500 text-xl mr-2"}),(0,a.jsx)("span",{className:"text-gray-600",children:"Loading company data..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("p",{className:"text-gray-500 text-sm",children:"Total Listings"}),(0,a.jsx)("p",{className:"text-lg font-semibold text-gray-700",children:O.totalListings})]})}),(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("p",{className:"text-gray-500 text-sm",children:"Eligible Jobs"}),(0,a.jsx)("p",{className:"text-lg font-semibold text-gray-700",children:O.eligibleJobs})]})})]})]})]}),(0,a.jsxs)("div",{className:"lg:col-span-3 space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg p-5 shadow-sm",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4 text-gray-800",children:"My Files"}),(0,a.jsxs)("div",{className:"flex items-center p-2 mb-3 cursor-pointer hover:bg-gray-50 rounded-lg transition-colors",onClick:()=>R(!0),children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-3",children:(0,a.jsx)("div",{className:"text-blue-600",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"})})})}),(0,a.jsxs)("div",{className:"flex-grow",children:[(0,a.jsx)("h3",{className:"font-medium text-gray-700",children:"Resumes"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:G>0?"".concat(G," resume").concat(G>1?"s":""," uploaded")+(Y?" • Last updated ".concat(new Date(Y).toLocaleDateString()):""):"No resumes uploaded"})]}),(0,a.jsx)("div",{className:"bg-green-50 px-3 py-1 rounded-full",children:(0,a.jsx)("span",{className:"text-green-600 font-medium",children:G})})]}),(0,a.jsxs)("div",{className:"flex items-center p-2 cursor-pointer hover:bg-gray-50 rounded-lg transition-colors",onClick:()=>D(!0),children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-3",children:(0,a.jsx)("div",{className:"text-blue-600",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})})}),(0,a.jsxs)("div",{className:"flex-grow",children:[(0,a.jsx)("h3",{className:"font-medium text-gray-700",children:"Documents"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Academic certificates and marksheets"})]}),(0,a.jsx)("div",{className:"bg-green-50 px-3 py-1 rounded-full",children:(0,a.jsx)("span",{className:"text-green-600 font-medium",children:(()=>{let e=+!!K(null==_?void 0:_.tenth_certificate);return e+ +!!K(null==_?void 0:_.twelfth_certificate)+(E?E.filter(e=>e&&K(e.marksheet_url)).length:0)})()})})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-5 shadow-sm",children:[(0,a.jsx)("div",{className:"flex justify-between items-center mb-4",children:(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-800",children:"CURRENT ADDRESS"})}),(0,a.jsxs)("div",{className:"space-y-3 text-sm",children:[(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"City"}),(0,a.jsxs)("p",{className:"font-medium text-gray-700",children:[": ",n?(0,a.jsx)("input",{type:"text",value:(null==r?void 0:r.city)||"",onChange:e=>x("city",e.target.value),className:"flex-1 p-1 border rounded text-sm",placeholder:"City name"}):(null==r?void 0:r.city)||"-"]})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"District"}),(0,a.jsxs)("p",{className:"font-medium text-gray-700",children:[": ",n?(0,a.jsx)("input",{type:"text",value:(null==r?void 0:r.district)||"",onChange:e=>x("district",e.target.value),className:"flex-1 p-1 border rounded text-sm",placeholder:"District name"}):(null==r?void 0:r.district)||"-"]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"State"}),(0,a.jsx)("span",{className:"mr-2",children:":"}),n?(0,a.jsx)("input",{type:"text",value:(null==r?void 0:r.state)||"",onChange:e=>x("state",e.target.value),className:"flex-1 p-2 border rounded text-sm",placeholder:"State name"}):(0,a.jsx)("p",{className:"font-medium text-gray-700",children:(null==r?void 0:r.state)||"-"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"Pin Code"}),(0,a.jsx)("span",{className:"mr-2",children:":"}),n?(0,a.jsx)("input",{type:"text",value:(null==r?void 0:r.pincode)||"",onChange:e=>x("pincode",e.target.value),className:"flex-1 p-2 border rounded text-sm",placeholder:"Pin code"}):(0,a.jsx)("p",{className:"font-medium text-gray-700",children:(null==r?void 0:r.pincode)||"-"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"Country"}),(0,a.jsx)("span",{className:"mr-2",children:":"}),n?(0,a.jsx)("input",{type:"text",value:(null==r?void 0:r.country)||"",onChange:e=>x("country",e.target.value),className:"flex-1 p-2 border rounded text-sm",placeholder:"Country name"}):(0,a.jsx)("p",{className:"font-medium text-gray-700",children:(null==r?void 0:r.country)||"-"})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20 mt-2",children:"Address"}),(0,a.jsx)("span",{className:"mr-2 mt-2",children:":"}),n?(0,a.jsx)("textarea",{value:(null==r?void 0:r.address)||"",onChange:e=>x("address",e.target.value),className:"flex-1 p-2 border rounded text-sm",rows:3,placeholder:"Full address"}):(0,a.jsx)("p",{className:"font-medium text-gray-700 flex-1",children:(null==r?void 0:r.address)||"-"})]})]})]})]}),(0,a.jsx)(w,{isOpen:M,onClose:()=>R(!1),resume:(null==_?void 0:_.resume_url)||(null==_?void 0:_.resume),onUpload:el,onDelete:er,studentId:null==l?void 0:l.id,isAdminMode:!0}),(0,a.jsx)(N,{isOpen:F,onClose:()=>D(!1),documents:{tenth:(null==_?void 0:_.tenth_certificate_url)||(null==_?void 0:_.tenth_certificate),twelfth:(null==_?void 0:_.twelfth_certificate_url)||(null==_?void 0:_.twelfth_certificate),semesterMarksheets:E},onUploadCertificate:(e,t)=>b.N.adminUploadCertificate(l.id,e,t),onUploadMarksheet:(e,t,s)=>b.N.adminUploadSemesterMarksheet(l.id,e,t,s),onDeleteCertificate:e=>b.N.adminDeleteCertificate(l.id,e),onDeleteMarksheet:e=>b.N.adminDeleteMarksheet(l.id,e),onUploadSuccess:()=>{l&&X(l.id)}}),(0,a.jsx)(S,{isOpen:U,onClose:()=>I(!1),onFreeze:en,onUnfreeze:ei,studentName:(null==r?void 0:r.name)||(null==l?void 0:l.name)||"Unknown Student",currentFreezeStatus:J,currentFreezeData:H})]})]}):(0,a.jsxs)("div",{className:"text-center p-8",children:[(0,a.jsx)("p",{className:"text-red-500",children:"No student selected"}),(0,a.jsx)("button",{onClick:i,className:"mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Back to List"})]})}var C=s(72713),A=s(17580),z=s(5040),P=s(69037),E=s(87949);function L(e){var t,s,r,n,i;let{departmentOptions:c,departmentStats:d,totalStudents:u,onSelect:x}=e,[h,p]=(0,o.useState)(null),[g,f]=(0,o.useState)(!0),[y,j]=(0,o.useState)(null),[v,b]=(0,o.useState)(!1),N=async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{f(!0),j(null);let s=await m.n0.getAllStudentAnalytics(e);if(s.success){if(p(s.data),s.fallback||s.has_errors){var t;let e=(null==(t=s.data)?void 0:t.errors)||[];console.warn("Analytics loaded with warnings:",e),j("Some data may be incomplete: ".concat(e.join(", ")))}}else j(s.error||"Failed to load analytics")}catch(e){console.error("Error fetching analytics:",e),j("Failed to load student analytics")}finally{f(!1)}},w=async()=>{b(!0);try{let e=await m.n0.refreshAllMetrics();e.success?await N(!0):j(e.error||"Failed to refresh metrics")}catch(e){console.error("Error refreshing metrics:",e),j("Failed to refresh metrics")}finally{b(!1)}};(0,o.useEffect)(()=>{N()},[]);let _=(null==h||null==(t=h.enhanced)?void 0:t.overview)||{},S=(null==h||null==(s=h.departments)?void 0:s.departments)||[];null==h||null==(r=h.years)||r.years,null==h||null==(n=h.performance)||n.performance_categories;let k={totalStudents:_.total_students||u||0,activeDepartments:_.active_departments||d.length||0,highPerformers:_.high_performers||0,highPerformerPercentage:_.high_performer_percentage||0,placementReady:_.placement_ready||0,averageGPA:S.length>0?(S.reduce((e,t)=>e+(t.avg_gpa||0),0)/S.length).toFixed(2):"0.00"};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"mb-8 flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Student Management & Analytics"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Comprehensive overview of student data and performance"})]}),(0,a.jsxs)("button",{onClick:w,disabled:v,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors",children:[(0,a.jsx)(l.A,{className:"w-4 h-4 mr-2 ".concat(v?"animate-spin":"")}),v?"Refreshing...":"Refresh Data"]})]}),y&&(0,a.jsxs)("div",{className:"mb-6 rounded-lg p-4 ".concat(y.includes("incomplete")||y.includes("fallback")?"bg-yellow-50 border border-yellow-200":"bg-red-50 border border-red-200"),children:[(0,a.jsx)("p",{className:"".concat(y.includes("incomplete")||y.includes("fallback")?"text-yellow-600":"text-red-600"),children:y.includes("incomplete")||y.includes("fallback")?"⚠️ ".concat(y):y}),(0,a.jsx)("button",{onClick:()=>N(),className:"mt-2 px-4 py-2 text-white rounded hover:opacity-90 text-sm ".concat(y.includes("incomplete")||y.includes("fallback")?"bg-yellow-600 hover:bg-yellow-700":"bg-red-600 hover:bg-red-700"),children:"Retry Load"})]}),(0,a.jsxs)("div",{className:"mb-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-800",children:"Overview Analytics"}),(0,a.jsx)(C.A,{className:"w-5 h-5 text-gray-400"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:k.totalStudents.toLocaleString()}),(0,a.jsx)("div",{className:"text-sm text-blue-700 font-medium",children:"Total Students"}),(0,a.jsx)("div",{className:"text-xs text-blue-600 mt-1",children:"All registered students"})]}),(0,a.jsx)(A.A,{className:"w-8 h-8 text-blue-600"})]})}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:k.activeDepartments}),(0,a.jsx)("div",{className:"text-sm text-green-700 font-medium",children:"Active Departments"}),(0,a.jsxs)("div",{className:"text-xs text-green-600 mt-1",children:["Avg GPA: ",k.averageGPA]})]}),(0,a.jsx)(z.A,{className:"w-8 h-8 text-green-600"})]})}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:k.highPerformers.toLocaleString()}),(0,a.jsx)("div",{className:"text-sm text-purple-700 font-medium",children:"High Performers"}),(0,a.jsxs)("div",{className:"text-xs text-purple-600 mt-1",children:[k.highPerformerPercentage.toFixed(1),"% (GPA ≥ 8.5)"]})]}),(0,a.jsx)(P.A,{className:"w-8 h-8 text-purple-600"})]})}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:k.placementReady.toLocaleString()}),(0,a.jsx)("div",{className:"text-sm text-orange-700 font-medium",children:"Placement Ready"}),(0,a.jsx)("div",{className:"text-xs text-orange-600 mt-1",children:"Current year eligible"})]}),(0,a.jsx)(E.A,{className:"w-8 h-8 text-orange-600"})]})})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Browse by Department"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:c.map(e=>{let t=d.find(t=>{var s,a,l,r,n,i;return(null==(s=t.department)?void 0:s.toLowerCase().trim())===(null==(a=e.value)?void 0:a.toLowerCase().trim())||(null==(r=t.department)?void 0:r.toLowerCase().includes(null==(l=e.value)?void 0:l.toLowerCase()))||(null==(i=e.value)?void 0:i.toLowerCase().includes(null==(n=t.department)?void 0:n.toLowerCase()))}),s=S.find(t=>{var s,a,l,r,n,i;return(null==(s=t.branch)?void 0:s.toLowerCase().trim())===(null==(a=e.value)?void 0:a.toLowerCase().trim())||(null==(r=t.branch)?void 0:r.toLowerCase().includes(null==(l=e.value)?void 0:l.toLowerCase()))||(null==(i=e.value)?void 0:i.toLowerCase().includes(null==(n=t.branch)?void 0:n.toLowerCase()))}),l=(null==t?void 0:t.count)||(null==s?void 0:s.total_students)||0;return(0,a.jsxs)("div",{onClick:()=>x(e.value),className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md hover:border-blue-300 cursor-pointer transition-all duration-200 group",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("div",{className:"p-3 bg-blue-50 rounded-lg group-hover:bg-blue-100 transition-colors",children:(0,a.jsx)(E.A,{className:"w-6 h-6 text-blue-600"})}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:l}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Students"})]})]}),(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:e.label}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 mb-3",children:["View and manage ",e.label.toLowerCase()," students"]}),s&&(0,a.jsx)("div",{className:"border-t pt-3 mt-3",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-xs",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"font-semibold text-blue-600",children:s.avg_gpa?s.avg_gpa.toFixed(2):"N/A"}),(0,a.jsx)("div",{className:"text-gray-500",children:"Avg GPA"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"font-semibold text-purple-600",children:s.high_performers||0}),(0,a.jsx)("div",{className:"text-gray-500",children:"Top Performers"})]})]})})]},e.value)})})]}),g&&(0,a.jsxs)("div",{className:"flex items-center justify-center h-32",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,a.jsx)("span",{className:"ml-2 text-gray-600",children:"Loading analytics..."})]}),(null==h||null==(i=h.enhanced)?void 0:i.last_updated)&&(0,a.jsxs)("div",{className:"text-center text-sm text-gray-500 mt-6",children:["Last updated: ",new Date(h.enhanced.last_updated).toLocaleString()]})]})}let F=(0,y.A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);var D=s(85339),M=s(69074);function R(e){let{departmentLabel:t,onBack:s,getAvailablePassoutYears:l,selectedDepartment:r,onSelectYear:n,yearStats:i=[],students:c=[],isLoading:o=!1,error:d=null}=e;console.log("PassoutYearCards - yearStats:",i),console.log("PassoutYearCards - students:",c),console.log("PassoutYearCards - selectedDepartment:",r);let m=l().slice().sort((e,t)=>Number(t)-Number(e));return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)("div",{className:"flex items-center gap-4 mb-4",children:[(0,a.jsx)("button",{onClick:s,className:"p-2 hover:bg-gray-100 rounded-full transition-colors",children:(0,a.jsx)(h.A,{className:"w-5 h-5 text-gray-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold text-gray-900",children:[t," - Passout Years"]}),(0,a.jsx)("p",{className:"text-gray-600",children:"Select a passout year to view students"})]})]})}),o&&(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center py-12",children:[(0,a.jsx)(F,{className:"w-10 h-10 text-blue-500 animate-spin mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading passout years data..."})]}),d&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(D.A,{className:"w-5 h-5 text-red-500 mr-2"}),(0,a.jsxs)("p",{className:"text-red-600",children:["Error loading data: ",d]})]})}),!o&&!d&&0===m.length&&(0,a.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-8 text-center",children:[(0,a.jsx)(M.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-700 mb-2",children:"No Passout Years Found"}),(0,a.jsxs)("p",{className:"text-gray-600 mb-4",children:["There are no passout years available for ",t]})]}),!o&&!d&&m.length>0&&(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6",children:m.map(e=>{let t=i.find(t=>{let s=t.passout_year||t.year||t.passoutYear;return(null==s?void 0:s.toString())===(null==e?void 0:e.toString())}),s=(null==t?void 0:t.total_students)||(null==t?void 0:t.count)||0;if(0===s&&i.length>0){let t=i.find(t=>t.year_metadata&&t.year_metadata[e]&&t.year_metadata[e].total);t&&t.year_metadata&&(s=t.year_metadata[e].total||0)}if(0===s&&c.length>0&&(s=c.filter(t=>{let s=(t.department||t.branch||t.dept||"").toLowerCase().trim(),a=(r||"").toLowerCase().trim();s===a||s.includes(a)||a.includes(s);let l=t.passoutYear||t.passout_year||t.year||t.expectedGraduationYear||t.expected_graduation_year||t.graduation_year;return(null==l?void 0:l.toString())===(null==e?void 0:e.toString())}).length),0===s&&r){let t=window.parent_component_context;t&&t.totalStudents&&t.selectedPassoutYear===e&&(s=t.totalStudents)}return(0,a.jsxs)("div",{onClick:()=>n(e),className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md hover:border-blue-300 cursor-pointer transition-all duration-200 group",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("div",{className:"p-3 bg-purple-50 rounded-lg group-hover:bg-purple-100 transition-colors",children:(0,a.jsx)(M.A,{className:"w-6 h-6 text-purple-600"})}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:s}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Total Students"})]})]}),(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:e}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Passout Year"})]},e)})})]})}function U(e){let{departmentLabel:t,passoutYear:s,onBack:l,searchTerm:r,handleSearchInputChange:i,handleSearchKeyDown:c,cgpaMin:o,setCgpaMin:d,cgpaMax:m,setCgpaMax:u,handleSearch:x,getFilteredStudents:p,currentPage:g,handlePageChange:f,handleStudentClick:y,loading:j,totalPages:v,totalStudents:b}=e,N=p(),w=v||1,_=b||0;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)("div",{className:"flex items-center gap-4 mb-4",children:[(0,a.jsx)("button",{onClick:l,className:"p-2 hover:bg-gray-100 rounded-full transition-colors",children:(0,a.jsx)(h.A,{className:"w-5 h-5 text-gray-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold text-gray-900",children:[t," - ",s," Students"]}),(0,a.jsxs)("p",{className:"text-gray-600",children:["Manage students from ",null==t?void 0:t.toLowerCase(),", passout year ",s]})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6 mb-6",children:(0,a.jsxs)("div",{className:"flex gap-4 flex-wrap",children:[(0,a.jsx)("div",{className:"flex-1 min-w-[200px]",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(n.A,{className:"h-5 w-5 text-gray-400"})}),(0,a.jsx)("input",{type:"text",placeholder:"Search by name or roll number...",value:r,onChange:i,onKeyDown:e=>{"Enter"===e.key&&x(),c&&c(e)},className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})}),(0,a.jsxs)("div",{className:"flex items-center gap-2 min-w-[220px]",children:[(0,a.jsx)("label",{className:"text-gray-700 text-sm",children:"CGPA:"}),(0,a.jsx)("input",{type:"number",step:"0.01",min:"0",max:"10",value:o,onChange:e=>d(e.target.value),placeholder:"Min",className:"w-20 px-2 py-1 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,a.jsx)("span",{className:"text-gray-500",children:"-"}),(0,a.jsx)("input",{type:"number",step:"0.01",min:"0",max:"10",value:m,onChange:e=>u(e.target.value),placeholder:"Max",className:"w-20 px-2 py-1 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsx)("button",{onClick:x,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 whitespace-nowrap",children:"Search"})]})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm overflow-hidden",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsxs)("h2",{className:"text-lg font-semibold text-gray-800 mb-4",children:["Students (",_," total, showing ",N.length," on page ",g,")"]})}),(0,a.jsxs)("div",{className:"overflow-x-auto",children:[(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Roll Number"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Name"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Email"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Year"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"CGPA"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:N.map(e=>(0,a.jsxs)("tr",{onClick:()=>y(e),className:"hover:bg-gray-50 cursor-pointer transition-colors",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.rollNumber}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-medium text-sm mr-3",children:e.name.charAt(0)}),(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.email}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.year}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800",children:e.cgpa})})]},e.id))})]}),j&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mb-2"}),(0,a.jsx)("div",{className:"text-gray-500",children:"Loading students..."})]}),0===N.length&&!j&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-gray-500 text-lg mb-2",children:"No students found"}),(0,a.jsx)("p",{className:"text-gray-400",children:r?"Try adjusting your search criteria":"No students in this department and passout year"})]})]}),w>1&&(0,a.jsxs)("div",{className:"flex items-center justify-between px-6 py-4 border-t border-gray-200 bg-gray-50",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-700",children:["Page ",g," of ",w," (",_," total students)"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("button",{onClick:()=>f(g-1),disabled:1===g,className:"px-3 py-2 rounded border text-sm ".concat(1===g?"bg-gray-100 text-gray-400 cursor-not-allowed border-gray-200":"bg-white hover:bg-gray-50 border-gray-300 text-gray-700"),children:"Previous"}),(0,a.jsx)("div",{className:"flex gap-1",children:Array.from({length:Math.min(5,w)},(e,t)=>{let s;return s=w<=5||g<=3?t+1:g>=w-2?w-4+t:g-2+t,(0,a.jsx)("button",{onClick:()=>f(s),className:"px-3 py-2 rounded text-sm ".concat(s===g?"bg-blue-600 text-white":"bg-white hover:bg-gray-50 border border-gray-300 text-gray-700"),children:s},s)})}),(0,a.jsx)("button",{onClick:()=>f(g+1),disabled:g===w,className:"px-3 py-2 rounded border text-sm ".concat(g===w?"bg-gray-100 text-gray-400 cursor-not-allowed border-gray-200":"bg-white hover:bg-gray-50 border-gray-300 text-gray-700"),children:"Next"})]})]})]})]})}function I(){var e,t;let s=(0,d.useRouter)(),h=(0,d.useSearchParams)(),{handleApiError:p,showValidationError:g,showSuccess:f}=(0,x.hN)(),[y,j]=(0,o.useState)(h.get("search")||""),[v,b]=(0,o.useState)(h.get("department")||null),[N,w]=(0,o.useState)(h.get("year")||"all"),[_,S]=(0,o.useState)(null),[C,A]=(0,o.useState)(!1),[z,P]=(0,o.useState)(null),E=(0,o.useRef)(null),[F,D]=(0,o.useState)(!1),[M,I]=(0,o.useState)([]),[O,T]=(0,o.useState)(!0),[G,B]=(0,o.useState)(null),[Y,V]=(0,o.useState)(!1),[J,W]=(0,o.useState)(parseInt(h.get("page"))||1),[H,q]=(0,o.useState)(1),[K,X]=(0,o.useState)(0),[Q,Z]=(0,o.useState)(10),[$,ee]=(0,o.useState)([]),[et,es]=(0,o.useState)([]),[ea,el]=(0,o.useState)(h.get("passout_year")||null),[er,en]=(0,o.useState)(h.get("cgpa_min")||""),[ei,ec]=(0,o.useState)(h.get("cgpa_max")||""),[eo,ed]=(0,o.useState)([]),[em,eu]=(0,o.useState)(""),[ex,eh]=(0,o.useState)(!1),[ep,eg]=(0,o.useState)([]),ef=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries({page:e.page||J.toString(),department:void 0!==e.department?e.department:v,passout_year:void 0!==e.passout_year?e.passout_year:ea,search:void 0!==e.search?e.search:y,cgpa_min:void 0!==e.cgpa_min?e.cgpa_min:er,cgpa_max:void 0!==e.cgpa_max?e.cgpa_max:ei,...e}).forEach(e=>{let[s,a]=e;a&&"all"!==a&&""!==a&&"null"!==a&&null!==a&&t.set(s,a)});let s="".concat(window.location.pathname,"?").concat(t.toString());window.history.pushState({},"",s)},ey=[{value:"Computer Science",label:"Computer Science"},{value:"Electronics",label:"Electronics"},{value:"Mechanical",label:"Mechanical"},{value:"Civil",label:"Civil"},{value:"Electrical",label:"Electrical"},{value:"Information Technology",label:"Information Technology"},{value:"Chemical",label:"Chemical"},{value:"Biotechnology",label:"Biotechnology"}],ej=e=>({id:e.id,rollNumber:e.student_id||"N/A",name:"".concat(e.first_name||""," ").concat(e.last_name||"").trim()||"Unknown",email:e.contact_email||e.email||"N/A",phone:e.phone||"N/A",department:e.branch||"N/A",year:eN(e.branch,e),cgpa:e.gpa||"N/A",gpa:e.gpa||"N/A",address:e.address||"N/A",dateOfBirth:e.date_of_birth||"",parentContact:e.parent_contact||"N/A",education:e.education||"N/A",skills:e.skills||[],joining_year:e.joining_year||e.admission_year||"",passout_year:e.passout_year||e.graduation_year||"",twelfth_cgpa:e.twelfth_cgpa||e.class_12_cgpa||"",twelfth_percentage:e.twelfth_percentage||e.class_12_percentage||"",twelfth_year_of_passing:e.twelfth_year_of_passing||e.class_12_year||"",twelfth_school:e.twelfth_school||e.class_12_school||"",twelfth_board:e.twelfth_board||e.class_12_board||"",twelfth_location:e.twelfth_location||e.class_12_location||"",twelfth_specialization:e.twelfth_specialization||e.class_12_stream||"",tenth_cgpa:e.tenth_cgpa||e.class_10_cgpa||"",tenth_percentage:e.tenth_percentage||e.class_10_percentage||"",tenth_year_of_passing:e.tenth_year_of_passing||e.class_10_year||"",tenth_school:e.tenth_school||e.class_10_school||"",tenth_board:e.tenth_board||e.class_10_board||"",tenth_location:e.tenth_location||e.class_10_location||"",tenth_specialization:e.tenth_specialization||e.class_10_stream||"",city:e.city||"",district:e.district||"",state:e.state||"",pincode:e.pincode||e.pin_code||"",country:e.country||"India",tenth_certificate:e.tenth_certificate||e.class_10_certificate||"",twelfth_certificate:e.twelfth_certificate||e.class_12_certificate||"",tenth_certificate_url:e.tenth_certificate_url||e.class_10_certificate_url||"",twelfth_certificate_url:e.twelfth_certificate_url||e.class_12_certificate_url||"",resume:e.resume||"",resume_url:e.resume_url||"",semester_cgpas:e.semester_marksheets||[],semester_marksheets:e.semester_marksheets||[]}),ev=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;try{if(T(!0),B(null),V(!1),!(0,u.c4)())throw Error("No authentication token found. Please login first.");let t={page:e,page_size:Q,ordering:"student_id"};y&&(t.search=y),v&&(t.department=v),ea&&(t.passout_year=ea),"all"!==N&&(t.year=N),er&&(t.cgpa_min=er),ei&&(t.cgpa_max=ei),console.log("Fetching students with params:",t);let s=await m.Nt.getStudents(t),a=s.data.map(ej);if(I(a),W(e),q(s.pagination.total_pages),X(s.pagination.total_count),s.metadata&&(s.metadata.available_years&&ee(s.metadata.available_years),s.metadata.available_departments)){let e=s.metadata.available_departments.map(e=>({department:e,count:0}));es(e)}T(!1)}catch(e){var t,s;console.error("Error fetching students:",e),(null==(t=e.response)?void 0:t.status)===401?(p(e,"loading student data"),B("Authentication failed. Please login again.")):(null==(s=e.response)?void 0:s.status)===403?(p(e,"accessing student management"),B("You do not have permission to view students. Admin access required.")):e.message.includes("token")?(p(e,"authentication"),B("Please login to access student management.")):(p(e,"loading student data"),B("Error: ".concat(e.message))),I([]),T(!1)}},eb=async e=>{try{if(!(0,u.c4)())return;console.log("Fetching year stats for department:",e);let t=await m.n0.getYearStats(null,!1,e);if(t.success&&t.data&&t.data.years)console.log("Year stats from API:",t.data.years),ed(t.data.years);else{console.error("Failed to fetch year stats:",t.error);let s=await m.Nt.getStudents({department:e,page_size:1e3,count_only:!0});if(console.log("Year stats response:",s),s.data){let t={},a=new Date().getFullYear();s.data.forEach(e=>{let s=e.passout_year||e.graduation_year||e.expected_graduation_year||e.passoutYear;if(s){let e=s.toString();t[e]||(t[e]={passout_year:parseInt(e),total_students:0,current_year_students:0}),t[e].total_students++,(parseInt(e)===a||parseInt(e)===a+1)&&t[e].current_year_students++}});let l=Object.values(t);console.log("Calculated year stats for department:",e,l),ed(l)}}}catch(e){console.error("Error fetching year stats:",e),ed([])}},eN=(e,t)=>t&&t.joining_year&&t.passout_year?"".concat(t.joining_year,"-").concat(t.passout_year):"N/A";(0,o.useEffect)(()=>{ev()},[]),(0,o.useEffect)(()=>{let e=()=>{let e=new URLSearchParams(window.location.search);W(parseInt(e.get("page"))||1),b(e.get("department")||null),el(e.get("passout_year")||null),j(e.get("search")||""),en(e.get("cgpa_min")||""),ec(e.get("cgpa_max")||""),ev(parseInt(e.get("page"))||1)};return window.addEventListener("popstate",e),()=>window.removeEventListener("popstate",e)},[]),(0,o.useEffect)(()=>{let e=h.get("student_id");if(e&&M.length>0){let t=M.find(t=>t.id.toString()===e);t&&(S(t),P({...t}))}},[M,h]),(0,o.useEffect)(()=>{if(!(0,u.c4)()){B("Please login to access student management."),T(!1);return}ev()},[]),(0,o.useEffect)(()=>{let e=e=>{E.current&&!E.current.contains(e.target)&&D(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),(0,o.useEffect)(()=>{W(1),ev(1)},[v,N,ea,er,ei,Q]),(0,o.useEffect)(()=>{J>1&&ev(J)},[J]);let ew=async()=>{try{T(!0);let e=e=>{if(""===e||null==e)return null;if("string"==typeof e){let t=e.trim();return""===t?null:t}return e},t=e=>{if(""===e||null==e)return null;if("string"==typeof e){let t=e.trim();if(""===t)return null;let s=parseInt(t);return isNaN(s)?null:s}return"number"==typeof e?isNaN(e)?null:e:null},s=e=>""===e||null==e?"":"string"==typeof e?e.trim():String(e).trim(),a=z.name?z.name.trim().split(" "):[],l=a[0]||"",r=a.slice(1).join(" ")||"",n={first_name:s(l),last_name:s(r),student_id:s(z.rollNumber),contact_email:e(z.email),phone:s(z.phone),branch:s(z.department),gpa:s(z.gpa),joining_year:t(z.joining_year),passout_year:t(z.passout_year),date_of_birth:e(z.dateOfBirth),address:s(z.address),city:s(z.city),district:s(z.district),state:s(z.state),pincode:s(z.pincode),country:s(z.country),parent_contact:s(z.parentContact),education:s(z.education),skills:Array.isArray(z.skills)?z.skills.filter(e=>e&&e.trim()).join(", "):s(z.skills),tenth_cgpa:s(z.tenth_cgpa),tenth_percentage:s(z.tenth_percentage),tenth_board:s(z.tenth_board),tenth_school:s(z.tenth_school),tenth_year_of_passing:s(z.tenth_year_of_passing),tenth_location:s(z.tenth_location),tenth_specialization:s(z.tenth_specialization),twelfth_cgpa:s(z.twelfth_cgpa),twelfth_percentage:s(z.twelfth_percentage),twelfth_board:s(z.twelfth_board),twelfth_school:s(z.twelfth_school),twelfth_year_of_passing:s(z.twelfth_year_of_passing),twelfth_location:s(z.twelfth_location),twelfth_specialization:s(z.twelfth_specialization)};z.semester_cgpas&&Array.isArray(z.semester_cgpas)&&z.semester_cgpas.forEach(e=>{e.semester>=1&&e.semester<=8&&e.cgpa&&(n["semester".concat(e.semester,"_cgpa")]=s(e.cgpa))});let i=Object.fromEntries(Object.entries(n).filter(e=>{let[t,s]=e;return["first_name","last_name","student_id","gpa"].includes(t)?null!=s:null!=s&&""!==s}));i.first_name||(i.first_name="Student"),i.last_name||(i.last_name=""),i.student_id||(i.student_id="TEMP_".concat(Date.now())),i.gpa||(i.gpa="0.0"),console.log("Original editedStudent:",z),console.log("Update data being sent:",i),console.log("Student ID:",z.id);let c=await m.Nt.updateStudent(z.id,i),o={...z,...c,name:"".concat(c.first_name||""," ").concat(c.last_name||"").trim(),rollNumber:c.student_id,email:c.contact_email,department:c.branch,gpa:c.gpa};I(e=>e.map(e=>e.id===z.id?o:e)),S(o),A(!1),f("Student Updated!","Student profile has been updated successfully.")}catch(s){var e;console.error("Error updating student:",s),s.response&&(console.error("Error response status:",s.response.status),console.error("Error response data:",s.response.data),console.error("Error response headers:",s.response.headers));let t="Failed to update student profile. Please try again.";if(null==(e=s.response)?void 0:e.data)if("object"==typeof s.response.data){let e=[];for(let[t,a]of Object.entries(s.response.data))Array.isArray(a)?e.push("".concat(t,": ").concat(a.join(", "))):e.push("".concat(t,": ").concat(a));e.length>0&&(t="Validation errors:\n".concat(e.join("\n")))}else s.response.data.detail?t=s.response.data.detail:s.response.data.message&&(t=s.response.data.message);g("Update Failed",{details:t})}finally{T(!1)}},e_=e=>{"Enter"===e.key&&(e.preventDefault(),v&&ea?eS():ek())},eS=()=>{W(1),ef({search:y,page:"1"}),ev(1)},ek=async()=>{try{if(T(!0),eh(!1),!(0,u.c4)())throw Error("No authentication token found. Please login first.");let e=(await m.Nt.getStudents({search:em,page_size:10})).data.map(ej);eg(e),eh(!0),T(!1)}catch(e){console.error("Error fetching search results:",e),p(e,"searching students"),eg([]),eh(!0),T(!1)}};return O?(0,a.jsx)("div",{className:"flex-1 p-6 ml-20 overflow-y-auto h-full",children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center h-full",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4"}),(0,a.jsx)("div",{className:"text-gray-600",children:"Loading students..."})]})}):G&&0===M.length?(0,a.jsx)("div",{className:"flex-1 p-6 ml-20 overflow-y-auto h-full",children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center h-full",children:[(0,a.jsxs)("div",{className:"text-red-500 mb-4 text-center max-w-md",children:[(0,a.jsx)("p",{className:"font-semibold text-lg mb-2",children:"Access Error"}),(0,a.jsx)("p",{children:G}),(0,a.jsxs)("div",{className:"mt-4 text-sm text-gray-600",children:[(0,a.jsx)("p",{children:"Possible solutions:"}),(0,a.jsxs)("ul",{className:"list-disc list-inside mt-2 text-left",children:[(0,a.jsx)("li",{children:"Make sure you're logged in with admin credentials"}),(0,a.jsx)("li",{children:"Check if your session has expired"}),(0,a.jsx)("li",{children:"Verify Django server is running on port 8000"}),(0,a.jsx)("li",{children:"Ensure proper permissions are set in Django"})]})]})]}),(0,a.jsxs)("div",{className:"flex gap-3 mt-4",children:[!G.includes("login")&&(0,a.jsxs)("button",{onClick:()=>{V(!0),ev()},className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",disabled:Y,children:[(0,a.jsx)(l.A,{className:"w-4 h-4 ".concat(Y?"animate-spin":"")}),Y?"Retrying...":"Retry"]}),(0,a.jsxs)("button",{onClick:()=>window.location.href="/login",className:"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,a.jsx)(r.A,{className:"w-4 h-4"}),"Go to Login"]})]})]})}):(0,a.jsxs)("div",{className:"flex-1 p-6 overflow-y-auto h-full",children:[!v&&!_&&(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)("input",{type:"text",value:em,onChange:e=>eu(e.target.value),onKeyDown:e_,placeholder:"Search students globally...",className:"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,a.jsxs)("button",{onClick:ek,className:"ml-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(n.A,{className:"w-4 h-4 inline-block mr-1"}),"Search"]}),(0,a.jsxs)("button",{onClick:()=>s.push("/admin/student-management/updatepage"),className:"ml-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2",children:[(0,a.jsx)(i.A,{className:"w-4 h-4"}),"Upload Excel"]})]})}),ex&&(0,a.jsx)("div",{className:"fixed inset-0 bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6 w-3/4 max-w-4xl max-h-3/4 overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold",children:"Global Search Results"}),(0,a.jsx)("button",{onClick:()=>{eh(!1),eg([])},className:"text-gray-500 hover:text-gray-700",children:(0,a.jsx)(c.A,{className:"w-5 h-5"})})]}),ep.length>0?(0,a.jsx)("div",{className:"space-y-2",children:ep.map(e=>(0,a.jsx)("div",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors",onClick:()=>{S(e),P({...e}),eh(!1)},children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-lg",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Roll: ",e.rollNumber]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Department: ",e.department]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Email: ",e.email]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["CGPA: ",e.cgpa]}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Year: ",e.passout_year]})]})]})},e.id))}):(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsxs)("p",{className:"text-gray-600 text-lg",children:['No results found for "',em,'"']}),(0,a.jsx)("p",{className:"text-gray-500 text-sm mt-2",children:"Try searching with different keywords like name, roll number, or email"})]})]})}),_?(0,a.jsx)(k,{selectedStudent:_,editedStudent:z,isEditing:C,handleBackToList:()=>{S(null),A(!1),P(null);let e=new URLSearchParams(window.location.search);e.delete("student_id");let t="".concat(window.location.pathname,"?").concat(e.toString());window.history.pushState({},"",t)},handleEdit:()=>{_&&(b(_.department),el(_.passout_year),ef({department:_.department,passout_year:_.passout_year,student_id:_.id}),A(!0))},handleSave:ew,handleCancel:()=>{P({..._}),A(!1)},handleInputChange:(e,t)=>{P(s=>({...s,[e]:t}))},departmentOptions:ey}):(0,a.jsx)(a.Fragment,{children:v?ea?(0,a.jsx)(U,{departmentLabel:null==(t=ey.find(e=>e.value===v))?void 0:t.label,passoutYear:ea,onBack:()=>el(null),searchTerm:y,handleSearchInputChange:e=>{j(e.target.value)},handleSearchKeyDown:e_,cgpaMin:er,setCgpaMin:e=>{en(e),W(1),ef({cgpa_min:e,page:"1"})},cgpaMax:ei,setCgpaMax:e=>{ec(e),W(1),ef({cgpa_max:e,page:"1"})},handleSearch:eS,getFilteredStudents:()=>M,currentPage:J,handlePageChange:e=>{e>=1&&e<=H&&e!==J&&(W(e),ef({page:e.toString()}))},handleStudentClick:e=>{S(e),P({...e}),A(!1),ef({student_id:e.id})},loading:O,totalPages:H,totalStudents:K}):(0,a.jsx)(R,{departmentLabel:null==(e=ey.find(e=>e.value===v))?void 0:e.label,onBack:()=>{b(null),w("all"),j(""),el(null),en(""),ec(""),W(1),window.history.pushState({},"",window.location.pathname)},getAvailablePassoutYears:()=>$,selectedDepartment:v,onSelectYear:e=>{console.log("Selected passout year:",e,"for department:",v),el(e),W(1),ef({passout_year:e,page:"1"})},yearStats:eo,students:M}):(0,a.jsx)(L,{departmentOptions:ey,departmentStats:et,totalStudents:K,onSelect:e=>{b(e),W(1),ef({department:e,page:"1"}),eb(e)}})})]})}},17580:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},28835:(e,t,s)=>{"use strict";function a(){try{let e=localStorage.getItem("access");if(!e)return null;let t=localStorage.getItem("user");if(t){let e=JSON.parse(t);return e.id||e.user_id||null}if(e)try{let t=e.split(".")[1],s=JSON.parse(atob(t));return s.user_id||s.id||s.sub||null}catch(e){console.error("Error decoding JWT token:",e)}return null}catch(e){return console.error("Error getting user ID:",e),null}}function l(){return localStorage.getItem("access")}function r(){try{let e=localStorage.getItem("user");return e?JSON.parse(e):null}catch(e){return console.error("Error getting user data:",e),null}}s.d(t,{F6:()=>a,c4:()=>l,gL:()=>r})},32919:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},35169:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},35695:(e,t,s)=>{"use strict";var a=s(18999);s.o(a,"useParams")&&s.d(t,{useParams:function(){return a.useParams}}),s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(t,{useSearchParams:function(){return a.useSearchParams}})},47924:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},53904:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},54712:(e,t,s)=>{Promise.resolve().then(s.bind(s,15717))},69037:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},69074:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},72713:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},87949:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("graduation-cap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,3464,485,3983,9327,2338,8441,1684,7358],()=>t(54712)),_N_E=e.O()}]);