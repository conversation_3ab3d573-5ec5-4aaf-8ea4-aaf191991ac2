(()=>{var e={};e.id=6073,e.ids=[6073],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10722:(e,t,s)=>{Promise.resolve().then(s.bind(s,11527))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11285:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var a=(0,s(6445).A)("outline","plus","IconPlus",[["path",{d:"M12 5l0 14",key:"svg-0"}],["path",{d:"M5 12l14 0",key:"svg-1"}]])},11527:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>b});var a=s(60687),r=s(43210),n=s(24664),i=s(98848),l=s(9535),o=s(37325),d=s(81080),p=s(95994),c=s(80556),m=s(90910),u=s(81172),x=s(20798),h=s(58869),g=s(53411);function b({children:e}){let[t,s]=(0,r.useState)(""),b=[{items:[{title:"Dashboard",href:"/admin/dashboard",icon:(0,a.jsx)(i.A,{})},{title:"Jobs",href:"/admin/jobs",icon:(0,a.jsx)(l.A,{})},{title:"Applications",href:"/admin/applications",icon:(0,a.jsx)(o.A,{})},{title:"Posts",href:"/admin/posts",icon:(0,a.jsx)(x.A,{className:"w-5 h-5"})},{title:"Student Management",href:"/admin/student-management",icon:(0,a.jsx)(h.A,{className:"w-5 h-5"})},{title:"Student Analytics",href:"/admin/analytics",icon:(0,a.jsx)(g.A,{className:"w-5 h-5"})},{title:"Company Management",href:"/admin/companymanagement",icon:(0,a.jsx)(d.A,{})},{title:"Forms",href:"/admin/form",icon:(0,a.jsx)(p.A,{})}]}],y=[{title:"My Profile",href:"/admin/profile",icon:(0,a.jsx)(c.A,{})},{title:"Settings",href:"../settings",icon:(0,a.jsx)(m.A,{})},{title:"Contact Support",href:"/admin/helpandsupport",icon:(0,a.jsx)(u.A,{})}];return(0,a.jsx)("div",{className:"h-screen bg-gray-50 overflow-hidden",children:(0,a.jsxs)("div",{className:"flex h-full",children:[(0,a.jsx)(n.A,{sections:b,bottomItems:y,defaultExpanded:!1,navbarHeight:"0",className:"z-20"}),(0,a.jsx)("div",{className:"flex-1 p-6 ml-20 overflow-y-auto",children:(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-md p-6 min-h-full text-black",children:e})})]})})}},12412:e=>{"use strict";e.exports=require("assert")},14401:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var a=(0,s(6445).A)("outline","search","IconSearch",[["path",{d:"M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0",key:"svg-0"}],["path",{d:"M21 21l-6 -6",key:"svg-1"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20798:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("megaphone",[["path",{d:"m3 11 18-5v12L3 14v-3z",key:"n962bs"}],["path",{d:"M11.6 16.8a3 3 0 1 1-5.8-1.6",key:"1yl0tm"}]])},21820:e=>{"use strict";e.exports=require("os")},23697:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\layout.jsx","default")},25306:(e,t,s)=>{Promise.resolve().then(s.bind(s,84125))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37325:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var a=(0,s(6445).A)("outline","clipboard-list","IconClipboardList",[["path",{d:"M9 5h-2a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-12a2 2 0 0 0 -2 -2h-2",key:"svg-0"}],["path",{d:"M9 3m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z",key:"svg-1"}],["path",{d:"M9 12l.01 0",key:"svg-2"}],["path",{d:"M13 12l2 0",key:"svg-3"}],["path",{d:"M9 16l.01 0",key:"svg-4"}],["path",{d:"M13 16l2 0",key:"svg-5"}]])},47956:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>p,routeModule:()=>m,tree:()=>d});var a=s(65239),r=s(48088),n=s(88170),i=s.n(n),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d={children:["",{children:["admin",{children:["jobs",{children:["listings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,84125)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\jobs\\listings\\page.jsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,23697)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\layout.jsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,75535)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\layout.js"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\jobs\\listings\\page.jsx"],c={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/jobs/listings/page",pathname:"/admin/jobs/listings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},48173:(e,t,s)=>{Promise.resolve().then(s.bind(s,23697))},53411:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58138:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var a=s(51060);s(51421);let r=a.A.create({baseURL:"http://127.0.0.1:8000",headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>{let t=localStorage.getItem("access_token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,async e=>{let t=e.config;if(e.response?.status===401&&!t._retry){t._retry=!0;try{let e=localStorage.getItem("refresh_token");if(e){let s=await a.A.post("http://127.0.0.1:8000/api/auth/token/refresh/",{refresh:e});return localStorage.setItem("access_token",s.data.access),t.headers.Authorization=`Bearer ${s.data.access}`,r(t)}}catch(e){console.error("Error refreshing token:",e)}}return Promise.reject(e)});let n=r},59586:(e,t,s)=>{Promise.resolve().then(s.bind(s,66881))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66881:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var a=s(60687),r=s(43210),n=s(16189),i=s(11285),l=s(14401),o=s(75518);function d({jobs:e=[],onTogglePublish:t}){return 0===e.length?(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center py-16 text-center",children:[(0,a.jsx)("div",{className:"text-gray-400 mb-4",children:(0,a.jsx)("svg",{className:"w-16 h-16 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6M8 8v10m8-10v10"})})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-500 mb-2",children:"No job listings found"}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Job listings will appear here once they are created"})]}):(0,a.jsx)("div",{className:"overflow-auto",children:(0,a.jsxs)("table",{className:"min-w-full text-sm text-left border-collapse",children:[(0,a.jsx)("thead",{className:"bg-gray-50 text-gray-700 sticky top-0",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-4 py-3 font-medium",children:"Company"}),(0,a.jsx)("th",{className:"px-4 py-3 font-medium",children:"Role"}),(0,a.jsx)("th",{className:"px-4 py-3 font-medium",children:"Type"}),(0,a.jsx)("th",{className:"px-4 py-3 font-medium",children:"CTC"}),(0,a.jsx)("th",{className:"px-4 py-3 font-medium",children:"Stipend"}),(0,a.jsx)("th",{className:"px-4 py-3 font-medium",children:"Deadline"}),(0,a.jsx)("th",{className:"px-4 py-3 font-medium",children:"Status"}),t&&(0,a.jsx)("th",{className:"px-4 py-3 font-medium",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"divide-y divide-gray-200",children:e.map((e,s)=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50 transition-colors",children:[(0,a.jsx)("td",{className:"px-4 py-4 font-medium text-gray-900",children:e.companyName}),(0,a.jsx)("td",{className:"px-4 py-4 text-gray-700",children:e.title}),(0,a.jsx)("td",{className:"px-4 py-4",children:(0,a.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-medium rounded-full ${"Full-Time"===e.type?"bg-green-100 text-green-800":"Internship"===e.type?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800"}`,children:e.type})}),(0,a.jsx)("td",{className:"px-4 py-4 text-gray-700",children:e.ctc?`${e.ctc} LPA`:"-"}),(0,a.jsx)("td",{className:"px-4 py-4 text-gray-700",children:e.stipend?`${e.stipend} INR `:"-"}),(0,a.jsx)("td",{className:"px-4 py-4 text-gray-700",children:e.deadline||e.application_deadline}),(0,a.jsx)("td",{className:"px-4 py-4",children:(0,a.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-medium rounded-full ${e.is_published?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:e.is_published?"Published":"To be Published"})}),t&&(0,a.jsx)("td",{className:"px-4 py-4",children:(0,a.jsx)("button",{onClick:()=>t(e.id,e.is_published),className:`px-3 py-1 text-xs font-medium rounded-md transition-colors ${e.is_published?"bg-red-100 text-red-700 hover:bg-red-200":"bg-green-100 text-green-700 hover:bg-green-200"}`,children:e.is_published?"Unpublish":"Publish"})})]},s))})]})})}function p({typeFilter:e,setTypeFilter:t,minCTC:s,setMinCTC:r,maxCTC:n,setMaxCTC:i,minStipend:l,setMinStipend:o,maxStipend:d,setMaxStipend:p,deadlineFilter:c,setDeadlineFilter:m}){return(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-3",children:[(0,a.jsxs)("select",{value:e,onChange:e=>t(e.target.value),className:"w-[100px] px-2 py-2 text-sm rounded-md border border-gray-300 bg-gray-100",children:[(0,a.jsx)("option",{value:"All",children:"All"}),(0,a.jsx)("option",{value:"Full-Time",children:"Full-Time"}),(0,a.jsx)("option",{value:"Internship",children:"Intern"})]}),(0,a.jsx)("input",{type:"number",placeholder:"Min CTC",value:s,onChange:e=>r(e.target.value),className:"w-[90px] px-2 py-2 text-sm rounded-md border border-gray-300 bg-gray-100"}),(0,a.jsx)("input",{type:"number",placeholder:"Max CTC",value:n,onChange:e=>i(e.target.value),className:"w-[90px] px-2 py-2 text-sm rounded-md border border-gray-300 bg-gray-100"}),(0,a.jsx)("input",{type:"number",placeholder:"Min Stipend",value:l,onChange:e=>o(e.target.value),className:"w-[120px] px-2 py-2 text-sm rounded-md border border-gray-300 bg-gray-100"}),(0,a.jsx)("input",{type:"number",placeholder:"Max Stipend",value:d,onChange:e=>p(e.target.value),className:"w-[120px] px-2 py-2 text-sm rounded-md border border-gray-300 bg-gray-100"}),(0,a.jsx)("input",{type:"date",value:c,onChange:e=>m(e.target.value),className:"w-[140px] px-2 py-2 text-sm rounded-md border border-gray-300 bg-gray-100"}),(0,a.jsx)("button",{onClick:()=>{t("All"),r(""),i(""),o(""),p(""),m("")},className:"px-3 py-2 text-sm font-medium bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors flex-shrink-0",children:"Reset"})]})}function c(){let e=(0,n.useRouter)(),t=(0,n.useSearchParams)(),[s,c]=(0,r.useState)([]),[m,u]=(0,r.useState)(!0),[x,h]=(0,r.useState)(null),[g,b]=(0,r.useState)(1),[y,v]=(0,r.useState)(0),[f,j]=(0,r.useState)(0),[N,C]=(0,r.useState)(0),[_,A]=(0,r.useState)(0),w=Number(t.get("page"))||1,k=t.get("search")||"",S=t.get("type")||"All",M=t.get("minCTC")||"",P=t.get("maxCTC")||"",T=t.get("minStipend")||"",$=t.get("maxStipend")||"",q=t.get("deadline")||"",D=({page:t=w,search:s=k,type:a=S,min_ctc:r=M,max_ctc:n=P,min_stipend:i=T,max_stipend:l=$,deadline:o=q})=>{let d=new URLSearchParams;t>1&&d.set("page",t),s&&d.set("search",s),"All"!==a&&d.set("type",a),r&&d.set("minCTC",r),n&&d.set("maxCTC",n),i&&d.set("minStipend",i),l&&d.set("maxStipend",l),o&&d.set("deadline",o),e.push(`/admin/jobs/listings${d.toString()?`?${d.toString()}`:""}`)},E=async(e,t)=>{try{await (0,o.T4)(e);let s=await (0,o.Om)({per_page:20,page:w,search:k,job_type:"All"!==S?S:void 0,salary_min:M||void 0,salary_max:P||void 0,stipend_min:T||void 0,stipend_max:$||void 0,deadline:q||void 0}),a=(Array.isArray(s.data?.data)?s.data.data:Array.isArray(s.data)?s.data:[]).map(e=>({...e,companyName:e.company_name,title:e.title,type:e.job_type,ctc:e.salary_max||0,stipend:"INTERNSHIP"===e.job_type&&e.salary_max||0,deadline:e.application_deadline}));c(a),alert(`Job ${!t?"published":"unpublished"} successfully!`)}catch(e){console.error("Error toggling job publish status:",e),alert("Failed to update job status. Please try again.")}};return(0,a.jsx)("div",{className:"p-6",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Job Listings"}),(0,a.jsxs)("p",{className:"text-gray-600",children:["Showing ",s.length," jobs (Page ",w," of ",g,", Total: ",y," jobs)"]})]}),(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsx)("button",{onClick:()=>e.push("/admin/jobs/companies"),className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700",children:"View Companies"}),(0,a.jsxs)("button",{onClick:()=>e.push("/admin/jobs/create"),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2",children:[(0,a.jsx)(i.A,{size:18}),"Post New Job"]})]})]}),(0,a.jsxs)("div",{className:"mt-6 grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:y}),(0,a.jsx)("div",{className:"text-sm text-blue-700",children:"Total Jobs"})]}),(0,a.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:f}),(0,a.jsx)("div",{className:"text-sm text-green-700",children:"Published"})]}),(0,a.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:N}),(0,a.jsx)("div",{className:"text-sm text-yellow-700",children:"To be Published"})]}),(0,a.jsxs)("div",{className:"bg-purple-50 p-4 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:_}),(0,a.jsx)("div",{className:"text-sm text-purple-700",children:"Internships"})]})]}),(0,a.jsxs)("div",{className:"mb-6 space-y-4",children:[(0,a.jsxs)("div",{className:"relative max-w-md",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(l.A,{className:"h-5 w-5 text-gray-400"})}),(0,a.jsx)("input",{type:"text",placeholder:"Search jobs...",value:k,onChange:e=>D({search:e.target.value,page:1}),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsx)(p,{typeFilter:S,setTypeFilter:e=>D({type:e,page:1}),minCTC:M,setMinCTC:e=>D({min_ctc:e,page:1}),maxCTC:P,setMaxCTC:e=>D({max_ctc:e,page:1}),minStipend:T,setMinStipend:e=>D({min_stipend:e,page:1}),maxStipend:$,setMaxStipend:e=>D({max_stipend:e,page:1}),deadlineFilter:q,setDeadlineFilter:e=>D({deadline:e,page:1})})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:0===s.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("p",{className:"text-gray-500 text-lg",children:"No job listings found"}),(0,a.jsx)("p",{className:"text-gray-400 mb-4",children:"Try adjusting your search criteria or create a new job posting"}),(0,a.jsx)("button",{onClick:()=>e.push("/admin/jobs/create"),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Post New Job"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d,{jobs:s,onTogglePublish:E}),(0,a.jsxs)("div",{className:"flex items-center justify-between px-4 py-3 border-t border-gray-200",children:[(0,a.jsxs)("div",{className:"flex-1 flex justify-between sm:hidden",children:[(0,a.jsx)("button",{onClick:()=>D({page:Math.max(w-1,1)}),disabled:1===w,className:`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${1===w?"bg-gray-100 text-gray-400":"bg-white text-gray-700 hover:bg-gray-50"}`,children:"Previous"}),(0,a.jsx)("button",{onClick:()=>D({page:Math.min(w+1,g)}),disabled:w===g,className:`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${w===g?"bg-gray-100 text-gray-400":"bg-white text-gray-700 hover:bg-gray-50"}`,children:"Next"})]}),(0,a.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,a.jsx)("div",{children:(0,a.jsxs)("p",{className:"text-sm text-gray-700",children:["Showing page ",(0,a.jsx)("span",{className:"font-medium",children:w})," of"," ",(0,a.jsx)("span",{className:"font-medium",children:g})," (",y," total jobs)"]})}),(0,a.jsx)("div",{children:(0,a.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,a.jsx)("button",{onClick:()=>D({page:Math.max(w-1,1)}),disabled:1===w,className:`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${1===w?"text-gray-300":"text-gray-500 hover:bg-gray-50"}`,children:"Previous"}),[...Array(Math.min(5,g))].map((e,t)=>{let s;return s=g<=5||w<=3?t+1:w>=g-2?g-4+t:w-2+t,(0,a.jsx)("button",{onClick:()=>D({page:s}),className:`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${w===s?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-500 hover:bg-gray-50"}`,children:s},s)}),(0,a.jsx)("button",{onClick:()=>D({page:Math.min(w+1,g)}),disabled:w===g,className:`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${w===g?"text-gray-300":"text-gray-500 hover:bg-gray-50"}`,children:"Next"})]})})]})]})]})})]})})}},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},75518:(e,t,s)=>{"use strict";s.d(t,{G$:()=>l,N6:()=>r,Om:()=>p,T4:()=>c,YQ:()=>i,_S:()=>o,lh:()=>d,vr:()=>n});var a=s(58138);function r(e={}){let t=new URLSearchParams;e.page&&t.append("page",e.page),e.per_page&&t.append("per_page",e.per_page),e.job_type&&"ALL"!==e.job_type&&t.append("job_type",e.job_type),e.location&&"ALL"!==e.location&&t.append("location",e.location),e.salary_min&&t.append("salary_min",e.salary_min),e.search&&t.append("search",e.search);let s=t.toString(),n=`/api/v1/college/default-college/jobs/${s?`?${s}`:""}`;return a.A.get(n)}function n(e,t,s={}){if(!Object.values(s).some(e=>e instanceof File))return a.A.post(`/api/v1/college/default-college/jobs/${e}/apply/`,{cover_letter:t,additional_field_responses:s});{let r=new FormData;return r.append("cover_letter",t),Object.entries(s).forEach(([e,t])=>{t instanceof File?r.append(e,t):r.append(e,JSON.stringify(t))}),a.A.post(`/api/v1/college/default-college/jobs/${e}/apply/`,r,{headers:{"Content-Type":"multipart/form-data"}})}}function i(e){return a.A.get(`/api/v1/college/default-college/jobs/${e}/`)}function l(){return a.A.get("/api/v1/college/default-college/jobs/applied/")}function o(e){return a.A.post("/api/v1/college/default-college/jobs/create/",e)}function d(e,t){return a.A.put(`/api/v1/college/default-college/jobs/${e}/`,t)}function p(e={}){let t=new URLSearchParams;e.page&&t.append("page",e.page),e.per_page&&t.append("per_page",e.per_page),e.search&&t.append("search",e.search),e.type&&"All"!==e.type&&t.append("job_type",e.type),e.minCTC&&t.append("salary_min",e.minCTC),e.maxCTC&&t.append("salary_max",e.maxCTC),e.minStipend&&t.append("stipend_min",e.minStipend),e.maxStipend&&t.append("stipend_max",e.maxStipend),e.location&&t.append("location",e.location),void 0!==e.is_published&&t.append("is_published",e.is_published),e.company_id&&t.append("company_id",e.company_id),e.company_name&&t.append("company_name",e.company_name);let s=t.toString(),r=`/api/v1/college/default-college/jobs/admin/${s?`?${s}`:""}`;return console.log("\uD83C\uDF10 listJobsAdmin calling URL:",r,"with params:",e),a.A.get(r).then(e=>(console.log("\uD83C\uDF10 listJobsAdmin response:",{status:e.status,totalJobs:e.data?.pagination?.total_count||0,currentPage:e.data?.pagination?.current_page||1,totalPages:e.data?.pagination?.total_pages||1}),e)).catch(e=>{throw console.error("\uD83C\uDF10 listJobsAdmin error:",e),console.error("\uD83C\uDF10 listJobsAdmin error response:",e.response?.data),e})}function c(e){return a.A.patch(`/api/v1/jobs/${e}/toggle-publish/`)}},79551:e=>{"use strict";e.exports=require("url")},81172:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var a=(0,s(6445).A)("outline","help","IconHelp",[["path",{d:"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0",key:"svg-0"}],["path",{d:"M12 17l0 .01",key:"svg-1"}],["path",{d:"M12 13.5a1.5 1.5 0 0 1 1 -1.5a2.6 2.6 0 1 0 -3 -4",key:"svg-2"}]])},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84125:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\jobs\\\\listings\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\jobs\\listings\\page.jsx","default")},94735:e=>{"use strict";e.exports=require("events")},95994:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var a=(0,s(6445).A)("outline","forms","IconForms",[["path",{d:"M12 3a3 3 0 0 0 -3 3v12a3 3 0 0 0 3 3",key:"svg-0"}],["path",{d:"M6 3a3 3 0 0 1 3 3v12a3 3 0 0 1 -3 3",key:"svg-1"}],["path",{d:"M13 7h7a1 1 0 0 1 1 1v8a1 1 0 0 1 -1 1h-7",key:"svg-2"}],["path",{d:"M5 7h-1a1 1 0 0 0 -1 1v8a1 1 0 0 0 1 1h1",key:"svg-3"}],["path",{d:"M17 12h.01",key:"svg-4"}],["path",{d:"M13 12h.01",key:"svg-5"}]])}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4447,681,1658,1060,2305],()=>s(47956));module.exports=a})();