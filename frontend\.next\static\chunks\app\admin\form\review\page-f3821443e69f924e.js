(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3358],{1243:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},16701:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var r=(0,s(86467).A)("outline","trash","IconTrash",[["path",{d:"M4 7l16 0",key:"svg-0"}],["path",{d:"M10 11l0 6",key:"svg-1"}],["path",{d:"M14 11l0 6",key:"svg-2"}],["path",{d:"M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2 -2l1 -12",key:"svg-3"}],["path",{d:"M9 7v-3a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v3",key:"svg-4"}]])},19111:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var r=s(95155),a=s(12115),l=s(35695),i=(0,s(86467).A)("outline","eye","IconEye",[["path",{d:"M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0",key:"svg-0"}],["path",{d:"M21 12c-2.4 4 -5.4 6 -9 6c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6",key:"svg-1"}]]),n=s(31503),d=s(16701),o=s(30699),c=s(85329),h=s(59852);function u(){let e=(0,l.useRouter)(),[t,s]=(0,a.useState)([]),[u,m]=(0,a.useState)(!1),[p,x]=(0,a.useState)(null),[g,y]=(0,a.useState)(null);(0,a.useEffect)(()=>{(async()=>{m(!0);try{let e=await (0,c.Uq)(),t=Array.isArray(null==e?void 0:e.data)?e.data:[];s(t)}catch(e){console.error("Error loading forms:",e),s([])}finally{m(!1)}})()},[]);let v=e=>"posted"===e?(0,r.jsx)("span",{className:"px-3 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium",children:"Posted"}):"approved"===e?(0,r.jsx)("span",{className:"px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium",children:"Approved"}):"interview"===e?(0,r.jsx)("span",{className:"px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium",children:"Interview"}):(0,r.jsx)("span",{className:"px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-xs font-medium",children:"Pending"}),f=t=>{e.push("/admin/form/edit/".concat(t.id))},b=async e=>{try{await (0,c.i6)(e),s((Array.isArray(t)?t:[]).filter(t=>t.id!==e)),y(null)}catch(t){console.error("Error deleting form:",t),console.log("Error details:",t.response);let e="Failed to delete form. Please try again.";t.response&&(405===t.response.status?(e="The server does not support this operation. Please contact the administrator.",console.log("Available methods:",t.response.headers.get("Allow"))):t.response.data&&t.response.data.detail&&(e="Error: ".concat(t.response.data.detail))),alert(e)}},j=async e=>{try{let r={...e,status:"posted"};await (0,c.wi)(e.id,r),s((Array.isArray(t)?t:[]).map(t=>t.id===e.id?r:t)),x(null),alert("Form has been posted successfully!")}catch(e){console.error("Error posting form:",e),alert("Failed to post form. Please try again.")}};return(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)(h.A,{}),(0,r.jsx)("div",{className:"p-6 ml-64 w-full",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,r.jsx)("div",{className:"flex justify-between items-center mb-6",children:(0,r.jsx)("h1",{className:"text-2xl font-semibold text-black",children:"Review Forms"})}),(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-black mb-6",children:"Submitted Forms"}),u?(0,r.jsx)("div",{className:"flex justify-center items-center h-40",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-10 w-10 border-b-2 border-blue-600"})}):Array.isArray(t)&&0!==t.length?(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Array.isArray(t)&&t.map(e=>{var t,s;return(0,r.jsx)("div",{className:"bg-white border rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow",children:(0,r.jsxs)("div",{className:"p-5",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-bold text-black",children:e.company}),e.details&&(0,r.jsxs)("p",{className:"text-sm text-gray-600 mt-1 line-clamp-2",children:[null==(t=e.details.description)?void 0:t.substring(0,100),(null==(s=e.details.description)?void 0:s.length)>100?"...":""]})]}),(0,r.jsx)("div",{children:v(e.status||(e.submitted?"approved":"pending"))})]}),(0,r.jsxs)("div",{className:"text-xs text-gray-500 mb-4 space-y-1",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Key:"})," ",e.key]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Status:"})," ",e.submitted?"Completed":"Not Submitted"]}),e.details&&e.details.deadline&&(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Deadline:"})," ",e.details.deadline]})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center mt-4 pt-3 border-t",children:[(0,r.jsxs)("button",{onClick:()=>x(e),className:"flex items-center gap-1 text-blue-600 hover:text-blue-800 text-sm font-medium",children:[(0,r.jsx)(i,{size:16}),"View Details"]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)("button",{onClick:()=>f(e),className:"p-1.5 rounded-md bg-blue-50 text-blue-600 hover:bg-blue-100",title:"Edit form",children:(0,r.jsx)(n.A,{size:16})}),(0,r.jsx)("button",{onClick:()=>y(e.id),className:"p-1.5 rounded-md bg-red-50 text-red-600 hover:bg-red-100",title:"Delete form",children:(0,r.jsx)(d.A,{size:16})})]})]})]})},e.id)})}):(0,r.jsx)("p",{className:"text-gray-500 text-center py-10",children:"No forms available for review."})]})]})}),p&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-30 flex justify-center items-center z-50",children:(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-lg w-[90%] max-w-2xl overflow-y-auto max-h-[90vh]",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-blue-600",children:"Form Details"}),(0,r.jsx)("div",{children:v(p.status||(p.submitted?"approved":"pending"))})]}),(0,r.jsxs)("div",{className:"space-y-3 text-black",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Company:"})," ",p.company]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Key:"})," ",p.key]}),p.details&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Description:"})," ",p.details.description]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Skills:"})," ",p.details.skills]}),p.details.salaryMin&&(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Salary Range:"})," ₹",p.details.salaryMin," - ₹",p.details.salaryMax]}),p.details.deadline&&(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Deadline:"})," ",p.details.deadline]})]})]}),(0,r.jsxs)("div",{className:"flex justify-between mt-6 pt-4 border-t",children:[(0,r.jsx)("button",{onClick:()=>x(null),className:"px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300",children:"Close"}),p.submitted&&"posted"!==p.status&&(0,r.jsxs)("button",{onClick:()=>j(p),className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center gap-2",children:[(0,r.jsx)(o.A,{size:18})," Post Form"]})]})]})}),g&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-30 flex justify-center items-center z-50",children:(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-lg w-[90%] max-w-md",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-red-600 mb-4",children:"Confirm Delete"}),(0,r.jsx)("p",{className:"text-gray-700 mb-6",children:"Are you sure you want to delete this form? This action cannot be undone."}),(0,r.jsxs)("div",{className:"flex justify-end gap-3",children:[(0,r.jsx)("button",{onClick:()=>y(null),className:"px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300",children:"Cancel"}),(0,r.jsxs)("button",{onClick:()=>b(g),className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 flex items-center gap-2",children:[(0,r.jsx)(d.A,{size:18})," Delete"]})]})]})})]})}},19946:(e,t,s)=>{"use strict";s.d(t,{A:()=>h});var r=s(12115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),i=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return t.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim()},d=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,r.forwardRef)((e,t)=>{let{color:s="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:i,className:c="",children:h,iconNode:u,...m}=e;return(0,r.createElement)("svg",{ref:t,...o,width:a,height:a,stroke:s,strokeWidth:i?24*Number(l)/Number(a):l,className:n("lucide",c),...!h&&!d(m)&&{"aria-hidden":"true"},...m},[...u.map(e=>{let[t,s]=e;return(0,r.createElement)(t,s)}),...Array.isArray(h)?h:[h]])}),h=(e,t)=>{let s=(0,r.forwardRef)((s,l)=>{let{className:d,...o}=s;return(0,r.createElement)(c,{ref:l,iconNode:t,className:n("lucide-".concat(a(i(e))),"lucide-".concat(e),d),...o})});return s.displayName=i(e),s}},29869:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},30699:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var r=(0,s(86467).A)("outline","check","IconCheck",[["path",{d:"M5 12l5 5l10 -10",key:"svg-0"}]])},31503:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var r=(0,s(86467).A)("outline","edit","IconEdit",[["path",{d:"M7 7h-1a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-1",key:"svg-0"}],["path",{d:"M20.385 6.585a2.1 2.1 0 0 0 -2.97 -2.97l-8.415 8.385v3h3l8.385 -8.415z",key:"svg-1"}],["path",{d:"M16 5l3 3",key:"svg-2"}]])},33786:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},35695:(e,t,s)=>{"use strict";var r=s(18999);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"usePathname")&&s.d(t,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},37719:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(23464);s(73983);let a=r.A.create({baseURL:"http://127.0.0.1:8000",headers:{"Content-Type":"application/json"}});a.interceptors.request.use(e=>{let t=localStorage.getItem("access_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),a.interceptors.response.use(e=>e,async e=>{var t;let s=e.config;if((null==(t=e.response)?void 0:t.status)===401&&!s._retry){s._retry=!0;try{let e=localStorage.getItem("refresh_token");if(e){let t=await r.A.post("http://127.0.0.1:8000/api/auth/token/refresh/",{refresh:e});return localStorage.setItem("access_token",t.data.access),s.headers.Authorization="Bearer ".concat(t.data.access),a(s)}}catch(e){console.error("Error refreshing token:",e),localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),window.location.href="/login"}}return Promise.reject(e)});let l=a},40646:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},54416:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},59852:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var r=s(95155),a=s(6874),l=s.n(a),i=s(35695),n=s(83406),d=s(72733);function o(){let e=(0,i.usePathname)(),t="/admin/form"===e?"dashboard":"/admin/form/review"===e?"review":"";return(0,r.jsxs)("div",{className:"w-64 bg-white h-full fixed left-30 top-28 pt-16 z-10 border-r border-gray-200",children:[(0,r.jsx)("div",{className:"p-4 border-b border-gray-200",children:(0,r.jsx)("h2",{className:"text-xl font-semibold text-blue-600",children:"Forms Management"})}),(0,r.jsx)("nav",{className:"p-4",children:(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsxs)(l(),{href:"/admin/form",className:"flex items-center gap-3 p-3 rounded-lg transition-colors ".concat("dashboard"===t?"bg-blue-50 text-blue-600":"text-gray-700 hover:bg-gray-50"),children:[(0,r.jsx)(n.A,{size:20}),(0,r.jsx)("span",{className:"font-medium",children:"Forms Dashboard"})]})}),(0,r.jsx)("li",{children:(0,r.jsxs)(l(),{href:"/admin/form/review",className:"flex items-center gap-3 p-3 rounded-lg transition-colors ".concat("review"===t?"bg-blue-50 text-blue-600":"text-gray-700 hover:bg-gray-50"),children:[(0,r.jsx)(d.A,{size:20}),(0,r.jsx)("span",{className:"font-medium",children:"Review to Post"})]})})]})})]})}},71007:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},72733:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var r=(0,s(86467).A)("outline","clipboard-list","IconClipboardList",[["path",{d:"M9 5h-2a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-12a2 2 0 0 0 -2 -2h-2",key:"svg-0"}],["path",{d:"M9 3m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z",key:"svg-1"}],["path",{d:"M9 12l.01 0",key:"svg-2"}],["path",{d:"M13 12l2 0",key:"svg-3"}],["path",{d:"M9 16l.01 0",key:"svg-4"}],["path",{d:"M13 16l2 0",key:"svg-5"}]])},75525:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},76517:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]])},81284:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},83406:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var r=(0,s(86467).A)("outline","forms","IconForms",[["path",{d:"M12 3a3 3 0 0 0 -3 3v12a3 3 0 0 0 3 3",key:"svg-0"}],["path",{d:"M6 3a3 3 0 0 1 3 3v12a3 3 0 0 1 -3 3",key:"svg-1"}],["path",{d:"M13 7h7a1 1 0 0 1 1 1v8a1 1 0 0 1 -1 1h-7",key:"svg-2"}],["path",{d:"M5 7h-1a1 1 0 0 0 -1 1v8a1 1 0 0 0 1 1h1",key:"svg-3"}],["path",{d:"M17 12h.01",key:"svg-4"}],["path",{d:"M13 12h.01",key:"svg-5"}]])},85329:(e,t,s)=>{"use strict";s.d(t,{DG:()=>l,Jy:()=>i,Uq:()=>a,i6:()=>d,wi:()=>n});var r=s(37719);function a(){return r.A.get("/api/v1/jobs/forms/")}function l(e){return r.A.post("/api/v1/jobs/forms/",e)}function i(e){return r.A.get("/api/v1/jobs/forms/".concat(e,"/"))}function n(e,t){return r.A.patch("/api/v1/jobs/forms/".concat(e,"/"),t)}function d(e){return r.A.post("/api/v1/jobs/forms/".concat(e,"/delete/"))}},85339:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},86467:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(12115),a={outline:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"},filled:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",stroke:"none"}};let l=(e,t,s,l)=>{let i=(0,r.forwardRef)((s,i)=>{let{color:n="currentColor",size:d=24,stroke:o=2,title:c,className:h,children:u,...m}=s;return(0,r.createElement)("svg",{ref:i,...a[e],width:d,height:d,className:["tabler-icon","tabler-icon-".concat(t),h].join(" "),..."filled"===e?{fill:n}:{strokeWidth:o,stroke:n},...m},[c&&(0,r.createElement)("title",{key:"svg-title"},c),...l.map(e=>{let[t,s]=e;return(0,r.createElement)(t,s)}),...Array.isArray(u)?u:[u]])});return i.displayName="".concat(s),i}},93653:(e,t,s)=>{Promise.resolve().then(s.bind(s,19111))}},e=>{var t=t=>e(e.s=t);e.O(0,[3464,6874,3983,8441,1684,7358],()=>t(93653)),_N_E=e.O()}]);