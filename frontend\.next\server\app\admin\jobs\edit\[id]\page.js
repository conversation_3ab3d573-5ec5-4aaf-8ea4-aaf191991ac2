(()=>{var e={};e.id=8518,e.ids=[8518],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8819:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},9428:(e,t,a)=>{Promise.resolve().then(a.bind(a,18120))},10722:(e,t,a)=>{Promise.resolve().then(a.bind(a,11527))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11527:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>b});var s=a(60687),r=a(43210),n=a(24664),i=a(98848),l=a(9535),o=a(37325),d=a(81080),c=a(95994),p=a(80556),u=a(90910),m=a(81172),h=a(20798),g=a(58869),x=a(53411);function b({children:e}){let[t,a]=(0,r.useState)(""),b=[{items:[{title:"Dashboard",href:"/admin/dashboard",icon:(0,s.jsx)(i.A,{})},{title:"Jobs",href:"/admin/jobs",icon:(0,s.jsx)(l.A,{})},{title:"Applications",href:"/admin/applications",icon:(0,s.jsx)(o.A,{})},{title:"Posts",href:"/admin/posts",icon:(0,s.jsx)(h.A,{className:"w-5 h-5"})},{title:"Student Management",href:"/admin/student-management",icon:(0,s.jsx)(g.A,{className:"w-5 h-5"})},{title:"Student Analytics",href:"/admin/analytics",icon:(0,s.jsx)(x.A,{className:"w-5 h-5"})},{title:"Company Management",href:"/admin/companymanagement",icon:(0,s.jsx)(d.A,{})},{title:"Forms",href:"/admin/form",icon:(0,s.jsx)(c.A,{})}]}],y=[{title:"My Profile",href:"/admin/profile",icon:(0,s.jsx)(p.A,{})},{title:"Settings",href:"../settings",icon:(0,s.jsx)(u.A,{})},{title:"Contact Support",href:"/admin/helpandsupport",icon:(0,s.jsx)(m.A,{})}];return(0,s.jsx)("div",{className:"h-screen bg-gray-50 overflow-hidden",children:(0,s.jsxs)("div",{className:"flex h-full",children:[(0,s.jsx)(n.A,{sections:b,bottomItems:y,defaultExpanded:!1,navbarHeight:"0",className:"z-20"}),(0,s.jsx)("div",{className:"flex-1 p-6 ml-20 overflow-y-auto",children:(0,s.jsx)("div",{className:"bg-white rounded-xl shadow-md p-6 min-h-full text-black",children:e})})]})})}},12412:e=>{"use strict";e.exports=require("assert")},18120:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\jobs\\\\edit\\\\[id]\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\jobs\\edit\\[id]\\page.jsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20798:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("megaphone",[["path",{d:"m3 11 18-5v12L3 14v-3z",key:"n962bs"}],["path",{d:"M11.6 16.8a3 3 0 1 1-5.8-1.6",key:"1yl0tm"}]])},21820:e=>{"use strict";e.exports=require("os")},23697:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\layout.jsx","default")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37325:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var s=(0,a(6445).A)("outline","clipboard-list","IconClipboardList",[["path",{d:"M9 5h-2a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-12a2 2 0 0 0 -2 -2h-2",key:"svg-0"}],["path",{d:"M9 3m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z",key:"svg-1"}],["path",{d:"M9 12l.01 0",key:"svg-2"}],["path",{d:"M13 12l2 0",key:"svg-3"}],["path",{d:"M9 16l.01 0",key:"svg-4"}],["path",{d:"M13 16l2 0",key:"svg-5"}]])},45812:(e,t,a)=>{Promise.resolve().then(a.bind(a,91570))},48173:(e,t,a)=>{Promise.resolve().then(a.bind(a,23697))},53411:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58138:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var s=a(51060);a(51421);let r=s.A.create({baseURL:"http://127.0.0.1:8000",headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>{let t=localStorage.getItem("access_token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,async e=>{let t=e.config;if(e.response?.status===401&&!t._retry){t._retry=!0;try{let e=localStorage.getItem("refresh_token");if(e){let a=await s.A.post("http://127.0.0.1:8000/api/auth/token/refresh/",{refresh:e});return localStorage.setItem("access_token",a.data.access),t.headers.Authorization=`Bearer ${a.data.access}`,r(t)}}catch(e){console.error("Error refreshing token:",e)}}return Promise.reject(e)});let n=r},58922:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=a(65239),r=a(48088),n=a(88170),i=a.n(n),l=a(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);a.d(t,o);let d={children:["",{children:["admin",{children:["jobs",{children:["edit",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,18120)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\jobs\\edit\\[id]\\page.jsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,23697)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\layout.jsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,75535)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\layout.js"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\jobs\\edit\\[id]\\page.jsx"],p={require:a,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/jobs/edit/[id]/page",pathname:"/admin/jobs/edit/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});var s=a(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},75518:(e,t,a)=>{"use strict";a.d(t,{G$:()=>l,N6:()=>r,Om:()=>c,T4:()=>p,YQ:()=>i,_S:()=>o,lh:()=>d,vr:()=>n});var s=a(58138);function r(e={}){let t=new URLSearchParams;e.page&&t.append("page",e.page),e.per_page&&t.append("per_page",e.per_page),e.job_type&&"ALL"!==e.job_type&&t.append("job_type",e.job_type),e.location&&"ALL"!==e.location&&t.append("location",e.location),e.salary_min&&t.append("salary_min",e.salary_min),e.search&&t.append("search",e.search);let a=t.toString(),n=`/api/v1/college/default-college/jobs/${a?`?${a}`:""}`;return s.A.get(n)}function n(e,t,a={}){if(!Object.values(a).some(e=>e instanceof File))return s.A.post(`/api/v1/college/default-college/jobs/${e}/apply/`,{cover_letter:t,additional_field_responses:a});{let r=new FormData;return r.append("cover_letter",t),Object.entries(a).forEach(([e,t])=>{t instanceof File?r.append(e,t):r.append(e,JSON.stringify(t))}),s.A.post(`/api/v1/college/default-college/jobs/${e}/apply/`,r,{headers:{"Content-Type":"multipart/form-data"}})}}function i(e){return s.A.get(`/api/v1/college/default-college/jobs/${e}/`)}function l(){return s.A.get("/api/v1/college/default-college/jobs/applied/")}function o(e){return s.A.post("/api/v1/college/default-college/jobs/create/",e)}function d(e,t){return s.A.put(`/api/v1/college/default-college/jobs/${e}/`,t)}function c(e={}){let t=new URLSearchParams;e.page&&t.append("page",e.page),e.per_page&&t.append("per_page",e.per_page),e.search&&t.append("search",e.search),e.type&&"All"!==e.type&&t.append("job_type",e.type),e.minCTC&&t.append("salary_min",e.minCTC),e.maxCTC&&t.append("salary_max",e.maxCTC),e.minStipend&&t.append("stipend_min",e.minStipend),e.maxStipend&&t.append("stipend_max",e.maxStipend),e.location&&t.append("location",e.location),void 0!==e.is_published&&t.append("is_published",e.is_published),e.company_id&&t.append("company_id",e.company_id),e.company_name&&t.append("company_name",e.company_name);let a=t.toString(),r=`/api/v1/college/default-college/jobs/admin/${a?`?${a}`:""}`;return console.log("\uD83C\uDF10 listJobsAdmin calling URL:",r,"with params:",e),s.A.get(r).then(e=>(console.log("\uD83C\uDF10 listJobsAdmin response:",{status:e.status,totalJobs:e.data?.pagination?.total_count||0,currentPage:e.data?.pagination?.current_page||1,totalPages:e.data?.pagination?.total_pages||1}),e)).catch(e=>{throw console.error("\uD83C\uDF10 listJobsAdmin error:",e),console.error("\uD83C\uDF10 listJobsAdmin error response:",e.response?.data),e})}function p(e){return s.A.patch(`/api/v1/jobs/${e}/toggle-publish/`)}},79551:e=>{"use strict";e.exports=require("url")},81172:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var s=(0,a(6445).A)("outline","help","IconHelp",[["path",{d:"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0",key:"svg-0"}],["path",{d:"M12 17l0 .01",key:"svg-1"}],["path",{d:"M12 13.5a1.5 1.5 0 0 1 1 -1.5a2.6 2.6 0 1 0 -3 -4",key:"svg-2"}]])},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},88233:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},91570:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>u});var s=a(60687),r=a(43210),n=a(16189),i=a(28559),l=a(11860),o=a(88233),d=a(96474),c=a(8819),p=a(75518);function u({params:e}){let t=parseInt((0,r.use)(e).id),a=(0,n.useRouter)(),[u,m]=(0,r.useState)(null),[h,g]=(0,r.useState)({title:"",description:"",location:"",job_type:"FULL_TIME",salary_max:"",salary_min:"",application_deadline:"",duration:"",is_published:!1,requirements:[],benefits:[],skills:[],company_id:null,company_name:""}),[x,b]=(0,r.useState)([{id:1,name:"",date:"",time:""}]),[y,f]=(0,r.useState)([]),[v,j]=(0,r.useState)(!0),[N,_]=(0,r.useState)(!1),[A,k]=(0,r.useState)(null),C=(e,t)=>{g(a=>({...a,[e]:t}))},w=(e,t,a)=>{b(s=>s.map(s=>s.id===e?{...s,[t]:a}:s))},M=e=>{b(t=>t.filter(t=>t.id!==e))},S=e=>{let t={id:Date.now(),type:e,label:"",required:!1,options:"multiple_choice"===e?[""]:void 0};f(e=>[...e,t])},P=(e,t,a)=>{f(s=>s.map(s=>s.id===e?{...s,[t]:a}:s))},E=e=>{f(t=>t.filter(t=>t.id!==e))},q=e=>{f(t=>t.map(t=>t.id===e?{...t,options:[...t.options||[],""]}:t))},D=(e,t,a)=>{f(s=>s.map(s=>s.id===e?{...s,options:s.options.map((e,s)=>s===t?a:e)}:s))},T=(e,t)=>{f(a=>a.map(a=>a.id===e?{...a,options:a.options.filter((e,a)=>a!==t)}:a))},F=async()=>{_(!0);try{console.log("Saving job data:",h);let e={title:h.title,description:h.description,location:h.location,job_type:h.job_type,salary_max:h.salary_max,salary_min:h.salary_min,application_deadline:h.application_deadline,duration:h.duration,is_published:h.is_published,requirements:h.requirements,benefits:h.benefits,skills:h.skills,interview_rounds:x.filter(e=>e.name.trim()),additional_fields:y},a=await (0,p.lh)(t,e);console.log("Update response:",a),a.data&&(m(a.data),alert("Job updated successfully!"))}catch(e){console.error("Error saving job:",e),k("Failed to save job. Please try again."),alert("Failed to save job. Please try again.")}finally{_(!1)}};return v?(0,s.jsx)("div",{className:"h-full overflow-y-auto",children:(0,s.jsx)("div",{className:"p-6",children:(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsx)("div",{className:"text-lg",children:"Loading job..."})})})}):A&&!u?(0,s.jsx)("div",{className:"h-full overflow-y-auto",children:(0,s.jsx)("div",{className:"p-6",children:(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsxs)("div",{className:"text-red-600",children:["Error: ",A]})})})}):(0,s.jsx)("div",{className:"h-full overflow-y-auto",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)("button",{onClick:()=>a.back(),className:"flex items-center text-gray-600 hover:text-gray-900 mb-4",children:[(0,s.jsx)(i.A,{className:"w-5 h-5 mr-2"}),"Back to Company Management"]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Edit Job Application"}),(0,s.jsxs)("p",{className:"text-gray-600 mt-1",children:["Job ID: ",t," | Company: ",h.company_name]})]}),(0,s.jsx)("button",{onClick:()=>a.back(),className:"text-gray-500 hover:text-gray-700",children:(0,s.jsx)(l.A,{className:"w-6 h-6"})})]})]}),A&&(0,s.jsx)("div",{className:"mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg",children:A}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:(0,s.jsxs)("div",{className:"p-6 space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Job Title *"}),(0,s.jsx)("input",{type:"text",value:h.title,onChange:e=>C("title",e.target.value),placeholder:"Enter job title",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Location *"}),(0,s.jsx)("input",{type:"text",value:h.location,onChange:e=>C("location",e.target.value),placeholder:"Enter location",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Job Type *"}),(0,s.jsxs)("select",{value:h.job_type,onChange:e=>C("job_type",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"FULL_TIME",children:"Full Time"}),(0,s.jsx)("option",{value:"PART_TIME",children:"Part Time"}),(0,s.jsx)("option",{value:"INTERNSHIP",children:"Internship"}),(0,s.jsx)("option",{value:"CONTRACT",children:"Contract"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Min Salary"}),(0,s.jsx)("input",{type:"text",value:h.salary_min,onChange:e=>C("salary_min",e.target.value),placeholder:"Enter minimum salary",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Max Salary"}),(0,s.jsx)("input",{type:"text",value:h.salary_max,onChange:e=>C("salary_max",e.target.value),placeholder:"Enter maximum salary",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Status"}),(0,s.jsxs)("select",{value:h.is_published?"published":"draft",onChange:e=>C("is_published","published"===e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"draft",children:"Draft"}),(0,s.jsx)("option",{value:"published",children:"Published"})]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Duration"}),(0,s.jsx)("input",{type:"text",value:h.duration,onChange:e=>C("duration",e.target.value),placeholder:"e.g., 6 months, 2 years",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Application Deadline"}),(0,s.jsx)("input",{type:"date",value:h.application_deadline,onChange:e=>C("application_deadline",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Job Description *"}),(0,s.jsx)("textarea",{value:h.description,onChange:e=>C("description",e.target.value),placeholder:"Enter job description",rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Interview Timeline"}),x.map((e,t)=>(0,s.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4 mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,s.jsxs)("h4",{className:"font-medium text-gray-700",children:["Round ",t+1]}),x.length>1&&(0,s.jsx)("button",{type:"button",onClick:()=>M(e.id),className:"text-red-500 hover:text-red-700",children:(0,s.jsx)(o.A,{className:"w-4 h-4"})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3",children:[(0,s.jsx)("input",{type:"text",value:e.name,onChange:t=>w(e.id,"name",t.target.value),placeholder:"Enter round name",className:"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,s.jsx)("input",{type:"date",value:e.date,onChange:t=>w(e.id,"date",t.target.value),placeholder:"Select date",className:"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,s.jsx)("input",{type:"time",value:e.time,onChange:t=>w(e.id,"time",t.target.value),placeholder:"Select time",className:"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]},e.id)),(0,s.jsxs)("button",{type:"button",onClick:()=>{let e=Math.max(...x.map(e=>e.id),0)+1;b(t=>[...t,{id:e,name:"",date:"",time:""}])},className:"flex items-center gap-2 px-4 py-2 text-blue-600 border border-blue-600 rounded-lg hover:bg-blue-50 transition-colors",children:[(0,s.jsx)(d.A,{className:"w-4 h-4"}),"Add Round"]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Additional Fields"}),y.map(e=>(0,s.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4 mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,s.jsx)("input",{type:"text",value:e.label,onChange:t=>P(e.id,"label",t.target.value),placeholder:"Field label",className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 mr-3"}),(0,s.jsx)("button",{type:"button",onClick:()=>E(e.id),className:"text-red-500 hover:text-red-700",children:(0,s.jsx)(o.A,{className:"w-4 h-4"})})]}),(0,s.jsxs)("div",{className:"text-xs text-gray-500 mb-2",children:["Field Type: ",e.type.replace("_"," ").toUpperCase()]}),"multiple_choice"===e.type&&(0,s.jsxs)("div",{className:"space-y-2",children:[e.options?.map((t,a)=>(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)("input",{type:"text",value:t,onChange:t=>D(e.id,a,t.target.value),placeholder:`Option ${a+1}`,className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),e.options.length>1&&(0,s.jsx)("button",{type:"button",onClick:()=>T(e.id,a),className:"text-red-500 hover:text-red-700",children:(0,s.jsx)(o.A,{className:"w-4 h-4"})})]},a)),(0,s.jsx)("button",{type:"button",onClick:()=>q(e.id),className:"text-blue-600 text-sm hover:text-blue-800",children:"+ Add Option"})]})]},e.id)),(0,s.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,s.jsxs)("button",{type:"button",onClick:()=>S("text"),className:"flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:[(0,s.jsx)(d.A,{className:"w-4 h-4"}),"Add Text Field"]}),(0,s.jsxs)("button",{type:"button",onClick:()=>S("number"),className:"flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:[(0,s.jsx)(d.A,{className:"w-4 h-4"}),"Add Number Field"]}),(0,s.jsxs)("button",{type:"button",onClick:()=>S("file"),className:"flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:[(0,s.jsx)(d.A,{className:"w-4 h-4"}),"Add File Upload Field"]}),(0,s.jsxs)("button",{type:"button",onClick:()=>S("multiple_choice"),className:"flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:[(0,s.jsx)(d.A,{className:"w-4 h-4"}),"Add Multiple Choice Field"]})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-end gap-4 pt-6 border-t border-gray-200",children:[(0,s.jsx)("button",{type:"button",onClick:()=>a.back(),disabled:N,className:"px-6 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50",children:"Cancel"}),(0,s.jsxs)("button",{onClick:F,disabled:N,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center gap-2",children:[(0,s.jsx)(c.A,{className:"w-4 h-4"}),N?"Saving Changes...":"Save Changes"]})]})]})})]})})}},94735:e=>{"use strict";e.exports=require("events")},95994:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var s=(0,a(6445).A)("outline","forms","IconForms",[["path",{d:"M12 3a3 3 0 0 0 -3 3v12a3 3 0 0 0 3 3",key:"svg-0"}],["path",{d:"M6 3a3 3 0 0 1 3 3v12a3 3 0 0 1 -3 3",key:"svg-1"}],["path",{d:"M13 7h7a1 1 0 0 1 1 1v8a1 1 0 0 1 -1 1h-7",key:"svg-2"}],["path",{d:"M5 7h-1a1 1 0 0 0 -1 1v8a1 1 0 0 0 1 1h1",key:"svg-3"}],["path",{d:"M17 12h.01",key:"svg-4"}],["path",{d:"M13 12h.01",key:"svg-5"}]])},96474:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var t=require("../../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[4447,681,1658,1060,2305],()=>a(58922));module.exports=s})();