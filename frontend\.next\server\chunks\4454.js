"use strict";exports.id=4454,exports.ids=[4454],exports.modules={78201:(t,e,n)=>{n.d(e,{m:()=>c});var r=n(87981),a=n(64722),s=n(78872),o=n(31504),i=n(29789),u=n(23711);function l(t,e){let n=(0,u.a)(t)-(0,u.a)(e);return n<0?-1:n>0?1:n}var f=n(58505);function c(t,e){return function(t,e,n){let r,c=(0,s.q)(),h=n?.locale??c.locale??a.c,d=l(t,e);if(isNaN(d))throw RangeError("Invalid time value");let M=Object.assign({},n,{addSuffix:n?.addSuffix,comparison:d}),[m,D]=(0,i.x)(n?.in,...d>0?[e,t]:[t,e]),x=function(t,e,n){var r;return(r=void 0,t=>{let e=(r?Math[r]:Math.trunc)(t);return 0===e?0:e})(((0,u.a)(t)-(0,u.a)(e))/1e3)}(D,m),v=Math.round((x-((0,o.G)(D)-(0,o.G)(m))/1e3)/60);if(v<2)if(n?.includeSeconds)if(x<5)return h.formatDistance("lessThanXSeconds",5,M);else if(x<10)return h.formatDistance("lessThanXSeconds",10,M);else if(x<20)return h.formatDistance("lessThanXSeconds",20,M);else if(x<40)return h.formatDistance("halfAMinute",0,M);else if(x<60)return h.formatDistance("lessThanXMinutes",1,M);else return h.formatDistance("xMinutes",1,M);else if(0===v)return h.formatDistance("lessThanXMinutes",1,M);else return h.formatDistance("xMinutes",v,M);if(v<45)return h.formatDistance("xMinutes",v,M);if(v<90)return h.formatDistance("aboutXHours",1,M);if(v<f.F6){let t=Math.round(v/60);return h.formatDistance("aboutXHours",t,M)}if(v<2520)return h.formatDistance("xDays",1,M);else if(v<f.Nw){let t=Math.round(v/f.F6);return h.formatDistance("xDays",t,M)}else if(v<2*f.Nw)return r=Math.round(v/f.Nw),h.formatDistance("aboutXMonths",r,M);if((r=function(t,e,n){let[r,a,s]=(0,i.x)(void 0,t,t,e),o=l(a,s),f=Math.abs(function(t,e,n){let[r,a]=(0,i.x)(void 0,t,e);return 12*(r.getFullYear()-a.getFullYear())+(r.getMonth()-a.getMonth())}(a,s));if(f<1)return 0;1===a.getMonth()&&a.getDate()>27&&a.setDate(30),a.setMonth(a.getMonth()-o*f);let c=l(a,s)===-o;(function(t,e){let n=(0,u.a)(t,void 0);return+function(t,e){let n=(0,u.a)(t,e?.in);return n.setHours(23,59,59,999),n}(n,void 0)==+function(t,e){let n=(0,u.a)(t,e?.in),r=n.getMonth();return n.setFullYear(n.getFullYear(),r+1,0),n.setHours(23,59,59,999),n}(n,e)})(r)&&1===f&&1===l(r,s)&&(c=!1);let h=o*(f-c);return 0===h?0:h}(D,m))<12){let t=Math.round(v/f.Nw);return h.formatDistance("xMonths",t,M)}{let t=r%12,e=Math.trunc(r/12);return t<3?h.formatDistance("aboutXYears",e,M):t<9?h.formatDistance("overXYears",e,M):h.formatDistance("almostXYears",e+1,M)}}(t,(0,r.w)(t,Date.now()),e)}},96474:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};