(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8518],{284:(e,t,a)=>{Promise.resolve().then(a.bind(a,83826))},1243:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});let l=(0,a(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},4229:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});let l=(0,a(19946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},19946:(e,t,a)=>{"use strict";a.d(t,{A:()=>u});var l=a(12115);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,a)=>a?a.toUpperCase():t.toLowerCase()),n=e=>{let t=s(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return t.filter((e,t,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===t).join(" ").trim()},o=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,l.forwardRef)((e,t)=>{let{color:a="currentColor",size:r=24,strokeWidth:s=2,absoluteStrokeWidth:n,className:c="",children:u,iconNode:p,...m}=e;return(0,l.createElement)("svg",{ref:t,...d,width:r,height:r,stroke:a,strokeWidth:n?24*Number(s)/Number(r):s,className:i("lucide",c),...!u&&!o(m)&&{"aria-hidden":"true"},...m},[...p.map(e=>{let[t,a]=e;return(0,l.createElement)(t,a)}),...Array.isArray(u)?u:[u]])}),u=(e,t)=>{let a=(0,l.forwardRef)((a,s)=>{let{className:o,...d}=a;return(0,l.createElement)(c,{ref:s,iconNode:t,className:i("lucide-".concat(r(n(e))),"lucide-".concat(e),o),...d})});return a.displayName=n(e),a}},29869:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});let l=(0,a(19946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},33786:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});let l=(0,a(19946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},34842:(e,t,a)=>{"use strict";a.d(t,{G$:()=>i,N6:()=>r,Om:()=>c,T4:()=>u,YQ:()=>n,_S:()=>o,lh:()=>d,vr:()=>s});var l=a(37719);function r(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.page&&t.append("page",e.page),e.per_page&&t.append("per_page",e.per_page),e.job_type&&"ALL"!==e.job_type&&t.append("job_type",e.job_type),e.location&&"ALL"!==e.location&&t.append("location",e.location),e.salary_min&&t.append("salary_min",e.salary_min),e.search&&t.append("search",e.search);let a=t.toString();return l.A.get("/api/v1/college/default-college/jobs/".concat(a?"?".concat(a):""))}function s(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!Object.values(a).some(e=>e instanceof File))return l.A.post("/api/v1/college/default-college/jobs/".concat(e,"/apply/"),{cover_letter:t,additional_field_responses:a});{let r=new FormData;return r.append("cover_letter",t),Object.entries(a).forEach(e=>{let[t,a]=e;a instanceof File?r.append(t,a):r.append(t,JSON.stringify(a))}),l.A.post("/api/v1/college/default-college/jobs/".concat(e,"/apply/"),r,{headers:{"Content-Type":"multipart/form-data"}})}}function n(e){return l.A.get("/api/v1/college/default-college/jobs/".concat(e,"/"))}function i(){return l.A.get("/api/v1/college/default-college/jobs/applied/")}function o(e){return l.A.post("/api/v1/college/default-college/jobs/create/",e)}function d(e,t){return l.A.put("/api/v1/college/default-college/jobs/".concat(e,"/"),t)}function c(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.page&&t.append("page",e.page),e.per_page&&t.append("per_page",e.per_page),e.search&&t.append("search",e.search),e.type&&"All"!==e.type&&t.append("job_type",e.type),e.minCTC&&t.append("salary_min",e.minCTC),e.maxCTC&&t.append("salary_max",e.maxCTC),e.minStipend&&t.append("stipend_min",e.minStipend),e.maxStipend&&t.append("stipend_max",e.maxStipend),e.location&&t.append("location",e.location),void 0!==e.is_published&&t.append("is_published",e.is_published),e.company_id&&t.append("company_id",e.company_id),e.company_name&&t.append("company_name",e.company_name);let a=t.toString(),r="/api/v1/college/default-college/jobs/admin/".concat(a?"?".concat(a):"");return console.log("\uD83C\uDF10 listJobsAdmin calling URL:",r,"with params:",e),l.A.get(r).then(e=>{var t,a,l,r,s,n;return console.log("\uD83C\uDF10 listJobsAdmin response:",{status:e.status,totalJobs:(null==(a=e.data)||null==(t=a.pagination)?void 0:t.total_count)||0,currentPage:(null==(r=e.data)||null==(l=r.pagination)?void 0:l.current_page)||1,totalPages:(null==(n=e.data)||null==(s=n.pagination)?void 0:s.total_pages)||1}),e}).catch(e=>{var t;throw console.error("\uD83C\uDF10 listJobsAdmin error:",e),console.error("\uD83C\uDF10 listJobsAdmin error response:",null==(t=e.response)?void 0:t.data),e})}function u(e){return l.A.patch("/api/v1/jobs/".concat(e,"/toggle-publish/"))}},35169:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});let l=(0,a(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},35695:(e,t,a)=>{"use strict";var l=a(18999);a.o(l,"useParams")&&a.d(t,{useParams:function(){return l.useParams}}),a.o(l,"usePathname")&&a.d(t,{usePathname:function(){return l.usePathname}}),a.o(l,"useRouter")&&a.d(t,{useRouter:function(){return l.useRouter}}),a.o(l,"useSearchParams")&&a.d(t,{useSearchParams:function(){return l.useSearchParams}})},37719:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var l=a(23464);a(73983);let r=l.A.create({baseURL:"http://127.0.0.1:8000",headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>{let t=localStorage.getItem("access_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,async e=>{var t;let a=e.config;if((null==(t=e.response)?void 0:t.status)===401&&!a._retry){a._retry=!0;try{let e=localStorage.getItem("refresh_token");if(e){let t=await l.A.post("http://127.0.0.1:8000/api/auth/token/refresh/",{refresh:e});return localStorage.setItem("access_token",t.data.access),a.headers.Authorization="Bearer ".concat(t.data.access),r(a)}}catch(e){console.error("Error refreshing token:",e),localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),window.location.href="/login"}}return Promise.reject(e)});let s=r},40646:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});let l=(0,a(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},54416:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});let l=(0,a(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},62525:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});let l=(0,a(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},71007:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});let l=(0,a(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},75525:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});let l=(0,a(19946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},76517:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});let l=(0,a(19946).A)("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]])},81284:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});let l=(0,a(19946).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},83826:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>p});var l=a(95155),r=a(12115),s=a(35695),n=a(35169),i=a(54416),o=a(62525),d=a(84616),c=a(4229),u=a(34842);function p(e){let{params:t}=e,a=parseInt((0,r.use)(t).id),p=(0,s.useRouter)(),[m,h]=(0,r.useState)(null),[g,y]=(0,r.useState)({title:"",description:"",location:"",job_type:"FULL_TIME",salary_max:"",salary_min:"",application_deadline:"",duration:"",is_published:!1,requirements:[],benefits:[],skills:[],company_id:null,company_name:""}),[x,b]=(0,r.useState)([{id:1,name:"",date:"",time:""}]),[f,v]=(0,r.useState)([]),[j,_]=(0,r.useState)(!0),[N,k]=(0,r.useState)(!1),[A,w]=(0,r.useState)(null);(0,r.useEffect)(()=>{let e=async()=>{try{console.log("Loading job data for ID:",a);let l=await (0,u.YQ)(a);if(console.log("Job API response:",l),l.data){var e,t;let a=l.data;console.log("Setting job data:",a),h(a);let r={id:a.id,title:a.title||"",description:a.description||"",location:a.location||"",job_type:a.job_type||"FULL_TIME",salary_max:a.salary_max||"",salary_min:a.salary_min||"",application_deadline:a.application_deadline||"",duration:a.duration||"",is_published:a.is_published||!1,requirements:a.requirements||[],benefits:a.benefits||[],skills:a.skills||[],company_id:a.company_id||(null==(e=a.company)?void 0:e.id)||null,company_name:a.company_name||(null==(t=a.company)?void 0:t.name)||""};y(r),a.interview_rounds&&a.interview_rounds.length>0&&b(a.interview_rounds.map((e,t)=>({id:t+1,name:e.name||"",date:e.date||"",time:e.time||""}))),a.additional_fields&&a.additional_fields.length>0&&v(a.additional_fields)}}catch(e){console.error("Error loading job:",e),w("Failed to load job data. Please try again.")}finally{_(!1)}};a&&e()},[a]);let C=(e,t)=>{y(a=>({...a,[e]:t}))},M=(e,t,a)=>{b(l=>l.map(l=>l.id===e?{...l,[t]:a}:l))},S=e=>{b(t=>t.filter(t=>t.id!==e))},E=e=>{let t={id:Date.now(),type:e,label:"",required:!1,options:"multiple_choice"===e?[""]:void 0};v(e=>[...e,t])},P=(e,t,a)=>{v(l=>l.map(l=>l.id===e?{...l,[t]:a}:l))},L=e=>{v(t=>t.filter(t=>t.id!==e))},T=e=>{v(t=>t.map(t=>t.id===e?{...t,options:[...t.options||[],""]}:t))},F=(e,t,a)=>{v(l=>l.map(l=>l.id===e?{...l,options:l.options.map((e,l)=>l===t?a:e)}:l))},I=(e,t)=>{v(a=>a.map(a=>a.id===e?{...a,options:a.options.filter((e,a)=>a!==t)}:a))},R=async()=>{k(!0);try{console.log("Saving job data:",g);let e={title:g.title,description:g.description,location:g.location,job_type:g.job_type,salary_max:g.salary_max,salary_min:g.salary_min,application_deadline:g.application_deadline,duration:g.duration,is_published:g.is_published,requirements:g.requirements,benefits:g.benefits,skills:g.skills,interview_rounds:x.filter(e=>e.name.trim()),additional_fields:f},t=await (0,u.lh)(a,e);console.log("Update response:",t),t.data&&(h(t.data),alert("Job updated successfully!"))}catch(e){console.error("Error saving job:",e),w("Failed to save job. Please try again."),alert("Failed to save job. Please try again.")}finally{k(!1)}};return j?(0,l.jsx)("div",{className:"h-full overflow-y-auto",children:(0,l.jsx)("div",{className:"p-6",children:(0,l.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,l.jsx)("div",{className:"text-lg",children:"Loading job..."})})})}):A&&!m?(0,l.jsx)("div",{className:"h-full overflow-y-auto",children:(0,l.jsx)("div",{className:"p-6",children:(0,l.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,l.jsxs)("div",{className:"text-red-600",children:["Error: ",A]})})})}):(0,l.jsx)("div",{className:"h-full overflow-y-auto",children:(0,l.jsxs)("div",{className:"p-6",children:[(0,l.jsxs)("div",{className:"mb-6",children:[(0,l.jsxs)("button",{onClick:()=>p.back(),className:"flex items-center text-gray-600 hover:text-gray-900 mb-4",children:[(0,l.jsx)(n.A,{className:"w-5 h-5 mr-2"}),"Back to Company Management"]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Edit Job Application"}),(0,l.jsxs)("p",{className:"text-gray-600 mt-1",children:["Job ID: ",a," | Company: ",g.company_name]})]}),(0,l.jsx)("button",{onClick:()=>p.back(),className:"text-gray-500 hover:text-gray-700",children:(0,l.jsx)(i.A,{className:"w-6 h-6"})})]})]}),A&&(0,l.jsx)("div",{className:"mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg",children:A}),(0,l.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:(0,l.jsxs)("div",{className:"p-6 space-y-6",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Job Title *"}),(0,l.jsx)("input",{type:"text",value:g.title,onChange:e=>C("title",e.target.value),placeholder:"Enter job title",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Location *"}),(0,l.jsx)("input",{type:"text",value:g.location,onChange:e=>C("location",e.target.value),placeholder:"Enter location",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Job Type *"}),(0,l.jsxs)("select",{value:g.job_type,onChange:e=>C("job_type",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,l.jsx)("option",{value:"FULL_TIME",children:"Full Time"}),(0,l.jsx)("option",{value:"PART_TIME",children:"Part Time"}),(0,l.jsx)("option",{value:"INTERNSHIP",children:"Internship"}),(0,l.jsx)("option",{value:"CONTRACT",children:"Contract"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Min Salary"}),(0,l.jsx)("input",{type:"text",value:g.salary_min,onChange:e=>C("salary_min",e.target.value),placeholder:"Enter minimum salary",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Max Salary"}),(0,l.jsx)("input",{type:"text",value:g.salary_max,onChange:e=>C("salary_max",e.target.value),placeholder:"Enter maximum salary",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Status"}),(0,l.jsxs)("select",{value:g.is_published?"published":"draft",onChange:e=>C("is_published","published"===e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,l.jsx)("option",{value:"draft",children:"Draft"}),(0,l.jsx)("option",{value:"published",children:"Published"})]})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Duration"}),(0,l.jsx)("input",{type:"text",value:g.duration,onChange:e=>C("duration",e.target.value),placeholder:"e.g., 6 months, 2 years",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Application Deadline"}),(0,l.jsx)("input",{type:"date",value:g.application_deadline,onChange:e=>C("application_deadline",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Job Description *"}),(0,l.jsx)("textarea",{value:g.description,onChange:e=>C("description",e.target.value),placeholder:"Enter job description",rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Interview Timeline"}),x.map((e,t)=>(0,l.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4 mb-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,l.jsxs)("h4",{className:"font-medium text-gray-700",children:["Round ",t+1]}),x.length>1&&(0,l.jsx)("button",{type:"button",onClick:()=>S(e.id),className:"text-red-500 hover:text-red-700",children:(0,l.jsx)(o.A,{className:"w-4 h-4"})})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3",children:[(0,l.jsx)("input",{type:"text",value:e.name,onChange:t=>M(e.id,"name",t.target.value),placeholder:"Enter round name",className:"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,l.jsx)("input",{type:"date",value:e.date,onChange:t=>M(e.id,"date",t.target.value),placeholder:"Select date",className:"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,l.jsx)("input",{type:"time",value:e.time,onChange:t=>M(e.id,"time",t.target.value),placeholder:"Select time",className:"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]},e.id)),(0,l.jsxs)("button",{type:"button",onClick:()=>{let e=Math.max(...x.map(e=>e.id),0)+1;b(t=>[...t,{id:e,name:"",date:"",time:""}])},className:"flex items-center gap-2 px-4 py-2 text-blue-600 border border-blue-600 rounded-lg hover:bg-blue-50 transition-colors",children:[(0,l.jsx)(d.A,{className:"w-4 h-4"}),"Add Round"]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Additional Fields"}),f.map(e=>{var t;return(0,l.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4 mb-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,l.jsx)("input",{type:"text",value:e.label,onChange:t=>P(e.id,"label",t.target.value),placeholder:"Field label",className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 mr-3"}),(0,l.jsx)("button",{type:"button",onClick:()=>L(e.id),className:"text-red-500 hover:text-red-700",children:(0,l.jsx)(o.A,{className:"w-4 h-4"})})]}),(0,l.jsxs)("div",{className:"text-xs text-gray-500 mb-2",children:["Field Type: ",e.type.replace("_"," ").toUpperCase()]}),"multiple_choice"===e.type&&(0,l.jsxs)("div",{className:"space-y-2",children:[null==(t=e.options)?void 0:t.map((t,a)=>(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsx)("input",{type:"text",value:t,onChange:t=>F(e.id,a,t.target.value),placeholder:"Option ".concat(a+1),className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),e.options.length>1&&(0,l.jsx)("button",{type:"button",onClick:()=>I(e.id,a),className:"text-red-500 hover:text-red-700",children:(0,l.jsx)(o.A,{className:"w-4 h-4"})})]},a)),(0,l.jsx)("button",{type:"button",onClick:()=>T(e.id),className:"text-blue-600 text-sm hover:text-blue-800",children:"+ Add Option"})]})]},e.id)}),(0,l.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,l.jsxs)("button",{type:"button",onClick:()=>E("text"),className:"flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:[(0,l.jsx)(d.A,{className:"w-4 h-4"}),"Add Text Field"]}),(0,l.jsxs)("button",{type:"button",onClick:()=>E("number"),className:"flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:[(0,l.jsx)(d.A,{className:"w-4 h-4"}),"Add Number Field"]}),(0,l.jsxs)("button",{type:"button",onClick:()=>E("file"),className:"flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:[(0,l.jsx)(d.A,{className:"w-4 h-4"}),"Add File Upload Field"]}),(0,l.jsxs)("button",{type:"button",onClick:()=>E("multiple_choice"),className:"flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:[(0,l.jsx)(d.A,{className:"w-4 h-4"}),"Add Multiple Choice Field"]})]})]}),(0,l.jsxs)("div",{className:"flex items-center justify-end gap-4 pt-6 border-t border-gray-200",children:[(0,l.jsx)("button",{type:"button",onClick:()=>p.back(),disabled:N,className:"px-6 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50",children:"Cancel"}),(0,l.jsxs)("button",{onClick:R,disabled:N,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center gap-2",children:[(0,l.jsx)(c.A,{className:"w-4 h-4"}),N?"Saving Changes...":"Save Changes"]})]})]})})]})})}},84616:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});let l=(0,a(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},85339:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});let l=(0,a(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[3464,3983,8441,1684,7358],()=>t(284)),_N_E=e.O()}]);