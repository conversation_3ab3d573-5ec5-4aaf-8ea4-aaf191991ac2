(()=>{var e={};e.id=733,e.ids=[733],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8819:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11043:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\[id]\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\companymanagement\\[id]\\page.jsx","default")},11437:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},12412:e=>{"use strict";e.exports=require("assert")},13861:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},16886:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var a=s(65239),r=s(48088),i=s(88170),l=s.n(i),n=s(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);s.d(t,d);let o={children:["",{children:["admin",{children:["companymanagement",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,11043)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\companymanagement\\[id]\\page.jsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,23697)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\layout.jsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,75535)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\layout.js"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\companymanagement\\[id]\\page.jsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/companymanagement/[id]/page",pathname:"/admin/companymanagement/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},17313:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27900:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35486:(e,t,s)=>{Promise.resolve().then(s.bind(s,94945))},40228:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41312:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},43125:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("loader",[["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m16.2 7.8 2.9-2.9",key:"r700ao"}],["path",{d:"M18 12h4",key:"wj9ykh"}],["path",{d:"m16.2 16.2 2.9 2.9",key:"1bxg5t"}],["path",{d:"M12 18v4",key:"jadmvz"}],["path",{d:"m4.9 19.1 2.9-2.9",key:"bwix9q"}],["path",{d:"M2 12h4",key:"j09sii"}],["path",{d:"m4.9 4.9 2.9 2.9",key:"giyufr"}]])},48730:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},50867:(e,t,s)=>{"use strict";s.d(t,{DG:()=>i,Jy:()=>l,Uq:()=>r,i6:()=>d,wi:()=>n});var a=s(58138);function r(){return a.A.get("/api/v1/jobs/forms/")}function i(e){return a.A.post("/api/v1/jobs/forms/",e)}function l(e){return a.A.get(`/api/v1/jobs/forms/${e}/`)}function n(e,t){return a.A.patch(`/api/v1/jobs/forms/${e}/`,t)}function d(e){return a.A.post(`/api/v1/jobs/forms/${e}/delete/`)}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57800:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},64398:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},65222:(e,t,s)=>{"use strict";s.d(t,{G_:()=>r});var a=s(60687);function r({description:e,className:t=""}){let s=e?e.replace(/\n/g,"<br />").replace(/•/g,"•").replace(/\*\s/g,"• ").replace(/-\s/g,"• ").trim():"No description provided.";return(0,a.jsx)("div",{className:`text-gray-700 leading-relaxed ${t}`,dangerouslySetInnerHTML:{__html:s}})}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},88630:(e,t,s)=>{Promise.resolve().then(s.bind(s,11043))},94735:e=>{"use strict";e.exports=require("events")},94945:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>M});var a=s(60687),r=s(43210),i=s(16189),l=s(43125),n=s(93613),d=s(28559),o=s(17313),c=s(8819),m=s(11860),x=s(63143),p=s(97992),h=s(41312),u=s(40228),g=s(11437),y=s(25334),b=s(64398),j=s(57800),f=s(5336),v=s(48730),N=s(27900),w=s(13861);s(1469);var k=s(14335),A=s(50867),_=s(75518),C=s(21513);function M({params:e}){let t=parseInt((0,r.use)(e).id),s=(0,i.useRouter)(),[N,w]=(0,r.useState)(null),[M,q]=(0,r.useState)("jobs"),[P,$]=(0,r.useState)(0),[D,E]=(0,r.useState)(!0),[J,T]=(0,r.useState)(null),[L,z]=(0,r.useState)(!1),[F,I]=(0,r.useState)({}),[R,U]=(0,r.useState)(!1),[V,H]=(0,r.useState)([]),[O,G]=(0,r.useState)([]),[B,W]=(0,r.useState)([]),[Y,K]=(0,r.useState)(!1),[X,Z]=(0,r.useState)(null),[Q,ee]=(0,r.useState)(!1),[et,es]=(0,r.useState)([]),ea=async()=>{if(N){K(!0),console.log("\uD83D\uDD0D LoadJobData called for company:",N.name,"ID:",N.id);try{let e=await (0,A.Uq)(),t=Array.isArray(e.data)?e.data:[];es(t),console.log("\uD83D\uDCDD All forms loaded:",t.length),console.log("\uD83D\uDCDD Current company name:",N.name);let s=t.filter(e=>{let t="approved"===e.status||"posted"===e.status||e.submitted&&"rejected"!==e.status&&!e.status,s=e.company&&""!==e.company.trim(),a=!1;if(s){let t=e.company.toLowerCase().trim(),s=N.name.toLowerCase().trim();a=t===s||t.includes(s)||s.includes(t)}return console.log(`📝 Form ${e.key}: status="${e.status}", submitted=${e.submitted}, approved=${t}, company="${e.company}", targetCompany="${N.name}", hasCompany=${s}, match=${a}`),t&&a});console.log("\uD83D\uDCDD Filtered approved forms:",s.length),H(s);try{console.log("\uD83D\uDD0D Loading jobs from admin API...");let e=await (0,_.Om)({company_id:N.id,per_page:100});console.log("\uD83D\uDD0D Raw jobs response:",e);let t=[];e.data?.data?(t=Array.isArray(e.data.data)?e.data.data:[],console.log("\uD83D\uDD0D Jobs from jobsResponse.data.data:",t.length)):e.data?(t=Array.isArray(e.data)?e.data:[],console.log("\uD83D\uDD0D Jobs from jobsResponse.data:",t.length)):Array.isArray(e)&&(t=e,console.log("\uD83D\uDD0D Jobs from direct array:",t.length)),console.log("\uD83D\uDD0D All jobs loaded:",t.length),t.length>0?(console.log("\uD83D\uDD0D Sample jobs data:"),t.slice(0,3).forEach((e,t)=>{console.log(`  Job ${t+1}:`,{id:e.id,title:e.title,company_name:e.company_name,company_id:e.company_id,is_published:e.is_published})})):console.log("⚠️ No jobs found in API response"),console.log("\uD83D\uDD0D Filtering jobs for company:",N.name,"Company ID:",N.id);let s=t.filter(e=>{let t=e.company_name&&e.company_name.toLowerCase().includes(N.name.toLowerCase()),s=e.company_id&&N.id&&parseInt(e.company_id)===parseInt(N.id),a=t||s;return(e.company_name||e.company_id)&&console.log(`🔍 Job "${e.title}": company_name="${e.company_name}", company_id="${e.company_id}", matchByName=${t}, matchById=${s}, overall=${a}`),a}),a=s.filter(e=>!0===e.is_published),r=s.filter(e=>!1===e.is_published);console.log("\uD83D\uDD0D Results:"),console.log("  Total company jobs:",s.length),console.log("  Published company jobs:",a.length),console.log("  Unpublished company jobs:",r.length),G(a),W(r)}catch(e){console.error("❌ Error loading jobs:",e),console.error("❌ Job error details:",e.response?.data),G([]),W([])}}catch(e){console.error("❌ Error loading job data:",e),console.error("❌ Error details:",e.response?.data),H([]),G([]),W([])}finally{K(!1)}}},er=()=>{L&&I(N),z(!L)},ei=async()=>{U(!0);try{let e=await (0,k.JT)(t,F),s=(0,k.Y_)(e.data);w(s),z(!1),alert("Company updated successfully!")}catch(e){console.error("Error updating company:",e),alert("Failed to update company. Please try again.")}finally{U(!1)}},el=(e,t)=>{I(s=>({...s,[e]:t}))},en=async e=>{try{console.log("Raw job data from form:",e);let t={title:e.title,description:e.description,location:e.location,job_type:e.job_type||"FULL_TIME",salary_min:parseFloat(e.salary_min)||0,salary_max:parseFloat(e.salary_max)||0,required_skills:(()=>{let t=Array.isArray(e.requirements)?e.requirements.filter(e=>e.trim()):[],s=Array.isArray(e.skills)?e.skills.filter(e=>e.trim()):[],a=[...t,...s];return a.length>0?a.join(", "):e.required_skills||"No specific requirements"})(),application_deadline:e.application_deadline||e.deadline,is_active:void 0===e.is_active||e.is_active,company_name:N.name};console.log("Transformed job data being sent:",t),await (0,_._S)(t),alert("Job published successfully! It will now appear in the Published section."),ee(!1),Z(null),H(e=>e.filter(e=>e.key!==X.key)),await ea()}catch(e){console.error("Error creating job:",e),console.error("Error details:",e.response?.data),alert("Failed to create job posting. Please try again.")}},ed=async(e,t)=>{try{let t=await (0,_.T4)(e);console.log("Toggle response:",t),await ea()}catch(e){console.error("Error toggling job publish status:",e),alert("Failed to update job status. Please try again.")}};return D?(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(l.A,{className:"h-12 w-12 animate-spin text-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading company details..."})]})}):J?(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsxs)("div",{className:"text-center max-w-md",children:[(0,a.jsx)("div",{className:"text-red-500 mb-4",children:(0,a.jsx)(n.A,{className:"w-16 h-16 mx-auto"})}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Error Loading Company"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:J}),(0,a.jsx)("button",{onClick:()=>s.push("/admin/companymanagement"),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Return to Company Management"})]})}):(0,a.jsxs)("div",{className:"h-full overflow-y-auto",children:[(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsxs)("button",{onClick:()=>s.push("/admin/companymanagement"),className:"flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors",children:[(0,a.jsx)(d.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Back to Company Management"})]})}),(0,a.jsxs)("div",{className:"relative mb-6",children:[(0,a.jsxs)("div",{className:"h-32 bg-gradient-to-r from-blue-600 to-blue-800 relative overflow-hidden rounded-xl",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black opacity-20"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"}),(0,a.jsx)("div",{className:"absolute bottom-0 left-0 right-0 p-6",children:(0,a.jsxs)("div",{className:"flex items-end justify-between",children:[(0,a.jsxs)("div",{className:"w-16 h-16 bg-white rounded-xl shadow-lg flex items-center justify-center overflow-hidden border-4 border-white",children:[N.logo?(0,a.jsx)("img",{src:N.logo,alt:N.name,className:"w-12 h-12 rounded-lg object-cover",onError:e=>{e.target.style.display="none",e.target.nextSibling.style.display="flex"}}):null,(0,a.jsx)(o.A,{className:`w-8 h-8 text-gray-600 ${N.logo?"hidden":"flex"}`})]}),(0,a.jsxs)("div",{className:"flex-1 text-white pb-2 ml-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4 mb-2",children:[L?(0,a.jsx)("input",{type:"text",value:F.name||"",onChange:e=>el("name",e.target.value),className:"text-2xl font-bold bg-white/20 text-white placeholder-white/70 border border-white/30 rounded-lg px-3 py-1",placeholder:"Company Name"}):(0,a.jsx)("h1",{className:"text-2xl font-bold",children:N.name}),(0,a.jsx)("div",{className:"flex items-center gap-2",children:L?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("button",{onClick:ei,disabled:R,className:"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50",children:[R?(0,a.jsx)(l.A,{className:"w-4 h-4 animate-spin"}):(0,a.jsx)(c.A,{className:"w-4 h-4"}),R?"Saving...":"Save"]}),(0,a.jsxs)("button",{onClick:er,className:"flex items-center gap-2 px-4 py-2 bg-white/20 text-white border border-white/30 rounded-lg hover:bg-white/30 transition-colors",children:[(0,a.jsx)(m.A,{className:"w-4 h-4"}),"Cancel"]})]}):(0,a.jsxs)("button",{onClick:er,className:"flex items-center gap-2 px-4 py-2 bg-white/20 text-white border border-white/30 rounded-lg hover:bg-white/30 transition-colors",children:[(0,a.jsx)(x.A,{className:"w-4 h-4"}),"Edit Company"]})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4 text-white/90",children:[L?(0,a.jsx)("input",{type:"text",value:F.industry||"",onChange:e=>el("industry",e.target.value),className:"bg-white/20 text-white placeholder-white/70 border border-white/30 rounded px-2 py-1",placeholder:"Industry"}):(0,a.jsx)("span",{children:N.industry}),(0,a.jsx)("span",{children:"•"}),(0,a.jsxs)("span",{children:[P.toLocaleString()," followers"]})]})]})]})})]}),(0,a.jsx)("div",{className:"bg-white shadow-sm border border-gray-200 rounded-lg mt-4",children:(0,a.jsx)("div",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-6 text-sm text-gray-600",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(p.A,{className:"w-4 h-4"}),L?(0,a.jsx)("input",{type:"text",value:F.location||"",onChange:e=>el("location",e.target.value),className:"border border-gray-300 rounded px-2 py-1",placeholder:"Location"}):(0,a.jsx)("span",{children:N.location})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"w-4 h-4"}),L?(0,a.jsx)("input",{type:"text",value:F.size||"",onChange:e=>el("size",e.target.value),className:"border border-gray-300 rounded px-2 py-1",placeholder:"Company Size"}):(0,a.jsx)("span",{children:N.size})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(u.A,{className:"w-4 h-4"}),L?(0,a.jsx)("input",{type:"text",value:F.founded||"",onChange:e=>el("founded",e.target.value),className:"border border-gray-300 rounded px-2 py-1",placeholder:"Founded Year"}):(0,a.jsxs)("span",{children:["Founded ",N.founded]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-blue-600 hover:text-blue-800",children:[(0,a.jsx)(g.A,{className:"w-4 h-4"}),L?(0,a.jsx)("input",{type:"url",value:F.website||"",onChange:e=>el("website",e.target.value),className:"border border-gray-300 rounded px-2 py-1",placeholder:"Website URL"}):(0,a.jsxs)("a",{href:N.website,target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-1",children:[(0,a.jsx)("span",{children:"Website"}),(0,a.jsx)(y.A,{className:"w-3 h-3"})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("div",{className:`inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium border ${(e=>{switch(e){case"Tier 1":return"bg-emerald-100 text-emerald-800 border-emerald-200";case"Tier 2":return"bg-blue-100 text-blue-800 border-blue-200";case"Tier 3":return"bg-purple-100 text-purple-800 border-purple-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}})(N.tier)}`,children:[(0,a.jsx)(b.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:N.tier})]}),N.campus_recruiting&&(0,a.jsx)("span",{className:"px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium",children:"Campus Recruiting"})]})]})})})]}),(0,a.jsx)("div",{className:"border-b border-gray-200 mb-6",children:(0,a.jsx)("nav",{className:"-mb-px flex space-x-8",children:["jobs","overview"].map(e=>(0,a.jsx)("button",{onClick:()=>q(e),className:`py-2 px-1 border-b-2 font-medium text-sm ${M===e?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"jobs"===e?"Job Management":"Overview"},e))})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-6 mb-6",children:["overview"===M&&(0,a.jsx)("div",{className:"max-w-4xl",children:(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:["About ",N.name]}),L?(0,a.jsx)("textarea",{value:F.description||"",onChange:e=>el("description",e.target.value),rows:"6",className:"w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Company description..."}):(0,a.jsx)("p",{className:"text-gray-700 leading-relaxed text-lg",children:N.description})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Company Metrics"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6",children:[(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-6 text-center",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-3",children:[(0,a.jsx)(j.A,{className:"w-6 h-6 text-blue-600"}),(0,a.jsx)("h4",{className:"font-semibold text-blue-900",children:"Active Jobs"})]}),(0,a.jsx)("p",{className:"text-3xl font-bold text-blue-900",children:N.totalActiveJobs||0})]}),(0,a.jsxs)("div",{className:"bg-green-50 rounded-lg p-6 text-center",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-3",children:[(0,a.jsx)(h.A,{className:"w-6 h-6 text-green-600"}),(0,a.jsx)("h4",{className:"font-semibold text-green-900",children:"Total Applicants"})]}),(0,a.jsx)("p",{className:"text-3xl font-bold text-green-900",children:N.totalApplicants||0})]}),(0,a.jsxs)("div",{className:"bg-purple-50 rounded-lg p-6 text-center",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-3",children:[(0,a.jsx)(f.A,{className:"w-6 h-6 text-purple-600"}),(0,a.jsx)("h4",{className:"font-semibold text-purple-900",children:"Total Hired"})]}),(0,a.jsx)("p",{className:"text-3xl font-bold text-purple-900",children:N.totalHired||0})]}),(0,a.jsxs)("div",{className:"bg-amber-50 rounded-lg p-6 text-center",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-3",children:[(0,a.jsx)(v.A,{className:"w-6 h-6 text-amber-600"}),(0,a.jsx)("h4",{className:"font-semibold text-amber-900",children:"Pending Review"})]}),(0,a.jsx)("p",{className:"text-3xl font-bold text-amber-900",children:N.awaited_approval||0})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Company Details"}),(0,a.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Industry"}),(0,a.jsx)("p",{className:"text-gray-700",children:N.industry})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Company Size"}),(0,a.jsx)("p",{className:"text-gray-700",children:N.size})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Founded"}),(0,a.jsx)("p",{className:"text-gray-700",children:N.founded})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Headquarters"}),(0,a.jsx)("p",{className:"text-gray-700",children:N.location})]})]})})]})]})}),"jobs"===M&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Job Management"}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("button",{onClick:ea,className:"px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors",children:"Refresh Data"}),(0,a.jsx)("button",{onClick:()=>{H([{id:"test-123",key:"TEST-FORM",company:N.name,status:"posted",details:{description:"Test job description",skills:"React, Node.js",deadline:"2024-12-31"}}])},className:"px-3 py-1 text-sm bg-green-100 text-green-700 rounded-md hover:bg-green-200 transition-colors",children:"Add Test Form"}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Manage job postings from approved forms"})]})]}),(0,a.jsx)(S,{approvedForms:V,publishedJobs:O,unpublishedJobs:B,loadingJobs:Y,onCreateJobFromForm:e=>{Z(e),ee(!0)},onPublishToggle:ed,onEditJob:e=>{s.push(`/admin/jobs/edit/${e}`)},onViewJob:e=>{s.push(`/admin/jobs/${e}`)}})]})]})]}),Q&&X&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto m-4",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Create Job Posting"}),(0,a.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:["Based on form: ",X.key]})]}),(0,a.jsx)("button",{onClick:()=>ee(!1),className:"p-2 hover:bg-gray-100 rounded-full",children:(0,a.jsx)(m.A,{className:"w-5 h-5"})})]})}),(0,a.jsx)("div",{className:"p-6",children:(0,a.jsx)(C.A,{companies:[N],onSubmit:en,onCancel:()=>ee(!1),initialData:{title:X.details?.title||"",description:X.details?.description||"",location:X.details?.location||N.location||"",salary_min:X.details?.salaryMin||"",salary_max:X.details?.salaryMax||"",skills:X.details?.skills?X.details.skills.split(",").map(e=>e.trim()):[],requirements:X.details?.requirements?X.details.requirements.split(",").map(e=>e.trim()):[],application_deadline:X.details?.deadline||"",company_name:N.name,company_id:t}})})]})})]})}function S({approvedForms:e,publishedJobs:t,unpublishedJobs:s,loadingJobs:i,onCreateJobFromForm:n,onPublishToggle:d,onEditJob:o,onViewJob:c}){let[p,h]=(0,r.useState)("to-publish");return i?(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsx)(l.A,{className:"h-8 w-8 animate-spin text-blue-600"})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"border-b border-gray-200",children:(0,a.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,a.jsxs)("button",{onClick:()=>h("to-publish"),className:`py-2 px-1 border-b-2 font-medium text-sm ${"to-publish"===p?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:["To be Published (",s.length,")"]}),(0,a.jsxs)("button",{onClick:()=>h("published"),className:`py-2 px-1 border-b-2 font-medium text-sm ${"published"===p?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:["Published (",t.length,")"]})]})}),(0,a.jsxs)("div",{className:"space-y-6",children:["to-publish"===p&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Jobs To be Published"}),0===s.length?(0,a.jsxs)("div",{className:"text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300",children:[(0,a.jsx)(j.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-500 text-lg",children:"No unpublished jobs"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Jobs created but not yet published will appear here"})]}):(0,a.jsx)("div",{className:"bg-white shadow-sm rounded-lg overflow-hidden",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Job Title"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Location"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Salary"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Deadline"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:s.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.title}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["ID: ",e.id]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.location})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.job_type})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.salary_max?`Up to $${e.salary_max.toLocaleString()}`:"Not specified"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.application_deadline?new Date(e.application_deadline).toLocaleDateString():"Not specified"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium",children:"To be Published"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)("button",{onClick:()=>d(e.id,!1),className:"inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",children:[(0,a.jsx)(N.A,{className:"w-3 h-3 mr-1"}),"Publish"]}),(0,a.jsxs)("button",{onClick:()=>o(e.id),className:"inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,a.jsx)(x.A,{className:"w-3 h-3 mr-1"}),"Edit"]})]})})]},e.id))})]})})})]}),"published"===p&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Published Jobs"}),0===t.length?(0,a.jsxs)("div",{className:"text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300",children:[(0,a.jsx)(j.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-500 text-lg",children:"No published jobs"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Published jobs will appear here"})]}):(0,a.jsx)("div",{className:"bg-white shadow-sm rounded-lg overflow-hidden",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Job Title"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Location"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Salary"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Deadline"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:t.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.title}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["ID: ",e.id]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.location})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.job_type})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.salary_max?`Up to $${e.salary_max.toLocaleString()}`:"Not specified"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.application_deadline?new Date(e.application_deadline).toLocaleDateString():"Not specified"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium",children:"Active"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)("button",{onClick:()=>c(e.id),className:"inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,a.jsx)(w.A,{className:"w-3 h-3 mr-1"}),"View"]}),(0,a.jsxs)("button",{onClick:()=>o(e.id),className:"inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,a.jsx)(x.A,{className:"w-3 h-3 mr-1"}),"Edit"]}),(0,a.jsxs)("button",{onClick:()=>d(e.id,!0),className:"inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:[(0,a.jsx)(m.A,{className:"w-3 h-3 mr-1"}),"Unpublish"]})]})})]},e.id))})]})})})]})]})]})}s(65222)},97992:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4447,681,1658,1060,2305,5956,1045],()=>s(16886));module.exports=a})();