(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1252],{12436:(e,t,s)=>{Promise.resolve().then(s.bind(s,16548))},16548:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var a=s(95155),r=s(12115),l=s(35695),n=s(94631),i=s(40646),o=s(35169),c=s(54861),d=s(27213),m=s(29869),u=s(48136),x=s(4516),p=s(17580),h=s(69074),g=s(38164),b=s(38564),y=s(4229),j=s(48937);function f(e){let{params:t}=e,s=(0,r.use)(t).id,f=(0,l.useRouter)(),[v,N]=(0,r.useState)(!1),[w,_]=(0,r.useState)({}),[C,k]=(0,r.useState)(null),[A,R]=(0,r.useState)(""),[S,T]=(0,r.useState)(!1),[z,E]=(0,r.useState)(!0),F=(0,r.useRef)(null),[L,q]=(0,r.useState)({name:"",industry:"",location:"",size:"",founded:"",website:"",tier:"Tier 3",description:"",campus_recruiting:!1,total_active_jobs:0,total_applicants:0,total_hired:0,awaited_approval:0,logo:null});(0,r.useEffect)(()=>{(async()=>{E(!0);try{let e=(await (0,j.C1)(s)).data;q({name:e.name||"",industry:e.industry||"",location:e.location||"",size:e.size||"",founded:e.founded||"",website:e.website||"",tier:e.tier||"Tier 3",description:e.description||"",campus_recruiting:e.campus_recruiting||!1,total_active_jobs:e.total_active_jobs||0,total_applicants:e.total_applicants||0,total_hired:e.total_hired||0,awaited_approval:e.awaited_approval||0}),e.logo&&R(e.logo)}catch(e){console.error("Error loading company:",e),alert("Failed to load company data. Redirecting back..."),f.push("/admin/companymanagement")}finally{E(!1)}})()},[s,f]);let U=e=>{let{name:t,value:s,type:a,checked:r}=e.target;q({...L,[t]:"checkbox"===a?r:s})},P=()=>{let e={};return L.name.trim()||(e.name="Company name is required"),L.industry.trim()||(e.industry="Industry is required"),L.location.trim()||(e.location="Location is required"),L.size.trim()||(e.size="Company size is required"),L.founded.trim()||(e.founded="Founded year is required"),L.description.trim()||(e.description="Description is required"),L.website.trim()?D(L.website)||(e.website="Please enter a valid URL (include http:// or https://)"):e.website="Website URL is required",_(e),0===Object.keys(e).length},D=e=>{try{return new URL(e),!0}catch(e){return!1}},I=async e=>{if(e.preventDefault(),P()){T(!0);try{let e={...L,total_active_jobs:Number(L.total_active_jobs),total_applicants:Number(L.total_applicants),total_hired:Number(L.total_hired),awaited_approval:Number(L.awaited_approval),campus_recruiting:!!L.campus_recruiting};C&&(e.logo=C),console.log("Submitting updated company data:",e);let t=await (0,j.JT)(s,e);console.log("API response:",t),N(!0),setTimeout(()=>{f.push("/admin/companymanagement")},1500)}catch(e){var t;console.error("Error updating company:",e),(null==(t=e.response)?void 0:t.data)?(console.log("API error details:",e.response.data),_(t=>({...t,...e.response.data,...e.response.data.non_field_errors?{api_error:e.response.data.non_field_errors.join(", ")}:{}}))):_(e=>({...e,api_error:"Failed to update company. Please try again."}))}finally{T(!1)}}},O=()=>{f.push("/admin/companymanagement")};return z?(0,a.jsx)("div",{className:"h-full flex items-center justify-center bg-gray-50",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(n.A,{className:"w-12 h-12 animate-spin mx-auto mb-4 text-blue-500"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading company data..."})]})}):v?(0,a.jsx)("div",{className:"h-full flex items-center justify-center bg-gray-50",children:(0,a.jsx)("div",{className:"bg-white p-8 rounded-xl shadow-sm border border-gray-200 max-w-md w-full",children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4",children:(0,a.jsx)(i.A,{className:"w-8 h-8 text-green-600"})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Company Updated!"}),(0,a.jsx)("p",{className:"text-gray-600 text-center mb-6",children:"The company profile has been successfully updated."}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Redirecting back to company management..."})]})})}):(0,a.jsx)("div",{className:"h-full overflow-y-auto bg-gray-50",children:(0,a.jsxs)("div",{className:"container mx-auto py-8 px-4 md:px-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-6",children:[(0,a.jsx)("button",{onClick:O,className:"mr-4 p-2 rounded-full hover:bg-gray-200 transition-colors",children:(0,a.jsx)(o.A,{className:"w-5 h-5 text-gray-600"})}),(0,a.jsxs)("h1",{className:"text-2xl font-bold text-gray-900",children:["Edit Company: ",L.name]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 md:p-8",children:[w.api_error&&(0,a.jsxs)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg text-red-700 flex items-start gap-3",children:[(0,a.jsx)(c.A,{className:"w-5 h-5 text-red-500 flex-shrink-0 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium",children:"Error"}),(0,a.jsx)("p",{children:w.api_error})]})]}),(0,a.jsxs)("form",{onSubmit:I,children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Company Logo"}),(0,a.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,a.jsx)("div",{className:"w-24 h-24 rounded-lg flex items-center justify-center overflow-hidden ".concat(A?"border border-gray-200":"bg-gradient-to-br from-blue-500 to-purple-600"),children:A?(0,a.jsx)("img",{src:A,alt:"Company logo preview",className:"w-full h-full object-cover"}):L.name?(0,a.jsx)("span",{className:"text-white font-bold text-2xl",children:L.name.charAt(0)}):(0,a.jsx)(d.A,{className:"w-10 h-10 text-white opacity-70"})}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)("button",{type:"button",onClick:()=>{F.current.click()},className:"px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"w-4 h-4"}),A?"Change Logo":"Upload Logo"]}),A&&(0,a.jsx)("button",{type:"button",onClick:()=>{k(null),R(""),F.current&&(F.current.value="")},className:"px-4 py-2 bg-red-50 hover:bg-red-100 text-red-600 rounded-lg",children:"Remove"}),(0,a.jsx)("input",{ref:F,type:"file",accept:"image/*",onChange:e=>{let t=e.target.files[0];if(t){k(t);let e=new FileReader;e.onloadend=()=>{R(e.result)},e.readAsDataURL(t)}},className:"hidden"})]}),(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"Recommended: Square image, at least 200x200px (.jpg, .png)"})]})]})]}),(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Company Name*"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(u.A,{className:"h-5 w-5 text-gray-400"})}),(0,a.jsx)("input",{type:"text",name:"name",value:L.name,onChange:U,className:"pl-10 w-full rounded-lg border ".concat(w.name?"border-red-300":"border-gray-300"," py-3 px-4 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500"),placeholder:"Enter company name"})]}),w.name&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:w.name})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Industry*"}),(0,a.jsxs)("select",{name:"industry",value:L.industry,onChange:U,className:"w-full rounded-lg border ".concat(w.industry?"border-red-300":"border-gray-300"," py-3 px-4 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500"),children:[(0,a.jsx)("option",{value:"",children:"Select Industry"}),(0,a.jsx)("option",{value:"Technology",children:"Technology"}),(0,a.jsx)("option",{value:"Finance",children:"Finance"}),(0,a.jsx)("option",{value:"Healthcare",children:"Healthcare"}),(0,a.jsx)("option",{value:"Education",children:"Education"}),(0,a.jsx)("option",{value:"Manufacturing",children:"Manufacturing"}),(0,a.jsx)("option",{value:"Retail",children:"Retail"}),(0,a.jsx)("option",{value:"Consulting",children:"Consulting"}),(0,a.jsx)("option",{value:"Media",children:"Media"}),(0,a.jsx)("option",{value:"Other",children:"Other"})]}),w.industry&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:w.industry})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Location*"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(x.A,{className:"h-5 w-5 text-gray-400"})}),(0,a.jsx)("input",{type:"text",name:"location",value:L.location,onChange:U,className:"pl-10 w-full rounded-lg border ".concat(w.location?"border-red-300":"border-gray-300"," py-3 px-4 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500"),placeholder:"e.g., New York, NY"})]}),w.location&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:w.location})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Company Size*"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(p.A,{className:"h-5 w-5 text-gray-400"})}),(0,a.jsxs)("select",{name:"size",value:L.size,onChange:U,className:"pl-10 w-full rounded-lg border ".concat(w.size?"border-red-300":"border-gray-300"," py-3 px-4 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500"),children:[(0,a.jsx)("option",{value:"",children:"Select Size"}),(0,a.jsx)("option",{value:"1-10 employees",children:"1-10 employees"}),(0,a.jsx)("option",{value:"11-50 employees",children:"11-50 employees"}),(0,a.jsx)("option",{value:"51-200 employees",children:"51-200 employees"}),(0,a.jsx)("option",{value:"201-500 employees",children:"201-500 employees"}),(0,a.jsx)("option",{value:"501-1000 employees",children:"501-1000 employees"}),(0,a.jsx)("option",{value:"1001-5000 employees",children:"1001-5000 employees"}),(0,a.jsx)("option",{value:"5001+ employees",children:"5001+ employees"})]})]}),w.size&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:w.size})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Founded Year*"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(h.A,{className:"h-5 w-5 text-gray-400"})}),(0,a.jsx)("input",{type:"number",name:"founded",min:"1800",max:new Date().getFullYear(),value:L.founded,onChange:U,className:"pl-10 w-full rounded-lg border ".concat(w.founded?"border-red-300":"border-gray-300"," py-3 px-4 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500"),placeholder:"e.g., 2010"})]}),w.founded&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:w.founded})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Website URL*"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(g.A,{className:"h-5 w-5 text-gray-400"})}),(0,a.jsx)("input",{type:"url",name:"website",value:L.website,onChange:U,className:"pl-10 w-full rounded-lg border ".concat(w.website?"border-red-300":"border-gray-300"," py-3 px-4 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500"),placeholder:"https://example.com"})]}),w.website&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:w.website})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Tier"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(b.A,{className:"h-5 w-5 text-gray-400"})}),(0,a.jsxs)("select",{name:"tier",value:L.tier,onChange:U,className:"pl-10 w-full rounded-lg border border-gray-300 py-3 px-4 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"Tier 1",children:"Tier 1"}),(0,a.jsx)("option",{value:"Tier 2",children:"Tier 2"}),(0,a.jsx)("option",{value:"Tier 3",children:"Tier 3"})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("input",{type:"checkbox",id:"campus_recruiting",name:"campus_recruiting",checked:L.campus_recruiting,onChange:U,className:"h-5 w-5 text-blue-600 rounded border-gray-300 focus:ring-blue-500"}),(0,a.jsx)("label",{htmlFor:"campus_recruiting",className:"text-sm font-medium text-gray-700",children:"Campus Recruiting Program"})]}),(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Description*"}),(0,a.jsx)("textarea",{name:"description",value:L.description,onChange:U,rows:"4",className:"w-full rounded-lg border ".concat(w.description?"border-red-300":"border-gray-300"," py-3 px-4 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500"),placeholder:"Enter a description of the company..."}),w.description&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:w.description})]})]}),(0,a.jsxs)("div",{className:"mt-8 flex flex-col md:flex-row gap-4 md:gap-3 justify-end",children:[(0,a.jsx)("button",{type:"button",onClick:O,className:"px-6 py-3 border border-gray-300 rounded-lg text-gray-700 bg-white hover:bg-gray-50 font-medium",disabled:S,children:"Cancel"}),(0,a.jsx)("button",{type:"submit",className:"px-6 py-3 bg-blue-600 rounded-lg text-white hover:bg-blue-700 font-medium flex items-center justify-center gap-2",disabled:S,children:S?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.A,{className:"w-5 h-5 animate-spin"}),"Updating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(y.A,{className:"w-5 h-5"}),"Save Changes"]})})]})]})]})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[3464,3885,3983,8937,8441,1684,7358],()=>t(12436)),_N_E=e.O()}]);