"use strict";exports.id=1420,exports.ids=[1420],exports.modules={1420:(e,s,t)=>{t.r(s),t.d(s,{default:()=>y});var a=t(60687),r=t(43210),l=t(58869),n=t(97051),i=t(62688);let c=(0,i.A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var o=t(64021);let d=(0,i.A)("shield-alert",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"M12 8v4",key:"1got3b"}],["path",{d:"M12 16h.01",key:"1drbdi"}]]);var u=t(41312),m=t(16023),x=t(21134),p=t(363),h=t(16189),f=t(97632),b=t(1469),g=t(84578);let y=()=>{let e=(0,h.useRouter)(),s=(0,h.usePathname)(),{theme:t,changeTheme:i}=(0,g.D)(),[y,j]=(0,r.useState)(null),[v,N]=(0,r.useState)(!1),[w,k]=(0,r.useState)(!1),[S,C]=(0,r.useState)(""),[_,A]=(0,r.useState)(""),[M,P]=(0,r.useState)(""),[U,R]=(0,r.useState)(""),[$,D]=(0,r.useState)(!0),[E,F]=(0,r.useState)(!0),[z,T]=(0,r.useState)("medium"),[Y,V]=(0,r.useState)(""),[q,L]=(0,r.useState)(""),[B,G]=(0,r.useState)(""),[H,I]=(0,r.useState)({maintenanceMode:!1,allowNewRegistrations:!0,defaultUserRole:"student"}),[O,J]=(0,r.useState)("profile"),[K,Q]=(0,r.useState)(!1),[W,X]=(0,r.useState)({type:"",text:""});(0,r.useEffect)(()=>{(async()=>{if(f.c4()){N(!0);try{let e=await b.Nt.getUserData();j(e);let s="ADMIN"===e.user_type||"admin"===e.role||e.user?.role==="admin";k(s),"undefined"!=typeof document&&(document.cookie=`role=${e.user_type||"STUDENT"}; path=/; max-age=86400`)}catch(s){console.error("Error fetching user data:",s),e.push("/login")}}else e.push("/login")})()},[e,s]),(0,r.useEffect)(()=>{v&&y&&(C(y.first_name&&y.last_name?`${y.first_name} ${y.last_name}`:y.name||""),A(y.contact_email||y.email||y.user?.email||""),P(y.profile_image_url||y.avatar||""),R(y.user_type||y.role||y.user?.role||"student"),y.notification_preferences&&(D(!1!==y.notification_preferences.email),F(!1!==y.notification_preferences.browser)))},[v,y]),(0,r.useEffect)(()=>{},[]);let Z=async e=>{e.preventDefault(),Q(!0),X({type:"",text:""});try{let e=S.trim().split(" "),s=e[0]||"",t=e.slice(1).join(" ")||"";await b.Nt.updateUserProfile({first_name:s,last_name:t,contact_email:_,profile_image_url:M}),X({type:"success",text:"Your profile information has been updated successfully."})}catch(e){X({type:"error",text:e.response?.data?.message||e.message||"Failed to update profile. Please try again."})}finally{Q(!1)}},ee=async e=>{e.preventDefault(),Q(!0),X({type:"",text:""});try{await b.Nt.updateUserProfile({notification_preferences:{email:$,browser:E}}),X({type:"success",text:"Your notification settings have been saved."})}catch(e){X({type:"error",text:e.response?.data?.message||e.message||"Failed to update notification preferences."})}finally{Q(!1)}},es=async e=>{if(e.preventDefault(),Q(!0),X({type:"",text:""}),q!==B){X({type:"error",text:"New passwords do not match."}),Q(!1);return}if(q.length<6){X({type:"error",text:"New password must be at least 6 characters long."}),Q(!1);return}try{let e=await b.Nt.updateUserPassword({current_password:Y,new_password:q,confirm_password:B});X({type:"success",text:e.message||"Your password has been changed successfully."}),V(""),L(""),G("")}catch(e){if(console.error("Password update error:",e),e.response?.data){let s=e.response.data,t="";X({type:"error",text:s.current_password?s.current_password[0]:s.new_password?s.new_password[0]:s.confirm_password?s.confirm_password[0]:s.detail?s.detail:s.message?s.message:"Failed to update password. Please try again."})}else X({type:"error",text:"Failed to update password. Please try again."})}finally{Q(!1)}},et=async e=>{e.preventDefault(),Q(!0),X({type:"",text:""});try{w&&(await b.Er.updateSystemSettings(H),X({type:"success",text:"System settings have been updated successfully."}))}catch(e){X({type:"error",text:e.message||"Failed to update system settings. Please try again."})}finally{Q(!1)}};return(0,a.jsx)("div",{className:"p-6 bg-gray-50 min-h-screen",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Account Settings"}),W.text&&(0,a.jsx)("div",{className:`mb-6 p-4 rounded-lg ${"success"===W.type?"bg-green-50 text-green-800 border border-green-200":"error"===W.type?"bg-red-50 text-red-800 border border-red-200":""}`,children:W.text}),(0,a.jsx)("div",{className:"bg-white shadow-sm rounded-lg mb-6",children:(0,a.jsxs)("div",{className:"flex border-b overflow-x-auto",children:[(0,a.jsxs)("button",{className:`px-6 py-4 font-medium text-sm flex items-center gap-2 settings-tab ${"profile"===O?"text-blue-600 border-b-2 border-blue-600 active":"text-gray-600 hover:text-gray-900"}`,onClick:()=>J("profile"),children:[(0,a.jsx)(l.A,{className:"w-4 h-4"}),"Profile"]}),(0,a.jsxs)("button",{className:`px-6 py-4 font-medium text-sm flex items-center gap-2 settings-tab ${"notifications"===O?"text-blue-600 border-b-2 border-blue-600 active":"text-gray-600 hover:text-gray-900"}`,onClick:()=>J("notifications"),children:[(0,a.jsx)(n.A,{className:"w-4 h-4"}),"Notifications"]}),(0,a.jsxs)("button",{className:`px-6 py-4 font-medium text-sm flex items-center gap-2 settings-tab ${"appearance"===O?"text-blue-600 border-b-2 border-blue-600 active":"text-gray-600 hover:text-gray-900"}`,onClick:()=>J("appearance"),children:[(0,a.jsx)(c,{className:"w-4 h-4"}),"Appearance"]}),(0,a.jsxs)("button",{className:`px-6 py-4 font-medium text-sm flex items-center gap-2 settings-tab ${"security"===O?"text-blue-600 border-b-2 border-blue-600 active":"text-gray-600 hover:text-gray-900"}`,onClick:()=>J("security"),children:[(0,a.jsx)(o.A,{className:"w-4 h-4"}),"Security"]}),w&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("button",{className:`px-6 py-4 font-medium text-sm flex items-center gap-2 settings-tab ${"system"===O?"text-blue-600 border-b-2 border-blue-600 active":"text-gray-600 hover:text-gray-900"}`,onClick:()=>J("system"),children:[(0,a.jsx)(d,{className:"w-4 h-4"}),"System"]}),(0,a.jsxs)("button",{className:`px-6 py-4 font-medium text-sm flex items-center gap-2 settings-tab ${"users"===O?"text-blue-600 border-b-2 border-blue-600 active":"text-gray-600 hover:text-gray-900"}`,onClick:()=>J("users"),children:[(0,a.jsx)(u.A,{className:"w-4 h-4"}),"User Management"]})]})]})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:["profile"===O&&(0,a.jsx)("form",{onSubmit:Z,children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-1",children:"Profile Settings"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"Update your personal information"})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-6 mb-6",children:[(0,a.jsx)("div",{className:"w-24 h-24 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden",children:M?(0,a.jsx)("img",{src:M,alt:S,className:"w-full h-full object-cover"}):(0,a.jsx)("span",{className:"text-2xl font-bold text-gray-400",children:S.charAt(0)})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:S||"Your Name"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:U}),(0,a.jsxs)("button",{type:"button",className:"px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg text-sm font-medium flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"w-4 h-4"}),"Change avatar"]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Full Name"}),(0,a.jsx)("input",{type:"text",value:S,onChange:e=>C(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Email Address"}),(0,a.jsx)("input",{type:"email",value:_,onChange:e=>A(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Role"}),(0,a.jsx)("input",{type:"text",value:U,disabled:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 cursor-not-allowed"})]})]})}),"notifications"===O&&(0,a.jsx)("form",{onSubmit:ee,children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-1",children:"Notification Preferences"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"Configure how you want to receive notifications"})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between py-3 border-b border-gray-200",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900",children:"Email Notifications"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Receive email notifications for important updates"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:$,onChange:()=>D(!$),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between py-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900",children:"Browser Notifications"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Receive push notifications in your browser"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:E,onChange:()=>F(!E),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]})]}),(0,a.jsx)("div",{className:"pt-4",children:(0,a.jsx)("button",{type:"submit",disabled:K,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:K?"Saving...":"Save Preferences"})})]})}),"appearance"===O&&(0,a.jsx)("form",{onSubmit:e=>{e.preventDefault(),Q(!0);try{X({type:"success",text:"Your appearance preferences have been saved and applied."})}catch(e){X({type:"error",text:"Failed to save appearance preferences."})}finally{Q(!1)}},children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-1",children:"Appearance Settings"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"Customize your visual experience"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Theme"}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsx)("div",{className:`p-4 border ${"light"===t?"border-blue-500 bg-blue-50":"border-gray-200"} rounded-lg cursor-pointer hover:bg-gray-50`,onClick:()=>i("light"),children:(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)(x.A,{className:"w-6 h-6 text-gray-700 mb-2"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"Light"})]})}),(0,a.jsx)("div",{className:`p-4 border ${"dark"===t?"border-blue-500 bg-blue-50":"border-gray-200"} rounded-lg cursor-pointer hover:bg-gray-50`,onClick:()=>i("dark"),children:(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)(p.A,{className:"w-6 h-6 text-gray-700 mb-2"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"Dark"})]})}),(0,a.jsx)("div",{className:`p-4 border ${"system"===t?"border-blue-500 bg-blue-50":"border-gray-200"} rounded-lg cursor-pointer hover:bg-gray-50`,onClick:()=>i("system"),children:(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)(c,{className:"w-6 h-6 text-gray-700 mb-2"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"System"})]})})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Font Size"}),(0,a.jsxs)("select",{value:z,onChange:e=>T(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"small",children:"Small"}),(0,a.jsx)("option",{value:"medium",children:"Medium"}),(0,a.jsx)("option",{value:"large",children:"Large"})]})]})]}),(0,a.jsx)("div",{className:"pt-4",children:(0,a.jsx)("button",{type:"submit",disabled:K,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:K?"Saving...":"Save Preferences"})})]})}),"security"===O&&(0,a.jsx)("form",{onSubmit:es,children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-1",children:"Security Settings"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"Update your password and security preferences"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Current Password"}),(0,a.jsx)("input",{type:"password",value:Y,onChange:e=>V(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"New Password"}),(0,a.jsx)("input",{type:"password",value:q,onChange:e=>L(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Confirm New Password"}),(0,a.jsx)("input",{type:"password",value:B,onChange:e=>G(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),(0,a.jsx)("div",{className:"pt-4",children:(0,a.jsx)("button",{type:"submit",disabled:K,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:K?"Updating...":"Update Password"})})]})}),w&&"system"===O&&(0,a.jsx)("form",{onSubmit:et,children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-1",children:"System Settings"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"Configure global system settings"})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between py-3 border-b border-gray-200",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900",children:"Maintenance Mode"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Enable to put the site in maintenance mode"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:H.maintenanceMode,onChange:()=>I({...H,maintenanceMode:!H.maintenanceMode}),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between py-3 border-b border-gray-200",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900",children:"Allow New Registrations"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Enable to allow new users to register"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:H.allowNewRegistrations,onChange:()=>I({...H,allowNewRegistrations:!H.allowNewRegistrations}),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Default User Role"}),(0,a.jsxs)("select",{value:H.defaultUserRole,onChange:e=>I({...H,defaultUserRole:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"student",children:"Student"}),(0,a.jsx)("option",{value:"teacher",children:"Teacher"}),(0,a.jsx)("option",{value:"admin",children:"Admin"})]})]})]}),(0,a.jsx)("div",{className:"pt-4",children:(0,a.jsx)("button",{type:"submit",disabled:K,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:K?"Saving...":"Save System Settings"})})]})}),w&&"users"===O&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-1",children:"User Management"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"Manage users and permissions"})]}),(0,a.jsx)("p",{className:"text-gray-700",children:"This panel allows you to manage users. For full user management functionality, please visit the admin dashboard."}),(0,a.jsx)("div",{className:"pt-4",children:(0,a.jsx)("a",{href:"/admin/users",className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors inline-block",children:"Go to User Management"})})]})]})]})})}},41312:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},64021:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},97051:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},97632:(e,s,t)=>{t.d(s,{O5:()=>r,c4:()=>a,fE:()=>l}),t(58138);let a=()=>null,r=e=>{},l=e=>{}}};