module.exports = {

"[project]/src/data/jobsData.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_data_jobsData_b1f67364.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/data/jobsData.js [app-ssr] (ecmascript)");
    });
});
}}),

};