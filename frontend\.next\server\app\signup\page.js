(()=>{var e={};e.id=879,e.ids=[879],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20325:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>p,tree:()=>d});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["signup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,52745)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\signup\\page.jsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,75535)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\layout.js"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\signup\\page.jsx"],c={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/signup/page",pathname:"/signup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35954:(e,t,r)=>{Promise.resolve().then(r.bind(r,84571))},52745:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\signup\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\signup\\page.jsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},72906:(e,t,r)=>{Promise.resolve().then(r.bind(r,52745))},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84571:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(60687),a=r(43210),i=r(16189),n=r(51060);let o=["Amrita Vishwa Vidyapeetham","Anna University","Ashoka University","Banaras Hindu University (BHU)","BITS Pilani","Christ University (Bangalore)","Delhi University (DU)","FMS Delhi","IIM Ahmedabad","IIM Bangalore","IIM Calcutta","IIM Lucknow","IIIT Allahabad","IIIT Bangalore","IIIT Delhi","IIIT Hyderabad","IIIT Pune","IIT Bombay","IIT Delhi","IIT Hyderabad","IIT Kanpur","IIT Kharagpur","IIT Madras","IIT Roorkee","Indian School of Business (ISB Hyderabad)","Jamia Millia Islamia","Jawaharlal Nehru University (JNU)","Jain University","Manipal University","NIT Calicut","NIT Rourkela","NIT Surathkal","NIT Trichy","NIT Warangal","Osmania University","Shiv Nadar University","SRM Institute of Science and Technology","Symbiosis International University (Pune)","University of Calcutta","University of Hyderabad","University of Mumbai","VIT Vellore","XLRI Jamshedpur"];function l(){let e=(0,i.useRouter)(),[t,r]=(0,a.useState)(""),[l,d]=(0,a.useState)(""),[u,c]=(0,a.useState)(""),[p,m]=(0,a.useState)(""),[x]=(0,a.useState)("STUDENT"),[g,h]=(0,a.useState)(!1),b=(0,a.useRef)(null),f=o.filter(e=>e.toLowerCase().includes(l.toLowerCase())),v=async r=>{if(r.preventDefault(),u!==p)return void alert("Passwords must match");let s="STUDENT";try{await n.A.post("http://127.0.0.1:8000/api/auth/register/student/",{email:t,password:u,first_name:t.split("@")[0],last_name:"",student_id:"TEMP123",contact_email:t,branch:"CSE",gpa:"8.5",college:1}),localStorage.setItem("userEmail",t),localStorage.setItem("collegeName",l),localStorage.setItem("role",s),document.cookie=`role=${s}; path=/; max-age=86400`,alert("Signup successful"),e.push("/login")}catch(e){alert(e.response?.data?.message||"Signup failed")}};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-r from-[#242734] to-[#241F2A] flex items-center justify-center p-4",children:(0,s.jsxs)("form",{onSubmit:v,className:"w-full max-w-md bg-white rounded-xl shadow-2xl p-10 flex flex-col gap-6",children:[(0,s.jsx)("h1",{className:"text-center text-2xl text-gray-800 font-bold mb-2",children:"SIGNUP"}),(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)("label",{className:"mb-2 font-semibold text-gray-800",children:"Email"}),(0,s.jsx)("input",{type:"email",value:t,onChange:e=>r(e.target.value),className:"p-3 rounded-lg border border-gray-300 text-gray-800 text-base outline-none",required:!0})]}),(0,s.jsxs)("div",{className:"flex flex-col relative",children:[(0,s.jsx)("label",{className:"mb-2 font-semibold text-gray-800",children:"College Name"}),(0,s.jsx)("input",{type:"text",value:l,onChange:e=>{d(e.target.value),h(""!==e.target.value.trim())},className:"p-3 rounded-lg border border-gray-300 text-gray-800 text-base outline-none",required:!0}),g&&f.length>0&&(0,s.jsx)("div",{className:"absolute top-full left-0 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg z-50 max-h-48 overflow-y-auto",children:f.map((e,t)=>(0,s.jsx)("div",{onClick:()=>{d(e),h(!1)},className:"cursor-pointer px-4 py-2 hover:bg-blue-100 text-gray-700 border-b border-gray-100",children:e},t))})]}),(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)("label",{className:"mb-2 font-semibold text-gray-800",children:"Password"}),(0,s.jsx)("input",{type:"password",value:u,onChange:e=>c(e.target.value),className:"p-3 rounded-lg border border-gray-300 text-gray-800 text-base outline-none",required:!0})]}),(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)("label",{className:"mb-2 font-semibold text-gray-800",children:"Confirm Password"}),(0,s.jsx)("input",{type:"password",value:p,ref:b,onChange:e=>m(e.target.value),className:"p-3 rounded-lg border border-gray-300 text-gray-800 text-base outline-none",required:!0})]}),p&&u!==p&&(0,s.jsx)("div",{className:"text-red-400 text-sm -mt-3",children:"Passwords must match."}),(0,s.jsx)("button",{type:"submit",disabled:!u||u!==p,className:"p-3 rounded-lg bg-indigo-500 text-white text-base font-medium hover:bg-indigo-600 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed",children:"Signup"})]})})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,681,1658,1060,2305],()=>r(20325));module.exports=s})();