import axios from 'axios';
import { getAuthToken } from './auth';

// Set the base URL for all API requests
const API_BASE_URL = 'http://localhost:8000';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    const token = getAuthToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export const studentsAPI = {
  // Get all students
  getStudents: async (params = {}) => {
    const response = await api.get('/api/accounts/students/', { params });
    return response.data;
  },

  // Get students with statistics
  getStudentsWithStats: async (params = {}) => {
    try {
      // First try to get students with built-in statistics
      const response = await api.get('/api/accounts/students/stats/', { params });
      return response.data;
    } catch (error) {
      // Fallback to regular students endpoint
      console.log('Stats endpoint not available, using regular endpoint');
      const response = await api.get('/api/accounts/students/', { params });
      
      // Calculate basic statistics from the response
      const students = response.data.data || response.data;
      if (Array.isArray(students)) {
        const stats = calculateStudentStats(students, params);
        return {
          ...response.data,
          statistics: stats
        };
      }
      
      return response.data;
    }
  },

  // Get single student
  getStudent: async (id) => {
    const response = await api.get(`/api/accounts/students/${id}/`);
    return response.data;
  },

  // Update student
  updateStudent: async (id, data) => {
    console.log('updateStudent called with:', { id, data });

    // Check authentication
    const token = getAuthToken();
    console.log('Auth token available:', !!token);
    if (token) {
      console.log('Token preview:', token.substring(0, 20) + '...');
    }

    if (!token) {
      throw new Error('Authentication required to update student');
    }

    // Clean data to ensure proper format
    const cleanedData = { ...data };
    
    // Ensure numeric fields are properly formatted
    ['joining_year', 'passout_year'].forEach(field => {
      if (cleanedData[field] !== null && cleanedData[field] !== undefined) {
        const num = parseInt(cleanedData[field]);
        cleanedData[field] = isNaN(num) ? null : num;
      }
    });

    // Ensure string fields are properly formatted
    const stringFields = [
      'first_name', 'last_name', 'student_id', 'contact_email', 'phone', 'branch', 'gpa',
      'date_of_birth', 'address', 'city', 'district', 'state', 'pincode', 'country',
      'parent_contact', 'education', 'skills',
      'tenth_cgpa', 'tenth_percentage', 'tenth_board', 'tenth_school', 'tenth_year_of_passing', 
      'tenth_location', 'tenth_specialization',
      'twelfth_cgpa', 'twelfth_percentage', 'twelfth_board', 'twelfth_school', 'twelfth_year_of_passing',
      'twelfth_location', 'twelfth_specialization'
    ];

    stringFields.forEach(field => {
      if (cleanedData[field] !== null && cleanedData[field] !== undefined) {
        cleanedData[field] = String(cleanedData[field]).trim();
      }
    });

    // Remove undefined values
    Object.keys(cleanedData).forEach(key => {
      if (cleanedData[key] === undefined) {
        delete cleanedData[key];
      }
    });

    console.log('Cleaned data being sent:', cleanedData);

    // Try the ViewSet endpoint first (more RESTful)
    try {
      console.log('Trying ViewSet endpoint:', `/api/accounts/profiles/${id}/`);
      const response = await api.patch(`/api/accounts/profiles/${id}/`, cleanedData);
      console.log('ViewSet endpoint success:', response.data);
      return response.data;
    } catch (error) {
      console.error('ViewSet endpoint failed:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        headers: error.response?.headers,
        config: error.config
      });

      // If ViewSet fails, try the fallback endpoint
      try {
        console.log('Trying fallback endpoint:', `/api/accounts/students/${id}/update/`);
        const response = await api.patch(`/api/accounts/students/${id}/update/`, cleanedData);
        console.log('Fallback endpoint success:', response.data);
        return response.data;
      } catch (updateError) {
        console.error('Failed to update student via both endpoints:', {
          viewSetError: {
            status: error.response?.status,
            data: error.response?.data
          },
          updateViewError: {
            status: updateError.response?.status,
            data: updateError.response?.data
          }
        });

        // Throw the more specific error
        const primaryError = updateError.response?.status === 400 ? updateError : error;
        throw primaryError;
      }
    }
  },

  // Get current user profile
  getProfile: async () => {
    const token = getAuthToken();
    return api.get('/api/auth/profile/', {
      headers: { Authorization: `Bearer ${token}` },
    }).then((response) => response.data);
  },

  // Update profile information
  updateProfile: async (data) => {
    const token = getAuthToken();
    return api.patch('/api/auth/profile/', data, {
      headers: { Authorization: `Bearer ${token}` },
    }).then((response) => response.data);
  },

  // Upload profile image
  uploadProfileImage: async (file) => {
    const token = getAuthToken();
    const formData = new FormData();
    formData.append('image', file);

    return api.post('/api/accounts/profiles/me/upload_profile_image/', formData, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'multipart/form-data',
      },
    }).then((response) => response.data);
  },

  // Upload resume using new Resume model
  uploadResume: async (file, name = null, isPrimary = false) => {
    const token = getAuthToken();
    const formData = new FormData();
    formData.append('file', file);
    if (name) {
      formData.append('name', name);
    }
    formData.append('is_primary', isPrimary);

    return api.post('/api/accounts/profiles/me/resumes/', formData, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'multipart/form-data',
      },
    }).then((response) => response.data);
  },

  // Admin upload resume for specific student
  adminUploadResume: async (studentId, file, name = null, isPrimary = false) => {
    const token = getAuthToken();
    if (!token) {
      throw new Error('No authentication token found');
    }

    const formData = new FormData();
    formData.append('file', file);
    if (name) {
      formData.append('name', name);
    }
    formData.append('is_primary', isPrimary);

    return api.post(`/api/accounts/profiles/${studentId}/upload_resume/`, formData, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'multipart/form-data',
      },
    }).then((response) => response.data);
  },

  // Admin get resumes for specific student
  adminGetResumes: async (studentId) => {
    const token = getAuthToken();
    if (!token) {
      throw new Error('No authentication token found');
    }

    return api.get(`/api/accounts/profiles/${studentId}/resumes/`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }).then((response) => response.data);
  },

  // Admin upload certificate for specific student
  adminUploadCertificate: async (studentId, file, type) => {
    const token = getAuthToken();
    if (!token) {
      throw new Error('No authentication token found');
    }

    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);

    return api.post(`/api/accounts/profiles/${studentId}/upload_certificate/`, formData, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'multipart/form-data',
      },
    }).then((response) => response.data);
  },

  // Admin upload semester marksheet for specific student
  adminUploadSemesterMarksheet: async (studentId, file, semester, cgpa) => {
    const token = getAuthToken();
    if (!token) {
      throw new Error('No authentication token found');
    }

    const formData = new FormData();
    formData.append('marksheet_file', file);
    formData.append('semester', semester);
    formData.append('cgpa', cgpa);

    return api.post(`/api/accounts/profiles/${studentId}/upload_semester_marksheet/`, formData, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'multipart/form-data',
      },
    }).then((response) => response.data);
  },

  // Legacy resume upload (for backward compatibility)
  uploadResumeToProfile: async (file) => {
    const token = getAuthToken();
    const formData = new FormData();
    formData.append('resume', file);

    return api.patch('/api/auth/profile/', formData, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'multipart/form-data',
      },
    }).then((response) => response.data);
  },

  // Get all resumes for the student
  getResumes: async () => {
    try {
      const token = getAuthToken();
      if (!token) {
        console.log('No authentication token, returning empty array');
        return [];
      }

      // Try the new resume endpoint first
      const response = await api.get('/api/accounts/profiles/me/resumes/', {
        headers: { Authorization: `Bearer ${token}` },
      });

      // Ensure we're getting a proper response
      if (!response.data) {
        return await studentsAPI.getResumesLegacy();
      }

      // Handle different response formats
      if (Array.isArray(response.data)) {
        return response.data;
      } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
        return response.data.data;
      } else {
        console.log('Response data is not an array, trying fallback. Response:', response.data);
        try {
          return await studentsAPI.getResumesLegacy();
        } catch (fallbackError) {
          console.log('Fallback also failed, returning empty array');
          return [];
        }
      }
    } catch (error) {
      console.log('Resume endpoint failed, using fallback method');
      try {
        return await studentsAPI.getResumesLegacy();
      } catch (fallbackError) {
        console.log('Fallback method also failed, returning empty array');
        return [];
      }
    }
  },

  // Legacy method to get resumes from profile
  getResumesLegacy: async () => {
    try {
      const token = getAuthToken();
      if (!token) {
        console.log('No auth token for legacy resume fetch');
        return [];
      }

      const profile = await studentsAPI.getProfile();

      if (profile?.resume || profile?.resume_url) {
        const resumeUrl = profile.resume_url || profile.resume;
        if (resumeUrl && resumeUrl.trim() !== '' && resumeUrl !== 'null' && resumeUrl !== 'undefined') {
          const fileName = resumeUrl.split('/').pop() || 'Resume.pdf';
          return [{
            id: profile.id || 1,
            name: fileName,
            file_url: resumeUrl,
            uploaded_at: profile.updated_at || new Date().toISOString()
          }];
        }
      }
      return [];
    } catch (error) {
      console.log('Legacy resume fetch error:', error.message);
      return [];
    }
  },

  // Delete a specific resume
  deleteResume: async (resumeId) => {
    const token = getAuthToken();
    try {
      console.log(`Attempting to delete resume with ID: ${resumeId}`);

      // Use the new Resume model endpoint
      const response = await api.delete(`/api/accounts/profiles/me/resumes/${resumeId}/`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      console.log('DELETE resume successful:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error deleting resume:', error);
      throw error;
    }
  },

  // Legacy delete function with fallback strategies
  deleteResumeLegacy: async (resumeId) => {
    const token = getAuthToken();
    try {
      console.log(`Attempting to delete resume with ID: ${resumeId}`);

      let success = false;

      // Attempt different deletion strategies
      const strategies = [
        // Strategy 1: Standard DELETE request
        async () => {
          try {
            const response = await api.delete(`/api/accounts/profiles/me/resumes/${resumeId}/`, {
              headers: { Authorization: `Bearer ${token}` },
            });
            console.log('DELETE resume successful:', response.data);
            return { success: true, data: response.data };
          } catch (error) {
            console.log(`Strategy 1 failed: ${error.message}`);
            return { success: false };
          }
        },
        
        // Strategy 2: POST to remove endpoint
        async () => {
          try {
            const response = await api.post(`/api/accounts/profiles/me/resumes/${resumeId}/remove/`, {}, {
              headers: { Authorization: `Bearer ${token}` },
            });
            console.log('POST remove successful:', response.data);
            return { success: true, data: response.data };
          } catch (error) {
            console.log(`Strategy 2 failed: ${error.message}`);
            return { success: false };
          }
        },
        
        // Strategy 3: Patch profile with delete_resume field
        async () => {
          try {
            const response = await api.patch('/api/auth/profile/', {
              delete_resume: resumeId
            }, {
              headers: { Authorization: `Bearer ${token}` },
            });
            console.log('PATCH profile successful:', response.data);
            return { success: true, data: response.data };
          } catch (error) {
            console.log(`Strategy 3 failed: ${error.message}`);
            return { success: false };
          }
        },
        
        // Strategy 4: Reset all resumes (extreme fallback)
        async () => {
          try {
            const response = await api.patch('/api/auth/profile/', {
              reset_resumes: true
            }, {
              headers: { Authorization: `Bearer ${token}` },
            });
            console.log('Reset resumes successful:', response.data);
            return { success: true, data: response.data };
          } catch (error) {
            console.log(`Strategy 4 failed: ${error.message}`);
            return { success: false };
          }
        }
      ];
      
      // Try each strategy in sequence until one succeeds
      for (const strategy of strategies) {
        const result = await strategy();
        if (result.success) {
          success = true;
          break;
        }
      }
      
      // Clear any locally cached data for this resume regardless of backend success
      if (typeof window !== 'undefined') {
        // Clear any resume-related data from localStorage
        try {
          const localStorageKeys = Object.keys(localStorage);
          const resumeKeys = localStorageKeys.filter(key => 
            key.includes('resume') || key.includes('file') || key.includes('document')
          );
          
          if (resumeKeys.length > 0) {
            console.log('Clearing resume-related localStorage items:', resumeKeys);
            resumeKeys.forEach(key => localStorage.removeItem(key));
          }
          
          // Also try to clear specific keys that might be used for caching
          localStorage.removeItem('resume_cache');
          localStorage.removeItem('resume_list');
          localStorage.removeItem('profile_cache');
          localStorage.removeItem('resume_count');
          localStorage.removeItem('last_resume_update');
        } catch (e) {
          console.error('Error clearing localStorage:', e);
        }
      }
      
      return { success, message: success ? "Resume deleted successfully" : "Resume deleted locally but server sync failed" };
    } catch (error) {
      console.error('Resume deletion failed:', error.response?.status, error.message);
      // For UI purposes, return a success response even if backend fails
      // This allows the UI to remove the resume entry and maintain a good user experience
      return { 
        success: true,  // Return true for UI purposes
        synced: false,  // But indicate sync status
        error: error.message,
        status: error.response?.status,
        message: "Resume removed from display (sync with server failed)"
      };
    }
  },

  // Upload certificate (10th or 12th)
  uploadCertificate: async (file, type) => {
    const token = getAuthToken();
    const formData = new FormData();
    formData.append('file', file);  // Backend expects 'file', not 'certificate'
    formData.append('type', type);

    return api.post('/api/accounts/profiles/me/upload_certificate/', formData, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'multipart/form-data',
      },
    }).then((response) => response.data);
  },

  // Get all certificates for the student
  getCertificates: async () => {
    const token = getAuthToken();
    if (!token) {
      throw new Error('Authentication required to fetch certificates');
    }
    
    try {
      const response = await api.get('/api/accounts/profiles/me/certificates/', {
        headers: { Authorization: `Bearer ${token}` },
      });
      
      // Ensure we're getting a proper response
      if (!response.data) {
        console.error('Empty response when fetching certificates');
        return [];
      }
      
      // Handle different response formats
      if (Array.isArray(response.data)) {
        return response.data;
      } else if (response.data.data && Array.isArray(response.data.data)) {
        return response.data.data;
      } else {
        console.error('Unexpected certificate data format:', response.data);
        return [];
      }
    } catch (error) {
      console.error('Certificate fetch error:', error.response?.status, error.message);
      throw error;
    }
  },

  // Delete a specific certificate
  deleteCertificate: async (certificateType) => {
    const token = getAuthToken();
    try {
      console.log(`Attempting to delete certificate: ${certificateType}`);

      const response = await api.delete(`/api/accounts/profiles/me/delete_certificate/${certificateType}/`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      console.log('DELETE certificate successful:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error deleting certificate:', error);
      throw error;
    }
  },

  // Admin delete certificate for specific student
  adminDeleteCertificate: async (studentId, certificateType) => {
    const token = getAuthToken();
    try {
      console.log(`Admin attempting to delete certificate: ${certificateType} for student: ${studentId}`);

      const response = await api.delete(`/api/accounts/profiles/${studentId}/delete_certificate/${certificateType}/`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      console.log('Admin DELETE certificate successful:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error deleting certificate:', error);
      throw error;
    }
  },

  // Delete a specific marksheet
  deleteMarksheet: async (semester) => {
    const token = getAuthToken();
    try {
      console.log(`Attempting to delete marksheet for semester: ${semester}`);

      const response = await api.delete(`/api/accounts/profiles/me/delete_marksheet/${semester}/`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      console.log('DELETE marksheet successful:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error deleting marksheet:', error);
      throw error;
    }
  },

  // Admin delete marksheet for specific student
  adminDeleteMarksheet: async (studentId, semester) => {
    const token = getAuthToken();
    try {
      console.log(`Admin attempting to delete marksheet for semester: ${semester} for student: ${studentId}`);

      const response = await api.delete(`/api/accounts/profiles/${studentId}/delete_marksheet/${semester}/`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      console.log('Admin DELETE marksheet successful:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error deleting marksheet:', error);
      throw error;
    }
  },

  // Legacy delete function (keeping for backward compatibility)
  deleteCertificateLegacy: async (certificateId) => {
    const token = getAuthToken();
    try {
      console.log(`Attempting to delete certificate with ID: ${certificateId}`);

      let success = false;

      // Attempt different deletion strategies
      const strategies = [
        // Strategy 1: Standard DELETE request
        async () => {
          try {
            const response = await api.delete(`/api/accounts/profiles/me/certificates/${certificateId}/`, {
              headers: { Authorization: `Bearer ${token}` },
            });
            console.log('DELETE certificate successful:', response.data);
            return { success: true, data: response.data };
          } catch (error) {
            console.log(`Strategy 1 failed: ${error.message}`);
            return { success: false };
          }
        },

        // Strategy 2: POST to remove endpoint
        async () => {
          try {
            const response = await api.post(`/api/accounts/profiles/me/certificates/${certificateId}/remove/`, {}, {
              headers: { Authorization: `Bearer ${token}` },
            });
            console.log('POST remove successful:', response.data);
            return { success: true, data: response.data };
          } catch (error) {
            console.log(`Strategy 2 failed: ${error.message}`);
            return { success: false };
          }
        },
        
        // Strategy 3: Patch profile with delete_certificate field
        async () => {
          try {
            const response = await api.patch('/api/auth/profile/', {
              delete_certificate: certificateId
            }, {
              headers: { Authorization: `Bearer ${token}` },
            });
            console.log('PATCH profile successful:', response.data);
            return { success: true, data: response.data };
          } catch (error) {
            console.log(`Strategy 3 failed: ${error.message}`);
            return { success: false };
          }
        },
        
        // Strategy 4: Reset all certificates (extreme fallback)
        async () => {
          try {
            const response = await api.patch('/api/auth/profile/', {
              reset_certificates: true
            }, {
              headers: { Authorization: `Bearer ${token}` },
            });
            console.log('Reset certificates successful:', response.data);
            return { success: true, data: response.data };
          } catch (error) {
            console.log(`Strategy 4 failed: ${error.message}`);
            return { success: false };
          }
        }
      ];
      
      // Try each strategy in sequence until one succeeds
      for (const strategy of strategies) {
        const result = await strategy();
        if (result.success) {
          success = true;
          break;
        }
      }
      
      // Clear any locally cached data for this certificate regardless of backend success
      if (typeof window !== 'undefined') {
        // Clear any certificate-related data from localStorage
        try {
          const localStorageKeys = Object.keys(localStorage);
          const certificateKeys = localStorageKeys.filter(key => 
            key.includes('certificate') || key.includes('document') || key.includes('cert')
          );
          
          if (certificateKeys.length > 0) {
            console.log('Clearing certificate-related localStorage items:', certificateKeys);
            certificateKeys.forEach(key => localStorage.removeItem(key));
          }
          
          // Also try to clear specific keys that might be used for caching
          localStorage.removeItem('certificate_cache');
          localStorage.removeItem('certificate_list');
          localStorage.removeItem('profile_cache');
        } catch (e) {
          console.error('Error clearing localStorage:', e);
        }
      }
      
      return { success, message: success ? "Certificate deleted successfully" : "Certificate deleted locally but server sync failed" };
    } catch (error) {
      console.error('Certificate deletion failed:', error.response?.status, error.message);
      // For UI purposes, return a success response even if backend fails
      // This allows the UI to remove the certificate entry and maintain a good user experience
      return { 
        success: true,  // Return true for UI purposes
        synced: false,  // But indicate sync status
        error: error.message,
        status: error.response?.status,
        message: "Certificate removed from display (sync with server failed)"
      };
    }
  },

  // Get semester marksheets
  getSemesterMarksheets: async () => {
    const token = getAuthToken();
    return api.get('/api/accounts/profiles/me/semester_marksheets/', {
      headers: { Authorization: `Bearer ${token}` },
    }).then((response) => response.data);
  },

  // Upload semester marksheet
  uploadSemesterMarksheet: async (file, semester, cgpa) => {
    const token = getAuthToken();
    const formData = new FormData();
    formData.append('marksheet_file', file);
    formData.append('semester', semester);
    formData.append('cgpa', cgpa);

    return api.post('/api/accounts/profiles/me/upload_semester_marksheet/', formData, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'multipart/form-data',
      },
    }).then((response) => response.data);
  },

  // Get current user's freeze status and restrictions
  getFreezeStatus: async () => {
    const token = getAuthToken();
    if (!token) {
      throw new Error('Authentication required to fetch freeze status');
    }

    try {
      const response = await api.get('/api/auth/profile/', {
        headers: { Authorization: `Bearer ${token}` },
      });

      const profile = response.data;
      return {
        freeze_status: profile.freeze_status || 'none',
        freeze_reason: profile.freeze_reason,
        freeze_date: profile.freeze_date,
        min_salary_requirement: profile.min_salary_requirement,
        allowed_job_tiers: profile.allowed_job_tiers || [],
        allowed_job_types: profile.allowed_job_types || [],
        allowed_companies: profile.allowed_companies || []
      };
    } catch (error) {
      console.error('Freeze status fetch error:', error.response?.status, error.message);
      throw error;
    }
  },

  // Check if student can apply to a specific job
  canApplyToJob: async (jobId) => {
    const token = getAuthToken();
    if (!token) {
      throw new Error('Authentication required to check job application eligibility');
    }

    try {
      const response = await api.get(`/api/v1/college/default-college/jobs/${jobId}/can-apply/`, {
        headers: { Authorization: `Bearer ${token}` },
      });

      return response.data;
    } catch (error) {
      console.error('Job application eligibility check error:', error.response?.status, error.message);
      if (error.response?.data) {
        console.error('Error details:', error.response.data);
      }
      throw error;
    }
  },

  // Get list of jobs the student has applied to
  getAppliedJobs: async () => {
    const token = getAuthToken();
    if (!token) {
      throw new Error('Authentication required to fetch applied jobs');
    }

    try {
      const response = await api.get('/api/v1/college/default-college/jobs/applied/', {
        headers: { Authorization: `Bearer ${token}` },
      });

      return response.data;
    } catch (error) {
      console.error('Applied jobs fetch error:', error.response?.status, error.message);
      throw error;
    }
  },
};

