(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[733],{25384:(e,s,t)=>{"use strict";t.d(s,{G_:()=>r});var a=t(95155);function r(e){let{description:s,className:t=""}=e,r=s?s.replace(/\n/g,"<br />").replace(/•/g,"•").replace(/\*\s/g,"• ").replace(/-\s/g,"• ").trim():"No description provided.";return(0,a.jsx)("div",{className:"text-gray-700 leading-relaxed ".concat(t),dangerouslySetInnerHTML:{__html:r}})}},29850:(e,s,t)=>{Promise.resolve().then(t.bind(t,33465))},33465:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>S});var a=t(95155),r=t(12115),l=t(35695),i=t(94631),n=t(85339),o=t(35169),c=t(23227),d=t(4229),x=t(54416),m=t(13717),p=t(4516),h=t(17580),u=t(69074),g=t(34869),b=t(33786),y=t(38564),j=t(17576),f=t(40646),N=t(14186),v=t(12486),w=t(92657);t(69327);var _=t(48937),A=t(85329),k=t(34842),C=t(7045);function S(e){var s,t,v,w,S,D,T,E;let{params:L}=e,F=parseInt((0,r.use)(L).id),P=(0,l.useRouter)(),[I,R]=(0,r.useState)(null),[q,M]=(0,r.useState)("jobs"),[U,z]=(0,r.useState)(0),[O,B]=(0,r.useState)(!0),[H,Y]=(0,r.useState)(null),[G,V]=(0,r.useState)(!1),[W,$]=(0,r.useState)({}),[K,Q]=(0,r.useState)(!1),[X,Z]=(0,r.useState)([]),[ee,es]=(0,r.useState)([]),[et,ea]=(0,r.useState)([]),[er,el]=(0,r.useState)(!1),[ei,en]=(0,r.useState)(null),[eo,ec]=(0,r.useState)(!1),[ed,ex]=(0,r.useState)([]);(0,r.useEffect)(()=>{em()},[F]),(0,r.useEffect)(()=>{I&&ep()},[I]);let em=async()=>{B(!0);try{let e=await (0,_.C1)(F),s=(0,_.Y_)(e.data);if(s){R(s),$(s);try{let e=await (0,_.bl)(F);z(e.data.count||0)}catch(e){console.error("Error fetching follower count:",e),z(0)}Y(null)}else Y("Company not found"),P.push("/admin/companymanagement")}catch(e){console.error("Error fetching company:",e),Y("Failed to load company data. Please try again.")}finally{B(!1)}},ep=async()=>{var e,s,t;if(I){el(!0),console.log("\uD83D\uDD0D LoadJobData called for company:",I.name,"ID:",I.id);try{let t=await (0,A.Uq)(),a=Array.isArray(t.data)?t.data:[];ex(a),console.log("\uD83D\uDCDD All forms loaded:",a.length),console.log("\uD83D\uDCDD Current company name:",I.name);let r=a.filter(e=>{let s="approved"===e.status||"posted"===e.status||e.submitted&&"rejected"!==e.status&&!e.status,t=e.company&&""!==e.company.trim(),a=!1;if(t){let s=e.company.toLowerCase().trim(),t=I.name.toLowerCase().trim();a=s===t||s.includes(t)||t.includes(s)}return console.log("\uD83D\uDCDD Form ".concat(e.key,': status="').concat(e.status,'", submitted=').concat(e.submitted,", approved=").concat(s,', company="').concat(e.company,'", targetCompany="').concat(I.name,'", hasCompany=').concat(t,", match=").concat(a)),s&&a});console.log("\uD83D\uDCDD Filtered approved forms:",r.length),Z(r);try{console.log("\uD83D\uDD0D Loading jobs from admin API...");let s=await (0,k.Om)({company_id:I.id,per_page:100});console.log("\uD83D\uDD0D Raw jobs response:",s);let t=[];(null==(e=s.data)?void 0:e.data)?(t=Array.isArray(s.data.data)?s.data.data:[],console.log("\uD83D\uDD0D Jobs from jobsResponse.data.data:",t.length)):s.data?(t=Array.isArray(s.data)?s.data:[],console.log("\uD83D\uDD0D Jobs from jobsResponse.data:",t.length)):Array.isArray(s)&&(t=s,console.log("\uD83D\uDD0D Jobs from direct array:",t.length)),console.log("\uD83D\uDD0D All jobs loaded:",t.length),t.length>0?(console.log("\uD83D\uDD0D Sample jobs data:"),t.slice(0,3).forEach((e,s)=>{console.log("  Job ".concat(s+1,":"),{id:e.id,title:e.title,company_name:e.company_name,company_id:e.company_id,is_published:e.is_published})})):console.log("⚠️ No jobs found in API response"),console.log("\uD83D\uDD0D Filtering jobs for company:",I.name,"Company ID:",I.id);let a=t.filter(e=>{let s=e.company_name&&e.company_name.toLowerCase().includes(I.name.toLowerCase()),t=e.company_id&&I.id&&parseInt(e.company_id)===parseInt(I.id),a=s||t;return(e.company_name||e.company_id)&&console.log('\uD83D\uDD0D Job "'.concat(e.title,'": company_name="').concat(e.company_name,'", company_id="').concat(e.company_id,'", matchByName=').concat(s,", matchById=").concat(t,", overall=").concat(a)),a}),r=a.filter(e=>!0===e.is_published),l=a.filter(e=>!1===e.is_published);console.log("\uD83D\uDD0D Results:"),console.log("  Total company jobs:",a.length),console.log("  Published company jobs:",r.length),console.log("  Unpublished company jobs:",l.length),es(r),ea(l)}catch(e){console.error("❌ Error loading jobs:",e),console.error("❌ Job error details:",null==(s=e.response)?void 0:s.data),es([]),ea([])}}catch(e){console.error("❌ Error loading job data:",e),console.error("❌ Error details:",null==(t=e.response)?void 0:t.data),Z([]),es([]),ea([])}finally{el(!1)}}},eh=()=>{G&&$(I),V(!G)},eu=async()=>{Q(!0);try{let e=await (0,_.JT)(F,W),s=(0,_.Y_)(e.data);R(s),V(!1),alert("Company updated successfully!")}catch(e){console.error("Error updating company:",e),alert("Failed to update company. Please try again.")}finally{Q(!1)}},eg=(e,s)=>{$(t=>({...t,[e]:s}))},eb=async e=>{try{console.log("Raw job data from form:",e);let s={title:e.title,description:e.description,location:e.location,job_type:e.job_type||"FULL_TIME",salary_min:parseFloat(e.salary_min)||0,salary_max:parseFloat(e.salary_max)||0,required_skills:(()=>{let s=Array.isArray(e.requirements)?e.requirements.filter(e=>e.trim()):[],t=Array.isArray(e.skills)?e.skills.filter(e=>e.trim()):[],a=[...s,...t];return a.length>0?a.join(", "):e.required_skills||"No specific requirements"})(),application_deadline:e.application_deadline||e.deadline,is_active:void 0===e.is_active||e.is_active,company_name:I.name};console.log("Transformed job data being sent:",s),await (0,k._S)(s),alert("Job published successfully! It will now appear in the Published section."),ec(!1),en(null),Z(e=>e.filter(e=>e.key!==ei.key)),await ep()}catch(e){var s;console.error("Error creating job:",e),console.error("Error details:",null==(s=e.response)?void 0:s.data),alert("Failed to create job posting. Please try again.")}},ey=async(e,s)=>{try{let s=await (0,k.T4)(e);console.log("Toggle response:",s),await ep()}catch(e){console.error("Error toggling job publish status:",e),alert("Failed to update job status. Please try again.")}};return O?(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(i.A,{className:"h-12 w-12 animate-spin text-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading company details..."})]})}):H?(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsxs)("div",{className:"text-center max-w-md",children:[(0,a.jsx)("div",{className:"text-red-500 mb-4",children:(0,a.jsx)(n.A,{className:"w-16 h-16 mx-auto"})}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Error Loading Company"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:H}),(0,a.jsx)("button",{onClick:()=>P.push("/admin/companymanagement"),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Return to Company Management"})]})}):(0,a.jsxs)("div",{className:"h-full overflow-y-auto",children:[(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsxs)("button",{onClick:()=>P.push("/admin/companymanagement"),className:"flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors",children:[(0,a.jsx)(o.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Back to Company Management"})]})}),(0,a.jsxs)("div",{className:"relative mb-6",children:[(0,a.jsxs)("div",{className:"h-32 bg-gradient-to-r from-blue-600 to-blue-800 relative overflow-hidden rounded-xl",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black opacity-20"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"}),(0,a.jsx)("div",{className:"absolute bottom-0 left-0 right-0 p-6",children:(0,a.jsxs)("div",{className:"flex items-end justify-between",children:[(0,a.jsxs)("div",{className:"w-16 h-16 bg-white rounded-xl shadow-lg flex items-center justify-center overflow-hidden border-4 border-white",children:[I.logo?(0,a.jsx)("img",{src:I.logo,alt:I.name,className:"w-12 h-12 rounded-lg object-cover",onError:e=>{e.target.style.display="none",e.target.nextSibling.style.display="flex"}}):null,(0,a.jsx)(c.A,{className:"w-8 h-8 text-gray-600 ".concat(I.logo?"hidden":"flex")})]}),(0,a.jsxs)("div",{className:"flex-1 text-white pb-2 ml-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4 mb-2",children:[G?(0,a.jsx)("input",{type:"text",value:W.name||"",onChange:e=>eg("name",e.target.value),className:"text-2xl font-bold bg-white/20 text-white placeholder-white/70 border border-white/30 rounded-lg px-3 py-1",placeholder:"Company Name"}):(0,a.jsx)("h1",{className:"text-2xl font-bold",children:I.name}),(0,a.jsx)("div",{className:"flex items-center gap-2",children:G?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("button",{onClick:eu,disabled:K,className:"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50",children:[K?(0,a.jsx)(i.A,{className:"w-4 h-4 animate-spin"}):(0,a.jsx)(d.A,{className:"w-4 h-4"}),K?"Saving...":"Save"]}),(0,a.jsxs)("button",{onClick:eh,className:"flex items-center gap-2 px-4 py-2 bg-white/20 text-white border border-white/30 rounded-lg hover:bg-white/30 transition-colors",children:[(0,a.jsx)(x.A,{className:"w-4 h-4"}),"Cancel"]})]}):(0,a.jsxs)("button",{onClick:eh,className:"flex items-center gap-2 px-4 py-2 bg-white/20 text-white border border-white/30 rounded-lg hover:bg-white/30 transition-colors",children:[(0,a.jsx)(m.A,{className:"w-4 h-4"}),"Edit Company"]})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4 text-white/90",children:[G?(0,a.jsx)("input",{type:"text",value:W.industry||"",onChange:e=>eg("industry",e.target.value),className:"bg-white/20 text-white placeholder-white/70 border border-white/30 rounded px-2 py-1",placeholder:"Industry"}):(0,a.jsx)("span",{children:I.industry}),(0,a.jsx)("span",{children:"•"}),(0,a.jsxs)("span",{children:[U.toLocaleString()," followers"]})]})]})]})})]}),(0,a.jsx)("div",{className:"bg-white shadow-sm border border-gray-200 rounded-lg mt-4",children:(0,a.jsx)("div",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-6 text-sm text-gray-600",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(p.A,{className:"w-4 h-4"}),G?(0,a.jsx)("input",{type:"text",value:W.location||"",onChange:e=>eg("location",e.target.value),className:"border border-gray-300 rounded px-2 py-1",placeholder:"Location"}):(0,a.jsx)("span",{children:I.location})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"w-4 h-4"}),G?(0,a.jsx)("input",{type:"text",value:W.size||"",onChange:e=>eg("size",e.target.value),className:"border border-gray-300 rounded px-2 py-1",placeholder:"Company Size"}):(0,a.jsx)("span",{children:I.size})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(u.A,{className:"w-4 h-4"}),G?(0,a.jsx)("input",{type:"text",value:W.founded||"",onChange:e=>eg("founded",e.target.value),className:"border border-gray-300 rounded px-2 py-1",placeholder:"Founded Year"}):(0,a.jsxs)("span",{children:["Founded ",I.founded]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-blue-600 hover:text-blue-800",children:[(0,a.jsx)(g.A,{className:"w-4 h-4"}),G?(0,a.jsx)("input",{type:"url",value:W.website||"",onChange:e=>eg("website",e.target.value),className:"border border-gray-300 rounded px-2 py-1",placeholder:"Website URL"}):(0,a.jsxs)("a",{href:I.website,target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-1",children:[(0,a.jsx)("span",{children:"Website"}),(0,a.jsx)(b.A,{className:"w-3 h-3"})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("div",{className:"inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium border ".concat((e=>{switch(e){case"Tier 1":return"bg-emerald-100 text-emerald-800 border-emerald-200";case"Tier 2":return"bg-blue-100 text-blue-800 border-blue-200";case"Tier 3":return"bg-purple-100 text-purple-800 border-purple-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}})(I.tier)),children:[(0,a.jsx)(y.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:I.tier})]}),I.campus_recruiting&&(0,a.jsx)("span",{className:"px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium",children:"Campus Recruiting"})]})]})})})]}),(0,a.jsx)("div",{className:"border-b border-gray-200 mb-6",children:(0,a.jsx)("nav",{className:"-mb-px flex space-x-8",children:["jobs","overview"].map(e=>(0,a.jsx)("button",{onClick:()=>M(e),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat(q===e?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:"jobs"===e?"Job Management":"Overview"},e))})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-6 mb-6",children:["overview"===q&&(0,a.jsx)("div",{className:"max-w-4xl",children:(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:["About ",I.name]}),G?(0,a.jsx)("textarea",{value:W.description||"",onChange:e=>eg("description",e.target.value),rows:"6",className:"w-full border border-gray-300 rounded-lg px-3 py-2 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Company description..."}):(0,a.jsx)("p",{className:"text-gray-700 leading-relaxed text-lg",children:I.description})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Company Metrics"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6",children:[(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-6 text-center",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-3",children:[(0,a.jsx)(j.A,{className:"w-6 h-6 text-blue-600"}),(0,a.jsx)("h4",{className:"font-semibold text-blue-900",children:"Active Jobs"})]}),(0,a.jsx)("p",{className:"text-3xl font-bold text-blue-900",children:I.totalActiveJobs||0})]}),(0,a.jsxs)("div",{className:"bg-green-50 rounded-lg p-6 text-center",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-3",children:[(0,a.jsx)(h.A,{className:"w-6 h-6 text-green-600"}),(0,a.jsx)("h4",{className:"font-semibold text-green-900",children:"Total Applicants"})]}),(0,a.jsx)("p",{className:"text-3xl font-bold text-green-900",children:I.totalApplicants||0})]}),(0,a.jsxs)("div",{className:"bg-purple-50 rounded-lg p-6 text-center",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-3",children:[(0,a.jsx)(f.A,{className:"w-6 h-6 text-purple-600"}),(0,a.jsx)("h4",{className:"font-semibold text-purple-900",children:"Total Hired"})]}),(0,a.jsx)("p",{className:"text-3xl font-bold text-purple-900",children:I.totalHired||0})]}),(0,a.jsxs)("div",{className:"bg-amber-50 rounded-lg p-6 text-center",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-3",children:[(0,a.jsx)(N.A,{className:"w-6 h-6 text-amber-600"}),(0,a.jsx)("h4",{className:"font-semibold text-amber-900",children:"Pending Review"})]}),(0,a.jsx)("p",{className:"text-3xl font-bold text-amber-900",children:I.awaited_approval||0})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Company Details"}),(0,a.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Industry"}),(0,a.jsx)("p",{className:"text-gray-700",children:I.industry})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Company Size"}),(0,a.jsx)("p",{className:"text-gray-700",children:I.size})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Founded"}),(0,a.jsx)("p",{className:"text-gray-700",children:I.founded})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Headquarters"}),(0,a.jsx)("p",{className:"text-gray-700",children:I.location})]})]})})]})]})}),"jobs"===q&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Job Management"}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("button",{onClick:ep,className:"px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors",children:"Refresh Data"}),(0,a.jsx)("button",{onClick:()=>{Z([{id:"test-123",key:"TEST-FORM",company:I.name,status:"posted",details:{description:"Test job description",skills:"React, Node.js",deadline:"2024-12-31"}}])},className:"px-3 py-1 text-sm bg-green-100 text-green-700 rounded-md hover:bg-green-200 transition-colors",children:"Add Test Form"}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Manage job postings from approved forms"})]})]}),(0,a.jsx)(J,{approvedForms:X,publishedJobs:ee,unpublishedJobs:et,loadingJobs:er,onCreateJobFromForm:e=>{en(e),ec(!0)},onPublishToggle:ey,onEditJob:e=>{P.push("/admin/jobs/edit/".concat(e))},onViewJob:e=>{P.push("/admin/jobs/".concat(e))}})]})]})]}),eo&&ei&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto m-4",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Create Job Posting"}),(0,a.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:["Based on form: ",ei.key]})]}),(0,a.jsx)("button",{onClick:()=>ec(!1),className:"p-2 hover:bg-gray-100 rounded-full",children:(0,a.jsx)(x.A,{className:"w-5 h-5"})})]})}),(0,a.jsx)("div",{className:"p-6",children:(0,a.jsx)(C.A,{companies:[I],onSubmit:eb,onCancel:()=>ec(!1),initialData:{title:(null==(s=ei.details)?void 0:s.title)||"",description:(null==(t=ei.details)?void 0:t.description)||"",location:(null==(v=ei.details)?void 0:v.location)||I.location||"",salary_min:(null==(w=ei.details)?void 0:w.salaryMin)||"",salary_max:(null==(S=ei.details)?void 0:S.salaryMax)||"",skills:(null==(D=ei.details)?void 0:D.skills)?ei.details.skills.split(",").map(e=>e.trim()):[],requirements:(null==(T=ei.details)?void 0:T.requirements)?ei.details.requirements.split(",").map(e=>e.trim()):[],application_deadline:(null==(E=ei.details)?void 0:E.deadline)||"",company_name:I.name,company_id:F}})})]})})]})}function J(e){let{approvedForms:s,publishedJobs:t,unpublishedJobs:l,loadingJobs:n,onCreateJobFromForm:o,onPublishToggle:c,onEditJob:d,onViewJob:p}=e,[h,u]=(0,r.useState)("to-publish");return n?(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsx)(i.A,{className:"h-8 w-8 animate-spin text-blue-600"})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"border-b border-gray-200",children:(0,a.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,a.jsxs)("button",{onClick:()=>u("to-publish"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("to-publish"===h?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:["To be Published (",l.length,")"]}),(0,a.jsxs)("button",{onClick:()=>u("published"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("published"===h?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:["Published (",t.length,")"]})]})}),(0,a.jsxs)("div",{className:"space-y-6",children:["to-publish"===h&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Jobs To be Published"}),0===l.length?(0,a.jsxs)("div",{className:"text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300",children:[(0,a.jsx)(j.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-500 text-lg",children:"No unpublished jobs"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Jobs created but not yet published will appear here"})]}):(0,a.jsx)("div",{className:"bg-white shadow-sm rounded-lg overflow-hidden",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Job Title"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Location"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Salary"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Deadline"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:l.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.title}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["ID: ",e.id]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.location})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.job_type})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.salary_max?"Up to $".concat(e.salary_max.toLocaleString()):"Not specified"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.application_deadline?new Date(e.application_deadline).toLocaleDateString():"Not specified"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium",children:"To be Published"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)("button",{onClick:()=>c(e.id,!1),className:"inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",children:[(0,a.jsx)(v.A,{className:"w-3 h-3 mr-1"}),"Publish"]}),(0,a.jsxs)("button",{onClick:()=>d(e.id),className:"inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,a.jsx)(m.A,{className:"w-3 h-3 mr-1"}),"Edit"]})]})})]},e.id))})]})})})]}),"published"===h&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Published Jobs"}),0===t.length?(0,a.jsxs)("div",{className:"text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300",children:[(0,a.jsx)(j.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-500 text-lg",children:"No published jobs"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Published jobs will appear here"})]}):(0,a.jsx)("div",{className:"bg-white shadow-sm rounded-lg overflow-hidden",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Job Title"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Location"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Salary"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Deadline"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:t.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.title}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["ID: ",e.id]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.location})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.job_type})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.salary_max?"Up to $".concat(e.salary_max.toLocaleString()):"Not specified"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.application_deadline?new Date(e.application_deadline).toLocaleDateString():"Not specified"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium",children:"Active"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)("button",{onClick:()=>p(e.id),className:"inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,a.jsx)(w.A,{className:"w-3 h-3 mr-1"}),"View"]}),(0,a.jsxs)("button",{onClick:()=>d(e.id),className:"inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,a.jsx)(m.A,{className:"w-3 h-3 mr-1"}),"Edit"]}),(0,a.jsxs)("button",{onClick:()=>c(e.id,!0),className:"inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:[(0,a.jsx)(x.A,{className:"w-3 h-3 mr-1"}),"Unpublish"]})]})})]},e.id))})]})})})]})]})]})}t(25384)},85329:(e,s,t)=>{"use strict";t.d(s,{DG:()=>l,Jy:()=>i,Uq:()=>r,i6:()=>o,wi:()=>n});var a=t(37719);function r(){return a.A.get("/api/v1/jobs/forms/")}function l(e){return a.A.post("/api/v1/jobs/forms/",e)}function i(e){return a.A.get("/api/v1/jobs/forms/".concat(e,"/"))}function n(e,s){return a.A.patch("/api/v1/jobs/forms/".concat(e,"/"),s)}function o(e){return a.A.post("/api/v1/jobs/forms/".concat(e,"/delete/"))}}},e=>{var s=s=>e(e.s=s);e.O(0,[3464,1044,3983,9327,4605,8441,1684,7358],()=>s(29850)),_N_E=e.O()}]);