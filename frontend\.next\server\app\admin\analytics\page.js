(()=>{var e={};e.id=1093,e.ids=[1093],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10722:(e,t,s)=>{Promise.resolve().then(s.bind(s,11527))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11527:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var r=s(60687),a=s(43210),n=s(24664),i=s(98848),l=s(9535),d=s(37325),o=s(81080),c=s(95994),m=s(80556),p=s(90910),h=s(81172),u=s(20798),x=s(58869),g=s(53411);function y({children:e}){let[t,s]=(0,a.useState)(""),y=[{items:[{title:"Dashboard",href:"/admin/dashboard",icon:(0,r.jsx)(i.A,{})},{title:"Jobs",href:"/admin/jobs",icon:(0,r.jsx)(l.A,{})},{title:"Applications",href:"/admin/applications",icon:(0,r.jsx)(d.A,{})},{title:"Posts",href:"/admin/posts",icon:(0,r.jsx)(u.A,{className:"w-5 h-5"})},{title:"Student Management",href:"/admin/student-management",icon:(0,r.jsx)(x.A,{className:"w-5 h-5"})},{title:"Student Analytics",href:"/admin/analytics",icon:(0,r.jsx)(g.A,{className:"w-5 h-5"})},{title:"Company Management",href:"/admin/companymanagement",icon:(0,r.jsx)(o.A,{})},{title:"Forms",href:"/admin/form",icon:(0,r.jsx)(c.A,{})}]}],f=[{title:"My Profile",href:"/admin/profile",icon:(0,r.jsx)(m.A,{})},{title:"Settings",href:"../settings",icon:(0,r.jsx)(p.A,{})},{title:"Contact Support",href:"/admin/helpandsupport",icon:(0,r.jsx)(h.A,{})}];return(0,r.jsx)("div",{className:"h-screen bg-gray-50 overflow-hidden",children:(0,r.jsxs)("div",{className:"flex h-full",children:[(0,r.jsx)(n.A,{sections:y,bottomItems:f,defaultExpanded:!1,navbarHeight:"0",className:"z-20"}),(0,r.jsx)("div",{className:"flex-1 p-6 ml-20 overflow-y-auto",children:(0,r.jsx)("div",{className:"bg-white rounded-xl shadow-md p-6 min-h-full text-black",children:e})})]})})}},12412:e=>{"use strict";e.exports=require("assert")},16020:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>o});var r=s(65239),a=s(48088),n=s(88170),i=s.n(n),l=s(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let o={children:["",{children:["admin",{children:["analytics",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,58473)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\analytics\\page.jsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,23697)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\layout.jsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,75535)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\layout.js"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\analytics\\page.jsx"],m={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/analytics/page",pathname:"/admin/analytics",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20798:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("megaphone",[["path",{d:"m3 11 18-5v12L3 14v-3z",key:"n962bs"}],["path",{d:"M11.6 16.8a3 3 0 1 1-5.8-1.6",key:"1yl0tm"}]])},21820:e=>{"use strict";e.exports=require("os")},23697:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\layout.jsx","default")},25541:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},27351:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("graduation-cap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]])},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37325:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var r=(0,s(6445).A)("outline","clipboard-list","IconClipboardList",[["path",{d:"M9 5h-2a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-12a2 2 0 0 0 -2 -2h-2",key:"svg-0"}],["path",{d:"M9 3m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z",key:"svg-1"}],["path",{d:"M9 12l.01 0",key:"svg-2"}],["path",{d:"M13 12l2 0",key:"svg-3"}],["path",{d:"M9 16l.01 0",key:"svg-4"}],["path",{d:"M13 16l2 0",key:"svg-5"}]])},40228:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41312:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},48173:(e,t,s)=>{Promise.resolve().then(s.bind(s,23697))},52027:(e,t,s)=>{Promise.resolve().then(s.bind(s,70345))},53411:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58473:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\analytics\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\analytics\\page.jsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70345:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var r=s(60687),a=s(43210),n=s(41312),i=s(82080),l=s(86561),d=s(27351),o=s(78122),c=s(25541),m=s(53411),p=s(40228);let h=(0,s(62688).A)("chart-pie",[["path",{d:"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z",key:"pzmjnu"}],["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}]]);var u=s(1469);let x={formatGPA:e=>e?parseFloat(e).toFixed(2):"0.00",getPerformanceCategoryColor:e=>({high_performers:"#10B981",good_performers:"#3B82F6",average_performers:"#F59E0B",poor_performers:"#EF4444"})[e]||"#6B7280",sortDepartmentsByCount:e=>[...e].sort((e,t)=>t.total_students-e.total_students),sortYearsChronologically:e=>[...e].sort((e,t)=>(e.passout_year||0)-(t.passout_year||0))},g=()=>{let[e,t]=(0,a.useState)(!0),[s,g]=(0,a.useState)(null),[y,f]=(0,a.useState)(!1),[v,j]=(0,a.useState)({enhanced:null,departments:null,years:null,performance:null}),b=async(e=!1)=>{try{t(!0),g(null);let s=await u.n0.getAllStudentAnalytics(e);s.success?(j(s.data),s.data.errors&&s.data.errors.length>0&&console.warn("Some metrics failed to load:",s.data.errors)):g(s.error||"Failed to load student analytics")}catch(e){console.error("Error fetching student analytics:",e),g("Failed to load student analytics. Please try again.")}finally{t(!1)}},N=async()=>{f(!0);try{let e=await u.n0.refreshAllMetrics();e.success?await b(!0):g(e.error||"Failed to refresh metrics")}catch(e){console.error("Error refreshing metrics:",e),g("Failed to refresh metrics. Please try again.")}finally{f(!1)}};if((0,a.useEffect)(()=>{b()},[]),e)return(0,r.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,r.jsx)("span",{className:"ml-2 text-gray-600",children:"Loading student analytics..."})]});if(s)return(0,r.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,r.jsx)("p",{className:"text-red-600",children:s}),(0,r.jsx)("button",{onClick:()=>b(),className:"mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700",children:"Try Again"})]});let{enhanced:w,departments:A,years:k,performance:_}=v,M=w?.overview||{},C=A?.departments||[],P=k?.years||[],D=[{title:"Total Students",value:M.total_students?.toLocaleString()||"0",icon:(0,r.jsx)(n.A,{className:"w-6 h-6"}),color:"bg-blue-500",description:"Registered students",trend:M.high_performer_percentage>20?"up":"down"},{title:"Departments",value:M.active_departments||"0",icon:(0,r.jsx)(i.A,{className:"w-6 h-6"}),color:"bg-green-500",description:"Active departments",trend:"up"},{title:"High Performers",value:M.high_performers?.toLocaleString()||"0",icon:(0,r.jsx)(l.A,{className:"w-6 h-6"}),color:"bg-purple-500",description:`${M.high_performer_percentage?.toFixed(1)||0}% of students`,trend:M.high_performer_percentage>25?"up":"down"},{title:"Placement Ready",value:M.placement_ready?.toLocaleString()||"0",icon:(0,r.jsx)(d.A,{className:"w-6 h-6"}),color:"bg-orange-500",description:"Current year eligible",trend:"up"}],S=x.sortDepartmentsByCount(C).slice(0,5),q=x.sortYearsChronologically(P).slice(-5);return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Student Analytics"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Comprehensive overview of student data and performance"})]}),(0,r.jsxs)("button",{onClick:N,disabled:y,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50",children:[(0,r.jsx)(o.A,{className:`w-4 h-4 mr-2 ${y?"animate-spin":""}`}),y?"Refreshing...":"Refresh Data"]})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:D.map((e,t)=>(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e.title}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.value}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:e.description})]}),(0,r.jsx)("div",{className:`${e.color} p-3 rounded-lg text-white`,children:e.icon})]}),(0,r.jsxs)("div",{className:"mt-4 flex items-center",children:["up"===e.trend?(0,r.jsx)(c.A,{className:"w-4 h-4 text-green-500 mr-1"}):(0,r.jsx)(c.A,{className:"w-4 h-4 text-red-500 mr-1 transform rotate-180"}),(0,r.jsx)("span",{className:`text-sm ${"up"===e.trend?"text-green-600":"text-red-600"}`,children:"Performance indicator"})]})]},t))}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Top Departments"}),(0,r.jsx)(m.A,{className:"w-5 h-5 text-gray-400"})]}),(0,r.jsx)("div",{className:"space-y-4",children:S.map((e,t)=>(0,r.jsx)("div",{className:"flex items-center justify-between",children:(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e.branch||"Unknown Department"}),(0,r.jsxs)("span",{className:"text-sm text-gray-600",children:[e.total_students," students"]})]}),(0,r.jsx)("div",{className:"mt-1 bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-blue-500 h-2 rounded-full",style:{width:`${e.total_students/(S[0]?.total_students||1)*100}%`}})}),(0,r.jsxs)("div",{className:"mt-1 flex justify-between text-xs text-gray-500",children:[(0,r.jsxs)("span",{children:["Avg GPA: ",x.formatGPA(e.avg_gpa)]}),(0,r.jsxs)("span",{children:["High Performers: ",e.high_performers||0]})]})]})},t))})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Year-wise Distribution"}),(0,r.jsx)(p.A,{className:"w-5 h-5 text-gray-400"})]}),(0,r.jsx)("div",{className:"space-y-4",children:q.map((e,t)=>(0,r.jsx)("div",{className:"flex items-center justify-between",children:(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e.passout_year||"Unknown Year"}),(0,r.jsxs)("span",{className:"text-sm text-gray-600",children:[e.total_students," students"]})]}),(0,r.jsx)("div",{className:"mt-1 bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-green-500 h-2 rounded-full",style:{width:`${e.total_students/(q[0]?.total_students||1)*100}%`}})}),(0,r.jsxs)("div",{className:"mt-1 flex justify-between text-xs text-gray-500",children:[(0,r.jsxs)("span",{children:["Avg GPA: ",x.formatGPA(e.avg_gpa)]}),(0,r.jsxs)("span",{children:["Placement Rate: ",e.placement_rate?.toFixed(1)||0,"%"]})]})]})},t))})]})]}),_&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Performance Analytics"}),(0,r.jsx)(h,{className:"w-5 h-5 text-gray-400"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:Object.entries(_.performance_categories||{}).map(([e,t],s)=>(0,r.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:t}),(0,r.jsx)("div",{className:"text-sm text-gray-600 capitalize",children:e.replace("_"," ")}),(0,r.jsx)("div",{className:"mt-2 h-2 bg-gray-200 rounded",children:(0,r.jsx)("div",{className:"h-2 rounded",style:{width:`${t/(_.overall_performance?.total_students||1)*100}%`,backgroundColor:x.getPerformanceCategoryColor(e)}})})]},e))})]}),(0,r.jsxs)("div",{className:"text-center text-sm text-gray-500",children:["Last updated: ",w?.last_updated?new Date(w.last_updated).toLocaleString():"Unknown"]})]})};function y(){return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsx)(g,{})})})}},74075:e=>{"use strict";e.exports=require("zlib")},78122:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},79551:e=>{"use strict";e.exports=require("url")},81172:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var r=(0,s(6445).A)("outline","help","IconHelp",[["path",{d:"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0",key:"svg-0"}],["path",{d:"M12 17l0 .01",key:"svg-1"}],["path",{d:"M12 13.5a1.5 1.5 0 0 1 1 -1.5a2.6 2.6 0 1 0 -3 -4",key:"svg-2"}]])},81630:e=>{"use strict";e.exports=require("http")},82080:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},83997:e=>{"use strict";e.exports=require("tty")},86371:(e,t,s)=>{Promise.resolve().then(s.bind(s,58473))},86561:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},94735:e=>{"use strict";e.exports=require("events")},95994:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var r=(0,s(6445).A)("outline","forms","IconForms",[["path",{d:"M12 3a3 3 0 0 0 -3 3v12a3 3 0 0 0 3 3",key:"svg-0"}],["path",{d:"M6 3a3 3 0 0 1 3 3v12a3 3 0 0 1 -3 3",key:"svg-1"}],["path",{d:"M13 7h7a1 1 0 0 1 1 1v8a1 1 0 0 1 -1 1h-7",key:"svg-2"}],["path",{d:"M5 7h-1a1 1 0 0 0 -1 1v8a1 1 0 0 0 1 1h1",key:"svg-3"}],["path",{d:"M17 12h.01",key:"svg-4"}],["path",{d:"M13 12h.01",key:"svg-5"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,681,1658,1060,2305,5956],()=>s(16020));module.exports=r})();