exports.id=6728,exports.ids=[6728],exports.modules={10722:(e,t,a)=>{Promise.resolve().then(a.bind(a,11527))},11527:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>y});var r=a(60687),o=a(43210),s=a(24664),n=a(98848),i=a(9535),c=a(37325),l=a(81080),d=a(95994),p=a(80556),u=a(90910),g=a(81172),h=a(20798),m=a(58869),f=a(53411);function y({children:e}){let[t,a]=(0,o.useState)(""),y=[{items:[{title:"Dashboard",href:"/admin/dashboard",icon:(0,r.jsx)(n.A,{})},{title:"Jobs",href:"/admin/jobs",icon:(0,r.jsx)(i.A,{})},{title:"Applications",href:"/admin/applications",icon:(0,r.jsx)(c.A,{})},{title:"Posts",href:"/admin/posts",icon:(0,r.jsx)(h.A,{className:"w-5 h-5"})},{title:"Student Management",href:"/admin/student-management",icon:(0,r.jsx)(m.A,{className:"w-5 h-5"})},{title:"Student Analytics",href:"/admin/analytics",icon:(0,r.jsx)(f.A,{className:"w-5 h-5"})},{title:"Company Management",href:"/admin/companymanagement",icon:(0,r.jsx)(l.A,{})},{title:"Forms",href:"/admin/form",icon:(0,r.jsx)(d.A,{})}]}],w=[{title:"My Profile",href:"/admin/profile",icon:(0,r.jsx)(p.A,{})},{title:"Settings",href:"../settings",icon:(0,r.jsx)(u.A,{})},{title:"Contact Support",href:"/admin/helpandsupport",icon:(0,r.jsx)(g.A,{})}];return(0,r.jsx)("div",{className:"h-screen bg-gray-50 overflow-hidden",children:(0,r.jsxs)("div",{className:"flex h-full",children:[(0,r.jsx)(s.A,{sections:y,bottomItems:w,defaultExpanded:!1,navbarHeight:"0",className:"z-20"}),(0,r.jsx)("div",{className:"flex-1 p-6 ml-20 overflow-y-auto",children:(0,r.jsx)("div",{className:"bg-white rounded-xl shadow-md p-6 min-h-full text-black",children:e})})]})})}},23697:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\layout.jsx","default")},28885:(e,t,a)=>{"use strict";a.d(t,{M$:()=>c,yp:()=>i});var r=a(51060);let o="http://localhost:5001/api",s=r.A.create({baseURL:o,headers:{"Content-Type":"application/json"},withCredentials:!1});s.interceptors.request.use(e=>{let t=localStorage.getItem("token");return t&&(e.headers.Authorization=`Bearer ${t}`),e}),s.interceptors.response.use(e=>(console.group("Help & Support API Response"),console.log("URL:",e.config.url),console.log("Method:",e.config.method?.toUpperCase()),console.log("Status:",e.status),console.log("Response Data:",e.data),console.groupEnd(),e),e=>{throw e.response&&401===e.response.status&&console.warn("Authentication failed. You may need to log in again."),console.error("Help & Support API Error:",e),e});let n=async(e,t)=>{try{let a=new FormData;a.append("username",e),a.append("password",t);let{access_token:s,user:n}=(await r.A.post(`${o}/auth/login`,a,{headers:{"Content-Type":"application/x-www-form-urlencoded"}})).data;return localStorage.setItem("token",s),localStorage.setItem("user",JSON.stringify(n)),{token:s,user:n}}catch(e){throw console.error("Login failed:",e),alert("Login failed: "+(e.response?.data?.message||"Invalid credentials")),e}},i={createTicket:async e=>{try{return(await s.post("/tickets",e)).data}catch(e){throw console.error("Error creating ticket:",e),e}},getTickets:async(e={})=>{try{let t=new URLSearchParams;e.page&&t.append("page",e.page),e.page_size&&t.append("limit",e.page_size),e.skip&&t.append("skip",e.skip),e.status&&"all"!==e.status&&t.append("status",e.status.replace("-","_")),e.search&&t.append("search",e.search),e.category&&t.append("category",e.category),e.priority&&t.append("priority",e.priority),e.user_id&&t.append("user_id",e.user_id);let a=t.toString(),r=`/tickets${a?`?${a}`:""}`;return(await s.get(r)).data}catch(e){throw console.error("Error fetching tickets:",e),e}},getTicket:async e=>{try{let t=(await s.get(`/tickets/${e}`)).data;return{...t,createdBy:t.created_by||t.createdBy,assignedTo:t.assigned_to||t.assignedTo,createdAt:new Date(t.created_at||t.createdAt),updatedAt:new Date(t.updated_at||t.updatedAt)}}catch(t){throw console.error(`Error fetching ticket ${e}:`,t),t}},updateTicket:async(e,t)=>{try{let a={...t};return a.status&&(a.status=a.status.replace("-","_")),(await s.put(`/tickets/${e}`,a)).data}catch(t){throw console.error(`Error updating ticket ${e}:`,t),t}},addComment:async(e,t)=>{try{return(await s.post(`/tickets/${e}/comments`,{content:t})).data}catch(t){throw console.error(`Error adding comment to ticket ${e}:`,t),t}},getComments:async(e,t={})=>{try{let a=new URLSearchParams;t.page&&a.append("page",t.page),t.page_size&&a.append("limit",t.page_size),t.skip&&a.append("skip",t.skip);let r=a.toString(),o=`/tickets/${e}/comments${r?`?${r}`:""}`;return(await s.get(o)).data}catch(t){throw console.error(`Error fetching comments for ticket ${e}:`,t),t}},addAttachment:async(e,t)=>{try{let a=new FormData;return a.append("file",t),(await s.post(`/tickets/${e}/attachments`,a,{headers:{"Content-Type":"multipart/form-data"}})).data}catch(t){throw console.error(`Error adding attachment to ticket ${e}:`,t),t}},submitFeedback:async(e,t)=>{try{return(await s.post(`/tickets/${e}/feedback`,t)).data}catch(t){throw console.error(`Error submitting feedback for ticket ${e}:`,t),t}},checkFeedback:async e=>{try{return(await s.get(`/tickets/${e}/feedback`)).data}catch(t){if(t.response&&404===t.response.status)return null;throw console.error(`Error checking feedback for ticket ${e}:`,t),t}},getAllFeedback:async()=>{try{return(await s.get("/feedback")).data}catch(e){throw console.error("Error fetching all feedback:",e),e}}},c={defaultCredentials:{email:"<EMAIL>",password:"admin123"},login:async(e=null,t=null)=>{let a=e||c.defaultCredentials.email,r=t||c.defaultCredentials.password;return console.log(`Attempting login with: ${a}`),n(a,r)},autoLogin:async()=>{try{console.log("Auto-logging in with default credentials...");let e=await c.login();return console.log("Auto-login successful"),e}catch(e){throw console.error("Auto-login failed:",e),e}},logout:()=>{localStorage.removeItem("token"),localStorage.removeItem("user")},getProfile:async()=>(await s.get("/auth/me")).data,ensureLoggedIn:async()=>c.autoLogin()};c.ensureLoggedIn().catch(e=>console.warn("Initial auto-login failed, will retry when API is used"))},48173:(e,t,a)=>{Promise.resolve().then(a.bind(a,23697))},70440:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>o});var r=a(31658);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},76717:(e,t,a)=>{"use strict";a.d(t,{$:()=>n});var r=a(60687),o=a(43210),s=a.n(o);let n=({children:e,variant:t="default",size:a="default",className:o="",asChild:n=!1,...i})=>{let c={default:"bg-blue-600 text-white hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700",outline:"border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200",ghost:"hover:bg-gray-100 hover:text-gray-900",link:"text-blue-600 underline-offset-4 hover:underline"},l={default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"},d=c[t]||c.default,p=l[a]||l.default,u=`inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 ${d} ${p} ${o}`;return n?s().cloneElement(e,{className:u,...i}):(0,r.jsx)("button",{className:u,...i,children:e})}},97299:(e,t,a)=>{"use strict";a.d(t,{BT:()=>i,Wu:()=>c,ZB:()=>n,Zp:()=>o,aR:()=>s});var r=a(60687);a(43210);let o=({children:e,className:t="",...a})=>(0,r.jsx)("div",{className:`bg-white rounded-lg border border-gray-200 shadow-sm ${t}`,...a,children:e}),s=({children:e,className:t="",...a})=>(0,r.jsx)("div",{className:`p-6 pb-4 ${t}`,...a,children:e}),n=({children:e,className:t="",...a})=>(0,r.jsx)("h3",{className:`text-lg font-semibold leading-none tracking-tight ${t}`,...a,children:e}),i=({children:e,className:t="",...a})=>(0,r.jsx)("p",{className:`text-sm text-gray-600 mt-1.5 ${t}`,...a,children:e}),c=({children:e,className:t="",...a})=>(0,r.jsx)("div",{className:`p-6 pt-0 ${t}`,...a,children:e})}};