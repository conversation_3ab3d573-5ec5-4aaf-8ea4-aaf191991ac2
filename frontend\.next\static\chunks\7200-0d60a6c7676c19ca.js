"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7200],{1243:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},17580:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},19946:(e,t,a)=>{a.d(t,{A:()=>u});var s=a(12115);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,a)=>a?a.toUpperCase():t.toLowerCase()),n=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return t.filter((e,t,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===t).join(" ").trim()},c=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,s.forwardRef)((e,t)=>{let{color:a="currentColor",size:r=24,strokeWidth:l=2,absoluteStrokeWidth:n,className:d="",children:u,iconNode:m,...x}=e;return(0,s.createElement)("svg",{ref:t,...o,width:r,height:r,stroke:a,strokeWidth:n?24*Number(l)/Number(r):l,className:i("lucide",d),...!u&&!c(x)&&{"aria-hidden":"true"},...x},[...m.map(e=>{let[t,a]=e;return(0,s.createElement)(t,a)}),...Array.isArray(u)?u:[u]])}),u=(e,t)=>{let a=(0,s.forwardRef)((a,l)=>{let{className:c,...o}=a;return(0,s.createElement)(d,{ref:l,iconNode:t,className:i("lucide-".concat(r(n(e))),"lucide-".concat(e),c),...o})});return a.displayName=n(e),a}},23861:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(19946).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},28496:(e,t,a)=>{a.r(t),a.d(t,{default:()=>b});var s=a(95155),r=a(12115),l=a(71007),n=a(23861),i=a(19946);let c=(0,i.A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var o=a(32919);let d=(0,i.A)("shield-alert",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"M12 8v4",key:"1got3b"}],["path",{d:"M12 16h.01",key:"1drbdi"}]]);var u=a(17580),m=a(29869),x=a(62098),h=a(93509),p=a(35695),f=a(72476),y=a(69327),g=a(44382);let b=()=>{let e=(0,p.useRouter)(),t=(0,p.usePathname)(),{theme:a,changeTheme:i}=(0,g.D)(),[b,v]=(0,r.useState)(null),[j,N]=(0,r.useState)(!1),[w,k]=(0,r.useState)(!1),[A,S]=(0,r.useState)(""),[M,C]=(0,r.useState)(""),[_,P]=(0,r.useState)(""),[E,z]=(0,r.useState)(""),[R,U]=(0,r.useState)(!0),[L,D]=(0,r.useState)(!0),[F,T]=(0,r.useState)("medium"),[q,I]=(0,r.useState)(""),[V,Y]=(0,r.useState)(""),[H,W]=(0,r.useState)(""),[Z,B]=(0,r.useState)({maintenanceMode:!1,allowNewRegistrations:!0,defaultUserRole:"student"}),[O,$]=(0,r.useState)("profile"),[G,J]=(0,r.useState)(!1),[K,Q]=(0,r.useState)({type:"",text:""});(0,r.useEffect)(()=>{(async()=>{if(f.c4()){N(!0);try{var t;let e=await y.Nt.getUserData();v(e);let a="ADMIN"===e.user_type||"admin"===e.role||(null==(t=e.user)?void 0:t.role)==="admin";k(a),"undefined"!=typeof document&&(document.cookie="role=".concat(e.user_type||"STUDENT","; path=/; max-age=86400"))}catch(t){console.error("Error fetching user data:",t),e.push("/login")}}else e.push("/login")})()},[e,t]),(0,r.useEffect)(()=>{if(j&&b){var e,t;S(b.first_name&&b.last_name?"".concat(b.first_name," ").concat(b.last_name):b.name||""),C(b.contact_email||b.email||(null==(e=b.user)?void 0:e.email)||""),P(b.profile_image_url||b.avatar||""),z(b.user_type||b.role||(null==(t=b.user)?void 0:t.role)||"student"),b.notification_preferences&&(U(!1!==b.notification_preferences.email),D(!1!==b.notification_preferences.browser))}},[j,b]),(0,r.useEffect)(()=>{{let e=localStorage.getItem("userFontSize");e&&(T(e),document.documentElement.setAttribute("data-font-size",e))}},[]);let X=async e=>{e.preventDefault(),J(!0),Q({type:"",text:""});try{let e=A.trim().split(" "),t=e[0]||"",a=e.slice(1).join(" ")||"";await y.Nt.updateUserProfile({first_name:t,last_name:a,contact_email:M,profile_image_url:_}),Q({type:"success",text:"Your profile information has been updated successfully."})}catch(e){var t,a;Q({type:"error",text:(null==(a=e.response)||null==(t=a.data)?void 0:t.message)||e.message||"Failed to update profile. Please try again."})}finally{J(!1)}},ee=async e=>{e.preventDefault(),J(!0),Q({type:"",text:""});try{await y.Nt.updateUserProfile({notification_preferences:{email:R,browser:L}}),Q({type:"success",text:"Your notification settings have been saved."})}catch(e){var t,a;Q({type:"error",text:(null==(a=e.response)||null==(t=a.data)?void 0:t.message)||e.message||"Failed to update notification preferences."})}finally{J(!1)}},et=async e=>{if(e.preventDefault(),J(!0),Q({type:"",text:""}),V!==H){Q({type:"error",text:"New passwords do not match."}),J(!1);return}if(V.length<6){Q({type:"error",text:"New password must be at least 6 characters long."}),J(!1);return}try{let e=await y.Nt.updateUserPassword({current_password:q,new_password:V,confirm_password:H});Q({type:"success",text:e.message||"Your password has been changed successfully."}),I(""),Y(""),W("")}catch(e){var t;if(console.error("Password update error:",e),null==(t=e.response)?void 0:t.data){let t=e.response.data,a="";Q({type:"error",text:t.current_password?t.current_password[0]:t.new_password?t.new_password[0]:t.confirm_password?t.confirm_password[0]:t.detail?t.detail:t.message?t.message:"Failed to update password. Please try again."})}else Q({type:"error",text:"Failed to update password. Please try again."})}finally{J(!1)}},ea=async e=>{e.preventDefault(),J(!0),Q({type:"",text:""});try{w&&(await y.Er.updateSystemSettings(Z),Q({type:"success",text:"System settings have been updated successfully."}))}catch(e){Q({type:"error",text:e.message||"Failed to update system settings. Please try again."})}finally{J(!1)}};return(0,s.jsx)("div",{className:"p-6 bg-gray-50 min-h-screen",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Account Settings"}),K.text&&(0,s.jsx)("div",{className:"mb-6 p-4 rounded-lg ".concat("success"===K.type?"bg-green-50 text-green-800 border border-green-200":"error"===K.type?"bg-red-50 text-red-800 border border-red-200":""),children:K.text}),(0,s.jsx)("div",{className:"bg-white shadow-sm rounded-lg mb-6",children:(0,s.jsxs)("div",{className:"flex border-b overflow-x-auto",children:[(0,s.jsxs)("button",{className:"px-6 py-4 font-medium text-sm flex items-center gap-2 settings-tab ".concat("profile"===O?"text-blue-600 border-b-2 border-blue-600 active":"text-gray-600 hover:text-gray-900"),onClick:()=>$("profile"),children:[(0,s.jsx)(l.A,{className:"w-4 h-4"}),"Profile"]}),(0,s.jsxs)("button",{className:"px-6 py-4 font-medium text-sm flex items-center gap-2 settings-tab ".concat("notifications"===O?"text-blue-600 border-b-2 border-blue-600 active":"text-gray-600 hover:text-gray-900"),onClick:()=>$("notifications"),children:[(0,s.jsx)(n.A,{className:"w-4 h-4"}),"Notifications"]}),(0,s.jsxs)("button",{className:"px-6 py-4 font-medium text-sm flex items-center gap-2 settings-tab ".concat("appearance"===O?"text-blue-600 border-b-2 border-blue-600 active":"text-gray-600 hover:text-gray-900"),onClick:()=>$("appearance"),children:[(0,s.jsx)(c,{className:"w-4 h-4"}),"Appearance"]}),(0,s.jsxs)("button",{className:"px-6 py-4 font-medium text-sm flex items-center gap-2 settings-tab ".concat("security"===O?"text-blue-600 border-b-2 border-blue-600 active":"text-gray-600 hover:text-gray-900"),onClick:()=>$("security"),children:[(0,s.jsx)(o.A,{className:"w-4 h-4"}),"Security"]}),w&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("button",{className:"px-6 py-4 font-medium text-sm flex items-center gap-2 settings-tab ".concat("system"===O?"text-blue-600 border-b-2 border-blue-600 active":"text-gray-600 hover:text-gray-900"),onClick:()=>$("system"),children:[(0,s.jsx)(d,{className:"w-4 h-4"}),"System"]}),(0,s.jsxs)("button",{className:"px-6 py-4 font-medium text-sm flex items-center gap-2 settings-tab ".concat("users"===O?"text-blue-600 border-b-2 border-blue-600 active":"text-gray-600 hover:text-gray-900"),onClick:()=>$("users"),children:[(0,s.jsx)(u.A,{className:"w-4 h-4"}),"User Management"]})]})]})}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:["profile"===O&&(0,s.jsx)("form",{onSubmit:X,children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-1",children:"Profile Settings"}),(0,s.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"Update your personal information"})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-6 mb-6",children:[(0,s.jsx)("div",{className:"w-24 h-24 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden",children:_?(0,s.jsx)("img",{src:_,alt:A,className:"w-full h-full object-cover"}):(0,s.jsx)("span",{className:"text-2xl font-bold text-gray-400",children:A.charAt(0)})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:A||"Your Name"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:E}),(0,s.jsxs)("button",{type:"button",className:"px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg text-sm font-medium flex items-center gap-2",children:[(0,s.jsx)(m.A,{className:"w-4 h-4"}),"Change avatar"]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Full Name"}),(0,s.jsx)("input",{type:"text",value:A,onChange:e=>S(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Email Address"}),(0,s.jsx)("input",{type:"email",value:M,onChange:e=>C(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Role"}),(0,s.jsx)("input",{type:"text",value:E,disabled:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 cursor-not-allowed"})]})]})}),"notifications"===O&&(0,s.jsx)("form",{onSubmit:ee,children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-1",children:"Notification Preferences"}),(0,s.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"Configure how you want to receive notifications"})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between py-3 border-b border-gray-200",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium text-gray-900",children:"Email Notifications"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Receive email notifications for important updates"})]}),(0,s.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,s.jsx)("input",{type:"checkbox",checked:R,onChange:()=>U(!R),className:"sr-only peer"}),(0,s.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between py-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium text-gray-900",children:"Browser Notifications"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Receive push notifications in your browser"})]}),(0,s.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,s.jsx)("input",{type:"checkbox",checked:L,onChange:()=>D(!L),className:"sr-only peer"}),(0,s.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]})]}),(0,s.jsx)("div",{className:"pt-4",children:(0,s.jsx)("button",{type:"submit",disabled:G,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:G?"Saving...":"Save Preferences"})})]})}),"appearance"===O&&(0,s.jsx)("form",{onSubmit:e=>{e.preventDefault(),J(!0);try{localStorage.setItem("userFontSize",F),document.documentElement.setAttribute("data-font-size",F),document.body.className=document.body.className.replace(/font-\w+/g,""),document.body.classList.add("font-".concat(F)),Q({type:"success",text:"Your appearance preferences have been saved and applied."})}catch(e){Q({type:"error",text:"Failed to save appearance preferences."})}finally{J(!1)}},children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-1",children:"Appearance Settings"}),(0,s.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"Customize your visual experience"})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Theme"}),(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,s.jsx)("div",{className:"p-4 border ".concat("light"===a?"border-blue-500 bg-blue-50":"border-gray-200"," rounded-lg cursor-pointer hover:bg-gray-50"),onClick:()=>i("light"),children:(0,s.jsxs)("div",{className:"flex flex-col items-center",children:[(0,s.jsx)(x.A,{className:"w-6 h-6 text-gray-700 mb-2"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"Light"})]})}),(0,s.jsx)("div",{className:"p-4 border ".concat("dark"===a?"border-blue-500 bg-blue-50":"border-gray-200"," rounded-lg cursor-pointer hover:bg-gray-50"),onClick:()=>i("dark"),children:(0,s.jsxs)("div",{className:"flex flex-col items-center",children:[(0,s.jsx)(h.A,{className:"w-6 h-6 text-gray-700 mb-2"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"Dark"})]})}),(0,s.jsx)("div",{className:"p-4 border ".concat("system"===a?"border-blue-500 bg-blue-50":"border-gray-200"," rounded-lg cursor-pointer hover:bg-gray-50"),onClick:()=>i("system"),children:(0,s.jsxs)("div",{className:"flex flex-col items-center",children:[(0,s.jsx)(c,{className:"w-6 h-6 text-gray-700 mb-2"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"System"})]})})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Font Size"}),(0,s.jsxs)("select",{value:F,onChange:e=>T(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"small",children:"Small"}),(0,s.jsx)("option",{value:"medium",children:"Medium"}),(0,s.jsx)("option",{value:"large",children:"Large"})]})]})]}),(0,s.jsx)("div",{className:"pt-4",children:(0,s.jsx)("button",{type:"submit",disabled:G,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:G?"Saving...":"Save Preferences"})})]})}),"security"===O&&(0,s.jsx)("form",{onSubmit:et,children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-1",children:"Security Settings"}),(0,s.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"Update your password and security preferences"})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Current Password"}),(0,s.jsx)("input",{type:"password",value:q,onChange:e=>I(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"New Password"}),(0,s.jsx)("input",{type:"password",value:V,onChange:e=>Y(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Confirm New Password"}),(0,s.jsx)("input",{type:"password",value:H,onChange:e=>W(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),(0,s.jsx)("div",{className:"pt-4",children:(0,s.jsx)("button",{type:"submit",disabled:G,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:G?"Updating...":"Update Password"})})]})}),w&&"system"===O&&(0,s.jsx)("form",{onSubmit:ea,children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-1",children:"System Settings"}),(0,s.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"Configure global system settings"})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between py-3 border-b border-gray-200",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium text-gray-900",children:"Maintenance Mode"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Enable to put the site in maintenance mode"})]}),(0,s.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,s.jsx)("input",{type:"checkbox",checked:Z.maintenanceMode,onChange:()=>B({...Z,maintenanceMode:!Z.maintenanceMode}),className:"sr-only peer"}),(0,s.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between py-3 border-b border-gray-200",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium text-gray-900",children:"Allow New Registrations"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Enable to allow new users to register"})]}),(0,s.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,s.jsx)("input",{type:"checkbox",checked:Z.allowNewRegistrations,onChange:()=>B({...Z,allowNewRegistrations:!Z.allowNewRegistrations}),className:"sr-only peer"}),(0,s.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Default User Role"}),(0,s.jsxs)("select",{value:Z.defaultUserRole,onChange:e=>B({...Z,defaultUserRole:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"student",children:"Student"}),(0,s.jsx)("option",{value:"teacher",children:"Teacher"}),(0,s.jsx)("option",{value:"admin",children:"Admin"})]})]})]}),(0,s.jsx)("div",{className:"pt-4",children:(0,s.jsx)("button",{type:"submit",disabled:G,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:G?"Saving...":"Save System Settings"})})]})}),w&&"users"===O&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-1",children:"User Management"}),(0,s.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"Manage users and permissions"})]}),(0,s.jsx)("p",{className:"text-gray-700",children:"This panel allows you to manage users. For full user management functionality, please visit the admin dashboard."}),(0,s.jsx)("div",{className:"pt-4",children:(0,s.jsx)("a",{href:"/admin/users",className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors inline-block",children:"Go to User Management"})})]})]})]})})}},29869:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(19946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},32919:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(19946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},33786:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(19946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},35695:(e,t,a)=>{var s=a(18999);a.o(s,"useParams")&&a.d(t,{useParams:function(){return s.useParams}}),a.o(s,"usePathname")&&a.d(t,{usePathname:function(){return s.usePathname}}),a.o(s,"useRouter")&&a.d(t,{useRouter:function(){return s.useRouter}}),a.o(s,"useSearchParams")&&a.d(t,{useSearchParams:function(){return s.useSearchParams}})},40646:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},44382:(e,t,a)=>{a.d(t,{D:()=>n,N:()=>i});var s=a(95155),r=a(12115);let l=(0,r.createContext)(),n=()=>{let e=(0,r.useContext)(l);if(!e)throw Error("useTheme must be used within a ThemeProvider");return e},i=e=>{let{children:t}=e,[a,n]=(0,r.useState)("system"),[i,c]=(0,r.useState)("light");return(0,r.useEffect)(()=>{n(localStorage.getItem("userTheme")||"system")},[]),(0,r.useEffect)(()=>{{let e=e=>{let t=document.documentElement,a=document.body;a.classList.remove("dark-mode","light-mode"),t.removeAttribute("data-theme");let s=e;"system"===e&&(s=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),"dark"===s?(a.classList.add("dark-mode"),t.setAttribute("data-theme","dark")):(a.classList.add("light-mode"),t.setAttribute("data-theme","light")),c(s)};if(e(a),"system"===a){let t=window.matchMedia("(prefers-color-scheme: dark)"),a=t=>{e("system")};return t.addEventListener("change",a),()=>t.removeEventListener("change",a)}}},[a]),(0,s.jsx)(l.Provider,{value:{theme:a,resolvedTheme:i,changeTheme:e=>{n(e),localStorage.setItem("userTheme",e)},isDark:"dark"===i,isLight:"light"===i},children:t})}},54416:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},62098:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(19946).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},71007:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},72476:(e,t,a)=>{a.d(t,{O5:()=>r,c4:()=>s,fE:()=>l}),a(37719);let s=()=>localStorage.getItem("access_token"),r=e=>{localStorage.setItem("access_token",e)},l=e=>{localStorage.setItem("refresh_token",e)}},75525:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(19946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},76517:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(19946).A)("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]])},81284:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(19946).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},85339:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},93509:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(19946).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])}}]);