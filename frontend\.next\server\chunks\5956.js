"use strict";exports.id=5956,exports.ids=[5956],exports.modules={1469:(t,e,a)=>{a.d(e,{Er:()=>c,Nt:()=>o,SY:()=>n,n0:()=>i});var r=a(58138);async function s(t,e={}){try{let a,s,n,o=new URLSearchParams;e.page&&o.append("page",e.page),e.page_size&&o.append("per_page",e.page_size),Object.keys(e).forEach(t=>{"page"!==t&&"page_size"!==t&&e[t]&&o.append(t,e[t])});let c=`${t}?${o.toString()}`,i=await r.A.get(c);if(i.data.data&&i.data.pagination)a=i.data.data,s={current_page:i.data.pagination.current_page,total_pages:i.data.pagination.total_pages,total_count:i.data.pagination.total_count,per_page:i.data.pagination.per_page,has_next:i.data.pagination.has_next,has_previous:i.data.pagination.has_previous},n=i.data.metadata||{};else if(void 0!==i.data.results){if(a=i.data.results,i.data.pagination)s={current_page:i.data.pagination.page||e.page||1,total_pages:i.data.pagination.total_pages||1,total_count:i.data.pagination.total_count||0,per_page:i.data.pagination.page_size||e.page_size||10,has_next:i.data.pagination.has_next||!1,has_previous:i.data.pagination.has_previous||!1};else{let t=i.data.count||0,a=e.page_size||10,r=e.page||1,n=Math.ceil(t/a);s={current_page:r,total_pages:n,total_count:t,per_page:a,has_next:!!i.data.next,has_previous:!!i.data.previous}}n=i.data.metadata||{}}else a=i.data,s={current_page:e.page||1,total_pages:1,total_count:Array.isArray(a)?a.length:0,per_page:e.page_size||10,has_next:!1,has_previous:!1},n={};return{success:!0,data:a||[],pagination:s,metadata:n}}catch(e){throw console.error(`Error fetching paginated data from ${t}:`,e),e}}let n={async getCompanies(t={}){try{return await s("/api/v1/companies/optimized/",t)}catch(e){console.log("Optimized endpoint failed, trying fallback endpoints...");try{let{fetchCompanies:e}=await a.e(4335).then(a.bind(a,14335)),r=await e();if(t.search){let e=t.search.toLowerCase();r=r.filter(t=>t.name.toLowerCase().includes(e)||t.industry.toLowerCase().includes(e)||t.description.toLowerCase().includes(e))}t.tier&&"ALL"!==t.tier&&(r=r.filter(e=>e.tier===t.tier)),t.industry&&"ALL"!==t.industry&&(r=r.filter(e=>e.industry===t.industry)),t.ordering&&r.sort((e,a)=>{switch(t.ordering){case"name":return e.name.localeCompare(a.name);case"-total_active_jobs":return(a.totalActiveJobs||0)-(e.totalActiveJobs||0);case"-total_applicants":return(a.totalApplicants||0)-(e.totalApplicants||0);case"tier":return e.tier.localeCompare(a.tier);default:return 0}});let s=t.page||1,n=t.page_size||10,o=(s-1)*n,c=r.slice(o,o+n),i=r.length,l=Math.ceil(i/n);return{success:!0,data:c,pagination:{current_page:s,total_pages:l,total_count:i,per_page:n,has_next:s<l,has_previous:s>1},metadata:{}}}catch(t){throw console.error("All company endpoints failed:",t),t}}},async getCompanyStats(t=!1){try{return(await r.A.get("/api/v1/companies/stats/")).data}catch(t){for(let e of(console.error("Error fetching company stats from primary endpoint:",t),["/api/v1/stats/companies/","/api/v1/jobs/stats/"]))try{let t=await r.A.get(e);if(t.data)return console.log(`Successfully fetched stats from ${e}`),t.data}catch(t){console.log(`Failed to fetch from ${e}: ${t.message}`)}return console.log("Falling back to calculated stats from companies data"),this.calculateStatsFromCompanies()}},async calculateStatsFromCompanies(){try{let{fetchCompanies:t}=await a.e(4335).then(a.bind(a,14335)),e=await t();return{total:e.length,active_jobs:e.reduce((t,e)=>t+(e.totalActiveJobs||0),0),campus_recruiting:e.filter(t=>t.campus_recruiting).length,tier1:e.filter(t=>"Tier 1"===t.tier).length,tier2:e.filter(t=>"Tier 2"===t.tier).length,tier3:e.filter(t=>"Tier 3"===t.tier).length}}catch(t){return console.error("Failed to calculate stats from companies:",t),{total:0,active_jobs:0,campus_recruiting:0,tier1:0,tier2:0,tier3:0}}},async searchCompanies(t,e={},a=1,r=20){let s={search:t,page:a,page_size:r,...e};return this.getCompanies(s)}},o={async getStudents(t={}){try{let e={...t},a=!0===t.count_only;a&&(e.per_page=1e3),t.page_size&&(e.per_page=t.page_size,delete e.page_size),e.count_only&&delete e.count_only;let r=await s("/api/accounts/students/optimized/",e);if(a&&!r.metadata?.year_counts&&r.data){let t={};r.data.forEach(e=>{let a=e.passout_year||e.graduation_year;a&&(t[a]=(t[a]||0)+1)}),r.metadata||(r.metadata={}),r.metadata.year_counts=t}return{success:!0,data:r.data||[],pagination:{current_page:r.pagination?.current_page||t.page||1,total_pages:r.pagination?.total_pages||1,total_count:r.pagination?.total_count||0,per_page:r.pagination?.per_page||t.page_size||10,has_next:r.pagination?.has_next||!1,has_previous:r.pagination?.has_previous||!1},metadata:r.metadata||{}}}catch(e){console.error("Error in studentsAPI.getStudents:",e);try{return await s("/api/accounts/students/",t)}catch(t){throw console.error("Fallback endpoint also failed:",t),t}}},async getStudentStats(t=!1){try{return(await r.A.get("/api/accounts/students/stats/")).data}catch(t){return console.error("Error fetching student stats:",t),{total:0,active:0,graduated:0,placed:0}}},async getDepartmentStats(t=!1){try{return(await r.A.get("/api/accounts/departments/stats/")).data}catch(t){return console.error("Error fetching department stats:",t),{total:0,departments:[]}}},async searchStudents(t,e={},a=1,r=20){let s={search:t,page:a,page_size:r,...e};return this.getStudents(s)},async getStudent(t){try{return(await r.A.get(`/api/accounts/students/${t}/`)).data}catch(t){throw console.error("Error fetching student:",t),t}},async updateStudent(t,e){try{console.log("updateStudent called with:",{id:t,data:e});let a={...e};["joining_year","passout_year"].forEach(t=>{if(null!==a[t]&&void 0!==a[t]){let e=parseInt(a[t]);a[t]=isNaN(e)?null:e}}),["first_name","last_name","student_id","contact_email","phone","branch","gpa","date_of_birth","address","city","district","state","pincode","country","parent_contact","education","skills","tenth_cgpa","tenth_percentage","tenth_board","tenth_school","tenth_year_of_passing","tenth_location","tenth_specialization","twelfth_cgpa","twelfth_percentage","twelfth_board","twelfth_school","twelfth_year_of_passing","twelfth_location","twelfth_specialization"].forEach(t=>{null!==a[t]&&void 0!==a[t]&&(a[t]=String(a[t]).trim())}),Object.keys(a).forEach(t=>{void 0===a[t]&&delete a[t]}),console.log("Cleaned data being sent:",a);try{console.log("Trying ViewSet endpoint:",`/api/accounts/profiles/${t}/`);let e=await r.A.patch(`/api/accounts/profiles/${t}/`,a);return console.log("ViewSet endpoint success:",e.data),e.data}catch(e){console.error("ViewSet endpoint failed:",{status:e.response?.status,statusText:e.response?.statusText,data:e.response?.data});try{console.log("Trying fallback endpoint:",`/api/accounts/students/${t}/update/`);let e=await r.A.patch(`/api/accounts/students/${t}/update/`,a);return console.log("Fallback endpoint success:",e.data),e.data}catch(t){throw console.error("Failed to update student via both endpoints:",{viewSetError:{status:e.response?.status,data:e.response?.data},updateViewError:{status:t.response?.status,data:t.response?.data}}),t.response?.status===400?t:e}}}catch(t){throw console.error("Error updating student:",t),t}},async uploadResume(t){try{let e=new FormData;return e.append("resume",t),(await r.A.patch("/api/auth/profile/",e,{headers:{"Content-Type":"multipart/form-data"}})).data}catch(t){throw console.error("Error uploading resume:",t),t}},async deleteResume(t){try{return(await r.A.delete(`/api/accounts/resumes/${t}/`)).data}catch(t){throw console.error("Error deleting resume:",t),t}},async uploadCertificate(t,e){try{let a=new FormData;return a.append(`${e}_certificate`,t),(await r.A.patch("/api/auth/profile/",a,{headers:{"Content-Type":"multipart/form-data"}})).data}catch(t){throw console.error("Error uploading certificate:",t),t}},async uploadSemesterMarksheet(t,e){try{let a=new FormData;return a.append("marksheet",t),a.append("semester",e),(await r.A.post("/api/accounts/semester-marksheets/",a,{headers:{"Content-Type":"multipart/form-data"}})).data}catch(t){throw console.error("Error uploading semester marksheet:",t),t}},async getUserData(){try{return(await r.A.get("/api/auth/profile/")).data}catch(t){throw console.error("Error fetching user data:",t),t}},async updateUserProfile(t){try{return(await r.A.patch("/api/auth/profile/",t)).data}catch(t){throw console.error("Error updating user profile:",t),t}},async updateUserPassword(t){try{return(await r.A.post("/api/auth/change-password/",t)).data}catch(t){throw console.error("Password update error:",t),t}}},c={async updateSystemSettings(t){try{return(await r.A.post("/api/admin/system-settings/",t)).data}catch(t){throw console.error("Error updating system settings:",t),t}},async getSystemSettings(){try{return(await r.A.get("/api/admin/system-settings/")).data}catch(t){throw console.error("Error fetching system settings:",t),t}}},i={getEnhancedStudentMetrics:async(t="enhanced_student_stats",e=!1)=>{try{let a={type:t};e&&(a.refresh="true");let s=await r.A.get("/api/v1/metrics/students/enhanced/",{params:a});return{success:!0,data:s.data}}catch(t){return console.error("Error fetching enhanced student metrics:",t),{success:!1,error:t.response?.data?.message||"Failed to fetch enhanced student metrics"}}},getDepartmentStats:async(t=null,e=!1)=>{try{let a={};t&&(a.department=t),e&&(a.refresh="true");let s=await r.A.get("/api/v1/metrics/students/departments/",{params:a});return{success:!0,data:s.data}}catch(t){return console.error("Error fetching department stats:",t),{success:!1,error:t.response?.data?.message||"Failed to fetch department statistics"}}},getYearStats:async(t=null,e=!1,a=null)=>{try{let s={};t&&(s.year=t),a&&(s.department=a),e&&(s.refresh="true");let n=await r.A.get("/api/v1/metrics/students/years/",{params:s});return{success:!0,data:n.data}}catch(e){console.error("Error fetching year stats:",e);let t={years:[],current_year:new Date().getFullYear(),department_filter:a||null,last_updated:new Date().toISOString(),error_fallback:!0};if(e.response?.status===500)return console.warn("Server error for year stats, using fallback data"),{success:!0,data:t,fallback:!0};return{success:!1,error:e.response?.data?.message||"Failed to fetch year statistics",fallbackData:t}}},getPerformanceAnalytics:async()=>{try{let t=await r.A.get("/api/v1/metrics/students/performance/");return{success:!0,data:t.data}}catch(t){return console.error("Error fetching performance analytics:",t),{success:!1,error:t.response?.data?.message||"Failed to fetch performance analytics"}}},getAllStudentAnalytics:async(t=!1)=>{try{let[e,a,r,s]=await Promise.allSettled([i.getEnhancedStudentMetrics("enhanced_student_stats",t),i.getDepartmentStats(null,t),i.getYearStats(null,t),i.getPerformanceAnalytics()]),n={enhanced:"fulfilled"===e.status?e.value.data:null,departments:"fulfilled"===a.status?a.value.data:null,years:"fulfilled"===r.status?r.value.data:null,performance:"fulfilled"===s.status?s.value.data:null,errors:[]};return[e,a,r,s].forEach((t,e)=>{let a=["enhanced","departments","years","performance"][e];"rejected"===t.status?n.errors.push(`${a}: ${t.reason.message||"Unknown error"}`):t.value?.fallback?n.errors.push(`${a}: Using fallback data due to server error`):t.value&&!t.value.success&&t.value.fallbackData&&(n[a]=t.value.fallbackData,n.errors.push(`${a}: ${t.value.error||"API error"}, using fallback`))}),n.enhanced||(n.enhanced={overview:{},last_updated:new Date().toISOString()}),n.departments||(n.departments={departments:[],last_updated:new Date().toISOString()}),n.years||(n.years={years:[],current_year:new Date().getFullYear(),last_updated:new Date().toISOString()}),n.performance||(n.performance={performance_categories:{},last_updated:new Date().toISOString()}),{success:!0,data:n,last_updated:new Date().toISOString(),has_errors:n.errors.length>0}}catch(t){return console.error("Error fetching all student analytics:",t),{success:!0,data:{enhanced:{overview:{},last_updated:new Date().toISOString()},departments:{departments:[],last_updated:new Date().toISOString()},years:{years:[],current_year:new Date().getFullYear(),last_updated:new Date().toISOString()},performance:{performance_categories:{},last_updated:new Date().toISOString()},errors:["Failed to fetch student analytics data - using fallback"]},last_updated:new Date().toISOString(),fallback:!0,has_errors:!0}}},refreshAllMetrics:async()=>{try{let t=[i.getEnhancedStudentMetrics("enhanced_student_stats",!0),i.getDepartmentStats(null,!0),i.getYearStats(null,!0)];return await Promise.all(t),{success:!0,message:"All student metrics refreshed successfully"}}catch(t){return console.error("Error refreshing student metrics:",t),{success:!1,error:"Failed to refresh student metrics"}}}}},58138:(t,e,a)=>{a.d(e,{A:()=>n});var r=a(51060);a(51421);let s=r.A.create({baseURL:"http://127.0.0.1:8000",headers:{"Content-Type":"application/json"}});s.interceptors.request.use(t=>{let e=localStorage.getItem("access_token");return e&&(t.headers.Authorization=`Bearer ${e}`),t},t=>Promise.reject(t)),s.interceptors.response.use(t=>t,async t=>{let e=t.config;if(t.response?.status===401&&!e._retry){e._retry=!0;try{let t=localStorage.getItem("refresh_token");if(t){let a=await r.A.post("http://127.0.0.1:8000/api/auth/token/refresh/",{refresh:t});return localStorage.setItem("access_token",a.data.access),e.headers.Authorization=`Bearer ${a.data.access}`,s(e)}}catch(t){console.error("Error refreshing token:",t)}}return Promise.reject(t)});let n=s},70440:(t,e,a)=>{a.r(e),a.d(e,{default:()=>s});var r=a(31658);let s=async t=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await t.params,"favicon.ico")+""}]}};