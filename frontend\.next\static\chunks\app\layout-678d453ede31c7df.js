(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{1243:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},15259:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var a=(0,s(86467).A)("outline","building","IconBuilding",[["path",{d:"M3 21l18 0",key:"svg-0"}],["path",{d:"M9 8l1 0",key:"svg-1"}],["path",{d:"M9 12l1 0",key:"svg-2"}],["path",{d:"M9 16l1 0",key:"svg-3"}],["path",{d:"M14 8l1 0",key:"svg-4"}],["path",{d:"M14 12l1 0",key:"svg-5"}],["path",{d:"M14 16l1 0",key:"svg-6"}],["path",{d:"M5 21v-16a2 2 0 0 1 2 -2h10a2 2 0 0 1 2 2v16",key:"svg-7"}]])},29869:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},30347:()=>{},33786:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},40646:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},44049:(e,t,s)=>{Promise.resolve().then(s.bind(s,78458))},44382:(e,t,s)=>{"use strict";s.d(t,{D:()=>n,N:()=>i});var a=s(95155),l=s(12115);let r=(0,l.createContext)(),n=()=>{let e=(0,l.useContext)(r);if(!e)throw Error("useTheme must be used within a ThemeProvider");return e},i=e=>{let{children:t}=e,[s,n]=(0,l.useState)("system"),[i,c]=(0,l.useState)("light");return(0,l.useEffect)(()=>{n(localStorage.getItem("userTheme")||"system")},[]),(0,l.useEffect)(()=>{{let e=e=>{let t=document.documentElement,s=document.body;s.classList.remove("dark-mode","light-mode"),t.removeAttribute("data-theme");let a=e;"system"===e&&(a=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),"dark"===a?(s.classList.add("dark-mode"),t.setAttribute("data-theme","dark")):(s.classList.add("light-mode"),t.setAttribute("data-theme","light")),c(a)};if(e(s),"system"===s){let t=window.matchMedia("(prefers-color-scheme: dark)"),s=t=>{e("system")};return t.addEventListener("change",s),()=>t.removeEventListener("change",s)}}},[s]),(0,a.jsx)(r.Provider,{value:{theme:s,resolvedTheme:i,changeTheme:e=>{n(e),localStorage.setItem("userTheme",e)},isDark:"dark"===i,isLight:"light"===i},children:t})}},54416:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},62098:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},75525:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},76517:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]])},78458:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>C});var a=s(95155);s(30347);var l=s(12115),r=s(35695),n=s(95370),i=s(7194),c=s(876),o=s(86467),d=(0,o.A)("outline","chevron-right","IconChevronRight",[["path",{d:"M9 6l6 6l-6 6",key:"svg-0"}]]),h=s(44382),m=s(62098),u=s(93509);let x=(0,s(19946).A)("monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]]),p=()=>{let{theme:e,changeTheme:t,isDark:s}=(0,h.D)(),[r,n]=(0,l.useState)(!1),i=(0,l.useRef)(null),c=[{value:"light",label:"Light",icon:m.A},{value:"dark",label:"Dark",icon:u.A},{value:"system",label:"System",icon:x}],o=c.find(t=>t.value===e);(0,l.useEffect)(()=>{let e=e=>{i.current&&!i.current.contains(e.target)&&n(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let d=e=>{t(e),n(!1)};return(0,a.jsxs)("div",{className:"relative",ref:i,children:[(0,a.jsx)("button",{onClick:()=>n(!r),className:"\n          p-2 rounded-lg border transition-colors duration-200\n          ".concat(s?"bg-gray-800 border-gray-600 text-gray-200 hover:bg-gray-700":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50","\n          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1\n        "),title:"Current theme: ".concat(null==o?void 0:o.label),children:o&&(0,a.jsx)(o.icon,{className:"w-5 h-5"})}),r&&(0,a.jsx)("div",{className:"\n          absolute right-0 mt-2 w-32 rounded-lg shadow-lg border z-50\n          ".concat(s?"bg-gray-800 border-gray-600":"bg-white border-gray-200","\n        "),children:(0,a.jsx)("div",{className:"py-1",children:c.map(t=>{let l=t.icon,r=e===t.value;return(0,a.jsxs)("button",{onClick:()=>d(t.value),className:"\n                    w-full px-3 py-2 text-left text-sm flex items-center gap-2 transition-colors duration-150\n                    ".concat(r?s?"bg-gray-700 text-blue-400":"bg-blue-50 text-blue-600":s?"text-gray-200 hover:bg-gray-700":"text-gray-700 hover:bg-gray-50","\n                  "),children:[(0,a.jsx)(l,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:t.label}),r&&(0,a.jsx)("span",{className:"ml-auto text-xs",children:"✓"})]},t.value)})})})]})};function g(){let[e,t]=(0,l.useState)(!1),[s,n]=(0,l.useState)(null),o=(0,l.useRef)(null),h=(0,r.useRouter)(),[m,u]=(0,l.useState)(null);(0,l.useEffect)(()=>{u(localStorage.getItem("role"))},[]);let x=[{title:"Profile",icon:i.A,hasSubmenu:!0,submenu:[{title:"My Profile",href:"ADMIN"===m?"/admin/profile":"/profile"},{title:"Edit Profile",href:"/profile/edit"},{title:"Account Settings",href:"/profile/settings"},{title:"Privacy Settings",href:"/profile/privacy"}]},{title:"Settings",icon:c.A,hasSubmenu:!0,submenu:[{title:"Theme",component:"theme"},{title:"Notification Preferences",href:"/settings/notifications"},{title:"Language",href:"/settings/language"},{title:"Data & Privacy",href:"/settings/privacy"}]},{title:"Help Center",href:"/help"},{title:"Terms of Service",href:"/terms"}];(0,l.useEffect)(()=>{let e=e=>{o.current&&!o.current.contains(e.target)&&(t(!1),n(null))};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let g=e=>{n(s===e?null:e)};return(0,a.jsxs)("div",{className:"relative flex items-center gap-3",ref:o,children:[(0,a.jsx)("span",{className:"text-black font-medium",children:"Student Career Center"}),(0,a.jsx)("button",{onClick:()=>t(!e),className:"text-blue-600 hover:text-blue-700 p-2 rounded-full hover:bg-blue-50 transition-colors",children:(0,a.jsx)(i.A,{size:24})}),e&&(0,a.jsxs)("div",{className:"absolute right-0 top-full mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50",children:[x.map((e,t)=>(0,a.jsx)("div",{className:"relative",children:e.hasSubmenu?(0,a.jsxs)("div",{children:[(0,a.jsxs)("button",{onClick:()=>g(t),className:"w-full flex items-center justify-between px-4 py-2 text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[e.icon&&(0,a.jsx)(e.icon,{size:18}),(0,a.jsx)("span",{children:e.title})]}),(0,a.jsx)(d,{size:16,className:"transform transition-transform duration-200 ".concat(s===t?"rotate-90":"")})]}),s===t&&(0,a.jsx)("div",{className:"ml-4 border-l border-gray-200 pl-4 py-2",children:e.submenu.map((e,t)=>(0,a.jsx)("div",{children:"theme"===e.component?(0,a.jsx)("div",{className:"px-2 py-2",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Theme"}),(0,a.jsx)(p,{})]})}):(0,a.jsx)("a",{href:e.href,className:"block px-2 py-2 text-sm text-gray-600 hover:text-blue-600 hover:bg-blue-25 rounded transition-all duration-200",children:e.title})},t))})]}):(0,a.jsx)("a",{href:e.href,className:"block px-4 py-2 text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200",children:e.title})},t)),(0,a.jsx)("hr",{className:"my-2 border-gray-200"}),(0,a.jsx)("button",{onClick:()=>{localStorage.removeItem("userEmail"),localStorage.removeItem("collegeName"),localStorage.removeItem("role"),document.cookie="role=; path=/; max-age=0",h.push("/login")},className:"w-full text-left px-4 py-2 text-red-600 hover:bg-red-50 transition-all duration-200",children:"Logout"})]})]})}var y=s(81110),f=s(33631),v=(0,o.A)("outline","compass","IconCompass",[["path",{d:"M8 16l2 -6l6 -2l-2 6l-6 2",key:"svg-0"}],["path",{d:"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0",key:"svg-1"}],["path",{d:"M12 3l0 2",key:"svg-2"}],["path",{d:"M12 19l0 2",key:"svg-3"}],["path",{d:"M3 12l2 0",key:"svg-4"}],["path",{d:"M19 12l2 0",key:"svg-5"}]]),b=s(61062),j=s(15259),k=(0,o.A)("outline","users","IconUsers",[["path",{d:"M9 7m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0",key:"svg-0"}],["path",{d:"M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2",key:"svg-1"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"svg-2"}],["path",{d:"M21 21v-2a4 4 0 0 0 -3 -3.85",key:"svg-3"}]]),N=(0,o.A)("outline","calendar-event","IconCalendarEvent",[["path",{d:"M4 5m0 2a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2z",key:"svg-0"}],["path",{d:"M16 3l0 4",key:"svg-1"}],["path",{d:"M8 3l0 4",key:"svg-2"}],["path",{d:"M4 11l16 0",key:"svg-3"}],["path",{d:"M8 15h2v2h-2z",key:"svg-4"}]]);let w=[{items:[{title:"My Campus",href:"/",icon:(0,a.jsx)(y.A,{})},{title:"My Jobs",href:"/myjobs",icon:(0,a.jsx)(f.A,{})},{title:"Explore",href:"/explore",icon:(0,a.jsx)(v,{})},{title:"Inbox",href:"/inbox",icon:(0,a.jsx)(b.A,{})}]},{items:[{title:"Job Postings",href:"/jobpostings",icon:(0,a.jsx)(j.A,{})},{title:"Companies",href:"/companies",icon:(0,a.jsx)(k,{})},{title:"Events",href:"/events",icon:(0,a.jsx)(N,{})},{title:"Calendar",href:"/calendar",icon:(0,a.jsx)(N,{})},{title:"My Profile",href:"/profile",icon:(0,a.jsx)(i.A,{})},{title:"Settings",href:"/settings",icon:(0,a.jsx)(c.A,{})}]}];var A=s(73983);let M=e=>e.startsWith("/company/")?"Company Profile":e.startsWith("/admin/")?"Admin Dashboard":({"/":"My Campus","/myjobs":"My Jobs","/explore":"Explore","/inbox":"Inbox","/jobpostings":"Job Postings","/companies":"Companies","/events":"Events","/calendar":"Calendar","/admin":"Admin Dashboard","/admin/posts":"Admin Posts"})[e]||"PlaceEasy",E=e=>e.startsWith("/admin"),P=e=>["/login","/signup","/onboarding"].includes(e);function C(e){let{children:t}=e,s=(0,r.usePathname)(),[i,c]=(0,l.useState)(""),[o,d]=(0,l.useState)(!1);if((0,l.useEffect)(()=>{d(!0);let e=localStorage.getItem("collegeName");e&&c(e)},[]),!o)return(0,a.jsxs)("html",{lang:"en",children:[(0,a.jsxs)("head",{children:[(0,a.jsx)("title",{children:"PlaceEasy - Campus Placement Platform"}),(0,a.jsx)("meta",{name:"description",content:"Your comprehensive campus placement and career management platform"})]}),(0,a.jsx)("body",{className:"h-screen overflow-hidden",children:(0,a.jsx)(h.N,{children:(0,a.jsx)(A.ph,{children:(0,a.jsx)("main",{className:"h-full bg-white text-black",children:t})})})})]});let m=M(s),u=P(s),x=E(s);return u?(0,a.jsxs)("html",{lang:"en",children:[(0,a.jsxs)("head",{children:[(0,a.jsx)("title",{children:"PlaceEasy - Campus Placement Platform"}),(0,a.jsx)("meta",{name:"description",content:"Your comprehensive campus placement and career management platform"})]}),(0,a.jsx)("body",{className:"h-screen overflow-hidden",children:(0,a.jsx)(h.N,{children:(0,a.jsx)(A.ph,{children:(0,a.jsx)("main",{className:"h-full bg-white text-black",children:t})})})})]}):x?(0,a.jsxs)("html",{lang:"en",children:[(0,a.jsxs)("head",{children:[(0,a.jsx)("title",{children:"PlaceEasy - Campus Placement Platform"}),(0,a.jsx)("meta",{name:"description",content:"Your comprehensive campus placement and career management platform"})]}),(0,a.jsx)("body",{className:"h-screen overflow-hidden",children:(0,a.jsx)(h.N,{children:(0,a.jsx)(A.ph,{children:(0,a.jsxs)("div",{className:"h-screen bg-gray-50",children:[(0,a.jsxs)("div",{className:"fixed w-full flex justify-between items-center py-4 bg-white shadow-sm z-10",children:[(0,a.jsx)("span",{className:"text-gray-700 font-medium text-xl ml-28",children:m}),(0,a.jsxs)("div",{className:"flex items-center gap-4 mr-6",children:[(0,a.jsx)(p,{}),(0,a.jsx)(g,{})]})]}),(0,a.jsx)("div",{className:"pt-16 h-[calc(100vh-4rem)]",children:t})]})})})})]}):(0,a.jsxs)("html",{lang:"en",children:[(0,a.jsxs)("head",{children:[(0,a.jsx)("title",{children:"PlaceEasy - Campus Placement Platform"}),(0,a.jsx)("meta",{name:"description",content:"Your comprehensive campus placement and career management platform"})]}),(0,a.jsx)("body",{className:"h-screen overflow-hidden",children:(0,a.jsx)(h.N,{children:(0,a.jsx)(A.ph,{children:(0,a.jsxs)("div",{className:"h-screen bg-gray-50",children:[(0,a.jsxs)("div",{className:"fixed w-full flex justify-between items-center py-4 bg-white shadow-sm z-10",children:[(0,a.jsx)("span",{className:"text-gray-700 font-medium text-xl ml-16 sm:ml-28",children:i||"AVV Chennai"}),(0,a.jsxs)("div",{className:"flex items-center gap-4 mr-6",children:[(0,a.jsx)(p,{}),(0,a.jsx)(g,{})]})]}),(0,a.jsx)(n.A,{sections:w,defaultExpanded:!1,navbarHeight:"4rem",className:"z-20"}),(0,a.jsx)("div",{className:"flex pt-16 h-[calc(100vh-4rem)]",children:(0,a.jsx)("div",{className:"flex-1 p-6 ml-0 md:ml-20 h-full overflow-auto",children:(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-md p-6 min-h-[800px] text-black content-card",children:t})})})]})})})})]})}},81284:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},85339:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},93509:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},95370:(e,t,s)=>{"use strict";s.d(t,{A:()=>p});var a=s(95155),l=s(12115),r=s(76408),n=s(60760);s(35695);var i=s(6874),c=s.n(i),o=s(54395),d=s(34382),h=s(52596),m=s(39688);function u(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,m.QP)((0,h.$)(t))}let x=(0,l.createContext)();function p(e){let{sections:t=[],bottomItems:s=[],defaultExpanded:i=!1,navbarHeight:h="4rem",className:m}=e,[p,g]=(0,l.useState)(i),[y,f]=(0,l.useState)(!1),[v,b]=(0,l.useState)(!1);(0,l.useEffect)(()=>{let e=()=>{f(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);let j=e=>l.isValidElement(e)?l.cloneElement(e,{className:"w-7 h-7",size:void 0}):e;return(0,a.jsxs)(a.Fragment,{children:[y&&(0,a.jsx)("button",{onClick:()=>b(!v),className:"fixed top-4 left-4 z-50 p-2 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow md:hidden",style:{marginTop:h},children:v?(0,a.jsx)(o.A,{size:24}):(0,a.jsx)(d.A,{size:24})}),(0,a.jsx)(x.Provider,{value:{expanded:p},children:(0,a.jsxs)(r.P.div,{animate:{width:p?"300px":"80px"},onMouseEnter:()=>g(!0),onMouseLeave:()=>g(!1),className:u("fixed top-0 left-0 h-screen bg-white px-4 py-4 shadow-lg rounded-r-3xl flex flex-col justify-between sidebar",y&&!v?"hidden":"",m),style:{marginTop:h},children:[(0,a.jsxs)("nav",{className:"flex flex-col gap-8",children:[(0,a.jsxs)(c(),{href:"/",className:u("flex items-center gap-4 p-3 text-black",!p&&"justify-center"),children:[(0,a.jsx)("div",{className:"flex-shrink-0 text-2xl font-bold",children:p?null:"P"}),p&&(0,a.jsx)(r.P.span,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"text-2xl font-bold whitespace-nowrap",children:"Placeeasy.in"})]}),t.map((e,t)=>(0,a.jsx)("div",{className:"bg-gray-50 rounded-xl p-2",children:(0,a.jsx)(n.N,{children:e.items.map(e=>(0,a.jsxs)(c(),{href:e.href,className:u("flex items-center gap-4 p-3 text-gray-600 hover:bg-blue-50 hover:text-blue-600 rounded-xl transition-colors",!p&&"justify-center"),children:[(0,a.jsx)("div",{className:"flex-shrink-0 flex items-center justify-center",children:j(e.icon)}),p&&(0,a.jsx)(r.P.span,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"text-base font-bold whitespace-nowrap",children:e.title})]},e.title))})},t))]}),s.length>0&&(0,a.jsx)("div",{className:"mt-6",children:s.map(e=>(0,a.jsxs)(c(),{href:e.href,className:u("flex items-center gap-4 p-3 text-gray-600 hover:bg-blue-50 hover:text-blue-600 rounded-xl transition-colors mb-2",!p&&"justify-center"),children:[(0,a.jsx)("div",{className:"flex-shrink-0 flex items-center justify-center",children:j(e.icon)}),p&&(0,a.jsx)(r.P.span,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"text-base font-bold whitespace-nowrap",children:e.title})]},e.title))})]})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[7690,6874,4734,3983,8441,1684,7358],()=>t(44049)),_N_E=e.O()}]);