(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3381],{19946:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var a=r(12115);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),s=e=>{let t=n(e);return t.charAt(0).toUpperCase()+t.slice(1)},c=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},i=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,a.forwardRef)((e,t)=>{let{color:r="currentColor",size:o=24,strokeWidth:n=2,absoluteStrokeWidth:s,className:d="",children:u,iconNode:h,...p}=e;return(0,a.createElement)("svg",{ref:t,...l,width:o,height:o,stroke:r,strokeWidth:s?24*Number(n)/Number(o):n,className:c("lucide",d),...!u&&!i(p)&&{"aria-hidden":"true"},...p},[...h.map(e=>{let[t,r]=e;return(0,a.createElement)(t,r)}),...Array.isArray(u)?u:[u]])}),u=(e,t)=>{let r=(0,a.forwardRef)((r,n)=>{let{className:i,...l}=r;return(0,a.createElement)(d,{ref:n,iconNode:t,className:c("lucide-".concat(o(s(e))),"lucide-".concat(e),i),...l})});return r.displayName=s(e),r}},35695:(e,t,r)=>{"use strict";var a=r(18999);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},37533:(e,t,r)=>{"use strict";r.d(t,{M$:()=>i,yp:()=>c});var a=r(23464);let o="http://localhost:5001/api",n=a.A.create({baseURL:o,headers:{"Content-Type":"application/json"},withCredentials:!1});n.interceptors.request.use(e=>{let t=localStorage.getItem("token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e}),n.interceptors.response.use(e=>{var t;return console.group("Help & Support API Response"),console.log("URL:",e.config.url),console.log("Method:",null==(t=e.config.method)?void 0:t.toUpperCase()),console.log("Status:",e.status),console.log("Response Data:",e.data),console.groupEnd(),e},e=>{throw e.response&&401===e.response.status&&console.warn("Authentication failed. You may need to log in again."),console.error("Help & Support API Error:",e),e});let s=async(e,t)=>{try{let r=new FormData;r.append("username",e),r.append("password",t);let{access_token:n,user:s}=(await a.A.post("".concat(o,"/auth/login"),r,{headers:{"Content-Type":"application/x-www-form-urlencoded"}})).data;return localStorage.setItem("token",n),localStorage.setItem("user",JSON.stringify(s)),{token:n,user:s}}catch(e){var r,n;throw console.error("Login failed:",e),alert("Login failed: "+((null==(n=e.response)||null==(r=n.data)?void 0:r.message)||"Invalid credentials")),e}},c={createTicket:async e=>{try{return(await n.post("/tickets",e)).data}catch(e){throw console.error("Error creating ticket:",e),e}},getTickets:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{let t=new URLSearchParams;e.page&&t.append("page",e.page),e.page_size&&t.append("limit",e.page_size),e.skip&&t.append("skip",e.skip),e.status&&"all"!==e.status&&t.append("status",e.status.replace("-","_")),e.search&&t.append("search",e.search),e.category&&t.append("category",e.category),e.priority&&t.append("priority",e.priority),e.user_id&&t.append("user_id",e.user_id);let r=t.toString();return(await n.get("/tickets".concat(r?"?".concat(r):""))).data}catch(e){throw console.error("Error fetching tickets:",e),e}},getTicket:async e=>{try{let t=(await n.get("/tickets/".concat(e))).data;return{...t,createdBy:t.created_by||t.createdBy,assignedTo:t.assigned_to||t.assignedTo,createdAt:new Date(t.created_at||t.createdAt),updatedAt:new Date(t.updated_at||t.updatedAt)}}catch(t){throw console.error("Error fetching ticket ".concat(e,":"),t),t}},updateTicket:async(e,t)=>{try{let r={...t};return r.status&&(r.status=r.status.replace("-","_")),(await n.put("/tickets/".concat(e),r)).data}catch(t){throw console.error("Error updating ticket ".concat(e,":"),t),t}},addComment:async(e,t)=>{try{return(await n.post("/tickets/".concat(e,"/comments"),{content:t})).data}catch(t){throw console.error("Error adding comment to ticket ".concat(e,":"),t),t}},getComments:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let r=new URLSearchParams;t.page&&r.append("page",t.page),t.page_size&&r.append("limit",t.page_size),t.skip&&r.append("skip",t.skip);let a=r.toString(),o="/tickets/".concat(e,"/comments").concat(a?"?".concat(a):"");return(await n.get(o)).data}catch(t){throw console.error("Error fetching comments for ticket ".concat(e,":"),t),t}},addAttachment:async(e,t)=>{try{let r=new FormData;return r.append("file",t),(await n.post("/tickets/".concat(e,"/attachments"),r,{headers:{"Content-Type":"multipart/form-data"}})).data}catch(t){throw console.error("Error adding attachment to ticket ".concat(e,":"),t),t}},submitFeedback:async(e,t)=>{try{return(await n.post("/tickets/".concat(e,"/feedback"),t)).data}catch(t){throw console.error("Error submitting feedback for ticket ".concat(e,":"),t),t}},checkFeedback:async e=>{try{return(await n.get("/tickets/".concat(e,"/feedback"))).data}catch(t){if(t.response&&404===t.response.status)return null;throw console.error("Error checking feedback for ticket ".concat(e,":"),t),t}},getAllFeedback:async()=>{try{return(await n.get("/feedback")).data}catch(e){throw console.error("Error fetching all feedback:",e),e}}},i={defaultCredentials:{email:"<EMAIL>",password:"admin123"},login:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=e||i.defaultCredentials.email,a=t||i.defaultCredentials.password;return console.log("Attempting login with: ".concat(r)),s(r,a)},autoLogin:async()=>{try{console.log("Auto-logging in with default credentials...");let e=await i.login();return console.log("Auto-login successful"),e}catch(e){throw console.error("Auto-login failed:",e),e}},logout:()=>{localStorage.removeItem("token"),localStorage.removeItem("user")},getProfile:async()=>(await n.get("/auth/me")).data,ensureLoggedIn:async()=>i.autoLogin()};i.ensureLoggedIn().catch(e=>console.warn("Initial auto-login failed, will retry when API is used"))},42355:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},46964:(e,t,r)=>{Promise.resolve().then(r.bind(r,83459))},50183:(e,t,r)=>{"use strict";r.d(t,{$:()=>n});var a=r(95155),o=r(12115);let n=e=>{let{children:t,variant:r="default",size:n="default",className:s="",asChild:c=!1,...i}=e,l={default:"bg-blue-600 text-white hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700",outline:"border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200",ghost:"hover:bg-gray-100 hover:text-gray-900",link:"text-blue-600 underline-offset-4 hover:underline"},d={default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"},u=l[r]||l.default,h=d[n]||d.default,p="".concat("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50"," ").concat(u," ").concat(h," ").concat(s);return c?o.cloneElement(t,{className:p,...i}):(0,a.jsx)("button",{className:p,...i,children:t})}},54765:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>i,ZB:()=>s,Zp:()=>o,aR:()=>n});var a=r(95155);r(12115);let o=e=>{let{children:t,className:r="",...o}=e;return(0,a.jsx)("div",{className:"bg-white rounded-lg border border-gray-200 shadow-sm ".concat(r),...o,children:t})},n=e=>{let{children:t,className:r="",...o}=e;return(0,a.jsx)("div",{className:"p-6 pb-4 ".concat(r),...o,children:t})},s=e=>{let{children:t,className:r="",...o}=e;return(0,a.jsx)("h3",{className:"text-lg font-semibold leading-none tracking-tight ".concat(r),...o,children:t})},c=e=>{let{children:t,className:r="",...o}=e;return(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1.5 ".concat(r),...o,children:t})},i=e=>{let{children:t,className:r="",...o}=e;return(0,a.jsx)("div",{className:"p-6 pt-0 ".concat(r),...o,children:t})}},83459:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var a=r(95155),o=r(12115),n=r(6874),s=r.n(n),c=r(54765),i=r(50183),l=r(42355),d=r(35695),u=r(37533);let h=()=>{let[e,t]=(0,o.useState)(""),[r,n]=(0,o.useState)(""),[s,c]=(0,o.useState)("technical"),[l,h]=(0,o.useState)("medium"),[p,g]=(0,o.useState)(!1),[m,f]=(0,o.useState)(null),b=(0,d.useRouter)(),y=async t=>{t.preventDefault(),g(!0),f(null);try{let t=await u.yp.createTicket({title:e,description:r,category:s,priority:l});console.log("Ticket created:",t),b.push("/admin/helpandsupport")}catch(e){var a,o;console.error("Error creating ticket:",e),f((null==(o=e.response)||null==(a=o.data)?void 0:a.message)||"Failed to create ticket. Please try again.")}finally{g(!1)}};return(0,a.jsxs)("form",{onSubmit:y,className:"space-y-6",children:[m&&(0,a.jsx)("div",{className:"p-3 bg-red-100 border border-red-300 text-red-700 rounded-md",children:m}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"title",className:"block text-sm font-medium text-gray-700 mb-2",children:"Title *"}),(0,a.jsx)("input",{type:"text",id:"title",value:e,onChange:e=>t(e.target.value),required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Brief description of the issue"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"category",className:"block text-sm font-medium text-gray-700 mb-2",children:"Category"}),(0,a.jsxs)("select",{id:"category",value:s,onChange:e=>c(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,a.jsx)("option",{value:"technical",children:"Technical Issue"}),(0,a.jsx)("option",{value:"bug",children:"Bug Report"}),(0,a.jsx)("option",{value:"feature",children:"Feature Request"}),(0,a.jsx)("option",{value:"account",children:"Account Issue"}),(0,a.jsx)("option",{value:"general",children:"General Question"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"priority",className:"block text-sm font-medium text-gray-700 mb-2",children:"Priority"}),(0,a.jsxs)("select",{id:"priority",value:l,onChange:e=>h(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,a.jsx)("option",{value:"low",children:"Low"}),(0,a.jsx)("option",{value:"medium",children:"Medium"}),(0,a.jsx)("option",{value:"high",children:"High"}),(0,a.jsx)("option",{value:"urgent",children:"Urgent"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-2",children:"Description *"}),(0,a.jsx)("textarea",{id:"description",value:r,onChange:e=>n(e.target.value),required:!0,rows:6,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Please provide detailed information about your issue or request"})]}),(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsx)(i.$,{type:"submit",disabled:p,className:"bg-blue-600 hover:bg-blue-700 text-white",children:p?"Creating...":"Create Ticket"}),(0,a.jsx)(i.$,{type:"button",variant:"outline",onClick:()=>b.push("/admin/helpandsupport"),children:"Cancel"})]})]})},p=()=>(0,a.jsxs)("div",{className:"space-y-6 pb-8",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(i.$,{variant:"outline",size:"icon",asChild:!0,children:(0,a.jsx)(s(),{href:"/admin/helpandsupport",children:(0,a.jsx)(l.A,{className:"h-4 w-4"})})}),(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Create a New Ticket"})]}),(0,a.jsxs)(c.Zp,{children:[(0,a.jsxs)(c.aR,{children:[(0,a.jsx)(c.ZB,{children:"Support Ticket Request"}),(0,a.jsx)(c.BT,{children:"Fill out the form below to submit a new support ticket."})]}),(0,a.jsx)(c.Wu,{children:(0,a.jsx)(h,{})})]})]})}},e=>{var t=t=>e(e.s=t);e.O(0,[3464,6874,8441,1684,7358],()=>t(46964)),_N_E=e.O()}]);