(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2609],{1243:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},4516:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},14186:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},15941:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>j});var s=a(95155),l=a(12115),r=a(14186),i=a(92657),n=a(40646),c=a(54861),o=a(33109),d=a(16785),p=a(47924),m=a(23227),x=a(4516),h=a(69074),u=a(55868),g=a(34842),y=a(25384);let j=()=>{var e,t,a,j,v,b,f,N;let[A,w]=(0,l.useState)([]),[_,k]=(0,l.useState)(null),[E,D]=(0,l.useState)("ALL"),[C,L]=(0,l.useState)(""),[I,S]=(0,l.useState)("recent");(0,l.useEffect)(()=>{(async()=>{try{let e=await (0,g.G$)(),t=[];e.data&&e.data.data&&Array.isArray(e.data.data)?t=e.data.data:Array.isArray(e.data)&&(t=e.data);let a=await Promise.all(t.map(async e=>{try{let t=await (0,g.YQ)(e.job);return{...e,jobDetails:t.data}}catch(t){return console.error("Failed to fetch job details for job ".concat(e.job,":"),t),e}}));console.log("Applications with job details:",a),w(a),k(a[0]||null)}catch(e){console.error("Failed to load applied jobs:",e),w([])}})()},[]);let M=e=>{let t={APPLIED:{color:"bg-blue-50 text-blue-700 border-blue-200",icon:(0,s.jsx)(r.A,{className:"w-4 h-4"}),bgIcon:"bg-blue-100",textIcon:"text-blue-600"},"UNDER REVIEW":{color:"bg-amber-50 text-amber-700 border-amber-200",icon:(0,s.jsx)(i.A,{className:"w-4 h-4"}),bgIcon:"bg-amber-100",textIcon:"text-amber-600"},"INTERVIEW SCHEDULED":{color:"bg-green-50 text-green-700 border-green-200",icon:(0,s.jsx)(n.A,{className:"w-4 h-4"}),bgIcon:"bg-green-100",textIcon:"text-green-600"},REJECTED:{color:"bg-red-50 text-red-700 border-red-200",icon:(0,s.jsx)(c.A,{className:"w-4 h-4"}),bgIcon:"bg-red-100",textIcon:"text-red-600"},ACCEPTED:{color:"bg-emerald-50 text-emerald-700 border-emerald-200",icon:(0,s.jsx)(o.A,{className:"w-4 h-4"}),bgIcon:"bg-emerald-100",textIcon:"text-emerald-600"}};return t[e]||t.APPLIED},R=Array.isArray(A)?A:[],T=R.filter(e=>{var t,a;let s="ALL"===E||e.status===E,l=(null==(t=e.jobDetails)?void 0:t.title)||e.job_title||e.title||"",r=(null==(a=e.jobDetails)?void 0:a.employer_name)||e.employer_name||e.company_name||"",i=l.toLowerCase().includes(C.toLowerCase())||r.toLowerCase().includes(C.toLowerCase());return s&&i}).sort((e,t)=>{if("recent"===I)return new Date(t.applied_at)-new Date(e.applied_at);if("company"===I){var a,s;let l=(null==(a=e.jobDetails)?void 0:a.employer_name)||e.employer_name||e.company_name||"",r=(null==(s=t.jobDetails)?void 0:s.employer_name)||t.employer_name||t.company_name||"";return l.localeCompare(r)}return"status"===I?(e.status||"").localeCompare(t.status||""):0}),P={total:R.length,pending:R.filter(e=>"APPLIED"===e.status||"UNDER REVIEW"===e.status).length,interviews:R.filter(e=>"INTERVIEW SCHEDULED"===e.status).length,rejected:R.filter(e=>"REJECTED"===e.status).length};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Job Applications"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Track and manage your job applications"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,s.jsx)(d.A,{className:"w-4 h-4 text-blue-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-lg font-bold text-gray-900",children:P.total}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Total"})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"p-2 bg-amber-100 rounded-lg",children:(0,s.jsx)(r.A,{className:"w-4 h-4 text-amber-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-lg font-bold text-gray-900",children:P.pending}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Pending"})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,s.jsx)(n.A,{className:"w-4 h-4 text-green-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-lg font-bold text-gray-900",children:P.interviews}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Interviews"})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,s.jsx)(o.A,{className:"w-4 h-4 text-purple-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"text-lg font-bold text-gray-900",children:[P.total>0?Math.round((P.total-P.rejected)/P.total*100):0,"%"]}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Response Rate"})]})]})]})]})}),(0,s.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 h-[calc(100vh-240px)]",children:(0,s.jsxs)("div",{className:"flex h-full",children:[(0,s.jsxs)("div",{className:"w-2/5 border-r border-gray-200 flex flex-col",children:[(0,s.jsxs)("div",{className:"p-3 border-b border-gray-200 space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Applications"}),(0,s.jsxs)("span",{className:"text-sm text-gray-500",children:[T.length," of ",R.length]})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),(0,s.jsx)("input",{type:"text",placeholder:"Search applications...",value:C,onChange:e=>L(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)("select",{value:E,onChange:e=>D(e.target.value),className:"flex-1 px-3 py-1.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm",children:[(0,s.jsx)("option",{value:"ALL",children:"All Status"}),(0,s.jsx)("option",{value:"APPLIED",children:"Applied"}),(0,s.jsx)("option",{value:"UNDER REVIEW",children:"Under Review"}),(0,s.jsx)("option",{value:"INTERVIEW SCHEDULED",children:"Interview"}),(0,s.jsx)("option",{value:"REJECTED",children:"Rejected"}),(0,s.jsx)("option",{value:"ACCEPTED",children:"Accepted"})]}),(0,s.jsxs)("select",{value:I,onChange:e=>S(e.target.value),className:"flex-1 px-3 py-1.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm",children:[(0,s.jsx)("option",{value:"recent",children:"Recent First"}),(0,s.jsx)("option",{value:"company",children:"Company A-Z"}),(0,s.jsx)("option",{value:"status",children:"Status"})]})]})]}),(0,s.jsx)("div",{className:"flex-1 overflow-y-auto",children:T.length>0?(0,s.jsx)("div",{className:"divide-y divide-gray-100",children:T.map(e=>{var t,a,l;let r=M(e.status),i=e.id===(null==_?void 0:_.id);return(0,s.jsx)("div",{onClick:()=>k(e),className:"p-3 cursor-pointer transition-all duration-200 hover:bg-gray-50 ".concat(i?"bg-blue-50 border-r-2 border-blue-500":""),children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsx)("h3",{className:"font-semibold text-sm leading-tight ".concat(i?"text-blue-900":"text-gray-900"),children:(null==(t=e.jobDetails)?void 0:t.title)||e.job_title||e.title||"Title not available"}),(0,s.jsx)("div",{className:"p-1 rounded-full ".concat(r.bgIcon),children:(0,s.jsx)("div",{className:r.textIcon,children:r.icon})})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[(0,s.jsx)(m.A,{className:"w-3 h-3"}),(0,s.jsx)("span",{className:"font-medium",children:(null==(a=e.jobDetails)?void 0:a.employer_name)||e.employer_name||e.company_name||"N/A"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-4 text-xs text-gray-500",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(x.A,{className:"w-3 h-3"}),(0,s.jsx)("span",{children:(null==(l=e.jobDetails)?void 0:l.location)||e.location||e.job_location||"N/A"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(h.A,{className:"w-3 h-3"}),(0,s.jsx)("span",{children:e.applied_at?new Date(e.applied_at).toLocaleDateString():"—"})]})]}),(0,s.jsxs)("div",{className:"inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ".concat(r.color),children:[r.icon,(0,s.jsx)("span",{children:e.status})]})]})},e.id)})}):(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center h-full p-6 text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4",children:(0,s.jsx)(p.A,{className:"w-8 h-8 text-gray-400"})}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No applications found"}),(0,s.jsx)("p",{className:"text-gray-500 text-sm",children:"Try adjusting your search or filters"})]})})]}),(0,s.jsx)("div",{className:"flex-1 flex flex-col",children:_?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:(null==(e=_.jobDetails)?void 0:e.title)||_.job_title||_.title||"Title not available"}),(0,s.jsxs)("div",{className:"flex items-center gap-4 text-gray-600",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(m.A,{className:"w-5 h-5"}),(0,s.jsx)("span",{className:"font-medium",children:(null==(t=_.jobDetails)?void 0:t.employer_name)||_.employer_name||_.company_name||"Company not available"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(x.A,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:(null==(a=_.jobDetails)?void 0:a.location)||_.location||_.job_location||"Location not available"})]})]})]}),(0,s.jsxs)("div",{className:"px-4 py-2 rounded-full text-sm font-medium border flex items-center gap-2 ".concat(M(_.status).color),children:[M(_.status).icon,(0,s.jsx)("span",{children:_.status})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-1",children:[(0,s.jsx)(u.A,{className:"w-4 h-4 text-gray-500"}),(0,s.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Salary"})]}),(0,s.jsx)("span",{className:"text-sm text-gray-600",children:(null==(j=_.jobDetails)?void 0:j.salary_min)&&(null==(v=_.jobDetails)?void 0:v.salary_max)?"$".concat(_.jobDetails.salary_min," - $").concat(_.jobDetails.salary_max):"Not disclosed"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-1",children:[(0,s.jsx)(h.A,{className:"w-4 h-4 text-gray-500"}),(0,s.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Applied"})]}),(0,s.jsx)("span",{className:"text-sm text-gray-600",children:_.applied_at?new Date(_.applied_at).toLocaleDateString():"—"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-1",children:[(0,s.jsx)(r.A,{className:"w-4 h-4 text-gray-500"}),(0,s.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Deadline"})]}),(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"Not specified"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-1",children:[(0,s.jsx)(d.A,{className:"w-4 h-4 text-gray-500"}),(0,s.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Type"})]}),(0,s.jsx)("span",{className:"text-sm text-gray-600",children:(null==(b=_.jobDetails)?void 0:b.job_type)||_.job_type||"Not specified"})]})]})]}),(0,s.jsx)("div",{className:"flex-1 p-6 overflow-y-auto",children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Job Description"}),(0,s.jsx)("div",{className:"prose prose-sm max-w-none",children:(0,s.jsx)(y.G_,{description:null==(f=_.jobDetails)?void 0:f.description,className:""})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Requirements"}),(0,s.jsx)("div",{className:"space-y-2 text-gray-700",children:(null==(N=_.jobDetails)?void 0:N.requirements)?(0,s.jsx)("div",{dangerouslySetInnerHTML:{__html:_.jobDetails.requirements}}):(0,s.jsx)("span",{children:"No requirements specified."})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Application Timeline"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,s.jsx)(n.A,{className:"w-5 h-5 text-blue-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-gray-900",children:"Application Submitted"}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:_.applied_at?new Date(_.applied_at).toLocaleDateString():"—"})]})]}),"APPLIED"!==_.status&&(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center",children:(0,s.jsx)(i.A,{className:"w-5 h-5 text-amber-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-gray-900",children:"Under Review"}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Application is being reviewed by the hiring team"})]})]}),"INTERVIEW SCHEDULED"===_.status&&(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,s.jsx)(h.A,{className:"w-5 h-5 text-green-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-gray-900",children:"Interview Scheduled"}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"You have been invited for an interview"})]})]})]})]})]})})]}):(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center h-full p-6 text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4",children:(0,s.jsx)(i.A,{className:"w-8 h-8 text-gray-400"})}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Select an application"}),(0,s.jsx)("p",{className:"text-gray-500",children:"Choose an application from the list to view details"})]})})]})})]})}},16785:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},19946:(e,t,a)=>{"use strict";a.d(t,{A:()=>p});var s=a(12115);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),r=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,a)=>a?a.toUpperCase():t.toLowerCase()),i=e=>{let t=r(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return t.filter((e,t,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===t).join(" ").trim()},c=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,s.forwardRef)((e,t)=>{let{color:a="currentColor",size:l=24,strokeWidth:r=2,absoluteStrokeWidth:i,className:d="",children:p,iconNode:m,...x}=e;return(0,s.createElement)("svg",{ref:t,...o,width:l,height:l,stroke:a,strokeWidth:i?24*Number(r)/Number(l):r,className:n("lucide",d),...!p&&!c(x)&&{"aria-hidden":"true"},...x},[...m.map(e=>{let[t,a]=e;return(0,s.createElement)(t,a)}),...Array.isArray(p)?p:[p]])}),p=(e,t)=>{let a=(0,s.forwardRef)((a,r)=>{let{className:c,...o}=a;return(0,s.createElement)(d,{ref:r,iconNode:t,className:n("lucide-".concat(l(i(e))),"lucide-".concat(e),c),...o})});return a.displayName=i(e),a}},23227:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},25384:(e,t,a)=>{"use strict";a.d(t,{G_:()=>l});var s=a(95155);function l(e){let{description:t,className:a=""}=e,l=t?t.replace(/\n/g,"<br />").replace(/•/g,"•").replace(/\*\s/g,"• ").replace(/-\s/g,"• ").trim():"No description provided.";return(0,s.jsx)("div",{className:"text-gray-700 leading-relaxed ".concat(a),dangerouslySetInnerHTML:{__html:l}})}},29869:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},33109:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},33786:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},34842:(e,t,a)=>{"use strict";a.d(t,{G$:()=>n,N6:()=>l,Om:()=>d,T4:()=>p,YQ:()=>i,_S:()=>c,lh:()=>o,vr:()=>r});var s=a(37719);function l(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.page&&t.append("page",e.page),e.per_page&&t.append("per_page",e.per_page),e.job_type&&"ALL"!==e.job_type&&t.append("job_type",e.job_type),e.location&&"ALL"!==e.location&&t.append("location",e.location),e.salary_min&&t.append("salary_min",e.salary_min),e.search&&t.append("search",e.search);let a=t.toString();return s.A.get("/api/v1/college/default-college/jobs/".concat(a?"?".concat(a):""))}function r(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!Object.values(a).some(e=>e instanceof File))return s.A.post("/api/v1/college/default-college/jobs/".concat(e,"/apply/"),{cover_letter:t,additional_field_responses:a});{let l=new FormData;return l.append("cover_letter",t),Object.entries(a).forEach(e=>{let[t,a]=e;a instanceof File?l.append(t,a):l.append(t,JSON.stringify(a))}),s.A.post("/api/v1/college/default-college/jobs/".concat(e,"/apply/"),l,{headers:{"Content-Type":"multipart/form-data"}})}}function i(e){return s.A.get("/api/v1/college/default-college/jobs/".concat(e,"/"))}function n(){return s.A.get("/api/v1/college/default-college/jobs/applied/")}function c(e){return s.A.post("/api/v1/college/default-college/jobs/create/",e)}function o(e,t){return s.A.put("/api/v1/college/default-college/jobs/".concat(e,"/"),t)}function d(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.page&&t.append("page",e.page),e.per_page&&t.append("per_page",e.per_page),e.search&&t.append("search",e.search),e.type&&"All"!==e.type&&t.append("job_type",e.type),e.minCTC&&t.append("salary_min",e.minCTC),e.maxCTC&&t.append("salary_max",e.maxCTC),e.minStipend&&t.append("stipend_min",e.minStipend),e.maxStipend&&t.append("stipend_max",e.maxStipend),e.location&&t.append("location",e.location),void 0!==e.is_published&&t.append("is_published",e.is_published),e.company_id&&t.append("company_id",e.company_id),e.company_name&&t.append("company_name",e.company_name);let a=t.toString(),l="/api/v1/college/default-college/jobs/admin/".concat(a?"?".concat(a):"");return console.log("\uD83C\uDF10 listJobsAdmin calling URL:",l,"with params:",e),s.A.get(l).then(e=>{var t,a,s,l,r,i;return console.log("\uD83C\uDF10 listJobsAdmin response:",{status:e.status,totalJobs:(null==(a=e.data)||null==(t=a.pagination)?void 0:t.total_count)||0,currentPage:(null==(l=e.data)||null==(s=l.pagination)?void 0:s.current_page)||1,totalPages:(null==(i=e.data)||null==(r=i.pagination)?void 0:r.total_pages)||1}),e}).catch(e=>{var t;throw console.error("\uD83C\uDF10 listJobsAdmin error:",e),console.error("\uD83C\uDF10 listJobsAdmin error response:",null==(t=e.response)?void 0:t.data),e})}function p(e){return s.A.patch("/api/v1/jobs/".concat(e,"/toggle-publish/"))}},36476:(e,t,a)=>{Promise.resolve().then(a.bind(a,15941))},37719:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var s=a(23464);a(73983);let l=s.A.create({baseURL:"http://127.0.0.1:8000",headers:{"Content-Type":"application/json"}});l.interceptors.request.use(e=>{let t=localStorage.getItem("access_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),l.interceptors.response.use(e=>e,async e=>{var t;let a=e.config;if((null==(t=e.response)?void 0:t.status)===401&&!a._retry){a._retry=!0;try{let e=localStorage.getItem("refresh_token");if(e){let t=await s.A.post("http://127.0.0.1:8000/api/auth/token/refresh/",{refresh:e});return localStorage.setItem("access_token",t.data.access),a.headers.Authorization="Bearer ".concat(t.data.access),l(a)}}catch(e){console.error("Error refreshing token:",e),localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),window.location.href="/login"}}return Promise.reject(e)});let r=l},40646:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},47924:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},54416:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},54861:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},55868:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},69074:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},71007:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},75525:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},76517:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]])},81284:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},85339:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},92657:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[3464,3983,8441,1684,7358],()=>t(36476)),_N_E=e.O()}]);