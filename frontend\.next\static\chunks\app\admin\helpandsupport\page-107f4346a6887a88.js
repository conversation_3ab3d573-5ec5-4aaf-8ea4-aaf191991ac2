(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7168],{14186:(e,s,t)=>{"use strict";t.d(s,{A:()=>l});let l=(0,t(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},29581:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>u});var l=t(95155),a=t(12115),r=t(6874),c=t.n(r),i=t(54765),d=t(50183),n=t(84616);let x=(0,t(19946).A)("ticket",[["path",{d:"M2 9a3 3 0 0 1 0 6v2a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2a3 3 0 0 1 0-6V7a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2Z",key:"qn84l0"}],["path",{d:"M13 5v2",key:"dyzc3o"}],["path",{d:"M13 17v2",key:"1ont0d"}],["path",{d:"M13 11v2",key:"1wjjxi"}]]);var m=t(85339),h=t(14186),o=t(40646),p=t(31199),j=t(37533),y=t(28835);let u=()=>{let[e,s]=(0,a.useState)([]),[t,r]=(0,a.useState)(!0),[u,N]=(0,a.useState)(null);(0,a.useEffect)(()=>{(async()=>{try{r(!0);let e=(0,y.gL)(),t={};e&&e.id&&(t.user_id=e.id);let l=await j.yp.getTickets(t),a=[];Array.isArray(l)?a=l:l.data&&Array.isArray(l.data)?a=l.data:l.results&&Array.isArray(l.results)&&(a=l.results),s(a),N(null)}catch(e){console.error("Error fetching tickets:",e),N("Failed to load tickets. Please try again later.")}finally{r(!1)}})()},[]);let g=s=>e.filter(e=>s.includes(e.status)),f=g(["open"]).length,b=g(["in-progress","in_progress"]).length,w=g(["resolved"]).length,k=e.length,v=[...e].sort((e,s)=>new Date(s.createdAt||s.created_at||0).getTime()-new Date(e.createdAt||e.created_at||0).getTime()).slice(0,6);return(0,l.jsxs)("div",{className:"space-y-6 pb-8",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center",children:[(0,l.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Help & Support Dashboard"}),(0,l.jsx)(d.$,{asChild:!0,className:"bg-blue-600 hover:bg-blue-700",children:(0,l.jsxs)(c(),{href:"/admin/helpandsupport/new",children:[(0,l.jsx)(n.A,{className:"mr-2 h-4 w-4"})," New Ticket"]})})]}),t?(0,l.jsx)("div",{className:"flex justify-center py-10",children:(0,l.jsx)("div",{className:"animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"})}):u?(0,l.jsxs)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg",children:[u,(0,l.jsx)(d.$,{onClick:()=>window.location.reload(),variant:"link",className:"text-red-700 underline pl-2",children:"Retry"})]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,l.jsx)(i.Zp,{className:"bg-white border shadow-sm",children:(0,l.jsx)(i.Wu,{className:"p-4",children:(0,l.jsxs)("div",{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm font-medium mt-6",children:"My Tickets"}),(0,l.jsx)("p",{className:"text-2xl font-bold",children:k}),(0,l.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Tickets I've created"})]}),(0,l.jsx)(x,{className:"h-7 w-7 text-gray-700"})]})})}),(0,l.jsx)(i.Zp,{className:"bg-white border shadow-sm",children:(0,l.jsx)(i.Wu,{className:"p-4",children:(0,l.jsxs)("div",{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm font-medium mt-6",children:"Open"}),(0,l.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:f}),(0,l.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Tickets waiting for response"})]}),(0,l.jsx)(m.A,{className:"h-7 w-7 text-blue-500"})]})})}),(0,l.jsx)(i.Zp,{className:"bg-white border shadow-sm",children:(0,l.jsx)(i.Wu,{className:"p-4",children:(0,l.jsxs)("div",{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm font-medium mt-6",children:"In Progress"}),(0,l.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:b}),(0,l.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Tickets being worked on"})]}),(0,l.jsx)(h.A,{className:"h-7 w-7 text-yellow-500"})]})})}),(0,l.jsx)(i.Zp,{className:"bg-white border shadow-sm",children:(0,l.jsx)(i.Wu,{className:"p-4",children:(0,l.jsxs)("div",{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm font-medium mt-6",children:"Resolved"}),(0,l.jsx)("p",{className:"text-2xl font-bold text-green-600",children:w}),(0,l.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Successfully resolved tickets"})]}),(0,l.jsx)(o.A,{className:"h-7 w-7 text-green-500"})]})})})]}),(0,l.jsxs)("div",{className:"mt-8",children:[(0,l.jsxs)("div",{className:"flex flex-row items-center justify-between space-y-0 pb-2 mb-4",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold",children:"My Recent Tickets"}),(0,l.jsx)(d.$,{variant:"outline",asChild:!0,className:"text-sm",children:(0,l.jsx)(c(),{href:"/admin/helpandsupport/tickets",children:"View All"})})]}),(0,l.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3",children:v.length>0?v.map(e=>(0,l.jsx)(p.A,{ticket:e},e.id)):(0,l.jsxs)(i.Zp,{className:"col-span-full p-6 text-center",children:[(0,l.jsx)("p",{className:"text-muted-foreground",children:"No tickets found"}),(0,l.jsx)(d.$,{className:"mt-4",asChild:!0,children:(0,l.jsx)(c(),{href:"/admin/helpandsupport/new",children:"Create Your First Ticket"})})]})})]})]})]})}},30274:(e,s,t)=>{Promise.resolve().then(t.bind(t,29581))},40646:(e,s,t)=>{"use strict";t.d(s,{A:()=>l});let l=(0,t(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},85339:(e,s,t)=>{"use strict";t.d(s,{A:()=>l});let l=(0,t(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[3464,6874,8384,6561,3371,8441,1684,7358],()=>s(30274)),_N_E=e.O()}]);