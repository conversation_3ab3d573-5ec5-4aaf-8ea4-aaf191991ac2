(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8711],{755:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var r=(0,s(86467).A)("outline","plus","IconPlus",[["path",{d:"M12 5l0 14",key:"svg-0"}],["path",{d:"M5 12l14 0",key:"svg-1"}]])},4173:(e,t,s)=>{Promise.resolve().then(s.bind(s,25798))},15259:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var r=(0,s(86467).A)("outline","building","IconBuilding",[["path",{d:"M3 21l18 0",key:"svg-0"}],["path",{d:"M9 8l1 0",key:"svg-1"}],["path",{d:"M9 12l1 0",key:"svg-2"}],["path",{d:"M9 16l1 0",key:"svg-3"}],["path",{d:"M14 8l1 0",key:"svg-4"}],["path",{d:"M14 12l1 0",key:"svg-5"}],["path",{d:"M14 16l1 0",key:"svg-6"}],["path",{d:"M5 21v-16a2 2 0 0 1 2 -2h10a2 2 0 0 1 2 2v16",key:"svg-7"}]])},25798:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var r=s(95155),a=s(12115),l=s(35695),i=s(15259),n=s(33631),o=s(755),c=(0,s(86467).A)("outline","arrow-right","IconArrowRight",[["path",{d:"M5 12l14 0",key:"svg-0"}],["path",{d:"M13 18l6 -6",key:"svg-1"}],["path",{d:"M13 6l6 6",key:"svg-2"}]]);function d(){let e=(0,l.useRouter)();(0,a.useEffect)(()=>{},[e]);let t=[{title:"Companies",description:"Manage company partnerships and view their job opportunities",icon:(0,r.jsx)(i.A,{className:"w-8 h-8"}),href:"/admin/jobs/companies",color:"blue",stats:"View all registered companies"},{title:"Job Listings",description:"View and manage all job postings and their publication status",icon:(0,r.jsx)(n.A,{className:"w-8 h-8"}),href:"/admin/jobs/listings",color:"green",stats:"Manage active job postings"},{title:"Post New Job",description:"Create a new job posting for companies",icon:(0,r.jsx)(o.A,{className:"w-8 h-8"}),href:"/admin/jobs/create",color:"purple",stats:"Create new opportunities"}],s=e=>{let t={blue:{bg:"bg-blue-50",border:"border-blue-200",icon:"text-blue-600",title:"text-blue-900",button:"bg-blue-600 hover:bg-blue-700"},green:{bg:"bg-green-50",border:"border-green-200",icon:"text-green-600",title:"text-green-900",button:"bg-green-600 hover:bg-green-700"},purple:{bg:"bg-purple-50",border:"border-purple-200",icon:"text-purple-600",title:"text-purple-900",button:"bg-purple-600 hover:bg-purple-700"}};return t[e]||t.blue};return(0,r.jsx)("div",{className:"p-6",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,r.jsxs)("div",{className:"text-center mb-12",children:[(0,r.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Job Management Center"}),(0,r.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Comprehensive platform for managing companies, job listings, and recruitment processes. Choose an option below to get started."})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12",children:t.map(t=>{let a=s(t.color);return(0,r.jsx)("div",{className:"".concat(a.bg," ").concat(a.border," border-2 rounded-xl p-8 hover:shadow-lg transition-all duration-200 cursor-pointer group"),onClick:()=>e.push(t.href),children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"".concat(a.icon," mb-4 flex justify-center"),children:t.icon}),(0,r.jsx)("h3",{className:"text-2xl font-bold ".concat(a.title," mb-3"),children:t.title}),(0,r.jsx)("p",{className:"text-gray-700 mb-4 min-h-[3rem]",children:t.description}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-6",children:t.stats}),(0,r.jsxs)("button",{className:"".concat(a.button," text-white px-6 py-3 rounded-lg font-medium flex items-center justify-center mx-auto group-hover:scale-105 transition-transform"),children:["Go to ",t.title,(0,r.jsx)(c,{className:"w-4 h-4 ml-2"})]})]})},t.href)})}),(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-8",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6 text-center",children:"Quick Overview"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-3xl font-bold text-blue-600 mb-2",children:"--"}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Total Companies"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-3xl font-bold text-green-600 mb-2",children:"--"}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Active Jobs"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-3xl font-bold text-yellow-600 mb-2",children:"--"}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Pending Publication"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-3xl font-bold text-purple-600 mb-2",children:"--"}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Total Applications"})]})]}),(0,r.jsx)("div",{className:"text-center mt-6",children:(0,r.jsx)("p",{className:"text-gray-500 text-sm",children:"Visit individual sections to view detailed statistics"})})]}),(0,r.jsxs)("div",{className:"mt-8 text-center",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Quick Actions"}),(0,r.jsxs)("div",{className:"flex justify-center space-x-4",children:[(0,r.jsxs)("button",{onClick:()=>e.push("/admin/jobs/create"),className:"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center space-x-2",children:[(0,r.jsx)(o.A,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"Post New Job"})]}),(0,r.jsxs)("button",{onClick:()=>e.push("/admin/jobs/listings"),className:"px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 flex items-center space-x-2",children:[(0,r.jsx)(n.A,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"View All Jobs"})]})]})]})]})})}},33631:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var r=(0,s(86467).A)("outline","briefcase","IconBriefcase",[["path",{d:"M3 7m0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v9a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z",key:"svg-0"}],["path",{d:"M8 7v-2a2 2 0 0 1 2 -2h4a2 2 0 0 1 2 2v2",key:"svg-1"}],["path",{d:"M12 12l0 .01",key:"svg-2"}],["path",{d:"M3 13a20 20 0 0 0 18 0",key:"svg-3"}]])},35695:(e,t,s)=>{"use strict";var r=s(18999);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"usePathname")&&s.d(t,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},86467:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(12115),a={outline:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"},filled:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",stroke:"none"}};let l=(e,t,s,l)=>{let i=(0,r.forwardRef)((s,i)=>{let{color:n="currentColor",size:o=24,stroke:c=2,title:d,className:x,children:h,...m}=s;return(0,r.createElement)("svg",{ref:i,...a[e],width:o,height:o,className:["tabler-icon","tabler-icon-".concat(t),x].join(" "),..."filled"===e?{fill:n}:{strokeWidth:c,stroke:n},...m},[d&&(0,r.createElement)("title",{key:"svg-title"},d),...l.map(e=>{let[t,s]=e;return(0,r.createElement)(t,s)}),...Array.isArray(h)?h:[h]])});return i.displayName="".concat(s),i}}},e=>{var t=t=>e(e.s=t);e.O(0,[8441,1684,7358],()=>t(4173)),_N_E=e.O()}]);