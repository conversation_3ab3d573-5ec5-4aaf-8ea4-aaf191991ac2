{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/admin/companymanagement/edit/[id]", "regex": "^/admin/companymanagement/edit/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/companymanagement/edit/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/admin/companymanagement/[id]", "regex": "^/admin/companymanagement/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/companymanagement/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/admin/form/edit/[id]", "regex": "^/admin/form/edit/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/form/edit/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/admin/helpandsupport/tickets/[id]", "regex": "^/admin/helpandsupport/tickets/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/helpandsupport/tickets/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/admin/jobs/edit/[id]", "regex": "^/admin/jobs/edit/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/jobs/edit/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/admin/jobs/[id]", "regex": "^/admin/jobs/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/jobs/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/company/[id]", "regex": "^/company/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/company/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/forms/[id]", "regex": "^/forms/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/forms/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/jobpostings/[id]/apply", "regex": "^/jobpostings/([^/]+?)/apply(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/jobpostings/(?<nxtPid>[^/]+?)/apply(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/admin/analytics", "regex": "^/admin/analytics(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/analytics(?:/)?$"}, {"page": "/admin/applications", "regex": "^/admin/applications(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/applications(?:/)?$"}, {"page": "/admin/companymanagement", "regex": "^/admin/companymanagement(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/companymanagement(?:/)?$"}, {"page": "/admin/companymanagement/create", "regex": "^/admin/companymanagement/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/companymanagement/create(?:/)?$"}, {"page": "/admin/dashboard", "regex": "^/admin/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/dashboard(?:/)?$"}, {"page": "/admin/form", "regex": "^/admin/form(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/form(?:/)?$"}, {"page": "/admin/form/review", "regex": "^/admin/form/review(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/form/review(?:/)?$"}, {"page": "/admin/helpandsupport", "regex": "^/admin/helpandsupport(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/helpandsupport(?:/)?$"}, {"page": "/admin/helpandsupport/new", "regex": "^/admin/helpandsupport/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/helpandsupport/new(?:/)?$"}, {"page": "/admin/helpandsupport/tickets", "regex": "^/admin/helpandsupport/tickets(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/helpandsupport/tickets(?:/)?$"}, {"page": "/admin/jobs", "regex": "^/admin/jobs(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/jobs(?:/)?$"}, {"page": "/admin/jobs/companies", "regex": "^/admin/jobs/companies(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/jobs/companies(?:/)?$"}, {"page": "/admin/jobs/create", "regex": "^/admin/jobs/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/jobs/create(?:/)?$"}, {"page": "/admin/jobs/listings", "regex": "^/admin/jobs/listings(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/jobs/listings(?:/)?$"}, {"page": "/admin/posts", "regex": "^/admin/posts(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/posts(?:/)?$"}, {"page": "/admin/profile", "regex": "^/admin/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/profile(?:/)?$"}, {"page": "/admin/settings", "regex": "^/admin/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/settings(?:/)?$"}, {"page": "/admin/student-management", "regex": "^/admin/student\\-management(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/student\\-management(?:/)?$"}, {"page": "/admin/student-management/updatepage", "regex": "^/admin/student\\-management/updatepage(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/student\\-management/updatepage(?:/)?$"}, {"page": "/calendar", "regex": "^/calendar(?:/)?$", "routeKeys": {}, "namedRegex": "^/calendar(?:/)?$"}, {"page": "/companies", "regex": "^/companies(?:/)?$", "routeKeys": {}, "namedRegex": "^/companies(?:/)?$"}, {"page": "/events", "regex": "^/events(?:/)?$", "routeKeys": {}, "namedRegex": "^/events(?:/)?$"}, {"page": "/explore", "regex": "^/explore(?:/)?$", "routeKeys": {}, "namedRegex": "^/explore(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/inbox", "regex": "^/inbox(?:/)?$", "routeKeys": {}, "namedRegex": "^/inbox(?:/)?$"}, {"page": "/jobpostings", "regex": "^/jobpostings(?:/)?$", "routeKeys": {}, "namedRegex": "^/jobpostings(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/myjobs", "regex": "^/myjobs(?:/)?$", "routeKeys": {}, "namedRegex": "^/myjobs(?:/)?$"}, {"page": "/profile", "regex": "^/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile(?:/)?$"}, {"page": "/settings", "regex": "^/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings(?:/)?$"}, {"page": "/signup", "regex": "^/signup(?:/)?$", "routeKeys": {}, "namedRegex": "^/signup(?:/)?$"}, {"page": "/thank-you", "regex": "^/thank\\-you(?:/)?$", "routeKeys": {}, "namedRegex": "^/thank\\-you(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}