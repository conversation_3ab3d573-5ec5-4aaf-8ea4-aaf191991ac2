(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7581],{2142:(e,t,a)=>{Promise.resolve().then(a.bind(a,47518))},47518:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>u});var s=a(95155),i=a(12115),n=a(95370),l=a(81110),r=a(33631),d=a(72733),o=a(61062),c=a(83406),h=a(7194),m=a(876),p=(0,a(86467).A)("outline","help","IconHelp",[["path",{d:"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0",key:"svg-0"}],["path",{d:"M12 17l0 .01",key:"svg-1"}],["path",{d:"M12 13.5a1.5 1.5 0 0 1 1 -1.5a2.6 2.6 0 1 0 -3 -4",key:"svg-2"}]]);let x=(0,a(19946).A)("megaphone",[["path",{d:"m3 11 18-5v12L3 14v-3z",key:"n962bs"}],["path",{d:"M11.6 16.8a3 3 0 1 1-5.8-1.6",key:"1yl0tm"}]]);var f=a(71007),v=a(72713);function u(e){let{children:t}=e,[a,u]=(0,i.useState)(""),y=[{items:[{title:"Dashboard",href:"/admin/dashboard",icon:(0,s.jsx)(l.A,{})},{title:"Jobs",href:"/admin/jobs",icon:(0,s.jsx)(r.A,{})},{title:"Applications",href:"/admin/applications",icon:(0,s.jsx)(d.A,{})},{title:"Posts",href:"/admin/posts",icon:(0,s.jsx)(x,{className:"w-5 h-5"})},{title:"Student Management",href:"/admin/student-management",icon:(0,s.jsx)(f.A,{className:"w-5 h-5"})},{title:"Student Analytics",href:"/admin/analytics",icon:(0,s.jsx)(v.A,{className:"w-5 h-5"})},{title:"Company Management",href:"/admin/companymanagement",icon:(0,s.jsx)(o.A,{})},{title:"Forms",href:"/admin/form",icon:(0,s.jsx)(c.A,{})}]}],g=[{title:"My Profile",href:"/admin/profile",icon:(0,s.jsx)(h.A,{})},{title:"Settings",href:"../settings",icon:(0,s.jsx)(m.A,{})},{title:"Contact Support",href:"/admin/helpandsupport",icon:(0,s.jsx)(p,{})}];return(0,i.useEffect)(()=>{let e=localStorage.getItem("collegeName");e&&u(e)},[]),(0,s.jsx)("div",{className:"h-screen bg-gray-50 overflow-hidden",children:(0,s.jsxs)("div",{className:"flex h-full",children:[(0,s.jsx)(n.A,{sections:y,bottomItems:g,defaultExpanded:!1,navbarHeight:"0",className:"z-20"}),(0,s.jsx)("div",{className:"flex-1 p-6 ml-20 overflow-y-auto",children:(0,s.jsx)("div",{className:"bg-white rounded-xl shadow-md p-6 min-h-full text-black",children:t})})]})})}},72713:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},72733:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var s=(0,a(86467).A)("outline","clipboard-list","IconClipboardList",[["path",{d:"M9 5h-2a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-12a2 2 0 0 0 -2 -2h-2",key:"svg-0"}],["path",{d:"M9 3m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z",key:"svg-1"}],["path",{d:"M9 12l.01 0",key:"svg-2"}],["path",{d:"M13 12l2 0",key:"svg-3"}],["path",{d:"M9 16l.01 0",key:"svg-4"}],["path",{d:"M13 16l2 0",key:"svg-5"}]])},83406:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var s=(0,a(86467).A)("outline","forms","IconForms",[["path",{d:"M12 3a3 3 0 0 0 -3 3v12a3 3 0 0 0 3 3",key:"svg-0"}],["path",{d:"M6 3a3 3 0 0 1 3 3v12a3 3 0 0 1 -3 3",key:"svg-1"}],["path",{d:"M13 7h7a1 1 0 0 1 1 1v8a1 1 0 0 1 -1 1h-7",key:"svg-2"}],["path",{d:"M5 7h-1a1 1 0 0 0 -1 1v8a1 1 0 0 0 1 1h1",key:"svg-3"}],["path",{d:"M17 12h.01",key:"svg-4"}],["path",{d:"M13 12h.01",key:"svg-5"}]])},95370:(e,t,a)=>{"use strict";a.d(t,{A:()=>f});var s=a(95155),i=a(12115),n=a(76408),l=a(60760);a(35695);var r=a(6874),d=a.n(r),o=a(54395),c=a(34382),h=a(52596),m=a(39688);function p(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,m.QP)((0,h.$)(t))}let x=(0,i.createContext)();function f(e){let{sections:t=[],bottomItems:a=[],defaultExpanded:r=!1,navbarHeight:h="4rem",className:m}=e,[f,v]=(0,i.useState)(r),[u,y]=(0,i.useState)(!1),[g,j]=(0,i.useState)(!1);(0,i.useEffect)(()=>{let e=()=>{y(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);let b=e=>i.isValidElement(e)?i.cloneElement(e,{className:"w-7 h-7",size:void 0}):e;return(0,s.jsxs)(s.Fragment,{children:[u&&(0,s.jsx)("button",{onClick:()=>j(!g),className:"fixed top-4 left-4 z-50 p-2 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow md:hidden",style:{marginTop:h},children:g?(0,s.jsx)(o.A,{size:24}):(0,s.jsx)(c.A,{size:24})}),(0,s.jsx)(x.Provider,{value:{expanded:f},children:(0,s.jsxs)(n.P.div,{animate:{width:f?"300px":"80px"},onMouseEnter:()=>v(!0),onMouseLeave:()=>v(!1),className:p("fixed top-0 left-0 h-screen bg-white px-4 py-4 shadow-lg rounded-r-3xl flex flex-col justify-between sidebar",u&&!g?"hidden":"",m),style:{marginTop:h},children:[(0,s.jsxs)("nav",{className:"flex flex-col gap-8",children:[(0,s.jsxs)(d(),{href:"/",className:p("flex items-center gap-4 p-3 text-black",!f&&"justify-center"),children:[(0,s.jsx)("div",{className:"flex-shrink-0 text-2xl font-bold",children:f?null:"P"}),f&&(0,s.jsx)(n.P.span,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"text-2xl font-bold whitespace-nowrap",children:"Placeeasy.in"})]}),t.map((e,t)=>(0,s.jsx)("div",{className:"bg-gray-50 rounded-xl p-2",children:(0,s.jsx)(l.N,{children:e.items.map(e=>(0,s.jsxs)(d(),{href:e.href,className:p("flex items-center gap-4 p-3 text-gray-600 hover:bg-blue-50 hover:text-blue-600 rounded-xl transition-colors",!f&&"justify-center"),children:[(0,s.jsx)("div",{className:"flex-shrink-0 flex items-center justify-center",children:b(e.icon)}),f&&(0,s.jsx)(n.P.span,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"text-base font-bold whitespace-nowrap",children:e.title})]},e.title))})},t))]}),a.length>0&&(0,s.jsx)("div",{className:"mt-6",children:a.map(e=>(0,s.jsxs)(d(),{href:e.href,className:p("flex items-center gap-4 p-3 text-gray-600 hover:bg-blue-50 hover:text-blue-600 rounded-xl transition-colors mb-2",!f&&"justify-center"),children:[(0,s.jsx)("div",{className:"flex-shrink-0 flex items-center justify-center",children:b(e.icon)}),f&&(0,s.jsx)(n.P.span,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"text-base font-bold whitespace-nowrap",children:e.title})]},e.title))})]})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6874,4734,8441,1684,7358],()=>t(2142)),_N_E=e.O()}]);