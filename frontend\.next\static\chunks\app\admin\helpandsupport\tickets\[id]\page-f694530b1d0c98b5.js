(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[592],{25082:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>v});var s=a(95155),r=a(12115),n=a(6874),l=a.n(n),c=a(35695),i=a(54765),o=a(50183),d=a(42355),m=a(85339),u=a(81284),h=a(54416),x=a(99890),p=a(50492),g=a(12486),y=a(37533),f=a(6824);let b=[1,2,3,4,5];function j(e){let{open:t,onClose:a,onSubmit:n,loading:l}=e,[c,i]=(0,r.useState)(0),[o,d]=(0,r.useState)(0),[m,u]=(0,r.useState)("");return t?(0,s.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-opacity-30",children:(0,s.jsxs)("div",{className:"bg-white rounded-xl shadow-xl max-w-lg w-full p-8 relative",children:[(0,s.jsx)("button",{className:"absolute top-4 right-4 text-gray-400 hover:text-gray-600 text-2xl",onClick:a,"aria-label":"Close",children:"\xd7"}),(0,s.jsx)("h2",{className:"text-2xl font-bold mb-2",children:"Support Ticket Feedback"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:"Please rate your support experience and provide any additional comments"}),(0,s.jsxs)("form",{onSubmit:e=>{e.preventDefault(),0!==c&&n({rating:c,comment:m})},children:[(0,s.jsx)("div",{className:"flex justify-center mb-6",children:b.map(e=>(0,s.jsx)("button",{type:"button",className:"focus:outline-none",onClick:()=>i(e),onMouseEnter:()=>d(e),onMouseLeave:()=>d(0),"aria-label":"Rate ".concat(e," star").concat(e>1?"s":""),children:(0,s.jsx)("svg",{className:"w-10 h-10 mx-1 ".concat(e<=(o||c)?"text-yellow-400":"text-gray-300"),fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.286 3.967a1 1 0 00.95.69h4.178c.969 0 1.371 1.24.588 1.81l-3.385 2.46a1 1 0 00-.364 1.118l1.287 3.966c.3.922-.755 1.688-1.54 1.118l-3.385-2.46a1 1 0 00-1.175 0l-3.385 2.46c-.784.57-1.838-.196-1.54-1.118l1.287-3.966a1 1 0 00-.364-1.118L2.045 9.394c-.783-.57-.38-1.81.588-1.81h4.178a1 1 0 00.95-.69l1.286-3.967z"})})},e))}),(0,s.jsx)("textarea",{className:"w-full border border-gray-300 rounded-md p-3 mb-6 focus:outline-none focus:ring-2 focus:ring-blue-500",rows:4,placeholder:"Share your experience with our support team...",value:m,onChange:e=>u(e.target.value)}),(0,s.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,s.jsx)("button",{type:"button",className:"px-4 py-2 rounded-md border border-gray-300 bg-white text-gray-700 hover:bg-gray-100",onClick:a,disabled:l,children:"Cancel"}),(0,s.jsx)("button",{type:"submit",className:"px-4 py-2 rounded-md bg-blue-600 text-white font-semibold hover:bg-blue-700 disabled:opacity-60",disabled:l||0===c,children:l?"Submitting...":"Submit Feedback"})]})]})]})}):null}let v=()=>{var e,t,a,n,b,v,w;let N=(0,c.useParams)().id,[k,_]=(0,r.useState)(null),[C,A]=(0,r.useState)(!0),[S,T]=(0,r.useState)([]),[D,I]=(0,r.useState)(!1),[E,L]=(0,r.useState)("comments"),[z,F]=(0,r.useState)(""),[P,R]=(0,r.useState)(null),[M,B]=(0,r.useState)(null),[O,$]=(0,r.useState)(!0),[U,Z]=(0,r.useState)(!1),[W,J]=(0,r.useState)(!1),[K,X]=(0,r.useState)(null),[q,G]=(0,r.useState)(!1),[H,V]=(0,r.useState)(!1),[Y,Q]=(0,r.useState)(!1),ee=(0,r.useRef)(null),et=(0,r.useRef)(null),ea=e=>{if(!e)return"";try{let t=new Date(e);return(0,f.GP)(t,"MMMM d, yyyy 'at' h:mm a")}catch(t){return console.error("Error formatting date:",t),e}},es=e=>{if(!e)return"";try{let t=new Date(e);return(0,f.GP)(t,"h:mm a")}catch(e){return console.error("Error formatting time:",e),""}};(0,r.useEffect)(()=>{(async()=>{try{A(!0),X(null),await y.M$.ensureLoggedIn();let e=await y.yp.getTicket(N);_(e),er(!1)}catch(e){console.error("Error fetching ticket:",e),X("Failed to load ticket. Please try again later.")}finally{A(!1)}})()},[N]);let er=async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{I(!0);let t=await y.yp.getComments(N);console.log("Raw API response:",t);let a=[];Array.isArray(t)?a=t:t&&"object"==typeof t&&(Array.isArray(t.data)?a=t.data:t.comments&&Array.isArray(t.comments)?a=t.comments:t.results&&Array.isArray(t.results)&&(a=t.results));let s=localStorage.getItem("user")||sessionStorage.getItem("user"),r=null;if(s)try{r=JSON.parse(s)}catch(e){console.error("Error parsing current user data:",e)}console.log("Current user data for comment processing:",r),e&&(a=[...S.filter(e=>e._isLocalComment).map(e=>e._originalData),...a]);let n=a.map(e=>{let t=!1;if(console.log("Processing comment for direction:",e),"sent"===e.direction||"sent"===e.type)t=!0,console.log("Comment marked as sent by direction/type");else if("received"===e.direction||"received"===e.type)t=!1,console.log("Comment marked as received by direction/type");else if("admin"===e.sender_type||"admin"===e.from||"admin"===e.sender)t=!0,console.log("Comment marked as sent by sender_type/from/sender = admin");else if("user"===e.sender_type||"user"===e.from||"user"===e.sender)t=!1,console.log("Comment marked as received by sender_type/from/sender = user");else if("admin"===e.created_by||"admin"===e.user_type)t=!0,console.log("Comment marked as sent by created_by/user_type = admin");else if("user"===e.created_by||"user"===e.user_type)t=!1,console.log("Comment marked as received by created_by/user_type = user");else if(console.log("Current user data for this comment:",r),r)e.created_by===r.id||e.user_id===r.id?(t=!0,console.log("Comment marked as sent by current user ID match")):"admin"===r.user_type||"admin"===r.role?(t=!1,console.log("Comment marked as received (admin interface, not from current admin)")):(t=!1,console.log("Comment marked as received (user interface)"));else{let a=["admin@","admin1@","support@","help@"];["Thank you for your feedback","Thank you for your patience","Ticket status changed to","Attached file:"].some(t=>e.content&&e.content.includes(t))||a.some(t=>e.created_by_email&&e.created_by_email.includes(t))||a.some(t=>e.user_email&&e.user_email.includes(t))?(t=!0,console.log("Comment marked as sent by admin pattern detection")):(t=!1,console.log("Comment marked as received (default fallback)"))}let a={id:e.id||e._id||"comment-".concat(Date.now(),"-").concat(Math.random()),content:e.content||e.message||e.text||"",is_sent:t,created_at:e.created_at||e.createdAt||e.timestamp||new Date().toISOString(),attachment:e.attachment||e.file||null,_originalData:e,_isLocalComment:e._isLocalComment||!1};return console.log("Final normalized comment:",a),a});console.log("Normalized comments:",n),T(n)}catch(e){console.error("Error fetching comments:",e)}finally{I(!1)}};(0,r.useEffect)(()=>{et.current&&et.current.scrollIntoView({behavior:"smooth"})},[S]);let en=()=>{R(null),B(null),ee.current&&(ee.current.value="")},el=async()=>{if((z.trim()||P)&&!W)try{J(!0);let e=null;if(z.trim()){e=await y.yp.addComment(N,z.trim()),console.log("New comment created:",e);let t={id:e.id||e._id||Date.now(),content:z.trim(),is_sent:!0,created_at:new Date().toISOString(),_originalData:e,_isLocalComment:!0};T(e=>[...e,t])}if(P){let e=await y.yp.addAttachment(N,P);if(console.log("File uploaded:",e),!z.trim()){let t=await y.yp.addComment(N,"Attached file: ".concat(P.name)),a={id:t.id||Date.now(),content:"Attached file: ".concat(P.name),is_sent:!0,created_at:new Date().toISOString(),attachment:{url:e.url||"#",filename:P.name,type:P.type,size:P.size},_originalData:t,_isLocalComment:!0};T(e=>[...e,a])}}F(""),en(),setTimeout(()=>{er(!0)},1e3)}catch(e){console.error("Error sending message:",e),alert("Failed to send message. Please try again.")}finally{J(!1)}},ec=async e=>{try{await y.yp.updateTicket(N,{status:e}),_(t=>({...t,status:e})),await y.yp.addComment(N,"Ticket status changed to ".concat(e)),er(!1)}catch(e){console.error("Error updating ticket status:",e),alert("Failed to update ticket status. Please try again.")}},ei=async()=>{alert("Reassign functionality would open a user selection modal")},eo=async(e,t)=>{let a="ticket_feedback_".concat(e,"_").concat(t);if(localStorage.getItem(a))return!0;try{let s=await y.yp.checkFeedback(e);if(s&&s.user_id===t)return localStorage.setItem(a,"1"),!0}catch(e){}return!1};(0,r.useEffect)(()=>{if(k&&"resolved"===k.status){var e;let t=localStorage.getItem("user"),a=null;if(t)try{a=JSON.parse(t).id}catch(e){}a&&(null==(e=k.createdBy)?void 0:e.id)===a&&eo(k.id,a).then(e=>{Q(e),G(!e)})}else G(!1)},[k]);let ed=async e=>{let{rating:t,comment:a}=e;V(!0);let s=localStorage.getItem("user"),r=null;if(s)try{r=JSON.parse(s).id}catch(e){}try{await y.yp.submitFeedback(k.id,{rating:t,comment:a}),r&&localStorage.setItem("ticket_feedback_".concat(k.id,"_").concat(r),"1"),Q(!0),G(!1)}catch(e){alert("Failed to submit feedback. Please try again.")}finally{V(!1)}};return((0,r.useEffect)(()=>{let e=setInterval(async()=>{try{let e=await y.yp.getComments(N),t=[];Array.isArray(e)?t=e:e&&"object"==typeof e&&(Array.isArray(e.data)?t=e.data:e.comments&&Array.isArray(e.comments)?t=e.comments:e.results&&Array.isArray(e.results)&&(t=e.results));let a=t.map(e=>{let t=!1;return"sent"===e.direction||"sent"===e.type?t=!0:"received"===e.direction||"received"===e.type?t=!1:"admin"===e.sender_type||"admin"===e.from||"admin"===e.sender?t=!0:"user"===e.sender_type||"user"===e.from||"user"===e.sender?t=!1:"admin"===e.created_by||"admin"===e.user_type?t=!0:("user"===e.created_by||"user"===e.user_type)&&(t=!1),{id:e.id||e._id||"comment-".concat(Date.now(),"-").concat(Math.random()),content:e.content||e.message||e.text||"",is_sent:t,created_at:e.created_at||e.createdAt||e.timestamp||new Date().toISOString(),attachment:e.attachment||e.file||null,_originalData:e,_isLocalComment:e._isLocalComment||!1}});T(e=>{let t=new Set(e.map(e=>e.id)),s=a.filter(e=>!t.has(e.id));return 0===s.length?e:[...e,...s].sort((e,t)=>new Date(e.created_at)-new Date(t.created_at))})}catch(e){}},3e3);return()=>clearInterval(e)},[N]),C)?(0,s.jsx)("div",{className:"space-y-6 pb-8",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(o.$,{variant:"outline",size:"icon",asChild:!0,children:(0,s.jsx)(l(),{href:"/admin/helpandsupport/tickets",children:(0,s.jsx)(d.A,{className:"h-4 w-4"})})}),(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Loading..."})]})}):K?(0,s.jsxs)("div",{className:"space-y-6 pb-8",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(o.$,{variant:"outline",size:"icon",asChild:!0,children:(0,s.jsx)(l(),{href:"/admin/helpandsupport/tickets",children:(0,s.jsx)(d.A,{className:"h-4 w-4"})})}),(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Error"})]}),(0,s.jsx)(i.Zp,{children:(0,s.jsxs)(i.Wu,{className:"p-6 text-center",children:[(0,s.jsx)(m.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-500",children:K}),(0,s.jsx)(o.$,{className:"mt-4",asChild:!0,children:(0,s.jsx)(l(),{href:"/admin/helpandsupport/tickets",children:"Back to Tickets"})})]})})]}):k?(0,s.jsxs)("div",{className:"space-y-6 pb-8 mt-16",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between sm:items-center gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(o.$,{variant:"outline",size:"icon",asChild:!0,children:(0,s.jsx)(l(),{href:"/admin/helpandsupport",children:(0,s.jsx)(d.A,{className:"h-4 w-4"})})}),(0,s.jsxs)("h1",{className:"text-2xl font-bold tracking-tight truncate",children:["Ticket #",k.id]})]}),(0,s.jsx)("span",{className:"px-2 py-1 text-sm bg-blue-100 text-blue-800 rounded-md font-medium",children:k.status})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,s.jsx)("div",{className:"lg:col-span-3 space-y-6",children:(0,s.jsx)(i.Zp,{className:"shadow-md",children:(0,s.jsxs)("div",{className:"flex flex-col h-[680px]",children:[(0,s.jsx)("div",{className:"p-4",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("h3",{className:"font-medium text-lg",children:"Conversation"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)(o.$,{variant:"outline",size:"sm",className:"flex items-center gap-1 text-xs",onClick:()=>Z(!U),children:[(0,s.jsx)(u.A,{className:"h-4 w-4"}),"Ticket Details"]}),U&&(0,s.jsxs)("div",{className:"absolute top-full left-0 mt-2 z-50 w-80 bg-white rounded-lg shadow-lg border border-gray-200 p-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,s.jsx)("h4",{className:"font-bold text-lg",children:k.title}),(0,s.jsx)("button",{onClick:()=>Z(!1),className:"text-gray-400 hover:text-gray-600",children:(0,s.jsx)(h.A,{className:"h-4 w-4"})})]}),(0,s.jsxs)("div",{className:"mb-3 flex gap-2",children:[(0,s.jsx)("span",{className:"px-2 py-0.5 text-xs bg-blue-100 text-blue-800 rounded-full font-medium",children:k.priority}),(0,s.jsx)("span",{className:"px-2 py-0.5 text-xs bg-purple-100 text-purple-800 rounded-full font-medium",children:k.category})]}),(0,s.jsx)("p",{className:"text-sm text-gray-700 mb-3 whitespace-pre-line",children:k.description}),(0,s.jsxs)("div",{className:"text-xs text-gray-500 border-t pt-2 mt-2",children:[(0,s.jsxs)("p",{children:["Created: ",ea(k.createdAt)]}),(0,s.jsxs)("p",{children:["Assigned to: ",(null==(e=k.assignedTo)?void 0:e.name)||"admin"]})]})]})]})]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)("button",{className:"text-xs bg-gray-100 px-3 py-1.5 rounded-full text-gray-600 hover:bg-gray-200 transition",onClick:()=>{F("Thank you for your feedback")},children:"Thank you for your feedback"}),(0,s.jsx)("button",{className:"text-xs bg-blue-100 text-blue-700 px-3 py-1.5 rounded-full hover:bg-blue-200 transition",onClick:()=>{F("Thank you for your patience")},children:"Thank you for your patience"})]})]})}),(0,s.jsxs)("div",{className:"flex-1 p-6 space-y-6 overflow-y-auto ",children:[(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsxs)("div",{className:"bg-gray-100 text-gray-700 px-4 py-2 rounded-lg text-sm max-w-md text-center",children:[(0,s.jsxs)("p",{children:["Ticket #",k.id," opened - ",k.title]}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:'Click on "Ticket Details" to view more information'})]})}),D?(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)("div",{className:"bg-gray-100 text-gray-700 px-4 py-2 rounded-lg text-sm",children:"Loading comments..."})}):S&&S.length>0?[...S].sort((e,t)=>new Date(e.created_at)-new Date(t.created_at)).map((e,t)=>{var a;console.log("Rendering comment ".concat(t,":"),e),console.log("Original data for comment ".concat(t,":"),e._originalData);let r=e.is_sent?"justify-end":"justify-start";return console.log("Comment ".concat(t," direction:"),r,"is_sent:",e.is_sent),(0,s.jsx)("div",{className:"flex ".concat(r),children:(0,s.jsxs)("div",{className:"".concat(e.is_sent?"bg-blue-500 text-white":"bg-white border border-gray-200 shadow-sm"," px-4 py-3 rounded-lg max-w-md"),children:[(0,s.jsx)("p",{children:e.content}),(0,s.jsx)("div",{className:"text-xs ".concat(e.is_sent?"text-blue-100":"text-gray-400"," mt-1 text-right"),children:es(e.created_at||e.createdAt)}),e.attachment&&(0,s.jsx)("div",{className:"mt-2 p-1 rounded ".concat(e.is_sent?"bg-white":"bg-gray-100"),children:(null==(a=e.attachment.type)?void 0:a.startsWith("image/"))?(0,s.jsx)("img",{src:e.attachment.url,alt:"Attachment",className:"rounded max-w-xs w-full h-auto"}):(0,s.jsxs)("div",{className:"flex items-center p-2 rounded bg-gray-100",children:[(0,s.jsx)(x.A,{className:"h-6 w-6 text-blue-600 mr-2"}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("div",{className:"text-sm font-medium",children:e.attachment.filename}),(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:[e.attachment.type," • ",Math.round(e.attachment.size/1024)," KB"]})]}),(0,s.jsx)("a",{href:e.attachment.url,target:"_blank",rel:"noopener noreferrer",className:"text-xs text-blue-600 hover:text-blue-800",children:"Download"})]})})]})},e.id||t)}):(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)("div",{className:"bg-gray-100 text-gray-700 px-4 py-2 rounded-lg text-sm",children:"No comments yet. Start the conversation!"})}),(0,s.jsx)("div",{ref:et})]}),P&&(0,s.jsx)("div",{className:"p-4 bg-blue-50 border-t border-blue-100",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[M?(0,s.jsx)("div",{className:"w-16 h-16 rounded border overflow-hidden mr-3 bg-white flex items-center justify-center",children:(0,s.jsx)("img",{src:M,alt:"Preview",className:"max-w-full max-h-full"})}):(0,s.jsx)("div",{className:"w-16 h-16 rounded border bg-gray-100 flex items-center justify-center mr-3",children:(0,s.jsx)(x.A,{className:"h-8 w-8 text-gray-400"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium truncate",children:P.name}),(0,s.jsxs)("p",{className:"text-xs text-gray-500",children:[(P.size/1024/1024).toFixed(2)," MB"]})]})]}),(0,s.jsx)("button",{onClick:en,className:"p-1 hover:bg-blue-100 rounded-full",children:(0,s.jsx)(h.A,{className:"h-5 w-5 text-blue-600"})})]})}),(0,s.jsxs)("div",{className:"p-4 bg-white",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("button",{onClick:()=>ee.current.click(),className:"p-2 hover:bg-gray-100 rounded-full text-gray-500 hover:text-blue-600 transition-colors",title:"Attach file",children:(0,s.jsx)(p.A,{className:"w-5 h-5"})}),(0,s.jsx)("input",{type:"text",value:z,onChange:e=>F(e.target.value),placeholder:"Type your message...",className:"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),el())},disabled:W}),(0,s.jsx)("button",{className:"".concat(W?"bg-blue-300":"bg-blue-500 hover:bg-blue-600"," text-white p-2 rounded-lg transition-colors flex items-center justify-center"),onClick:el,disabled:W,children:(0,s.jsx)(g.A,{className:"w-5 h-5"})}),(0,s.jsx)("input",{ref:ee,type:"file",className:"hidden",onChange:e=>{let t=e.target.files[0];if(t)if(R(t),t.type.startsWith("image/")){let e=new FileReader;e.onloadend=()=>{B(e.result)},e.readAsDataURL(t)}else B(null)},accept:"image/*,.pdf,.doc,.docx,.xls,.xlsx,.txt"})]}),(0,s.jsx)("div",{className:"mt-2 text-xs text-gray-500 pl-2",children:"Supported formats: Images, PDF, DOC, XLSX, TXT (Max: 10MB)"})]})]})})}),(0,s.jsx)("div",{className:"lg:col-span-1",children:(0,s.jsxs)(i.Zp,{className:"sticky top-6",children:[(0,s.jsx)(i.aR,{className:"pb-2",children:(0,s.jsx)(i.ZB,{className:"text-lg font-medium",children:"Ticket Information"})}),(0,s.jsxs)(i.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-500 mb-1",children:"Submitted By"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"h-6 w-6 rounded-full bg-gray-200 flex items-center justify-center text-xs",children:(null==(a=k.createdBy)||null==(t=a.name)?void 0:t.charAt(0))||"a"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:(null==(n=k.createdBy)?void 0:n.name)||"admin1"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-500 mb-1",children:"Assigned To"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"h-6 w-6 rounded-full bg-gray-200 flex items-center justify-center text-xs",children:(null==(v=k.assignedTo)||null==(b=v.name)?void 0:b.charAt(0))||"a"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:(null==(w=k.assignedTo)?void 0:w.name)||"admin"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-500 mb-1",children:"Created At"}),(0,s.jsx)("p",{className:"text-sm",children:ea(k.createdAt)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-500 mb-1",children:"Last Updated"}),(0,s.jsx)("p",{className:"text-sm",children:ea(k.updatedAt)})]}),(0,s.jsxs)("div",{className:"pt-2",children:[(0,s.jsx)(o.$,{className:"w-full",onClick:()=>ec("open"===k.status?"resolved":"open"),children:"open"===k.status?"Mark as Resolved":"Reopen Ticket"}),(0,s.jsx)(o.$,{variant:"outline",className:"w-full mt-2",onClick:ei,children:"Reassign Ticket"})]})]})]})})]}),(0,s.jsx)(j,{open:q,onClose:()=>G(!1),onSubmit:ed,loading:H})]}):(0,s.jsxs)("div",{className:"space-y-6 pb-8",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(o.$,{variant:"outline",size:"icon",asChild:!0,children:(0,s.jsx)(l(),{href:"/admin/helpandsupport/tickets",children:(0,s.jsx)(d.A,{className:"h-4 w-4"})})}),(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Ticket Not Found"})]}),(0,s.jsx)(i.Zp,{children:(0,s.jsxs)(i.Wu,{className:"p-6 text-center",children:[(0,s.jsx)("p",{className:"text-gray-500",children:"The requested ticket could not be found."}),(0,s.jsx)(o.$,{className:"mt-4",asChild:!0,children:(0,s.jsx)(l(),{href:"/admin/helpandsupport/tickets",children:"Back to Tickets"})})]})})]})}},37533:(e,t,a)=>{"use strict";a.d(t,{M$:()=>i,yp:()=>c});var s=a(23464);let r="http://localhost:5001/api",n=s.A.create({baseURL:r,headers:{"Content-Type":"application/json"},withCredentials:!1});n.interceptors.request.use(e=>{let t=localStorage.getItem("token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e}),n.interceptors.response.use(e=>{var t;return console.group("Help & Support API Response"),console.log("URL:",e.config.url),console.log("Method:",null==(t=e.config.method)?void 0:t.toUpperCase()),console.log("Status:",e.status),console.log("Response Data:",e.data),console.groupEnd(),e},e=>{throw e.response&&401===e.response.status&&console.warn("Authentication failed. You may need to log in again."),console.error("Help & Support API Error:",e),e});let l=async(e,t)=>{try{let a=new FormData;a.append("username",e),a.append("password",t);let{access_token:n,user:l}=(await s.A.post("".concat(r,"/auth/login"),a,{headers:{"Content-Type":"application/x-www-form-urlencoded"}})).data;return localStorage.setItem("token",n),localStorage.setItem("user",JSON.stringify(l)),{token:n,user:l}}catch(e){var a,n;throw console.error("Login failed:",e),alert("Login failed: "+((null==(n=e.response)||null==(a=n.data)?void 0:a.message)||"Invalid credentials")),e}},c={createTicket:async e=>{try{return(await n.post("/tickets",e)).data}catch(e){throw console.error("Error creating ticket:",e),e}},getTickets:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{let t=new URLSearchParams;e.page&&t.append("page",e.page),e.page_size&&t.append("limit",e.page_size),e.skip&&t.append("skip",e.skip),e.status&&"all"!==e.status&&t.append("status",e.status.replace("-","_")),e.search&&t.append("search",e.search),e.category&&t.append("category",e.category),e.priority&&t.append("priority",e.priority),e.user_id&&t.append("user_id",e.user_id);let a=t.toString();return(await n.get("/tickets".concat(a?"?".concat(a):""))).data}catch(e){throw console.error("Error fetching tickets:",e),e}},getTicket:async e=>{try{let t=(await n.get("/tickets/".concat(e))).data;return{...t,createdBy:t.created_by||t.createdBy,assignedTo:t.assigned_to||t.assignedTo,createdAt:new Date(t.created_at||t.createdAt),updatedAt:new Date(t.updated_at||t.updatedAt)}}catch(t){throw console.error("Error fetching ticket ".concat(e,":"),t),t}},updateTicket:async(e,t)=>{try{let a={...t};return a.status&&(a.status=a.status.replace("-","_")),(await n.put("/tickets/".concat(e),a)).data}catch(t){throw console.error("Error updating ticket ".concat(e,":"),t),t}},addComment:async(e,t)=>{try{return(await n.post("/tickets/".concat(e,"/comments"),{content:t})).data}catch(t){throw console.error("Error adding comment to ticket ".concat(e,":"),t),t}},getComments:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let a=new URLSearchParams;t.page&&a.append("page",t.page),t.page_size&&a.append("limit",t.page_size),t.skip&&a.append("skip",t.skip);let s=a.toString(),r="/tickets/".concat(e,"/comments").concat(s?"?".concat(s):"");return(await n.get(r)).data}catch(t){throw console.error("Error fetching comments for ticket ".concat(e,":"),t),t}},addAttachment:async(e,t)=>{try{let a=new FormData;return a.append("file",t),(await n.post("/tickets/".concat(e,"/attachments"),a,{headers:{"Content-Type":"multipart/form-data"}})).data}catch(t){throw console.error("Error adding attachment to ticket ".concat(e,":"),t),t}},submitFeedback:async(e,t)=>{try{return(await n.post("/tickets/".concat(e,"/feedback"),t)).data}catch(t){throw console.error("Error submitting feedback for ticket ".concat(e,":"),t),t}},checkFeedback:async e=>{try{return(await n.get("/tickets/".concat(e,"/feedback"))).data}catch(t){if(t.response&&404===t.response.status)return null;throw console.error("Error checking feedback for ticket ".concat(e,":"),t),t}},getAllFeedback:async()=>{try{return(await n.get("/feedback")).data}catch(e){throw console.error("Error fetching all feedback:",e),e}}},i={defaultCredentials:{email:"<EMAIL>",password:"admin123"},login:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,a=e||i.defaultCredentials.email,s=t||i.defaultCredentials.password;return console.log("Attempting login with: ".concat(a)),l(a,s)},autoLogin:async()=>{try{console.log("Auto-logging in with default credentials...");let e=await i.login();return console.log("Auto-login successful"),e}catch(e){throw console.error("Auto-login failed:",e),e}},logout:()=>{localStorage.removeItem("token"),localStorage.removeItem("user")},getProfile:async()=>(await n.get("/auth/me")).data,ensureLoggedIn:async()=>i.autoLogin()};i.ensureLoggedIn().catch(e=>console.warn("Initial auto-login failed, will retry when API is used"))},50183:(e,t,a)=>{"use strict";a.d(t,{$:()=>n});var s=a(95155),r=a(12115);let n=e=>{let{children:t,variant:a="default",size:n="default",className:l="",asChild:c=!1,...i}=e,o={default:"bg-blue-600 text-white hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700",outline:"border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200",ghost:"hover:bg-gray-100 hover:text-gray-900",link:"text-blue-600 underline-offset-4 hover:underline"},d={default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"},m=o[a]||o.default,u=d[n]||d.default,h="".concat("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50"," ").concat(m," ").concat(u," ").concat(l);return c?r.cloneElement(t,{className:h,...i}):(0,s.jsx)("button",{className:h,...i,children:t})}},53432:(e,t,a)=>{Promise.resolve().then(a.bind(a,25082))},54765:(e,t,a)=>{"use strict";a.d(t,{BT:()=>c,Wu:()=>i,ZB:()=>l,Zp:()=>r,aR:()=>n});var s=a(95155);a(12115);let r=e=>{let{children:t,className:a="",...r}=e;return(0,s.jsx)("div",{className:"bg-white rounded-lg border border-gray-200 shadow-sm ".concat(a),...r,children:t})},n=e=>{let{children:t,className:a="",...r}=e;return(0,s.jsx)("div",{className:"p-6 pb-4 ".concat(a),...r,children:t})},l=e=>{let{children:t,className:a="",...r}=e;return(0,s.jsx)("h3",{className:"text-lg font-semibold leading-none tracking-tight ".concat(a),...r,children:t})},c=e=>{let{children:t,className:a="",...r}=e;return(0,s.jsx)("p",{className:"text-sm text-gray-600 mt-1.5 ".concat(a),...r,children:t})},i=e=>{let{children:t,className:a="",...r}=e;return(0,s.jsx)("div",{className:"p-6 pt-0 ".concat(a),...r,children:t})}}},e=>{var t=t=>e(e.s=t);e.O(0,[3464,6874,8384,7201,8441,1684,7358],()=>t(53432)),_N_E=e.O()}]);