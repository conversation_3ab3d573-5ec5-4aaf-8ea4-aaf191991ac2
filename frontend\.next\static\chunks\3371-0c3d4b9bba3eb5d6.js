"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3371],{28835:(e,t,r)=>{function a(){try{let e=localStorage.getItem("access");if(!e)return null;let t=localStorage.getItem("user");if(t){let e=JSON.parse(t);return e.id||e.user_id||null}if(e)try{let t=e.split(".")[1],r=JSON.parse(atob(t));return r.user_id||r.id||r.sub||null}catch(e){console.error("Error decoding JWT token:",e)}return null}catch(e){return console.error("Error getting user ID:",e),null}}function n(){return localStorage.getItem("access")}function o(){try{let e=localStorage.getItem("user");return e?JSON.parse(e):null}catch(e){return console.error("Error getting user data:",e),null}}r.d(t,{F6:()=>a,c4:()=>n,gL:()=>o})},31199:(e,t,r)=>{r.d(t,{A:()=>l});var a=r(95155);r(12115);var n=r(6874),o=r.n(n),c=r(54765),s=r(29344);let l=e=>{let{ticket:t}=e,r=()=>{let e=t.createdBy||t.created_by;return e&&(e.name||e.username||e.email)||"Unknown User"};return(0,a.jsx)(o(),{href:"/admin/helpandsupport/tickets/".concat(t.id),children:(0,a.jsx)(c.Zp,{className:"h-full hover:shadow-md transition-shadow",children:(0,a.jsx)(c.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h3",{className:"text-base font-medium text-gray-900 line-clamp-1 mb-3 mt-6",children:t.title}),(0,a.jsxs)("div",{className:"flex gap-2 flex-wrap",children:[(0,a.jsx)("span",{className:"px-2 py-1 rounded-md text-xs font-medium ".concat((e=>{switch(e){case"high":return"bg-red-100 text-red-800";case"medium":return"bg-orange-100 text-orange-800";default:return"bg-gray-100 text-gray-800"}})(t.priority)),children:t.priority}),(0,a.jsx)("span",{className:"px-2 py-1 rounded-md text-xs font-medium ".concat((e=>{switch(e){case"open":return"bg-blue-100 text-blue-800";case"in-progress":return"bg-yellow-100 text-yellow-800";case"resolved":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}})(t.status)),children:t.status.replace("-"," ")}),(0,a.jsx)("span",{className:"px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700",children:t.category.replace("-"," ")})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 line-clamp-2",children:t.description}),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-2 border-t border-gray-100",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-xs text-gray-500",children:[(0,a.jsx)("div",{className:"h-5 w-5 rounded-full bg-gray-200 flex items-center justify-center text-xs font-medium",children:r().charAt(0).toUpperCase()}),(0,a.jsx)("span",{children:r()})]}),(0,a.jsx)("div",{className:"text-xs text-gray-400",children:(e=>{try{let t=new Date(e);if(isNaN(t.getTime()))return"Invalid date";return(0,s.m)(t,{addSuffix:!0})}catch(e){return"Invalid date"}})(t.createdAt||t.created_at||t.created_at_date)})]})]})})})})}},37533:(e,t,r)=>{r.d(t,{M$:()=>l,yp:()=>s});var a=r(23464);let n="http://localhost:5001/api",o=a.A.create({baseURL:n,headers:{"Content-Type":"application/json"},withCredentials:!1});o.interceptors.request.use(e=>{let t=localStorage.getItem("token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e}),o.interceptors.response.use(e=>{var t;return console.group("Help & Support API Response"),console.log("URL:",e.config.url),console.log("Method:",null==(t=e.config.method)?void 0:t.toUpperCase()),console.log("Status:",e.status),console.log("Response Data:",e.data),console.groupEnd(),e},e=>{throw e.response&&401===e.response.status&&console.warn("Authentication failed. You may need to log in again."),console.error("Help & Support API Error:",e),e});let c=async(e,t)=>{try{let r=new FormData;r.append("username",e),r.append("password",t);let{access_token:o,user:c}=(await a.A.post("".concat(n,"/auth/login"),r,{headers:{"Content-Type":"application/x-www-form-urlencoded"}})).data;return localStorage.setItem("token",o),localStorage.setItem("user",JSON.stringify(c)),{token:o,user:c}}catch(e){var r,o;throw console.error("Login failed:",e),alert("Login failed: "+((null==(o=e.response)||null==(r=o.data)?void 0:r.message)||"Invalid credentials")),e}},s={createTicket:async e=>{try{return(await o.post("/tickets",e)).data}catch(e){throw console.error("Error creating ticket:",e),e}},getTickets:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{let t=new URLSearchParams;e.page&&t.append("page",e.page),e.page_size&&t.append("limit",e.page_size),e.skip&&t.append("skip",e.skip),e.status&&"all"!==e.status&&t.append("status",e.status.replace("-","_")),e.search&&t.append("search",e.search),e.category&&t.append("category",e.category),e.priority&&t.append("priority",e.priority),e.user_id&&t.append("user_id",e.user_id);let r=t.toString();return(await o.get("/tickets".concat(r?"?".concat(r):""))).data}catch(e){throw console.error("Error fetching tickets:",e),e}},getTicket:async e=>{try{let t=(await o.get("/tickets/".concat(e))).data;return{...t,createdBy:t.created_by||t.createdBy,assignedTo:t.assigned_to||t.assignedTo,createdAt:new Date(t.created_at||t.createdAt),updatedAt:new Date(t.updated_at||t.updatedAt)}}catch(t){throw console.error("Error fetching ticket ".concat(e,":"),t),t}},updateTicket:async(e,t)=>{try{let r={...t};return r.status&&(r.status=r.status.replace("-","_")),(await o.put("/tickets/".concat(e),r)).data}catch(t){throw console.error("Error updating ticket ".concat(e,":"),t),t}},addComment:async(e,t)=>{try{return(await o.post("/tickets/".concat(e,"/comments"),{content:t})).data}catch(t){throw console.error("Error adding comment to ticket ".concat(e,":"),t),t}},getComments:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let r=new URLSearchParams;t.page&&r.append("page",t.page),t.page_size&&r.append("limit",t.page_size),t.skip&&r.append("skip",t.skip);let a=r.toString(),n="/tickets/".concat(e,"/comments").concat(a?"?".concat(a):"");return(await o.get(n)).data}catch(t){throw console.error("Error fetching comments for ticket ".concat(e,":"),t),t}},addAttachment:async(e,t)=>{try{let r=new FormData;return r.append("file",t),(await o.post("/tickets/".concat(e,"/attachments"),r,{headers:{"Content-Type":"multipart/form-data"}})).data}catch(t){throw console.error("Error adding attachment to ticket ".concat(e,":"),t),t}},submitFeedback:async(e,t)=>{try{return(await o.post("/tickets/".concat(e,"/feedback"),t)).data}catch(t){throw console.error("Error submitting feedback for ticket ".concat(e,":"),t),t}},checkFeedback:async e=>{try{return(await o.get("/tickets/".concat(e,"/feedback"))).data}catch(t){if(t.response&&404===t.response.status)return null;throw console.error("Error checking feedback for ticket ".concat(e,":"),t),t}},getAllFeedback:async()=>{try{return(await o.get("/feedback")).data}catch(e){throw console.error("Error fetching all feedback:",e),e}}},l={defaultCredentials:{email:"<EMAIL>",password:"admin123"},login:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=e||l.defaultCredentials.email,a=t||l.defaultCredentials.password;return console.log("Attempting login with: ".concat(r)),c(r,a)},autoLogin:async()=>{try{console.log("Auto-logging in with default credentials...");let e=await l.login();return console.log("Auto-login successful"),e}catch(e){throw console.error("Auto-login failed:",e),e}},logout:()=>{localStorage.removeItem("token"),localStorage.removeItem("user")},getProfile:async()=>(await o.get("/auth/me")).data,ensureLoggedIn:async()=>l.autoLogin()};l.ensureLoggedIn().catch(e=>console.warn("Initial auto-login failed, will retry when API is used"))},50183:(e,t,r)=>{r.d(t,{$:()=>o});var a=r(95155),n=r(12115);let o=e=>{let{children:t,variant:r="default",size:o="default",className:c="",asChild:s=!1,...l}=e,i={default:"bg-blue-600 text-white hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700",outline:"border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200",ghost:"hover:bg-gray-100 hover:text-gray-900",link:"text-blue-600 underline-offset-4 hover:underline"},d={default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"},u=i[r]||i.default,g=d[o]||d.default,p="".concat("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50"," ").concat(u," ").concat(g," ").concat(c);return s?n.cloneElement(t,{className:p,...l}):(0,a.jsx)("button",{className:p,...l,children:t})}},54765:(e,t,r)=>{r.d(t,{BT:()=>s,Wu:()=>l,ZB:()=>c,Zp:()=>n,aR:()=>o});var a=r(95155);r(12115);let n=e=>{let{children:t,className:r="",...n}=e;return(0,a.jsx)("div",{className:"bg-white rounded-lg border border-gray-200 shadow-sm ".concat(r),...n,children:t})},o=e=>{let{children:t,className:r="",...n}=e;return(0,a.jsx)("div",{className:"p-6 pb-4 ".concat(r),...n,children:t})},c=e=>{let{children:t,className:r="",...n}=e;return(0,a.jsx)("h3",{className:"text-lg font-semibold leading-none tracking-tight ".concat(r),...n,children:t})},s=e=>{let{children:t,className:r="",...n}=e;return(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1.5 ".concat(r),...n,children:t})},l=e=>{let{children:t,className:r="",...n}=e;return(0,a.jsx)("div",{className:"p-6 pt-0 ".concat(r),...n,children:t})}}}]);