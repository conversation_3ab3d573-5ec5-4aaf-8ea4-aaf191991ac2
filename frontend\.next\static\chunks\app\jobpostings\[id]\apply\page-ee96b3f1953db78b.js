(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4458],{1243:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},4516:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},14186:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},17576:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},17580:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},19946:(e,t,a)=>{"use strict";a.d(t,{A:()=>m});var s=a(12115);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,a)=>a?a.toUpperCase():t.toLowerCase()),l=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return t.filter((e,t,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===t).join(" ").trim()},c=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,s.forwardRef)((e,t)=>{let{color:a="currentColor",size:r=24,strokeWidth:i=2,absoluteStrokeWidth:l,className:d="",children:m,iconNode:p,...h}=e;return(0,s.createElement)("svg",{ref:t,...o,width:r,height:r,stroke:a,strokeWidth:l?24*Number(i)/Number(r):i,className:n("lucide",d),...!m&&!c(h)&&{"aria-hidden":"true"},...h},[...p.map(e=>{let[t,a]=e;return(0,s.createElement)(t,a)}),...Array.isArray(m)?m:[m]])}),m=(e,t)=>{let a=(0,s.forwardRef)((a,i)=>{let{className:c,...o}=a;return(0,s.createElement)(d,{ref:i,iconNode:t,className:n("lucide-".concat(r(l(e))),"lucide-".concat(e),c),...o})});return a.displayName=l(e),a}},23227:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},29869:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},33786:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},34842:(e,t,a)=>{"use strict";a.d(t,{G$:()=>n,N6:()=>r,Om:()=>d,T4:()=>m,YQ:()=>l,_S:()=>c,lh:()=>o,vr:()=>i});var s=a(37719);function r(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.page&&t.append("page",e.page),e.per_page&&t.append("per_page",e.per_page),e.job_type&&"ALL"!==e.job_type&&t.append("job_type",e.job_type),e.location&&"ALL"!==e.location&&t.append("location",e.location),e.salary_min&&t.append("salary_min",e.salary_min),e.search&&t.append("search",e.search);let a=t.toString();return s.A.get("/api/v1/college/default-college/jobs/".concat(a?"?".concat(a):""))}function i(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!Object.values(a).some(e=>e instanceof File))return s.A.post("/api/v1/college/default-college/jobs/".concat(e,"/apply/"),{cover_letter:t,additional_field_responses:a});{let r=new FormData;return r.append("cover_letter",t),Object.entries(a).forEach(e=>{let[t,a]=e;a instanceof File?r.append(t,a):r.append(t,JSON.stringify(a))}),s.A.post("/api/v1/college/default-college/jobs/".concat(e,"/apply/"),r,{headers:{"Content-Type":"multipart/form-data"}})}}function l(e){return s.A.get("/api/v1/college/default-college/jobs/".concat(e,"/"))}function n(){return s.A.get("/api/v1/college/default-college/jobs/applied/")}function c(e){return s.A.post("/api/v1/college/default-college/jobs/create/",e)}function o(e,t){return s.A.put("/api/v1/college/default-college/jobs/".concat(e,"/"),t)}function d(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.page&&t.append("page",e.page),e.per_page&&t.append("per_page",e.per_page),e.search&&t.append("search",e.search),e.type&&"All"!==e.type&&t.append("job_type",e.type),e.minCTC&&t.append("salary_min",e.minCTC),e.maxCTC&&t.append("salary_max",e.maxCTC),e.minStipend&&t.append("stipend_min",e.minStipend),e.maxStipend&&t.append("stipend_max",e.maxStipend),e.location&&t.append("location",e.location),void 0!==e.is_published&&t.append("is_published",e.is_published),e.company_id&&t.append("company_id",e.company_id),e.company_name&&t.append("company_name",e.company_name);let a=t.toString(),r="/api/v1/college/default-college/jobs/admin/".concat(a?"?".concat(a):"");return console.log("\uD83C\uDF10 listJobsAdmin calling URL:",r,"with params:",e),s.A.get(r).then(e=>{var t,a,s,r,i,l;return console.log("\uD83C\uDF10 listJobsAdmin response:",{status:e.status,totalJobs:(null==(a=e.data)||null==(t=a.pagination)?void 0:t.total_count)||0,currentPage:(null==(r=e.data)||null==(s=r.pagination)?void 0:s.current_page)||1,totalPages:(null==(l=e.data)||null==(i=l.pagination)?void 0:i.total_pages)||1}),e}).catch(e=>{var t;throw console.error("\uD83C\uDF10 listJobsAdmin error:",e),console.error("\uD83C\uDF10 listJobsAdmin error response:",null==(t=e.response)?void 0:t.data),e})}function m(e){return s.A.patch("/api/v1/jobs/".concat(e,"/toggle-publish/"))}},35169:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},35695:(e,t,a)=>{"use strict";var s=a(18999);a.o(s,"useParams")&&a.d(t,{useParams:function(){return s.useParams}}),a.o(s,"usePathname")&&a.d(t,{usePathname:function(){return s.usePathname}}),a.o(s,"useRouter")&&a.d(t,{useRouter:function(){return s.useRouter}}),a.o(s,"useSearchParams")&&a.d(t,{useSearchParams:function(){return s.useSearchParams}})},37719:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var s=a(23464);a(73983);let r=s.A.create({baseURL:"http://127.0.0.1:8000",headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>{let t=localStorage.getItem("access_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,async e=>{var t;let a=e.config;if((null==(t=e.response)?void 0:t.status)===401&&!a._retry){a._retry=!0;try{let e=localStorage.getItem("refresh_token");if(e){let t=await s.A.post("http://127.0.0.1:8000/api/auth/token/refresh/",{refresh:e});return localStorage.setItem("access_token",t.data.access),a.headers.Authorization="Bearer ".concat(t.data.access),r(a)}}catch(e){console.error("Error refreshing token:",e),localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),window.location.href="/login"}}return Promise.reject(e)});let i=r},40646:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},47317:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>T});var s=a(95155),r=a(12115),i=a(35695),l=a(23227),n=a(17576),c=a(55868),o=a(4516),d=a(69074),m=a(14186),p=a(57434),h=a(17580),u=a(69037),x=a(40646),g=a(85339),f=a(35169),y=a(34842),b=a(37719),j=a(73983);let v={basic:{first_name:"First Name",last_name:"Last Name",email:"Email Address",phone:"Phone Number",date_of_birth:"Date of Birth"},academic:{student_id:"Student ID/Roll Number",branch:"Department/Branch",gpa:"CGPA/GPA",joining_year:"Joining Year",passout_year:"Passout Year"},contact:{address:"Address",city:"City",state:"State",pincode:"PIN Code"},documents:{resume:"Resume",tenth_certificate:"Class 10 Certificate",twelfth_certificate:"Class 12 Certificate"},education:{tenth_percentage:"Class 10 Percentage",twelfth_percentage:"Class 12 Percentage",tenth_year_of_passing:"Class 10 Year of Passing",twelfth_year_of_passing:"Class 12 Year of Passing"}},N=["first_name","last_name","email","phone","student_id","branch","gpa","resume"],w=e=>{let t=[],a=[],s=[];if(!e)return{isValid:!1,missing:["Profile not found"],warnings:[],errors:["Please complete your profile before applying"],score:0};if(N.forEach(a=>{let s=e[a],r=A(a);s&&("string"!=typeof s||""!==s.trim())||t.push(r)}),e.gpa){let t=parseFloat(e.gpa);isNaN(t)||t<0||t>10?s.push("CGPA must be between 0 and 10"):t<6&&a.push("CGPA below 6.0 may limit job opportunities")}e.phone&&(/^[6-9]\d{9}$/.test(e.phone.replace(/[^\d]/g,""))||s.push("Phone number must be a valid 10-digit Indian mobile number")),e.email&&(/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.email)||s.push("Please provide a valid email address")),e.resume||e.resume_url||(t.push("Resume"),s.push("Resume is required for job applications")),e.tenth_percentage&&e.tenth_percentage<60&&a.push("Class 10 percentage below 60% may limit opportunities"),e.twelfth_percentage&&e.twelfth_percentage<60&&a.push("Class 12 percentage below 60% may limit opportunities");let r=Object.keys(v).reduce((e,t)=>e+Object.keys(v[t]).length,0),i=Math.round((r-t.length)/r*100);return{isValid:0===t.length&&0===s.length,canApply:N.every(t=>{let a=e[t];return a&&("string"!=typeof a||""!==a.trim())}),missing:t,warnings:a,errors:s,score:i,summary:k(i,t.length,a.length,s.length)}},_=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=w(e),s=[];return t.minCgpa&&e.gpa&&parseFloat(e.gpa)<t.minCgpa&&s.push("CGPA ".concat(t.minCgpa," or above required")),t.allowedBranches&&e.branch&&!t.allowedBranches.includes(e.branch)&&s.push("This job is not open for ".concat(e.branch," students")),t.minTenthPercentage&&e.tenth_percentage&&e.tenth_percentage<t.minTenthPercentage&&s.push("Class 10: ".concat(t.minTenthPercentage,"% or above required")),t.minTwelfthPercentage&&e.twelfth_percentage&&e.twelfth_percentage<t.minTwelfthPercentage&&s.push("Class 12: ".concat(t.minTwelfthPercentage,"% or above required")),{...a,jobSpecific:{errors:s,warnings:[],isEligible:0===s.length}}},A=e=>{for(let t of Object.values(v))if(t[e])return t[e];return e.replace(/_/g," ").replace(/\b\w/g,e=>e.toUpperCase())},k=(e,t,a,s)=>s>0?{status:"error",message:"".concat(s," error(s) need to be fixed before applying"),color:"red"}:t>0?{status:"incomplete",message:"".concat(t," required field(s) missing"),color:"yellow"}:a>0?{status:"warning",message:"Profile complete with ".concat(a," warning(s)"),color:"orange"}:e>=90?{status:"excellent",message:"Profile is excellent and ready for applications",color:"green"}:e>=80?{status:"good",message:"Profile is good for most applications",color:"blue"}:{status:"needs_improvement",message:"Profile needs more information for better job matching",color:"yellow"};var C=a(52338);let P=e=>{let{title:t,onEdit:a,children:r,icon:i}=e;return(0,s.jsxs)("div",{className:"bg-white p-4 sm:p-6 rounded-xl shadow-sm border border-gray-200/80 mb-6",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[i&&(0,s.jsx)("span",{className:"mr-2 text-lg",children:i}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:t})]}),a&&(0,s.jsx)("button",{onClick:a,className:"text-sm font-medium text-indigo-600 hover:text-indigo-800 transition-colors duration-200",children:"Edit"})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4",children:r})]})},D=e=>{let{label:t,type:a="text",placeholder:r,name:i,value:l,onChange:n,isFullWidth:c=!1,required:o=!1}=e;return(0,s.jsxs)("div",{className:c?"md:col-span-2":"",children:[(0,s.jsxs)("label",{htmlFor:i,className:"block text-sm font-medium text-gray-700 mb-1.5",children:[t," ",o&&(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("input",{type:a,name:i,id:i,placeholder:r,value:l,onChange:n,required:o,className:"w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 transition-shadow duration-200"})]})},S=e=>{let{label:t,name:a,value:r,onChange:i,children:l,required:n=!1}=e;return(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:a,className:"block text-sm font-medium text-gray-700 mb-1.5",children:[t," ",n&&(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("select",{id:a,name:a,value:r,onChange:i,required:n,className:"w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 transition-shadow duration-200",children:l})]})},M=e=>{let{label:t,name:a,value:r,onChange:i,placeholder:l,rows:n=6,tip:c,required:o=!1}=e;return(0,s.jsxs)("div",{className:"md:col-span-2",children:[(0,s.jsxs)("label",{htmlFor:a,className:"block text-sm font-medium text-gray-700 mb-1.5",children:[t," ",o&&(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("textarea",{id:a,name:a,rows:n,value:r,onChange:i,required:o,className:"w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 transition-shadow duration-200",placeholder:l}),c&&(0,s.jsx)("p",{className:"mt-2 text-xs text-gray-500",children:c})]})},q=e=>{let{label:t,name:a,fileName:r,onChange:i,required:l=!1}=e;return(0,s.jsxs)("div",{className:"md:col-span-2",children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1.5",children:[t," ",l&&(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("div",{className:"mt-1 flex items-center justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg hover:border-indigo-400 transition-colors duration-200",children:(0,s.jsxs)("div",{className:"space-y-1 text-center",children:[(0,s.jsx)("svg",{className:"mx-auto h-12 w-12 text-gray-400",stroke:"currentColor",fill:"none",viewBox:"0 0 48 48","aria-hidden":"true",children:(0,s.jsx)("path",{d:"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"})}),(0,s.jsxs)("div",{className:"flex text-sm text-gray-600",children:[(0,s.jsxs)("label",{htmlFor:a,className:"relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500 focus-within:outline-none",children:[(0,s.jsx)("span",{children:"Upload a file"}),(0,s.jsx)("input",{id:a,name:a,type:"file",className:"sr-only",onChange:i,required:l})]}),(0,s.jsx)("p",{className:"pl-1",children:"or drag and drop"})]}),r?(0,s.jsx)("p",{className:"text-sm font-semibold text-green-600",children:r}):(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"PDF, DOCX, PNG, JPG up to 10MB"})]})})]})},L=e=>{let{job:t}=e;return t?(0,s.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200/80 p-6 mb-6",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:t.title}),(0,s.jsxs)("div",{className:"flex items-center text-lg text-gray-600 mb-2",children:[(0,s.jsx)(l.A,{className:"w-5 h-5 mr-2"}),(0,s.jsx)("span",{children:t.company_name})]}),(0,s.jsxs)("div",{className:"flex items-center text-sm text-indigo-600 font-medium",children:[(0,s.jsx)(n.A,{className:"w-4 h-4 mr-1"}),(0,s.jsx)("span",{children:t.job_type||"FULL TIME"})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6 pb-6 border-b border-gray-200",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(c.A,{className:"w-5 h-5 text-indigo-600"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Salary"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:t.salary_min&&t.salary_max?"$".concat(t.salary_min," - $").concat(t.salary_max):"Competitive salary"})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(o.A,{className:"w-5 h-5 text-indigo-600"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Location"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:t.location})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(d.A,{className:"w-5 h-5 text-indigo-600"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Deadline"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:t.application_deadline?new Date(t.application_deadline).toLocaleDateString():"Not specified"})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(m.A,{className:"w-5 h-5 text-indigo-600"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Duration"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:t.duration||"Not specified"})]})]})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Job Description"}),(0,s.jsx)("p",{className:"text-gray-700 leading-relaxed",children:t.description})]}),t.requirements&&t.requirements.length>0&&(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Requirements"}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:t.requirements.map((e,t)=>(0,s.jsx)("span",{className:"bg-gray-100 border border-gray-200 rounded-lg px-3 py-1 text-sm font-medium text-gray-700",children:e},t))})]}),t.additional_fields&&t.additional_fields.length>0&&(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-3 flex items-center",children:[(0,s.jsx)(p.A,{className:"w-5 h-5 mr-2 text-indigo-600"}),"Additional Information Required"]}),(0,s.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,s.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"You'll need to provide the following information when applying:"}),(0,s.jsx)("div",{className:"space-y-2",children:t.additional_fields.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-indigo-600 rounded-full"}),(0,s.jsxs)("span",{className:"text-sm text-gray-700",children:[e.label,e.required&&(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,s.jsx)("span",{className:"text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded",children:e.type})]},t))})]})]}),t.interview_rounds&&t.interview_rounds.length>0&&(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-3 flex items-center",children:[(0,s.jsx)(h.A,{className:"w-5 h-5 mr-2 text-indigo-600"}),"Interview Process"]}),(0,s.jsx)("div",{className:"space-y-3",children:t.interview_rounds.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-indigo-600 text-white rounded-full flex items-center justify-center text-sm font-semibold flex-shrink-0",children:t+1}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h4",{className:"font-medium text-gray-900",children:e.name}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:e.description})]})]},t))})]}),(t.min_cgpa||t.allowed_branches||t.min_tenth_percentage||t.min_twelfth_percentage)&&(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-3 flex items-center",children:[(0,s.jsx)(u.A,{className:"w-5 h-5 mr-2 text-indigo-600"}),"Eligibility Requirements"]}),(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[t.min_cgpa&&(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(x.A,{className:"w-4 h-4 text-blue-600"}),(0,s.jsxs)("span",{className:"text-sm text-gray-700",children:["Minimum CGPA: ",t.min_cgpa]})]}),t.allowed_branches&&t.allowed_branches.length>0&&(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(x.A,{className:"w-4 h-4 text-blue-600"}),(0,s.jsxs)("span",{className:"text-sm text-gray-700",children:["Branches: ",t.allowed_branches.join(", ")]})]}),t.min_tenth_percentage&&(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(x.A,{className:"w-4 h-4 text-blue-600"}),(0,s.jsxs)("span",{className:"text-sm text-gray-700",children:["10th Percentage: ",t.min_tenth_percentage,"%"]})]}),t.min_twelfth_percentage&&(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(x.A,{className:"w-4 h-4 text-blue-600"}),(0,s.jsxs)("span",{className:"text-sm text-gray-700",children:["12th Percentage: ",t.min_twelfth_percentage,"%"]})]})]})})]}),(0,s.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)(g.A,{className:"w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-yellow-800 mb-1",children:"Application Tips"}),(0,s.jsxs)("ul",{className:"text-sm text-yellow-700 space-y-1",children:[(0,s.jsx)("li",{children:"• Ensure your profile is complete with all required information"}),(0,s.jsx)("li",{children:"• Prepare a compelling cover letter highlighting relevant experience"}),(0,s.jsx)("li",{children:"• Have your resume and any required documents ready"}),(0,s.jsx)("li",{children:"• Review the job requirements and eligibility criteria carefully"})]})]})]})})]}):null},R=e=>{let{job:t,formData:a,setFormData:r,setStep:i,canApply:l=!0}=e,n=(e,t)=>{r(a=>({...a,additional_fields:{...a.additional_fields,[e]:t}}))},c=e=>{let t="field_".concat(e.id),r=a.additional_fields[t]||"";switch(e.type){case"text":return(0,s.jsx)(D,{label:e.label,name:t,value:r,onChange:e=>n(t,e.target.value),placeholder:"Enter ".concat(e.label.toLowerCase()),required:e.required},e.id);case"number":return(0,s.jsx)(D,{label:e.label,type:"number",name:t,value:r,onChange:e=>n(t,e.target.value),placeholder:"Enter ".concat(e.label.toLowerCase()),required:e.required},e.id);case"file":return(0,s.jsx)(q,{label:e.label,name:t,fileName:r instanceof File?r.name:"",onChange:e=>{n(t,e.target.files[0])},required:e.required},e.id);case"multiple_choice":var i;return(0,s.jsxs)(S,{label:e.label,name:t,value:r,onChange:e=>n(t,e.target.value),required:e.required,children:[(0,s.jsxs)("option",{value:"",children:["Select ",e.label.toLowerCase()]}),null==(i=e.options)?void 0:i.map((e,t)=>(0,s.jsx)("option",{value:e,children:e},t))]},e.id);default:return null}};return(0,s.jsxs)("form",{onSubmit:e=>{e.preventDefault(),i("review")},children:[(0,s.jsx)(P,{title:"\uD83D\uDCDD Cover Letter",icon:"\uD83D\uDCDD",children:(0,s.jsx)(M,{label:"Cover Letter",name:"cover_letter",value:a.cover_letter,onChange:e=>{let{name:t,value:a,type:s,files:i}=e.target;"file"===s?r(e=>({...e,additional_fields:{...e.additional_fields,[t]:i[0]}})):r(e=>({...e,[t]:a}))},placeholder:"Dear Hiring Manager, I am writing to express my interest in this position...",tip:"Tip: Mention specific skills from the job requirements and explain how your experience aligns with the role.",required:!0})}),t.additional_fields&&t.additional_fields.length>0&&(0,s.jsx)(P,{title:"\uD83D\uDCCB Additional Information",icon:"\uD83D\uDCCB",children:(0,s.jsx)("div",{className:"md:col-span-2 space-y-4",children:t.additional_fields.map(e=>c(e))})}),(0,s.jsx)("div",{className:"mt-8 flex justify-end",children:(0,s.jsx)("button",{type:"submit",disabled:!l,className:"w-full sm:w-auto px-6 py-3 font-semibold rounded-lg shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 ".concat(l?"bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500":"bg-gray-300 text-gray-500 cursor-not-allowed"),children:l?"Review Application":"Application Restricted"})})]})},E=e=>{let{job:t,formData:a,setStep:r,onSubmit:i,isSubmitting:l,canApply:n=!0}=e,c=e=>{let{label:t,value:a}=e;return(0,s.jsxs)("div",{className:"py-3 sm:grid sm:grid-cols-3 sm:gap-4",children:[(0,s.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:t}),(0,s.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2 break-words",children:a||"Not provided"})]})};return(0,s.jsxs)("div",{children:[(0,s.jsx)(P,{title:"\uD83D\uDCDD Cover Letter",onEdit:()=>r("form"),icon:"\uD83D\uDCDD",children:(0,s.jsx)("div",{className:"md:col-span-2",children:(0,s.jsx)(e=>{let{label:t,value:a}=e;return(0,s.jsxs)("div",{className:"py-3",children:[(0,s.jsx)("dt",{className:"text-sm font-medium text-gray-500 mb-2",children:t}),(0,s.jsx)("dd",{className:"text-sm text-gray-800 p-4 bg-gray-50 rounded-lg whitespace-pre-wrap",children:a||"Not provided"})]})},{label:"Cover Letter",value:a.cover_letter})})}),t.additional_fields&&t.additional_fields.length>0&&(0,s.jsx)(P,{title:"\uD83D\uDCCB Additional Information",onEdit:()=>r("form"),icon:"\uD83D\uDCCB",children:(0,s.jsx)("div",{className:"md:col-span-2 divide-y divide-gray-200",children:t.additional_fields.map(e=>{let t="field_".concat(e.id),r=a.additional_fields[t];return(0,s.jsx)(c,{label:e.label,value:"file"===e.type&&r instanceof File?r.name:r},e.id)})})}),(0,s.jsxs)("div",{className:"mt-8 flex justify-between",children:[(0,s.jsx)("button",{type:"button",onClick:()=>r("form"),className:"px-6 py-3 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200",children:"Back to Edit"}),(0,s.jsx)("button",{type:"button",onClick:i,disabled:l||!n,className:"px-8 py-3 font-semibold rounded-lg shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 disabled:opacity-50 ".concat(n?"bg-green-600 text-white hover:bg-green-700 focus:ring-green-500":"bg-gray-300 text-gray-500 cursor-not-allowed"),children:l?"Submitting...":n?"Submit Application":"Application Restricted"})]})]})};function T(){let e=(0,i.useRouter)(),t=(0,i.useParams)().id,{showApplicationSubmissionError:a,showSuccess:n,handleApiError:m,showProfileIncompleteModal:p}=(0,j.hN)(),[h,u]=(0,r.useState)(null),[x,v]=(0,r.useState)(!0),[N,w]=(0,r.useState)(null),[A,k]=(0,r.useState)("form"),[P,D]=(0,r.useState)(!1),[S,M]=(0,r.useState)(null),[q,T]=(0,r.useState)(null),[F,B]=(0,r.useState)(null),[z,H]=(0,r.useState)(!0),[I,J]=(0,r.useState)([]),[O,G]=(0,r.useState)({cover_letter:"",additional_fields:{}});(0,r.useEffect)(()=>{let e=async()=>{try{v(!0);let e=await b.A.get("/api/v1/college/default-college/jobs/".concat(t,"/"));u(e.data),w(null)}catch(e){console.error("Failed to fetch job details:",e),m(e,"loading job details"),w("Failed to load job details. Please try again.")}finally{v(!1)}};t&&(e(),U(),$())},[t]);let U=async()=>{try{if(!localStorage.getItem("access_token"))return;let e=(await b.A.get("/api/auth/profile/")).data;M(e);let t=h?{minCgpa:h.min_cgpa,allowedBranches:h.allowed_branches,minTenthPercentage:h.min_tenth_percentage,minTwelfthPercentage:h.min_twelfth_percentage}:{},a=_(e,t);T(a)}catch(e){console.error("Failed to fetch user profile:",e)}},$=async()=>{if(t)try{let e=await C.N.canApplyToJob(t);H(e.can_apply),e.can_apply||(B({status:e.freeze_status,reason:e.reason,freeze_reason:e.freeze_reason,restrictions:e.restrictions||[]}),J(e.restrictions||[]))}catch(e){console.error("Failed to check job application eligibility:",e),H(!0)}};(0,r.useEffect)(()=>{S&&h&&T(_(S,{minCgpa:h.min_cgpa,allowedBranches:h.allowed_branches,minTenthPercentage:h.min_tenth_percentage,minTwelfthPercentage:h.min_twelfth_percentage}))},[S,h]);let V=async()=>{if(q&&!q.canApply)return void p(q.missing.filter(e=>["Resume","First Name","Last Name","Email","Student ID/Roll Number"].includes(e)));if((null==q?void 0:q.jobSpecific)&&!q.jobSpecific.isEligible)return void a({response:{data:{eligibility:q.jobSpecific.errors}}});if(!O.cover_letter.trim())return void a({response:{data:{cover_letter:["Cover letter is required."]}}});D(!0);try{await (0,y.vr)(t,O.cover_letter,O.additional_fields),n("Application Submitted!","Your job application has been submitted successfully. Good luck!"),setTimeout(()=>{e.push("/jobpostings")},2e3)}catch(e){console.error("Failed to submit application:",e),a(e)}finally{D(!1)}};return x?(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-16 w-16 border-b-2 border-indigo-600 mx-auto"}),(0,s.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading job details..."})]})}):N?(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-red-600 mb-4",children:N}),(0,s.jsx)("button",{onClick:()=>e.push("/jobpostings"),className:"px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700",children:"Back to Jobs"})]})}):h?(0,s.jsx)("div",{className:"bg-gray-50 font-sans",children:(0,s.jsxs)("div",{className:"container mx-auto max-w-4xl p-4 sm:p-6 lg:p-8",children:[(0,s.jsxs)("header",{className:"mb-8",children:[(0,s.jsx)("div",{className:"flex items-center mb-4",children:(0,s.jsxs)("button",{onClick:()=>e.push("/jobpostings"),className:"flex items-center text-gray-600 hover:text-gray-900 mr-4",children:[(0,s.jsx)(f.A,{className:"w-5 h-5 mr-2"}),"Back to Jobs"]})}),(0,s.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200/80 p-6 mb-6",children:[(0,s.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between mb-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-900 mb-2",children:h.title}),(0,s.jsxs)("div",{className:"flex items-center text-lg text-gray-600 mb-2",children:[(0,s.jsx)(l.A,{className:"w-5 h-5 mr-2"}),(0,s.jsx)("span",{children:h.company_name})]})]}),(0,s.jsx)("div",{className:"flex items-center space-x-2 mt-4 lg:mt-0",children:[1,2,3].map(e=>(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ".concat("form"===A&&1===e||"review"===A&&e<=2?"bg-indigo-600 text-white":"bg-gray-200 text-gray-600"),children:e}),e<3&&(0,s.jsx)("div",{className:"w-8 h-0.5 mx-2 ".concat("review"===A&&e<2?"bg-indigo-600":"bg-gray-200")})]},e))})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,s.jsx)(o.A,{className:"w-4 h-4 mr-2"}),(0,s.jsx)("span",{children:h.location})]}),(0,s.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,s.jsx)(c.A,{className:"w-4 h-4 mr-2"}),(0,s.jsx)("span",{children:h.salary_min&&h.salary_max?"$".concat(h.salary_min," - $").concat(h.salary_max):"Competitive salary"})]}),(0,s.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,s.jsx)(d.A,{className:"w-4 h-4 mr-2"}),(0,s.jsxs)("span",{children:["Deadline: ",h.application_deadline?new Date(h.application_deadline).toLocaleDateString():"Not specified"]})]})]})]})]}),(0,s.jsx)(L,{job:h}),!z&&F&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6",children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)(g.A,{className:"w-5 h-5 text-red-600 mt-0.5 mr-3 flex-shrink-0"}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:"text-red-800 font-semibold mb-2",children:"complete"===F.status?"Account Completely Frozen":"Account Partially Restricted"}),(0,s.jsx)("p",{className:"text-red-700 mb-3",children:F.reason}),F.freeze_reason&&(0,s.jsx)("div",{className:"bg-red-100 rounded-md p-3 mb-3",children:(0,s.jsxs)("p",{className:"text-red-800 font-medium text-sm",children:["Admin Reason: ",F.freeze_reason]})}),I.length>0&&(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-red-700 font-medium text-sm mb-2",children:"Specific restrictions for this job:"}),(0,s.jsx)("ul",{className:"list-disc list-inside text-red-600 text-sm space-y-1",children:I.map((e,t)=>(0,s.jsx)("li",{children:e},t))})]})]})]})}),(0,s.jsx)("main",{children:"form"===A?(0,s.jsx)(R,{job:h,formData:O,setFormData:G,setStep:k,canApply:z}):(0,s.jsx)(E,{job:h,formData:O,setStep:k,onSubmit:V,isSubmitting:P,canApply:z})})]})}):(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"Job not found"}),(0,s.jsx)("button",{onClick:()=>e.push("/jobpostings"),className:"px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700",children:"Back to Jobs"})]})})}},54416:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},55868:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},57434:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},69037:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},69074:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},69305:(e,t,a)=>{Promise.resolve().then(a.bind(a,47317))},71007:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},75525:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},76517:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]])},81284:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},85339:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[3464,3983,2338,8441,1684,7358],()=>t(69305)),_N_E=e.O()}]);