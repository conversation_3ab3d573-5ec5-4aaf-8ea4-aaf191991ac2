(()=>{var e={};e.id=5481,e.ids=[1286,4335,5481],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4917:(e,t,a)=>{Promise.resolve().then(a.bind(a,33526))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11285:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var r=(0,a(6445).A)("outline","plus","IconPlus",[["path",{d:"M12 5l0 14",key:"svg-0"}],["path",{d:"M5 12l14 0",key:"svg-1"}]])},11529:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var r=(0,a(6445).A)("outline","edit","IconEdit",[["path",{d:"M7 7h-1a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-1",key:"svg-0"}],["path",{d:"M20.385 6.585a2.1 2.1 0 0 0 -2.97 -2.97l-8.415 8.385v3h3l8.385 -8.415z",key:"svg-1"}],["path",{d:"M16 5l3 3",key:"svg-2"}]])},12412:e=>{"use strict";e.exports=require("assert")},14335:(e,t,a)=>{"use strict";a.d(t,{C1:()=>o,Gu:()=>g,JT:()=>l,RC:()=>c,S0:()=>y,Y_:()=>p,bl:()=>m,dl:()=>x,eK:()=>i,fetchCompanies:()=>n,getCompanyStats:()=>d,jQ:()=>u,mm:()=>s,oY:()=>h});var r=a(58138);function s(e={}){let t=new URLSearchParams;e.page&&t.append("page",e.page),e.per_page&&t.append("per_page",e.per_page),e.tier&&"ALL"!==e.tier&&t.append("tier",e.tier),e.industry&&"ALL"!==e.industry&&t.append("industry",e.industry),e.campus_recruiting&&t.append("campus_recruiting",e.campus_recruiting),e.search&&t.append("search",e.search),e.sort&&t.append("sort",e.sort),t.append("_t",new Date().getTime());let a=t.toString(),n=[`/api/v1/companies/${a?`?${a}`:""}`,`/api/v1/college/default-college/companies/${a?`?${a}`:""}`];return r.A.get(n[0]).catch(e=>(console.log(`Primary endpoint failed: ${e.message}, trying fallback...`),r.A.get(n[1])))}async function n(e={}){try{console.log("Fetching companies from API...");let t=await s(e),a=[];if(t.data&&Array.isArray(t.data)?a=t.data:t.data&&t.data.results&&Array.isArray(t.data.results)?a=t.data.results:t.data&&t.data.data&&Array.isArray(t.data.data)&&(a=t.data.data),console.log(`Retrieved ${a.length} companies from API`),a.length>0)return await Promise.all(a.map(async e=>{try{let t=await o(e.id);return p(t.data)}catch(t){return console.log(`Could not fetch details for company ${e.id}:`,t),p(e)}}));throw Error("No companies returned from API")}catch(t){console.error("Error fetching companies from API:",t),console.log("Falling back to static company data");let{companies:e}=await a.e(1286).then(a.bind(a,61286));return e}}function o(e){let t=[`/api/v1/company/${e}/`,`/api/v1/companies/${e}/`,`/api/v1/college/default-college/companies/${e}/`];return r.A.get(t[0]).catch(e=>(console.log(`First company endpoint failed: ${e.message}, trying second...`),r.A.get(t[1]).catch(e=>(console.log(`Second company endpoint failed: ${e.message}, trying third...`),r.A.get(t[2])))))}function i(e){let t=new FormData;return Object.keys(e).forEach(a=>{"logo"===a&&e[a]instanceof File?t.append(a,e[a]):null!==e[a]&&void 0!==e[a]&&t.append(a,e[a])}),r.A.post("/api/v1/companies/",t,{headers:{"Content-Type":"multipart/form-data"}})}function l(e,t){let a=new FormData;return Object.keys(t).forEach(e=>{"logo"===e&&t[e]instanceof File?a.append(e,t[e]):null!==t[e]&&void 0!==t[e]&&a.append(e,t[e])}),r.A.put(`/api/v1/companies/${e}/`,a,{headers:{"Content-Type":"multipart/form-data"}})}function c(e){return r.A.delete(`/api/v1/companies/${e}/`)}function d(){return r.A.get("/api/v1/companies/stats/")}function p(e){return{id:e.id,name:e.name,logo:e.logo||`https://via.placeholder.com/48x48/4285F4/FFFFFF?text=${e.name.charAt(0)}`,description:e.description||"",industry:e.industry||"",size:e.size||"",founded:e.founded||"",location:e.location||"Location not specified",website:e.website||"",tier:e.tier||"Tier 3",campus_recruiting:e.campus_recruiting||!1,totalActiveJobs:e.total_active_jobs||0,totalApplicants:e.total_applicants||0,totalHired:e.total_hired||0,awaitedApproval:e.pending_approval||0}}function m(e){return r.A.get(`/api/v1/companies/${e}/followers/count/`)}function u(e,t){return r.A.post(`/api/v1/companies/${e}/followers/`,{user_id:t})}function h(e,t){return r.A.delete(`/api/v1/companies/${e}/followers/`,{data:{user_id:t}})}function g(e,t){return r.A.get(`/api/v1/companies/${e}/followers/status/?user_id=${t}`)}function x(e){return r.A.get(`/api/v1/users/${e}/following/`)}function y(){return r.A.get("/api/v1/companies/simple/").then(e=>{if(e.data&&e.data.success)return e.data;throw Error("Failed to fetch companies")}).catch(e=>(console.error("Error fetching companies:",e),{success:!1,data:[],count:0,error:e.message}))}},14401:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var r=(0,a(6445).A)("outline","search","IconSearch",[["path",{d:"M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0",key:"svg-0"}],["path",{d:"M21 21l-6 -6",key:"svg-1"}]])},14645:(e,t,a)=>{Promise.resolve().then(a.bind(a,57147))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33526:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>x});var r=a(60687),s=a(43210),n=a(16189),o=a(11285),i=a(6445),l=(0,i.A)("outline","external-link","IconExternalLink",[["path",{d:"M12 6h-6a2 2 0 0 0 -2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-6",key:"svg-0"}],["path",{d:"M11 13l9 -9",key:"svg-1"}],["path",{d:"M15 4h5v5",key:"svg-2"}]]),c=a(95603),d=(0,i.A)("outline","copy","IconCopy",[["path",{d:"M7 7m0 2.667a2.667 2.667 0 0 1 2.667 -2.667h8.666a2.667 2.667 0 0 1 2.667 2.667v8.666a2.667 2.667 0 0 1 -2.667 2.667h-8.666a2.667 2.667 0 0 1 -2.667 -2.667z",key:"svg-0"}],["path",{d:"M4.012 16.737a2.005 2.005 0 0 1 -1.012 -1.737v-10c0 -1.1 .9 -2 2 -2h10c.75 0 1.158 .385 1.5 1",key:"svg-1"}]]),p=a(11529),m=a(95501),u=a(14401),h=a(50867);a(61286);var g=a(58838);function x(){let e=(0,n.useRouter)(),[t,a]=(0,s.useState)([]),[i,x]=(0,s.useState)(!1),[y,f]=(0,s.useState)({company:""}),[b,v]=(0,s.useState)(null),[j,A]=(0,s.useState)(""),[w,N]=(0,s.useState)(!1),[C,_]=(0,s.useState)([]),[k,P]=(0,s.useState)(!1),[E,D]=(0,s.useState)(null),S=Array.isArray(C)?C.filter(e=>(e.name||e.companyName||e.employer_name||"").toLowerCase().includes(j.toLowerCase())):[],F=e=>{let t=e.name||e.companyName||e.employer_name;b?v({...b,company:t}):f({company:t}),A(t),N(!1)},$=e=>{A(e),b?v({...b,company:e}):f({company:e}),N(!0)},q=(e,t)=>{e.preventDefault();let a=`${window.location.origin}/forms/${t}`;navigator.clipboard.writeText(a),D(t),setTimeout(()=>D(null),2e3)},z=async()=>{try{if(!y.company||""===y.company.trim())return void alert("Please enter a company name");let e={company:y.company,submitted:!1,details:null},r=await (0,h.DG)(e);a([r.data,...Array.isArray(t)?t:[]]),f({company:""}),A(""),x(!1)}catch(e){console.error("Error creating form:",e),alert("Failed to create form. Please try again.")}},I=e=>{v(e),A(e.company),x(!0)},M=async()=>{try{if(!b.company||""===b.company.trim())return void alert("Please enter a company name");await (0,h.wi)(b.id,b),a((Array.isArray(t)?t:[]).map(e=>e.id===b.id?b:e)),v(null),A(""),x(!1)}catch(e){console.error("Error updating form:",e),alert("Failed to update form. Please try again.")}};return(0,r.jsxs)("div",{className:"flex min-h-screen bg-gray-50",children:[(0,r.jsx)(g.A,{}),(0,r.jsx)("div",{className:"flex-1 ml-64 p-6",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-semibold text-gray-900",children:"Forms Dashboard"}),(0,r.jsxs)("button",{onClick:()=>x(!0),className:"flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(o.A,{size:18})," Create Form"]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-6",children:"Available Forms"}),k?(0,r.jsx)("div",{className:"flex justify-center items-center h-40",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-10 w-10 border-b-2 border-blue-600"})}):0===t.length?(0,r.jsx)("p",{className:"text-gray-500 text-center py-10",children:"No forms created yet."}):(0,r.jsx)("div",{className:"space-y-4 max-h-[70vh] overflow-y-auto pr-2",children:Array.isArray(t)&&t.map(t=>(0,r.jsxs)("div",{className:"border-b border-gray-200 pb-4 mb-4 last:border-b-0 last:mb-0 last:pb-0",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-bold text-gray-900",children:t.company}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["Key: ",t.key]}),(0,r.jsxs)("p",{className:"text-xs text-gray-500",children:["Status: ",t.submitted?(0,r.jsx)("span",{className:"px-2 py-0.5 bg-green-100 text-green-800 rounded-full text-xs font-medium",children:"Completed"}):(0,r.jsx)("span",{className:"px-2 py-0.5 bg-gray-100 text-gray-800 rounded-full text-xs font-medium",children:"Not Submitted"})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)("a",{href:`/forms/${t.id}`,className:"flex items-center gap-1 text-blue-600 hover:text-blue-800 text-sm font-medium",target:"_blank",rel:"noopener noreferrer",children:[(0,r.jsx)(l,{size:16}),"Open Form"]}),(0,r.jsx)("button",{onClick:e=>q(e,t.id),className:"p-1.5 rounded-md hover:bg-gray-100 text-gray-600 transition-colors",title:"Copy form link",children:E===t.id?(0,r.jsx)(c.A,{size:18,className:"text-green-500"}):(0,r.jsx)(d,{size:18})}),(0,r.jsx)("button",{onClick:()=>I(t),className:"p-1.5 rounded-md bg-blue-50 text-blue-600 hover:bg-blue-100",title:"Edit form",children:(0,r.jsx)(p.A,{size:18})})]})]}),t.submitted&&(0,r.jsx)("div",{className:"mt-2 text-right",children:(0,r.jsx)("button",{onClick:()=>e.push(`/admin/form/edit/${t.id}`),className:"text-sm text-blue-600 hover:text-blue-800 hover:underline font-medium",children:"View Details"})})]},t.id))})]})]})}),i&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-30 flex justify-center items-center z-50",children:(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-lg w-[90%] max-w-md",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-blue-600 mb-4",children:b?"Edit Form":"Create New Form"}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Company Name"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(m.A,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsx)("input",{type:"text",value:j,onChange:e=>$(e.target.value),onFocus:()=>N(!0),placeholder:"Search for a company...",className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0}),(0,r.jsx)("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none",children:(0,r.jsx)(u.A,{size:18,className:"text-gray-400"})})]}),w&&j&&(0,r.jsx)("div",{className:"absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto",children:S.length>0?(0,r.jsx)("div",{className:"py-1",children:S.map((e,t)=>(0,r.jsx)("button",{type:"button",onClick:()=>F(e),className:"w-full text-left px-4 py-2 hover:bg-gray-50 focus:bg-gray-50 focus:outline-none border-b border-gray-100 last:border-b-0",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium text-gray-900",children:e.name||e.companyName||e.employer_name}),e.location&&(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.location})]}),e.totalActiveJobs>0&&(0,r.jsxs)("div",{className:"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full",children:[e.totalActiveJobs," jobs"]})]})},e.id||t))}):(0,r.jsxs)("div",{className:"px-4 py-3 text-sm text-gray-500 text-center",children:['No companies found matching "',j,'"']})}),w&&(0,r.jsx)("div",{className:"fixed inset-0 z-5",onClick:()=>N(!1)})]}),(0,r.jsxs)("div",{className:"flex justify-end gap-3 mt-6",children:[(0,r.jsx)("button",{onClick:()=>{x(!1),v(null),A(""),f({company:""})},className:"px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors",children:"Cancel"}),(0,r.jsx)("button",{onClick:b?M:z,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:b?"Save Changes":"Create"})]})]})})]})}},33873:e=>{"use strict";e.exports=require("path")},35306:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=a(65239),s=a(48088),n=a(88170),o=a.n(n),i=a(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);a.d(t,l);let c={children:["",{children:["admin",{children:["form",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,57147)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\form\\page.jsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,23697)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\layout.jsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,75535)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\layout.js"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\form\\page.jsx"],p={require:a,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/admin/form/page",pathname:"/admin/form",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57147:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\form\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\form\\page.jsx","default")},61286:(e,t,a)=>{"use strict";a.d(t,{Ly:()=>n,_N:()=>o,companies:()=>s,zZ:()=>i});var r=a(14335);let s=[{id:1,name:"TechCorp Inc",description:"Leading technology solutions provider",industry:"Technology",size:"500-1000",founded:"2010",website:"https://techcorp.com"},{id:2,name:"DataCorp",description:"Data analytics and insights company",industry:"Data Analytics",size:"100-500",founded:"2015",website:"https://datacorp.com"}],n=[{id:1,job_id:25,title:"Software Engineer",company:"TechCorp Inc",status:"APPLIED",application_deadline:"2024-05-30T23:59:59Z"},{id:2,job_id:26,title:"Data Scientist",company:"DataCorp",status:"INTERVIEW SCHEDULED",application_deadline:"2024-06-15T23:59:59Z"}],o=async(e={})=>{try{let t={...e,_t:new Date().getTime()};console.log("Fetching companies with cache busting...");let a=await (0,r.mm)(t),n=[];a.data&&Array.isArray(a.data)?n=a.data:a.data&&a.data.results&&Array.isArray(a.data.results)?n=a.data.results:a.data&&a.data.data&&Array.isArray(a.data.data)&&(n=a.data.data);let o=n.map(r.Y_);if(console.log(`Fetched ${o.length} companies from API`),0===o.length)return console.warn("API returned empty companies array, using static data"),s;return o}catch(e){console.error("Error fetching companies:",e);try{console.log("Trying alternate endpoint format...");let e=await fetch("/api/v1/college/default-college/companies/");if(e.ok){let t=await e.json(),a=Array.isArray(t)?t:t.data||t.results||[];if(a.length>0)return console.log("Successfully retrieved companies from alternate endpoint"),a.map(r.Y_)}}catch(e){console.error("Alternate endpoint also failed:",e)}return s}};function i(e){return console.log(`Fetching jobs for company ID: ${e}`),[]}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},95603:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var r=(0,a(6445).A)("outline","check","IconCheck",[["path",{d:"M5 12l5 5l10 -10",key:"svg-0"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[4447,681,1658,1060,2305,9661],()=>a(35306));module.exports=r})();