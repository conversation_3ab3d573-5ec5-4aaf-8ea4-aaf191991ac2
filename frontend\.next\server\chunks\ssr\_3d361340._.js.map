{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/app/admin/layout.jsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useState } from 'react';\r\nimport Sidebar from '../../components/ui/Sidebar';\r\nimport {\r\n  IconHome,\r\n  IconBriefcase,\r\n  IconCompass,\r\n  IconMail,\r\n  IconSettings,\r\n  IconHelp,\r\n  IconForms,\r\n  IconUser,\r\n  IconClipboardList\r\n} from '@tabler/icons-react';\r\nimport {\r\n  User,\r\n  Megaphone,\r\n  BarChart3\r\n} from \"lucide-react\";\r\n\r\nexport default function AdminLayout({ children }) {\r\n  const [college, setCollege] = useState('');\r\n\r\n  // Admin sidebar configuration\r\n  const adminLinks = [\r\n    {\r\n      items: [\r\n        { title: 'Dashboard', href: '/admin/dashboard', icon: <IconHome /> },\r\n        { title: 'Jobs', href: '/admin/jobs', icon: <IconBriefcase /> },\r\n        { title: 'Applications', href: '/admin/applications', icon: <IconClipboardList /> },\r\n        { title: 'Posts', href: '/admin/posts', icon: <Megaphone className=\"w-5 h-5\" /> },\r\n        { title: 'Student Management', href: '/admin/student-management', icon: <User className=\"w-5 h-5\" /> },\r\n        { title: 'Student Analytics', href: '/admin/analytics', icon: <BarChart3 className=\"w-5 h-5\" /> },\r\n        { title: 'Company Management', href: '/admin/companymanagement', icon: <IconMail /> },\r\n        { title: 'Forms', href: '/admin/form', icon: <IconForms /> }\r\n      ]\r\n    }\r\n  ];\r\n\r\n  const bottomItems = [\r\n    { title: 'My Profile', href: '/admin/profile', icon: <IconUser /> },\r\n    { title: 'Settings', href: '../settings', icon: <IconSettings /> },\r\n    { title: 'Contact Support', href: '/admin/helpandsupport', icon: <IconHelp /> }\r\n  ];\r\n\r\n  useEffect(() => {\r\n    const storedCollege = localStorage.getItem('collegeName');\r\n    if (storedCollege) {\r\n      setCollege(storedCollege);\r\n    }\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"h-screen bg-gray-50 overflow-hidden\">\r\n      {/* Sidebar + Main Content */}\r\n      <div className=\"flex h-full\">\r\n        <Sidebar\r\n          sections={adminLinks}\r\n          bottomItems={bottomItems}\r\n          defaultExpanded={false}\r\n          navbarHeight=\"0\"\r\n          className=\"z-20\"\r\n        />\r\n        \r\n        {/* Main Content Area with left margin for sidebar */}\r\n        <div className=\"flex-1 p-6 ml-20 overflow-y-auto\">\r\n          <div className=\"bg-white rounded-xl shadow-md p-6 min-h-full text-black\">\r\n            {children}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAAA;AAAA;AAfA;;;;;;AAqBe,SAAS,YAAY,EAAE,QAAQ,EAAE;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,8BAA8B;IAC9B,MAAM,aAAa;QACjB;YACE,OAAO;gBACL;oBAAE,OAAO;oBAAa,MAAM;oBAAoB,oBAAM,8OAAC,sNAAA,CAAA,WAAQ;;;;;gBAAI;gBACnE;oBAAE,OAAO;oBAAQ,MAAM;oBAAe,oBAAM,8OAAC,gOAAA,CAAA,gBAAa;;;;;gBAAI;gBAC9D;oBAAE,OAAO;oBAAgB,MAAM;oBAAuB,oBAAM,8OAAC,wOAAA,CAAA,oBAAiB;;;;;gBAAI;gBAClF;oBAAE,OAAO;oBAAS,MAAM;oBAAgB,oBAAM,8OAAC,4MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;gBAAa;gBAChF;oBAAE,OAAO;oBAAsB,MAAM;oBAA6B,oBAAM,8OAAC,kMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;gBAAa;gBACrG;oBAAE,OAAO;oBAAqB,MAAM;oBAAoB,oBAAM,8OAAC,kNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;gBAAa;gBAChG;oBAAE,OAAO;oBAAsB,MAAM;oBAA4B,oBAAM,8OAAC,sNAAA,CAAA,WAAQ;;;;;gBAAI;gBACpF;oBAAE,OAAO;oBAAS,MAAM;oBAAe,oBAAM,8OAAC,wNAAA,CAAA,YAAS;;;;;gBAAI;aAC5D;QACH;KACD;IAED,MAAM,cAAc;QAClB;YAAE,OAAO;YAAc,MAAM;YAAkB,oBAAM,8OAAC,sNAAA,CAAA,WAAQ;;;;;QAAI;QAClE;YAAE,OAAO;YAAY,MAAM;YAAe,oBAAM,8OAAC,8NAAA,CAAA,eAAY;;;;;QAAI;QACjE;YAAE,OAAO;YAAmB,MAAM;YAAyB,oBAAM,8OAAC,sNAAA,CAAA,WAAQ;;;;;QAAI;KAC/E;IAED,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,aAAa,OAAO,CAAC;QAC3C,IAAI,eAAe;YACjB,WAAW;QACb;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,mIAAA,CAAA,UAAO;oBACN,UAAU;oBACV,aAAa;oBACb,iBAAiB;oBACjB,cAAa;oBACb,WAAU;;;;;;8BAIZ,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "file": "IconHelp.mjs", "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/node_modules/%40tabler/icons-react/src/icons/IconHelp.ts"], "sourcesContent": ["import createReactComponent from '../createReactComponent';\nexport default createReactComponent('outline', 'help', 'IconHelp', [[\"path\",{\"d\":\"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0\",\"key\":\"svg-0\"}],[\"path\",{\"d\":\"M12 17l0 .01\",\"key\":\"svg-1\"}],[\"path\",{\"d\":\"M12 13.5a1.5 1.5 0 0 1 1 -1.5a2.6 2.6 0 1 0 -3 -4\",\"key\":\"svg-2\"}]]);"], "names": [], "mappings": ";;;;;;;;;;AACA,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,wLAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAqB,AAArB,CAAA,CAAA,AAAqB,CAArB,AAAqB,CAArB,AAAqB,CAArB,AAAqB,CAArB,AAAqB,CAArB,AAAqB,CAArB,AAAqB,CAAA,CAAA,CAAA,CAAA,CAAW,MAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;IAAC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAO,CAAC;YAAA,CAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6C,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAM,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAC;KAAA;IAAE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAO,CAAC;YAAA,CAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAM,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAC;KAAA;IAAE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAO,CAAC;YAAA,CAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoD,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAM,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAC;KAAC;CAAC,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 252, "column": 0}, "map": {"version": 3, "file": "IconForms.mjs", "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/node_modules/%40tabler/icons-react/src/icons/IconForms.ts"], "sourcesContent": ["import createReactComponent from '../createReactComponent';\nexport default createReactComponent('outline', 'forms', 'IconForms', [[\"path\",{\"d\":\"M12 3a3 3 0 0 0 -3 3v12a3 3 0 0 0 3 3\",\"key\":\"svg-0\"}],[\"path\",{\"d\":\"M6 3a3 3 0 0 1 3 3v12a3 3 0 0 1 -3 3\",\"key\":\"svg-1\"}],[\"path\",{\"d\":\"M13 7h7a1 1 0 0 1 1 1v8a1 1 0 0 1 -1 1h-7\",\"key\":\"svg-2\"}],[\"path\",{\"d\":\"M5 7h-1a1 1 0 0 0 -1 1v8a1 1 0 0 0 1 1h1\",\"key\":\"svg-3\"}],[\"path\",{\"d\":\"M17 12h.01\",\"key\":\"svg-4\"}],[\"path\",{\"d\":\"M13 12h.01\",\"key\":\"svg-5\"}]]);"], "names": [], "mappings": ";;;;;;;;;;AACA,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,wLAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAqB,CAArB,AAAgC,CAAhC,AAAgC,CAAhC,AAAgC,CAAA,AAAhC,CAAgC,AAAhC,CAAgC,AAAhC,CAAgC,CAAA,CAAA,CAAA,CAAA,OAAA,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,EAAA;IAAC;QAAC,MAAA,CAAO;QAAA,CAAA;YAAC,GAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwC;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,OAAO;QAAC,CAAA;KAAE;IAAA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO;QAAA,CAAC;YAAA,CAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAuC,KAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAQ;KAAA,CAAA;IAAE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAO;YAAC,CAAA,CAAA,CAAA,CAAA,CAAI,2CAA4C,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAM,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAC;KAAE,CAAA;IAAA;QAAC,MAAO,CAAA;QAAA,CAAA;YAAC,GAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAA2C,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA;QAAQ,CAAA;KAAA;IAAE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAO,CAAC;YAAA,CAAA,CAAA,CAAA,EAAI,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,KAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO;QAAA,CAAC;KAAE,CAAA;IAAA;QAAC,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAC,CAAI,CAAA,CAAA,CAAA,CAAA,YAAA,CAAa;YAAA,CAAA,CAAA,CAAA,CAAA,CAAM,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAQ;KAAC;CAAC,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 324, "column": 0}, "map": {"version": 3, "file": "IconClipboardList.mjs", "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/node_modules/%40tabler/icons-react/src/icons/IconClipboardList.ts"], "sourcesContent": ["import createReactComponent from '../createReactComponent';\nexport default createReactComponent('outline', 'clipboard-list', 'IconClipboardList', [[\"path\",{\"d\":\"M9 5h-2a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-12a2 2 0 0 0 -2 -2h-2\",\"key\":\"svg-0\"}],[\"path\",{\"d\":\"M9 3m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z\",\"key\":\"svg-1\"}],[\"path\",{\"d\":\"M9 12l.01 0\",\"key\":\"svg-2\"}],[\"path\",{\"d\":\"M13 12l2 0\",\"key\":\"svg-3\"}],[\"path\",{\"d\":\"M9 16l.01 0\",\"key\":\"svg-4\"}],[\"path\",{\"d\":\"M13 16l2 0\",\"key\":\"svg-5\"}]]);"], "names": [], "mappings": ";;;;;;;;;;AACA,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,wLAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAqB,CAAW,AAAhC,CAAgC,AAAhC,CAAA,AAAgC,CAAhC,AAAgC,CAAA,AAAhC,CAAgC,AAAhC,CAAgC,CAAA,CAAA,CAAA,CAAA,gBAAA,CAAkB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,EAAA;IAAC;QAAC,MAAA,CAAO;QAAA,CAAA;YAAC,GAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmF;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,OAAO;QAAC,CAAA;KAAE;IAAA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO;QAAA,CAAC;YAAA,CAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAA+E,KAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAQ;KAAA,CAAA;IAAE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAO;YAAC,CAAA,CAAA,CAAA,CAAA,CAAI,aAAc,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAM,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAC;KAAE,CAAA;IAAA;QAAC,MAAO,CAAA;QAAA,CAAA;YAAC,GAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAa,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA;QAAQ,CAAA;KAAA;IAAE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAO,CAAC;YAAA,CAAA,CAAA,CAAA,EAAI,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,KAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO;QAAA,CAAC;KAAE,CAAA;IAAA;QAAC,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAC,CAAI,CAAA,CAAA,CAAA,CAAA,YAAA,CAAa;YAAA,CAAA,CAAA,CAAA,CAAA,CAAM,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAQ;KAAC;CAAC,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 396, "column": 0}, "map": {"version": 3, "file": "megaphone.js", "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/node_modules/lucide-react/src/icons/megaphone.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm3 11 18-5v12L3 14v-3z', key: 'n962bs' }],\n  ['path', { d: 'M11.6 16.8a3 3 0 1 1-5.8-1.6', key: '1yl0tm' }],\n];\n\n/**\n * @component @name Megaphone\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMyAxMSAxOC01djEyTDMgMTR2LTN6IiAvPgogIDxwYXRoIGQ9Ik0xMS42IDE2LjhhMyAzIDAgMSAxLTUuOC0xLjYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/megaphone\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Megaphone = createLucideIcon('megaphone', __iconNode);\n\nexport default Megaphone;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA0B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACvD;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC/D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAa,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 442, "column": 0}, "map": {"version": 3, "file": "chart-column.js", "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/node_modules/lucide-react/src/icons/chart-column.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 3v16a2 2 0 0 0 2 2h16', key: 'c24i48' }],\n  ['path', { d: 'M18 17V9', key: '2bz60n' }],\n  ['path', { d: 'M13 17V5', key: '1frdt8' }],\n  ['path', { d: 'M8 17v-3', key: '17ska0' }],\n];\n\n/**\n * @component @name ChartColumn\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAzdjE2YTIgMiAwIDAgMCAyIDJoMTYiIC8+CiAgPHBhdGggZD0iTTE4IDE3VjkiIC8+CiAgPHBhdGggZD0iTTEzIDE3VjUiIC8+CiAgPHBhdGggZD0iTTggMTd2LTMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/chart-column\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChartColumn = createLucideIcon('chart-column', __iconNode);\n\nexport default ChartColumn;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA4B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}