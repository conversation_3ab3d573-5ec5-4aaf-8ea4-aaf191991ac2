(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3998],{39634:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>v});var a=t(95155),r=t(12115),l=t(35695),n=t(40646),i=t(35169),o=t(54861),c=t(27213),d=t(29869),m=t(48136),u=t(4516),x=t(17580),p=t(69074),h=t(38564),g=t(38164),b=t(94631),y=t(4229),j=t(81284),f=t(48937);function v(){let e=(0,l.useRouter)(),[s,t]=(0,r.useState)(!1),[v,N]=(0,r.useState)({}),[w,_]=(0,r.useState)(null),[C,k]=(0,r.useState)(""),[A,R]=(0,r.useState)(!1),S=(0,r.useRef)(null),[T,z]=(0,r.useState)({name:"",industry:"",location:"",size:"",founded:"",website:"",tier:"Tier 3",description:"",campus_recruiting:!1,total_active_jobs:0,total_applicants:0,total_hired:0,awaited_approval:0,logo:null}),F=e=>{let{name:s,value:t,type:a,checked:r}=e.target;z({...T,[s]:"checkbox"===a?r:t})},L=()=>{let e={};return T.name.trim()||(e.name="Company name is required"),T.industry.trim()||(e.industry="Industry is required"),T.location.trim()||(e.location="Location is required"),T.size.trim()||(e.size="Company size is required"),T.founded.trim()||(e.founded="Founded year is required"),T.description.trim()||(e.description="Description is required"),T.website.trim()?E(T.website)||(e.website="Please enter a valid URL (include http:// or https://)"):e.website="Website URL is required",N(e),0===Object.keys(e).length},E=e=>{try{return new URL(e),!0}catch(e){return!1}},I=async s=>{if(s.preventDefault(),L()){R(!0);try{let s={...T,total_active_jobs:Number(T.total_active_jobs),total_applicants:Number(T.total_applicants),total_hired:Number(T.total_hired),awaited_approval:Number(T.awaited_approval),campus_recruiting:!!T.campus_recruiting,logo:w};console.log("Submitting company data:",s);let a=await (0,f.eK)(s);console.log("API response:",a),t(!0),setTimeout(()=>{e.push("/admin/companymanagement")},1500)}catch(e){var a;console.error("Error creating company:",e),(null==(a=e.response)?void 0:a.data)?(console.log("API error details:",e.response.data),N(s=>({...s,...e.response.data,...e.response.data.non_field_errors?{api_error:e.response.data.non_field_errors.join(", ")}:{}}))):N(e=>({...e,api_error:"Failed to create company. Please try again."}))}finally{R(!1)}}},q=()=>{e.push("/admin/companymanagement")};return s?(0,a.jsx)("div",{className:"h-full flex items-center justify-center bg-gray-50",children:(0,a.jsx)("div",{className:"bg-white p-8 rounded-xl shadow-sm border border-gray-200 max-w-md w-full",children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4",children:(0,a.jsx)(n.A,{className:"w-8 h-8 text-green-600"})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Company Created!"}),(0,a.jsx)("p",{className:"text-gray-600 text-center mb-6",children:"The company profile has been successfully created."}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Redirecting back to company management..."})]})})}):(0,a.jsx)("div",{className:"h-full overflow-y-auto bg-gray-50",children:(0,a.jsxs)("div",{className:"container mx-auto py-8 px-4 md:px-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-6",children:[(0,a.jsx)("button",{onClick:q,className:"mr-4 p-2 rounded-full hover:bg-gray-200 transition-colors",children:(0,a.jsx)(i.A,{className:"w-5 h-5 text-gray-600"})}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Create New Company"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 md:p-8",children:[v.api_error&&(0,a.jsxs)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg text-red-700 flex items-start gap-3",children:[(0,a.jsx)(o.A,{className:"w-5 h-5 text-red-500 flex-shrink-0 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium",children:"Error"}),(0,a.jsx)("p",{children:v.api_error})]})]}),(0,a.jsxs)("form",{onSubmit:I,children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Company Logo"}),(0,a.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,a.jsx)("div",{className:"w-24 h-24 rounded-lg flex items-center justify-center overflow-hidden ".concat(C?"border border-gray-200":"bg-gradient-to-br from-blue-500 to-purple-600"),children:C?(0,a.jsx)("img",{src:C,alt:"Company logo preview",className:"w-full h-full object-cover"}):T.name?(0,a.jsx)("span",{className:"text-white font-bold text-2xl",children:T.name.charAt(0)}):(0,a.jsx)(c.A,{className:"w-10 h-10 text-white opacity-70"})}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)("button",{type:"button",onClick:()=>{S.current.click()},className:"px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg flex items-center gap-2",children:[(0,a.jsx)(d.A,{className:"w-4 h-4"}),C?"Change Logo":"Upload Logo"]}),C&&(0,a.jsx)("button",{type:"button",onClick:()=>{_(null),k(""),z({...T,logo:""}),S.current&&(S.current.value="")},className:"px-4 py-2 bg-red-50 hover:bg-red-100 text-red-600 rounded-lg",children:"Remove"}),(0,a.jsx)("input",{ref:S,type:"file",accept:"image/*",onChange:e=>{let s=e.target.files[0];if(s){_(s);let e=new FileReader;e.onloadend=()=>{k(e.result),z({...T,logo:e.result})},e.readAsDataURL(s)}},className:"hidden"})]}),(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"Recommended: Square image, at least 200x200px (.jpg, .png)"}),(0,a.jsx)("p",{className:"text-xs text-gray-400",children:"If no logo is uploaded, the first letter of the company name will be used."})]})]})]}),(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Company Name*"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(m.A,{className:"h-5 w-5 text-gray-400"})}),(0,a.jsx)("input",{type:"text",name:"name",value:T.name,onChange:F,className:"pl-10 w-full rounded-lg border ".concat(v.name?"border-red-300":"border-gray-300"," py-3 px-4 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500"),placeholder:"Enter company name"})]}),v.name&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:v.name})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Industry*"}),(0,a.jsxs)("select",{name:"industry",value:T.industry,onChange:F,className:"w-full rounded-lg border ".concat(v.industry?"border-red-300":"border-gray-300"," py-3 px-4 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500"),children:[(0,a.jsx)("option",{value:"",children:"Select Industry"}),(0,a.jsx)("option",{value:"Technology",children:"Technology"}),(0,a.jsx)("option",{value:"Finance",children:"Finance"}),(0,a.jsx)("option",{value:"Healthcare",children:"Healthcare"}),(0,a.jsx)("option",{value:"Education",children:"Education"}),(0,a.jsx)("option",{value:"Manufacturing",children:"Manufacturing"}),(0,a.jsx)("option",{value:"Retail",children:"Retail"}),(0,a.jsx)("option",{value:"Consulting",children:"Consulting"}),(0,a.jsx)("option",{value:"Media",children:"Media"}),(0,a.jsx)("option",{value:"Other",children:"Other"})]}),v.industry&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:v.industry})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Location*"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(u.A,{className:"h-5 w-5 text-gray-400"})}),(0,a.jsx)("input",{type:"text",name:"location",value:T.location,onChange:F,className:"pl-10 w-full rounded-lg border ".concat(v.location?"border-red-300":"border-gray-300"," py-3 px-4 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500"),placeholder:"e.g., New York, NY"})]}),v.location&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:v.location})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Company Size*"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(x.A,{className:"h-5 w-5 text-gray-400"})}),(0,a.jsxs)("select",{name:"size",value:T.size,onChange:F,className:"pl-10 w-full rounded-lg border ".concat(v.size?"border-red-300":"border-gray-300"," py-3 px-4 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500"),children:[(0,a.jsx)("option",{value:"",children:"Select Size"}),(0,a.jsx)("option",{value:"1-10 employees",children:"1-10 employees"}),(0,a.jsx)("option",{value:"11-50 employees",children:"11-50 employees"}),(0,a.jsx)("option",{value:"51-200 employees",children:"51-200 employees"}),(0,a.jsx)("option",{value:"201-500 employees",children:"201-500 employees"}),(0,a.jsx)("option",{value:"501-1000 employees",children:"501-1000 employees"}),(0,a.jsx)("option",{value:"1001-5000 employees",children:"1001-5000 employees"}),(0,a.jsx)("option",{value:"5001+ employees",children:"5001+ employees"})]})]}),v.size&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:v.size})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Founded Year*"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(p.A,{className:"h-5 w-5 text-gray-400"})}),(0,a.jsx)("input",{type:"number",name:"founded",min:"1800",max:new Date().getFullYear(),value:T.founded,onChange:F,className:"pl-10 w-full rounded-lg border ".concat(v.founded?"border-red-300":"border-gray-300"," py-3 px-4 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500"),placeholder:"e.g., 2010"})]}),v.founded&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:v.founded})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Tier"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(h.A,{className:"h-5 w-5 text-gray-400"})}),(0,a.jsxs)("select",{name:"tier",value:T.tier,onChange:F,className:"pl-10 w-full rounded-lg border border-gray-300 py-3 px-4 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"Tier 1",children:"Tier 1"}),(0,a.jsx)("option",{value:"Tier 2",children:"Tier 2"}),(0,a.jsx)("option",{value:"Tier 3",children:"Tier 3"})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("input",{type:"checkbox",id:"campus_recruiting",name:"campus_recruiting",checked:T.campus_recruiting,onChange:F,className:"h-5 w-5 text-blue-600 rounded border-gray-300 focus:ring-blue-500"}),(0,a.jsx)("label",{htmlFor:"campus_recruiting",className:"text-sm font-medium text-gray-700",children:"Campus Recruiting Program"})]}),(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Description*"}),(0,a.jsx)("textarea",{name:"description",value:T.description,onChange:F,rows:"4",className:"w-full rounded-lg border ".concat(v.description?"border-red-300":"border-gray-300"," py-3 px-4 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500"),placeholder:"Enter a description of the company..."}),v.description&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:v.description})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Website URL*"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(g.A,{className:"h-5 w-5 text-gray-400"})}),(0,a.jsx)("input",{type:"url",name:"website",value:T.website,onChange:F,className:"pl-10 w-full rounded-lg border ".concat(v.website?"border-red-300":"border-gray-300"," py-3 px-4 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500"),placeholder:"https://example.com"})]}),v.website&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:v.website})]})]}),(0,a.jsxs)("div",{className:"mt-8 flex flex-col md:flex-row gap-4 md:gap-3 justify-end",children:[(0,a.jsx)("button",{type:"button",onClick:q,className:"px-6 py-3 border border-gray-300 rounded-lg text-gray-700 bg-white hover:bg-gray-50 font-medium",disabled:A,children:"Cancel"}),(0,a.jsx)("button",{type:"submit",className:"px-6 py-3 bg-blue-600 rounded-lg text-white hover:bg-blue-700 font-medium flex items-center justify-center gap-2",disabled:A,children:A?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(b.A,{className:"w-5 h-5 animate-spin"}),"Creating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(y.A,{className:"w-5 h-5"}),"Create Company"]})})]})]})]}),(0,a.jsxs)("div",{className:"mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4 flex items-start gap-3",children:[(0,a.jsx)(j.A,{className:"w-5 h-5 text-blue-500 flex-shrink-0 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-blue-800",children:"Important Note"}),(0,a.jsx)("p",{className:"text-sm text-blue-600",children:"In a production environment, this would submit to an API endpoint. Currently, this demo adds the company to the local data store."})]})]})]})})}},69179:(e,s,t)=>{Promise.resolve().then(t.bind(t,39634))}},e=>{var s=s=>e(e.s=s);e.O(0,[3464,3885,3983,8937,8441,1684,7358],()=>s(69179)),_N_E=e.O()}]);