exports.id=1045,exports.ids=[1045,4335],exports.modules={10722:(e,a,t)=>{Promise.resolve().then(t.bind(t,11527))},11285:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});var s=(0,t(6445).A)("outline","plus","IconPlus",[["path",{d:"M12 5l0 14",key:"svg-0"}],["path",{d:"M5 12l14 0",key:"svg-1"}]])},11527:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>x});var s=t(60687),n=t(43210),r=t(24664),l=t(98848),o=t(9535),i=t(37325),d=t(81080),c=t(95994),p=t(80556),u=t(90910),m=t(81172),g=t(20798),h=t(58869),y=t(53411);function x({children:e}){let[a,t]=(0,n.useState)(""),x=[{items:[{title:"Dashboard",href:"/admin/dashboard",icon:(0,s.jsx)(l.A,{})},{title:"Jobs",href:"/admin/jobs",icon:(0,s.jsx)(o.A,{})},{title:"Applications",href:"/admin/applications",icon:(0,s.jsx)(i.A,{})},{title:"Posts",href:"/admin/posts",icon:(0,s.jsx)(g.A,{className:"w-5 h-5"})},{title:"Student Management",href:"/admin/student-management",icon:(0,s.jsx)(h.A,{className:"w-5 h-5"})},{title:"Student Analytics",href:"/admin/analytics",icon:(0,s.jsx)(y.A,{className:"w-5 h-5"})},{title:"Company Management",href:"/admin/companymanagement",icon:(0,s.jsx)(d.A,{})},{title:"Forms",href:"/admin/form",icon:(0,s.jsx)(c.A,{})}]}],b=[{title:"My Profile",href:"/admin/profile",icon:(0,s.jsx)(p.A,{})},{title:"Settings",href:"../settings",icon:(0,s.jsx)(u.A,{})},{title:"Contact Support",href:"/admin/helpandsupport",icon:(0,s.jsx)(m.A,{})}];return(0,s.jsx)("div",{className:"h-screen bg-gray-50 overflow-hidden",children:(0,s.jsxs)("div",{className:"flex h-full",children:[(0,s.jsx)(r.A,{sections:x,bottomItems:b,defaultExpanded:!1,navbarHeight:"0",className:"z-20"}),(0,s.jsx)("div",{className:"flex-1 p-6 ml-20 overflow-y-auto",children:(0,s.jsx)("div",{className:"bg-white rounded-xl shadow-md p-6 min-h-full text-black",children:e})})]})})}},14335:(e,a,t)=>{"use strict";t.d(a,{C1:()=>l,Gu:()=>h,JT:()=>i,RC:()=>d,S0:()=>x,Y_:()=>p,bl:()=>u,dl:()=>y,eK:()=>o,fetchCompanies:()=>r,getCompanyStats:()=>c,jQ:()=>m,mm:()=>n,oY:()=>g});var s=t(58138);function n(e={}){let a=new URLSearchParams;e.page&&a.append("page",e.page),e.per_page&&a.append("per_page",e.per_page),e.tier&&"ALL"!==e.tier&&a.append("tier",e.tier),e.industry&&"ALL"!==e.industry&&a.append("industry",e.industry),e.campus_recruiting&&a.append("campus_recruiting",e.campus_recruiting),e.search&&a.append("search",e.search),e.sort&&a.append("sort",e.sort),a.append("_t",new Date().getTime());let t=a.toString(),r=[`/api/v1/companies/${t?`?${t}`:""}`,`/api/v1/college/default-college/companies/${t?`?${t}`:""}`];return s.A.get(r[0]).catch(e=>(console.log(`Primary endpoint failed: ${e.message}, trying fallback...`),s.A.get(r[1])))}async function r(e={}){try{console.log("Fetching companies from API...");let a=await n(e),t=[];if(a.data&&Array.isArray(a.data)?t=a.data:a.data&&a.data.results&&Array.isArray(a.data.results)?t=a.data.results:a.data&&a.data.data&&Array.isArray(a.data.data)&&(t=a.data.data),console.log(`Retrieved ${t.length} companies from API`),t.length>0)return await Promise.all(t.map(async e=>{try{let a=await l(e.id);return p(a.data)}catch(a){return console.log(`Could not fetch details for company ${e.id}:`,a),p(e)}}));throw Error("No companies returned from API")}catch(a){console.error("Error fetching companies from API:",a),console.log("Falling back to static company data");let{companies:e}=await t.e(1286).then(t.bind(t,61286));return e}}function l(e){let a=[`/api/v1/company/${e}/`,`/api/v1/companies/${e}/`,`/api/v1/college/default-college/companies/${e}/`];return s.A.get(a[0]).catch(e=>(console.log(`First company endpoint failed: ${e.message}, trying second...`),s.A.get(a[1]).catch(e=>(console.log(`Second company endpoint failed: ${e.message}, trying third...`),s.A.get(a[2])))))}function o(e){let a=new FormData;return Object.keys(e).forEach(t=>{"logo"===t&&e[t]instanceof File?a.append(t,e[t]):null!==e[t]&&void 0!==e[t]&&a.append(t,e[t])}),s.A.post("/api/v1/companies/",a,{headers:{"Content-Type":"multipart/form-data"}})}function i(e,a){let t=new FormData;return Object.keys(a).forEach(e=>{"logo"===e&&a[e]instanceof File?t.append(e,a[e]):null!==a[e]&&void 0!==a[e]&&t.append(e,a[e])}),s.A.put(`/api/v1/companies/${e}/`,t,{headers:{"Content-Type":"multipart/form-data"}})}function d(e){return s.A.delete(`/api/v1/companies/${e}/`)}function c(){return s.A.get("/api/v1/companies/stats/")}function p(e){return{id:e.id,name:e.name,logo:e.logo||`https://via.placeholder.com/48x48/4285F4/FFFFFF?text=${e.name.charAt(0)}`,description:e.description||"",industry:e.industry||"",size:e.size||"",founded:e.founded||"",location:e.location||"Location not specified",website:e.website||"",tier:e.tier||"Tier 3",campus_recruiting:e.campus_recruiting||!1,totalActiveJobs:e.total_active_jobs||0,totalApplicants:e.total_applicants||0,totalHired:e.total_hired||0,awaitedApproval:e.pending_approval||0}}function u(e){return s.A.get(`/api/v1/companies/${e}/followers/count/`)}function m(e,a){return s.A.post(`/api/v1/companies/${e}/followers/`,{user_id:a})}function g(e,a){return s.A.delete(`/api/v1/companies/${e}/followers/`,{data:{user_id:a}})}function h(e,a){return s.A.get(`/api/v1/companies/${e}/followers/status/?user_id=${a}`)}function y(e){return s.A.get(`/api/v1/users/${e}/following/`)}function x(){return s.A.get("/api/v1/companies/simple/").then(e=>{if(e.data&&e.data.success)return e.data;throw Error("Failed to fetch companies")}).catch(e=>(console.error("Error fetching companies:",e),{success:!1,data:[],count:0,error:e.message}))}},14401:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});var s=(0,t(6445).A)("outline","search","IconSearch",[["path",{d:"M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0",key:"svg-0"}],["path",{d:"M21 21l-6 -6",key:"svg-1"}]])},20798:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("megaphone",[["path",{d:"m3 11 18-5v12L3 14v-3z",key:"n962bs"}],["path",{d:"M11.6 16.8a3 3 0 1 1-5.8-1.6",key:"1yl0tm"}]])},21513:(e,a,t)=>{"use strict";t.d(a,{A:()=>d});var s=t(60687),n=t(43210),r=t(58843),l=t(14401),o=t(83237),i=t(11285);function d({companies:e,onSubmit:a,onCancel:t,initialData:d={}}){let[c,p]=(0,n.useState)({title:d.title||"",location:d.location||"",salary_min:d.salary_min||"",salary_max:d.salary_max||"",job_type:d.job_type||"FULL_TIME",description:d.description||"",required_skills:d.required_skills||"",requirements:d.requirements||[],skills:d.skills||[],benefits:d.benefits||[],application_deadline:d.application_deadline||"",duration:d.duration||"",company_id:d.company_id||"",company_name:d.company_name||""}),[u,m]=(0,n.useState)(d.interview_rounds||[{name:"",date:"",time:""}]),[g,h]=(0,n.useState)(d.additional_fields||[]),[y,x]=(0,n.useState)(!1),[b,f]=(0,n.useState)(d.company_name||""),[v,j]=(0,n.useState)(!1),[A,N]=(0,n.useState)(d.company_name||""),_=(e,a)=>{p(t=>({...t,[e]:a}))},w=e=>{let a=e.companyName||e.name;p(t=>({...t,company_id:e.id||e.companyName,company_name:a||""})),N(a),f(a),j(!1)},k=e=>{f(e),j(!0),e.trim()||(p(e=>({...e,company_id:"",company_name:""})),N(""))},C=(e||[]).filter(e=>(e.companyName||e.name||"").toLowerCase().includes(b.toLowerCase())),S=(e,a,t)=>{m(s=>s.map((s,n)=>n===e?{...s,[a]:t}:s))},M=e=>{m(a=>a.filter((a,t)=>t!==e))},F=e=>{let a={id:Date.now(),type:e,label:"",required:!1,options:"multiple_choice"===e?[""]:void 0};h(e=>[...e,a])},$=(e,a,t)=>{h(s=>s.map(s=>s.id===e?{...s,[a]:t}:s))},P=e=>{h(a=>a.filter(a=>a.id!==e))},T=e=>{h(a=>a.map(a=>a.id===e?{...a,options:[...a.options||[],""]}:a))},E=(e,a,t)=>{h(s=>s.map(s=>s.id===e?{...s,options:s.options.map((e,s)=>s===a?t:e)}:s))},L=async e=>{e.preventDefault(),x(!0);try{let e={...c,interview_rounds:u.filter(e=>e.name.trim()),additional_fields:g,salary_min:parseFloat(c.salary_min)||0,salary_max:parseFloat(c.salary_max)||0,requirements:c.requirements.filter(e=>e.trim()),skills:c.skills.filter(e=>e.trim()),benefits:c.benefits.filter(e=>e.trim())};await a(e)}catch(e){console.error("Error creating job:",e),alert("Failed to create job posting. Please try again.")}finally{x(!1)}};return(0,s.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Publish Job Posting"}),(0,s.jsx)("button",{onClick:t,className:"text-gray-500 hover:text-gray-700",children:(0,s.jsx)(r.A,{className:"w-6 h-6"})})]}),(0,s.jsxs)("form",{onSubmit:L,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select Company *"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(l.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none w-4 h-4"}),(0,s.jsx)("input",{type:"text",value:b,onChange:e=>k(e.target.value),onFocus:()=>j(!0),placeholder:"Search for a company...",className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0}),A&&(0,s.jsx)("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-xs text-green-600 font-medium",children:"Selected"}),(0,s.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"})]})})]}),v&&b&&(0,s.jsx)("div",{className:"absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto",children:C.length>0?(0,s.jsx)("div",{className:"py-1",children:C.map(e=>(0,s.jsx)("button",{type:"button",onClick:()=>w(e),className:"w-full text-left px-4 py-2 hover:bg-gray-50 focus:bg-gray-50 focus:outline-none border-b border-gray-100 last:border-b-0",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium text-gray-900",children:e.companyName||e.name}),e.location&&(0,s.jsx)("div",{className:"text-sm text-gray-500",children:e.location})]}),e.totalActiveJobs>0&&(0,s.jsxs)("div",{className:"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full",children:[e.totalActiveJobs," jobs"]})]})},e.id||e.companyName))}):(0,s.jsxs)("div",{className:"px-4 py-3 text-sm text-gray-500 text-center",children:['No companies found matching "',b,'"']})}),v&&(0,s.jsx)("div",{className:"fixed inset-0 z-5",onClick:()=>j(!1)})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Job Title *"}),(0,s.jsx)("input",{type:"text",value:c.title,onChange:e=>_("title",e.target.value),placeholder:"Enter job title",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Location *"}),(0,s.jsx)("input",{type:"text",value:c.location,onChange:e=>_("location",e.target.value),placeholder:"Enter location",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Job Type *"}),(0,s.jsxs)("select",{value:c.job_type,onChange:e=>_("job_type",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"FULL_TIME",children:"Full Time"}),(0,s.jsx)("option",{value:"PART_TIME",children:"Part Time"}),(0,s.jsx)("option",{value:"INTERNSHIP",children:"Internship"}),(0,s.jsx)("option",{value:"CONTRACT",children:"Contract"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Min Salary"}),(0,s.jsx)("input",{type:"number",value:c.salary_min,onChange:e=>_("salary_min",e.target.value),placeholder:"Enter minimum salary",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Max Salary"}),(0,s.jsx)("input",{type:"number",value:c.salary_max,onChange:e=>_("salary_max",e.target.value),placeholder:"Enter maximum salary",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Duration"}),(0,s.jsx)("input",{type:"text",value:c.duration,onChange:e=>_("duration",e.target.value),placeholder:"e.g., 6 months, 2 years",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Application Deadline *"}),(0,s.jsx)("input",{type:"date",value:c.application_deadline,onChange:e=>_("application_deadline",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Job Description *"}),(0,s.jsx)("textarea",{value:c.description,onChange:e=>_("description",e.target.value),placeholder:"Enter job description",rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Required Skills *"}),(0,s.jsx)("textarea",{value:c.required_skills,onChange:e=>_("required_skills",e.target.value),placeholder:"Enter required skills (e.g., Python, React, SQL, etc.)",rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Interview Timeline"}),u.map((e,a)=>(0,s.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4 mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,s.jsxs)("h4",{className:"font-medium text-gray-700",children:["Round ",a+1]}),a>0&&(0,s.jsx)("button",{type:"button",onClick:()=>M(a),className:"text-red-500 hover:text-red-700",children:(0,s.jsx)(o.A,{className:"w-4 h-4"})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3",children:[(0,s.jsx)("input",{type:"text",value:e.name,onChange:e=>S(a,"name",e.target.value),placeholder:"Enter round name",className:"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,s.jsx)("input",{type:"date",value:e.date,onChange:e=>S(a,"date",e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,s.jsx)("input",{type:"time",value:e.time,onChange:e=>S(a,"time",e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]},a)),(0,s.jsxs)("button",{type:"button",onClick:()=>{m(e=>[...e,{name:"",date:"",time:""}])},className:"flex items-center gap-2 px-4 py-2 text-blue-600 border border-blue-600 rounded-lg hover:bg-blue-50 transition-colors",children:[(0,s.jsx)(i.A,{className:"w-4 h-4"}),"Add Round"]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Additional Fields"}),g.map(e=>(0,s.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4 mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,s.jsx)("input",{type:"text",value:e.label,onChange:a=>$(e.id,"label",a.target.value),placeholder:"Field label",className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 mr-3"}),(0,s.jsx)("button",{type:"button",onClick:()=>P(e.id),className:"text-red-500 hover:text-red-700",children:(0,s.jsx)(o.A,{className:"w-4 h-4"})})]}),"multiple_choice"===e.type&&(0,s.jsxs)("div",{className:"space-y-2",children:[e.options?.map((a,t)=>(0,s.jsx)("input",{type:"text",value:a,onChange:a=>E(e.id,t,a.target.value),placeholder:`Option ${t+1}`,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"},t)),(0,s.jsx)("button",{type:"button",onClick:()=>T(e.id),className:"text-blue-600 text-sm hover:text-blue-800",children:"+ Add Option"})]})]},e.id)),(0,s.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,s.jsxs)("button",{type:"button",onClick:()=>F("text"),className:"flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:[(0,s.jsx)(i.A,{className:"w-4 h-4"}),"Add Text Field"]}),(0,s.jsxs)("button",{type:"button",onClick:()=>F("number"),className:"flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:[(0,s.jsx)(i.A,{className:"w-4 h-4"}),"Add Number Field"]}),(0,s.jsxs)("button",{type:"button",onClick:()=>F("file"),className:"flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:[(0,s.jsx)(i.A,{className:"w-4 h-4"}),"Add File Upload Field"]}),(0,s.jsxs)("button",{type:"button",onClick:()=>F("multiple_choice"),className:"flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:[(0,s.jsx)(i.A,{className:"w-4 h-4"}),"Add Multiple Choice Field"]})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-end gap-4 pt-6 border-t border-gray-200",children:[(0,s.jsx)("button",{type:"button",onClick:t,className:"px-6 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:"Cancel"}),(0,s.jsx)("button",{type:"submit",disabled:y,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:y?"Publishing...":"Publish Job"})]})]})]})})}t(75518)},23697:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\layout.jsx","default")},37325:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});var s=(0,t(6445).A)("outline","clipboard-list","IconClipboardList",[["path",{d:"M9 5h-2a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-12a2 2 0 0 0 -2 -2h-2",key:"svg-0"}],["path",{d:"M9 3m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z",key:"svg-1"}],["path",{d:"M9 12l.01 0",key:"svg-2"}],["path",{d:"M13 12l2 0",key:"svg-3"}],["path",{d:"M9 16l.01 0",key:"svg-4"}],["path",{d:"M13 16l2 0",key:"svg-5"}]])},48173:(e,a,t)=>{Promise.resolve().then(t.bind(t,23697))},53411:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},75518:(e,a,t)=>{"use strict";t.d(a,{G$:()=>o,N6:()=>n,Om:()=>c,T4:()=>p,YQ:()=>l,_S:()=>i,lh:()=>d,vr:()=>r});var s=t(58138);function n(e={}){let a=new URLSearchParams;e.page&&a.append("page",e.page),e.per_page&&a.append("per_page",e.per_page),e.job_type&&"ALL"!==e.job_type&&a.append("job_type",e.job_type),e.location&&"ALL"!==e.location&&a.append("location",e.location),e.salary_min&&a.append("salary_min",e.salary_min),e.search&&a.append("search",e.search);let t=a.toString(),r=`/api/v1/college/default-college/jobs/${t?`?${t}`:""}`;return s.A.get(r)}function r(e,a,t={}){if(!Object.values(t).some(e=>e instanceof File))return s.A.post(`/api/v1/college/default-college/jobs/${e}/apply/`,{cover_letter:a,additional_field_responses:t});{let n=new FormData;return n.append("cover_letter",a),Object.entries(t).forEach(([e,a])=>{a instanceof File?n.append(e,a):n.append(e,JSON.stringify(a))}),s.A.post(`/api/v1/college/default-college/jobs/${e}/apply/`,n,{headers:{"Content-Type":"multipart/form-data"}})}}function l(e){return s.A.get(`/api/v1/college/default-college/jobs/${e}/`)}function o(){return s.A.get("/api/v1/college/default-college/jobs/applied/")}function i(e){return s.A.post("/api/v1/college/default-college/jobs/create/",e)}function d(e,a){return s.A.put(`/api/v1/college/default-college/jobs/${e}/`,a)}function c(e={}){let a=new URLSearchParams;e.page&&a.append("page",e.page),e.per_page&&a.append("per_page",e.per_page),e.search&&a.append("search",e.search),e.type&&"All"!==e.type&&a.append("job_type",e.type),e.minCTC&&a.append("salary_min",e.minCTC),e.maxCTC&&a.append("salary_max",e.maxCTC),e.minStipend&&a.append("stipend_min",e.minStipend),e.maxStipend&&a.append("stipend_max",e.maxStipend),e.location&&a.append("location",e.location),void 0!==e.is_published&&a.append("is_published",e.is_published),e.company_id&&a.append("company_id",e.company_id),e.company_name&&a.append("company_name",e.company_name);let t=a.toString(),n=`/api/v1/college/default-college/jobs/admin/${t?`?${t}`:""}`;return console.log("\uD83C\uDF10 listJobsAdmin calling URL:",n,"with params:",e),s.A.get(n).then(e=>(console.log("\uD83C\uDF10 listJobsAdmin response:",{status:e.status,totalJobs:e.data?.pagination?.total_count||0,currentPage:e.data?.pagination?.current_page||1,totalPages:e.data?.pagination?.total_pages||1}),e)).catch(e=>{throw console.error("\uD83C\uDF10 listJobsAdmin error:",e),console.error("\uD83C\uDF10 listJobsAdmin error response:",e.response?.data),e})}function p(e){return s.A.patch(`/api/v1/jobs/${e}/toggle-publish/`)}},81172:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});var s=(0,t(6445).A)("outline","help","IconHelp",[["path",{d:"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0",key:"svg-0"}],["path",{d:"M12 17l0 .01",key:"svg-1"}],["path",{d:"M12 13.5a1.5 1.5 0 0 1 1 -1.5a2.6 2.6 0 1 0 -3 -4",key:"svg-2"}]])},83237:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});var s=(0,t(6445).A)("outline","trash","IconTrash",[["path",{d:"M4 7l16 0",key:"svg-0"}],["path",{d:"M10 11l0 6",key:"svg-1"}],["path",{d:"M14 11l0 6",key:"svg-2"}],["path",{d:"M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2 -2l1 -12",key:"svg-3"}],["path",{d:"M9 7v-3a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v3",key:"svg-4"}]])},95994:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});var s=(0,t(6445).A)("outline","forms","IconForms",[["path",{d:"M12 3a3 3 0 0 0 -3 3v12a3 3 0 0 0 3 3",key:"svg-0"}],["path",{d:"M6 3a3 3 0 0 1 3 3v12a3 3 0 0 1 -3 3",key:"svg-1"}],["path",{d:"M13 7h7a1 1 0 0 1 1 1v8a1 1 0 0 1 -1 1h-7",key:"svg-2"}],["path",{d:"M5 7h-1a1 1 0 0 0 -1 1v8a1 1 0 0 0 1 1h1",key:"svg-3"}],["path",{d:"M17 12h.01",key:"svg-4"}],["path",{d:"M13 12h.01",key:"svg-5"}]])}};