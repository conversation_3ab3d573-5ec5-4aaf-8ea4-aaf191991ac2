(()=>{var e={};e.id=1252,e.ids=[1252],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30564:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var r=s(65239),a=s(48088),n=s(88170),i=s.n(n),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d={children:["",{children:["admin",{children:["companymanagement",{children:["edit",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,78568)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\companymanagement\\edit\\[id]\\page.jsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,23697)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\layout.jsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,75535)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\layout.js"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\companymanagement\\edit\\[id]\\page.jsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/companymanagement/edit/[id]/page",pathname:"/admin/companymanagement/edit/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68412:(e,t,s)=>{Promise.resolve().then(s.bind(s,78568))},74075:e=>{"use strict";e.exports=require("zlib")},74902:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>j});var r=s(60687),a=s(43210),n=s(16189),i=s(43125),l=s(5336),o=s(28559),d=s(35071),c=s(9005),m=s(16023),u=s(79410),p=s(97992),x=s(41312),h=s(40228),g=s(47342),b=s(64398),y=s(8819),f=s(14335);function j({params:e}){let t=(0,a.use)(e).id,s=(0,n.useRouter)(),[j,v]=(0,a.useState)(!1),[N,w]=(0,a.useState)({}),[C,_]=(0,a.useState)(null),[k,q]=(0,a.useState)(""),[A,P]=(0,a.useState)(!1),[S,E]=(0,a.useState)(!0),D=(0,a.useRef)(null),[R,z]=(0,a.useState)({name:"",industry:"",location:"",size:"",founded:"",website:"",tier:"Tier 3",description:"",campus_recruiting:!1,total_active_jobs:0,total_applicants:0,total_hired:0,awaited_approval:0,logo:null}),T=e=>{let{name:t,value:s,type:r,checked:a}=e.target;z({...R,[t]:"checkbox"===r?a:s})},U=()=>{let e={};return R.name.trim()||(e.name="Company name is required"),R.industry.trim()||(e.industry="Industry is required"),R.location.trim()||(e.location="Location is required"),R.size.trim()||(e.size="Company size is required"),R.founded.trim()||(e.founded="Founded year is required"),R.description.trim()||(e.description="Description is required"),R.website.trim()?L(R.website)||(e.website="Please enter a valid URL (include http:// or https://)"):e.website="Website URL is required",w(e),0===Object.keys(e).length},L=e=>{try{return new URL(e),!0}catch(e){return!1}},F=async e=>{if(e.preventDefault(),U()){P(!0);try{let e={...R,total_active_jobs:Number(R.total_active_jobs),total_applicants:Number(R.total_applicants),total_hired:Number(R.total_hired),awaited_approval:Number(R.awaited_approval),campus_recruiting:!!R.campus_recruiting};C&&(e.logo=C),console.log("Submitting updated company data:",e);let r=await (0,f.JT)(t,e);console.log("API response:",r),v(!0),setTimeout(()=>{s.push("/admin/companymanagement")},1500)}catch(e){console.error("Error updating company:",e),e.response?.data?(console.log("API error details:",e.response.data),w(t=>({...t,...e.response.data,...e.response.data.non_field_errors?{api_error:e.response.data.non_field_errors.join(", ")}:{}}))):w(e=>({...e,api_error:"Failed to update company. Please try again."}))}finally{P(!1)}}},O=()=>{s.push("/admin/companymanagement")};return S?(0,r.jsx)("div",{className:"h-full flex items-center justify-center bg-gray-50",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(i.A,{className:"w-12 h-12 animate-spin mx-auto mb-4 text-blue-500"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading company data..."})]})}):j?(0,r.jsx)("div",{className:"h-full flex items-center justify-center bg-gray-50",children:(0,r.jsx)("div",{className:"bg-white p-8 rounded-xl shadow-sm border border-gray-200 max-w-md w-full",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4",children:(0,r.jsx)(l.A,{className:"w-8 h-8 text-green-600"})}),(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Company Updated!"}),(0,r.jsx)("p",{className:"text-gray-600 text-center mb-6",children:"The company profile has been successfully updated."}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Redirecting back to company management..."})]})})}):(0,r.jsx)("div",{className:"h-full overflow-y-auto bg-gray-50",children:(0,r.jsxs)("div",{className:"container mx-auto py-8 px-4 md:px-6",children:[(0,r.jsxs)("div",{className:"flex items-center mb-6",children:[(0,r.jsx)("button",{onClick:O,className:"mr-4 p-2 rounded-full hover:bg-gray-200 transition-colors",children:(0,r.jsx)(o.A,{className:"w-5 h-5 text-gray-600"})}),(0,r.jsxs)("h1",{className:"text-2xl font-bold text-gray-900",children:["Edit Company: ",R.name]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 md:p-8",children:[N.api_error&&(0,r.jsxs)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg text-red-700 flex items-start gap-3",children:[(0,r.jsx)(d.A,{className:"w-5 h-5 text-red-500 flex-shrink-0 mt-0.5"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:"Error"}),(0,r.jsx)("p",{children:N.api_error})]})]}),(0,r.jsxs)("form",{onSubmit:F,children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"md:col-span-2",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Company Logo"}),(0,r.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,r.jsx)("div",{className:`w-24 h-24 rounded-lg flex items-center justify-center overflow-hidden ${k?"border border-gray-200":"bg-gradient-to-br from-blue-500 to-purple-600"}`,children:k?(0,r.jsx)("img",{src:k,alt:"Company logo preview",className:"w-full h-full object-cover"}):R.name?(0,r.jsx)("span",{className:"text-white font-bold text-2xl",children:R.name.charAt(0)}):(0,r.jsx)(c.A,{className:"w-10 h-10 text-white opacity-70"})}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)("button",{type:"button",onClick:()=>{D.current.click()},className:"px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg flex items-center gap-2",children:[(0,r.jsx)(m.A,{className:"w-4 h-4"}),k?"Change Logo":"Upload Logo"]}),k&&(0,r.jsx)("button",{type:"button",onClick:()=>{_(null),q(""),D.current&&(D.current.value="")},className:"px-4 py-2 bg-red-50 hover:bg-red-100 text-red-600 rounded-lg",children:"Remove"}),(0,r.jsx)("input",{ref:D,type:"file",accept:"image/*",onChange:e=>{let t=e.target.files[0];if(t){_(t);let e=new FileReader;e.onloadend=()=>{q(e.result)},e.readAsDataURL(t)}},className:"hidden"})]}),(0,r.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"Recommended: Square image, at least 200x200px (.jpg, .png)"})]})]})]}),(0,r.jsxs)("div",{className:"md:col-span-2",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Company Name*"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(u.A,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsx)("input",{type:"text",name:"name",value:R.name,onChange:T,className:`pl-10 w-full rounded-lg border ${N.name?"border-red-300":"border-gray-300"} py-3 px-4 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500`,placeholder:"Enter company name"})]}),N.name&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:N.name})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Industry*"}),(0,r.jsxs)("select",{name:"industry",value:R.industry,onChange:T,className:`w-full rounded-lg border ${N.industry?"border-red-300":"border-gray-300"} py-3 px-4 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500`,children:[(0,r.jsx)("option",{value:"",children:"Select Industry"}),(0,r.jsx)("option",{value:"Technology",children:"Technology"}),(0,r.jsx)("option",{value:"Finance",children:"Finance"}),(0,r.jsx)("option",{value:"Healthcare",children:"Healthcare"}),(0,r.jsx)("option",{value:"Education",children:"Education"}),(0,r.jsx)("option",{value:"Manufacturing",children:"Manufacturing"}),(0,r.jsx)("option",{value:"Retail",children:"Retail"}),(0,r.jsx)("option",{value:"Consulting",children:"Consulting"}),(0,r.jsx)("option",{value:"Media",children:"Media"}),(0,r.jsx)("option",{value:"Other",children:"Other"})]}),N.industry&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:N.industry})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Location*"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(p.A,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsx)("input",{type:"text",name:"location",value:R.location,onChange:T,className:`pl-10 w-full rounded-lg border ${N.location?"border-red-300":"border-gray-300"} py-3 px-4 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500`,placeholder:"e.g., New York, NY"})]}),N.location&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:N.location})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Company Size*"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(x.A,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsxs)("select",{name:"size",value:R.size,onChange:T,className:`pl-10 w-full rounded-lg border ${N.size?"border-red-300":"border-gray-300"} py-3 px-4 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500`,children:[(0,r.jsx)("option",{value:"",children:"Select Size"}),(0,r.jsx)("option",{value:"1-10 employees",children:"1-10 employees"}),(0,r.jsx)("option",{value:"11-50 employees",children:"11-50 employees"}),(0,r.jsx)("option",{value:"51-200 employees",children:"51-200 employees"}),(0,r.jsx)("option",{value:"201-500 employees",children:"201-500 employees"}),(0,r.jsx)("option",{value:"501-1000 employees",children:"501-1000 employees"}),(0,r.jsx)("option",{value:"1001-5000 employees",children:"1001-5000 employees"}),(0,r.jsx)("option",{value:"5001+ employees",children:"5001+ employees"})]})]}),N.size&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:N.size})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Founded Year*"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(h.A,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsx)("input",{type:"number",name:"founded",min:"1800",max:new Date().getFullYear(),value:R.founded,onChange:T,className:`pl-10 w-full rounded-lg border ${N.founded?"border-red-300":"border-gray-300"} py-3 px-4 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500`,placeholder:"e.g., 2010"})]}),N.founded&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:N.founded})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Website URL*"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(g.A,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsx)("input",{type:"url",name:"website",value:R.website,onChange:T,className:`pl-10 w-full rounded-lg border ${N.website?"border-red-300":"border-gray-300"} py-3 px-4 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500`,placeholder:"https://example.com"})]}),N.website&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:N.website})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Tier"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(b.A,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsxs)("select",{name:"tier",value:R.tier,onChange:T,className:"pl-10 w-full rounded-lg border border-gray-300 py-3 px-4 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"Tier 1",children:"Tier 1"}),(0,r.jsx)("option",{value:"Tier 2",children:"Tier 2"}),(0,r.jsx)("option",{value:"Tier 3",children:"Tier 3"})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("input",{type:"checkbox",id:"campus_recruiting",name:"campus_recruiting",checked:R.campus_recruiting,onChange:T,className:"h-5 w-5 text-blue-600 rounded border-gray-300 focus:ring-blue-500"}),(0,r.jsx)("label",{htmlFor:"campus_recruiting",className:"text-sm font-medium text-gray-700",children:"Campus Recruiting Program"})]}),(0,r.jsxs)("div",{className:"md:col-span-2",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Description*"}),(0,r.jsx)("textarea",{name:"description",value:R.description,onChange:T,rows:"4",className:`w-full rounded-lg border ${N.description?"border-red-300":"border-gray-300"} py-3 px-4 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500`,placeholder:"Enter a description of the company..."}),N.description&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:N.description})]})]}),(0,r.jsxs)("div",{className:"mt-8 flex flex-col md:flex-row gap-4 md:gap-3 justify-end",children:[(0,r.jsx)("button",{type:"button",onClick:O,className:"px-6 py-3 border border-gray-300 rounded-lg text-gray-700 bg-white hover:bg-gray-50 font-medium",disabled:A,children:"Cancel"}),(0,r.jsx)("button",{type:"submit",className:"px-6 py-3 bg-blue-600 rounded-lg text-white hover:bg-blue-700 font-medium flex items-center justify-center gap-2",disabled:A,children:A?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(i.A,{className:"w-5 h-5 animate-spin"}),"Updating..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(y.A,{className:"w-5 h-5"}),"Save Changes"]})})]})]})]})]})})}},78140:(e,t,s)=>{Promise.resolve().then(s.bind(s,74902))},78568:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\edit\\\\[id]\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\companymanagement\\edit\\[id]\\page.jsx","default")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,681,1658,1060,2305,1372],()=>s(30564));module.exports=r})();