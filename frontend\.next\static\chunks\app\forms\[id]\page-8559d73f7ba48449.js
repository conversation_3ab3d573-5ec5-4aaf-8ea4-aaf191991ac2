(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5328],{1243:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},7792:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>c});var r=a(95155),s=a(12115),l=a(35695),o=a(85329);function c(){let{id:e}=(0,l.useParams)(),t=(0,l.useRouter)(),[a,c]=(0,s.useState)(!1),[n,i]=(0,s.useState)(""),[u,d]=(0,s.useState)(null),[h,p]=(0,s.useState)({description:"",skills:"",salaryMin:"",salaryMax:"",deadline:""});(0,s.useEffect)(()=>{(async()=>{try{let t=await (0,o.Jy)(e);d(t.data),t.data.submitted&&t.data.details&&p(t.data.details),localStorage.getItem("form-access-".concat(e))===t.data.key&&c(!0)}catch(e){console.error("Error fetching form:",e)}})()},[e]);let m=async()=>{try{await (0,o.wi)(e,{submitted:!0,details:h}),localStorage.removeItem("form-access-".concat(e)),t.push("/thank-you")}catch(e){console.error("Error submitting form:",e),alert("Failed to submit form. Please try again.")}};return u?a?(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 p-6",children:(0,r.jsxs)("div",{className:"max-w-3xl mx-auto bg-white p-6 rounded-lg shadow",children:[(0,r.jsxs)("h2",{className:"text-2xl font-bold text-black mb-6",children:["Fill Job Details for ",u.title]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("textarea",{rows:4,placeholder:"Job Description",value:h.description,onChange:e=>p({...h,description:e.target.value}),className:"w-full border p-3 rounded focus:ring-2 text-black focus:ring-blue-500"}),(0,r.jsx)("input",{type:"text",placeholder:"Required Skills (comma-separated)",value:h.skills,onChange:e=>p({...h,skills:e.target.value}),className:"w-full border p-2 rounded focus:ring-2 text-black focus:ring-blue-500"}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)("input",{type:"number",placeholder:"Min Salary",value:h.salaryMin,onChange:e=>p({...h,salaryMin:e.target.value}),className:"w-1/2 border p-2 rounded focus:ring-2 text-black focus:ring-blue-500"}),(0,r.jsx)("input",{type:"number",placeholder:"Max Salary",value:h.salaryMax,onChange:e=>p({...h,salaryMax:e.target.value}),className:"w-1/2 border p-2 rounded focus:ring-2 text-black focus:ring-blue-500"})]}),(0,r.jsx)("input",{type:"date",value:h.deadline,onChange:e=>p({...h,deadline:e.target.value}),className:"w-full border p-2 rounded focus:ring-2 text-black focus:ring-blue-500"}),(0,r.jsx)("div",{className:"pt-4",children:(0,r.jsx)("button",{onClick:m,className:"w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700",children:"Submit Form"})})]})]})}):(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 p-6",children:(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow w-full max-w-md",children:[(0,r.jsx)("h2",{className:"text-lg font-bold mb-4 text-black",children:"Enter Access Key"}),(0,r.jsx)("input",{type:"text",value:n,onChange:e=>i(e.target.value),className:"w-full p-2 border rounded mb-4 focus:ring-2 text-black focus:ring-blue-500",placeholder:"Enter the key provided"}),(0,r.jsx)("button",{onClick:()=>{n===(null==u?void 0:u.key)?(c(!0),localStorage.setItem("form-access-".concat(e),n)):alert("Invalid access key")},className:"w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700",children:"Submit Key"})]})}):(0,r.jsx)("div",{className:"p-6",children:"Form not found."})}},19946:(e,t,a)=>{"use strict";a.d(t,{A:()=>d});var r=a(12115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,a)=>a?a.toUpperCase():t.toLowerCase()),o=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},c=function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return t.filter((e,t,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===t).join(" ").trim()},n=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,r.forwardRef)((e,t)=>{let{color:a="currentColor",size:s=24,strokeWidth:l=2,absoluteStrokeWidth:o,className:u="",children:d,iconNode:h,...p}=e;return(0,r.createElement)("svg",{ref:t,...i,width:s,height:s,stroke:a,strokeWidth:o?24*Number(l)/Number(s):l,className:c("lucide",u),...!d&&!n(p)&&{"aria-hidden":"true"},...p},[...h.map(e=>{let[t,a]=e;return(0,r.createElement)(t,a)}),...Array.isArray(d)?d:[d]])}),d=(e,t)=>{let a=(0,r.forwardRef)((a,l)=>{let{className:n,...i}=a;return(0,r.createElement)(u,{ref:l,iconNode:t,className:c("lucide-".concat(s(o(e))),"lucide-".concat(e),n),...i})});return a.displayName=o(e),a}},28052:(e,t,a)=>{Promise.resolve().then(a.bind(a,7792))},29869:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},33786:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},35695:(e,t,a)=>{"use strict";var r=a(18999);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"usePathname")&&a.d(t,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},37719:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var r=a(23464);a(73983);let s=r.A.create({baseURL:"http://127.0.0.1:8000",headers:{"Content-Type":"application/json"}});s.interceptors.request.use(e=>{let t=localStorage.getItem("access_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),s.interceptors.response.use(e=>e,async e=>{var t;let a=e.config;if((null==(t=e.response)?void 0:t.status)===401&&!a._retry){a._retry=!0;try{let e=localStorage.getItem("refresh_token");if(e){let t=await r.A.post("http://127.0.0.1:8000/api/auth/token/refresh/",{refresh:e});return localStorage.setItem("access_token",t.data.access),a.headers.Authorization="Bearer ".concat(t.data.access),s(a)}}catch(e){console.error("Error refreshing token:",e),localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),window.location.href="/login"}}return Promise.reject(e)});let l=s},40646:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},54416:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},71007:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},75525:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},76517:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]])},81284:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},85329:(e,t,a)=>{"use strict";a.d(t,{DG:()=>l,Jy:()=>o,Uq:()=>s,i6:()=>n,wi:()=>c});var r=a(37719);function s(){return r.A.get("/api/v1/jobs/forms/")}function l(e){return r.A.post("/api/v1/jobs/forms/",e)}function o(e){return r.A.get("/api/v1/jobs/forms/".concat(e,"/"))}function c(e,t){return r.A.patch("/api/v1/jobs/forms/".concat(e,"/"),t)}function n(e){return r.A.post("/api/v1/jobs/forms/".concat(e,"/delete/"))}},85339:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[3464,3983,8441,1684,7358],()=>t(28052)),_N_E=e.O()}]);