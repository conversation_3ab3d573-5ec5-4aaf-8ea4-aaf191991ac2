(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9755],{3442:(e,t,r)=>{Promise.resolve().then(r.bind(r,76091))},30699:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var s=(0,r(86467).A)("outline","check","IconCheck",[["path",{d:"M5 12l5 5l10 -10",key:"svg-0"}]])},76091:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(95155);r(6874);var l=r(30699);function n(){return(0,s.jsx)("div",{className:"min-h-screen flex flex-col items-center justify-center bg-gray-50 px-4",children:(0,s.jsxs)("div",{className:"bg-white shadow-lg rounded-2xl p-10 max-w-md text-center",children:[(0,s.jsx)("div",{className:"flex items-center justify-center w-16 h-16 rounded-full bg-green-100 mx-auto mb-4",children:(0,s.jsx)(l.A,{size:32,className:"text-green-600"})}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Thank You!"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:"Your form has been submitted successfully. We appreciate your time."})]})})}},86467:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(12115),l={outline:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"},filled:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",stroke:"none"}};let n=(e,t,r,n)=>{let i=(0,s.forwardRef)((r,i)=>{let{color:a="currentColor",size:c=24,stroke:o=2,title:d,className:h,children:u,...m}=r;return(0,s.createElement)("svg",{ref:i,...l[e],width:c,height:c,className:["tabler-icon","tabler-icon-".concat(t),h].join(" "),..."filled"===e?{fill:a}:{strokeWidth:o,stroke:a},...m},[d&&(0,s.createElement)("title",{key:"svg-title"},d),...n.map(e=>{let[t,r]=e;return(0,s.createElement)(t,r)}),...Array.isArray(u)?u:[u]])});return i.displayName="".concat(r),i}}},e=>{var t=t=>e(e.s=t);e.O(0,[6874,8441,1684,7358],()=>t(3442)),_N_E=e.O()}]);