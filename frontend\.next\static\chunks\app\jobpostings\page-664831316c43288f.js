(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7633],{25384:(e,s,a)=>{"use strict";a.d(s,{G_:()=>l});var t=a(95155);function l(e){let{description:s,className:a=""}=e,l=s?s.replace(/\n/g,"<br />").replace(/•/g,"•").replace(/\*\s/g,"• ").replace(/-\s/g,"• ").trim():"No description provided.";return(0,t.jsx)("div",{className:"text-gray-700 leading-relaxed ".concat(a),dangerouslySetInnerHTML:{__html:l}})}},34842:(e,s,a)=>{"use strict";a.d(s,{G$:()=>i,N6:()=>l,Om:()=>o,T4:()=>x,YQ:()=>n,_S:()=>c,lh:()=>d,vr:()=>r});var t=a(37719);function l(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},s=new URLSearchParams;e.page&&s.append("page",e.page),e.per_page&&s.append("per_page",e.per_page),e.job_type&&"ALL"!==e.job_type&&s.append("job_type",e.job_type),e.location&&"ALL"!==e.location&&s.append("location",e.location),e.salary_min&&s.append("salary_min",e.salary_min),e.search&&s.append("search",e.search);let a=s.toString();return t.A.get("/api/v1/college/default-college/jobs/".concat(a?"?".concat(a):""))}function r(e,s){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!Object.values(a).some(e=>e instanceof File))return t.A.post("/api/v1/college/default-college/jobs/".concat(e,"/apply/"),{cover_letter:s,additional_field_responses:a});{let l=new FormData;return l.append("cover_letter",s),Object.entries(a).forEach(e=>{let[s,a]=e;a instanceof File?l.append(s,a):l.append(s,JSON.stringify(a))}),t.A.post("/api/v1/college/default-college/jobs/".concat(e,"/apply/"),l,{headers:{"Content-Type":"multipart/form-data"}})}}function n(e){return t.A.get("/api/v1/college/default-college/jobs/".concat(e,"/"))}function i(){return t.A.get("/api/v1/college/default-college/jobs/applied/")}function c(e){return t.A.post("/api/v1/college/default-college/jobs/create/",e)}function d(e,s){return t.A.put("/api/v1/college/default-college/jobs/".concat(e,"/"),s)}function o(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},s=new URLSearchParams;e.page&&s.append("page",e.page),e.per_page&&s.append("per_page",e.per_page),e.search&&s.append("search",e.search),e.type&&"All"!==e.type&&s.append("job_type",e.type),e.minCTC&&s.append("salary_min",e.minCTC),e.maxCTC&&s.append("salary_max",e.maxCTC),e.minStipend&&s.append("stipend_min",e.minStipend),e.maxStipend&&s.append("stipend_max",e.maxStipend),e.location&&s.append("location",e.location),void 0!==e.is_published&&s.append("is_published",e.is_published),e.company_id&&s.append("company_id",e.company_id),e.company_name&&s.append("company_name",e.company_name);let a=s.toString(),l="/api/v1/college/default-college/jobs/admin/".concat(a?"?".concat(a):"");return console.log("\uD83C\uDF10 listJobsAdmin calling URL:",l,"with params:",e),t.A.get(l).then(e=>{var s,a,t,l,r,n;return console.log("\uD83C\uDF10 listJobsAdmin response:",{status:e.status,totalJobs:(null==(a=e.data)||null==(s=a.pagination)?void 0:s.total_count)||0,currentPage:(null==(l=e.data)||null==(t=l.pagination)?void 0:t.current_page)||1,totalPages:(null==(n=e.data)||null==(r=n.pagination)?void 0:r.total_pages)||1}),e}).catch(e=>{var s;throw console.error("\uD83C\uDF10 listJobsAdmin error:",e),console.error("\uD83C\uDF10 listJobsAdmin error response:",null==(s=e.response)?void 0:s.data),e})}function x(e){return t.A.patch("/api/v1/jobs/".concat(e,"/toggle-publish/"))}},37719:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});var t=a(23464);a(73983);let l=t.A.create({baseURL:"http://127.0.0.1:8000",headers:{"Content-Type":"application/json"}});l.interceptors.request.use(e=>{let s=localStorage.getItem("access_token");return s&&(e.headers.Authorization="Bearer ".concat(s)),e},e=>Promise.reject(e)),l.interceptors.response.use(e=>e,async e=>{var s;let a=e.config;if((null==(s=e.response)?void 0:s.status)===401&&!a._retry){a._retry=!0;try{let e=localStorage.getItem("refresh_token");if(e){let s=await t.A.post("http://127.0.0.1:8000/api/auth/token/refresh/",{refresh:e});return localStorage.setItem("access_token",s.data.access),a.headers.Authorization="Bearer ".concat(s.data.access),l(a)}}catch(e){console.error("Error refreshing token:",e),localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),window.location.href="/login"}}return Promise.reject(e)});let r=l},48060:(e,s,a)=>{Promise.resolve().then(a.bind(a,90661))},90661:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>v});var t=a(95155),l=a(12115),r=a(17576),n=a(87949),i=a(34869),c=a(51976),d=a(47924),o=a(23829),x=a(62108),m=a(23227),p=a(4516),h=a(55868),u=a(40646),g=a(14186),b=a(69074),j=a(17580),f=a(34842),y=a(25384),N=a(52338);function v(){let[e,s]=(0,l.useState)([]),[a,v]=(0,l.useState)(null),[w,_]=(0,l.useState)(""),[A,k]=(0,l.useState)("ALL"),[S,L]=(0,l.useState)("ALL"),[C,T]=(0,l.useState)(new Set),[q,I]=(0,l.useState)(!1),[P,E]=(0,l.useState)(null),[R,M]=(0,l.useState)(new Map),[J,F]=(0,l.useState)(!1),[z,D]=(0,l.useState)(!1),[U,$]=(0,l.useState)(1),[B,H]=(0,l.useState)({current_page:1,total_pages:1,total_count:0,per_page:10,has_next:!1,has_previous:!1}),[O,W]=(0,l.useState)(!1),G=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;W(!0);try{let t=await (0,f.N6)({page:e,per_page:10,search:w,job_type:A,location:S}),l=[];if(t.data&&t.data.data&&Array.isArray(t.data.data)){l=t.data.data;let e=t.data.pagination,s=Math.ceil(e.total_count/e.per_page),a={...e,total_pages:s,has_next:e.current_page<s,has_previous:e.current_page>1};H(a)}else Array.isArray(t.data)&&(l=t.data,console.log("No pagination data in response"));s(l),$(e),l.length>0&&!a&&(console.log("First job data:",l[0]),console.log("Requirements field:",{value:l[0].requirements,type:typeof l[0].requirements,isArray:Array.isArray(l[0].requirements)}),console.log("Additional fields:",l[0].additional_fields),console.log("Interview rounds:",l[0].interview_rounds),v(l[0]))}catch(e){console.error("Failed to load jobs:",e),s([]),H({current_page:1,total_pages:1,total_count:0,per_page:10,has_next:!1,has_previous:!1})}finally{W(!1)}},Y=async()=>{try{let e=await N.N.getFreezeStatus();E(e)}catch(e){console.error("Failed to fetch freeze status:",e)}},Q=async e=>{try{let s=await N.N.canApplyToJob(e);return M(a=>new Map(a.set(e,s))),s}catch(e){return console.error("Failed to check job eligibility:",e),{can_apply:!0}}},K=async()=>{a&&((await Q(a.id)).can_apply||alert("You have restrictions that may prevent you from applying to this job. Please check the application page for details."),window.location.href="/jobpostings/".concat(a.id,"/apply"))};(0,l.useEffect)(()=>{G(1),Y();let e=localStorage.getItem("savedJobs");e&&T(new Set(JSON.parse(e)))},[]),(0,l.useEffect)(()=>{let e=setTimeout(()=>{1===U||$(1),G(1)},500);return()=>clearTimeout(e)},[w,A,S]),(0,l.useEffect)(()=>{let e=()=>{F(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);let V=Array.isArray(e)?e:[],X={total:B.total_count,internships:V.filter(e=>"INTERNSHIP"===e.job_type).length,fullTime:V.filter(e=>"FULL_TIME"===e.job_type).length,remote:V.filter(e=>(e.location||"").toLowerCase().includes("remote")).length},Z=e=>{let s=new Set(C);s.has(e)?s.delete(e):s.add(e),T(s),localStorage.setItem("savedJobs",JSON.stringify([...s]))},ee=e=>{v(e),J&&D(!0)};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Discover Jobs"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Find your next opportunity from top companies"})]}),(0,t.jsxs)("div",{className:"hidden md:flex items-center gap-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,t.jsx)(r.A,{className:"w-4 h-4 text-blue-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-lg font-bold text-gray-900",children:X.total}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Total Jobs"})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,t.jsx)(n.A,{className:"w-4 h-4 text-green-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-lg font-bold text-gray-900",children:X.internships}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Internships"})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,t.jsx)(i.A,{className:"w-4 h-4 text-purple-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-lg font-bold text-gray-900",children:X.remote}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Remote"})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"p-2 bg-amber-100 rounded-lg",children:(0,t.jsx)(c.A,{className:"w-4 h-4 text-amber-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-lg font-bold text-gray-900",children:C.size}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Saved"})]})]})]})]})}),(0,t.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 h-[calc(100vh-240px)]",children:J?(0,t.jsx)("div",{className:"h-full",children:z?(0,t.jsxs)("div",{className:"h-full flex flex-col",children:[(0,t.jsxs)("div",{className:"p-4 border-b border-gray-200 bg-white",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsxs)("button",{onClick:()=>{D(!1)},className:"flex items-center gap-2 text-blue-600 hover:text-blue-700 font-medium",children:[(0,t.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})}),"Back to Jobs"]}),(0,t.jsx)("button",{onClick:()=>Z(a.id),className:"px-4 py-2 rounded-lg border transition-colors ".concat(C.has(a.id)?"border-blue-500 bg-blue-50 text-blue-700":"border-gray-300 hover:bg-gray-50"),children:C.has(a.id)?"Saved":"Save"})]}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("h1",{className:"text-xl font-bold text-gray-900 mb-2",children:a.title||"Title not available"}),(0,t.jsx)("div",{className:"flex items-center gap-3 text-gray-600 mb-3",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(m.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{className:"font-semibold",children:a.company_name||"Company not available"})]})}),(0,t.jsx)("div",{className:"flex items-center gap-2",children:(0,t.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat("INTERNSHIP"===a.job_type?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800"),children:a.job_type||"Type not specified"})})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[P&&"none"!==P.freeze_status&&(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm text-amber-600 bg-amber-50 p-2 rounded-lg",children:[(0,t.jsx)(AlertCircle,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"Account restrictions may apply"})]}),(0,t.jsx)("button",{onClick:K,className:"w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium",disabled:q,children:q?"Applying...":"Apply Now"})]})]}),(0,t.jsx)("div",{className:"flex-1 overflow-y-auto p-4",children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,t.jsx)(h.A,{className:"w-3 h-3 text-green-600"}),(0,t.jsx)("h3",{className:"font-semibold text-gray-900 text-sm",children:"Salary"})]}),(0,t.jsx)("p",{className:"text-sm font-bold text-gray-900",children:a.salary_min&&a.salary_max?"$".concat(a.salary_min," - $").concat(a.salary_max):"Salary not specified"}),(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:["per ",a.per_unit||"N/A"]})]}),(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,t.jsx)(p.A,{className:"w-3 h-3 text-red-600"}),(0,t.jsx)("h3",{className:"font-semibold text-gray-900 text-sm",children:"Location"})]}),(0,t.jsx)("p",{className:"text-sm font-bold text-gray-900",children:a.location||"Not specified"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-base font-semibold text-gray-900 mb-3",children:"Job Description"}),(0,t.jsx)(y.G_,{description:a.description,className:"text-sm"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-base font-semibold text-gray-900 mb-3",children:"Requirements"}),(0,t.jsx)("div",{className:"text-sm text-gray-700",children:a.requirements?"string"==typeof a.requirements?(0,t.jsx)("ul",{className:"space-y-2",children:a.requirements.split(",").map((e,s)=>(0,t.jsxs)("li",{className:"flex items-start gap-2",children:[(0,t.jsx)(u.A,{className:"w-4 h-4 text-green-600 mt-0.5 flex-shrink-0"}),(0,t.jsx)("span",{children:e.trim()})]},s))}):(0,t.jsx)("p",{children:String(a.requirements)}):(0,t.jsx)("p",{children:"No requirements specified."})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-base font-semibold text-gray-900 mb-3",children:"Required Skills"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:a.requirements&&"string"==typeof a.requirements?a.requirements.split(",").map((e,s)=>(0,t.jsx)("span",{className:"px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium",children:e.trim()},s)):(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"No skills specified."})})]}),a.interview_rounds&&a.interview_rounds.length>0&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-base font-semibold text-gray-900 mb-3",children:"Interview Process"}),(0,t.jsx)("div",{className:"bg-gray-50 rounded-lg p-4",children:(0,t.jsx)("div",{className:"space-y-3",children:a.interview_rounds.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center justify-between bg-white rounded-lg p-3 border border-gray-200",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-sm font-semibold text-blue-600",children:s+1})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-gray-900",children:e.name}),e.date&&(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[new Date(e.date).toLocaleDateString(),e.time&&" at ".concat(e.time)]})]})]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:["Round ",s+1]})]},s))})})]}),a.additional_fields&&a.additional_fields.length>0&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-base font-semibold text-gray-900 mb-3",children:"Additional Application Fields"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"These fields will be required when applying for this position."}),(0,t.jsxs)("div",{className:"bg-white rounded-xl border border-gray-200 p-4",children:[(0,t.jsx)("div",{className:"space-y-4",children:a.additional_fields.map((e,s)=>(0,t.jsx)("div",{className:"bg-gray-50 rounded-lg p-4",children:(()=>{switch(e.type){case"text":return(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"block text-sm font-semibold text-gray-900",children:[e.label," ",e.required&&(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)("input",{type:"text",placeholder:"Enter ".concat(e.label.toLowerCase()),className:"w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm",disabled:!0})]});case"number":return(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"block text-sm font-semibold text-gray-900",children:[e.label," ",e.required&&(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)("input",{type:"number",placeholder:"e.g., 5",className:"w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm",disabled:!0})]});case"file":return(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"block text-sm font-semibold text-gray-900",children:[e.label," ",e.required&&(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsxs)("div",{className:"w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 border-dashed",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center text-gray-500",children:[(0,t.jsx)("svg",{className:"w-5 h-5 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})}),(0,t.jsxs)("span",{className:"text-sm font-medium",children:["Upload ",e.label.toLowerCase()]})]}),(0,t.jsx)("div",{className:"text-center mt-2",children:(0,t.jsx)("span",{className:"text-xs text-gray-400",children:"No file chosen"})})]})]});case"multiple_choice":var s;return(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"block text-sm font-semibold text-gray-900",children:[e.label," ",e.required&&(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsxs)("select",{className:"w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm",disabled:!0,children:[(0,t.jsxs)("option",{value:"",children:["Select ",e.label.toLowerCase()]}),null==(s=e.options)?void 0:s.map((e,s)=>(0,t.jsx)("option",{value:e,children:e},s))]})]});case"textarea":return(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"block text-sm font-semibold text-gray-900",children:[e.label," ",e.required&&(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)("textarea",{rows:4,placeholder:"Enter ".concat(e.label.toLowerCase()),className:"w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm resize-none",disabled:!0})]});default:return(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"block text-sm font-semibold text-gray-900",children:[e.label," ",e.required&&(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)("input",{type:"text",placeholder:"Unknown field type",className:"w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-100 opacity-50 cursor-not-allowed text-sm",disabled:!0})]})}})()},e.id||s))}),(0,t.jsx)("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200",children:(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5",children:(0,t.jsx)("svg",{className:"w-4 h-4 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-semibold text-blue-900 mb-1",children:"Preview Mode"}),(0,t.jsx)("p",{className:"text-sm text-blue-800 leading-relaxed",children:"These are preview fields. You'll be able to fill them out when you apply for this position."})]})]})})]})]})]})})]}):(0,t.jsxs)("div",{className:"h-full flex flex-col",children:[(0,t.jsxs)("div",{className:"p-4 border-b border-gray-200",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Jobs"}),(0,t.jsx)("span",{className:"text-sm text-gray-500",children:B.total_count>0?"".concat(V.length," of ").concat(B.total_count," jobs"):"".concat(V.length," positions")})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(d.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),(0,t.jsx)("input",{type:"text",placeholder:"Search jobs, companies, skills...",value:w,onChange:e=>_(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,t.jsxs)("select",{value:A,onChange:e=>k(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm",children:[(0,t.jsx)("option",{value:"ALL",children:"All Types"}),(0,t.jsx)("option",{value:"INTERNSHIP",children:"Internships"}),(0,t.jsx)("option",{value:"FULL_TIME",children:"Full-time"}),(0,t.jsx)("option",{value:"PART_TIME",children:"Part-time"})]}),(0,t.jsxs)("select",{value:S,onChange:e=>L(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm",children:[(0,t.jsx)("option",{value:"ALL",children:"All Locations"}),(0,t.jsx)("option",{value:"Remote",children:"Remote"})]})]})]})]}),(0,t.jsx)("div",{className:"flex-1 overflow-y-auto",children:O?(0,t.jsx)("div",{className:"flex items-center justify-center p-8",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):V.length>0?(0,t.jsx)("div",{className:"divide-y divide-gray-100",children:V.map(e=>{let s=C.has(e.id);return(0,t.jsx)("div",{onClick:()=>ee(e),className:"p-4 cursor-pointer transition-all duration-200 hover:bg-gray-50",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("span",{className:"px-2 py-1 rounded text-xs font-medium ".concat("INTERNSHIP"===e.job_type?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800"),children:e.job_type||"Type not specified"}),(0,t.jsx)("h3",{className:"font-semibold text-base leading-tight mt-2",children:e.title||"Title not available"})]}),(0,t.jsx)("button",{onClick:s=>{s.stopPropagation(),Z(e.id)},className:"p-2 rounded-full hover:bg-gray-200 transition-colors",children:s?(0,t.jsx)(o.A,{className:"w-5 h-5 text-blue-600"}):(0,t.jsx)(x.A,{className:"w-5 h-5 text-gray-400"})})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[(0,t.jsx)(m.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{className:"font-medium",children:e.company_name||"N/A"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(p.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:e.location||"N/A"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(h.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:e.salary_min&&e.salary_max?"$".concat(e.salary_min," - $").concat(e.salary_max):"Salary not specified"})]})]})]})},e.id)})}):(0,t.jsxs)("div",{className:"flex flex-col items-center justify-center h-full text-gray-500 p-8",children:[(0,t.jsx)(d.A,{className:"w-12 h-12 mb-4 text-gray-300"}),(0,t.jsx)("p",{className:"text-lg font-medium",children:"No jobs found"}),(0,t.jsx)("p",{className:"text-sm text-center",children:"Try adjusting your search or filters"})]})}),B.total_pages>1&&(0,t.jsx)("div",{className:"p-4 border-t border-gray-200 bg-white",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:["Showing ",(U-1)*B.per_page+1," to ",Math.min(U*B.per_page,B.total_count)," of ",B.total_count," jobs"]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("button",{onClick:()=>G(U-1),disabled:!B.has_previous||O,className:"px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,t.jsxs)("span",{className:"text-sm text-gray-600",children:[U," of ",B.total_pages]}),(0,t.jsx)("button",{onClick:()=>G(U+1),disabled:!B.has_next||O,className:"px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]})]})})]})}):(0,t.jsxs)("div",{className:"flex h-full",children:[(0,t.jsxs)("div",{className:"w-2/5 border-r border-gray-200 flex flex-col",children:[(0,t.jsxs)("div",{className:"p-3 border-b border-gray-200 space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Jobs"}),(0,t.jsx)("span",{className:"text-sm text-gray-500",children:B.total_count>0?"".concat(V.length," of ").concat(B.total_count," jobs"):"".concat(V.length," positions")})]}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(d.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),(0,t.jsx)("input",{type:"text",placeholder:"Search jobs, companies, skills...",value:w,onChange:e=>_(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)("select",{value:A,onChange:e=>k(e.target.value),className:"flex-1 px-3 py-1.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm",children:[(0,t.jsx)("option",{value:"ALL",children:"All Types"}),(0,t.jsx)("option",{value:"INTERNSHIP",children:"Internships"}),(0,t.jsx)("option",{value:"FULL_TIME",children:"Full-time"}),(0,t.jsx)("option",{value:"PART_TIME",children:"Part-time"})]}),(0,t.jsxs)("select",{value:S,onChange:e=>L(e.target.value),className:"flex-1 px-3 py-1.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm",children:[(0,t.jsx)("option",{value:"ALL",children:"All Locations"}),(0,t.jsx)("option",{value:"Remote",children:"Remote"})]})]})]}),(0,t.jsx)("div",{className:"flex-1 overflow-y-auto",children:O?(0,t.jsx)("div",{className:"flex items-center justify-center p-8",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):V.length>0?(0,t.jsx)("div",{className:"divide-y divide-gray-100",children:V.map(e=>{let s=e.id===(null==a?void 0:a.id),l=C.has(e.id);return(0,t.jsx)("div",{onClick:()=>ee(e),className:"p-3 cursor-pointer transition-all duration-200 hover:bg-gray-50 ".concat(s?"bg-blue-50 border-r-2 border-blue-500":""),children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("span",{className:"px-2 py-0.5 rounded text-xs font-medium ".concat("INTERNSHIP"===e.job_type?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800"),children:e.job_type||"Type not specified"}),(0,t.jsx)("h3",{className:"font-semibold text-sm leading-tight ".concat(s?"text-blue-900":"text-gray-900"),children:e.title||"Title not available"})]}),(0,t.jsx)("button",{onClick:s=>{s.stopPropagation(),Z(e.id)},className:"p-1 rounded-full hover:bg-gray-200 transition-colors",children:l?(0,t.jsx)(o.A,{className:"w-4 h-4 text-blue-600"}):(0,t.jsx)(x.A,{className:"w-4 h-4 text-gray-400"})})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[(0,t.jsx)(m.A,{className:"w-3 h-3"}),(0,t.jsx)("span",{className:"font-medium",children:e.company_name||"N/A"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(p.A,{className:"w-3 h-3"}),(0,t.jsx)("span",{children:e.location||"N/A"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(h.A,{className:"w-3 h-3"}),(0,t.jsx)("span",{children:e.salary_min&&e.salary_max?"$".concat(e.salary_min," - $").concat(e.salary_max):"Salary not specified"})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(g.A,{className:"w-3 h-3"}),(0,t.jsx)("span",{children:"—"})]})]})]})},e.id)})}):(0,t.jsxs)("div",{className:"flex flex-col items-center justify-center h-full text-gray-500",children:[(0,t.jsx)(d.A,{className:"w-12 h-12 mb-4 text-gray-300"}),(0,t.jsx)("p",{className:"text-lg font-medium",children:"No jobs found"}),(0,t.jsx)("p",{className:"text-sm",children:"Try adjusting your search or filters"})]})}),B.total_pages>1&&(0,t.jsx)("div",{className:"p-4 border-t border-gray-200 bg-white shadow-sm",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:["Showing ",(U-1)*B.per_page+1," to ",Math.min(U*B.per_page,B.total_count)," of ",B.total_count," jobs"]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("button",{onClick:()=>G(U-1),disabled:!B.has_previous||O,className:"px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,t.jsx)("div",{className:"flex items-center gap-1",children:Array.from({length:Math.min(B.total_pages,5)},(e,s)=>{let a;return a=B.total_pages<=5||U<=3?s+1:U>=B.total_pages-2?B.total_pages-4+s:U-2+s,(0,t.jsx)("button",{onClick:()=>G(a),disabled:O,className:"px-3 py-1 text-sm border rounded-md ".concat(a===U?"border-blue-500 bg-blue-50 text-blue-700":"border-gray-300 hover:bg-gray-100"," disabled:opacity-50 disabled:cursor-not-allowed"),children:a},a)})}),(0,t.jsx)("button",{onClick:()=>G(U+1),disabled:!B.has_next||O,className:"px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]})]})})]}),(0,t.jsx)("div",{className:"flex-1 flex flex-col",children:a?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"p-4 border-b border-gray-200",children:(0,t.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h1",{className:"text-xl font-bold text-gray-900 mb-1",children:a.title||"Title not available"}),(0,t.jsxs)("div",{className:"flex items-center gap-4 text-gray-600 mb-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(m.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{className:"font-semibold",children:a.company_name||"Company not available"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(p.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:a.location||"Location not available"})]})]}),(0,t.jsx)("div",{className:"flex items-center gap-2 mt-2",children:(0,t.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat("INTERNSHIP"===a.job_type?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800"),children:a.job_type||"Type not specified"})})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[P&&"none"!==P.freeze_status&&(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm text-amber-600 bg-amber-50 p-2 rounded-lg",children:[(0,t.jsx)(AlertCircle,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"Account restrictions may apply"})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)("button",{onClick:()=>Z(a.id),className:"px-4 py-2 rounded-lg border transition-colors ".concat(C.has(a.id)?"border-blue-500 bg-blue-50 text-blue-700":"border-gray-300 hover:bg-gray-50"),children:C.has(a.id)?"Saved":"Save"}),(0,t.jsx)("button",{onClick:K,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium",disabled:q,children:q?"Applying...":"Apply Now"})]})]})]})}),(0,t.jsx)("div",{className:"flex-1 overflow-y-auto p-4",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3",children:[(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,t.jsx)(h.A,{className:"w-3 h-3 text-green-600"}),(0,t.jsx)("h3",{className:"font-semibold text-gray-900 text-sm",children:"Salary"})]}),(0,t.jsx)("p",{className:"text-sm font-bold text-gray-900",children:a.salary_min&&a.salary_max?"$".concat(a.salary_min," - $").concat(a.salary_max):"Salary not specified"}),(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:["per ",a.per_unit||"N/A"]})]}),(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,t.jsx)(b.A,{className:"w-3 h-3 text-red-600"}),(0,t.jsx)("h3",{className:"font-semibold text-gray-900 text-sm",children:"Deadline"})]}),(0,t.jsx)("p",{className:"text-sm font-bold text-gray-900",children:a.application_deadline?new Date(a.application_deadline).toLocaleDateString():"No deadline specified"})]}),(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,t.jsx)(g.A,{className:"w-3 h-3 text-blue-600"}),(0,t.jsx)("h3",{className:"font-semibold text-gray-900 text-sm",children:"Duration"})]}),(0,t.jsx)("p",{className:"text-sm font-bold text-gray-900",children:a.duration||"Not specified"})]}),(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,t.jsx)(j.A,{className:"w-3 h-3 text-purple-600"}),(0,t.jsx)("h3",{className:"font-semibold text-gray-900 text-sm",children:"Company Size"})]}),(0,t.jsx)("p",{className:"text-sm font-bold text-gray-900",children:"N/A"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-base font-semibold text-gray-900 mb-2",children:"Job Description"}),(0,t.jsx)(y.G_,{description:a.description,className:"text-sm"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-base font-semibold text-gray-900 mb-2",children:"Requirements"}),(0,t.jsx)("div",{className:"text-sm text-gray-700",children:a.requirements?"string"==typeof a.requirements?(0,t.jsx)("ul",{className:"space-y-1",children:a.requirements.split(",").map((e,s)=>(0,t.jsxs)("li",{className:"flex items-start gap-2",children:[(0,t.jsx)(u.A,{className:"w-4 h-4 text-green-600 mt-0.5 flex-shrink-0"}),(0,t.jsx)("span",{children:e.trim()})]},s))}):(0,t.jsx)("p",{children:String(a.requirements)}):(0,t.jsx)("p",{children:"No requirements specified."})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-base font-semibold text-gray-900 mb-2",children:"Required Skills"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:a.requirements&&"string"==typeof a.requirements?a.requirements.split(",").map((e,s)=>(0,t.jsx)("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium",children:e.trim()},s)):(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"No skills specified."})})]}),a.interview_rounds&&a.interview_rounds.length>0&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-base font-semibold text-gray-900 mb-2",children:"Interview Process"}),(0,t.jsx)("div",{className:"bg-gray-50 rounded-lg p-4",children:(0,t.jsx)("div",{className:"space-y-3",children:a.interview_rounds.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center justify-between bg-white rounded-lg p-3 border border-gray-200",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-sm font-semibold text-blue-600",children:s+1})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-gray-900",children:e.name}),e.date&&(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[new Date(e.date).toLocaleDateString(),e.time&&" at ".concat(e.time)]})]})]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:["Round ",s+1]})]},s))})})]}),a.additional_fields&&a.additional_fields.length>0&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-base font-semibold text-gray-900 mb-2",children:"Additional Application Fields"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-6",children:"These fields will be required when applying for this position."}),(0,t.jsxs)("div",{className:"bg-white rounded-xl border border-gray-200 p-6",children:[(0,t.jsx)("div",{className:"space-y-6",children:a.additional_fields.map((e,s)=>(0,t.jsx)("div",{className:"bg-gray-50 rounded-lg p-4",children:(()=>{switch(e.type){case"text":return(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"block text-sm font-semibold text-gray-900",children:[e.label," ",e.required&&(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)("input",{type:"text",placeholder:"Enter ".concat(e.label.toLowerCase()),className:"w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"})]});case"number":return(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"block text-sm font-semibold text-gray-900",children:[e.label," ",e.required&&(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)("input",{type:"number",placeholder:"e.g., 5",className:"w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"})]});case"file":return(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"block text-sm font-semibold text-gray-900",children:[e.label," ",e.required&&(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsxs)("div",{className:"w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 border-dashed",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center text-gray-500",children:[(0,t.jsx)("svg",{className:"w-5 h-5 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})}),(0,t.jsxs)("span",{className:"text-sm font-medium",children:["Upload ",e.label.toLowerCase()]})]}),(0,t.jsx)("div",{className:"text-center mt-2",children:(0,t.jsx)("span",{className:"text-xs text-gray-400",children:"No file chosen"})})]})]});case"multiple_choice":var s;return(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"block text-sm font-semibold text-gray-900",children:[e.label," ",e.required&&(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsxs)("select",{className:"w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm",children:[(0,t.jsxs)("option",{value:"",children:["Select ",e.label.toLowerCase()]}),null==(s=e.options)?void 0:s.map((e,s)=>(0,t.jsx)("option",{value:e,children:e},s))]})]});case"textarea":return(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"block text-sm font-semibold text-gray-900",children:[e.label," ",e.required&&(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)("textarea",{rows:4,placeholder:"Enter ".concat(e.label.toLowerCase()),className:"w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm resize-none"})]});default:return(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"block text-sm font-semibold text-gray-900",children:[e.label," ",e.required&&(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)("input",{type:"text",placeholder:"Unknown field type",className:"w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-100 opacity-50 cursor-not-allowed text-sm"})]})}})()},e.id||s))}),(0,t.jsx)("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200",children:(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5",children:(0,t.jsx)("svg",{className:"w-4 h-4 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-semibold text-blue-900 mb-1",children:"Preview Mode"}),(0,t.jsx)("p",{className:"text-sm text-blue-800 leading-relaxed",children:"These are preview fields. You'll be able to fill them out when you apply for this position."})]})]})})]})]})]})})]}):(0,t.jsx)("div",{className:"flex-1 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)(r.A,{className:"w-8 h-8 text-gray-400"})}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Select a Job"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Choose a position from the list to view details"})]})})})]})})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[3464,2868,3983,2338,8441,1684,7358],()=>s(48060)),_N_E=e.O()}]);