(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/api/companies.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_data_jobsData_3af5997e.js",
  "static/chunks/src_api_companies_b50e4eb9.js",
  "static/chunks/src_api_companies_dff78d0b.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/api/companies.js [app-client] (ecmascript)");
    });
});
}}),
}]);