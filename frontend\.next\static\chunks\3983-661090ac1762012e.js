"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3983],{73983:(e,o,t)=>{t.d(o,{ph:()=>f,hN:()=>h});var l=t(95155),i=t(12115),r=t(40646),n=t(1243),a=t(81284),s=t(76517),c=t(75525),d=t(85339),u=t(54416);let g=e=>{let{isOpen:o,onClose:t,type:g="error",title:b,message:p,details:m,actions:y=[],dismissible:h=!0,autoClose:f=!1,autoCloseDelay:C=5e3,showIcon:x=!0}=e;if(i.useEffect(()=>{if(f&&o){let e=setTimeout(()=>{t()},C);return()=>clearTimeout(e)}},[f,o,C,t]),i.useEffect(()=>(o?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[o]),!o)return null;let{icon:w,iconColor:v,bgColor:k,borderColor:j,buttonColor:A}=(()=>{switch(g){case"success":return{icon:r.A,iconColor:"text-green-500",bgColor:"bg-green-50",borderColor:"border-green-200",buttonColor:"bg-green-600 hover:bg-green-700"};case"warning":return{icon:n.A,iconColor:"text-yellow-500",bgColor:"bg-yellow-50",borderColor:"border-yellow-200",buttonColor:"bg-yellow-600 hover:bg-yellow-700"};case"info":return{icon:a.A,iconColor:"text-blue-500",bgColor:"bg-blue-50",borderColor:"border-blue-200",buttonColor:"bg-blue-600 hover:bg-blue-700"};case"network":return{icon:s.A,iconColor:"text-red-500",bgColor:"bg-red-50",borderColor:"border-red-200",buttonColor:"bg-red-600 hover:bg-red-700"};case"auth":return{icon:c.A,iconColor:"text-red-500",bgColor:"bg-red-50",borderColor:"border-red-200",buttonColor:"bg-red-600 hover:bg-red-700"};case"validation":return{icon:d.A,iconColor:"text-orange-500",bgColor:"bg-orange-50",borderColor:"border-orange-200",buttonColor:"bg-orange-600 hover:bg-orange-700"};default:return{icon:d.A,iconColor:"text-red-500",bgColor:"bg-red-50",borderColor:"border-red-200",buttonColor:"bg-red-600 hover:bg-red-700"}}})();return(0,l.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-md w-full mx-4 border-2 ".concat(j),children:[(0,l.jsxs)("div",{className:"".concat(k," px-6 py-4 rounded-t-lg border-b ").concat(j," flex items-center justify-between"),children:[(0,l.jsxs)("div",{className:"flex items-center",children:[x&&(0,l.jsx)(w,{className:"w-6 h-6 ".concat(v," mr-3")}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:b})]}),h&&(0,l.jsx)("button",{onClick:t,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,l.jsx)(u.A,{className:"w-5 h-5"})})]}),(0,l.jsxs)("div",{className:"px-6 py-4",children:[(0,l.jsx)("p",{className:"text-gray-700 mb-4",children:p}),m&&(0,l.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 mb-4",children:[(0,l.jsx)("h4",{className:"text-sm font-medium text-gray-800 mb-2",children:"Details:"}),"string"==typeof m?(0,l.jsx)("p",{className:"text-sm text-gray-600",children:m}):Array.isArray(m)?(0,l.jsx)("ul",{className:"text-sm text-gray-600 space-y-1",children:m.map((e,o)=>(0,l.jsxs)("li",{className:"flex items-start",children:[(0,l.jsx)("span",{className:"w-2 h-2 bg-gray-400 rounded-full mt-2 mr-2 flex-shrink-0"}),e]},o))}):(0,l.jsx)("div",{className:"text-sm text-gray-600",children:Object.entries(m).map(e=>{let[o,t]=e;return(0,l.jsxs)("div",{className:"mb-1",children:[(0,l.jsxs)("span",{className:"font-medium",children:[o,":"]})," ",Array.isArray(t)?t.join(", "):t]},o)})})]})]}),(0,l.jsx)("div",{className:"px-6 py-4 bg-gray-50 rounded-b-lg flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-3 sm:justify-end",children:y.length>0?y.map((e,o)=>(0,l.jsxs)("button",{onClick:e.onClick,className:"px-4 py-2 rounded-lg font-medium transition-colors ".concat("secondary"===e.variant?"bg-gray-200 text-gray-800 hover:bg-gray-300":"danger"===e.variant?"bg-red-600 text-white hover:bg-red-700":A+" text-white"),disabled:e.disabled,children:[e.loading&&(0,l.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 inline-block"}),e.icon&&(0,l.jsx)(e.icon,{className:"w-4 h-4 mr-2 inline-block"}),e.label]},o)):(0,l.jsx)("button",{onClick:t,className:"px-4 py-2 rounded-lg font-medium text-white transition-colors ".concat(A),children:"OK"})})]})})};var b=t(71007),p=t(29869),m=t(33786);let y=(0,i.createContext)(),h=()=>{let e=(0,i.useContext)(y);if(!e)throw Error("useNotification must be used within a NotificationProvider");return e},f=e=>{let{children:o}=e,[t,r]=(0,i.useState)(null),n=(0,i.useCallback)(e=>{r({id:Date.now(),...e})},[]),a=(0,i.useCallback)(()=>{r(null)},[]),d=(0,i.useCallback)(function(e,o){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,l=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[];n({type:"error",title:e,message:o,details:t,actions:l})},[n]),u=(0,i.useCallback)(function(e,o){let t=!(arguments.length>2)||void 0===arguments[2]||arguments[2];n({type:"success",title:e,message:o,autoClose:t,autoCloseDelay:3e3})},[n]),h=(0,i.useCallback)(function(e,o){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,l=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[];n({type:"warning",title:e,message:o,details:t,actions:l})},[n]),f=(0,i.useCallback)(function(e,o){let t=arguments.length>2&&void 0!==arguments[2]&&arguments[2];n({type:"info",title:e,message:o,autoClose:t})},[n]),C=(0,i.useCallback)(()=>{n({type:"warning",title:"Resume Required",message:"You need to upload a resume before applying to this job.",details:["Please upload your resume to your profile first","You can upload it from the Profile section","Accepted formats: PDF, DOC, DOCX (max 5MB)"],showIcon:!0,actions:[{text:"Go to Profile",style:"primary",icon:b.A,action:()=>{window.location.href="/profile"}},{text:"Upload Resume",style:"secondary",icon:p.A,action:()=>{let e=document.createElement("input");e.type="file",e.accept=".pdf,.doc,.docx",e.onchange=e=>{let o=e.target.files[0];o&&console.log("Resume file selected:",o)},e.click()}}],dismissible:!0})},[n]),x=(0,i.useCallback)(e=>{var o;let t=(null==e||null==(o=e.response)?void 0:o.data)||{};if(t.resume)return void C();let l={},i=!1;if(Object.keys(t).forEach(e=>{Array.isArray(t[e])&&t[e].length>0&&(l[e]=t[e],i=!0)}),i)return void n({type:"error",title:"Application Submission Failed",message:"Please fix the following validation errors:",details:Object.entries(l).map(e=>{let[o,t]=e;return"".concat(o,": ").concat(Array.isArray(t)?t.join(", "):t)}),showIcon:!0,dismissible:!0});n({type:"error",title:"Application Failed",message:t.detail||t.message||(null==e?void 0:e.message)||"Failed to submit your job application.",details:["Please check your application details","Ensure all required fields are filled","Try again in a few moments"],showIcon:!0,actions:[{text:"Try Again",style:"primary",action:()=>{window.location.reload()}}],dismissible:!0})},[n,C]),w=(0,i.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];n({type:"warning",title:"Profile Incomplete",message:"Please complete your profile before applying to jobs.",details:e.length>0?["Missing: ".concat(e.join(", "))]:["Some required profile information is missing","Please update your profile with all necessary details","This ensures better job matching and application processing"],showIcon:!0,actions:[{text:"Complete Profile",style:"primary",icon:b.A,action:()=>{window.location.href="/profile"}}],dismissible:!0})},[n]),v=(0,i.useCallback)(()=>{n({type:"warning",title:"Session Expired",message:"Your login session has expired. Please login again to continue.",details:["For security reasons, sessions expire after a period of inactivity","Please login again to access your account","Your data is safe and will be available after login"],showIcon:!0,actions:[{text:"Login",style:"primary",icon:c.A,action:()=>{window.location.href="/login"}}],dismissible:!1,autoClose:!1})},[n]),k=(0,i.useCallback)(()=>{n({type:"info",title:"System Maintenance",message:"The system is currently under maintenance. Please try again later.",details:["We are working to improve your experience","Maintenance should be completed shortly","Thank you for your patience"],showIcon:!0,actions:[{text:"Check Status",style:"secondary",icon:m.A,action:()=>{window.open("/status","_blank")}}],dismissible:!0,autoClose:!1})},[n]),j=(0,i.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Authentication failed. Please log in again.";n({type:"auth",title:"Authentication Required",message:e,actions:[{label:"Go to Login",onClick:()=>{a(),window.location.href="/login"},icon:b.A}]})},[n,a]),A=(0,i.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;n({type:"network",title:"Connection Error",message:"Unable to connect to the server. Please check your internet connection and try again.",details:e?["Make sure you're connected to the internet","Try refreshing the page","Check if the server is running",e.message||"Unknown network error"]:["Make sure you're connected to the internet","Try refreshing the page","Check if the server is running"],actions:[{label:"Retry",onClick:()=>{a(),window.location.reload()},icon:s.A},{label:"Cancel",onClick:a,variant:"secondary"}]})},[n,a]),P=(0,i.useCallback)(()=>{n({type:"validation",title:"Resume Required",message:"You need to upload a resume before applying to this job. Please update your profile first.",details:["Go to your profile page",'Upload your resume in the "Resume" section',"Come back and apply for the job"],actions:[{label:"Go to Profile",onClick:()=>{a(),window.location.href="/profile"},icon:b.A},{label:"Cancel",onClick:a,variant:"secondary"}]})},[n,a]),N=(0,i.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;n({type:"error",title:"File Upload Failed",message:"There was a problem uploading your file. Please try again with a different file.",details:e||["Check that your file is not too large (max 10MB)","Supported formats: PDF, DOC, DOCX","Make sure the file is not corrupted","Try uploading a different file"],actions:[{label:"Try Again",onClick:a,icon:p.A}]})},[n,a]),E=(0,i.useCallback)((e,o)=>{let t,l;"object"==typeof o&&null!==o?(t={},Object.entries(o).forEach(e=>{let[o,l]=e;Array.isArray(l)?t[o]=l.join(", "):t[o]=l}),l="Please fix the following errors and try again:"):Array.isArray(o)?(t=o,l="Please fix the following issues:"):(l=o||"Please check your input and try again.",t=null),n({type:"validation",title:e||"Validation Error",message:l,details:t})},[n]),D=(0,i.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"You don't have permission to perform this action.";n({type:"auth",title:"Permission Denied",message:e,details:["Contact your administrator if you believe this is an error","Make sure you're logged in with the correct account","Check if your session has expired"],actions:[{label:"Go to Login",onClick:()=>{a(),window.location.href="/login"},icon:c.A},{label:"Contact Support",onClick:()=>{a(),window.location.href="/admin/helpandsupport"},icon:m.A,variant:"secondary"}]})},[n,a]),T=(0,i.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];n({type:"warning",title:"Profile Incomplete",message:"Your profile needs to be completed before you can apply for jobs.",details:e.length>0?["Please fill in the following fields: ".concat(e.join(", "))]:["Make sure your personal information is complete","Upload your resume","Verify your contact information","Add your academic details"],actions:[{label:"Complete Profile",onClick:()=>{a(),window.location.href="/profile"},icon:b.A},{label:"Later",onClick:a,variant:"secondary"}]})},[n,a]),I=(0,i.useCallback)(function(e){var o,t,l,i,r,n,a;let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"operation";(null==e||null==(o=e.response)?void 0:o.status)===401?j():(null==e||null==(t=e.response)?void 0:t.status)===403?D():(null==e||null==(l=e.response)?void 0:l.status)>=500?d("Server Error","A server error occurred while performing the ".concat(s,". Please try again later."),"If the problem persists, please contact support."):(null==e?void 0:e.response)?d("Error",(null==e||null==(r=e.response)||null==(i=r.data)?void 0:i.detail)||(null==e||null==(a=e.response)||null==(n=a.data)?void 0:n.message)||(null==e?void 0:e.message)||"Failed to complete ".concat(s,". Please try again.")):A(e)},[d,j,D,A]);return(0,l.jsxs)(y.Provider,{value:{showNotification:n,hideNotification:a,showError:d,showSuccess:u,showWarning:h,showInfo:f,showAuthError:j,showNetworkError:A,showResumeRequiredError:P,showFileUploadError:N,showValidationError:E,showPermissionError:D,showProfileIncompleteError:T,showApplicationSubmissionError:x,showMissingResumeModal:C,showProfileIncompleteModal:w,showSessionExpiredModal:v,showMaintenanceModal:k,handleApiError:I},children:[o,t&&(0,l.jsx)(g,{isOpen:!0,onClose:a,type:t.type,title:t.title,message:t.message,details:t.details,actions:t.actions,dismissible:!1!==t.dismissible,autoClose:t.autoClose,autoCloseDelay:t.autoCloseDelay,showIcon:!1!==t.showIcon})]})}}}]);