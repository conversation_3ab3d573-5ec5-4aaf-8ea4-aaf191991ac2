(()=>{var e={};e.id=2609,e.ids=[2609],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13861:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},17313:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23928:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},25541:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28947:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35071:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},40228:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},40526:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>o});var a=s(65239),r=s(48088),l=s(88170),i=s.n(l),n=s(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);s.d(t,c);let o={children:["",{children:["myjobs",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,99096)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\myjobs\\page.jsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,75535)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\layout.js"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\myjobs\\page.jsx"],p={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/myjobs/page",pathname:"/myjobs",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},41300:(e,t,s)=>{Promise.resolve().then(s.bind(s,98257))},48730:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58138:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var a=s(51060);s(51421);let r=a.A.create({baseURL:"http://127.0.0.1:8000",headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>{let t=localStorage.getItem("access_token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,async e=>{let t=e.config;if(e.response?.status===401&&!t._retry){t._retry=!0;try{let e=localStorage.getItem("refresh_token");if(e){let s=await a.A.post("http://127.0.0.1:8000/api/auth/token/refresh/",{refresh:e});return localStorage.setItem("access_token",s.data.access),t.headers.Authorization=`Bearer ${s.data.access}`,r(t)}}catch(e){console.error("Error refreshing token:",e)}}return Promise.reject(e)});let l=r},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65222:(e,t,s)=>{"use strict";s.d(t,{G_:()=>r});var a=s(60687);function r({description:e,className:t=""}){let s=e?e.replace(/\n/g,"<br />").replace(/•/g,"•").replace(/\*\s/g,"• ").replace(/-\s/g,"• ").trim():"No description provided.";return(0,a.jsx)("div",{className:`text-gray-700 leading-relaxed ${t}`,dangerouslySetInnerHTML:{__html:s}})}},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},75518:(e,t,s)=>{"use strict";s.d(t,{G$:()=>n,N6:()=>r,Om:()=>d,T4:()=>p,YQ:()=>i,_S:()=>c,lh:()=>o,vr:()=>l});var a=s(58138);function r(e={}){let t=new URLSearchParams;e.page&&t.append("page",e.page),e.per_page&&t.append("per_page",e.per_page),e.job_type&&"ALL"!==e.job_type&&t.append("job_type",e.job_type),e.location&&"ALL"!==e.location&&t.append("location",e.location),e.salary_min&&t.append("salary_min",e.salary_min),e.search&&t.append("search",e.search);let s=t.toString(),l=`/api/v1/college/default-college/jobs/${s?`?${s}`:""}`;return a.A.get(l)}function l(e,t,s={}){if(!Object.values(s).some(e=>e instanceof File))return a.A.post(`/api/v1/college/default-college/jobs/${e}/apply/`,{cover_letter:t,additional_field_responses:s});{let r=new FormData;return r.append("cover_letter",t),Object.entries(s).forEach(([e,t])=>{t instanceof File?r.append(e,t):r.append(e,JSON.stringify(t))}),a.A.post(`/api/v1/college/default-college/jobs/${e}/apply/`,r,{headers:{"Content-Type":"multipart/form-data"}})}}function i(e){return a.A.get(`/api/v1/college/default-college/jobs/${e}/`)}function n(){return a.A.get("/api/v1/college/default-college/jobs/applied/")}function c(e){return a.A.post("/api/v1/college/default-college/jobs/create/",e)}function o(e,t){return a.A.put(`/api/v1/college/default-college/jobs/${e}/`,t)}function d(e={}){let t=new URLSearchParams;e.page&&t.append("page",e.page),e.per_page&&t.append("per_page",e.per_page),e.search&&t.append("search",e.search),e.type&&"All"!==e.type&&t.append("job_type",e.type),e.minCTC&&t.append("salary_min",e.minCTC),e.maxCTC&&t.append("salary_max",e.maxCTC),e.minStipend&&t.append("stipend_min",e.minStipend),e.maxStipend&&t.append("stipend_max",e.maxStipend),e.location&&t.append("location",e.location),void 0!==e.is_published&&t.append("is_published",e.is_published),e.company_id&&t.append("company_id",e.company_id),e.company_name&&t.append("company_name",e.company_name);let s=t.toString(),r=`/api/v1/college/default-college/jobs/admin/${s?`?${s}`:""}`;return console.log("\uD83C\uDF10 listJobsAdmin calling URL:",r,"with params:",e),a.A.get(r).then(e=>(console.log("\uD83C\uDF10 listJobsAdmin response:",{status:e.status,totalJobs:e.data?.pagination?.total_count||0,currentPage:e.data?.pagination?.current_page||1,totalPages:e.data?.pagination?.total_pages||1}),e)).catch(e=>{throw console.error("\uD83C\uDF10 listJobsAdmin error:",e),console.error("\uD83C\uDF10 listJobsAdmin error response:",e.response?.data),e})}function p(e){return a.A.patch(`/api/v1/jobs/${e}/toggle-publish/`)}},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},88564:(e,t,s)=>{Promise.resolve().then(s.bind(s,99096))},94735:e=>{"use strict";e.exports=require("events")},97992:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},98257:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var a=s(60687),r=s(43210),l=s(48730),i=s(13861),n=s(5336),c=s(35071),o=s(25541),d=s(28947),p=s(99270),m=s(17313),x=s(97992),u=s(40228),h=s(23928),g=s(75518),j=s(65222);let y=()=>{let[e,t]=(0,r.useState)([]),[s,y]=(0,r.useState)(null),[b,f]=(0,r.useState)("ALL"),[v,N]=(0,r.useState)(""),[A,_]=(0,r.useState)("recent");(0,r.useEffect)(()=>{(async()=>{try{let e=await (0,g.G$)(),s=[];e.data&&e.data.data&&Array.isArray(e.data.data)?s=e.data.data:Array.isArray(e.data)&&(s=e.data);let a=await Promise.all(s.map(async e=>{try{let t=await (0,g.YQ)(e.job);return{...e,jobDetails:t.data}}catch(t){return console.error(`Failed to fetch job details for job ${e.job}:`,t),e}}));console.log("Applications with job details:",a),t(a),y(a[0]||null)}catch(e){console.error("Failed to load applied jobs:",e),t([])}})()},[]);let w=e=>{let t={APPLIED:{color:"bg-blue-50 text-blue-700 border-blue-200",icon:(0,a.jsx)(l.A,{className:"w-4 h-4"}),bgIcon:"bg-blue-100",textIcon:"text-blue-600"},"UNDER REVIEW":{color:"bg-amber-50 text-amber-700 border-amber-200",icon:(0,a.jsx)(i.A,{className:"w-4 h-4"}),bgIcon:"bg-amber-100",textIcon:"text-amber-600"},"INTERVIEW SCHEDULED":{color:"bg-green-50 text-green-700 border-green-200",icon:(0,a.jsx)(n.A,{className:"w-4 h-4"}),bgIcon:"bg-green-100",textIcon:"text-green-600"},REJECTED:{color:"bg-red-50 text-red-700 border-red-200",icon:(0,a.jsx)(c.A,{className:"w-4 h-4"}),bgIcon:"bg-red-100",textIcon:"text-red-600"},ACCEPTED:{color:"bg-emerald-50 text-emerald-700 border-emerald-200",icon:(0,a.jsx)(o.A,{className:"w-4 h-4"}),bgIcon:"bg-emerald-100",textIcon:"text-emerald-600"}};return t[e]||t.APPLIED},D=Array.isArray(e)?e:[],E=D.filter(e=>{let t="ALL"===b||e.status===b,s=e.jobDetails?.title||e.job_title||e.title||"",a=e.jobDetails?.employer_name||e.employer_name||e.company_name||"",r=s.toLowerCase().includes(v.toLowerCase())||a.toLowerCase().includes(v.toLowerCase());return t&&r}).sort((e,t)=>{if("recent"===A)return new Date(t.applied_at)-new Date(e.applied_at);if("company"===A){let s=e.jobDetails?.employer_name||e.employer_name||e.company_name||"",a=t.jobDetails?.employer_name||t.employer_name||t.company_name||"";return s.localeCompare(a)}return"status"===A?(e.status||"").localeCompare(t.status||""):0}),C={total:D.length,pending:D.filter(e=>"APPLIED"===e.status||"UNDER REVIEW"===e.status).length,interviews:D.filter(e=>"INTERVIEW SCHEDULED"===e.status).length,rejected:D.filter(e=>"REJECTED"===e.status).length};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Job Applications"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Track and manage your job applications"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,a.jsx)(d.A,{className:"w-4 h-4 text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-lg font-bold text-gray-900",children:C.total}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Total"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"p-2 bg-amber-100 rounded-lg",children:(0,a.jsx)(l.A,{className:"w-4 h-4 text-amber-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-lg font-bold text-gray-900",children:C.pending}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Pending"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,a.jsx)(n.A,{className:"w-4 h-4 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-lg font-bold text-gray-900",children:C.interviews}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Interviews"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,a.jsx)(o.A,{className:"w-4 h-4 text-purple-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-lg font-bold text-gray-900",children:[C.total>0?Math.round((C.total-C.rejected)/C.total*100):0,"%"]}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Response Rate"})]})]})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 h-[calc(100vh-240px)]",children:(0,a.jsxs)("div",{className:"flex h-full",children:[(0,a.jsxs)("div",{className:"w-2/5 border-r border-gray-200 flex flex-col",children:[(0,a.jsxs)("div",{className:"p-3 border-b border-gray-200 space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Applications"}),(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[E.length," of ",D.length]})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),(0,a.jsx)("input",{type:"text",placeholder:"Search applications...",value:v,onChange:e=>N(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("select",{value:b,onChange:e=>f(e.target.value),className:"flex-1 px-3 py-1.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm",children:[(0,a.jsx)("option",{value:"ALL",children:"All Status"}),(0,a.jsx)("option",{value:"APPLIED",children:"Applied"}),(0,a.jsx)("option",{value:"UNDER REVIEW",children:"Under Review"}),(0,a.jsx)("option",{value:"INTERVIEW SCHEDULED",children:"Interview"}),(0,a.jsx)("option",{value:"REJECTED",children:"Rejected"}),(0,a.jsx)("option",{value:"ACCEPTED",children:"Accepted"})]}),(0,a.jsxs)("select",{value:A,onChange:e=>_(e.target.value),className:"flex-1 px-3 py-1.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm",children:[(0,a.jsx)("option",{value:"recent",children:"Recent First"}),(0,a.jsx)("option",{value:"company",children:"Company A-Z"}),(0,a.jsx)("option",{value:"status",children:"Status"})]})]})]}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto",children:E.length>0?(0,a.jsx)("div",{className:"divide-y divide-gray-100",children:E.map(e=>{let t=w(e.status),r=e.id===s?.id;return(0,a.jsx)("div",{onClick:()=>y(e),className:`p-3 cursor-pointer transition-all duration-200 hover:bg-gray-50 ${r?"bg-blue-50 border-r-2 border-blue-500":""}`,children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsx)("h3",{className:`font-semibold text-sm leading-tight ${r?"text-blue-900":"text-gray-900"}`,children:e.jobDetails?.title||e.job_title||e.title||"Title not available"}),(0,a.jsx)("div",{className:`p-1 rounded-full ${t.bgIcon}`,children:(0,a.jsx)("div",{className:t.textIcon,children:t.icon})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[(0,a.jsx)(m.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{className:"font-medium",children:e.jobDetails?.employer_name||e.employer_name||e.company_name||"N/A"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4 text-xs text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(x.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:e.jobDetails?.location||e.location||e.job_location||"N/A"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(u.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:e.applied_at?new Date(e.applied_at).toLocaleDateString():"—"})]})]}),(0,a.jsxs)("div",{className:`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${t.color}`,children:[t.icon,(0,a.jsx)("span",{children:e.status})]})]})},e.id)})}):(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center h-full p-6 text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4",children:(0,a.jsx)(p.A,{className:"w-8 h-8 text-gray-400"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No applications found"}),(0,a.jsx)("p",{className:"text-gray-500 text-sm",children:"Try adjusting your search or filters"})]})})]}),(0,a.jsx)("div",{className:"flex-1 flex flex-col",children:s?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:s.jobDetails?.title||s.job_title||s.title||"Title not available"}),(0,a.jsxs)("div",{className:"flex items-center gap-4 text-gray-600",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{className:"font-medium",children:s.jobDetails?.employer_name||s.employer_name||s.company_name||"Company not available"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(x.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:s.jobDetails?.location||s.location||s.job_location||"Location not available"})]})]})]}),(0,a.jsxs)("div",{className:`px-4 py-2 rounded-full text-sm font-medium border flex items-center gap-2 ${w(s.status).color}`,children:[w(s.status).icon,(0,a.jsx)("span",{children:s.status})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-1",children:[(0,a.jsx)(h.A,{className:"w-4 h-4 text-gray-500"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Salary"})]}),(0,a.jsx)("span",{className:"text-sm text-gray-600",children:s.jobDetails?.salary_min&&s.jobDetails?.salary_max?`$${s.jobDetails.salary_min} - $${s.jobDetails.salary_max}`:"Not disclosed"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-1",children:[(0,a.jsx)(u.A,{className:"w-4 h-4 text-gray-500"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Applied"})]}),(0,a.jsx)("span",{className:"text-sm text-gray-600",children:s.applied_at?new Date(s.applied_at).toLocaleDateString():"—"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-1",children:[(0,a.jsx)(l.A,{className:"w-4 h-4 text-gray-500"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Deadline"})]}),(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Not specified"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-1",children:[(0,a.jsx)(d.A,{className:"w-4 h-4 text-gray-500"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Type"})]}),(0,a.jsx)("span",{className:"text-sm text-gray-600",children:s.jobDetails?.job_type||s.job_type||"Not specified"})]})]})]}),(0,a.jsx)("div",{className:"flex-1 p-6 overflow-y-auto",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Job Description"}),(0,a.jsx)("div",{className:"prose prose-sm max-w-none",children:(0,a.jsx)(j.G_,{description:s.jobDetails?.description,className:""})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Requirements"}),(0,a.jsx)("div",{className:"space-y-2 text-gray-700",children:s.jobDetails?.requirements?(0,a.jsx)("div",{dangerouslySetInnerHTML:{__html:s.jobDetails.requirements}}):(0,a.jsx)("span",{children:"No requirements specified."})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Application Timeline"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(n.A,{className:"w-5 h-5 text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:"Application Submitted"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:s.applied_at?new Date(s.applied_at).toLocaleDateString():"—"})]})]}),"APPLIED"!==s.status&&(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(i.A,{className:"w-5 h-5 text-amber-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:"Under Review"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Application is being reviewed by the hiring team"})]})]}),"INTERVIEW SCHEDULED"===s.status&&(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(u.A,{className:"w-5 h-5 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:"Interview Scheduled"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"You have been invited for an interview"})]})]})]})]})]})})]}):(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center h-full p-6 text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4",children:(0,a.jsx)(i.A,{className:"w-8 h-8 text-gray-400"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Select an application"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Choose an application from the list to view details"})]})})]})})]})}},99096:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\myjobs\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\myjobs\\page.jsx","default")},99270:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4447,681,1658,1060,2305],()=>s(40526));module.exports=a})();