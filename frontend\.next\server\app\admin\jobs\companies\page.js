(()=>{var e={};e.id=3123,e.ids=[3123,4335],e.modules={2032:(e,t,a)=>{Promise.resolve().then(a.bind(a,85912))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10722:(e,t,a)=>{Promise.resolve().then(a.bind(a,11527))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11527:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>f});var s=a(60687),r=a(43210),n=a(24664),o=a(98848),i=a(9535),l=a(37325),c=a(81080),p=a(95994),d=a(80556),m=a(90910),u=a(81172),h=a(20798),g=a(58869),x=a(53411);function f({children:e}){let[t,a]=(0,r.useState)(""),f=[{items:[{title:"Dashboard",href:"/admin/dashboard",icon:(0,s.jsx)(o.A,{})},{title:"Jobs",href:"/admin/jobs",icon:(0,s.jsx)(i.A,{})},{title:"Applications",href:"/admin/applications",icon:(0,s.jsx)(l.A,{})},{title:"Posts",href:"/admin/posts",icon:(0,s.jsx)(h.A,{className:"w-5 h-5"})},{title:"Student Management",href:"/admin/student-management",icon:(0,s.jsx)(g.A,{className:"w-5 h-5"})},{title:"Student Analytics",href:"/admin/analytics",icon:(0,s.jsx)(x.A,{className:"w-5 h-5"})},{title:"Company Management",href:"/admin/companymanagement",icon:(0,s.jsx)(c.A,{})},{title:"Forms",href:"/admin/form",icon:(0,s.jsx)(p.A,{})}]}],v=[{title:"My Profile",href:"/admin/profile",icon:(0,s.jsx)(d.A,{})},{title:"Settings",href:"../settings",icon:(0,s.jsx)(m.A,{})},{title:"Contact Support",href:"/admin/helpandsupport",icon:(0,s.jsx)(u.A,{})}];return(0,s.jsx)("div",{className:"h-screen bg-gray-50 overflow-hidden",children:(0,s.jsxs)("div",{className:"flex h-full",children:[(0,s.jsx)(n.A,{sections:f,bottomItems:v,defaultExpanded:!1,navbarHeight:"0",className:"z-20"}),(0,s.jsx)("div",{className:"flex-1 p-6 ml-20 overflow-y-auto",children:(0,s.jsx)("div",{className:"bg-white rounded-xl shadow-md p-6 min-h-full text-black",children:e})})]})})}},12412:e=>{"use strict";e.exports=require("assert")},14335:(e,t,a)=>{"use strict";a.d(t,{C1:()=>o,Gu:()=>g,JT:()=>l,RC:()=>c,S0:()=>f,Y_:()=>d,bl:()=>m,dl:()=>x,eK:()=>i,fetchCompanies:()=>n,getCompanyStats:()=>p,jQ:()=>u,mm:()=>r,oY:()=>h});var s=a(58138);function r(e={}){let t=new URLSearchParams;e.page&&t.append("page",e.page),e.per_page&&t.append("per_page",e.per_page),e.tier&&"ALL"!==e.tier&&t.append("tier",e.tier),e.industry&&"ALL"!==e.industry&&t.append("industry",e.industry),e.campus_recruiting&&t.append("campus_recruiting",e.campus_recruiting),e.search&&t.append("search",e.search),e.sort&&t.append("sort",e.sort),t.append("_t",new Date().getTime());let a=t.toString(),n=[`/api/v1/companies/${a?`?${a}`:""}`,`/api/v1/college/default-college/companies/${a?`?${a}`:""}`];return s.A.get(n[0]).catch(e=>(console.log(`Primary endpoint failed: ${e.message}, trying fallback...`),s.A.get(n[1])))}async function n(e={}){try{console.log("Fetching companies from API...");let t=await r(e),a=[];if(t.data&&Array.isArray(t.data)?a=t.data:t.data&&t.data.results&&Array.isArray(t.data.results)?a=t.data.results:t.data&&t.data.data&&Array.isArray(t.data.data)&&(a=t.data.data),console.log(`Retrieved ${a.length} companies from API`),a.length>0)return await Promise.all(a.map(async e=>{try{let t=await o(e.id);return d(t.data)}catch(t){return console.log(`Could not fetch details for company ${e.id}:`,t),d(e)}}));throw Error("No companies returned from API")}catch(t){console.error("Error fetching companies from API:",t),console.log("Falling back to static company data");let{companies:e}=await a.e(1286).then(a.bind(a,61286));return e}}function o(e){let t=[`/api/v1/company/${e}/`,`/api/v1/companies/${e}/`,`/api/v1/college/default-college/companies/${e}/`];return s.A.get(t[0]).catch(e=>(console.log(`First company endpoint failed: ${e.message}, trying second...`),s.A.get(t[1]).catch(e=>(console.log(`Second company endpoint failed: ${e.message}, trying third...`),s.A.get(t[2])))))}function i(e){let t=new FormData;return Object.keys(e).forEach(a=>{"logo"===a&&e[a]instanceof File?t.append(a,e[a]):null!==e[a]&&void 0!==e[a]&&t.append(a,e[a])}),s.A.post("/api/v1/companies/",t,{headers:{"Content-Type":"multipart/form-data"}})}function l(e,t){let a=new FormData;return Object.keys(t).forEach(e=>{"logo"===e&&t[e]instanceof File?a.append(e,t[e]):null!==t[e]&&void 0!==t[e]&&a.append(e,t[e])}),s.A.put(`/api/v1/companies/${e}/`,a,{headers:{"Content-Type":"multipart/form-data"}})}function c(e){return s.A.delete(`/api/v1/companies/${e}/`)}function p(){return s.A.get("/api/v1/companies/stats/")}function d(e){return{id:e.id,name:e.name,logo:e.logo||`https://via.placeholder.com/48x48/4285F4/FFFFFF?text=${e.name.charAt(0)}`,description:e.description||"",industry:e.industry||"",size:e.size||"",founded:e.founded||"",location:e.location||"Location not specified",website:e.website||"",tier:e.tier||"Tier 3",campus_recruiting:e.campus_recruiting||!1,totalActiveJobs:e.total_active_jobs||0,totalApplicants:e.total_applicants||0,totalHired:e.total_hired||0,awaitedApproval:e.pending_approval||0}}function m(e){return s.A.get(`/api/v1/companies/${e}/followers/count/`)}function u(e,t){return s.A.post(`/api/v1/companies/${e}/followers/`,{user_id:t})}function h(e,t){return s.A.delete(`/api/v1/companies/${e}/followers/`,{data:{user_id:t}})}function g(e,t){return s.A.get(`/api/v1/companies/${e}/followers/status/?user_id=${t}`)}function x(e){return s.A.get(`/api/v1/users/${e}/following/`)}function f(){return s.A.get("/api/v1/companies/simple/").then(e=>{if(e.data&&e.data.success)return e.data;throw Error("Failed to fetch companies")}).catch(e=>(console.error("Error fetching companies:",e),{success:!1,data:[],count:0,error:e.message}))}},14401:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var s=(0,a(6445).A)("outline","search","IconSearch",[["path",{d:"M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0",key:"svg-0"}],["path",{d:"M21 21l-6 -6",key:"svg-1"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20798:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("megaphone",[["path",{d:"m3 11 18-5v12L3 14v-3z",key:"n962bs"}],["path",{d:"M11.6 16.8a3 3 0 1 1-5.8-1.6",key:"1yl0tm"}]])},21820:e=>{"use strict";e.exports=require("os")},23697:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\layout.jsx","default")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37325:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var s=(0,a(6445).A)("outline","clipboard-list","IconClipboardList",[["path",{d:"M9 5h-2a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-12a2 2 0 0 0 -2 -2h-2",key:"svg-0"}],["path",{d:"M9 3m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z",key:"svg-1"}],["path",{d:"M9 12l.01 0",key:"svg-2"}],["path",{d:"M13 12l2 0",key:"svg-3"}],["path",{d:"M9 16l.01 0",key:"svg-4"}],["path",{d:"M13 16l2 0",key:"svg-5"}]])},38230:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>o.a,__next_app__:()=>d,pages:()=>p,routeModule:()=>m,tree:()=>c});var s=a(65239),r=a(48088),n=a(88170),o=a.n(n),i=a(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);a.d(t,l);let c={children:["",{children:["admin",{children:["jobs",{children:["companies",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,78317)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\jobs\\companies\\page.jsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,23697)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\layout.jsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,75535)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\layout.js"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\jobs\\companies\\page.jsx"],d={require:a,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/jobs/companies/page",pathname:"/admin/jobs/companies",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},48173:(e,t,a)=>{Promise.resolve().then(a.bind(a,23697))},53411:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58138:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var s=a(51060);a(51421);let r=s.A.create({baseURL:"http://127.0.0.1:8000",headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>{let t=localStorage.getItem("access_token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,async e=>{let t=e.config;if(e.response?.status===401&&!t._retry){t._retry=!0;try{let e=localStorage.getItem("refresh_token");if(e){let a=await s.A.post("http://127.0.0.1:8000/api/auth/token/refresh/",{refresh:e});return localStorage.setItem("access_token",a.data.access),t.headers.Authorization=`Bearer ${a.data.access}`,r(t)}}catch(e){console.error("Error refreshing token:",e)}}return Promise.reject(e)});let n=r},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});var s=a(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},75518:(e,t,a)=>{"use strict";a.d(t,{G$:()=>i,N6:()=>r,Om:()=>p,T4:()=>d,YQ:()=>o,_S:()=>l,lh:()=>c,vr:()=>n});var s=a(58138);function r(e={}){let t=new URLSearchParams;e.page&&t.append("page",e.page),e.per_page&&t.append("per_page",e.per_page),e.job_type&&"ALL"!==e.job_type&&t.append("job_type",e.job_type),e.location&&"ALL"!==e.location&&t.append("location",e.location),e.salary_min&&t.append("salary_min",e.salary_min),e.search&&t.append("search",e.search);let a=t.toString(),n=`/api/v1/college/default-college/jobs/${a?`?${a}`:""}`;return s.A.get(n)}function n(e,t,a={}){if(!Object.values(a).some(e=>e instanceof File))return s.A.post(`/api/v1/college/default-college/jobs/${e}/apply/`,{cover_letter:t,additional_field_responses:a});{let r=new FormData;return r.append("cover_letter",t),Object.entries(a).forEach(([e,t])=>{t instanceof File?r.append(e,t):r.append(e,JSON.stringify(t))}),s.A.post(`/api/v1/college/default-college/jobs/${e}/apply/`,r,{headers:{"Content-Type":"multipart/form-data"}})}}function o(e){return s.A.get(`/api/v1/college/default-college/jobs/${e}/`)}function i(){return s.A.get("/api/v1/college/default-college/jobs/applied/")}function l(e){return s.A.post("/api/v1/college/default-college/jobs/create/",e)}function c(e,t){return s.A.put(`/api/v1/college/default-college/jobs/${e}/`,t)}function p(e={}){let t=new URLSearchParams;e.page&&t.append("page",e.page),e.per_page&&t.append("per_page",e.per_page),e.search&&t.append("search",e.search),e.type&&"All"!==e.type&&t.append("job_type",e.type),e.minCTC&&t.append("salary_min",e.minCTC),e.maxCTC&&t.append("salary_max",e.maxCTC),e.minStipend&&t.append("stipend_min",e.minStipend),e.maxStipend&&t.append("stipend_max",e.maxStipend),e.location&&t.append("location",e.location),void 0!==e.is_published&&t.append("is_published",e.is_published),e.company_id&&t.append("company_id",e.company_id),e.company_name&&t.append("company_name",e.company_name);let a=t.toString(),r=`/api/v1/college/default-college/jobs/admin/${a?`?${a}`:""}`;return console.log("\uD83C\uDF10 listJobsAdmin calling URL:",r,"with params:",e),s.A.get(r).then(e=>(console.log("\uD83C\uDF10 listJobsAdmin response:",{status:e.status,totalJobs:e.data?.pagination?.total_count||0,currentPage:e.data?.pagination?.current_page||1,totalPages:e.data?.pagination?.total_pages||1}),e)).catch(e=>{throw console.error("\uD83C\uDF10 listJobsAdmin error:",e),console.error("\uD83C\uDF10 listJobsAdmin error response:",e.response?.data),e})}function d(e){return s.A.patch(`/api/v1/jobs/${e}/toggle-publish/`)}},78317:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\jobs\\\\companies\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\jobs\\companies\\page.jsx","default")},79551:e=>{"use strict";e.exports=require("url")},81172:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var s=(0,a(6445).A)("outline","help","IconHelp",[["path",{d:"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0",key:"svg-0"}],["path",{d:"M12 17l0 .01",key:"svg-1"}],["path",{d:"M12 13.5a1.5 1.5 0 0 1 1 -1.5a2.6 2.6 0 1 0 -3 -4",key:"svg-2"}]])},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85912:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>p});var s=a(60687),r=a(43210),n=a(16189),o=a(14401);function i({company:e,onClick:t}){let[a,n]=(0,r.useState)(!1);return(0,s.jsxs)("div",{className:"bg-gray-100 p-4 rounded-xl shadow w-full hover:shadow-lg transition-shadow",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)("div",{className:"w-14 h-14 rounded bg-gray-300 flex items-center justify-center text-sm font-semibold text-gray-600 overflow-hidden",children:a?(0,s.jsx)("span",{children:"Logo"}):(0,s.jsx)("img",{src:e.Logo,alt:"Logo",className:"w-full h-full object-cover",onError:()=>n(!0)})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{onClick:()=>t(e),className:"text-lg font-semibold text-blue-500 cursor-pointer hover:underline",children:e.companyName}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:e.companyDescription})]})]}),(0,s.jsxs)("div",{className:"mt-3 text-sm text-gray-700",children:[(0,s.jsxs)("p",{children:[e.totalActiveJobs," Active Listing, ",e.awaitedApproval," Awaiting Approval"]}),(0,s.jsx)("br",{}),(0,s.jsx)("p",{className:"text-gray-500",children:e.location})]})]})}function l({label:e,value:t}){return(0,s.jsxs)("div",{className:"bg-gray-100 rounded-lg p-4 text-center shadow",children:[(0,s.jsx)("p",{className:"text-xl font-bold",children:t}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:e})]})}function c({company:e,onClose:t}){let[a,n]=(0,r.useState)(!1);return(0,s.jsx)("div",{className:"fixed inset-0 z-50 bg-white/80 backdrop-blur-none flex items-center justify-center",children:(0,s.jsxs)("div",{className:"bg-white w-full max-w-4xl rounded-xl p-6 relative shadow-2xl border",children:[(0,s.jsx)("button",{onClick:t,className:"absolute top-3 right-4 text-gray-600 text-xl hover:text-black",children:"\xd7"}),(0,s.jsxs)("div",{className:"flex items-center gap-4 border-b pb-4",children:[(0,s.jsx)("div",{className:"w-20 h-20 bg-gray-200 rounded-full flex items-center justify-center overflow-hidden",children:a?(0,s.jsx)("span",{className:"text-sm text-gray-500",children:"Logo"}):(0,s.jsx)("img",{src:e.Logo,alt:"Logo",className:"w-full h-full object-cover",onError:()=>n(!0)})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-xl font-bold",children:e.companyName}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:e.companyDescription}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:e.location}),(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:[e.employeeCount," Employees"]}),(0,s.jsx)("a",{href:e.website,className:"text-blue-500 underline text-sm",target:"_blank",rel:"noopener noreferrer",children:e.website})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-5 gap-4 my-6",children:[(0,s.jsx)(l,{label:"Total Active Jobs",value:e.totalActiveJobs}),(0,s.jsx)(l,{label:"Total Applicants",value:e.totalApplicants}),(0,s.jsx)(l,{label:"Total Hired",value:e.totalHired}),(0,s.jsx)(l,{label:"Waiting for Approval",value:e.awaitedApproval}),(0,s.jsx)(l,{label:"Other Stat",value:"..."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-lg mb-2",children:"Active Listing"}),(0,s.jsxs)("table",{className:"w-full table-auto text-sm",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"text-left bg-gray-100",children:[(0,s.jsx)("th",{className:"p-2",children:"Title"}),(0,s.jsx)("th",{className:"p-2",children:"Type"}),(0,s.jsx)("th",{className:"p-2",children:"CTC"}),(0,s.jsx)("th",{className:"p-2",children:"Stipend"}),(0,s.jsx)("th",{className:"p-2",children:"Deadline"})]})}),(0,s.jsx)("tbody",{children:(e.activeListingsData||[]).map((e,t)=>(0,s.jsxs)("tr",{className:`${t%2==0?"bg-white":"bg-gray-50"} text-left`,children:[(0,s.jsx)("td",{className:"p-2",children:e.title}),(0,s.jsx)("td",{className:"p-2",children:e.type}),(0,s.jsx)("td",{className:"px-4 py-4 text-gray-700",children:e.ctc?`${e.ctc} LPA`:"-"}),(0,s.jsx)("td",{className:"px-4 py-4 text-gray-700",children:e.stipend?`${e.stipend} INR `:"-"}),(0,s.jsx)("td",{className:"p-2",children:e.deadline})]},t))})]})]})]})})}function p(){let e=(0,n.useRouter)(),[t,a]=(0,r.useState)([]),[l,p]=(0,r.useState)(null),[d,m]=(0,r.useState)(""),[u,h]=(0,r.useState)(!0),[g,x]=(0,r.useState)(null),f=t.filter(e=>(e.companyName||e.company_name||"").toLowerCase().includes(d.toLowerCase())).sort((e,t)=>(e.companyName||e.company_name||"").localeCompare(t.companyName||t.company_name||""));return u?(0,s.jsx)("div",{className:"h-full flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto"}),(0,s.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading companies..."})]})}):g?(0,s.jsx)("div",{className:"h-full flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center text-red-600",children:[(0,s.jsx)("p",{className:"text-xl mb-4",children:g}),(0,s.jsx)("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:"Try Again"})]})}):(0,s.jsx)("div",{className:"p-6",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Companies"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Manage company partnerships and job opportunities"})]}),(0,s.jsxs)("div",{className:"flex gap-3",children:[(0,s.jsx)("button",{onClick:()=>e.push("/admin/jobs/listings"),className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700",children:"View Job Listings"}),(0,s.jsx)("button",{onClick:()=>e.push("/admin/jobs/create"),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Post New Job"})]})]}),(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsxs)("div",{className:"relative max-w-md",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(o.A,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{type:"text",placeholder:"Search companies...",value:d,onChange:e=>m(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:0===f.length?(0,s.jsxs)("div",{className:"col-span-full text-center py-12",children:[(0,s.jsx)("p",{className:"text-gray-500 text-lg",children:"No companies found"}),(0,s.jsx)("p",{className:"text-gray-400",children:"Try adjusting your search criteria"})]}):f.map(e=>(0,s.jsx)(i,{company:e,onClick:()=>p(e)},e.id))}),l&&(0,s.jsx)(c,{company:l,isOpen:!!l,onClose:()=>p(null)})]})})}a(14335),a(75518)},92304:(e,t,a)=>{Promise.resolve().then(a.bind(a,78317))},94735:e=>{"use strict";e.exports=require("events")},95994:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var s=(0,a(6445).A)("outline","forms","IconForms",[["path",{d:"M12 3a3 3 0 0 0 -3 3v12a3 3 0 0 0 3 3",key:"svg-0"}],["path",{d:"M6 3a3 3 0 0 1 3 3v12a3 3 0 0 1 -3 3",key:"svg-1"}],["path",{d:"M13 7h7a1 1 0 0 1 1 1v8a1 1 0 0 1 -1 1h-7",key:"svg-2"}],["path",{d:"M5 7h-1a1 1 0 0 0 -1 1v8a1 1 0 0 0 1 1h1",key:"svg-3"}],["path",{d:"M17 12h.01",key:"svg-4"}],["path",{d:"M13 12h.01",key:"svg-5"}]])}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[4447,681,1658,1060,2305],()=>a(38230));module.exports=s})();