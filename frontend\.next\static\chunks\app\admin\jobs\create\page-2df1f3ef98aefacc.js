(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6020],{755:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var r=(0,s(86467).A)("outline","plus","IconPlus",[["path",{d:"M12 5l0 14",key:"svg-0"}],["path",{d:"M5 12l14 0",key:"svg-1"}]])},1243:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},16701:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var r=(0,s(86467).A)("outline","trash","IconTrash",[["path",{d:"M4 7l16 0",key:"svg-0"}],["path",{d:"M10 11l0 6",key:"svg-1"}],["path",{d:"M14 11l0 6",key:"svg-2"}],["path",{d:"M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2 -2l1 -12",key:"svg-3"}],["path",{d:"M9 7v-3a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v3",key:"svg-4"}]])},19946:(e,t,s)=>{"use strict";s.d(t,{A:()=>h});var r=s(12115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),l=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return t.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim()},o=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,r.forwardRef)((e,t)=>{let{color:s="currentColor",size:a=24,strokeWidth:i=2,absoluteStrokeWidth:l,className:d="",children:h,iconNode:u,...m}=e;return(0,r.createElement)("svg",{ref:t,...c,width:a,height:a,stroke:s,strokeWidth:l?24*Number(i)/Number(a):i,className:n("lucide",d),...!h&&!o(m)&&{"aria-hidden":"true"},...m},[...u.map(e=>{let[t,s]=e;return(0,r.createElement)(t,s)}),...Array.isArray(h)?h:[h]])}),h=(e,t)=>{let s=(0,r.forwardRef)((s,i)=>{let{className:o,...c}=s;return(0,r.createElement)(d,{ref:i,iconNode:t,className:n("lucide-".concat(a(l(e))),"lucide-".concat(e),o),...c})});return s.displayName=l(e),s}},22439:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var r=(0,s(86467).A)("outline","search","IconSearch",[["path",{d:"M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0",key:"svg-0"}],["path",{d:"M21 21l-6 -6",key:"svg-1"}]])},29869:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},30699:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var r=(0,s(86467).A)("outline","check","IconCheck",[["path",{d:"M5 12l5 5l10 -10",key:"svg-0"}]])},33786:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},35695:(e,t,s)=>{"use strict";var r=s(18999);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"usePathname")&&s.d(t,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},37719:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(23464);s(73983);let a=r.A.create({baseURL:"http://127.0.0.1:8000",headers:{"Content-Type":"application/json"}});a.interceptors.request.use(e=>{let t=localStorage.getItem("access_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),a.interceptors.response.use(e=>e,async e=>{var t;let s=e.config;if((null==(t=e.response)?void 0:t.status)===401&&!s._retry){s._retry=!0;try{let e=localStorage.getItem("refresh_token");if(e){let t=await r.A.post("http://127.0.0.1:8000/api/auth/token/refresh/",{refresh:e});return localStorage.setItem("access_token",t.data.access),s.headers.Authorization="Bearer ".concat(t.data.access),a(s)}}catch(e){console.error("Error refreshing token:",e),localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),window.location.href="/login"}}return Promise.reject(e)});let i=a},40646:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},48406:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h});var r=s(95155),a=s(12115),i=s(35695),l=s(30699),n=(0,s(86467).A)("outline","arrow-left","IconArrowLeft",[["path",{d:"M5 12l14 0",key:"svg-0"}],["path",{d:"M5 12l6 6",key:"svg-1"}],["path",{d:"M5 12l6 -6",key:"svg-2"}]]),o=s(7045),c=s(34842),d=s(48937);function h(){let e=(0,i.useRouter)(),[t,s]=(0,a.useState)(!1),[h,u]=(0,a.useState)(!1),[m,p]=(0,a.useState)([]),[x,y]=(0,a.useState)(!0),[g,b]=(0,a.useState)(null);(0,a.useEffect)(()=>{(async()=>{y(!0);try{console.log("Fetching companies for job creation...");let e=await (0,d.S0)();if(e.success&&Array.isArray(e.data))p(e.data),console.log("Loaded ".concat(e.data.length," companies for job creation")),b(null);else throw Error(e.error||"Failed to fetch companies")}catch(e){console.error("Failed to fetch companies:",e),b("Failed to load companies. Please check your connection and try again."),p([])}finally{y(!1)}})()},[]);let v=async t=>{s(!0);try{let s={title:t.title,description:t.description,location:t.location,job_type:t.job_type||"FULL_TIME",salary_min:parseFloat(t.salary_min)||0,salary_max:parseFloat(t.salary_max)||0,required_skills:t.required_skills||"",application_deadline:t.application_deadline||t.deadline,is_active:void 0===t.is_active||t.is_active,company_name:t.company_name,interview_rounds:t.interview_rounds||[],additional_fields:t.additional_fields||[]};console.log("Sending job data to backend:",s),await (0,c._S)(s),u(!0),setTimeout(()=>{e.push("/admin/jobs/listings")},2e3)}catch(e){var r,a;throw console.error("Error creating job:",e),console.error("Error response:",null==(r=e.response)?void 0:r.data),console.error("Error status:",null==(a=e.response)?void 0:a.status),e}finally{s(!1)}};return h?(0,r.jsx)("div",{className:"h-full flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4",children:(0,r.jsx)(l.A,{className:"h-8 w-8 text-green-600"})}),(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Job Posted Successfully!"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"The job posting has been created and is ready for publication."}),(0,r.jsxs)("div",{className:"space-x-3",children:[(0,r.jsx)("button",{onClick:()=>e.push("/admin/jobs/listings"),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"View All Jobs"}),(0,r.jsx)("button",{onClick:()=>e.push("/admin/jobs/create"),className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700",children:"Create Another Job"})]})]})}):x?(0,r.jsx)("div",{className:"h-full flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading companies..."})]})}):(0,r.jsx)("div",{className:"p-6",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("button",{onClick:()=>e.push("/admin/jobs"),className:"flex items-center text-gray-600 hover:text-gray-900",children:[(0,r.jsx)(n,{size:20,className:"mr-2"}),"Back to Jobs"]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Create New Job Posting"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Fill out the details below to create a new job opportunity"})]})]}),(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsx)("button",{onClick:()=>e.push("/admin/jobs/companies"),className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700",children:"View Companies"}),(0,r.jsx)("button",{onClick:()=>e.push("/admin/jobs/listings"),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"View All Jobs"})]})]}),g&&(0,r.jsxs)("div",{className:"mb-6 bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,r.jsx)("p",{className:"text-red-600",children:g}),(0,r.jsx)("button",{onClick:()=>window.location.reload(),className:"mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 text-sm",children:"Try Again"})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Job Details"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Complete all required fields to create the job posting. The job will be saved as a draft and can be published later."})]}),(0,r.jsx)(o.A,{companies:m,onSubmit:v,onCancel:()=>{window.confirm("Are you sure you want to cancel? Any unsaved changes will be lost.")&&e.push("/admin/jobs/listings")},isSubmitting:t,submitButtonText:"Create Job Posting",mode:"create"})]})}),(0,r.jsxs)("div",{className:"mt-8 bg-blue-50 rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-blue-900 mb-3",children:"\uD83D\uDCA1 Tips for Creating Great Job Postings"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"Job Title"}),(0,r.jsxs)("ul",{className:"space-y-1 list-disc list-inside",children:[(0,r.jsx)("li",{children:"Be specific and clear"}),(0,r.jsx)("li",{children:"Avoid internal jargon"}),(0,r.jsx)("li",{children:"Include seniority level if relevant"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"Description"}),(0,r.jsxs)("ul",{className:"space-y-1 list-disc list-inside",children:[(0,r.jsx)("li",{children:"Start with company overview"}),(0,r.jsx)("li",{children:"Detail key responsibilities"}),(0,r.jsx)("li",{children:"Highlight growth opportunities"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"Requirements"}),(0,r.jsxs)("ul",{className:"space-y-1 list-disc list-inside",children:[(0,r.jsx)("li",{children:"List must-have skills first"}),(0,r.jsx)("li",{children:"Separate nice-to-have skills"}),(0,r.jsx)("li",{children:"Include soft skills requirements"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"Salary Range"}),(0,r.jsxs)("ul",{className:"space-y-1 list-disc list-inside",children:[(0,r.jsx)("li",{children:"Be transparent with compensation"}),(0,r.jsx)("li",{children:"Include benefits if applicable"}),(0,r.jsx)("li",{children:"Consider market rates"})]})]})]})]})]})})}},54395:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var r=(0,s(86467).A)("outline","x","IconX",[["path",{d:"M18 6l-12 12",key:"svg-0"}],["path",{d:"M6 6l12 12",key:"svg-1"}]])},54416:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},71007:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},75525:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},76517:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]])},81284:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},85339:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},86467:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(12115),a={outline:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"},filled:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",stroke:"none"}};let i=(e,t,s,i)=>{let l=(0,r.forwardRef)((s,l)=>{let{color:n="currentColor",size:o=24,stroke:c=2,title:d,className:h,children:u,...m}=s;return(0,r.createElement)("svg",{ref:l,...a[e],width:o,height:o,className:["tabler-icon","tabler-icon-".concat(t),h].join(" "),..."filled"===e?{fill:n}:{strokeWidth:c,stroke:n},...m},[d&&(0,r.createElement)("title",{key:"svg-title"},d),...i.map(e=>{let[t,s]=e;return(0,r.createElement)(t,s)}),...Array.isArray(u)?u:[u]])});return l.displayName="".concat(s),l}},93059:(e,t,s)=>{Promise.resolve().then(s.bind(s,48406))}},e=>{var t=t=>e(e.s=t);e.O(0,[3464,3983,4605,8441,1684,7358],()=>t(93059)),_N_E=e.O()}]);