(()=>{var e={};e.id=8808,e.ids=[8808],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10722:(e,t,r)=>{Promise.resolve().then(r.bind(r,11527))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11527:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var s=r(60687),a=r(43210),n=r(24664),i=r(98848),o=r(9535),l=r(37325),d=r(81080),c=r(95994),p=r(80556),u=r(90910),m=r(81172),h=r(20798),x=r(58869),f=r(53411);function g({children:e}){let[t,r]=(0,a.useState)(""),g=[{items:[{title:"Dashboard",href:"/admin/dashboard",icon:(0,s.jsx)(i.A,{})},{title:"Jobs",href:"/admin/jobs",icon:(0,s.jsx)(o.A,{})},{title:"Applications",href:"/admin/applications",icon:(0,s.jsx)(l.A,{})},{title:"Posts",href:"/admin/posts",icon:(0,s.jsx)(h.A,{className:"w-5 h-5"})},{title:"Student Management",href:"/admin/student-management",icon:(0,s.jsx)(x.A,{className:"w-5 h-5"})},{title:"Student Analytics",href:"/admin/analytics",icon:(0,s.jsx)(f.A,{className:"w-5 h-5"})},{title:"Company Management",href:"/admin/companymanagement",icon:(0,s.jsx)(d.A,{})},{title:"Forms",href:"/admin/form",icon:(0,s.jsx)(c.A,{})}]}],b=[{title:"My Profile",href:"/admin/profile",icon:(0,s.jsx)(p.A,{})},{title:"Settings",href:"../settings",icon:(0,s.jsx)(u.A,{})},{title:"Contact Support",href:"/admin/helpandsupport",icon:(0,s.jsx)(m.A,{})}];return(0,s.jsx)("div",{className:"h-screen bg-gray-50 overflow-hidden",children:(0,s.jsxs)("div",{className:"flex h-full",children:[(0,s.jsx)(n.A,{sections:g,bottomItems:b,defaultExpanded:!1,navbarHeight:"0",className:"z-20"}),(0,s.jsx)("div",{className:"flex-1 p-6 ml-20 overflow-y-auto",children:(0,s.jsx)("div",{className:"bg-white rounded-xl shadow-md p-6 min-h-full text-black",children:e})})]})})}},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20798:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("megaphone",[["path",{d:"m3 11 18-5v12L3 14v-3z",key:"n962bs"}],["path",{d:"M11.6 16.8a3 3 0 1 1-5.8-1.6",key:"1yl0tm"}]])},21820:e=>{"use strict";e.exports=require("os")},23697:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\layout.jsx","default")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34536:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\profile\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\profile\\page.jsx","default")},37325:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var s=(0,r(6445).A)("outline","clipboard-list","IconClipboardList",[["path",{d:"M9 5h-2a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-12a2 2 0 0 0 -2 -2h-2",key:"svg-0"}],["path",{d:"M9 3m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z",key:"svg-1"}],["path",{d:"M9 12l.01 0",key:"svg-2"}],["path",{d:"M13 12l2 0",key:"svg-3"}],["path",{d:"M9 16l.01 0",key:"svg-4"}],["path",{d:"M13 16l2 0",key:"svg-5"}]])},48173:(e,t,r)=>{Promise.resolve().then(r.bind(r,23697))},51632:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=r(65239),a=r(48088),n=r(88170),i=r.n(n),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["admin",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,34536)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\profile\\page.jsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,23697)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\layout.jsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,75535)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\layout.js"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\profile\\page.jsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/profile/page",pathname:"/admin/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},53411:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58138:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(51060);r(51421);let a=s.A.create({baseURL:"http://127.0.0.1:8000",headers:{"Content-Type":"application/json"}});a.interceptors.request.use(e=>{let t=localStorage.getItem("access_token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e)),a.interceptors.response.use(e=>e,async e=>{let t=e.config;if(e.response?.status===401&&!t._retry){t._retry=!0;try{let e=localStorage.getItem("refresh_token");if(e){let r=await s.A.post("http://127.0.0.1:8000/api/auth/token/refresh/",{refresh:e});return localStorage.setItem("access_token",r.data.access),t.headers.Authorization=`Bearer ${r.data.access}`,a(t)}}catch(e){console.error("Error refreshing token:",e)}}return Promise.reject(e)});let n=a},60064:(e,t,r)=>{Promise.resolve().then(r.bind(r,83142))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73216:(e,t,r)=>{Promise.resolve().then(r.bind(r,34536))},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81172:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var s=(0,r(6445).A)("outline","help","IconHelp",[["path",{d:"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0",key:"svg-0"}],["path",{d:"M12 17l0 .01",key:"svg-1"}],["path",{d:"M12 13.5a1.5 1.5 0 0 1 1 -1.5a2.6 2.6 0 1 0 -3 -4",key:"svg-2"}]])},81630:e=>{"use strict";e.exports=require("http")},83142:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(60687),a=r(43210);function n({name:e}){let[t,r]=(0,a.useState)("/images/default-avatar.png"),[n,i]=(0,a.useState)(!1),[o,l]=(0,a.useState)(!1),d=(0,a.useRef)(null);return(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsxs)("div",{className:"flex flex-col items-center bg-white rounded-2xl p-6 shadow-sm content-card profile-container",children:[o?(0,s.jsx)("div",{className:"w-32 h-48 flex items-center justify-center bg-blue-100 text-blue-600 font-bold text-5xl rounded-lg border-2 border-gray-100",children:e&&e.length>0?e.charAt(0).toUpperCase():"?"}):(0,s.jsx)("img",{src:t,alt:"Profile",className:"w-32 h-48 object-cover border-2 border-gray-100 rounded-lg",onError:()=>{l(!0)}}),(0,s.jsxs)("div",{className:"flex gap-2 mt-2",children:[(0,s.jsx)("button",{onClick:()=>{d.current.click()},className:"text-sm px-3 py-1 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors",children:"Edit"}),n&&(0,s.jsx)("button",{onClick:()=>{i(!1)},className:"text-sm px-3 py-1 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Save"})]}),(0,s.jsx)("input",{type:"file",ref:d,onChange:e=>{let t=e.target.files[0];if(t&&t.type.startsWith("image/")){let e=new FileReader;e.onload=e=>{r(e.target.result),i(!0),l(!1)},e.readAsDataURL(t)}},accept:"image/*",className:"hidden"})]})})}r(33322);let i=function(){let[e,t]=(0,a.useState)({email:"",phone:"",fullName:"",role:""}),[r,i]=(0,a.useState)(!0),[o,l]=(0,a.useState)("");return(0,s.jsxs)("div",{className:"flex-1 bg-white rounded-2xl p-6 shadow-sm content-card profile-container",children:[(0,s.jsx)("div",{className:"flex justify-between items-center mb-6",children:(0,s.jsx)("h3",{className:"text-2xl font-medium text-black",children:"Profile Information"})}),r?(0,s.jsx)("div",{className:"text-gray-500",children:"Loading..."}):o?(0,s.jsx)("div",{className:"text-red-500",children:o}):(0,s.jsxs)("div",{className:"flex flex-col md:flex-row gap-6",children:[(0,s.jsx)(n,{name:e.fullName}),(0,s.jsx)("div",{className:"space-y-4 flex-1",children:(0,s.jsxs)("div",{className:"border-b pb-4",children:[(0,s.jsx)("h4",{className:"text-lg font-medium text-black mb-4",children:"Basic Information"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name"}),(0,s.jsx)("div",{className:"p-2 bg-gray-50 rounded-lg text-black",children:e.fullName})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Job Role"}),(0,s.jsx)("div",{className:"p-2 bg-gray-50 rounded-lg text-black capitalize",children:e.role?.toLowerCase()||"N/A"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),(0,s.jsx)("div",{className:"p-2 bg-gray-50 rounded-lg text-black",children:e.email})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Phone"}),(0,s.jsx)("div",{className:"p-2 bg-gray-50 rounded-lg text-black",children:e.phone||"N/A"})]})]})]})})]})]})}},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},95994:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var s=(0,r(6445).A)("outline","forms","IconForms",[["path",{d:"M12 3a3 3 0 0 0 -3 3v12a3 3 0 0 0 3 3",key:"svg-0"}],["path",{d:"M6 3a3 3 0 0 1 3 3v12a3 3 0 0 1 -3 3",key:"svg-1"}],["path",{d:"M13 7h7a1 1 0 0 1 1 1v8a1 1 0 0 1 -1 1h-7",key:"svg-2"}],["path",{d:"M5 7h-1a1 1 0 0 0 -1 1v8a1 1 0 0 0 1 1h1",key:"svg-3"}],["path",{d:"M17 12h.01",key:"svg-4"}],["path",{d:"M13 12h.01",key:"svg-5"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,681,1658,1060,2305,3322],()=>r(51632));module.exports=s})();