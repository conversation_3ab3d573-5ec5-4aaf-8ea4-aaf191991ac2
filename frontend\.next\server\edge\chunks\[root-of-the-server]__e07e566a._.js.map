{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.js"], "sourcesContent": ["import { NextResponse } from 'next/server';\r\n\r\nexport function middleware(request) {\r\n  const url = request.nextUrl.clone();\r\n  const role = request.cookies.get('role')?.value;\r\n\r\n  // Only log in development and reduce verbosity\r\n  if (process.env.NODE_ENV === 'development') {\r\n    console.log(`[Middleware] ${url.pathname} - Role: ${role || 'None'}`);\r\n  }\r\n\r\n  // For testing purposes, you can uncomment the following:\r\n  // const role = 'ADMIN'; // or 'STUDENT'\r\n\r\n  // Redirect profile URLs based on role\r\n  if (url.pathname === '/profile' && role === 'ADMIN') {\r\n    url.pathname = '/admin/profile';\r\n    return NextResponse.redirect(url);\r\n  }\r\n\r\n  // Handle settings redirects based on role\r\n  if (url.pathname === '/settings' && role === 'ADMIN') {\r\n    url.pathname = '/admin/settings';\r\n    return NextResponse.redirect(url);\r\n  }\r\n\r\n  // Block /profile unless logged in as student or admin\r\n  if (url.pathname.startsWith('/profile') && role !== 'STUDENT' && role !== 'ADMIN') {\r\n    url.pathname = '/login';\r\n    return NextResponse.redirect(url);\r\n  }\r\n\r\n  // Admin-only access\r\n  if (url.pathname.startsWith('/admin') && role !== 'ADMIN') {\r\n    url.pathname = '/login';\r\n    return NextResponse.redirect(url);\r\n  }\r\n\r\n  // Block student pages unless logged in as student or admin\r\n  if ((url.pathname.startsWith('/myjobs') || \r\n       url.pathname.startsWith('/explore') || \r\n       url.pathname.startsWith('/jobpostings') || \r\n       url.pathname.startsWith('/companies') || \r\n       url.pathname.startsWith('/company/') || \r\n       url.pathname.startsWith('/events') || \r\n       url.pathname.startsWith('/inbox')) && \r\n      role !== 'STUDENT' && role !== 'ADMIN') {\r\n    url.pathname = '/login';\r\n    return NextResponse.redirect(url);\r\n  }\r\n\r\n  // Block homepage `/` unless student or admin\r\n  if (url.pathname === '/' && role !== 'STUDENT' && role !== 'ADMIN') {\r\n    url.pathname = '/login';\r\n    return NextResponse.redirect(url);\r\n  }\r\n\r\n  // Otherwise allow the request\r\n  return NextResponse.next();\r\n}\r\n\r\n// 🔁 Apply middleware to selected routes - optimized for performance\r\nexport const config = {\r\n  matcher: [\r\n    /*\r\n     * Match all request paths except for the ones starting with:\r\n     * - api (API routes)\r\n     * - _next/static (static files)\r\n     * - _next/image (image optimization files)\r\n     * - favicon.ico (favicon file)\r\n     * - public folder files\r\n     */\r\n    '/((?!api|_next/static|_next/image|favicon.ico|.*\\\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',\r\n  ],\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAEO,SAAS,WAAW,OAAO;IAChC,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;IACjC,MAAM,OAAO,QAAQ,OAAO,CAAC,GAAG,CAAC,SAAS;IAE1C,+CAA+C;IAC/C,wCAA4C;QAC1C,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,IAAI,QAAQ,CAAC,SAAS,EAAE,QAAQ,QAAQ;IACtE;IAEA,yDAAyD;IACzD,wCAAwC;IAExC,sCAAsC;IACtC,IAAI,IAAI,QAAQ,KAAK,cAAc,SAAS,SAAS;QACnD,IAAI,QAAQ,GAAG;QACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,0CAA0C;IAC1C,IAAI,IAAI,QAAQ,KAAK,eAAe,SAAS,SAAS;QACpD,IAAI,QAAQ,GAAG;QACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,sDAAsD;IACtD,IAAI,IAAI,QAAQ,CAAC,UAAU,CAAC,eAAe,SAAS,aAAa,SAAS,SAAS;QACjF,IAAI,QAAQ,GAAG;QACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,oBAAoB;IACpB,IAAI,IAAI,QAAQ,CAAC,UAAU,CAAC,aAAa,SAAS,SAAS;QACzD,IAAI,QAAQ,GAAG;QACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,2DAA2D;IAC3D,IAAI,CAAC,IAAI,QAAQ,CAAC,UAAU,CAAC,cACxB,IAAI,QAAQ,CAAC,UAAU,CAAC,eACxB,IAAI,QAAQ,CAAC,UAAU,CAAC,mBACxB,IAAI,QAAQ,CAAC,UAAU,CAAC,iBACxB,IAAI,QAAQ,CAAC,UAAU,CAAC,gBACxB,IAAI,QAAQ,CAAC,UAAU,CAAC,cACxB,IAAI,QAAQ,CAAC,UAAU,CAAC,SAAS,KAClC,SAAS,aAAa,SAAS,SAAS;QAC1C,IAAI,QAAQ,GAAG;QACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,6CAA6C;IAC7C,IAAI,IAAI,QAAQ,KAAK,OAAO,SAAS,aAAa,SAAS,SAAS;QAClE,IAAI,QAAQ,GAAG;QACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,8BAA8B;IAC9B,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAGO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;;KAOC,GACD;KACD;AACH"}}]}