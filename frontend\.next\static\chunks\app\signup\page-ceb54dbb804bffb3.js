(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[879],{1102:(e,a,t)=>{Promise.resolve().then(t.bind(t,77567))},35695:(e,a,t)=>{"use strict";var r=t(18999);t.o(r,"useParams")&&t.d(a,{useParams:function(){return r.useParams}}),t.o(r,"usePathname")&&t.d(a,{usePathname:function(){return r.usePathname}}),t.o(r,"useRouter")&&t.d(a,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(a,{useSearchParams:function(){return r.useSearchParams}})},77567:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>o});var r=t(95155),s=t(12115),l=t(35695),n=t(23464);let i=["Amrita Vishwa Vidyapeetham","Anna University","Ashoka University","Banaras Hindu University (BHU)","BITS Pilani","Christ University (Bangalore)","Delhi University (DU)","FMS Delhi","IIM Ahmedabad","IIM Bangalore","IIM Calcutta","IIM Lucknow","IIIT Allahabad","IIIT Bangalore","IIIT Delhi","IIIT Hyderabad","IIIT Pune","IIT Bombay","IIT Delhi","IIT Hyderabad","IIT Kanpur","IIT Kharagpur","IIT Madras","IIT Roorkee","Indian School of Business (ISB Hyderabad)","Jamia Millia Islamia","Jawaharlal Nehru University (JNU)","Jain University","Manipal University","NIT Calicut","NIT Rourkela","NIT Surathkal","NIT Trichy","NIT Warangal","Osmania University","Shiv Nadar University","SRM Institute of Science and Technology","Symbiosis International University (Pune)","University of Calcutta","University of Hyderabad","University of Mumbai","VIT Vellore","XLRI Jamshedpur"];function o(){let e=(0,l.useRouter)(),[a,t]=(0,s.useState)(""),[o,u]=(0,s.useState)(""),[d,c]=(0,s.useState)(""),[m,h]=(0,s.useState)(""),[g]=(0,s.useState)("STUDENT"),[b,x]=(0,s.useState)(!1),I=(0,s.useRef)(null),y=i.filter(e=>e.toLowerCase().includes(o.toLowerCase())),f=async t=>{if(t.preventDefault(),d!==m)return void alert("Passwords must match");let r="STUDENT";try{await n.A.post("http://127.0.0.1:8000/api/auth/register/student/",{email:a,password:d,first_name:a.split("@")[0],last_name:"",student_id:"TEMP123",contact_email:a,branch:"CSE",gpa:"8.5",college:1}),localStorage.setItem("userEmail",a),localStorage.setItem("collegeName",o),localStorage.setItem("role",r),document.cookie="role=".concat(r,"; path=/; max-age=86400"),alert("Signup successful"),e.push("/login")}catch(e){var s,l;alert((null==(l=e.response)||null==(s=l.data)?void 0:s.message)||"Signup failed")}};return(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-r from-[#242734] to-[#241F2A] flex items-center justify-center p-4",children:(0,r.jsxs)("form",{onSubmit:f,className:"w-full max-w-md bg-white rounded-xl shadow-2xl p-10 flex flex-col gap-6",children:[(0,r.jsx)("h1",{className:"text-center text-2xl text-gray-800 font-bold mb-2",children:"SIGNUP"}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)("label",{className:"mb-2 font-semibold text-gray-800",children:"Email"}),(0,r.jsx)("input",{type:"email",value:a,onChange:e=>t(e.target.value),className:"p-3 rounded-lg border border-gray-300 text-gray-800 text-base outline-none",required:!0})]}),(0,r.jsxs)("div",{className:"flex flex-col relative",children:[(0,r.jsx)("label",{className:"mb-2 font-semibold text-gray-800",children:"College Name"}),(0,r.jsx)("input",{type:"text",value:o,onChange:e=>{u(e.target.value),x(""!==e.target.value.trim())},className:"p-3 rounded-lg border border-gray-300 text-gray-800 text-base outline-none",required:!0}),b&&y.length>0&&(0,r.jsx)("div",{className:"absolute top-full left-0 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg z-50 max-h-48 overflow-y-auto",children:y.map((e,a)=>(0,r.jsx)("div",{onClick:()=>{u(e),x(!1)},className:"cursor-pointer px-4 py-2 hover:bg-blue-100 text-gray-700 border-b border-gray-100",children:e},a))})]}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)("label",{className:"mb-2 font-semibold text-gray-800",children:"Password"}),(0,r.jsx)("input",{type:"password",value:d,onChange:e=>c(e.target.value),className:"p-3 rounded-lg border border-gray-300 text-gray-800 text-base outline-none",required:!0})]}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)("label",{className:"mb-2 font-semibold text-gray-800",children:"Confirm Password"}),(0,r.jsx)("input",{type:"password",value:m,ref:I,onChange:e=>h(e.target.value),className:"p-3 rounded-lg border border-gray-300 text-gray-800 text-base outline-none",required:!0})]}),m&&d!==m&&(0,r.jsx)("div",{className:"text-red-400 text-sm -mt-3",children:"Passwords must match."}),(0,r.jsx)("button",{type:"submit",disabled:!d||d!==m,className:"p-3 rounded-lg bg-indigo-500 text-white text-base font-medium hover:bg-indigo-600 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed",children:"Signup"})]})})}}},e=>{var a=a=>e(e.s=a);e.O(0,[3464,8441,1684,7358],()=>a(1102)),_N_E=e.O()}]);