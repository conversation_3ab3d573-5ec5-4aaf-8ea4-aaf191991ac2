(()=>{var e={};e.id=7168,e.ids=[7168],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4542:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\helpandsupport\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\helpandsupport\\page.jsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},18130:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>o,routeModule:()=>u,tree:()=>c});var r=s(65239),a=s(48088),n=s(88170),i=s.n(n),l=s(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let c={children:["",{children:["admin",{children:["helpandsupport",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,4542)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\helpandsupport\\page.jsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,23697)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\layout.jsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,75535)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\layout.js"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\helpandsupport\\page.jsx"],p={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/helpandsupport/page",pathname:"/admin/helpandsupport",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20142:(e,t,s)=>{Promise.resolve().then(s.bind(s,4542))},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},48730:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63787:(e,t,s)=>{"use strict";function r(){try{let e=localStorage.getItem("access");if(!e)return null;let t=localStorage.getItem("user");if(t){let e=JSON.parse(t);return e.id||e.user_id||null}if(e)try{let t=e.split(".")[1],s=JSON.parse(atob(t));return s.user_id||s.id||s.sub||null}catch(e){console.error("Error decoding JWT token:",e)}return null}catch(e){return console.error("Error getting user ID:",e),null}}function a(){return localStorage.getItem("access")}function n(){try{let e=localStorage.getItem("user");return e?JSON.parse(e):null}catch(e){return console.error("Error getting user data:",e),null}}s.d(t,{F6:()=>r,c4:()=>a,gL:()=>n})},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82881:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var r=s(60687),a=s(43210),n=s(85814),i=s.n(n),l=s(97299),d=s(76717),c=s(96474);let o=(0,s(62688).A)("ticket",[["path",{d:"M2 9a3 3 0 0 1 0 6v2a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2a3 3 0 0 1 0-6V7a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2Z",key:"qn84l0"}],["path",{d:"M13 5v2",key:"dyzc3o"}],["path",{d:"M13 17v2",key:"1ont0d"}],["path",{d:"M13 11v2",key:"1wjjxi"}]]);var p=s(93613),u=s(48730),x=s(5336),m=s(83967),h=s(28885),g=s(63787);let f=()=>{let[e,t]=(0,a.useState)([]),[s,n]=(0,a.useState)(!0),[f,j]=(0,a.useState)(null);(0,a.useEffect)(()=>{(async()=>{try{n(!0);let e=(0,g.gL)(),s={};e&&e.id&&(s.user_id=e.id);let r=await h.yp.getTickets(s),a=[];Array.isArray(r)?a=r:r.data&&Array.isArray(r.data)?a=r.data:r.results&&Array.isArray(r.results)&&(a=r.results),t(a),j(null)}catch(e){console.error("Error fetching tickets:",e),j("Failed to load tickets. Please try again later.")}finally{n(!1)}})()},[]);let y=t=>e.filter(e=>t.includes(e.status)),b=y(["open"]).length,v=y(["in-progress","in_progress"]).length,N=y(["resolved"]).length,w=e.length,k=[...e].sort((e,t)=>new Date(t.createdAt||t.created_at||0).getTime()-new Date(e.createdAt||e.created_at||0).getTime()).slice(0,6);return(0,r.jsxs)("div",{className:"space-y-6 pb-8",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Help & Support Dashboard"}),(0,r.jsx)(d.$,{asChild:!0,className:"bg-blue-600 hover:bg-blue-700",children:(0,r.jsxs)(i(),{href:"/admin/helpandsupport/new",children:[(0,r.jsx)(c.A,{className:"mr-2 h-4 w-4"})," New Ticket"]})})]}),s?(0,r.jsx)("div",{className:"flex justify-center py-10",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"})}):f?(0,r.jsxs)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg",children:[f,(0,r.jsx)(d.$,{onClick:()=>window.location.reload(),variant:"link",className:"text-red-700 underline pl-2",children:"Retry"})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,r.jsx)(l.Zp,{className:"bg-white border shadow-sm",children:(0,r.jsx)(l.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium mt-6",children:"My Tickets"}),(0,r.jsx)("p",{className:"text-2xl font-bold",children:w}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Tickets I've created"})]}),(0,r.jsx)(o,{className:"h-7 w-7 text-gray-700"})]})})}),(0,r.jsx)(l.Zp,{className:"bg-white border shadow-sm",children:(0,r.jsx)(l.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium mt-6",children:"Open"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:b}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Tickets waiting for response"})]}),(0,r.jsx)(p.A,{className:"h-7 w-7 text-blue-500"})]})})}),(0,r.jsx)(l.Zp,{className:"bg-white border shadow-sm",children:(0,r.jsx)(l.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium mt-6",children:"In Progress"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:v}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Tickets being worked on"})]}),(0,r.jsx)(u.A,{className:"h-7 w-7 text-yellow-500"})]})})}),(0,r.jsx)(l.Zp,{className:"bg-white border shadow-sm",children:(0,r.jsx)(l.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium mt-6",children:"Resolved"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-green-600",children:N}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Successfully resolved tickets"})]}),(0,r.jsx)(x.A,{className:"h-7 w-7 text-green-500"})]})})})]}),(0,r.jsxs)("div",{className:"mt-8",children:[(0,r.jsxs)("div",{className:"flex flex-row items-center justify-between space-y-0 pb-2 mb-4",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold",children:"My Recent Tickets"}),(0,r.jsx)(d.$,{variant:"outline",asChild:!0,className:"text-sm",children:(0,r.jsx)(i(),{href:"/admin/helpandsupport/tickets",children:"View All"})})]}),(0,r.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3",children:k.length>0?k.map(e=>(0,r.jsx)(m.A,{ticket:e},e.id)):(0,r.jsxs)(l.Zp,{className:"col-span-full p-6 text-center",children:[(0,r.jsx)("p",{className:"text-muted-foreground",children:"No tickets found"}),(0,r.jsx)(d.$,{className:"mt-4",asChild:!0,children:(0,r.jsx)(i(),{href:"/admin/helpandsupport/new",children:"Create Your First Ticket"})})]})})]})]})]})}},83190:(e,t,s)=>{Promise.resolve().then(s.bind(s,82881))},83967:(e,t,s)=>{"use strict";s.d(t,{A:()=>d});var r=s(60687);s(43210);var a=s(85814),n=s.n(a),i=s(97299),l=s(78201);let d=({ticket:e})=>{let t=()=>{let t=e.createdBy||e.created_by;return t&&(t.name||t.username||t.email)||"Unknown User"};return(0,r.jsx)(n(),{href:`/admin/helpandsupport/tickets/${e.id}`,children:(0,r.jsx)(i.Zp,{className:"h-full hover:shadow-md transition-shadow",children:(0,r.jsx)(i.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("h3",{className:"text-base font-medium text-gray-900 line-clamp-1 mb-3 mt-6",children:e.title}),(0,r.jsxs)("div",{className:"flex gap-2 flex-wrap",children:[(0,r.jsx)("span",{className:`px-2 py-1 rounded-md text-xs font-medium ${(e=>{switch(e){case"high":return"bg-red-100 text-red-800";case"medium":return"bg-orange-100 text-orange-800";default:return"bg-gray-100 text-gray-800"}})(e.priority)}`,children:e.priority}),(0,r.jsx)("span",{className:`px-2 py-1 rounded-md text-xs font-medium ${(e=>{switch(e){case"open":return"bg-blue-100 text-blue-800";case"in-progress":return"bg-yellow-100 text-yellow-800";case"resolved":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}})(e.status)}`,children:e.status.replace("-"," ")}),(0,r.jsx)("span",{className:"px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700",children:e.category.replace("-"," ")})]}),(0,r.jsx)("p",{className:"text-sm text-gray-600 line-clamp-2",children:e.description}),(0,r.jsxs)("div",{className:"flex items-center justify-between pt-2 border-t border-gray-100",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-xs text-gray-500",children:[(0,r.jsx)("div",{className:"h-5 w-5 rounded-full bg-gray-200 flex items-center justify-center text-xs font-medium",children:t().charAt(0).toUpperCase()}),(0,r.jsx)("span",{children:t()})]}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:(e=>{try{let t=new Date(e);if(isNaN(t.getTime()))return"Invalid date";return(0,l.m)(t,{addSuffix:!0})}catch(e){return"Invalid date"}})(e.createdAt||e.created_at||e.created_at_date)})]})]})})})})}},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,681,1658,1060,4403,4454,2305,6728],()=>s(18130));module.exports=r})();