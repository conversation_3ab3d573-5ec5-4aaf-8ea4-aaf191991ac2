(()=>{var e={};e.id=111,e.ids=[111],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10722:(e,s,t)=>{Promise.resolve().then(t.bind(t,11527))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11527:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>f});var a=t(60687),r=t(43210),i=t(24664),n=t(98848),l=t(9535),o=t(37325),c=t(81080),d=t(95994),p=t(80556),m=t(90910),u=t(81172),h=t(20798),x=t(58869),g=t(53411);function f({children:e}){let[s,t]=(0,r.useState)(""),f=[{items:[{title:"Dashboard",href:"/admin/dashboard",icon:(0,a.jsx)(n.A,{})},{title:"Jobs",href:"/admin/jobs",icon:(0,a.jsx)(l.A,{})},{title:"Applications",href:"/admin/applications",icon:(0,a.jsx)(o.A,{})},{title:"Posts",href:"/admin/posts",icon:(0,a.jsx)(h.A,{className:"w-5 h-5"})},{title:"Student Management",href:"/admin/student-management",icon:(0,a.jsx)(x.A,{className:"w-5 h-5"})},{title:"Student Analytics",href:"/admin/analytics",icon:(0,a.jsx)(g.A,{className:"w-5 h-5"})},{title:"Company Management",href:"/admin/companymanagement",icon:(0,a.jsx)(c.A,{})},{title:"Forms",href:"/admin/form",icon:(0,a.jsx)(d.A,{})}]}],y=[{title:"My Profile",href:"/admin/profile",icon:(0,a.jsx)(p.A,{})},{title:"Settings",href:"../settings",icon:(0,a.jsx)(m.A,{})},{title:"Contact Support",href:"/admin/helpandsupport",icon:(0,a.jsx)(u.A,{})}];return(0,a.jsx)("div",{className:"h-screen bg-gray-50 overflow-hidden",children:(0,a.jsxs)("div",{className:"flex h-full",children:[(0,a.jsx)(i.A,{sections:f,bottomItems:y,defaultExpanded:!1,navbarHeight:"0",className:"z-20"}),(0,a.jsx)("div",{className:"flex-1 p-6 ml-20 overflow-y-auto",children:(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-md p-6 min-h-full text-black",children:e})})]})})}},12412:e=>{"use strict";e.exports=require("assert")},13637:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var a=t(60687),r=t(43210),i=t(16189),n=t(28559),l=t(63143),o=t(97992),c=t(57800),d=t(23928),p=t(40228),m=t(13861),u=t(27900),h=t(65222);function x({params:e}){let s=parseInt((0,r.use)(e).id),t=(0,i.useRouter)(),[x,g]=(0,r.useState)(null),[f,y]=(0,r.useState)(!0),[j,b]=(0,r.useState)(null);return f?(0,a.jsx)("div",{className:"h-full overflow-y-auto",children:(0,a.jsx)("div",{className:"p-6",children:(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"text-lg",children:"Loading job details..."})})})}):j?(0,a.jsx)("div",{className:"h-full overflow-y-auto",children:(0,a.jsx)("div",{className:"p-6",children:(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsxs)("div",{className:"text-red-600",children:["Error: ",j]})})})}):x?(0,a.jsx)("div",{className:"h-full overflow-y-auto",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("button",{onClick:()=>t.back(),className:"flex items-center text-gray-600 hover:text-gray-900 mb-4",children:[(0,a.jsx)(n.A,{className:"w-5 h-5 mr-2"}),"Back to Company Management"]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:x.title}),(0,a.jsxs)("p",{className:"text-gray-600 mt-2",children:[x.company_name," • Job ID: ",s]})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)("span",{className:`px-3 py-1 rounded-full text-sm font-medium ${x.is_published?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:x.is_published?"Published":"Draft"}),(0,a.jsxs)("button",{onClick:()=>t.push(`/admin/jobs/edit/${s}`),className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center",children:[(0,a.jsx)(l.A,{className:"w-4 h-4 mr-2"}),"Edit Job"]})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white shadow-sm rounded-lg p-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Job Details"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(o.A,{className:"w-5 h-5 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Location"}),(0,a.jsx)("p",{className:"font-medium",children:x.location})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(c.A,{className:"w-5 h-5 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Type"}),(0,a.jsx)("p",{className:"font-medium",children:x.job_type})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(d.A,{className:"w-5 h-5 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Salary"}),(0,a.jsx)("p",{className:"font-medium",children:x.salary_min&&x.salary_max?`$${x.salary_min?.toLocaleString()} - $${x.salary_max?.toLocaleString()}`:"Not specified"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(p.A,{className:"w-5 h-5 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Deadline"}),(0,a.jsx)("p",{className:"font-medium",children:x.application_deadline?new Date(x.application_deadline).toLocaleDateString():"No deadline"})]})]})]}),x.duration&&(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(p.A,{className:"w-5 h-5 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Duration"}),(0,a.jsx)("p",{className:"font-medium",children:x.duration})]})]})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900 mb-3",children:"Description"}),(0,a.jsx)(h.G_,{description:x.description,className:""})]})]}),x.requirements&&x.requirements.length>0&&(0,a.jsxs)("div",{className:"bg-white shadow-sm rounded-lg p-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Requirements"}),(0,a.jsx)("ul",{className:"space-y-2",children:x.requirements.map((e,s)=>(0,a.jsxs)("li",{className:"flex items-start space-x-2",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-gray-700",children:e})]},s))})]}),x.skills&&x.skills.length>0&&(0,a.jsxs)("div",{className:"bg-white shadow-sm rounded-lg p-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Required Skills"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:x.skills.map((e,s)=>(0,a.jsx)("span",{className:"px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium",children:e},s))})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white shadow-sm rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Job Statistics"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Applications"}),(0,a.jsx)("span",{className:"font-semibold text-gray-900",children:x.applications_count})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Created"}),(0,a.jsx)("span",{className:"font-semibold text-gray-900",children:new Date(x.created_at).toLocaleDateString()})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Last Updated"}),(0,a.jsx)("span",{className:"font-semibold text-gray-900",children:new Date(x.updated_at).toLocaleDateString()})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Status"}),(0,a.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${x.is_published?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:x.is_published?"Published":"Draft"})]})]})]}),(0,a.jsxs)("div",{className:"bg-white shadow-sm rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Actions"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("button",{onClick:()=>t.push(`/admin/jobs/edit/${s}`),className:"w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center justify-center",children:[(0,a.jsx)(l.A,{className:"w-4 h-4 mr-2"}),"Edit Job"]}),(0,a.jsxs)("button",{onClick:()=>t.push(`/admin/applications?job_id=${s}`),className:"w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 flex items-center justify-center",children:[(0,a.jsx)(m.A,{className:"w-4 h-4 mr-2"}),"View Applications"]}),(0,a.jsxs)("button",{className:"w-full bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center justify-center",children:[(0,a.jsx)(u.A,{className:"w-4 h-4 mr-2"}),"Send Message"]})]})]}),(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-semibold text-blue-900 mb-2",children:"Quick Info"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-blue-700",children:"Job ID:"}),(0,a.jsx)("span",{className:"font-medium text-blue-900",children:x.id})]}),x.company_id&&(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-blue-700",children:"Company ID:"}),(0,a.jsx)("span",{className:"font-medium text-blue-900",children:x.company_id})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-blue-700",children:"Type:"}),(0,a.jsx)("span",{className:"font-medium text-blue-900",children:x.job_type})]})]})]})]})]})]})}):(0,a.jsx)("div",{className:"h-full overflow-y-auto",children:(0,a.jsx)("div",{className:"p-6",children:(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"text-gray-600",children:"Job not found"})})})})}t(75518)},13861:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20798:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("megaphone",[["path",{d:"m3 11 18-5v12L3 14v-3z",key:"n962bs"}],["path",{d:"M11.6 16.8a3 3 0 1 1-5.8-1.6",key:"1yl0tm"}]])},21820:e=>{"use strict";e.exports=require("os")},23697:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\layout.jsx","default")},23928:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},27900:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37325:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});var a=(0,t(6445).A)("outline","clipboard-list","IconClipboardList",[["path",{d:"M9 5h-2a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-12a2 2 0 0 0 -2 -2h-2",key:"svg-0"}],["path",{d:"M9 3m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z",key:"svg-1"}],["path",{d:"M9 12l.01 0",key:"svg-2"}],["path",{d:"M13 12l2 0",key:"svg-3"}],["path",{d:"M9 16l.01 0",key:"svg-4"}],["path",{d:"M13 16l2 0",key:"svg-5"}]])},40228:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},48173:(e,s,t)=>{Promise.resolve().then(t.bind(t,23697))},53411:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57800:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},58138:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var a=t(51060);t(51421);let r=a.A.create({baseURL:"http://127.0.0.1:8000",headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>{let s=localStorage.getItem("access_token");return s&&(e.headers.Authorization=`Bearer ${s}`),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,async e=>{let s=e.config;if(e.response?.status===401&&!s._retry){s._retry=!0;try{let e=localStorage.getItem("refresh_token");if(e){let t=await a.A.post("http://127.0.0.1:8000/api/auth/token/refresh/",{refresh:e});return localStorage.setItem("access_token",t.data.access),s.headers.Authorization=`Bearer ${t.data.access}`,r(s)}}catch(e){console.error("Error refreshing token:",e)}}return Promise.reject(e)});let i=r},62278:(e,s,t)=>{Promise.resolve().then(t.bind(t,13637))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},65091:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\jobs\\\\[id]\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\jobs\\[id]\\page.jsx","default")},65222:(e,s,t)=>{"use strict";t.d(s,{G_:()=>r});var a=t(60687);function r({description:e,className:s=""}){let t=e?e.replace(/\n/g,"<br />").replace(/•/g,"•").replace(/\*\s/g,"• ").replace(/-\s/g,"• ").trim():"No description provided.";return(0,a.jsx)("div",{className:`text-gray-700 leading-relaxed ${s}`,dangerouslySetInnerHTML:{__html:t}})}},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var a=t(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},75518:(e,s,t)=>{"use strict";t.d(s,{G$:()=>l,N6:()=>r,Om:()=>d,T4:()=>p,YQ:()=>n,_S:()=>o,lh:()=>c,vr:()=>i});var a=t(58138);function r(e={}){let s=new URLSearchParams;e.page&&s.append("page",e.page),e.per_page&&s.append("per_page",e.per_page),e.job_type&&"ALL"!==e.job_type&&s.append("job_type",e.job_type),e.location&&"ALL"!==e.location&&s.append("location",e.location),e.salary_min&&s.append("salary_min",e.salary_min),e.search&&s.append("search",e.search);let t=s.toString(),i=`/api/v1/college/default-college/jobs/${t?`?${t}`:""}`;return a.A.get(i)}function i(e,s,t={}){if(!Object.values(t).some(e=>e instanceof File))return a.A.post(`/api/v1/college/default-college/jobs/${e}/apply/`,{cover_letter:s,additional_field_responses:t});{let r=new FormData;return r.append("cover_letter",s),Object.entries(t).forEach(([e,s])=>{s instanceof File?r.append(e,s):r.append(e,JSON.stringify(s))}),a.A.post(`/api/v1/college/default-college/jobs/${e}/apply/`,r,{headers:{"Content-Type":"multipart/form-data"}})}}function n(e){return a.A.get(`/api/v1/college/default-college/jobs/${e}/`)}function l(){return a.A.get("/api/v1/college/default-college/jobs/applied/")}function o(e){return a.A.post("/api/v1/college/default-college/jobs/create/",e)}function c(e,s){return a.A.put(`/api/v1/college/default-college/jobs/${e}/`,s)}function d(e={}){let s=new URLSearchParams;e.page&&s.append("page",e.page),e.per_page&&s.append("per_page",e.per_page),e.search&&s.append("search",e.search),e.type&&"All"!==e.type&&s.append("job_type",e.type),e.minCTC&&s.append("salary_min",e.minCTC),e.maxCTC&&s.append("salary_max",e.maxCTC),e.minStipend&&s.append("stipend_min",e.minStipend),e.maxStipend&&s.append("stipend_max",e.maxStipend),e.location&&s.append("location",e.location),void 0!==e.is_published&&s.append("is_published",e.is_published),e.company_id&&s.append("company_id",e.company_id),e.company_name&&s.append("company_name",e.company_name);let t=s.toString(),r=`/api/v1/college/default-college/jobs/admin/${t?`?${t}`:""}`;return console.log("\uD83C\uDF10 listJobsAdmin calling URL:",r,"with params:",e),a.A.get(r).then(e=>(console.log("\uD83C\uDF10 listJobsAdmin response:",{status:e.status,totalJobs:e.data?.pagination?.total_count||0,currentPage:e.data?.pagination?.current_page||1,totalPages:e.data?.pagination?.total_pages||1}),e)).catch(e=>{throw console.error("\uD83C\uDF10 listJobsAdmin error:",e),console.error("\uD83C\uDF10 listJobsAdmin error response:",e.response?.data),e})}function p(e){return a.A.patch(`/api/v1/jobs/${e}/toggle-publish/`)}},79551:e=>{"use strict";e.exports=require("url")},79576:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>c});var a=t(65239),r=t(48088),i=t(88170),n=t.n(i),l=t(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let c={children:["",{children:["admin",{children:["jobs",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,65091)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\jobs\\[id]\\page.jsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,23697)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\layout.jsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,75535)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\layout.js"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\jobs\\[id]\\page.jsx"],p={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/jobs/[id]/page",pathname:"/admin/jobs/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},81172:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});var a=(0,t(6445).A)("outline","help","IconHelp",[["path",{d:"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0",key:"svg-0"}],["path",{d:"M12 17l0 .01",key:"svg-1"}],["path",{d:"M12 13.5a1.5 1.5 0 0 1 1 -1.5a2.6 2.6 0 1 0 -3 -4",key:"svg-2"}]])},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},93822:(e,s,t)=>{Promise.resolve().then(t.bind(t,65091))},94735:e=>{"use strict";e.exports=require("events")},95994:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});var a=(0,t(6445).A)("outline","forms","IconForms",[["path",{d:"M12 3a3 3 0 0 0 -3 3v12a3 3 0 0 0 3 3",key:"svg-0"}],["path",{d:"M6 3a3 3 0 0 1 3 3v12a3 3 0 0 1 -3 3",key:"svg-1"}],["path",{d:"M13 7h7a1 1 0 0 1 1 1v8a1 1 0 0 1 -1 1h-7",key:"svg-2"}],["path",{d:"M5 7h-1a1 1 0 0 0 -1 1v8a1 1 0 0 0 1 1h1",key:"svg-3"}],["path",{d:"M17 12h.01",key:"svg-4"}],["path",{d:"M13 12h.01",key:"svg-5"}]])},97992:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[4447,681,1658,1060,2305],()=>t(79576));module.exports=a})();