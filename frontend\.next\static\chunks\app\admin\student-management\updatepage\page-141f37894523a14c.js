(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3309],{1243:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},2415:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var a=s(95155),l=s(35169),r=s(29869);let n=(0,s(19946).A)("file-spreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]]);var i=s(91788),c=s(53904),d=s(40646),o=s(54861),h=s(1243),u=s(12115),m=s(35695),x=s(3925),p=s(69327),g=s(28835);function y(){let e=(0,m.useRouter)(),t=(0,u.useRef)(null),[s,y]=(0,u.useState)(null),[b,f]=(0,u.useState)(!1),[j,N]=(0,u.useState)(0),[w,v]=(0,u.useState)([]),[k,_]=(0,u.useState)(null),[A,C]=(0,u.useState)(null),[S,P]=(0,u.useState)(!1),[M,E]=(0,u.useState)(null),R={"Roll Number":"student_id",RollNumber:"student_id","Student ID":"student_id",StudentID:"student_id","Roll No":"student_id",Student_ID:"student_id","First Name":"first_name",FirstName:"first_name","Last Name":"last_name",LastName:"last_name",Name:"name","Full Name":"name",Email:"contact_email","Contact Email":"contact_email",Phone:"phone","Phone Number":"phone",Mobile:"phone",Department:"branch",Branch:"branch",CGPA:"gpa",GPA:"gpa",Grade:"gpa",Address:"address",City:"city",District:"district",State:"state",Pincode:"pincode","Pin Code":"pincode",Country:"country","Joining Year":"joining_year","Passout Year":"passout_year","Graduation Year":"passout_year","Date of Birth":"date_of_birth",DOB:"date_of_birth","Parent Contact":"parent_contact",Skills:"skills","Class 10 CGPA":"tenth_cgpa","10th CGPA":"tenth_cgpa","Class 10 Percentage":"tenth_percentage","10th Percentage":"tenth_percentage","Class 10 Board":"tenth_board","10th Board":"tenth_board","Class 10 School":"tenth_school","10th School":"tenth_school","Class 10 Year":"tenth_year_of_passing","10th Year":"tenth_year_of_passing","Class 10 Location":"tenth_location","10th Location":"tenth_location","Class 12 CGPA":"twelfth_cgpa","12th CGPA":"twelfth_cgpa","Class 12 Percentage":"twelfth_percentage","12th Percentage":"twelfth_percentage","Class 12 Board":"twelfth_board","12th Board":"twelfth_board","Class 12 School":"twelfth_school","12th School":"twelfth_school","Class 12 Year":"twelfth_year_of_passing","12th Year":"twelfth_year_of_passing","Class 12 Location":"twelfth_location","12th Location":"twelfth_location","Class 12 Stream":"twelfth_specialization","12th Stream":"twelfth_specialization"},D=()=>{y(null),f(!1),N(0),v([]),_(null),C(null),P(!1),E(null),t.current&&(t.current.value="")},L=(e,t)=>{let s=new Date().toLocaleTimeString();v(a=>[...a,{type:e,message:t,timestamp:s}])},F=async e=>new Promise((t,s)=>{let a=new FileReader;a.onload=e=>{try{let a=e.target.result,l=x.LF(a,{type:"binary"}),r=l.SheetNames[0],n=l.Sheets[r],i=x.Wp.sheet_to_json(n);if(0===i.length)return void s(Error("Excel file is empty"));let c=Object.keys(i[0]),d=c.find(e=>Object.keys(R).some(t=>t.toLowerCase()===e.toLowerCase()&&"student_id"===R[t]));if(!d)return void s(Error("Roll Number column is mandatory. Please include a column with roll numbers."));L("success","Excel file parsed successfully. Found ".concat(i.length," rows.")),L("info","Detected columns: ".concat(c.join(", "))),L("success","Roll Number column found: ".concat(d)),t({data:i,headers:c,rollNumberColumn:d})}catch(e){s(Error("Failed to parse Excel file: ".concat(e.message)))}},a.onerror=()=>s(Error("Failed to read file")),a.readAsBinaryString(e)}),z=e=>{let t=[],s=[];return e.data.forEach((e,a)=>{let l={},r=!1;Object.entries(e).forEach(e=>{let[t,a]=e,n=Object.keys(R).find(e=>e.toLowerCase()===t.toLowerCase());if(n){let e=R[n];if(null!=a&&""!==a){if("name"===e&&"string"==typeof a){let e=a.trim().split(" ");l.first_name=e[0]||"",l.last_name=e.slice(1).join(" ")||""}else l[e]=a;r=!0}}else s.includes(t)||s.push(t)}),r&&l.student_id&&t.push({rowIndex:a+2,data:l})}),s.length>0&&L("warning","Unmapped columns (will be ignored): ".concat(s.join(", "))),t},B=async()=>{if(!s)return void L("error","Please select an Excel file first");f(!0),N(0),v([]),E(null),P(!1),_(null),C(null);try{L("info","Parsing Excel file..."),N(10);let e=await F(s);_(e),L("info","Mapping Excel columns to database fields..."),N(20);let t=z(e);L("success","Successfully mapped ".concat(t.length," rows for processing")),C(t.slice(0,5)),P(!0),N(30)}catch(e){L("error",e.message),N(0)}finally{f(!1)}},U=async()=>{if(!k||!A)return;f(!0),P(!1);let e=z(k),t=e.length,s=0,a=0,l=0,r=[];if(L("info","Starting update process for ".concat(t," records...")),!(0,g.c4)()){L("error","Authentication token not found. Please login again."),f(!1);return}try{for(let n=0;n<e.length;n++){let{rowIndex:i,data:c}=e[n],d=Math.round((n+1)/t*70)+30;N(d);try{let e=await p.Nt.getStudents({search:c.student_id,page_size:1});if(0===e.data.length){l++,L("warning","Row ".concat(i,': Student with Roll Number "').concat(c.student_id,'" not found - skipped'));continue}let t=e.data[0],a={...c};delete a.student_id,await p.Nt.updateStudent(t.id,a),s++,L("success","Row ".concat(i,": Updated student ").concat(c.student_id," successfully"))}catch(t){a++;let e="Row ".concat(i,": Failed to update ").concat(c.student_id," - ").concat(t.message);L("error",e),r.push(e)}n<e.length-1&&await new Promise(e=>setTimeout(e,100))}let n={total:t,success:s,errors:a,skipped:l,errorDetails:r};E(n),N(100),L("info","=== UPDATE SUMMARY ==="),L("success","✅ Successfully updated: ".concat(s," students")),L("error","❌ Failed updates: ".concat(a," students")),L("warning","⚠️ Skipped (not found): ".concat(l," students")),L("info","\uD83D\uDCCA Total processed: ".concat(t," records"))}catch(e){L("error","Fatal error during update process: ".concat(e.message))}finally{f(!1)}};return(0,a.jsxs)("div",{className:"flex-1 p-6 overflow-y-auto h-full",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)("button",{onClick:()=>e.back(),className:"flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:[(0,a.jsx)(l.A,{className:"w-4 h-4"}),"Back"]}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-800 ml-4",children:"Update Student Data"})]}),M&&(0,a.jsxs)("button",{onClick:()=>{D(),L("info","Ready for new file upload")},className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(r.A,{className:"w-4 h-4"}),"Upload New File"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:[(0,a.jsxs)("h2",{className:"text-lg font-semibold text-blue-800 mb-4 flex items-center gap-2",children:[(0,a.jsx)(n,{className:"w-5 h-5"}),"Instructions"]}),(0,a.jsxs)("div",{className:"space-y-3 text-sm text-blue-700",children:[(0,a.jsx)("p",{children:(0,a.jsx)("strong",{children:"Requirements:"})}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-1 ml-4",children:[(0,a.jsx)("li",{children:(0,a.jsx)("strong",{children:"Only one File can be processed at a time"})}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"Roll Number column is mandatory"})," (will be used to match existing students)"]}),(0,a.jsx)("li",{children:"Only existing students will be updated (no new students created)"}),(0,a.jsx)("li",{children:"Excel files (.xlsx, .xls) are supported"}),(0,a.jsx)("li",{children:"Any number of columns can be included"})]}),(0,a.jsx)("p",{className:"mt-4",children:(0,a.jsx)("strong",{children:"Supported Column Names:"})}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-xs bg-white p-3 rounded border",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:"Identity:"}),(0,a.jsx)("p",{children:"Roll Number, Student ID"}),(0,a.jsx)("p",{className:"font-medium mt-2",children:"Basic Info:"}),(0,a.jsx)("p",{children:"First Name, Last Name, Name"}),(0,a.jsx)("p",{children:"Email, Phone, Department"}),(0,a.jsx)("p",{children:"CGPA, Address, City, State"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:"Academic:"}),(0,a.jsx)("p",{children:"Joining Year, Passout Year"}),(0,a.jsx)("p",{children:"Class 10 CGPA, 10th Board"}),(0,a.jsx)("p",{children:"Class 12 CGPA, 12th Board"}),(0,a.jsx)("p",{children:"Date of Birth, Skills"})]})]})]}),(0,a.jsxs)("button",{onClick:()=>{let e=x.Wp.json_to_sheet([{"Roll Number":"CS2021001","First Name":"John","Last Name":"Doe",Email:"<EMAIL>",Phone:"9876543210",Department:"Computer Science",CGPA:"8.5",City:"Mumbai",State:"Maharashtra"},{"Roll Number":"EC2021002","First Name":"Jane","Last Name":"Smith",Email:"<EMAIL>",Phone:"9876543211",Department:"Electronics",CGPA:"9.0",City:"Delhi",State:"Delhi"}]),t=x.Wp.book_new();x.Wp.book_append_sheet(t,e,"Students"),x._h(t,"student_update_template.xlsx")},className:"mt-4 flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(i.A,{className:"w-4 h-4"}),"Download Sample Template"]})]}),(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Upload Excel File"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer ".concat(b?"border-gray-200 bg-gray-50 cursor-not-allowed":"border-gray-300 hover:border-blue-400"),onClick:()=>{var e;return!b&&(null==(e=t.current)?void 0:e.click())},children:[(0,a.jsx)(r.A,{className:"w-12 h-12 mx-auto mb-4 ".concat(b?"text-gray-300":"text-gray-400")}),(0,a.jsx)("p",{className:"".concat(b?"text-gray-400":"text-gray-600"),children:b?"Processing...":"Click to select Excel file or drag and drop"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"Supports .xlsx and .xls files"})]}),(0,a.jsx)("input",{ref:t,type:"file",accept:".xlsx,.xls",onChange:e=>{let t=e.target.files[0];if(t){if(!t.name.match(/\.(xlsx|xls)$/i))return void L("error","Please select a valid Excel file (.xlsx or .xls)");N(0),v([]),_(null),C(null),P(!1),E(null),y(t),L("info","File selected: ".concat(t.name," (").concat((t.size/1024/1024).toFixed(2)," MB)"))}},disabled:b,className:"hidden"}),s&&(0,a.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-green-50 border border-green-200 rounded-lg",children:[(0,a.jsx)(n,{className:"w-5 h-5 text-green-600"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"font-medium text-green-800",children:s.name}),(0,a.jsxs)("p",{className:"text-sm text-green-600",children:[(s.size/1024/1024).toFixed(2)," MB"]})]})]}),(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsxs)("button",{onClick:B,disabled:!s||b||S,className:"flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:[b?(0,a.jsx)(c.A,{className:"w-4 h-4 animate-spin"}):(0,a.jsx)(r.A,{className:"w-4 h-4"}),b?"Processing...":"Process File"]}),(s||w.length>0)&&!b&&(0,a.jsx)("button",{onClick:D,className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:"Reset"})]})]})]}),(b||j>0)&&(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Progress"}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3 mb-2",children:(0,a.jsx)("div",{className:"bg-blue-600 h-3 rounded-full transition-all duration-300",style:{width:"".concat(j,"%")}})}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[j,"% complete"]}),b&&(0,a.jsx)("p",{className:"text-xs text-blue-600 mt-2",children:"Please wait while we process your file..."})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[S&&A&&(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Preview Data (First 5 rows)"}),(0,a.jsx)("div",{className:"overflow-x-auto mb-4",children:(0,a.jsxs)("table",{className:"min-w-full text-sm border border-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-3 py-2 border text-left",children:"Row"}),(0,a.jsx)("th",{className:"px-3 py-2 border text-left",children:"Roll Number"}),(0,a.jsx)("th",{className:"px-3 py-2 border text-left",children:"Fields to Update"})]})}),(0,a.jsx)("tbody",{children:A.map((e,t)=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-3 py-2 border",children:e.rowIndex}),(0,a.jsx)("td",{className:"px-3 py-2 border font-medium",children:e.data.student_id}),(0,a.jsx)("td",{className:"px-3 py-2 border",children:Object.keys(e.data).filter(e=>"student_id"!==e).join(", ")})]},t))})]})}),(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsxs)("button",{onClick:U,disabled:b,className:"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:[(0,a.jsx)(d.A,{className:"w-4 h-4"}),b?"Updating...":"Confirm & Update"]}),(0,a.jsx)("button",{onClick:()=>P(!1),disabled:b,className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:"Cancel"})]})]}),M&&(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"Update Summary"}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-green-600",children:[(0,a.jsx)(d.A,{className:"w-4 h-4"}),"Process Complete"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[(0,a.jsxs)("div",{className:"text-center p-3 bg-green-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:M.success}),(0,a.jsx)("div",{className:"text-sm text-green-700",children:"Updated"})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-red-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-red-600",children:M.errors}),(0,a.jsx)("div",{className:"text-sm text-red-700",children:"Errors"})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-yellow-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:M.skipped}),(0,a.jsx)("div",{className:"text-sm text-yellow-700",children:"Skipped"})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-blue-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:M.total}),(0,a.jsx)("div",{className:"text-sm text-blue-700",children:"Total"})]})]}),(0,a.jsx)("div",{className:"mt-4 p-3 bg-blue-50 rounded-lg",children:(0,a.jsx)("p",{className:"text-sm text-blue-800",children:"Upload completed! You can now upload a new file or go back to student management."})})]}),(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"Activity Log"}),w.length>0&&(0,a.jsx)("button",{onClick:()=>v([]),className:"text-sm text-gray-500 hover:text-gray-700",children:"Clear Logs"})]}),(0,a.jsx)("div",{className:"max-h-96 overflow-y-auto space-y-2",children:0===w.length?(0,a.jsx)("p",{className:"text-gray-500 text-sm",children:"No activity yet. Upload an Excel file to begin."}):w.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-start gap-3 p-3 rounded-lg text-sm ".concat("success"===e.type?"bg-green-50 text-green-800":"error"===e.type?"bg-red-50 text-red-800":"warning"===e.type?"bg-yellow-50 text-yellow-800":"bg-blue-50 text-blue-800"),children:["success"===e.type&&(0,a.jsx)(d.A,{className:"w-4 h-4 mt-0.5 flex-shrink-0"}),"error"===e.type&&(0,a.jsx)(o.A,{className:"w-4 h-4 mt-0.5 flex-shrink-0"}),"warning"===e.type&&(0,a.jsx)(h.A,{className:"w-4 h-4 mt-0.5 flex-shrink-0"}),"info"===e.type&&(0,a.jsx)(c.A,{className:"w-4 h-4 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{children:e.message}),(0,a.jsx)("p",{className:"text-xs opacity-70",children:e.timestamp})]})]},t))})]})]})]})]})}},19946:(e,t,s)=>{"use strict";s.d(t,{A:()=>h});var a=s(12115);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),r=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),n=e=>{let t=r(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return t.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim()},c=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,a.forwardRef)((e,t)=>{let{color:s="currentColor",size:l=24,strokeWidth:r=2,absoluteStrokeWidth:n,className:o="",children:h,iconNode:u,...m}=e;return(0,a.createElement)("svg",{ref:t,...d,width:l,height:l,stroke:s,strokeWidth:n?24*Number(r)/Number(l):r,className:i("lucide",o),...!h&&!c(m)&&{"aria-hidden":"true"},...m},[...u.map(e=>{let[t,s]=e;return(0,a.createElement)(t,s)}),...Array.isArray(h)?h:[h]])}),h=(e,t)=>{let s=(0,a.forwardRef)((s,r)=>{let{className:c,...d}=s;return(0,a.createElement)(o,{ref:r,iconNode:t,className:i("lucide-".concat(l(n(e))),"lucide-".concat(e),c),...d})});return s.displayName=n(e),s}},28835:(e,t,s)=>{"use strict";function a(){try{let e=localStorage.getItem("access");if(!e)return null;let t=localStorage.getItem("user");if(t){let e=JSON.parse(t);return e.id||e.user_id||null}if(e)try{let t=e.split(".")[1],s=JSON.parse(atob(t));return s.user_id||s.id||s.sub||null}catch(e){console.error("Error decoding JWT token:",e)}return null}catch(e){return console.error("Error getting user ID:",e),null}}function l(){return localStorage.getItem("access")}function r(){try{let e=localStorage.getItem("user");return e?JSON.parse(e):null}catch(e){return console.error("Error getting user data:",e),null}}s.d(t,{F6:()=>a,c4:()=>l,gL:()=>r})},29869:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},32383:()=>{},33786:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},35169:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},35695:(e,t,s)=>{"use strict";var a=s(18999);s.o(a,"useParams")&&s.d(t,{useParams:function(){return a.useParams}}),s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(t,{useSearchParams:function(){return a.useSearchParams}})},40646:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},53904:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},54416:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},54861:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},71007:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},75525:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},76517:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]])},81284:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},83686:()=>{},85339:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},91788:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},95720:(e,t,s)=>{Promise.resolve().then(s.bind(s,2415))}},e=>{var t=t=>e(e.s=t);e.O(0,[3524,3464,3983,9327,8441,1684,7358],()=>t(95720)),_N_E=e.O()}]);