(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1260,1318,5481,8937],{21260:(e,t,a)=>{"use strict";a.d(t,{Ly:()=>n,_N:()=>o,companies:()=>s,zZ:()=>i});var r=a(48937);let s=[{id:1,name:"TechCorp Inc",description:"Leading technology solutions provider",industry:"Technology",size:"500-1000",founded:"2010",website:"https://techcorp.com"},{id:2,name:"DataCorp",description:"Data analytics and insights company",industry:"Data Analytics",size:"100-500",founded:"2015",website:"https://datacorp.com"}],n=[{id:1,job_id:25,title:"Software Engineer",company:"TechCorp Inc",status:"APPLIED",application_deadline:"2024-05-30T23:59:59Z"},{id:2,job_id:26,title:"Data Scientist",company:"DataCorp",status:"INTERVIEW SCHEDULED",application_deadline:"2024-06-15T23:59:59Z"}],o=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{let t={...e,_t:new Date().getTime()};console.log("Fetching companies with cache busting...");let a=await (0,r.mm)(t),n=[];a.data&&Array.isArray(a.data)?n=a.data:a.data&&a.data.results&&Array.isArray(a.data.results)?n=a.data.results:a.data&&a.data.data&&Array.isArray(a.data.data)&&(n=a.data.data);let o=n.map(r.Y_);if(console.log("Fetched ".concat(o.length," companies from API")),0===o.length)return console.warn("API returned empty companies array, using static data"),s;return o}catch(e){console.error("Error fetching companies:",e);try{console.log("Trying alternate endpoint format...");let e=await fetch("/api/v1/college/default-college/companies/");if(e.ok){let t=await e.json(),a=Array.isArray(t)?t:t.data||t.results||[];if(a.length>0)return console.log("Successfully retrieved companies from alternate endpoint"),a.map(r.Y_)}}catch(e){console.error("Alternate endpoint also failed:",e)}return s}};function i(e){return console.log("Fetching jobs for company ID: ".concat(e)),[]}},23813:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>y});var r=a(95155),s=a(12115),n=a(35695),o=a(755),i=a(28018),c=a(30699),l=a(53884),d=a(31503),m=a(15259),p=a(22439),u=a(85329),g=a(21260),h=a(59852);function y(){let e=(0,n.useRouter)(),[t,a]=(0,s.useState)([]),[y,f]=(0,s.useState)(!1),[x,b]=(0,s.useState)({company:""}),[v,j]=(0,s.useState)(null),[A,w]=(0,s.useState)(""),[N,_]=(0,s.useState)(!1),[C,S]=(0,s.useState)([]),[k,F]=(0,s.useState)(!1),[E,I]=(0,s.useState)(null);(0,s.useEffect)(()=>{let e=async()=>{try{let e=await (0,g._N)();S(Array.isArray(e)?e:[])}catch(e){console.error("Error loading companies:",e),S([])}};(async()=>{F(!0);try{let e=await (0,u.Uq)(),t=Array.isArray(null==e?void 0:e.data)?e.data:[];a(t)}catch(e){console.error("Error loading forms:",e),a([])}finally{F(!1)}})(),e()},[]);let D=Array.isArray(C)?C.filter(e=>(e.name||e.companyName||e.employer_name||"").toLowerCase().includes(A.toLowerCase())):[],P=e=>{let t=e.name||e.companyName||e.employer_name;v?j({...v,company:t}):b({company:t}),w(t),_(!1)},z=e=>{w(e),v?j({...v,company:e}):b({company:e}),_(!0)},T=(e,t)=>{e.preventDefault();let a="".concat(window.location.origin,"/forms/").concat(t);navigator.clipboard.writeText(a),I(t),setTimeout(()=>I(null),2e3)},L=async()=>{try{if(!x.company||""===x.company.trim())return void alert("Please enter a company name");let e={company:x.company,submitted:!1,details:null},r=await (0,u.DG)(e);a([r.data,...Array.isArray(t)?t:[]]),b({company:""}),w(""),f(!1)}catch(e){console.error("Error creating form:",e),alert("Failed to create form. Please try again.")}},J=e=>{j(e),w(e.company),f(!0)},O=async()=>{try{if(!v.company||""===v.company.trim())return void alert("Please enter a company name");await (0,u.wi)(v.id,v),a((Array.isArray(t)?t:[]).map(e=>e.id===v.id?v:e)),j(null),w(""),f(!1)}catch(e){console.error("Error updating form:",e),alert("Failed to update form. Please try again.")}};return(0,r.jsxs)("div",{className:"flex min-h-screen bg-gray-50",children:[(0,r.jsx)(h.A,{}),(0,r.jsx)("div",{className:"flex-1 ml-64 p-6",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-semibold text-gray-900",children:"Forms Dashboard"}),(0,r.jsxs)("button",{onClick:()=>f(!0),className:"flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(o.A,{size:18})," Create Form"]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-6",children:"Available Forms"}),k?(0,r.jsx)("div",{className:"flex justify-center items-center h-40",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-10 w-10 border-b-2 border-blue-600"})}):0===t.length?(0,r.jsx)("p",{className:"text-gray-500 text-center py-10",children:"No forms created yet."}):(0,r.jsx)("div",{className:"space-y-4 max-h-[70vh] overflow-y-auto pr-2",children:Array.isArray(t)&&t.map(t=>(0,r.jsxs)("div",{className:"border-b border-gray-200 pb-4 mb-4 last:border-b-0 last:mb-0 last:pb-0",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-bold text-gray-900",children:t.company}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["Key: ",t.key]}),(0,r.jsxs)("p",{className:"text-xs text-gray-500",children:["Status: ",t.submitted?(0,r.jsx)("span",{className:"px-2 py-0.5 bg-green-100 text-green-800 rounded-full text-xs font-medium",children:"Completed"}):(0,r.jsx)("span",{className:"px-2 py-0.5 bg-gray-100 text-gray-800 rounded-full text-xs font-medium",children:"Not Submitted"})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)("a",{href:"/forms/".concat(t.id),className:"flex items-center gap-1 text-blue-600 hover:text-blue-800 text-sm font-medium",target:"_blank",rel:"noopener noreferrer",children:[(0,r.jsx)(i.A,{size:16}),"Open Form"]}),(0,r.jsx)("button",{onClick:e=>T(e,t.id),className:"p-1.5 rounded-md hover:bg-gray-100 text-gray-600 transition-colors",title:"Copy form link",children:E===t.id?(0,r.jsx)(c.A,{size:18,className:"text-green-500"}):(0,r.jsx)(l.A,{size:18})}),(0,r.jsx)("button",{onClick:()=>J(t),className:"p-1.5 rounded-md bg-blue-50 text-blue-600 hover:bg-blue-100",title:"Edit form",children:(0,r.jsx)(d.A,{size:18})})]})]}),t.submitted&&(0,r.jsx)("div",{className:"mt-2 text-right",children:(0,r.jsx)("button",{onClick:()=>e.push("/admin/form/edit/".concat(t.id)),className:"text-sm text-blue-600 hover:text-blue-800 hover:underline font-medium",children:"View Details"})})]},t.id))})]})]})}),y&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-30 flex justify-center items-center z-50",children:(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-lg w-[90%] max-w-md",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-blue-600 mb-4",children:v?"Edit Form":"Create New Form"}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Company Name"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(m.A,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsx)("input",{type:"text",value:A,onChange:e=>z(e.target.value),onFocus:()=>_(!0),placeholder:"Search for a company...",className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0}),(0,r.jsx)("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none",children:(0,r.jsx)(p.A,{size:18,className:"text-gray-400"})})]}),N&&A&&(0,r.jsx)("div",{className:"absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto",children:D.length>0?(0,r.jsx)("div",{className:"py-1",children:D.map((e,t)=>(0,r.jsx)("button",{type:"button",onClick:()=>P(e),className:"w-full text-left px-4 py-2 hover:bg-gray-50 focus:bg-gray-50 focus:outline-none border-b border-gray-100 last:border-b-0",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium text-gray-900",children:e.name||e.companyName||e.employer_name}),e.location&&(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.location})]}),e.totalActiveJobs>0&&(0,r.jsxs)("div",{className:"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full",children:[e.totalActiveJobs," jobs"]})]})},e.id||t))}):(0,r.jsxs)("div",{className:"px-4 py-3 text-sm text-gray-500 text-center",children:['No companies found matching "',A,'"']})}),N&&(0,r.jsx)("div",{className:"fixed inset-0 z-5",onClick:()=>_(!1)})]}),(0,r.jsxs)("div",{className:"flex justify-end gap-3 mt-6",children:[(0,r.jsx)("button",{onClick:()=>{f(!1),j(null),w(""),b({company:""})},className:"px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors",children:"Cancel"}),(0,r.jsx)("button",{onClick:v?O:L,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:v?"Save Changes":"Create"})]})]})})]})}},34563:(e,t,a)=>{Promise.resolve().then(a.bind(a,23813))},37719:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var r=a(23464);a(73983);let s=r.A.create({baseURL:"http://127.0.0.1:8000",headers:{"Content-Type":"application/json"}});s.interceptors.request.use(e=>{let t=localStorage.getItem("access_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),s.interceptors.response.use(e=>e,async e=>{var t;let a=e.config;if((null==(t=e.response)?void 0:t.status)===401&&!a._retry){a._retry=!0;try{let e=localStorage.getItem("refresh_token");if(e){let t=await r.A.post("http://127.0.0.1:8000/api/auth/token/refresh/",{refresh:e});return localStorage.setItem("access_token",t.data.access),a.headers.Authorization="Bearer ".concat(t.data.access),s(a)}}catch(e){console.error("Error refreshing token:",e),localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),window.location.href="/login"}}return Promise.reject(e)});let n=s},48937:(e,t,a)=>{"use strict";a.d(t,{C1:()=>o,Gu:()=>h,JT:()=>c,RC:()=>l,S0:()=>f,Y_:()=>m,bl:()=>p,dl:()=>y,eK:()=>i,fetchCompanies:()=>n,getCompanyStats:()=>d,jQ:()=>u,mm:()=>s,oY:()=>g});var r=a(37719);function s(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.page&&t.append("page",e.page),e.per_page&&t.append("per_page",e.per_page),e.tier&&"ALL"!==e.tier&&t.append("tier",e.tier),e.industry&&"ALL"!==e.industry&&t.append("industry",e.industry),e.campus_recruiting&&t.append("campus_recruiting",e.campus_recruiting),e.search&&t.append("search",e.search),e.sort&&t.append("sort",e.sort),t.append("_t",new Date().getTime());let a=t.toString(),s=["/api/v1/companies/".concat(a?"?".concat(a):""),"/api/v1/college/default-college/companies/".concat(a?"?".concat(a):"")];return r.A.get(s[0]).catch(e=>(console.log("Primary endpoint failed: ".concat(e.message,", trying fallback...")),r.A.get(s[1])))}async function n(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{console.log("Fetching companies from API...");let t=await s(e),a=[];if(t.data&&Array.isArray(t.data)?a=t.data:t.data&&t.data.results&&Array.isArray(t.data.results)?a=t.data.results:t.data&&t.data.data&&Array.isArray(t.data.data)&&(a=t.data.data),console.log("Retrieved ".concat(a.length," companies from API")),a.length>0){let e=await Promise.all(a.map(async e=>{try{let t=await o(e.id);return m(t.data)}catch(t){return console.log("Could not fetch details for company ".concat(e.id,":"),t),m(e)}}));return sessionStorage.setItem("companies_data",JSON.stringify(e)),sessionStorage.setItem("companies_timestamp",Date.now()),e}throw Error("No companies returned from API")}catch(t){console.error("Error fetching companies from API:",t);{let e=sessionStorage.getItem("companies_data"),t=sessionStorage.getItem("companies_timestamp");if(e&&t&&Date.now()-parseInt(t)<3e5)return console.log("Using cached company data (< 5 min old)"),JSON.parse(e)}console.log("Falling back to static company data");let{companies:e}=await a.e(1260).then(a.bind(a,21260));return e}}function o(e){let t=["/api/v1/company/".concat(e,"/"),"/api/v1/companies/".concat(e,"/"),"/api/v1/college/default-college/companies/".concat(e,"/")];return r.A.get(t[0]).catch(e=>(console.log("First company endpoint failed: ".concat(e.message,", trying second...")),r.A.get(t[1]).catch(e=>(console.log("Second company endpoint failed: ".concat(e.message,", trying third...")),r.A.get(t[2])))))}function i(e){let t=new FormData;return Object.keys(e).forEach(a=>{"logo"===a&&e[a]instanceof File?t.append(a,e[a]):null!==e[a]&&void 0!==e[a]&&t.append(a,e[a])}),r.A.post("/api/v1/companies/",t,{headers:{"Content-Type":"multipart/form-data"}})}function c(e,t){let a=new FormData;return Object.keys(t).forEach(e=>{"logo"===e&&t[e]instanceof File?a.append(e,t[e]):null!==t[e]&&void 0!==t[e]&&a.append(e,t[e])}),r.A.put("/api/v1/companies/".concat(e,"/"),a,{headers:{"Content-Type":"multipart/form-data"}})}function l(e){return r.A.delete("/api/v1/companies/".concat(e,"/"))}function d(){return r.A.get("/api/v1/companies/stats/")}function m(e){return{id:e.id,name:e.name,logo:e.logo||"https://via.placeholder.com/48x48/4285F4/FFFFFF?text=".concat(e.name.charAt(0)),description:e.description||"",industry:e.industry||"",size:e.size||"",founded:e.founded||"",location:e.location||"Location not specified",website:e.website||"",tier:e.tier||"Tier 3",campus_recruiting:e.campus_recruiting||!1,totalActiveJobs:e.total_active_jobs||0,totalApplicants:e.total_applicants||0,totalHired:e.total_hired||0,awaitedApproval:e.pending_approval||0}}function p(e){return r.A.get("/api/v1/companies/".concat(e,"/followers/count/"))}function u(e,t){return r.A.post("/api/v1/companies/".concat(e,"/followers/"),{user_id:t})}function g(e,t){return r.A.delete("/api/v1/companies/".concat(e,"/followers/"),{data:{user_id:t}})}function h(e,t){return r.A.get("/api/v1/companies/".concat(e,"/followers/status/?user_id=").concat(t))}function y(e){return r.A.get("/api/v1/users/".concat(e,"/following/"))}function f(){return r.A.get("/api/v1/companies/simple/").then(e=>{if(e.data&&e.data.success)return e.data;throw Error("Failed to fetch companies")}).catch(e=>(console.error("Error fetching companies:",e),{success:!1,data:[],count:0,error:e.message}))}},59852:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var r=a(95155),s=a(6874),n=a.n(s),o=a(35695),i=a(83406),c=a(72733);function l(){let e=(0,o.usePathname)(),t="/admin/form"===e?"dashboard":"/admin/form/review"===e?"review":"";return(0,r.jsxs)("div",{className:"w-64 bg-white h-full fixed left-30 top-28 pt-16 z-10 border-r border-gray-200",children:[(0,r.jsx)("div",{className:"p-4 border-b border-gray-200",children:(0,r.jsx)("h2",{className:"text-xl font-semibold text-blue-600",children:"Forms Management"})}),(0,r.jsx)("nav",{className:"p-4",children:(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsxs)(n(),{href:"/admin/form",className:"flex items-center gap-3 p-3 rounded-lg transition-colors ".concat("dashboard"===t?"bg-blue-50 text-blue-600":"text-gray-700 hover:bg-gray-50"),children:[(0,r.jsx)(i.A,{size:20}),(0,r.jsx)("span",{className:"font-medium",children:"Forms Dashboard"})]})}),(0,r.jsx)("li",{children:(0,r.jsxs)(n(),{href:"/admin/form/review",className:"flex items-center gap-3 p-3 rounded-lg transition-colors ".concat("review"===t?"bg-blue-50 text-blue-600":"text-gray-700 hover:bg-gray-50"),children:[(0,r.jsx)(c.A,{size:20}),(0,r.jsx)("span",{className:"font-medium",children:"Review to Post"})]})})]})})]})}},85329:(e,t,a)=>{"use strict";a.d(t,{DG:()=>n,Jy:()=>o,Uq:()=>s,i6:()=>c,wi:()=>i});var r=a(37719);function s(){return r.A.get("/api/v1/jobs/forms/")}function n(e){return r.A.post("/api/v1/jobs/forms/",e)}function o(e){return r.A.get("/api/v1/jobs/forms/".concat(e,"/"))}function i(e,t){return r.A.patch("/api/v1/jobs/forms/".concat(e,"/"),t)}function c(e){return r.A.post("/api/v1/jobs/forms/".concat(e,"/delete/"))}}},e=>{var t=t=>e(e.s=t);e.O(0,[3464,6874,1682,3983,8441,1684,7358],()=>t(34563)),_N_E=e.O()}]);