(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6073],{755:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var s=(0,a(86467).A)("outline","plus","IconPlus",[["path",{d:"M12 5l0 14",key:"svg-0"}],["path",{d:"M5 12l14 0",key:"svg-1"}]])},1243:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},19946:(e,t,a)=>{"use strict";a.d(t,{A:()=>p});var s=a(12115);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,a)=>a?a.toUpperCase():t.toLowerCase()),n=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return t.filter((e,t,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===t).join(" ").trim()},o=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,s.forwardRef)((e,t)=>{let{color:a="currentColor",size:r=24,strokeWidth:l=2,absoluteStrokeWidth:n,className:c="",children:p,iconNode:m,...u}=e;return(0,s.createElement)("svg",{ref:t,...d,width:r,height:r,stroke:a,strokeWidth:n?24*Number(l)/Number(r):l,className:i("lucide",c),...!p&&!o(u)&&{"aria-hidden":"true"},...u},[...m.map(e=>{let[t,a]=e;return(0,s.createElement)(t,a)}),...Array.isArray(p)?p:[p]])}),p=(e,t)=>{let a=(0,s.forwardRef)((a,l)=>{let{className:o,...d}=a;return(0,s.createElement)(c,{ref:l,iconNode:t,className:i("lucide-".concat(r(n(e))),"lucide-".concat(e),o),...d})});return a.displayName=n(e),a}},22439:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var s=(0,a(86467).A)("outline","search","IconSearch",[["path",{d:"M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0",key:"svg-0"}],["path",{d:"M21 21l-6 -6",key:"svg-1"}]])},29869:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},33786:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},34842:(e,t,a)=>{"use strict";a.d(t,{G$:()=>i,N6:()=>r,Om:()=>c,T4:()=>p,YQ:()=>n,_S:()=>o,lh:()=>d,vr:()=>l});var s=a(37719);function r(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.page&&t.append("page",e.page),e.per_page&&t.append("per_page",e.per_page),e.job_type&&"ALL"!==e.job_type&&t.append("job_type",e.job_type),e.location&&"ALL"!==e.location&&t.append("location",e.location),e.salary_min&&t.append("salary_min",e.salary_min),e.search&&t.append("search",e.search);let a=t.toString();return s.A.get("/api/v1/college/default-college/jobs/".concat(a?"?".concat(a):""))}function l(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!Object.values(a).some(e=>e instanceof File))return s.A.post("/api/v1/college/default-college/jobs/".concat(e,"/apply/"),{cover_letter:t,additional_field_responses:a});{let r=new FormData;return r.append("cover_letter",t),Object.entries(a).forEach(e=>{let[t,a]=e;a instanceof File?r.append(t,a):r.append(t,JSON.stringify(a))}),s.A.post("/api/v1/college/default-college/jobs/".concat(e,"/apply/"),r,{headers:{"Content-Type":"multipart/form-data"}})}}function n(e){return s.A.get("/api/v1/college/default-college/jobs/".concat(e,"/"))}function i(){return s.A.get("/api/v1/college/default-college/jobs/applied/")}function o(e){return s.A.post("/api/v1/college/default-college/jobs/create/",e)}function d(e,t){return s.A.put("/api/v1/college/default-college/jobs/".concat(e,"/"),t)}function c(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.page&&t.append("page",e.page),e.per_page&&t.append("per_page",e.per_page),e.search&&t.append("search",e.search),e.type&&"All"!==e.type&&t.append("job_type",e.type),e.minCTC&&t.append("salary_min",e.minCTC),e.maxCTC&&t.append("salary_max",e.maxCTC),e.minStipend&&t.append("stipend_min",e.minStipend),e.maxStipend&&t.append("stipend_max",e.maxStipend),e.location&&t.append("location",e.location),void 0!==e.is_published&&t.append("is_published",e.is_published),e.company_id&&t.append("company_id",e.company_id),e.company_name&&t.append("company_name",e.company_name);let a=t.toString(),r="/api/v1/college/default-college/jobs/admin/".concat(a?"?".concat(a):"");return console.log("\uD83C\uDF10 listJobsAdmin calling URL:",r,"with params:",e),s.A.get(r).then(e=>{var t,a,s,r,l,n;return console.log("\uD83C\uDF10 listJobsAdmin response:",{status:e.status,totalJobs:(null==(a=e.data)||null==(t=a.pagination)?void 0:t.total_count)||0,currentPage:(null==(r=e.data)||null==(s=r.pagination)?void 0:s.current_page)||1,totalPages:(null==(n=e.data)||null==(l=n.pagination)?void 0:l.total_pages)||1}),e}).catch(e=>{var t;throw console.error("\uD83C\uDF10 listJobsAdmin error:",e),console.error("\uD83C\uDF10 listJobsAdmin error response:",null==(t=e.response)?void 0:t.data),e})}function p(e){return s.A.patch("/api/v1/jobs/".concat(e,"/toggle-publish/"))}},35695:(e,t,a)=>{"use strict";var s=a(18999);a.o(s,"useParams")&&a.d(t,{useParams:function(){return s.useParams}}),a.o(s,"usePathname")&&a.d(t,{usePathname:function(){return s.usePathname}}),a.o(s,"useRouter")&&a.d(t,{useRouter:function(){return s.useRouter}}),a.o(s,"useSearchParams")&&a.d(t,{useSearchParams:function(){return s.useSearchParams}})},37719:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var s=a(23464);a(73983);let r=s.A.create({baseURL:"http://127.0.0.1:8000",headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>{let t=localStorage.getItem("access_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,async e=>{var t;let a=e.config;if((null==(t=e.response)?void 0:t.status)===401&&!a._retry){a._retry=!0;try{let e=localStorage.getItem("refresh_token");if(e){let t=await s.A.post("http://127.0.0.1:8000/api/auth/token/refresh/",{refresh:e});return localStorage.setItem("access_token",t.data.access),a.headers.Authorization="Bearer ".concat(t.data.access),r(a)}}catch(e){console.error("Error refreshing token:",e),localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),window.location.href="/login"}}return Promise.reject(e)});let l=r},37758:(e,t,a)=>{Promise.resolve().then(a.bind(a,66293))},40646:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},54416:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},66293:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>p});var s=a(95155),r=a(12115),l=a(35695),n=a(755),i=a(22439),o=a(34842);function d(e){let{jobs:t=[],onTogglePublish:a}=e;return 0===t.length?(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center py-16 text-center",children:[(0,s.jsx)("div",{className:"text-gray-400 mb-4",children:(0,s.jsx)("svg",{className:"w-16 h-16 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6M8 8v10m8-10v10"})})}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-500 mb-2",children:"No job listings found"}),(0,s.jsx)("p",{className:"text-sm text-gray-400",children:"Job listings will appear here once they are created"})]}):(0,s.jsx)("div",{className:"overflow-auto",children:(0,s.jsxs)("table",{className:"min-w-full text-sm text-left border-collapse",children:[(0,s.jsx)("thead",{className:"bg-gray-50 text-gray-700 sticky top-0",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-4 py-3 font-medium",children:"Company"}),(0,s.jsx)("th",{className:"px-4 py-3 font-medium",children:"Role"}),(0,s.jsx)("th",{className:"px-4 py-3 font-medium",children:"Type"}),(0,s.jsx)("th",{className:"px-4 py-3 font-medium",children:"CTC"}),(0,s.jsx)("th",{className:"px-4 py-3 font-medium",children:"Stipend"}),(0,s.jsx)("th",{className:"px-4 py-3 font-medium",children:"Deadline"}),(0,s.jsx)("th",{className:"px-4 py-3 font-medium",children:"Status"}),a&&(0,s.jsx)("th",{className:"px-4 py-3 font-medium",children:"Actions"})]})}),(0,s.jsx)("tbody",{className:"divide-y divide-gray-200",children:t.map((e,t)=>(0,s.jsxs)("tr",{className:"hover:bg-gray-50 transition-colors",children:[(0,s.jsx)("td",{className:"px-4 py-4 font-medium text-gray-900",children:e.companyName}),(0,s.jsx)("td",{className:"px-4 py-4 text-gray-700",children:e.title}),(0,s.jsx)("td",{className:"px-4 py-4",children:(0,s.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-medium rounded-full ".concat("Full-Time"===e.type?"bg-green-100 text-green-800":"Internship"===e.type?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800"),children:e.type})}),(0,s.jsx)("td",{className:"px-4 py-4 text-gray-700",children:e.ctc?"".concat(e.ctc," LPA"):"-"}),(0,s.jsx)("td",{className:"px-4 py-4 text-gray-700",children:e.stipend?"".concat(e.stipend," INR "):"-"}),(0,s.jsx)("td",{className:"px-4 py-4 text-gray-700",children:e.deadline||e.application_deadline}),(0,s.jsx)("td",{className:"px-4 py-4",children:(0,s.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-medium rounded-full ".concat(e.is_published?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"),children:e.is_published?"Published":"To be Published"})}),a&&(0,s.jsx)("td",{className:"px-4 py-4",children:(0,s.jsx)("button",{onClick:()=>a(e.id,e.is_published),className:"px-3 py-1 text-xs font-medium rounded-md transition-colors ".concat(e.is_published?"bg-red-100 text-red-700 hover:bg-red-200":"bg-green-100 text-green-700 hover:bg-green-200"),children:e.is_published?"Unpublish":"Publish"})})]},t))})]})})}function c(e){let{typeFilter:t,setTypeFilter:a,minCTC:r,setMinCTC:l,maxCTC:n,setMaxCTC:i,minStipend:o,setMinStipend:d,maxStipend:c,setMaxStipend:p,deadlineFilter:m,setDeadlineFilter:u}=e;return(0,s.jsxs)("div",{className:"flex flex-wrap items-center gap-3",children:[(0,s.jsxs)("select",{value:t,onChange:e=>a(e.target.value),className:"w-[100px] px-2 py-2 text-sm rounded-md border border-gray-300 bg-gray-100",children:[(0,s.jsx)("option",{value:"All",children:"All"}),(0,s.jsx)("option",{value:"Full-Time",children:"Full-Time"}),(0,s.jsx)("option",{value:"Internship",children:"Intern"})]}),(0,s.jsx)("input",{type:"number",placeholder:"Min CTC",value:r,onChange:e=>l(e.target.value),className:"w-[90px] px-2 py-2 text-sm rounded-md border border-gray-300 bg-gray-100"}),(0,s.jsx)("input",{type:"number",placeholder:"Max CTC",value:n,onChange:e=>i(e.target.value),className:"w-[90px] px-2 py-2 text-sm rounded-md border border-gray-300 bg-gray-100"}),(0,s.jsx)("input",{type:"number",placeholder:"Min Stipend",value:o,onChange:e=>d(e.target.value),className:"w-[120px] px-2 py-2 text-sm rounded-md border border-gray-300 bg-gray-100"}),(0,s.jsx)("input",{type:"number",placeholder:"Max Stipend",value:c,onChange:e=>p(e.target.value),className:"w-[120px] px-2 py-2 text-sm rounded-md border border-gray-300 bg-gray-100"}),(0,s.jsx)("input",{type:"date",value:m,onChange:e=>u(e.target.value),className:"w-[140px] px-2 py-2 text-sm rounded-md border border-gray-300 bg-gray-100"}),(0,s.jsx)("button",{onClick:()=>{a("All"),l(""),i(""),d(""),p(""),u("")},className:"px-3 py-2 text-sm font-medium bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors flex-shrink-0",children:"Reset"})]})}function p(){let e=(0,l.useRouter)(),t=(0,l.useSearchParams)(),[a,p]=(0,r.useState)([]),[m,u]=(0,r.useState)(!0),[h,x]=(0,r.useState)(null),[g,y]=(0,r.useState)(1),[b,v]=(0,r.useState)(0),[j,f]=(0,r.useState)(0),[N,_]=(0,r.useState)(0),[w,A]=(0,r.useState)(0),k=Number(t.get("page"))||1,C=t.get("search")||"",S=t.get("type")||"All",M=t.get("minCTC")||"",P=t.get("maxCTC")||"",T=t.get("minStipend")||"",L=t.get("maxStipend")||"",I=t.get("deadline")||"",R=t=>{let{page:a=k,search:s=C,type:r=S,min_ctc:l=M,max_ctc:n=P,min_stipend:i=T,max_stipend:o=L,deadline:d=I}=t,c=new URLSearchParams;a>1&&c.set("page",a),s&&c.set("search",s),"All"!==r&&c.set("type",r),l&&c.set("minCTC",l),n&&c.set("maxCTC",n),i&&c.set("minStipend",i),o&&c.set("maxStipend",o),d&&c.set("deadline",d),e.push("/admin/jobs/listings".concat(c.toString()?"?".concat(c.toString()):""))};(0,r.useEffect)(()=>{(async()=>{u(!0);try{var e,t;console.log("\uD83D\uDD0D Admin listings: Fetching jobs with filters",{page:k,search:C,type:S,minCTC:M,maxCTC:P,minStipend:T,maxStipend:L,deadline:I});let a=((null==(e=(await (0,o.Om)({per_page:1,page:1,search:C,job_type:"All"!==S?S:void 0,salary_min:M||void 0,salary_max:P||void 0,stipend_min:T||void 0,stipend_max:L||void 0,deadline:I||void 0})).data)?void 0:e.pagination)||{}).total_count||0;v(a),y(Math.ceil(a/20));let s=await (0,o.Om)({per_page:20,page:k,search:C,job_type:"All"!==S?S:void 0,salary_min:M||void 0,salary_max:P||void 0,stipend_min:T||void 0,stipend_max:L||void 0,deadline:I||void 0}),r=(Array.isArray(null==(t=s.data)?void 0:t.data)?s.data.data:Array.isArray(s.data)?s.data:[]).map(e=>({...e,companyName:e.company_name,title:e.title,type:e.job_type,ctc:e.salary_max||0,stipend:"INTERNSHIP"===e.job_type&&e.salary_max||0,deadline:e.application_deadline}));f(r.filter(e=>e.is_published).length),_(r.filter(e=>!e.is_published).length),A(r.filter(e=>"INTERNSHIP"===e.job_type).length),p(r),x(null)}catch(e){console.error("\uD83D\uDEA8 Admin listings: Failed to fetch jobs:",e),x("Failed to load job listings. Please try again."),p([])}finally{u(!1)}})()},[k,C,S,M,P,T,L,I]);let E=async(e,t)=>{try{var a;await (0,o.T4)(e);let s=await (0,o.Om)({per_page:20,page:k,search:C,job_type:"All"!==S?S:void 0,salary_min:M||void 0,salary_max:P||void 0,stipend_min:T||void 0,stipend_max:L||void 0,deadline:I||void 0}),r=(Array.isArray(null==(a=s.data)?void 0:a.data)?s.data.data:Array.isArray(s.data)?s.data:[]).map(e=>({...e,companyName:e.company_name,title:e.title,type:e.job_type,ctc:e.salary_max||0,stipend:"INTERNSHIP"===e.job_type&&e.salary_max||0,deadline:e.application_deadline}));p(r),alert("Job ".concat(t?"unpublished":"published"," successfully!"))}catch(e){console.error("Error toggling job publish status:",e),alert("Failed to update job status. Please try again.")}};return(0,s.jsx)("div",{className:"p-6",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Job Listings"}),(0,s.jsxs)("p",{className:"text-gray-600",children:["Showing ",a.length," jobs (Page ",k," of ",g,", Total: ",b," jobs)"]})]}),(0,s.jsxs)("div",{className:"flex gap-3",children:[(0,s.jsx)("button",{onClick:()=>e.push("/admin/jobs/companies"),className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700",children:"View Companies"}),(0,s.jsxs)("button",{onClick:()=>e.push("/admin/jobs/create"),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2",children:[(0,s.jsx)(n.A,{size:18}),"Post New Job"]})]})]}),(0,s.jsxs)("div",{className:"mt-6 grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,s.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:b}),(0,s.jsx)("div",{className:"text-sm text-blue-700",children:"Total Jobs"})]}),(0,s.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-green-600",children:j}),(0,s.jsx)("div",{className:"text-sm text-green-700",children:"Published"})]}),(0,s.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:N}),(0,s.jsx)("div",{className:"text-sm text-yellow-700",children:"To be Published"})]}),(0,s.jsxs)("div",{className:"bg-purple-50 p-4 rounded-lg",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:w}),(0,s.jsx)("div",{className:"text-sm text-purple-700",children:"Internships"})]})]}),(0,s.jsxs)("div",{className:"mb-6 space-y-4",children:[(0,s.jsxs)("div",{className:"relative max-w-md",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(i.A,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{type:"text",placeholder:"Search jobs...",value:C,onChange:e=>R({search:e.target.value,page:1}),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsx)(c,{typeFilter:S,setTypeFilter:e=>R({type:e,page:1}),minCTC:M,setMinCTC:e=>R({min_ctc:e,page:1}),maxCTC:P,setMaxCTC:e=>R({max_ctc:e,page:1}),minStipend:T,setMinStipend:e=>R({min_stipend:e,page:1}),maxStipend:L,setMaxStipend:e=>R({max_stipend:e,page:1}),deadlineFilter:I,setDeadlineFilter:e=>R({deadline:e,page:1})})]}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:0===a.length?(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("p",{className:"text-gray-500 text-lg",children:"No job listings found"}),(0,s.jsx)("p",{className:"text-gray-400 mb-4",children:"Try adjusting your search criteria or create a new job posting"}),(0,s.jsx)("button",{onClick:()=>e.push("/admin/jobs/create"),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Post New Job"})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d,{jobs:a,onTogglePublish:E}),(0,s.jsxs)("div",{className:"flex items-center justify-between px-4 py-3 border-t border-gray-200",children:[(0,s.jsxs)("div",{className:"flex-1 flex justify-between sm:hidden",children:[(0,s.jsx)("button",{onClick:()=>R({page:Math.max(k-1,1)}),disabled:1===k,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ".concat(1===k?"bg-gray-100 text-gray-400":"bg-white text-gray-700 hover:bg-gray-50"),children:"Previous"}),(0,s.jsx)("button",{onClick:()=>R({page:Math.min(k+1,g)}),disabled:k===g,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ".concat(k===g?"bg-gray-100 text-gray-400":"bg-white text-gray-700 hover:bg-gray-50"),children:"Next"})]}),(0,s.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,s.jsx)("div",{children:(0,s.jsxs)("p",{className:"text-sm text-gray-700",children:["Showing page ",(0,s.jsx)("span",{className:"font-medium",children:k})," of"," ",(0,s.jsx)("span",{className:"font-medium",children:g})," (",b," total jobs)"]})}),(0,s.jsx)("div",{children:(0,s.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,s.jsx)("button",{onClick:()=>R({page:Math.max(k-1,1)}),disabled:1===k,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ".concat(1===k?"text-gray-300":"text-gray-500 hover:bg-gray-50"),children:"Previous"}),[...Array(Math.min(5,g))].map((e,t)=>{let a;return a=g<=5||k<=3?t+1:k>=g-2?g-4+t:k-2+t,(0,s.jsx)("button",{onClick:()=>R({page:a}),className:"relative inline-flex items-center px-4 py-2 border text-sm font-medium ".concat(k===a?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-500 hover:bg-gray-50"),children:a},a)}),(0,s.jsx)("button",{onClick:()=>R({page:Math.min(k+1,g)}),disabled:k===g,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ".concat(k===g?"text-gray-300":"text-gray-500 hover:bg-gray-50"),children:"Next"})]})})]})]})]})})]})})}},71007:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},75525:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},76517:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]])},81284:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},85339:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},86467:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var s=a(12115),r={outline:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"},filled:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",stroke:"none"}};let l=(e,t,a,l)=>{let n=(0,s.forwardRef)((a,n)=>{let{color:i="currentColor",size:o=24,stroke:d=2,title:c,className:p,children:m,...u}=a;return(0,s.createElement)("svg",{ref:n,...r[e],width:o,height:o,className:["tabler-icon","tabler-icon-".concat(t),p].join(" "),..."filled"===e?{fill:i}:{strokeWidth:d,stroke:i},...u},[c&&(0,s.createElement)("title",{key:"svg-title"},c),...l.map(e=>{let[t,a]=e;return(0,s.createElement)(t,a)}),...Array.isArray(m)?m:[m]])});return n.displayName="".concat(a),n}}},e=>{var t=t=>e(e.s=t);e.O(0,[3464,3983,8441,1684,7358],()=>t(37758)),_N_E=e.O()}]);