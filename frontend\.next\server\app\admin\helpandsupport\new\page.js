(()=>{var e={};e.id=3381,e.ids=[3381],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13353:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var s=r(60687),a=r(43210),n=r(85814),o=r.n(n),i=r(97299),l=r(76717),d=r(47033),u=r(16189),c=r(28885);let p=()=>{let[e,t]=(0,a.useState)(""),[r,n]=(0,a.useState)(""),[o,i]=(0,a.useState)("technical"),[d,p]=(0,a.useState)("medium"),[h,m]=(0,a.useState)(!1),[x,v]=(0,a.useState)(null),g=(0,u.useRouter)(),b=async t=>{t.preventDefault(),m(!0),v(null);try{let t=await c.yp.createTicket({title:e,description:r,category:o,priority:d});console.log("Ticket created:",t),g.push("/admin/helpandsupport")}catch(e){console.error("Error creating ticket:",e),v(e.response?.data?.message||"Failed to create ticket. Please try again.")}finally{m(!1)}};return(0,s.jsxs)("form",{onSubmit:b,className:"space-y-6",children:[x&&(0,s.jsx)("div",{className:"p-3 bg-red-100 border border-red-300 text-red-700 rounded-md",children:x}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"title",className:"block text-sm font-medium text-gray-700 mb-2",children:"Title *"}),(0,s.jsx)("input",{type:"text",id:"title",value:e,onChange:e=>t(e.target.value),required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Brief description of the issue"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"category",className:"block text-sm font-medium text-gray-700 mb-2",children:"Category"}),(0,s.jsxs)("select",{id:"category",value:o,onChange:e=>i(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,s.jsx)("option",{value:"technical",children:"Technical Issue"}),(0,s.jsx)("option",{value:"bug",children:"Bug Report"}),(0,s.jsx)("option",{value:"feature",children:"Feature Request"}),(0,s.jsx)("option",{value:"account",children:"Account Issue"}),(0,s.jsx)("option",{value:"general",children:"General Question"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"priority",className:"block text-sm font-medium text-gray-700 mb-2",children:"Priority"}),(0,s.jsxs)("select",{id:"priority",value:d,onChange:e=>p(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,s.jsx)("option",{value:"low",children:"Low"}),(0,s.jsx)("option",{value:"medium",children:"Medium"}),(0,s.jsx)("option",{value:"high",children:"High"}),(0,s.jsx)("option",{value:"urgent",children:"Urgent"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-2",children:"Description *"}),(0,s.jsx)("textarea",{id:"description",value:r,onChange:e=>n(e.target.value),required:!0,rows:6,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Please provide detailed information about your issue or request"})]}),(0,s.jsxs)("div",{className:"flex gap-3",children:[(0,s.jsx)(l.$,{type:"submit",disabled:h,className:"bg-blue-600 hover:bg-blue-700 text-white",children:h?"Creating...":"Create Ticket"}),(0,s.jsx)(l.$,{type:"button",variant:"outline",onClick:()=>g.push("/admin/helpandsupport"),children:"Cancel"})]})]})},h=()=>(0,s.jsxs)("div",{className:"space-y-6 pb-8",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(l.$,{variant:"outline",size:"icon",asChild:!0,children:(0,s.jsx)(o(),{href:"/admin/helpandsupport",children:(0,s.jsx)(d.A,{className:"h-4 w-4"})})}),(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Create a New Ticket"})]}),(0,s.jsxs)(i.Zp,{children:[(0,s.jsxs)(i.aR,{children:[(0,s.jsx)(i.ZB,{children:"Support Ticket Request"}),(0,s.jsx)(i.BT,{children:"Fill out the form below to submit a new support ticket."})]}),(0,s.jsx)(i.Wu,{children:(0,s.jsx)(p,{})})]})]})},15207:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\helpandsupport\\\\new\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\helpandsupport\\new\\page.jsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20798:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("megaphone",[["path",{d:"m3 11 18-5v12L3 14v-3z",key:"n962bs"}],["path",{d:"M11.6 16.8a3 3 0 1 1-5.8-1.6",key:"1yl0tm"}]])},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37325:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var s=(0,r(6445).A)("outline","clipboard-list","IconClipboardList",[["path",{d:"M9 5h-2a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-12a2 2 0 0 0 -2 -2h-2",key:"svg-0"}],["path",{d:"M9 3m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z",key:"svg-1"}],["path",{d:"M9 12l.01 0",key:"svg-2"}],["path",{d:"M13 12l2 0",key:"svg-3"}],["path",{d:"M9 16l.01 0",key:"svg-4"}],["path",{d:"M13 16l2 0",key:"svg-5"}]])},47033:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},53411:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67774:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>p,tree:()=>d});var s=r(65239),a=r(48088),n=r(88170),o=r.n(n),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d={children:["",{children:["admin",{children:["helpandsupport",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,15207)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\helpandsupport\\new\\page.jsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,23697)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\layout.jsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,75535)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\layout.js"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\helpandsupport\\new\\page.jsx"],c={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/helpandsupport/new/page",pathname:"/admin/helpandsupport/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},72508:(e,t,r)=>{Promise.resolve().then(r.bind(r,13353))},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81172:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var s=(0,r(6445).A)("outline","help","IconHelp",[["path",{d:"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0",key:"svg-0"}],["path",{d:"M12 17l0 .01",key:"svg-1"}],["path",{d:"M12 13.5a1.5 1.5 0 0 1 1 -1.5a2.6 2.6 0 1 0 -3 -4",key:"svg-2"}]])},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86076:(e,t,r)=>{Promise.resolve().then(r.bind(r,15207))},94735:e=>{"use strict";e.exports=require("events")},95994:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var s=(0,r(6445).A)("outline","forms","IconForms",[["path",{d:"M12 3a3 3 0 0 0 -3 3v12a3 3 0 0 0 3 3",key:"svg-0"}],["path",{d:"M6 3a3 3 0 0 1 3 3v12a3 3 0 0 1 -3 3",key:"svg-1"}],["path",{d:"M13 7h7a1 1 0 0 1 1 1v8a1 1 0 0 1 -1 1h-7",key:"svg-2"}],["path",{d:"M5 7h-1a1 1 0 0 0 -1 1v8a1 1 0 0 0 1 1h1",key:"svg-3"}],["path",{d:"M17 12h.01",key:"svg-4"}],["path",{d:"M13 12h.01",key:"svg-5"}]])}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,681,1658,1060,2305,6728],()=>r(67774));module.exports=s})();