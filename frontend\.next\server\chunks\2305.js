exports.id=2305,exports.ids=[2305],exports.modules={24664:(e,t,s)=>{"use strict";s.d(t,{A:()=>b});var r=s(60687),i=s(43210),o=s.n(i),l=s(26001),a=s(88920);s(16189);var n=s(85814),c=s.n(n),d=s(58843),m=s(86206),u=s(49384),h=s(82348);function p(...e){return(0,h.QP)((0,u.$)(e))}let x=(0,i.createContext)();function b({sections:e=[],bottomItems:t=[],defaultExpanded:s=!1,navbarHeight:n="4rem",className:u}){let[h,b]=(0,i.useState)(s),[g,f]=(0,i.useState)(!1),[y,j]=(0,i.useState)(!1),v=e=>o().isValidElement(e)?o().cloneElement(e,{className:"w-7 h-7",size:void 0}):e;return(0,r.jsxs)(r.Fragment,{children:[g&&(0,r.jsx)("button",{onClick:()=>j(!y),className:"fixed top-4 left-4 z-50 p-2 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow md:hidden",style:{marginTop:n},children:y?(0,r.jsx)(d.A,{size:24}):(0,r.jsx)(m.A,{size:24})}),(0,r.jsx)(x.Provider,{value:{expanded:h},children:(0,r.jsxs)(l.P.div,{animate:{width:h?"300px":"80px"},onMouseEnter:()=>b(!0),onMouseLeave:()=>b(!1),className:p("fixed top-0 left-0 h-screen bg-white px-4 py-4 shadow-lg rounded-r-3xl flex flex-col justify-between sidebar",g&&!y?"hidden":"",u),style:{marginTop:n},children:[(0,r.jsxs)("nav",{className:"flex flex-col gap-8",children:[(0,r.jsxs)(c(),{href:"/",className:p("flex items-center gap-4 p-3 text-black",!h&&"justify-center"),children:[(0,r.jsx)("div",{className:"flex-shrink-0 text-2xl font-bold",children:h?null:"P"}),h&&(0,r.jsx)(l.P.span,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"text-2xl font-bold whitespace-nowrap",children:"Placeeasy.in"})]}),e.map((e,t)=>(0,r.jsx)("div",{className:"bg-gray-50 rounded-xl p-2",children:(0,r.jsx)(a.N,{children:e.items.map(e=>(0,r.jsxs)(c(),{href:e.href,className:p("flex items-center gap-4 p-3 text-gray-600 hover:bg-blue-50 hover:text-blue-600 rounded-xl transition-colors",!h&&"justify-center"),children:[(0,r.jsx)("div",{className:"flex-shrink-0 flex items-center justify-center",children:v(e.icon)}),h&&(0,r.jsx)(l.P.span,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"text-base font-bold whitespace-nowrap",children:e.title})]},e.title))})},t))]}),t.length>0&&(0,r.jsx)("div",{className:"mt-6",children:t.map(e=>(0,r.jsxs)(c(),{href:e.href,className:p("flex items-center gap-4 p-3 text-gray-600 hover:bg-blue-50 hover:text-blue-600 rounded-xl transition-colors mb-2",!h&&"justify-center"),children:[(0,r.jsx)("div",{className:"flex-shrink-0 flex items-center justify-center",children:v(e.icon)}),h&&(0,r.jsx)(l.P.span,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"text-base font-bold whitespace-nowrap",children:e.title})]},e.title))})]})})]})}},27215:(e,t,s)=>{Promise.resolve().then(s.bind(s,75535))},27348:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>E});var r=s(60687);s(44263);var i=s(43210),o=s(16189),l=s(24664),a=s(80556),n=s(90910),c=s(25596),d=s(84578),m=s(21134),u=s(363),h=s(34318);let p=()=>{let{theme:e,changeTheme:t,isDark:s}=(0,d.D)(),[o,l]=(0,i.useState)(!1),a=(0,i.useRef)(null),n=[{value:"light",label:"Light",icon:m.A},{value:"dark",label:"Dark",icon:u.A},{value:"system",label:"System",icon:h.A}],c=n.find(t=>t.value===e);(0,i.useEffect)(()=>{let e=e=>{a.current&&!a.current.contains(e.target)&&l(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let p=e=>{t(e),l(!1)};return(0,r.jsxs)("div",{className:"relative",ref:a,children:[(0,r.jsx)("button",{onClick:()=>l(!o),className:`
          p-2 rounded-lg border transition-colors duration-200
          ${s?"bg-gray-800 border-gray-600 text-gray-200 hover:bg-gray-700":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50"}
          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1
        `,title:`Current theme: ${c?.label}`,children:c&&(0,r.jsx)(c.icon,{className:"w-5 h-5"})}),o&&(0,r.jsx)("div",{className:`
          absolute right-0 mt-2 w-32 rounded-lg shadow-lg border z-50
          ${s?"bg-gray-800 border-gray-600":"bg-white border-gray-200"}
        `,children:(0,r.jsx)("div",{className:"py-1",children:n.map(t=>{let i=t.icon,o=e===t.value;return(0,r.jsxs)("button",{onClick:()=>p(t.value),className:`
                    w-full px-3 py-2 text-left text-sm flex items-center gap-2 transition-colors duration-150
                    ${o?s?"bg-gray-700 text-blue-400":"bg-blue-50 text-blue-600":s?"text-gray-200 hover:bg-gray-700":"text-gray-700 hover:bg-gray-50"}
                  `,children:[(0,r.jsx)(i,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:t.label}),o&&(0,r.jsx)("span",{className:"ml-auto text-xs",children:"✓"})]},t.value)})})})]})};function x(){let[e,t]=(0,i.useState)(!1),[s,l]=(0,i.useState)(null),d=(0,i.useRef)(null),m=(0,o.useRouter)(),[u,h]=(0,i.useState)(null),x=[{title:"Profile",icon:a.A,hasSubmenu:!0,submenu:[{title:"My Profile",href:"ADMIN"===u?"/admin/profile":"/profile"},{title:"Edit Profile",href:"/profile/edit"},{title:"Account Settings",href:"/profile/settings"},{title:"Privacy Settings",href:"/profile/privacy"}]},{title:"Settings",icon:n.A,hasSubmenu:!0,submenu:[{title:"Theme",component:"theme"},{title:"Notification Preferences",href:"/settings/notifications"},{title:"Language",href:"/settings/language"},{title:"Data & Privacy",href:"/settings/privacy"}]},{title:"Help Center",href:"/help"},{title:"Terms of Service",href:"/terms"}],b=e=>{l(s===e?null:e)};return(0,r.jsxs)("div",{className:"relative flex items-center gap-3",ref:d,children:[(0,r.jsx)("span",{className:"text-black font-medium",children:"Student Career Center"}),(0,r.jsx)("button",{onClick:()=>t(!e),className:"text-blue-600 hover:text-blue-700 p-2 rounded-full hover:bg-blue-50 transition-colors",children:(0,r.jsx)(a.A,{size:24})}),e&&(0,r.jsxs)("div",{className:"absolute right-0 top-full mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50",children:[x.map((e,t)=>(0,r.jsx)("div",{className:"relative",children:e.hasSubmenu?(0,r.jsxs)("div",{children:[(0,r.jsxs)("button",{onClick:()=>b(t),className:"w-full flex items-center justify-between px-4 py-2 text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[e.icon&&(0,r.jsx)(e.icon,{size:18}),(0,r.jsx)("span",{children:e.title})]}),(0,r.jsx)(c.A,{size:16,className:`transform transition-transform duration-200 ${s===t?"rotate-90":""}`})]}),s===t&&(0,r.jsx)("div",{className:"ml-4 border-l border-gray-200 pl-4 py-2",children:e.submenu.map((e,t)=>(0,r.jsx)("div",{children:"theme"===e.component?(0,r.jsx)("div",{className:"px-2 py-2",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Theme"}),(0,r.jsx)(p,{})]})}):(0,r.jsx)("a",{href:e.href,className:"block px-2 py-2 text-sm text-gray-600 hover:text-blue-600 hover:bg-blue-25 rounded transition-all duration-200",children:e.title})},t))})]}):(0,r.jsx)("a",{href:e.href,className:"block px-4 py-2 text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200",children:e.title})},t)),(0,r.jsx)("hr",{className:"my-2 border-gray-200"}),(0,r.jsx)("button",{onClick:()=>{localStorage.removeItem("userEmail"),localStorage.removeItem("collegeName"),localStorage.removeItem("role"),document.cookie="role=; path=/; max-age=0",m.push("/login")},className:"w-full text-left px-4 py-2 text-red-600 hover:bg-red-50 transition-all duration-200",children:"Logout"})]})]})}var b=s(98848),g=s(9535),f=s(84659),y=s(81080),j=s(95501),v=s(47831),w=s(54413);let C=[{items:[{title:"My Campus",href:"/",icon:(0,r.jsx)(b.A,{})},{title:"My Jobs",href:"/myjobs",icon:(0,r.jsx)(g.A,{})},{title:"Explore",href:"/explore",icon:(0,r.jsx)(f.A,{})},{title:"Inbox",href:"/inbox",icon:(0,r.jsx)(y.A,{})}]},{items:[{title:"Job Postings",href:"/jobpostings",icon:(0,r.jsx)(j.A,{})},{title:"Companies",href:"/companies",icon:(0,r.jsx)(v.A,{})},{title:"Events",href:"/events",icon:(0,r.jsx)(w.A,{})},{title:"Calendar",href:"/calendar",icon:(0,r.jsx)(w.A,{})},{title:"My Profile",href:"/profile",icon:(0,r.jsx)(a.A,{})},{title:"Settings",href:"/settings",icon:(0,r.jsx)(n.A,{})}]}];var N=s(51421);let P=e=>e.startsWith("/company/")?"Company Profile":e.startsWith("/admin/")?"Admin Dashboard":({"/":"My Campus","/myjobs":"My Jobs","/explore":"Explore","/inbox":"Inbox","/jobpostings":"Job Postings","/companies":"Companies","/events":"Events","/calendar":"Calendar","/admin":"Admin Dashboard","/admin/posts":"Admin Posts"})[e]||"PlaceEasy",k=e=>e.startsWith("/admin"),A=e=>["/login","/signup","/onboarding"].includes(e);function E({children:e}){let t=(0,o.usePathname)(),[s,a]=(0,i.useState)(""),[n,c]=(0,i.useState)(!1);if(!n)return(0,r.jsxs)("html",{lang:"en",children:[(0,r.jsxs)("head",{children:[(0,r.jsx)("title",{children:"PlaceEasy - Campus Placement Platform"}),(0,r.jsx)("meta",{name:"description",content:"Your comprehensive campus placement and career management platform"})]}),(0,r.jsx)("body",{className:"h-screen overflow-hidden",children:(0,r.jsx)(d.N,{children:(0,r.jsx)(N.ph,{children:(0,r.jsx)("main",{className:"h-full bg-white text-black",children:e})})})})]});let m=P(t),u=A(t),h=k(t);return u?(0,r.jsxs)("html",{lang:"en",children:[(0,r.jsxs)("head",{children:[(0,r.jsx)("title",{children:"PlaceEasy - Campus Placement Platform"}),(0,r.jsx)("meta",{name:"description",content:"Your comprehensive campus placement and career management platform"})]}),(0,r.jsx)("body",{className:"h-screen overflow-hidden",children:(0,r.jsx)(d.N,{children:(0,r.jsx)(N.ph,{children:(0,r.jsx)("main",{className:"h-full bg-white text-black",children:e})})})})]}):h?(0,r.jsxs)("html",{lang:"en",children:[(0,r.jsxs)("head",{children:[(0,r.jsx)("title",{children:"PlaceEasy - Campus Placement Platform"}),(0,r.jsx)("meta",{name:"description",content:"Your comprehensive campus placement and career management platform"})]}),(0,r.jsx)("body",{className:"h-screen overflow-hidden",children:(0,r.jsx)(d.N,{children:(0,r.jsx)(N.ph,{children:(0,r.jsxs)("div",{className:"h-screen bg-gray-50",children:[(0,r.jsxs)("div",{className:"fixed w-full flex justify-between items-center py-4 bg-white shadow-sm z-10",children:[(0,r.jsx)("span",{className:"text-gray-700 font-medium text-xl ml-28",children:m}),(0,r.jsxs)("div",{className:"flex items-center gap-4 mr-6",children:[(0,r.jsx)(p,{}),(0,r.jsx)(x,{})]})]}),(0,r.jsx)("div",{className:"pt-16 h-[calc(100vh-4rem)]",children:e})]})})})})]}):(0,r.jsxs)("html",{lang:"en",children:[(0,r.jsxs)("head",{children:[(0,r.jsx)("title",{children:"PlaceEasy - Campus Placement Platform"}),(0,r.jsx)("meta",{name:"description",content:"Your comprehensive campus placement and career management platform"})]}),(0,r.jsx)("body",{className:"h-screen overflow-hidden",children:(0,r.jsx)(d.N,{children:(0,r.jsx)(N.ph,{children:(0,r.jsxs)("div",{className:"h-screen bg-gray-50",children:[(0,r.jsxs)("div",{className:"fixed w-full flex justify-between items-center py-4 bg-white shadow-sm z-10",children:[(0,r.jsx)("span",{className:"text-gray-700 font-medium text-xl ml-16 sm:ml-28",children:s||"AVV Chennai"}),(0,r.jsxs)("div",{className:"flex items-center gap-4 mr-6",children:[(0,r.jsx)(p,{}),(0,r.jsx)(x,{})]})]}),(0,r.jsx)(l.A,{sections:C,defaultExpanded:!1,navbarHeight:"4rem",className:"z-20"}),(0,r.jsx)("div",{className:"flex pt-16 h-[calc(100vh-4rem)]",children:(0,r.jsx)("div",{className:"flex-1 p-6 ml-0 md:ml-20 h-full overflow-auto",children:(0,r.jsx)("div",{className:"bg-white rounded-xl shadow-md p-6 min-h-[800px] text-black content-card",children:e})})})]})})})})]})}},35463:(e,t,s)=>{Promise.resolve().then(s.bind(s,27348))},37347:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},44263:()=>{},51421:(e,t,s)=>{"use strict";s.d(t,{ph:()=>y,hN:()=>f});var r=s(60687),i=s(43210),o=s.n(i),l=s(5336),a=s(43649),n=s(96882),c=s(87891),d=s(99891),m=s(93613),u=s(11860);let h=({isOpen:e,onClose:t,type:s="error",title:i,message:h,details:p,actions:x=[],dismissible:b=!0,autoClose:g=!1,autoCloseDelay:f=5e3,showIcon:y=!0})=>{if(o().useEffect(()=>{if(g&&e){let e=setTimeout(()=>{t()},f);return()=>clearTimeout(e)}},[g,e,f,t]),o().useEffect(()=>(e?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[e]),!e)return null;let{icon:j,iconColor:v,bgColor:w,borderColor:C,buttonColor:N}=(()=>{switch(s){case"success":return{icon:l.A,iconColor:"text-green-500",bgColor:"bg-green-50",borderColor:"border-green-200",buttonColor:"bg-green-600 hover:bg-green-700"};case"warning":return{icon:a.A,iconColor:"text-yellow-500",bgColor:"bg-yellow-50",borderColor:"border-yellow-200",buttonColor:"bg-yellow-600 hover:bg-yellow-700"};case"info":return{icon:n.A,iconColor:"text-blue-500",bgColor:"bg-blue-50",borderColor:"border-blue-200",buttonColor:"bg-blue-600 hover:bg-blue-700"};case"network":return{icon:c.A,iconColor:"text-red-500",bgColor:"bg-red-50",borderColor:"border-red-200",buttonColor:"bg-red-600 hover:bg-red-700"};case"auth":return{icon:d.A,iconColor:"text-red-500",bgColor:"bg-red-50",borderColor:"border-red-200",buttonColor:"bg-red-600 hover:bg-red-700"};case"validation":return{icon:m.A,iconColor:"text-orange-500",bgColor:"bg-orange-50",borderColor:"border-orange-200",buttonColor:"bg-orange-600 hover:bg-orange-700"};default:return{icon:m.A,iconColor:"text-red-500",bgColor:"bg-red-50",borderColor:"border-red-200",buttonColor:"bg-red-600 hover:bg-red-700"}}})();return(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,r.jsxs)("div",{className:`bg-white rounded-lg shadow-xl max-w-md w-full mx-4 border-2 ${C}`,children:[(0,r.jsxs)("div",{className:`${w} px-6 py-4 rounded-t-lg border-b ${C} flex items-center justify-between`,children:[(0,r.jsxs)("div",{className:"flex items-center",children:[y&&(0,r.jsx)(j,{className:`w-6 h-6 ${v} mr-3`}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:i})]}),b&&(0,r.jsx)("button",{onClick:t,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,r.jsx)(u.A,{className:"w-5 h-5"})})]}),(0,r.jsxs)("div",{className:"px-6 py-4",children:[(0,r.jsx)("p",{className:"text-gray-700 mb-4",children:h}),p&&(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 mb-4",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-800 mb-2",children:"Details:"}),"string"==typeof p?(0,r.jsx)("p",{className:"text-sm text-gray-600",children:p}):Array.isArray(p)?(0,r.jsx)("ul",{className:"text-sm text-gray-600 space-y-1",children:p.map((e,t)=>(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-gray-400 rounded-full mt-2 mr-2 flex-shrink-0"}),e]},t))}):(0,r.jsx)("div",{className:"text-sm text-gray-600",children:Object.entries(p).map(([e,t])=>(0,r.jsxs)("div",{className:"mb-1",children:[(0,r.jsxs)("span",{className:"font-medium",children:[e,":"]})," ",Array.isArray(t)?t.join(", "):t]},e))})]})]}),(0,r.jsx)("div",{className:"px-6 py-4 bg-gray-50 rounded-b-lg flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-3 sm:justify-end",children:x.length>0?x.map((e,t)=>(0,r.jsxs)("button",{onClick:e.onClick,className:`px-4 py-2 rounded-lg font-medium transition-colors ${"secondary"===e.variant?"bg-gray-200 text-gray-800 hover:bg-gray-300":"danger"===e.variant?"bg-red-600 text-white hover:bg-red-700":N+" text-white"}`,disabled:e.disabled,children:[e.loading&&(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 inline-block"}),e.icon&&(0,r.jsx)(e.icon,{className:"w-4 h-4 mr-2 inline-block"}),e.label]},t)):(0,r.jsx)("button",{onClick:t,className:`px-4 py-2 rounded-lg font-medium text-white transition-colors ${N}`,children:"OK"})})]})})};var p=s(58869),x=s(16023),b=s(25334);let g=(0,i.createContext)(),f=()=>{let e=(0,i.useContext)(g);if(!e)throw Error("useNotification must be used within a NotificationProvider");return e},y=({children:e})=>{let[t,s]=(0,i.useState)(null),o=(0,i.useCallback)(e=>{s({id:Date.now(),...e})},[]),l=(0,i.useCallback)(()=>{s(null)},[]),a=(0,i.useCallback)((e,t,s=null,r=[])=>{o({type:"error",title:e,message:t,details:s,actions:r})},[o]),n=(0,i.useCallback)((e,t,s=!0)=>{o({type:"success",title:e,message:t,autoClose:s,autoCloseDelay:3e3})},[o]),m=(0,i.useCallback)((e,t,s=null,r=[])=>{o({type:"warning",title:e,message:t,details:s,actions:r})},[o]),u=(0,i.useCallback)((e,t,s=!1)=>{o({type:"info",title:e,message:t,autoClose:s})},[o]),f=(0,i.useCallback)(()=>{o({type:"warning",title:"Resume Required",message:"You need to upload a resume before applying to this job.",details:["Please upload your resume to your profile first","You can upload it from the Profile section","Accepted formats: PDF, DOC, DOCX (max 5MB)"],showIcon:!0,actions:[{text:"Go to Profile",style:"primary",icon:p.A,action:()=>{window.location.href="/profile"}},{text:"Upload Resume",style:"secondary",icon:x.A,action:()=>{let e=document.createElement("input");e.type="file",e.accept=".pdf,.doc,.docx",e.onchange=e=>{let t=e.target.files[0];t&&console.log("Resume file selected:",t)},e.click()}}],dismissible:!0})},[o]),y=(0,i.useCallback)(e=>{let t=e?.response?.data||{};if(t.resume)return void f();let s={},r=!1;if(Object.keys(t).forEach(e=>{Array.isArray(t[e])&&t[e].length>0&&(s[e]=t[e],r=!0)}),r)return void o({type:"error",title:"Application Submission Failed",message:"Please fix the following validation errors:",details:Object.entries(s).map(([e,t])=>`${e}: ${Array.isArray(t)?t.join(", "):t}`),showIcon:!0,dismissible:!0});o({type:"error",title:"Application Failed",message:t.detail||t.message||e?.message||"Failed to submit your job application.",details:["Please check your application details","Ensure all required fields are filled","Try again in a few moments"],showIcon:!0,actions:[{text:"Try Again",style:"primary",action:()=>{window.location.reload()}}],dismissible:!0})},[o,f]),j=(0,i.useCallback)((e=[])=>{o({type:"warning",title:"Profile Incomplete",message:"Please complete your profile before applying to jobs.",details:e.length>0?[`Missing: ${e.join(", ")}`]:["Some required profile information is missing","Please update your profile with all necessary details","This ensures better job matching and application processing"],showIcon:!0,actions:[{text:"Complete Profile",style:"primary",icon:p.A,action:()=>{window.location.href="/profile"}}],dismissible:!0})},[o]),v=(0,i.useCallback)(()=>{o({type:"warning",title:"Session Expired",message:"Your login session has expired. Please login again to continue.",details:["For security reasons, sessions expire after a period of inactivity","Please login again to access your account","Your data is safe and will be available after login"],showIcon:!0,actions:[{text:"Login",style:"primary",icon:d.A,action:()=>{window.location.href="/login"}}],dismissible:!1,autoClose:!1})},[o]),w=(0,i.useCallback)(()=>{o({type:"info",title:"System Maintenance",message:"The system is currently under maintenance. Please try again later.",details:["We are working to improve your experience","Maintenance should be completed shortly","Thank you for your patience"],showIcon:!0,actions:[{text:"Check Status",style:"secondary",icon:b.A,action:()=>{window.open("/status","_blank")}}],dismissible:!0,autoClose:!1})},[o]),C=(0,i.useCallback)((e="Authentication failed. Please log in again.")=>{o({type:"auth",title:"Authentication Required",message:e,actions:[{label:"Go to Login",onClick:()=>{l(),window.location.href="/login"},icon:p.A}]})},[o,l]),N=(0,i.useCallback)((e=null)=>{o({type:"network",title:"Connection Error",message:"Unable to connect to the server. Please check your internet connection and try again.",details:e?["Make sure you're connected to the internet","Try refreshing the page","Check if the server is running",e.message||"Unknown network error"]:["Make sure you're connected to the internet","Try refreshing the page","Check if the server is running"],actions:[{label:"Retry",onClick:()=>{l(),window.location.reload()},icon:c.A},{label:"Cancel",onClick:l,variant:"secondary"}]})},[o,l]),P=(0,i.useCallback)(()=>{o({type:"validation",title:"Resume Required",message:"You need to upload a resume before applying to this job. Please update your profile first.",details:["Go to your profile page",'Upload your resume in the "Resume" section',"Come back and apply for the job"],actions:[{label:"Go to Profile",onClick:()=>{l(),window.location.href="/profile"},icon:p.A},{label:"Cancel",onClick:l,variant:"secondary"}]})},[o,l]),k=(0,i.useCallback)((e=null)=>{o({type:"error",title:"File Upload Failed",message:"There was a problem uploading your file. Please try again with a different file.",details:e||["Check that your file is not too large (max 10MB)","Supported formats: PDF, DOC, DOCX","Make sure the file is not corrupted","Try uploading a different file"],actions:[{label:"Try Again",onClick:l,icon:x.A}]})},[o,l]),A=(0,i.useCallback)((e,t)=>{let s,r;"object"==typeof t&&null!==t?(s={},Object.entries(t).forEach(([e,t])=>{Array.isArray(t)?s[e]=t.join(", "):s[e]=t}),r="Please fix the following errors and try again:"):Array.isArray(t)?(s=t,r="Please fix the following issues:"):(r=t||"Please check your input and try again.",s=null),o({type:"validation",title:e||"Validation Error",message:r,details:s})},[o]),E=(0,i.useCallback)((e="You don't have permission to perform this action.")=>{o({type:"auth",title:"Permission Denied",message:e,details:["Contact your administrator if you believe this is an error","Make sure you're logged in with the correct account","Check if your session has expired"],actions:[{label:"Go to Login",onClick:()=>{l(),window.location.href="/login"},icon:d.A},{label:"Contact Support",onClick:()=>{l(),window.location.href="/admin/helpandsupport"},icon:b.A,variant:"secondary"}]})},[o,l]),S=(0,i.useCallback)((e=[])=>{o({type:"warning",title:"Profile Incomplete",message:"Your profile needs to be completed before you can apply for jobs.",details:e.length>0?[`Please fill in the following fields: ${e.join(", ")}`]:["Make sure your personal information is complete","Upload your resume","Verify your contact information","Add your academic details"],actions:[{label:"Complete Profile",onClick:()=>{l(),window.location.href="/profile"},icon:p.A},{label:"Later",onClick:l,variant:"secondary"}]})},[o,l]),D=(0,i.useCallback)((e,t="operation")=>{e?.response?.status===401?C():e?.response?.status===403?E():e?.response?.status>=500?a("Server Error",`A server error occurred while performing the ${t}. Please try again later.`,"If the problem persists, please contact support."):e?.response?a("Error",e?.response?.data?.detail||e?.response?.data?.message||e?.message||`Failed to complete ${t}. Please try again.`):N(e)},[a,C,E,N]);return(0,r.jsxs)(g.Provider,{value:{showNotification:o,hideNotification:l,showError:a,showSuccess:n,showWarning:m,showInfo:u,showAuthError:C,showNetworkError:N,showResumeRequiredError:P,showFileUploadError:k,showValidationError:A,showPermissionError:E,showProfileIncompleteError:S,showApplicationSubmissionError:y,showMissingResumeModal:f,showProfileIncompleteModal:j,showSessionExpiredModal:v,showMaintenanceModal:w,handleApiError:D},children:[e,t&&(0,r.jsx)(h,{isOpen:!0,onClose:l,type:t.type,title:t.title,message:t.message,details:t.details,actions:t.actions,dismissible:!1!==t.dismissible,autoClose:t.autoClose,autoCloseDelay:t.autoCloseDelay,showIcon:!1!==t.showIcon})]})}},61323:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},75535:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\layout.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\layout.js","default")},84578:(e,t,s)=>{"use strict";s.d(t,{D:()=>l,N:()=>a});var r=s(60687),i=s(43210);let o=(0,i.createContext)(),l=()=>{let e=(0,i.useContext)(o);if(!e)throw Error("useTheme must be used within a ThemeProvider");return e},a=({children:e})=>{let[t,s]=(0,i.useState)("system"),[l,a]=(0,i.useState)("light");return(0,i.useEffect)(()=>{},[]),(0,i.useEffect)(()=>{},[t]),(0,r.jsx)(o.Provider,{value:{theme:t,resolvedTheme:l,changeTheme:e=>{s(e)},isDark:"dark"===l,isLight:"light"===l},children:e})}}};