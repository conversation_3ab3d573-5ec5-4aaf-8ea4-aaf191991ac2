(()=>{var e={};e.id=2249,e.ids=[2249,4335],e.modules={2584:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>c});var r=a(65239),s=a(48088),n=a(88170),i=a.n(n),l=a(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);a.d(t,o);let c={children:["",{children:["admin",{children:["companymanagement",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,28841)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\companymanagement\\page.jsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,23697)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\layout.jsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,75535)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\layout.js"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\companymanagement\\page.jsx"],p={require:a,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/admin/companymanagement/page",pathname:"/admin/companymanagement",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10722:(e,t,a)=>{Promise.resolve().then(a.bind(a,11527))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11527:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>y});var r=a(60687),s=a(43210),n=a(24664),i=a(98848),l=a(9535),o=a(37325),c=a(81080),d=a(95994),p=a(80556),u=a(90910),m=a(81172),h=a(20798),x=a(58869),g=a(53411);function y({children:e}){let[t,a]=(0,s.useState)(""),y=[{items:[{title:"Dashboard",href:"/admin/dashboard",icon:(0,r.jsx)(i.A,{})},{title:"Jobs",href:"/admin/jobs",icon:(0,r.jsx)(l.A,{})},{title:"Applications",href:"/admin/applications",icon:(0,r.jsx)(o.A,{})},{title:"Posts",href:"/admin/posts",icon:(0,r.jsx)(h.A,{className:"w-5 h-5"})},{title:"Student Management",href:"/admin/student-management",icon:(0,r.jsx)(x.A,{className:"w-5 h-5"})},{title:"Student Analytics",href:"/admin/analytics",icon:(0,r.jsx)(g.A,{className:"w-5 h-5"})},{title:"Company Management",href:"/admin/companymanagement",icon:(0,r.jsx)(c.A,{})},{title:"Forms",href:"/admin/form",icon:(0,r.jsx)(d.A,{})}]}],f=[{title:"My Profile",href:"/admin/profile",icon:(0,r.jsx)(p.A,{})},{title:"Settings",href:"../settings",icon:(0,r.jsx)(u.A,{})},{title:"Contact Support",href:"/admin/helpandsupport",icon:(0,r.jsx)(m.A,{})}];return(0,r.jsx)("div",{className:"h-screen bg-gray-50 overflow-hidden",children:(0,r.jsxs)("div",{className:"flex h-full",children:[(0,r.jsx)(n.A,{sections:y,bottomItems:f,defaultExpanded:!1,navbarHeight:"0",className:"z-20"}),(0,r.jsx)("div",{className:"flex-1 p-6 ml-20 overflow-y-auto",children:(0,r.jsx)("div",{className:"bg-white rounded-xl shadow-md p-6 min-h-full text-black",children:e})})]})})}},12412:e=>{"use strict";e.exports=require("assert")},14335:(e,t,a)=>{"use strict";a.d(t,{C1:()=>i,Gu:()=>x,JT:()=>o,RC:()=>c,S0:()=>y,Y_:()=>p,bl:()=>u,dl:()=>g,eK:()=>l,fetchCompanies:()=>n,getCompanyStats:()=>d,jQ:()=>m,mm:()=>s,oY:()=>h});var r=a(58138);function s(e={}){let t=new URLSearchParams;e.page&&t.append("page",e.page),e.per_page&&t.append("per_page",e.per_page),e.tier&&"ALL"!==e.tier&&t.append("tier",e.tier),e.industry&&"ALL"!==e.industry&&t.append("industry",e.industry),e.campus_recruiting&&t.append("campus_recruiting",e.campus_recruiting),e.search&&t.append("search",e.search),e.sort&&t.append("sort",e.sort),t.append("_t",new Date().getTime());let a=t.toString(),n=[`/api/v1/companies/${a?`?${a}`:""}`,`/api/v1/college/default-college/companies/${a?`?${a}`:""}`];return r.A.get(n[0]).catch(e=>(console.log(`Primary endpoint failed: ${e.message}, trying fallback...`),r.A.get(n[1])))}async function n(e={}){try{console.log("Fetching companies from API...");let t=await s(e),a=[];if(t.data&&Array.isArray(t.data)?a=t.data:t.data&&t.data.results&&Array.isArray(t.data.results)?a=t.data.results:t.data&&t.data.data&&Array.isArray(t.data.data)&&(a=t.data.data),console.log(`Retrieved ${a.length} companies from API`),a.length>0)return await Promise.all(a.map(async e=>{try{let t=await i(e.id);return p(t.data)}catch(t){return console.log(`Could not fetch details for company ${e.id}:`,t),p(e)}}));throw Error("No companies returned from API")}catch(t){console.error("Error fetching companies from API:",t),console.log("Falling back to static company data");let{companies:e}=await a.e(1286).then(a.bind(a,61286));return e}}function i(e){let t=[`/api/v1/company/${e}/`,`/api/v1/companies/${e}/`,`/api/v1/college/default-college/companies/${e}/`];return r.A.get(t[0]).catch(e=>(console.log(`First company endpoint failed: ${e.message}, trying second...`),r.A.get(t[1]).catch(e=>(console.log(`Second company endpoint failed: ${e.message}, trying third...`),r.A.get(t[2])))))}function l(e){let t=new FormData;return Object.keys(e).forEach(a=>{"logo"===a&&e[a]instanceof File?t.append(a,e[a]):null!==e[a]&&void 0!==e[a]&&t.append(a,e[a])}),r.A.post("/api/v1/companies/",t,{headers:{"Content-Type":"multipart/form-data"}})}function o(e,t){let a=new FormData;return Object.keys(t).forEach(e=>{"logo"===e&&t[e]instanceof File?a.append(e,t[e]):null!==t[e]&&void 0!==t[e]&&a.append(e,t[e])}),r.A.put(`/api/v1/companies/${e}/`,a,{headers:{"Content-Type":"multipart/form-data"}})}function c(e){return r.A.delete(`/api/v1/companies/${e}/`)}function d(){return r.A.get("/api/v1/companies/stats/")}function p(e){return{id:e.id,name:e.name,logo:e.logo||`https://via.placeholder.com/48x48/4285F4/FFFFFF?text=${e.name.charAt(0)}`,description:e.description||"",industry:e.industry||"",size:e.size||"",founded:e.founded||"",location:e.location||"Location not specified",website:e.website||"",tier:e.tier||"Tier 3",campus_recruiting:e.campus_recruiting||!1,totalActiveJobs:e.total_active_jobs||0,totalApplicants:e.total_applicants||0,totalHired:e.total_hired||0,awaitedApproval:e.pending_approval||0}}function u(e){return r.A.get(`/api/v1/companies/${e}/followers/count/`)}function m(e,t){return r.A.post(`/api/v1/companies/${e}/followers/`,{user_id:t})}function h(e,t){return r.A.delete(`/api/v1/companies/${e}/followers/`,{data:{user_id:t}})}function x(e,t){return r.A.get(`/api/v1/companies/${e}/followers/status/?user_id=${t}`)}function g(e){return r.A.get(`/api/v1/users/${e}/following/`)}function y(){return r.A.get("/api/v1/companies/simple/").then(e=>{if(e.data&&e.data.success)return e.data;throw Error("Failed to fetch companies")}).catch(e=>(console.error("Error fetching companies:",e),{success:!1,data:[],count:0,error:e.message}))}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20798:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("megaphone",[["path",{d:"m3 11 18-5v12L3 14v-3z",key:"n962bs"}],["path",{d:"M11.6 16.8a3 3 0 1 1-5.8-1.6",key:"1yl0tm"}]])},21820:e=>{"use strict";e.exports=require("os")},23697:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\layout.jsx","default")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28841:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\companymanagement\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\companymanagement\\page.jsx","default")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37325:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var r=(0,a(6445).A)("outline","clipboard-list","IconClipboardList",[["path",{d:"M9 5h-2a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-12a2 2 0 0 0 -2 -2h-2",key:"svg-0"}],["path",{d:"M9 3m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z",key:"svg-1"}],["path",{d:"M9 12l.01 0",key:"svg-2"}],["path",{d:"M13 12l2 0",key:"svg-3"}],["path",{d:"M9 16l.01 0",key:"svg-4"}],["path",{d:"M13 16l2 0",key:"svg-5"}]])},40228:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41312:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},48173:(e,t,a)=>{Promise.resolve().then(a.bind(a,23697))},53411:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},53531:(e,t,a)=>{Promise.resolve().then(a.bind(a,95510))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},63787:(e,t,a)=>{"use strict";function r(){try{let e=localStorage.getItem("access");if(!e)return null;let t=localStorage.getItem("user");if(t){let e=JSON.parse(t);return e.id||e.user_id||null}if(e)try{let t=e.split(".")[1],a=JSON.parse(atob(t));return a.user_id||a.id||a.sub||null}catch(e){console.error("Error decoding JWT token:",e)}return null}catch(e){return console.error("Error getting user ID:",e),null}}function s(){return localStorage.getItem("access")}function n(){try{let e=localStorage.getItem("user");return e?JSON.parse(e):null}catch(e){return console.error("Error getting user data:",e),null}}a.d(t,{F6:()=>r,c4:()=>s,gL:()=>n})},64398:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},67760:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},74075:e=>{"use strict";e.exports=require("zlib")},79410:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},79551:e=>{"use strict";e.exports=require("url")},81172:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var r=(0,a(6445).A)("outline","help","IconHelp",[["path",{d:"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0",key:"svg-0"}],["path",{d:"M12 17l0 .01",key:"svg-1"}],["path",{d:"M12 13.5a1.5 1.5 0 0 1 1 -1.5a2.6 2.6 0 1 0 -3 -4",key:"svg-2"}]])},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},87875:(e,t,a)=>{Promise.resolve().then(a.bind(a,28841))},88233:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},94735:e=>{"use strict";e.exports=require("events")},95510:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>v});var r=a(60687),s=a(43210),n=a(16189),i=a(93613);let l=(0,a(62688).A)("circle-plus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]]);var o=a(79410),c=a(64398),d=a(67760),p=a(99270),u=a(63143),m=a(88233),h=a(97992),x=a(41312),g=a(40228),y=a(1469),f=a(14335);function b(){let e=(0,n.useRouter)(),t=(0,n.useSearchParams)(),[a,b]=(0,s.useState)([]),[v,j]=(0,s.useState)(t.get("search")||""),[N,w]=(0,s.useState)(t.get("tier")||"ALL"),[A,k]=(0,s.useState)(t.get("industry")||"ALL"),[C,M]=(0,s.useState)(t.get("sort")||"name"),[S,_]=(0,s.useState)(new Set),[P,L]=(0,s.useState)(!0),[F,$]=(0,s.useState)(null),[E,T]=(0,s.useState)({total:0,tier1:0,tier2:0,tier3:0,campus_recruiting:0}),[q,D]=(0,s.useState)(parseInt(t.get("page"))||1),[z,I]=(0,s.useState)(1),[O,R]=(0,s.useState)(0),[V,J]=(0,s.useState)(8),H=(e={})=>{let t=new URLSearchParams;Object.entries({page:q.toString(),search:v,tier:N,industry:A,sort:C,...e}).forEach(([e,a])=>{a&&"ALL"!==a&&""!==a&&"null"!==a&&null!==a&&t.set(e,a)});let a=`${window.location.pathname}?${t.toString()}`;window.history.pushState({},"",a)},U=async(e=1)=>{try{L(!0),$(null);let t={page:e,page_size:V};v&&(t.search=v),"ALL"!==N&&(t.tier=N),"ALL"!==A&&(t.industry=A),C&&(t.ordering="name"===C?"name":"jobs"===C?"-total_active_jobs":"applicants"===C?"-total_applicants":"tier"===C?"tier":"name");let a=await y.SY.getCompanies(t),r=a.data.map(e=>({id:e.id,name:e.name||"",logo:e.logo||`https://via.placeholder.com/48x48/4285F4/FFFFFF?text=${(e.name||"C").charAt(0)}`,description:e.description||"",industry:e.industry||"",size:e.size||"Size not specified",founded:e.founded||"",location:e.location||"Location not specified",website:e.website||"",tier:e.tier||"Tier 3",campus_recruiting:e.campus_recruiting||!1,totalActiveJobs:e.total_active_jobs||0,totalApplicants:e.total_applicants||0,totalHired:e.total_hired||0,awaitedApproval:e.pending_approval||0}));b(r),D(e),I(a.pagination.total_pages),R(a.pagination.total_count),L(!1)}catch(e){console.error("Error fetching companies:",e),$("Failed to load companies. Please try again."),b([]),L(!1)}},G=e=>{D(e),H({page:e.toString()}),U(e)},K=e=>{j(e)},Y=()=>{D(1),H({search:v,page:"1"}),U(1)},Z=e=>{w(e),D(1),H({tier:e,page:"1"}),U(1)},Q=e=>{k(e),D(1),H({industry:e,page:"1"}),U(1)},W=e=>{M(e),D(1),H({sort:e,page:"1"}),U(1)},X=e=>{switch(e){case"Tier 1":return"bg-emerald-100 text-emerald-800 border-emerald-200";case"Tier 2":return"bg-blue-100 text-blue-800 border-blue-200";case"Tier 3":return"bg-purple-100 text-purple-800 border-purple-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}},B=t=>{e.push(`/admin/companymanagement/${t}`)},ee=(t,a)=>{t.stopPropagation(),e.push(`/admin/companymanagement/edit/${a}`)},et=async(e,t)=>{if(e.stopPropagation(),confirm("Are you sure you want to delete this company? This action cannot be undone."))try{await (0,f.RC)(t),U(q),alert("Company deleted successfully")}catch(e){console.error("Error deleting company:",e),alert("Failed to delete company. Please try again.")}};return P?(0,r.jsxs)("div",{className:"h-full flex flex-col items-center justify-center",children:[(0,r.jsxs)("div",{className:"relative w-16 h-16 mb-8",children:[(0,r.jsx)("div",{className:"absolute inset-0 border-t-4 border-blue-500 rounded-full animate-spin"}),(0,r.jsx)("div",{className:"absolute inset-2 border-r-4 border-transparent rounded-full"})]}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"Loading companies..."})]}):F?(0,r.jsx)("div",{className:"h-full flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center max-w-md",children:[(0,r.jsx)("div",{className:"text-red-500 mb-4",children:(0,r.jsx)(i.A,{className:"w-16 h-16 mx-auto"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Error Loading Companies"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:F}),(0,r.jsx)("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Retry"})]})}):(0,r.jsx)("div",{className:"h-full overflow-y-auto",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("div",{className:"mb-6 flex justify-between items-center",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Company Management"}),(0,r.jsxs)("button",{onClick:()=>{e.push("/admin/companymanagement/create")},className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm",children:[(0,r.jsx)(l,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"Create New Company"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4 mb-8",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-100",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,r.jsx)(o.A,{className:"w-5 h-5 text-blue-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:E.total}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Total Companies"})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-100",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"p-2 bg-emerald-100 rounded-lg",children:(0,r.jsx)(c.A,{className:"w-5 h-5 text-emerald-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:E.tier1}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Tier 1"})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-100",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,r.jsx)(c.A,{className:"w-5 h-5 text-blue-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:E.tier2}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Tier 2"})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-100",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"p-2 bg-amber-100 rounded-lg",children:(0,r.jsx)(c.A,{className:"w-5 h-5 text-amber-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:E.tier3}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Tier 3"})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-100",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"p-2 bg-amber-100 rounded-lg",children:(0,r.jsx)(d.A,{className:"w-5 h-5 text-amber-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:S.size}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Following"})]})]})})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-100",children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,r.jsxs)("div",{className:"flex-1 flex gap-2",children:[(0,r.jsxs)("div",{className:"relative flex-1",children:[(0,r.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,r.jsx)("input",{type:"text",placeholder:"Search companies, industries...",value:v,onChange:e=>K(e.target.value),onKeyDown:e=>{"Enter"===e.key&&(e.preventDefault(),Y())},className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,r.jsx)("button",{onClick:Y,className:"px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 whitespace-nowrap transition-colors",children:"Search"})]}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsxs)("select",{value:N,onChange:e=>Z(e.target.value),className:"px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[120px]",children:[(0,r.jsx)("option",{value:"ALL",children:"All Tiers"}),(0,r.jsx)("option",{value:"Tier 1",children:"Tier 1"}),(0,r.jsx)("option",{value:"Tier 2",children:"Tier 2"}),(0,r.jsx)("option",{value:"Tier 3",children:"Tier 3"})]}),(0,r.jsxs)("select",{value:A,onChange:e=>Q(e.target.value),className:"px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[150px]",children:[(0,r.jsx)("option",{value:"ALL",children:"All Industries"}),["Technology","Finance","Healthcare","Manufacturing","Consulting","E-commerce"].map(e=>(0,r.jsx)("option",{value:e,children:e},e))]}),(0,r.jsxs)("select",{value:C,onChange:e=>W(e.target.value),className:"px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[130px]",children:[(0,r.jsx)("option",{value:"name",children:"Company A-Z"}),(0,r.jsx)("option",{value:"jobs",children:"Most Jobs"}),(0,r.jsx)("option",{value:"applicants",children:"Most Popular"}),(0,r.jsx)("option",{value:"tier",children:"Tier"})]})]})]}),(0,r.jsxs)("div",{className:"mt-4 text-sm text-gray-600",children:["Showing ",a.length," of ",O," companies (Page ",q," of ",z,")"]})]})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:a.length>0?a.map(e=>(0,r.jsxs)("div",{onClick:()=>B(e.id),className:"bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-lg transition-all duration-200 cursor-pointer group",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-lg",children:e.name.charAt(0)})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900 group-hover:text-blue-600 transition-colors",children:e.name}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:e.industry})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("button",{onClick:t=>ee(t,e.id),className:"p-2 rounded-lg transition-colors bg-blue-100 text-blue-600 hover:bg-blue-200",children:(0,r.jsx)(u.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{onClick:t=>et(t,e.id),className:"p-2 rounded-lg transition-colors bg-red-100 text-red-600 hover:bg-red-200",children:(0,r.jsx)(m.A,{className:"w-4 h-4"})})]})]}),(0,r.jsxs)("div",{className:"space-y-3 mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[(0,r.jsx)(h.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:e.location})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[(0,r.jsx)(x.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:e.size})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[(0,r.jsx)(g.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:e.founded?`Founded ${e.founded}`:"Founded year not specified"})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,r.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium border ${X(e.tier)}`,children:e.tier}),e.campus_recruiting&&(0,r.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-700 border border-green-200",children:"Campus Recruiting"})]}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-4 line-clamp-3",children:e.description}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4 pt-4 border-t border-gray-100",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-lg font-bold text-gray-900",children:e.totalActiveJobs||0}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Active Jobs"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-lg font-bold text-gray-900",children:e.totalApplicants||0}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Applicants"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-lg font-bold text-gray-900",children:e.totalHired||0}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Hired"})]})]})]},e.id)):(0,r.jsxs)("div",{className:"col-span-full flex flex-col items-center justify-center py-16",children:[(0,r.jsx)("div",{className:"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4",children:(0,r.jsx)(o.A,{className:"w-12 h-12 text-gray-400"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"No companies found"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"Try adjusting your search or filters"}),(0,r.jsx)("button",{onClick:()=>{j(""),w("ALL"),k("ALL"),M("name"),D(1),window.history.pushState({},"",window.location.pathname),U(1)},className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Clear Filters"})]})}),a.length>0&&z>1&&(0,r.jsx)("div",{className:"mt-8 flex justify-center",children:(0,r.jsxs)("nav",{className:"flex items-center space-x-2","aria-label":"Pagination",children:[(0,r.jsx)("button",{onClick:()=>{if(q>1){let e=q-1;D(e),H({page:e.toString()}),U(e)}},disabled:1===q,className:`px-4 py-2 rounded-md border ${1===q?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 hover:bg-gray-100"}`,children:"Previous"}),[...Array(Math.min(5,z))].map((e,t)=>{let a;return a=z<=5||q<=3?t+1:q>=z-2?z-4+t:q-2+t,(0,r.jsx)("button",{onClick:()=>G(a),className:`w-10 h-10 flex items-center justify-center rounded-md ${q===a?"bg-blue-500 text-white":"text-gray-700 hover:bg-gray-100"}`,"aria-current":q===a?"page":void 0,children:a},a)}),(0,r.jsx)("button",{onClick:()=>{if(q<z){let e=q+1;D(e),H({page:e.toString()}),U(e)}},disabled:q===z,className:`px-4 py-2 rounded-md border ${q===z?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 hover:bg-gray-100"}`,children:"Next"})]})})]})})}function v(){return(0,r.jsx)(s.Suspense,{fallback:(0,r.jsx)("div",{children:"Loading..."}),children:(0,r.jsx)(b,{})})}a(63787)},95994:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var r=(0,a(6445).A)("outline","forms","IconForms",[["path",{d:"M12 3a3 3 0 0 0 -3 3v12a3 3 0 0 0 3 3",key:"svg-0"}],["path",{d:"M6 3a3 3 0 0 1 3 3v12a3 3 0 0 1 -3 3",key:"svg-1"}],["path",{d:"M13 7h7a1 1 0 0 1 1 1v8a1 1 0 0 1 -1 1h-7",key:"svg-2"}],["path",{d:"M5 7h-1a1 1 0 0 0 -1 1v8a1 1 0 0 0 1 1h1",key:"svg-3"}],["path",{d:"M17 12h.01",key:"svg-4"}],["path",{d:"M13 12h.01",key:"svg-5"}]])},97992:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},99270:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[4447,681,1658,1060,2305,5956],()=>a(2584));module.exports=r})();