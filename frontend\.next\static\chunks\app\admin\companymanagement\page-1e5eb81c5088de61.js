(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1318,2249],{1243:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},4516:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},13717:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},17580:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},19946:(e,t,a)=>{"use strict";a.d(t,{A:()=>p});var r=a(12115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,a)=>a?a.toUpperCase():t.toLowerCase()),l=e=>{let t=n(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return t.filter((e,t,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===t).join(" ").trim()},c=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,r.forwardRef)((e,t)=>{let{color:a="currentColor",size:s=24,strokeWidth:n=2,absoluteStrokeWidth:l,className:d="",children:p,iconNode:u,...m}=e;return(0,r.createElement)("svg",{ref:t,...o,width:s,height:s,stroke:a,strokeWidth:l?24*Number(n)/Number(s):n,className:i("lucide",d),...!p&&!c(m)&&{"aria-hidden":"true"},...m},[...u.map(e=>{let[t,a]=e;return(0,r.createElement)(t,a)}),...Array.isArray(p)?p:[p]])}),p=(e,t)=>{let a=(0,r.forwardRef)((a,n)=>{let{className:c,...o}=a;return(0,r.createElement)(d,{ref:n,iconNode:t,className:i("lucide-".concat(s(l(e))),"lucide-".concat(e),c),...o})});return a.displayName=l(e),a}},28835:(e,t,a)=>{"use strict";function r(){try{let e=localStorage.getItem("access");if(!e)return null;let t=localStorage.getItem("user");if(t){let e=JSON.parse(t);return e.id||e.user_id||null}if(e)try{let t=e.split(".")[1],a=JSON.parse(atob(t));return a.user_id||a.id||a.sub||null}catch(e){console.error("Error decoding JWT token:",e)}return null}catch(e){return console.error("Error getting user ID:",e),null}}function s(){return localStorage.getItem("access")}function n(){try{let e=localStorage.getItem("user");return e?JSON.parse(e):null}catch(e){return console.error("Error getting user data:",e),null}}a.d(t,{F6:()=>r,c4:()=>s,gL:()=>n})},29869:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},33786:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},35695:(e,t,a)=>{"use strict";var r=a(18999);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"usePathname")&&a.d(t,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},38564:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},40646:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},47924:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},48136:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},48937:(e,t,a)=>{"use strict";a.d(t,{C1:()=>l,Gu:()=>g,JT:()=>c,RC:()=>o,S0:()=>y,Y_:()=>p,bl:()=>u,dl:()=>x,eK:()=>i,fetchCompanies:()=>n,getCompanyStats:()=>d,jQ:()=>m,mm:()=>s,oY:()=>h});var r=a(37719);function s(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.page&&t.append("page",e.page),e.per_page&&t.append("per_page",e.per_page),e.tier&&"ALL"!==e.tier&&t.append("tier",e.tier),e.industry&&"ALL"!==e.industry&&t.append("industry",e.industry),e.campus_recruiting&&t.append("campus_recruiting",e.campus_recruiting),e.search&&t.append("search",e.search),e.sort&&t.append("sort",e.sort),t.append("_t",new Date().getTime());let a=t.toString(),s=["/api/v1/companies/".concat(a?"?".concat(a):""),"/api/v1/college/default-college/companies/".concat(a?"?".concat(a):"")];return r.A.get(s[0]).catch(e=>(console.log("Primary endpoint failed: ".concat(e.message,", trying fallback...")),r.A.get(s[1])))}async function n(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{console.log("Fetching companies from API...");let t=await s(e),a=[];if(t.data&&Array.isArray(t.data)?a=t.data:t.data&&t.data.results&&Array.isArray(t.data.results)?a=t.data.results:t.data&&t.data.data&&Array.isArray(t.data.data)&&(a=t.data.data),console.log("Retrieved ".concat(a.length," companies from API")),a.length>0){let e=await Promise.all(a.map(async e=>{try{let t=await l(e.id);return p(t.data)}catch(t){return console.log("Could not fetch details for company ".concat(e.id,":"),t),p(e)}}));return sessionStorage.setItem("companies_data",JSON.stringify(e)),sessionStorage.setItem("companies_timestamp",Date.now()),e}throw Error("No companies returned from API")}catch(t){console.error("Error fetching companies from API:",t);{let e=sessionStorage.getItem("companies_data"),t=sessionStorage.getItem("companies_timestamp");if(e&&t&&Date.now()-parseInt(t)<3e5)return console.log("Using cached company data (< 5 min old)"),JSON.parse(e)}console.log("Falling back to static company data");let{companies:e}=await a.e(1260).then(a.bind(a,21260));return e}}function l(e){let t=["/api/v1/company/".concat(e,"/"),"/api/v1/companies/".concat(e,"/"),"/api/v1/college/default-college/companies/".concat(e,"/")];return r.A.get(t[0]).catch(e=>(console.log("First company endpoint failed: ".concat(e.message,", trying second...")),r.A.get(t[1]).catch(e=>(console.log("Second company endpoint failed: ".concat(e.message,", trying third...")),r.A.get(t[2])))))}function i(e){let t=new FormData;return Object.keys(e).forEach(a=>{"logo"===a&&e[a]instanceof File?t.append(a,e[a]):null!==e[a]&&void 0!==e[a]&&t.append(a,e[a])}),r.A.post("/api/v1/companies/",t,{headers:{"Content-Type":"multipart/form-data"}})}function c(e,t){let a=new FormData;return Object.keys(t).forEach(e=>{"logo"===e&&t[e]instanceof File?a.append(e,t[e]):null!==t[e]&&void 0!==t[e]&&a.append(e,t[e])}),r.A.put("/api/v1/companies/".concat(e,"/"),a,{headers:{"Content-Type":"multipart/form-data"}})}function o(e){return r.A.delete("/api/v1/companies/".concat(e,"/"))}function d(){return r.A.get("/api/v1/companies/stats/")}function p(e){return{id:e.id,name:e.name,logo:e.logo||"https://via.placeholder.com/48x48/4285F4/FFFFFF?text=".concat(e.name.charAt(0)),description:e.description||"",industry:e.industry||"",size:e.size||"",founded:e.founded||"",location:e.location||"Location not specified",website:e.website||"",tier:e.tier||"Tier 3",campus_recruiting:e.campus_recruiting||!1,totalActiveJobs:e.total_active_jobs||0,totalApplicants:e.total_applicants||0,totalHired:e.total_hired||0,awaitedApproval:e.pending_approval||0}}function u(e){return r.A.get("/api/v1/companies/".concat(e,"/followers/count/"))}function m(e,t){return r.A.post("/api/v1/companies/".concat(e,"/followers/"),{user_id:t})}function h(e,t){return r.A.delete("/api/v1/companies/".concat(e,"/followers/"),{data:{user_id:t}})}function g(e,t){return r.A.get("/api/v1/companies/".concat(e,"/followers/status/?user_id=").concat(t))}function x(e){return r.A.get("/api/v1/users/".concat(e,"/following/"))}function y(){return r.A.get("/api/v1/companies/simple/").then(e=>{if(e.data&&e.data.success)return e.data;throw Error("Failed to fetch companies")}).catch(e=>(console.error("Error fetching companies:",e),{success:!1,data:[],count:0,error:e.message}))}},51976:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},54416:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},62525:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},69074:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},70213:(e,t,a)=>{Promise.resolve().then(a.bind(a,77662))},71007:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},75525:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},76517:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]])},77662:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>j});var r=a(95155),s=a(12115),n=a(35695),l=a(85339);let i=(0,a(19946).A)("circle-plus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]]);var c=a(48136),o=a(38564),d=a(51976),p=a(47924),u=a(13717),m=a(62525),h=a(4516),g=a(17580),x=a(69074),y=a(69327),f=a(48937),b=a(28835);function v(){let e=(0,n.useRouter)(),t=(0,n.useSearchParams)(),[a,v]=(0,s.useState)([]),[j,w]=(0,s.useState)(t.get("search")||""),[N,A]=(0,s.useState)(t.get("tier")||"ALL"),[k,S]=(0,s.useState)(t.get("industry")||"ALL"),[M,_]=(0,s.useState)(t.get("sort")||"name"),[C,L]=(0,s.useState)(new Set),[F,E]=(0,s.useState)(!0),[P,T]=(0,s.useState)(null),[z,I]=(0,s.useState)({total:0,tier1:0,tier2:0,tier3:0,campus_recruiting:0}),[R,J]=(0,s.useState)(parseInt(t.get("page"))||1),[H,O]=(0,s.useState)(1),[q,D]=(0,s.useState)(0),[U,V]=(0,s.useState)(8),W=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries({page:R.toString(),search:j,tier:N,industry:k,sort:M,...e}).forEach(e=>{let[a,r]=e;r&&"ALL"!==r&&""!==r&&"null"!==r&&null!==r&&t.set(a,r)});let a="".concat(window.location.pathname,"?").concat(t.toString());window.history.pushState({},"",a)},Y=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;try{E(!0),T(null);let t={page:e,page_size:U};j&&(t.search=j),"ALL"!==N&&(t.tier=N),"ALL"!==k&&(t.industry=k),M&&(t.ordering="name"===M?"name":"jobs"===M?"-total_active_jobs":"applicants"===M?"-total_applicants":"tier"===M?"tier":"name");let a=await y.SY.getCompanies(t),r=a.data.map(e=>({id:e.id,name:e.name||"",logo:e.logo||"https://via.placeholder.com/48x48/4285F4/FFFFFF?text=".concat((e.name||"C").charAt(0)),description:e.description||"",industry:e.industry||"",size:e.size||"Size not specified",founded:e.founded||"",location:e.location||"Location not specified",website:e.website||"",tier:e.tier||"Tier 3",campus_recruiting:e.campus_recruiting||!1,totalActiveJobs:e.total_active_jobs||0,totalApplicants:e.total_applicants||0,totalHired:e.total_hired||0,awaitedApproval:e.pending_approval||0}));v(r),J(e),O(a.pagination.total_pages),D(a.pagination.total_count),E(!1)}catch(e){console.error("Error fetching companies:",e),T("Failed to load companies. Please try again."),v([]),E(!1)}},Z=async()=>{try{let e=await y.SY.getCompanyStats();I(e)}catch(e){console.error("Error fetching company stats:",e),I({total:q,tier1:0,tier2:0,tier3:0,campus_recruiting:0})}};(0,s.useEffect)(()=>{Y(),Z();let e=(0,b.F6)();e&&K(e)},[]),(0,s.useEffect)(()=>{let e=()=>{let e=new URLSearchParams(window.location.search);J(parseInt(e.get("page"))||1),w(e.get("search")||""),A(e.get("tier")||"ALL"),S(e.get("industry")||"ALL"),_(e.get("sort")||"name"),Y(parseInt(e.get("page"))||1)};return window.addEventListener("popstate",e),()=>window.removeEventListener("popstate",e)},[]);let K=async e=>{try{let t=(await (0,f.dl)(e)).data.map(e=>e.id);L(new Set(t))}catch(e){console.error("Error loading followed companies:",e)}},$=e=>{J(e),W({page:e.toString()}),Y(e)};(0,s.useEffect)(()=>{Y(1)},[N,k,M,U]);let B=e=>{w(e)},G=()=>{J(1),W({search:j,page:"1"}),Y(1)},Q=e=>{A(e),J(1),W({tier:e,page:"1"}),Y(1)},X=e=>{S(e),J(1),W({industry:e,page:"1"}),Y(1)},ee=e=>{_(e),J(1),W({sort:e,page:"1"}),Y(1)},et=e=>{switch(e){case"Tier 1":return"bg-emerald-100 text-emerald-800 border-emerald-200";case"Tier 2":return"bg-blue-100 text-blue-800 border-blue-200";case"Tier 3":return"bg-purple-100 text-purple-800 border-purple-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}},ea=t=>{e.push("/admin/companymanagement/".concat(t))},er=(t,a)=>{t.stopPropagation(),e.push("/admin/companymanagement/edit/".concat(a))},es=async(e,t)=>{if(e.stopPropagation(),confirm("Are you sure you want to delete this company? This action cannot be undone."))try{await (0,f.RC)(t),Y(R),alert("Company deleted successfully")}catch(e){console.error("Error deleting company:",e),alert("Failed to delete company. Please try again.")}};return F?(0,r.jsxs)("div",{className:"h-full flex flex-col items-center justify-center",children:[(0,r.jsxs)("div",{className:"relative w-16 h-16 mb-8",children:[(0,r.jsx)("div",{className:"absolute inset-0 border-t-4 border-blue-500 rounded-full animate-spin"}),(0,r.jsx)("div",{className:"absolute inset-2 border-r-4 border-transparent rounded-full"})]}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"Loading companies..."})]}):P?(0,r.jsx)("div",{className:"h-full flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center max-w-md",children:[(0,r.jsx)("div",{className:"text-red-500 mb-4",children:(0,r.jsx)(l.A,{className:"w-16 h-16 mx-auto"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Error Loading Companies"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:P}),(0,r.jsx)("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Retry"})]})}):(0,r.jsx)("div",{className:"h-full overflow-y-auto",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("div",{className:"mb-6 flex justify-between items-center",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Company Management"}),(0,r.jsxs)("button",{onClick:()=>{e.push("/admin/companymanagement/create")},className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm",children:[(0,r.jsx)(i,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"Create New Company"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4 mb-8",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-100",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,r.jsx)(c.A,{className:"w-5 h-5 text-blue-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:z.total}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Total Companies"})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-100",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"p-2 bg-emerald-100 rounded-lg",children:(0,r.jsx)(o.A,{className:"w-5 h-5 text-emerald-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:z.tier1}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Tier 1"})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-100",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,r.jsx)(o.A,{className:"w-5 h-5 text-blue-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:z.tier2}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Tier 2"})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-100",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"p-2 bg-amber-100 rounded-lg",children:(0,r.jsx)(o.A,{className:"w-5 h-5 text-amber-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:z.tier3}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Tier 3"})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-100",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"p-2 bg-amber-100 rounded-lg",children:(0,r.jsx)(d.A,{className:"w-5 h-5 text-amber-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:C.size}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Following"})]})]})})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-100",children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,r.jsxs)("div",{className:"flex-1 flex gap-2",children:[(0,r.jsxs)("div",{className:"relative flex-1",children:[(0,r.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,r.jsx)("input",{type:"text",placeholder:"Search companies, industries...",value:j,onChange:e=>B(e.target.value),onKeyDown:e=>{"Enter"===e.key&&(e.preventDefault(),G())},className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,r.jsx)("button",{onClick:G,className:"px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 whitespace-nowrap transition-colors",children:"Search"})]}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsxs)("select",{value:N,onChange:e=>Q(e.target.value),className:"px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[120px]",children:[(0,r.jsx)("option",{value:"ALL",children:"All Tiers"}),(0,r.jsx)("option",{value:"Tier 1",children:"Tier 1"}),(0,r.jsx)("option",{value:"Tier 2",children:"Tier 2"}),(0,r.jsx)("option",{value:"Tier 3",children:"Tier 3"})]}),(0,r.jsxs)("select",{value:k,onChange:e=>X(e.target.value),className:"px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[150px]",children:[(0,r.jsx)("option",{value:"ALL",children:"All Industries"}),["Technology","Finance","Healthcare","Manufacturing","Consulting","E-commerce"].map(e=>(0,r.jsx)("option",{value:e,children:e},e))]}),(0,r.jsxs)("select",{value:M,onChange:e=>ee(e.target.value),className:"px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[130px]",children:[(0,r.jsx)("option",{value:"name",children:"Company A-Z"}),(0,r.jsx)("option",{value:"jobs",children:"Most Jobs"}),(0,r.jsx)("option",{value:"applicants",children:"Most Popular"}),(0,r.jsx)("option",{value:"tier",children:"Tier"})]})]})]}),(0,r.jsxs)("div",{className:"mt-4 text-sm text-gray-600",children:["Showing ",a.length," of ",q," companies (Page ",R," of ",H,")"]})]})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:a.length>0?a.map(e=>(0,r.jsxs)("div",{onClick:()=>ea(e.id),className:"bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-lg transition-all duration-200 cursor-pointer group",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-lg",children:e.name.charAt(0)})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900 group-hover:text-blue-600 transition-colors",children:e.name}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:e.industry})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("button",{onClick:t=>er(t,e.id),className:"p-2 rounded-lg transition-colors bg-blue-100 text-blue-600 hover:bg-blue-200",children:(0,r.jsx)(u.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{onClick:t=>es(t,e.id),className:"p-2 rounded-lg transition-colors bg-red-100 text-red-600 hover:bg-red-200",children:(0,r.jsx)(m.A,{className:"w-4 h-4"})})]})]}),(0,r.jsxs)("div",{className:"space-y-3 mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[(0,r.jsx)(h.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:e.location})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[(0,r.jsx)(g.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:e.size})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[(0,r.jsx)(x.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:e.founded?"Founded ".concat(e.founded):"Founded year not specified"})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,r.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium border ".concat(et(e.tier)),children:e.tier}),e.campus_recruiting&&(0,r.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-700 border border-green-200",children:"Campus Recruiting"})]}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-4 line-clamp-3",children:e.description}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4 pt-4 border-t border-gray-100",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-lg font-bold text-gray-900",children:e.totalActiveJobs||0}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Active Jobs"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-lg font-bold text-gray-900",children:e.totalApplicants||0}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Applicants"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-lg font-bold text-gray-900",children:e.totalHired||0}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Hired"})]})]})]},e.id)):(0,r.jsxs)("div",{className:"col-span-full flex flex-col items-center justify-center py-16",children:[(0,r.jsx)("div",{className:"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4",children:(0,r.jsx)(c.A,{className:"w-12 h-12 text-gray-400"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"No companies found"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"Try adjusting your search or filters"}),(0,r.jsx)("button",{onClick:()=>{w(""),A("ALL"),S("ALL"),_("name"),J(1),window.history.pushState({},"",window.location.pathname),Y(1)},className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Clear Filters"})]})}),a.length>0&&H>1&&(0,r.jsx)("div",{className:"mt-8 flex justify-center",children:(0,r.jsxs)("nav",{className:"flex items-center space-x-2","aria-label":"Pagination",children:[(0,r.jsx)("button",{onClick:()=>{if(R>1){let e=R-1;J(e),W({page:e.toString()}),Y(e)}},disabled:1===R,className:"px-4 py-2 rounded-md border ".concat(1===R?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 hover:bg-gray-100"),children:"Previous"}),[...Array(Math.min(5,H))].map((e,t)=>{let a;return a=H<=5||R<=3?t+1:R>=H-2?H-4+t:R-2+t,(0,r.jsx)("button",{onClick:()=>$(a),className:"w-10 h-10 flex items-center justify-center rounded-md ".concat(R===a?"bg-blue-500 text-white":"text-gray-700 hover:bg-gray-100"),"aria-current":R===a?"page":void 0,children:a},a)}),(0,r.jsx)("button",{onClick:()=>{if(R<H){let e=R+1;J(e),W({page:e.toString()}),Y(e)}},disabled:R===H,className:"px-4 py-2 rounded-md border ".concat(R===H?"text-gray-400 bg-gray-100 cursor-not-allowed":"text-gray-700 hover:bg-gray-100"),children:"Next"})]})})]})})}function j(){return(0,r.jsx)(s.Suspense,{fallback:(0,r.jsx)("div",{children:"Loading..."}),children:(0,r.jsx)(v,{})})}},81284:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},85339:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[3464,3983,9327,8441,1684,7358],()=>t(70213)),_N_E=e.O()}]);