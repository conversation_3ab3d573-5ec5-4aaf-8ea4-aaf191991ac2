(()=>{var t={};t.id=5957,t.ids=[5957],t.modules={22:(t,e,r)=>{var n=r(75254),o=r(20623),i=r(48169),a=r(40542),c=r(45058);t.exports=function(t){return"function"==typeof t?t:null==t?i:"object"==typeof t?a(t)?o(t[0],t[1]):n(t):c(t)}},658:(t,e,r)=>{t.exports=r(41547)(r(85718),"Map")},1427:(t,e,r)=>{Promise.resolve().then(r.bind(r,54578))},1566:(t,e,r)=>{var n=r(89167),o=r(658),i=r(30401),a=r(34772),c=r(17830),u=r(29395),l=r(12290),s="[object Map]",f="[object Promise]",p="[object Set]",h="[object WeakMap]",d="[object DataView]",y=l(n),v=l(o),m=l(i),b=l(a),g=l(c),x=u;(n&&x(new n(new ArrayBuffer(1)))!=d||o&&x(new o)!=s||i&&x(i.resolve())!=f||a&&x(new a)!=p||c&&x(new c)!=h)&&(x=function(t){var e=u(t),r="[object Object]"==e?t.constructor:void 0,n=r?l(r):"";if(n)switch(n){case y:return d;case v:return s;case m:return f;case b:return p;case g:return h}return e}),t.exports=x},1707:(t,e,r)=>{var n=r(35142),o=r(46436);t.exports=function(t,e){e=n(e,t);for(var r=0,i=e.length;null!=t&&r<i;)t=t[o(e[r++])];return r&&r==i?t:void 0}},1944:t=>{t.exports=function(){return!1}},2408:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t){r[++e]=t}),r}},2896:(t,e,r)=>{var n=r(81488),o=r(59467);t.exports=function(t,e){return null!=t&&o(t,e,n)}},2984:(t,e,r)=>{var n=r(49227);t.exports=function(t,e,r){for(var o=-1,i=t.length;++o<i;){var a=t[o],c=e(a);if(null!=c&&(void 0===u?c==c&&!n(c):r(c,u)))var u=c,l=a}return l}},3105:t=>{t.exports=function(t){return t.split("")}},3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4999:(t,e,r)=>{t.exports=r(85718).Uint8Array},5231:(t,e,r)=>{var n=r(29395),o=r(55048);t.exports=function(t){if(!o(t))return!1;var e=n(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}},5359:t=>{t.exports=function(t){var e=null==t?0:t.length;return e?t[e-1]:void 0}},5566:(t,e,r)=>{var n=r(41011),o=r(34117),i=r(66713),a=r(42403);t.exports=function(t){return function(e){var r=o(e=a(e))?i(e):void 0,c=r?r[0]:e.charAt(0),u=r?n(r,1).join(""):e.slice(1);return c[t]()+u}}},6053:t=>{var e=/\s/;t.exports=function(t){for(var r=t.length;r--&&e.test(t.charAt(r)););return r}},6330:t=>{t.exports=function(){return[]}},7383:(t,e,r)=>{var n=r(67009),o=r(32269),i=r(38428),a=r(55048);t.exports=function(t,e,r){if(!a(r))return!1;var c=typeof e;return("number"==c?!!(o(r)&&i(e,r.length)):"string"==c&&e in r)&&n(r[e],t)}},7651:(t,e,r)=>{var n=r(82038),o=r(52931),i=r(32269);t.exports=function(t){return i(t)?n(t):o(t)}},8336:(t,e,r)=>{var n=r(45803);t.exports=function(t,e){var r=t.__data__;return n(e)?r["string"==typeof e?"string":"hash"]:r.map}},8852:(t,e,r)=>{var n=r(1707);t.exports=function(t){return function(e){return n(e,t)}}},10090:(t,e,r)=>{var n=r(80458),o=r(89624),i=r(47282),a=i&&i.isTypedArray;t.exports=a?o(a):n},10653:(t,e,r)=>{var n=r(21456),o=r(63979),i=r(7651);t.exports=function(t){return n(t,i,o)}},10663:t=>{t.exports="object"==typeof global&&global&&global.Object===Object&&global},10722:(t,e,r)=>{Promise.resolve().then(r.bind(r,11527))},10846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11117:t=>{"use strict";var e=Object.prototype.hasOwnProperty,r="~";function n(){}function o(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function i(t,e,n,i,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var c=new o(n,i||t,a),u=r?r+e:e;return t._events[u]?t._events[u].fn?t._events[u]=[t._events[u],c]:t._events[u].push(c):(t._events[u]=c,t._eventsCount++),t}function a(t,e){0==--t._eventsCount?t._events=new n:delete t._events[e]}function c(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),c.prototype.eventNames=function(){var t,n,o=[];if(0===this._eventsCount)return o;for(n in t=this._events)e.call(t,n)&&o.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(t)):o},c.prototype.listeners=function(t){var e=r?r+t:t,n=this._events[e];if(!n)return[];if(n.fn)return[n.fn];for(var o=0,i=n.length,a=Array(i);o<i;o++)a[o]=n[o].fn;return a},c.prototype.listenerCount=function(t){var e=r?r+t:t,n=this._events[e];return n?n.fn?1:n.length:0},c.prototype.emit=function(t,e,n,o,i,a){var c=r?r+t:t;if(!this._events[c])return!1;var u,l,s=this._events[c],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(t,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,e),!0;case 3:return s.fn.call(s.context,e,n),!0;case 4:return s.fn.call(s.context,e,n,o),!0;case 5:return s.fn.call(s.context,e,n,o,i),!0;case 6:return s.fn.call(s.context,e,n,o,i,a),!0}for(l=1,u=Array(f-1);l<f;l++)u[l-1]=arguments[l];s.fn.apply(s.context,u)}else{var p,h=s.length;for(l=0;l<h;l++)switch(s[l].once&&this.removeListener(t,s[l].fn,void 0,!0),f){case 1:s[l].fn.call(s[l].context);break;case 2:s[l].fn.call(s[l].context,e);break;case 3:s[l].fn.call(s[l].context,e,n);break;case 4:s[l].fn.call(s[l].context,e,n,o);break;default:if(!u)for(p=1,u=Array(f-1);p<f;p++)u[p-1]=arguments[p];s[l].fn.apply(s[l].context,u)}}return!0},c.prototype.on=function(t,e,r){return i(this,t,e,r,!1)},c.prototype.once=function(t,e,r){return i(this,t,e,r,!0)},c.prototype.removeListener=function(t,e,n,o){var i=r?r+t:t;if(!this._events[i])return this;if(!e)return a(this,i),this;var c=this._events[i];if(c.fn)c.fn!==e||o&&!c.once||n&&c.context!==n||a(this,i);else{for(var u=0,l=[],s=c.length;u<s;u++)(c[u].fn!==e||o&&!c[u].once||n&&c[u].context!==n)&&l.push(c[u]);l.length?this._events[i]=1===l.length?l[0]:l:a(this,i)}return this},c.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&a(this,e)):(this._events=new n,this._eventsCount=0),this},c.prototype.off=c.prototype.removeListener,c.prototype.addListener=c.prototype.on,c.prefixed=r,c.EventEmitter=c,t.exports=c},11424:(t,e,r)=>{var n=r(47603);t.exports=r(66400)(n)},11527:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>m});var n=r(60687),o=r(43210),i=r(24664),a=r(98848),c=r(9535),u=r(37325),l=r(81080),s=r(95994),f=r(80556),p=r(90910),h=r(81172),d=r(20798),y=r(58869),v=r(53411);function m({children:t}){let[e,r]=(0,o.useState)(""),m=[{items:[{title:"Dashboard",href:"/admin/dashboard",icon:(0,n.jsx)(a.A,{})},{title:"Jobs",href:"/admin/jobs",icon:(0,n.jsx)(c.A,{})},{title:"Applications",href:"/admin/applications",icon:(0,n.jsx)(u.A,{})},{title:"Posts",href:"/admin/posts",icon:(0,n.jsx)(d.A,{className:"w-5 h-5"})},{title:"Student Management",href:"/admin/student-management",icon:(0,n.jsx)(y.A,{className:"w-5 h-5"})},{title:"Student Analytics",href:"/admin/analytics",icon:(0,n.jsx)(v.A,{className:"w-5 h-5"})},{title:"Company Management",href:"/admin/companymanagement",icon:(0,n.jsx)(l.A,{})},{title:"Forms",href:"/admin/form",icon:(0,n.jsx)(s.A,{})}]}],b=[{title:"My Profile",href:"/admin/profile",icon:(0,n.jsx)(f.A,{})},{title:"Settings",href:"../settings",icon:(0,n.jsx)(p.A,{})},{title:"Contact Support",href:"/admin/helpandsupport",icon:(0,n.jsx)(h.A,{})}];return(0,n.jsx)("div",{className:"h-screen bg-gray-50 overflow-hidden",children:(0,n.jsxs)("div",{className:"flex h-full",children:[(0,n.jsx)(i.A,{sections:m,bottomItems:b,defaultExpanded:!1,navbarHeight:"0",className:"z-20"}),(0,n.jsx)("div",{className:"flex-1 p-6 ml-20 overflow-y-auto",children:(0,n.jsx)("div",{className:"bg-white rounded-xl shadow-md p-6 min-h-full text-black",children:t})})]})})}},11539:(t,e,r)=>{var n=r(37643),o=r(55048),i=r(49227),a=0/0,c=/^[-+]0x[0-9a-f]+$/i,u=/^0b[01]+$/i,l=/^0o[0-7]+$/i,s=parseInt;t.exports=function(t){if("number"==typeof t)return t;if(i(t))return a;if(o(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=o(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=n(t);var r=u.test(t);return r||l.test(t)?s(t.slice(2),r?2:8):c.test(t)?a:+t}},12290:t=>{var e=Function.prototype.toString;t.exports=function(t){if(null!=t){try{return e.call(t)}catch(t){}try{return t+""}catch(t){}}return""}},12344:(t,e,r)=>{t.exports=r(65984)()},14675:t=>{t.exports=function(t){return function(){return t}}},15451:(t,e,r)=>{var n=r(29395),o=r(27467);t.exports=function(t){return o(t)&&"[object Arguments]"==n(t)}},15871:(t,e,r)=>{var n=r(36341),o=r(27467);t.exports=function t(e,r,i,a,c){return e===r||(null!=e&&null!=r&&(o(e)||o(r))?n(e,r,i,a,t,c):e!=e&&r!=r)}},15883:(t,e,r)=>{var n=r(2984),o=r(46063),i=r(48169);t.exports=function(t){return t&&t.length?n(t,i,o):void 0}},15909:(t,e,r)=>{var n=r(87506),o=r(66930),i=r(658);t.exports=function(){this.size=0,this.__data__={hash:new n,map:new(i||o),string:new n}}},16854:t=>{t.exports=function(t){return this.__data__.has(t)}},17518:(t,e,r)=>{var n=r(21367),o=r(1707),i=r(22),a=r(54765),c=r(43378),u=r(89624),l=r(65727),s=r(48169),f=r(40542);t.exports=function(t,e,r){e=e.length?n(e,function(t){return f(t)?function(e){return o(e,1===t.length?t[0]:t)}:t}):[s];var p=-1;return e=n(e,u(i)),c(a(t,function(t,r,o){return{criteria:n(e,function(e){return e(t)}),index:++p,value:t}}),function(t,e){return l(t,e,r)})}},17830:(t,e,r)=>{t.exports=r(41547)(r(85718),"WeakMap")},18108:(t,e,r)=>{"use strict";r.r(e),r.d(e,{GlobalError:()=>a.a,__next_app__:()=>f,pages:()=>s,routeModule:()=>p,tree:()=>l});var n=r(65239),o=r(48088),i=r(88170),a=r.n(i),c=r(30893),u={};for(let t in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(t)&&(u[t]=()=>c[t]);r.d(e,u);let l={children:["",{children:["admin",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,27009)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\dashboard\\page.jsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,23697)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\layout.jsx"],metadata:{icon:[async t=>(await Promise.resolve().then(r.bind(r,70440))).default(t)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,75535)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\layout.js"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async t=>(await Promise.resolve().then(r.bind(r,70440))).default(t)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,s=["C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\dashboard\\page.jsx"],f={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/admin/dashboard/page",pathname:"/admin/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},18234:(t,e,r)=>{var n=r(91290),o=r(22),i=r(84482),a=Math.max;t.exports=function(t,e,r){var c=null==t?0:t.length;if(!c)return -1;var u=null==r?0:i(r);return u<0&&(u=a(c+u,0)),n(t,o(e,3),u)}},19121:t=>{"use strict";t.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19976:(t,e,r)=>{var n=r(8336);t.exports=function(t,e){var r=n(this,t),o=r.size;return r.set(t,e),this.size+=+(r.size!=o),this}},20540:(t,e,r)=>{var n=r(55048),o=r(70151),i=r(11539),a=Math.max,c=Math.min;t.exports=function(t,e,r){var u,l,s,f,p,h,d=0,y=!1,v=!1,m=!0;if("function"!=typeof t)throw TypeError("Expected a function");function b(e){var r=u,n=l;return u=l=void 0,d=e,f=t.apply(n,r)}function g(t){var r=t-h,n=t-d;return void 0===h||r>=e||r<0||v&&n>=s}function x(){var t,r,n,i=o();if(g(i))return w(i);p=setTimeout(x,(t=i-h,r=i-d,n=e-t,v?c(n,s-r):n))}function w(t){return(p=void 0,m&&u)?b(t):(u=l=void 0,f)}function O(){var t,r=o(),n=g(r);if(u=arguments,l=this,h=r,n){if(void 0===p)return d=t=h,p=setTimeout(x,e),y?b(t):f;if(v)return clearTimeout(p),p=setTimeout(x,e),b(h)}return void 0===p&&(p=setTimeout(x,e)),f}return e=i(e)||0,n(r)&&(y=!!r.leading,s=(v="maxWait"in r)?a(i(r.maxWait)||0,e):s,m="trailing"in r?!!r.trailing:m),O.cancel=function(){void 0!==p&&clearTimeout(p),d=0,u=h=l=p=void 0},O.flush=function(){return void 0===p?f:w(o())},O}},20623:(t,e,r)=>{var n=r(15871),o=r(40491),i=r(2896),a=r(67619),c=r(34883),u=r(41132),l=r(46436);t.exports=function(t,e){return a(t)&&c(e)?u(l(t),e):function(r){var a=o(r,t);return void 0===a&&a===e?i(r,t):n(e,a,3)}}},20798:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("megaphone",[["path",{d:"m3 11 18-5v12L3 14v-3z",key:"n962bs"}],["path",{d:"M11.6 16.8a3 3 0 1 1-5.8-1.6",key:"1yl0tm"}]])},21367:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,o=Array(n);++r<n;)o[r]=e(t[r],r,t);return o}},21456:(t,e,r)=>{var n=r(41693),o=r(40542);t.exports=function(t,e,r){var i=e(t);return o(t)?i:n(i,r(t))}},21592:(t,e,r)=>{var n=r(42205),o=r(61837);t.exports=function(t,e){return n(o(t,e),1)}},21630:(t,e,r)=>{var n=r(10653),o=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,i,a,c){var u=1&r,l=n(t),s=l.length;if(s!=n(e).length&&!u)return!1;for(var f=s;f--;){var p=l[f];if(!(u?p in e:o.call(e,p)))return!1}var h=c.get(t),d=c.get(e);if(h&&d)return h==e&&d==t;var y=!0;c.set(t,e),c.set(e,t);for(var v=u;++f<s;){var m=t[p=l[f]],b=e[p];if(i)var g=u?i(b,m,p,e,t,c):i(m,b,p,t,e,c);if(!(void 0===g?m===b||a(m,b,r,i,c):g)){y=!1;break}v||(v="constructor"==p)}if(y&&!v){var x=t.constructor,w=e.constructor;x!=w&&"constructor"in t&&"constructor"in e&&!("function"==typeof x&&x instanceof x&&"function"==typeof w&&w instanceof w)&&(y=!1)}return c.delete(t),c.delete(e),y}},22964:(t,e,r)=>{t.exports=r(23729)(r(18234))},23697:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\layout.jsx","default")},23729:(t,e,r)=>{var n=r(22),o=r(32269),i=r(7651);t.exports=function(t){return function(e,r,a){var c=Object(e);if(!o(e)){var u=n(r,3);e=i(e),r=function(t){return u(c[t],t,c)}}var l=t(e,r,a);return l>-1?c[u?e[l]:l]:void 0}}},24987:(t,e,r)=>{Promise.resolve().then(r.bind(r,27009))},25118:t=>{t.exports=function(t){return this.__data__.has(t)}},25541:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},27006:(t,e,r)=>{var n=r(46328),o=r(99525),i=r(58276);t.exports=function(t,e,r,a,c,u){var l=1&r,s=t.length,f=e.length;if(s!=f&&!(l&&f>s))return!1;var p=u.get(t),h=u.get(e);if(p&&h)return p==e&&h==t;var d=-1,y=!0,v=2&r?new n:void 0;for(u.set(t,e),u.set(e,t);++d<s;){var m=t[d],b=e[d];if(a)var g=l?a(b,m,d,e,t,u):a(m,b,d,t,e,u);if(void 0!==g){if(g)continue;y=!1;break}if(v){if(!o(e,function(t,e){if(!i(v,e)&&(m===t||c(m,t,r,a,u)))return v.push(e)})){y=!1;break}}else if(!(m===b||c(m,b,r,a,u))){y=!1;break}}return u.delete(t),u.delete(e),y}},27009:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\dashboard\\page.jsx","default")},27467:t=>{t.exports=function(t){return null!=t&&"object"==typeof t}},27669:t=>{t.exports=function(){this.__data__=[],this.size=0}},28837:(t,e,r)=>{var n=r(57797),o=Array.prototype.splice;t.exports=function(t){var e=this.__data__,r=n(e,t);return!(r<0)&&(r==e.length-1?e.pop():o.call(e,r,1),--this.size,!0)}},28977:(t,e,r)=>{var n=r(11539),o=1/0;t.exports=function(t){return t?(t=n(t))===o||t===-o?(t<0?-1:1)*17976931348623157e292:t==t?t:0:0===t?t:0}},29205:(t,e,r)=>{var n=r(8336);t.exports=function(t){var e=n(this,t).delete(t);return this.size-=!!e,e}},29294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29395:(t,e,r)=>{var n=r(79474),o=r(70222),i=r(84713),a=n?n.toStringTag:void 0;t.exports=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":a&&a in Object(t)?o(t):i(t)}},29508:(t,e,r)=>{var n=r(8336);t.exports=function(t){return n(this,t).get(t)}},30316:(t,e,r)=>{var n=r(67554);t.exports=function(t,e){var r=!0;return n(t,function(t,n,o){return r=!!e(t,n,o)}),r}},30401:(t,e,r)=>{t.exports=r(41547)(r(85718),"Promise")},30854:(t,e,r)=>{var n=r(66930),o=r(658),i=r(95746);t.exports=function(t,e){var r=this.__data__;if(r instanceof n){var a=r.__data__;if(!o||a.length<199)return a.push([t,e]),this.size=++r.size,this;r=this.__data__=new i(a)}return r.set(t,e),this.size=r.size,this}},32269:(t,e,r)=>{var n=r(5231),o=r(69619);t.exports=function(t){return null!=t&&o(t.length)&&!n(t)}},33873:t=>{"use strict";t.exports=require("path")},34117:t=>{var e=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");t.exports=function(t){return e.test(t)}},34452:t=>{"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},34746:t=>{t.exports=function(t){return this.__data__.get(t)}},34772:(t,e,r)=>{t.exports=r(41547)(r(85718),"Set")},34821:t=>{t.exports=function(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}},34883:(t,e,r)=>{var n=r(55048);t.exports=function(t){return t==t&&!n(t)}},34990:(t,e,r)=>{t.exports=r(87321)()},35142:(t,e,r)=>{var n=r(40542),o=r(67619),i=r(51449),a=r(42403);t.exports=function(t,e){return n(t)?t:o(t,e)?[t]:i(a(t))}},35163:(t,e,r)=>{var n=r(15451),o=r(27467),i=Object.prototype,a=i.hasOwnProperty,c=i.propertyIsEnumerable;t.exports=n(function(){return arguments}())?n:function(t){return o(t)&&a.call(t,"callee")&&!c.call(t,"callee")}},35697:(t,e,r)=>{var n=r(79474),o=r(4999),i=r(67009),a=r(27006),c=r(59774),u=r(2408),l=n?n.prototype:void 0,s=l?l.valueOf:void 0;t.exports=function(t,e,r,n,l,f,p){switch(r){case"[object DataView]":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)break;t=t.buffer,e=e.buffer;case"[object ArrayBuffer]":if(t.byteLength!=e.byteLength||!f(new o(t),new o(e)))break;return!0;case"[object Boolean]":case"[object Date]":case"[object Number]":return i(+t,+e);case"[object Error]":return t.name==e.name&&t.message==e.message;case"[object RegExp]":case"[object String]":return t==e+"";case"[object Map]":var h=c;case"[object Set]":var d=1&n;if(h||(h=u),t.size!=e.size&&!d)break;var y=p.get(t);if(y)return y==e;n|=2,p.set(t,e);var v=a(h(t),h(e),n,l,f,p);return p.delete(t),v;case"[object Symbol]":if(s)return s.call(t)==s.call(e)}return!1}},35800:(t,e,r)=>{var n=r(57797);t.exports=function(t){return n(this.__data__,t)>-1}},36315:(t,e,r)=>{var n=r(22),o=r(92662);t.exports=function(t,e){return t&&t.length?o(t,n(e,2)):[]}},36341:(t,e,r)=>{var n=r(67200),o=r(27006),i=r(35697),a=r(21630),c=r(1566),u=r(40542),l=r(80329),s=r(10090),f="[object Arguments]",p="[object Array]",h="[object Object]",d=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,y,v,m){var b=u(t),g=u(e),x=b?p:c(t),w=g?p:c(e);x=x==f?h:x,w=w==f?h:w;var O=x==h,j=w==h,S=x==w;if(S&&l(t)){if(!l(e))return!1;b=!0,O=!1}if(S&&!O)return m||(m=new n),b||s(t)?o(t,e,r,y,v,m):i(t,e,x,r,y,v,m);if(!(1&r)){var P=O&&d.call(t,"__wrapped__"),A=j&&d.call(e,"__wrapped__");if(P||A){var k=P?t.value():t,E=A?e.value():e;return m||(m=new n),v(k,E,r,y,m)}}return!!S&&(m||(m=new n),a(t,e,r,y,v,m))}},36959:t=>{t.exports=function(){}},37325:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});var n=(0,r(6445).A)("outline","clipboard-list","IconClipboardList",[["path",{d:"M9 5h-2a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-12a2 2 0 0 0 -2 -2h-2",key:"svg-0"}],["path",{d:"M9 3m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z",key:"svg-1"}],["path",{d:"M9 12l.01 0",key:"svg-2"}],["path",{d:"M13 12l2 0",key:"svg-3"}],["path",{d:"M9 16l.01 0",key:"svg-4"}],["path",{d:"M13 16l2 0",key:"svg-5"}]])},37456:t=>{t.exports=function(t){return null==t}},37575:(t,e,r)=>{var n=r(66930);t.exports=function(){this.__data__=new n,this.size=0}},37643:(t,e,r)=>{var n=r(6053),o=/^\s+/;t.exports=function(t){return t?t.slice(0,n(t)+1).replace(o,""):t}},38404:(t,e,r)=>{var n=r(29395),o=r(65932),i=r(27467),a=Object.prototype,c=Function.prototype.toString,u=a.hasOwnProperty,l=c.call(Object);t.exports=function(t){if(!i(t)||"[object Object]"!=n(t))return!1;var e=o(t);if(null===e)return!0;var r=u.call(e,"constructor")&&e.constructor;return"function"==typeof r&&r instanceof r&&c.call(r)==l}},38428:t=>{var e=/^(?:0|[1-9]\d*)$/;t.exports=function(t,r){var n=typeof t;return!!(r=null==r?0x1fffffffffffff:r)&&("number"==n||"symbol"!=n&&e.test(t))&&t>-1&&t%1==0&&t<r}},39672:(t,e,r)=>{var n=r(58141);t.exports=function(t,e){var r=this.__data__;return this.size+=+!this.has(t),r[t]=n&&void 0===e?"__lodash_hash_undefined__":e,this}},39774:t=>{t.exports=function(t){return t!=t}},40491:(t,e,r)=>{var n=r(1707);t.exports=function(t,e,r){var o=null==t?void 0:n(t,e);return void 0===o?r:o}},40542:t=>{t.exports=Array.isArray},41011:(t,e,r)=>{var n=r(41353);t.exports=function(t,e,r){var o=t.length;return r=void 0===r?o:r,!e&&r>=o?t:n(t,e,r)}},41132:t=>{t.exports=function(t,e){return function(r){return null!=r&&r[t]===e&&(void 0!==e||t in Object(r))}}},41157:(t,e,r)=>{var n=r(91928);t.exports=function(t,e,r){"__proto__"==e&&n?n(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}},41312:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},41353:t=>{t.exports=function(t,e,r){var n=-1,o=t.length;e<0&&(e=-e>o?0:o+e),(r=r>o?o:r)<0&&(r+=o),o=e>r?0:r-e>>>0,e>>>=0;for(var i=Array(o);++n<o;)i[n]=t[n+e];return i}},41547:(t,e,r)=>{var n=r(61548),o=r(90851);t.exports=function(t,e){var r=o(t,e);return n(r)?r:void 0}},41693:t=>{t.exports=function(t,e){for(var r=-1,n=e.length,o=t.length;++r<n;)t[o+r]=e[r];return t}},42082:t=>{t.exports=function(t){return function(e){return null==e?void 0:e[t]}}},42205:(t,e,r)=>{var n=r(41693),o=r(85450);t.exports=function t(e,r,i,a,c){var u=-1,l=e.length;for(i||(i=o),c||(c=[]);++u<l;){var s=e[u];r>0&&i(s)?r>1?t(s,r-1,i,a,c):n(c,s):a||(c[c.length]=s)}return c}},42403:(t,e,r)=>{var n=r(80195);t.exports=function(t){return null==t?"":n(t)}},43378:t=>{t.exports=function(t,e){var r=t.length;for(t.sort(e);r--;)t[r]=t[r].value;return t}},45058:(t,e,r)=>{var n=r(42082),o=r(8852),i=r(67619),a=r(46436);t.exports=function(t){return i(t)?n(a(t)):o(t)}},45603:(t,e,r)=>{var n=r(20540),o=r(55048);t.exports=function(t,e,r){var i=!0,a=!0;if("function"!=typeof t)throw TypeError("Expected a function");return o(r)&&(i="leading"in r?!!r.leading:i,a="trailing"in r?!!r.trailing:a),n(t,e,{leading:i,maxWait:e,trailing:a})}},45803:t=>{t.exports=function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t}},46063:t=>{t.exports=function(t,e){return t<e}},46229:(t,e,r)=>{var n=r(48169),o=r(66354),i=r(11424);t.exports=function(t,e){return i(o(t,e,n),t+"")}},46328:(t,e,r)=>{var n=r(95746),o=r(89185),i=r(16854);function a(t){var e=-1,r=null==t?0:t.length;for(this.__data__=new n;++e<r;)this.add(t[e])}a.prototype.add=a.prototype.push=o,a.prototype.has=i,t.exports=a},46436:(t,e,r)=>{var n=r(49227),o=1/0;t.exports=function(t){if("string"==typeof t||n(t))return t;var e=t+"";return"0"==e&&1/t==-o?"-0":e}},47212:(t,e,r)=>{var n=r(87270),o=r(30316),i=r(22),a=r(40542),c=r(7383);t.exports=function(t,e,r){var u=a(t)?n:o;return r&&c(t,e,r)&&(e=void 0),u(t,i(e,3))}},47282:(t,e,r)=>{t=r.nmd(t);var n=r(10663),o=e&&!e.nodeType&&e,i=o&&t&&!t.nodeType&&t,a=i&&i.exports===o&&n.process,c=function(){try{var t=i&&i.require&&i.require("util").types;if(t)return t;return a&&a.binding&&a.binding("util")}catch(t){}}();t.exports=c},47603:(t,e,r)=>{var n=r(14675),o=r(91928),i=r(48169);t.exports=o?function(t,e){return o(t,"toString",{configurable:!0,enumerable:!1,value:n(e),writable:!0})}:i},48169:t=>{t.exports=function(t){return t}},48173:(t,e,r)=>{Promise.resolve().then(r.bind(r,23697))},48385:t=>{var e="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",n="\ud83c[\udffb-\udfff]",o="[^"+e+"]",i="(?:\ud83c[\udde6-\uddff]){2}",a="[\ud800-\udbff][\udc00-\udfff]",c="(?:"+r+"|"+n+")?",u="[\\ufe0e\\ufe0f]?",l="(?:\\u200d(?:"+[o,i,a].join("|")+")"+u+c+")*",s=RegExp(n+"(?="+n+")|"+("(?:"+[o+r+"?",r,i,a,"["+e+"]"].join("|"))+")"+(u+c+l),"g");t.exports=function(t){return t.match(s)||[]}},49227:(t,e,r)=>{var n=r(29395),o=r(27467);t.exports=function(t){return"symbol"==typeof t||o(t)&&"[object Symbol]"==n(t)}},51449:(t,e,r)=>{var n=r(85745),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g;t.exports=n(function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(o,function(t,r,n,o){e.push(n?o.replace(i,"$1"):r||t)}),e})},52599:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,o=0,i=[];++r<n;){var a=t[r];e(a,r,t)&&(i[o++]=a)}return i}},52823:(t,e,r)=>{var n=r(85406),o=function(){var t=/[^.]+$/.exec(n&&n.keys&&n.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();t.exports=function(t){return!!o&&o in t}},52931:(t,e,r)=>{var n=r(77834),o=r(89605),i=Object.prototype.hasOwnProperty;t.exports=function(t){if(!n(t))return o(t);var e=[];for(var r in Object(t))i.call(t,r)&&"constructor"!=r&&e.push(r);return e}},53411:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},54578:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>dR});var n={};r.r(n),r.d(n,{scaleBand:()=>nO,scaleDiverging:()=>function t(){var e=iu(cP()(oQ));return e.copy=function(){return cO(e,t())},nv.apply(e,arguments)},scaleDivergingLog:()=>function t(){var e=ib(cP()).domain([.1,1,10]);return e.copy=function(){return cO(e,t()).base(e.base())},nv.apply(e,arguments)},scaleDivergingPow:()=>cA,scaleDivergingSqrt:()=>ck,scaleDivergingSymlog:()=>function t(){var e=iw(cP());return e.copy=function(){return cO(e,t()).constant(e.constant())},nv.apply(e,arguments)},scaleIdentity:()=>function t(e){var r;function n(t){return null==t||isNaN(t*=1)?r:t}return n.invert=n,n.domain=n.range=function(t){return arguments.length?(e=Array.from(t,oZ),n):e.slice()},n.unknown=function(t){return arguments.length?(r=t,n):r},n.copy=function(){return t(e).unknown(r)},e=arguments.length?Array.from(e,oZ):[0,1],iu(n)},scaleImplicit:()=>nx,scaleLinear:()=>il,scaleLog:()=>function t(){let e=ib(o3()).domain([1,10]);return e.copy=()=>o5(e,t()).base(e.base()),ny.apply(e,arguments),e},scaleOrdinal:()=>nw,scalePoint:()=>nj,scalePow:()=>iA,scaleQuantile:()=>function t(){var e,r=[],n=[],o=[];function i(){var t=0,e=Math.max(1,n.length);for(o=Array(e-1);++t<e;)o[t-1]=function(t,e,r=oa){if(!(!(n=t.length)||isNaN(e*=1))){if(e<=0||n<2)return+r(t[0],0,t);if(e>=1)return+r(t[n-1],n-1,t);var n,o=(n-1)*e,i=Math.floor(o),a=+r(t[i],i,t);return a+(r(t[i+1],i+1,t)-a)*(o-i)}}(r,t/e);return a}function a(t){return null==t||isNaN(t*=1)?e:n[ou(o,t)]}return a.invertExtent=function(t){var e=n.indexOf(t);return e<0?[NaN,NaN]:[e>0?o[e-1]:r[0],e<o.length?o[e]:r[r.length-1]]},a.domain=function(t){if(!arguments.length)return r.slice();for(let e of(r=[],t))null==e||isNaN(e*=1)||r.push(e);return r.sort(or),i()},a.range=function(t){return arguments.length?(n=Array.from(t),i()):n.slice()},a.unknown=function(t){return arguments.length?(e=t,a):e},a.quantiles=function(){return o.slice()},a.copy=function(){return t().domain(r).range(n).unknown(e)},ny.apply(a,arguments)},scaleQuantize:()=>function t(){var e,r=0,n=1,o=1,i=[.5],a=[0,1];function c(t){return null!=t&&t<=t?a[ou(i,t,0,o)]:e}function u(){var t=-1;for(i=Array(o);++t<o;)i[t]=((t+1)*n-(t-o)*r)/(o+1);return c}return c.domain=function(t){return arguments.length?([r,n]=t,r*=1,n*=1,u()):[r,n]},c.range=function(t){return arguments.length?(o=(a=Array.from(t)).length-1,u()):a.slice()},c.invertExtent=function(t){var e=a.indexOf(t);return e<0?[NaN,NaN]:e<1?[r,i[0]]:e>=o?[i[o-1],n]:[i[e-1],i[e]]},c.unknown=function(t){return arguments.length&&(e=t),c},c.thresholds=function(){return i.slice()},c.copy=function(){return t().domain([r,n]).range(a).unknown(e)},ny.apply(iu(c),arguments)},scaleRadial:()=>function t(){var e,r=o4(),n=[0,1],o=!1;function i(t){var n,i=Math.sign(n=r(t))*Math.sqrt(Math.abs(n));return isNaN(i)?e:o?Math.round(i):i}return i.invert=function(t){return r.invert(iE(t))},i.domain=function(t){return arguments.length?(r.domain(t),i):r.domain()},i.range=function(t){return arguments.length?(r.range((n=Array.from(t,oZ)).map(iE)),i):n.slice()},i.rangeRound=function(t){return i.range(t).round(!0)},i.round=function(t){return arguments.length?(o=!!t,i):o},i.clamp=function(t){return arguments.length?(r.clamp(t),i):r.clamp()},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return t(r.domain(),n).round(o).clamp(r.clamp()).unknown(e)},ny.apply(i,arguments),iu(i)},scaleSequential:()=>function t(){var e=iu(cw()(oQ));return e.copy=function(){return cO(e,t())},nv.apply(e,arguments)},scaleSequentialLog:()=>function t(){var e=ib(cw()).domain([1,10]);return e.copy=function(){return cO(e,t()).base(e.base())},nv.apply(e,arguments)},scaleSequentialPow:()=>cj,scaleSequentialQuantile:()=>function t(){var e=[],r=oQ;function n(t){if(null!=t&&!isNaN(t*=1))return r((ou(e,t,1)-1)/(e.length-1))}return n.domain=function(t){if(!arguments.length)return e.slice();for(let r of(e=[],t))null==r||isNaN(r*=1)||e.push(r);return e.sort(or),n},n.interpolator=function(t){return arguments.length?(r=t,n):r},n.range=function(){return e.map((t,n)=>r(n/(e.length-1)))},n.quantiles=function(t){return Array.from({length:t+1},(r,n)=>(function(t,e,r){if(!(!(n=(t=Float64Array.from(function*(t,e){if(void 0===e)for(let e of t)null!=e&&(e*=1)>=e&&(yield e);else{let r=-1;for(let n of t)null!=(n=e(n,++r,t))&&(n*=1)>=n&&(yield n)}}(t,void 0))).length)||isNaN(e*=1))){if(e<=0||n<2)return i_(t);if(e>=1)return iM(t);var n,o=(n-1)*e,i=Math.floor(o),a=iM((function t(e,r,n=0,o=1/0,i){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),o=Math.floor(Math.min(e.length-1,o)),!(n<=r&&r<=o))return e;for(i=void 0===i?iT:function(t=or){if(t===or)return iT;if("function"!=typeof t)throw TypeError("compare is not a function");return(e,r)=>{let n=t(e,r);return n||0===n?n:(0===t(r,r))-(0===t(e,e))}}(i);o>n;){if(o-n>600){let a=o-n+1,c=r-n+1,u=Math.log(a),l=.5*Math.exp(2*u/3),s=.5*Math.sqrt(u*l*(a-l)/a)*(c-a/2<0?-1:1),f=Math.max(n,Math.floor(r-c*l/a+s)),p=Math.min(o,Math.floor(r+(a-c)*l/a+s));t(e,r,f,p,i)}let a=e[r],c=n,u=o;for(iC(e,n,r),i(e[o],a)>0&&iC(e,n,o);c<u;){for(iC(e,c,u),++c,--u;0>i(e[c],a);)++c;for(;i(e[u],a)>0;)--u}0===i(e[n],a)?iC(e,n,u):iC(e,++u,o),u<=r&&(n=u+1),r<=u&&(o=u-1)}return e})(t,i).subarray(0,i+1));return a+(i_(t.subarray(i+1))-a)*(o-i)}})(e,n/t))},n.copy=function(){return t(r).domain(e)},nv.apply(n,arguments)},scaleSequentialSqrt:()=>cS,scaleSequentialSymlog:()=>function t(){var e=iw(cw());return e.copy=function(){return cO(e,t()).constant(e.constant())},nv.apply(e,arguments)},scaleSqrt:()=>ik,scaleSymlog:()=>function t(){var e=iw(o3());return e.copy=function(){return o5(e,t()).constant(e.constant())},ny.apply(e,arguments)},scaleThreshold:()=>function t(){var e,r=[.5],n=[0,1],o=1;function i(t){return null!=t&&t<=t?n[ou(r,t,0,o)]:e}return i.domain=function(t){return arguments.length?(o=Math.min((r=Array.from(t)).length,n.length-1),i):r.slice()},i.range=function(t){return arguments.length?(n=Array.from(t),o=Math.min(r.length,n.length-1),i):n.slice()},i.invertExtent=function(t){var e=n.indexOf(t);return[r[e-1],r[e]]},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return t().domain(r).range(n).unknown(e)},ny.apply(i,arguments)},scaleTime:()=>cg,scaleUtc:()=>cx,tickFormat:()=>ic});var o=r(60687),i=r(57800),a=r(62688);let c=(0,a.A)("clipboard-list",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"M12 11h4",key:"1jrz19"}],["path",{d:"M12 16h4",key:"n85exb"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 16h.01",key:"18s6g9"}]]);var u=r(41312),l=r(25541);let s=(0,a.A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]]);var f=r(43210),p=r.n(f),h=r(49384),d=r(45603),y=r.n(d),v=r(63866),m=r.n(v),b=r(77822),g=r.n(b),x=r(40491),w=r.n(x),O=r(93490),j=r.n(O),S=function(t){return 0===t?0:t>0?1:-1},P=function(t){return m()(t)&&t.indexOf("%")===t.length-1},A=function(t){return j()(t)&&!g()(t)},k=function(t){return A(t)||m()(t)},E=0,M=function(t){var e=++E;return"".concat(t||"").concat(e)},_=function(t,e){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!A(t)&&!m()(t))return n;if(P(t)){var i=t.indexOf("%");r=e*parseFloat(t.slice(0,i))/100}else r=+t;return g()(r)&&(r=n),o&&r>e&&(r=e),r},T=function(t){if(!t)return null;var e=Object.keys(t);return e&&e.length?t[e[0]]:null},C=function(t){if(!Array.isArray(t))return!1;for(var e=t.length,r={},n=0;n<e;n++)if(r[t[n]])return!0;else r[t[n]]=!0;return!1},N=function(t,e){return A(t)&&A(e)?function(r){return t+r*(e-t)}:function(){return e}};function D(t,e,r){return t&&t.length?t.find(function(t){return t&&("function"==typeof e?e(t):w()(t,e))===r}):null}var I=function(t,e){return A(t)&&A(e)?t-e:m()(t)&&m()(e)?t.localeCompare(e):t instanceof Date&&e instanceof Date?t.getTime()-e.getTime():String(t).localeCompare(String(e))},B=function(t,e){for(var r=arguments.length,n=Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o]},R=r(37456),L=r.n(R),z=r(5231),U=r.n(z),F=r(55048),$=r.n(F),W=r(93780);function q(t,e){for(var r in t)if(({}).hasOwnProperty.call(t,r)&&(!({}).hasOwnProperty.call(e,r)||t[r]!==e[r]))return!1;for(var n in e)if(({}).hasOwnProperty.call(e,n)&&!({}).hasOwnProperty.call(t,n))return!1;return!0}function X(t){return(X="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var H=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],V=["points","pathLength"],G={svg:["viewBox","children"],polygon:V,polyline:V},Y=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],K=function(t,e){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var r=t;if((0,f.isValidElement)(t)&&(r=t.props),!$()(r))return null;var n={};return Object.keys(r).forEach(function(t){Y.includes(t)&&(n[t]=e||function(e){return r[t](r,e)})}),n},Z=function(t,e,r){if(!$()(t)||"object"!==X(t))return null;var n=null;return Object.keys(t).forEach(function(o){var i=t[o];Y.includes(o)&&"function"==typeof i&&(n||(n={}),n[o]=function(t){return i(e,r,t),null})}),n},J=["children"],Q=["children"];function tt(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function te(t){return(te="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var tr={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},tn=function(t){return"string"==typeof t?t:t?t.displayName||t.name||"Component":""},to=null,ti=null,ta=function t(e){if(e===to&&Array.isArray(ti))return ti;var r=[];return f.Children.forEach(e,function(e){L()(e)||((0,W.isFragment)(e)?r=r.concat(t(e.props.children)):r.push(e))}),ti=r,to=e,r};function tc(t,e){var r=[],n=[];return n=Array.isArray(e)?e.map(function(t){return tn(t)}):[tn(e)],ta(t).forEach(function(t){var e=w()(t,"type.displayName")||w()(t,"type.name");-1!==n.indexOf(e)&&r.push(t)}),r}function tu(t,e){var r=tc(t,e);return r&&r[0]}var tl=function(t){if(!t||!t.props)return!1;var e=t.props,r=e.width,n=e.height;return!!A(r)&&!(r<=0)&&!!A(n)&&!(n<=0)},ts=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],tf=function(t,e,r,n){var o,i=null!=(o=null==G?void 0:G[n])?o:[];return e.startsWith("data-")||!U()(t)&&(n&&i.includes(e)||H.includes(e))||r&&Y.includes(e)},tp=function(t,e,r){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var n=t;if((0,f.isValidElement)(t)&&(n=t.props),!$()(n))return null;var o={};return Object.keys(n).forEach(function(t){var i;tf(null==(i=n)?void 0:i[t],t,e,r)&&(o[t]=n[t])}),o},th=function t(e,r){if(e===r)return!0;var n=f.Children.count(e);if(n!==f.Children.count(r))return!1;if(0===n)return!0;if(1===n)return td(Array.isArray(e)?e[0]:e,Array.isArray(r)?r[0]:r);for(var o=0;o<n;o++){var i=e[o],a=r[o];if(Array.isArray(i)||Array.isArray(a)){if(!t(i,a))return!1}else if(!td(i,a))return!1}return!0},td=function(t,e){if(L()(t)&&L()(e))return!0;if(!L()(t)&&!L()(e)){var r=t.props||{},n=r.children,o=tt(r,J),i=e.props||{},a=i.children,c=tt(i,Q);if(n&&a)return q(o,c)&&th(n,a);if(!n&&!a)return q(o,c)}return!1},ty=function(t,e){var r=[],n={};return ta(t).forEach(function(t,o){var i;if((i=t)&&i.type&&m()(i.type)&&ts.indexOf(i.type)>=0)r.push(t);else if(t){var a=tn(t.type),c=e[a]||{},u=c.handler,l=c.once;if(u&&(!l||!n[a])){var s=u(t,a,o);r.push(s),n[a]=!0}}}),r},tv=function(t){var e=t&&t.type;return e&&tr[e]?tr[e]:null};function tm(t){return(tm="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tb(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tg(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tb(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=tm(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tm(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tm(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tb(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tx(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var tw=(0,f.forwardRef)(function(t,e){var r,n=t.aspect,o=t.initialDimension,i=void 0===o?{width:-1,height:-1}:o,a=t.width,c=void 0===a?"100%":a,u=t.height,l=void 0===u?"100%":u,s=t.minWidth,d=void 0===s?0:s,v=t.minHeight,m=t.maxHeight,b=t.children,g=t.debounce,x=void 0===g?0:g,w=t.id,O=t.className,j=t.onResize,S=t.style,A=(0,f.useRef)(null),k=(0,f.useRef)();k.current=j,(0,f.useImperativeHandle)(e,function(){return Object.defineProperty(A.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),A.current},configurable:!0})});var E=function(t){if(Array.isArray(t))return t}(r=(0,f.useState)({containerWidth:i.width,containerHeight:i.height}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(r,2)||function(t,e){if(t){if("string"==typeof t)return tx(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tx(t,e)}}(r,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),M=E[0],_=E[1],T=(0,f.useCallback)(function(t,e){_(function(r){var n=Math.round(t),o=Math.round(e);return r.containerWidth===n&&r.containerHeight===o?r:{containerWidth:n,containerHeight:o}})},[]);(0,f.useEffect)(function(){var t=function(t){var e,r=t[0].contentRect,n=r.width,o=r.height;T(n,o),null==(e=k.current)||e.call(k,n,o)};x>0&&(t=y()(t,x,{trailing:!0,leading:!1}));var e=new ResizeObserver(t),r=A.current.getBoundingClientRect();return T(r.width,r.height),e.observe(A.current),function(){e.disconnect()}},[T,x]);var C=(0,f.useMemo)(function(){var t=M.containerWidth,e=M.containerHeight;if(t<0||e<0)return null;B(P(c)||P(l),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",c,l),B(!n||n>0,"The aspect(%s) must be greater than zero.",n);var r=P(c)?t:c,o=P(l)?e:l;n&&n>0&&(r?o=r/n:o&&(r=o*n),m&&o>m&&(o=m)),B(r>0||o>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",r,o,c,l,d,v,n);var i=!Array.isArray(b)&&tn(b.type).endsWith("Chart");return p().Children.map(b,function(t){return p().isValidElement(t)?(0,f.cloneElement)(t,tg({width:r,height:o},i?{style:tg({height:"100%",width:"100%",maxHeight:o,maxWidth:r},t.props.style)}:{})):t})},[n,b,l,m,v,d,M,c]);return p().createElement("div",{id:w?"".concat(w):void 0,className:(0,h.A)("recharts-responsive-container",O),style:tg(tg({},void 0===S?{}:S),{},{width:c,height:l,minWidth:d,minHeight:v,maxHeight:m}),ref:A},C)}),tO=r(34990),tj=r.n(tO),tS=r(85938),tP=r.n(tS);function tA(t,e){if(!t)throw Error("Invariant failed")}var tk=["children","width","height","viewBox","className","style","title","desc"];function tE(){return(tE=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function tM(t){var e=t.children,r=t.width,n=t.height,o=t.viewBox,i=t.className,a=t.style,c=t.title,u=t.desc,l=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,tk),s=o||{width:r,height:n,x:0,y:0},f=(0,h.A)("recharts-surface",i);return p().createElement("svg",tE({},tp(l,!0,"svg"),{className:f,width:r,height:n,style:a,viewBox:"".concat(s.x," ").concat(s.y," ").concat(s.width," ").concat(s.height)}),p().createElement("title",null,c),p().createElement("desc",null,u),e)}var t_=["children","className"];function tT(){return(tT=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var tC=p().forwardRef(function(t,e){var r=t.children,n=t.className,o=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,t_),i=(0,h.A)("recharts-layer",n);return p().createElement("g",tT({className:i},tp(o,!0),{ref:e}),r)});function tN(t){return(tN="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tD(){return(tD=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function tI(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function tB(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tR(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tB(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=tN(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tN(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tN(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tB(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tL(t){return Array.isArray(t)&&k(t[0])&&k(t[1])?t.join(" ~ "):t}var tz=function(t){var e=t.separator,r=void 0===e?" : ":e,n=t.contentStyle,o=t.itemStyle,i=void 0===o?{}:o,a=t.labelStyle,c=t.payload,u=t.formatter,l=t.itemSorter,s=t.wrapperClassName,f=t.labelClassName,d=t.label,y=t.labelFormatter,v=t.accessibilityLayer,m=tR({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},void 0===n?{}:n),b=tR({margin:0},void 0===a?{}:a),g=!L()(d),x=g?d:"",w=(0,h.A)("recharts-default-tooltip",s),O=(0,h.A)("recharts-tooltip-label",f);return g&&y&&null!=c&&(x=y(d,c)),p().createElement("div",tD({className:w,style:m},void 0!==v&&v?{role:"status","aria-live":"assertive"}:{}),p().createElement("p",{className:O,style:b},p().isValidElement(x)?x:"".concat(x)),function(){if(c&&c.length){var t=(l?tP()(c,l):c).map(function(t,e){if("none"===t.type)return null;var n=tR({display:"block",paddingTop:4,paddingBottom:4,color:t.color||"#000"},i),o=t.formatter||u||tL,a=t.value,l=t.name,s=a,f=l;if(o&&null!=s&&null!=f){var h=o(a,l,t,e,c);if(Array.isArray(h)){var d=function(t){if(Array.isArray(t))return t}(h)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(h,2)||function(t,e){if(t){if("string"==typeof t)return tI(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tI(t,e)}}(h,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();s=d[0],f=d[1]}else s=h}return p().createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(e),style:n},k(f)?p().createElement("span",{className:"recharts-tooltip-item-name"},f):null,k(f)?p().createElement("span",{className:"recharts-tooltip-item-separator"},r):null,p().createElement("span",{className:"recharts-tooltip-item-value"},s),p().createElement("span",{className:"recharts-tooltip-item-unit"},t.unit||""))});return p().createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},t)}return null}())};function tU(t){return(tU="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tF(t,e,r){var n;return(n=function(t,e){if("object"!=tU(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tU(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==tU(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var t$="recharts-tooltip-wrapper",tW={visibility:"hidden"};function tq(t){var e=t.allowEscapeViewBox,r=t.coordinate,n=t.key,o=t.offsetTopLeft,i=t.position,a=t.reverseDirection,c=t.tooltipDimension,u=t.viewBox,l=t.viewBoxDimension;if(i&&A(i[n]))return i[n];var s=r[n]-c-o,f=r[n]+o;return e[n]?a[n]?s:f:a[n]?s<u[n]?Math.max(f,u[n]):Math.max(s,u[n]):f+c>u[n]+l?Math.max(s,u[n]):Math.max(f,u[n])}function tX(t){return(tX="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tH(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tV(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tH(Object(r),!0).forEach(function(e){tZ(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tH(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tG(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(tG=function(){return!!t})()}function tY(t){return(tY=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function tK(t,e){return(tK=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tZ(t,e,r){return(e=tJ(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tJ(t){var e=function(t,e){if("object"!=tX(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tX(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tX(e)?e:e+""}var tQ=function(t){var e;function r(){var t,e,n;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");for(var o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=r,n=[].concat(i),e=tY(e),tZ(t=function(t,e){if(e&&("object"===tX(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,tG()?Reflect.construct(e,n||[],tY(this).constructor):e.apply(this,n)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),tZ(t,"handleKeyDown",function(e){if("Escape"===e.key){var r,n,o,i;t.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(r=null==(n=t.props.coordinate)?void 0:n.x)?r:0,y:null!=(o=null==(i=t.props.coordinate)?void 0:i.y)?o:0}})}}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&tK(r,t),e=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();(Math.abs(t.width-this.state.lastBoundingBox.width)>1||Math.abs(t.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:t.width,height:t.height}})}else(-1!==this.state.lastBoundingBox.width||-1!==this.state.lastBoundingBox.height)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var t,e;this.props.active&&this.updateBBox(),this.state.dismissed&&((null==(t=this.props.coordinate)?void 0:t.x)!==this.state.dismissedAtCoordinate.x||(null==(e=this.props.coordinate)?void 0:e.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var t,e,r,n,o,i,a,c,u,l,s,f,d,y,v,m,b,g,x,w=this,O=this.props,j=O.active,S=O.allowEscapeViewBox,P=O.animationDuration,k=O.animationEasing,E=O.children,M=O.coordinate,_=O.hasPayload,T=O.isAnimationActive,C=O.offset,N=O.position,D=O.reverseDirection,I=O.useTranslate3d,B=O.viewBox,R=O.wrapperStyle,L=(f=(t={allowEscapeViewBox:S,coordinate:M,offsetTopLeft:C,position:N,reverseDirection:D,tooltipBox:this.state.lastBoundingBox,useTranslate3d:I,viewBox:B}).allowEscapeViewBox,d=t.coordinate,y=t.offsetTopLeft,v=t.position,m=t.reverseDirection,b=t.tooltipBox,g=t.useTranslate3d,x=t.viewBox,b.height>0&&b.width>0&&d?(r=(e={translateX:l=tq({allowEscapeViewBox:f,coordinate:d,key:"x",offsetTopLeft:y,position:v,reverseDirection:m,tooltipDimension:b.width,viewBox:x,viewBoxDimension:x.width}),translateY:s=tq({allowEscapeViewBox:f,coordinate:d,key:"y",offsetTopLeft:y,position:v,reverseDirection:m,tooltipDimension:b.height,viewBox:x,viewBoxDimension:x.height}),useTranslate3d:g}).translateX,n=e.translateY,u={transform:e.useTranslate3d?"translate3d(".concat(r,"px, ").concat(n,"px, 0)"):"translate(".concat(r,"px, ").concat(n,"px)")}):u=tW,{cssProperties:u,cssClasses:(i=(o={translateX:l,translateY:s,coordinate:d}).coordinate,a=o.translateX,c=o.translateY,(0,h.A)(t$,tF(tF(tF(tF({},"".concat(t$,"-right"),A(a)&&i&&A(i.x)&&a>=i.x),"".concat(t$,"-left"),A(a)&&i&&A(i.x)&&a<i.x),"".concat(t$,"-bottom"),A(c)&&i&&A(i.y)&&c>=i.y),"".concat(t$,"-top"),A(c)&&i&&A(i.y)&&c<i.y)))}),z=L.cssClasses,U=L.cssProperties,F=tV(tV({transition:T&&j?"transform ".concat(P,"ms ").concat(k):void 0},U),{},{pointerEvents:"none",visibility:!this.state.dismissed&&j&&_?"visible":"hidden",position:"absolute",top:0,left:0},R);return p().createElement("div",{tabIndex:-1,className:z,style:F,ref:function(t){w.wrapperNode=t}},E)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tJ(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(f.PureComponent),t0={isSsr:!0,get:function(t){return t0[t]},set:function(t,e){if("string"==typeof t)t0[t]=e;else{var r=Object.keys(t);r&&r.length&&r.forEach(function(e){t0[e]=t[e]})}}},t1=r(36315),t2=r.n(t1);function t5(t,e,r){return!0===e?t2()(t,r):U()(e)?t2()(t,e):t}function t3(t){return(t3="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function t4(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function t6(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?t4(Object(r),!0).forEach(function(e){et(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):t4(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function t8(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(t8=function(){return!!t})()}function t7(t){return(t7=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function t9(t,e){return(t9=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function et(t,e,r){return(e=ee(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ee(t){var e=function(t,e){if("object"!=t3(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=t3(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==t3(e)?e:e+""}function er(t){return t.dataKey}var en=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=t7(t),function(t,e){if(e&&("object"===t3(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,t8()?Reflect.construct(t,e||[],t7(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&t9(r,t),e=[{key:"render",value:function(){var t,e=this,r=this.props,n=r.active,o=r.allowEscapeViewBox,i=r.animationDuration,a=r.animationEasing,c=r.content,u=r.coordinate,l=r.filterNull,s=r.isAnimationActive,f=r.offset,h=r.payload,d=r.payloadUniqBy,y=r.position,v=r.reverseDirection,m=r.useTranslate3d,b=r.viewBox,g=r.wrapperStyle,x=null!=h?h:[];l&&x.length&&(x=t5(h.filter(function(t){return null!=t.value&&(!0!==t.hide||e.props.includeHidden)}),d,er));var w=x.length>0;return p().createElement(tQ,{allowEscapeViewBox:o,animationDuration:i,animationEasing:a,isAnimationActive:s,active:n,coordinate:u,hasPayload:w,offset:f,position:y,reverseDirection:v,useTranslate3d:m,viewBox:b,wrapperStyle:g},(t=t6(t6({},this.props),{},{payload:x}),p().isValidElement(c)?p().cloneElement(c,t):"function"==typeof c?p().createElement(c,t):p().createElement(tz,t)))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ee(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(f.PureComponent);et(en,"displayName","Tooltip"),et(en,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!t0.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var eo=r(69433),ei=r.n(eo);let ea=Math.cos,ec=Math.sin,eu=Math.sqrt,el=Math.PI,es=2*el,ef={draw(t,e){let r=eu(e/el);t.moveTo(r,0),t.arc(0,0,r,0,es)}},ep=eu(1/3),eh=2*ep,ed=ec(el/10)/ec(7*el/10),ey=ec(es/10)*ed,ev=-ea(es/10)*ed,em=eu(3),eb=eu(3)/2,eg=1/eu(12),ex=(eg/2+1)*3;function ew(t){return function(){return t}}let eO=Math.PI,ej=2*eO,eS=ej-1e-6;function eP(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=arguments[e]+t[e]}class eA{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?eP:function(t){let e=Math.floor(t);if(!(e>=0))throw Error(`invalid digits: ${t}`);if(e>15)return eP;let r=10**e;return function(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=Math.round(arguments[e]*r)/r+t[e]}}(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,r,n){this._append`Q${+t},${+e},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(t,e,r,n,o,i){this._append`C${+t},${+e},${+r},${+n},${this._x1=+o},${this._y1=+i}`}arcTo(t,e,r,n,o){if(t*=1,e*=1,r*=1,n*=1,(o*=1)<0)throw Error(`negative radius: ${o}`);let i=this._x1,a=this._y1,c=r-t,u=n-e,l=i-t,s=a-e,f=l*l+s*s;if(null===this._x1)this._append`M${this._x1=t},${this._y1=e}`;else if(f>1e-6)if(Math.abs(s*c-u*l)>1e-6&&o){let p=r-i,h=n-a,d=c*c+u*u,y=Math.sqrt(d),v=Math.sqrt(f),m=o*Math.tan((eO-Math.acos((d+f-(p*p+h*h))/(2*y*v)))/2),b=m/v,g=m/y;Math.abs(b-1)>1e-6&&this._append`L${t+b*l},${e+b*s}`,this._append`A${o},${o},0,0,${+(s*p>l*h)},${this._x1=t+g*c},${this._y1=e+g*u}`}else this._append`L${this._x1=t},${this._y1=e}`}arc(t,e,r,n,o,i){if(t*=1,e*=1,r*=1,i=!!i,r<0)throw Error(`negative radius: ${r}`);let a=r*Math.cos(n),c=r*Math.sin(n),u=t+a,l=e+c,s=1^i,f=i?n-o:o-n;null===this._x1?this._append`M${u},${l}`:(Math.abs(this._x1-u)>1e-6||Math.abs(this._y1-l)>1e-6)&&this._append`L${u},${l}`,r&&(f<0&&(f=f%ej+ej),f>eS?this._append`A${r},${r},0,1,${s},${t-a},${e-c}A${r},${r},0,1,${s},${this._x1=u},${this._y1=l}`:f>1e-6&&this._append`A${r},${r},0,${+(f>=eO)},${s},${this._x1=t+r*Math.cos(o)},${this._y1=e+r*Math.sin(o)}`)}rect(t,e,r,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${r*=1}v${+n}h${-r}Z`}toString(){return this._}}function ek(t){let e=3;return t.digits=function(r){if(!arguments.length)return e;if(null==r)e=null;else{let t=Math.floor(r);if(!(t>=0))throw RangeError(`invalid digits: ${r}`);e=t}return t},()=>new eA(e)}function eE(t){return(eE="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}eA.prototype,eu(3),eu(3);var eM=["type","size","sizeType"];function e_(){return(e_=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function eT(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function eC(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eT(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=eE(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eE(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eE(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eT(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var eN={symbolCircle:ef,symbolCross:{draw(t,e){let r=eu(e/5)/2;t.moveTo(-3*r,-r),t.lineTo(-r,-r),t.lineTo(-r,-3*r),t.lineTo(r,-3*r),t.lineTo(r,-r),t.lineTo(3*r,-r),t.lineTo(3*r,r),t.lineTo(r,r),t.lineTo(r,3*r),t.lineTo(-r,3*r),t.lineTo(-r,r),t.lineTo(-3*r,r),t.closePath()}},symbolDiamond:{draw(t,e){let r=eu(e/eh),n=r*ep;t.moveTo(0,-r),t.lineTo(n,0),t.lineTo(0,r),t.lineTo(-n,0),t.closePath()}},symbolSquare:{draw(t,e){let r=eu(e),n=-r/2;t.rect(n,n,r,r)}},symbolStar:{draw(t,e){let r=eu(.8908130915292852*e),n=ey*r,o=ev*r;t.moveTo(0,-r),t.lineTo(n,o);for(let e=1;e<5;++e){let i=es*e/5,a=ea(i),c=ec(i);t.lineTo(c*r,-a*r),t.lineTo(a*n-c*o,c*n+a*o)}t.closePath()}},symbolTriangle:{draw(t,e){let r=-eu(e/(3*em));t.moveTo(0,2*r),t.lineTo(-em*r,-r),t.lineTo(em*r,-r),t.closePath()}},symbolWye:{draw(t,e){let r=eu(e/ex),n=r/2,o=r*eg,i=r*eg+r,a=-n;t.moveTo(n,o),t.lineTo(n,i),t.lineTo(a,i),t.lineTo(-.5*n-eb*o,eb*n+-.5*o),t.lineTo(-.5*n-eb*i,eb*n+-.5*i),t.lineTo(-.5*a-eb*i,eb*a+-.5*i),t.lineTo(-.5*n+eb*o,-.5*o-eb*n),t.lineTo(-.5*n+eb*i,-.5*i-eb*n),t.lineTo(-.5*a+eb*i,-.5*i-eb*a),t.closePath()}}},eD=Math.PI/180,eI=function(t,e,r){if("area"===e)return t;switch(r){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":var n=18*eD;return 1.25*t*t*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},eB=function(t){var e,r=t.type,n=void 0===r?"circle":r,o=t.size,i=void 0===o?64:o,a=t.sizeType,c=void 0===a?"area":a,u=eC(eC({},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,eM)),{},{type:n,size:i,sizeType:c}),l=u.className,s=u.cx,f=u.cy,d=tp(u,!0);return s===+s&&f===+f&&i===+i?p().createElement("path",e_({},d,{className:(0,h.A)("recharts-symbols",l),transform:"translate(".concat(s,", ").concat(f,")"),d:(e=eN["symbol".concat(ei()(n))]||ef,(function(t,e){let r=null,n=ek(o);function o(){let o;if(r||(r=o=n()),t.apply(this,arguments).draw(r,+e.apply(this,arguments)),o)return r=null,o+""||null}return t="function"==typeof t?t:ew(t||ef),e="function"==typeof e?e:ew(void 0===e?64:+e),o.type=function(e){return arguments.length?(t="function"==typeof e?e:ew(e),o):t},o.size=function(t){return arguments.length?(e="function"==typeof t?t:ew(+t),o):e},o.context=function(t){return arguments.length?(r=null==t?null:t,o):r},o})().type(e).size(eI(i,c,n))())})):null};function eR(t){return(eR="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function eL(){return(eL=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function ez(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}eB.registerSymbol=function(t,e){eN["symbol".concat(ei()(t))]=e};function eU(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(eU=function(){return!!t})()}function eF(t){return(eF=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function e$(t,e){return(e$=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function eW(t,e,r){return(e=eq(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function eq(t){var e=function(t,e){if("object"!=eR(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eR(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eR(e)?e:e+""}var eX=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=eF(t),function(t,e){if(e&&("object"===eR(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,eU()?Reflect.construct(t,e||[],eF(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&e$(r,t),e=[{key:"renderIcon",value:function(t){var e=this.props.inactiveColor,r=32/6,n=32/3,o=t.inactive?e:t.color;if("plainline"===t.type)return p().createElement("line",{strokeWidth:4,fill:"none",stroke:o,strokeDasharray:t.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===t.type)return p().createElement("path",{strokeWidth:4,fill:"none",stroke:o,d:"M0,".concat(16,"h").concat(n,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(2*n,",").concat(16,"\n            H").concat(32,"M").concat(2*n,",").concat(16,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(n,",").concat(16),className:"recharts-legend-icon"});if("rect"===t.type)return p().createElement("path",{stroke:"none",fill:o,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(p().isValidElement(t.legendIcon)){var i=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ez(Object(r),!0).forEach(function(e){eW(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ez(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({},t);return delete i.legendIcon,p().cloneElement(t.legendIcon,i)}return p().createElement(eB,{fill:o,cx:16,cy:16,size:32,sizeType:"diameter",type:t.type})}},{key:"renderItems",value:function(){var t=this,e=this.props,r=e.payload,n=e.iconSize,o=e.layout,i=e.formatter,a=e.inactiveColor,c={x:0,y:0,width:32,height:32},u={display:"horizontal"===o?"inline-block":"block",marginRight:10},l={display:"inline-block",verticalAlign:"middle",marginRight:4};return r.map(function(e,r){var o=e.formatter||i,s=(0,h.A)(eW(eW({"recharts-legend-item":!0},"legend-item-".concat(r),!0),"inactive",e.inactive));if("none"===e.type)return null;var f=U()(e.value)?null:e.value;B(!U()(e.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var d=e.inactive?a:e.color;return p().createElement("li",eL({className:s,style:u,key:"legend-item-".concat(r)},Z(t.props,e,r)),p().createElement(tM,{width:n,height:n,viewBox:c,style:l},t.renderIcon(e)),p().createElement("span",{className:"recharts-legend-item-text",style:{color:d}},o?o(f,e,r):f))})}},{key:"render",value:function(){var t=this.props,e=t.payload,r=t.layout,n=t.align;return e&&e.length?p().createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===r?n:"left"}},this.renderItems()):null}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,eq(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(f.PureComponent);function eH(t){return(eH="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}eW(eX,"displayName","Legend"),eW(eX,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var eV=["ref"];function eG(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function eY(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eG(Object(r),!0).forEach(function(e){e0(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eG(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function eK(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,e1(n.key),n)}}function eZ(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(eZ=function(){return!!t})()}function eJ(t){return(eJ=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function eQ(t,e){return(eQ=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function e0(t,e,r){return(e=e1(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function e1(t){var e=function(t,e){if("object"!=eH(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eH(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eH(e)?e:e+""}function e2(t){return t.value}var e5=function(t){var e,r;function n(){var t,e,r;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");for(var o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=n,r=[].concat(i),e=eJ(e),e0(t=function(t,e){if(e&&("object"===eH(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,eZ()?Reflect.construct(e,r||[],eJ(this).constructor):e.apply(this,r)),"lastBoundingBox",{width:-1,height:-1}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&eQ(n,t),e=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();return t.height=this.wrapperNode.offsetHeight,t.width=this.wrapperNode.offsetWidth,t}return null}},{key:"updateBBox",value:function(){var t=this.props.onBBoxUpdate,e=this.getBBox();e?(Math.abs(e.width-this.lastBoundingBox.width)>1||Math.abs(e.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=e.width,this.lastBoundingBox.height=e.height,t&&t(e)):(-1!==this.lastBoundingBox.width||-1!==this.lastBoundingBox.height)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,t&&t(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?eY({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(t){var e,r,n=this.props,o=n.layout,i=n.align,a=n.verticalAlign,c=n.margin,u=n.chartWidth,l=n.chartHeight;return t&&(void 0!==t.left&&null!==t.left||void 0!==t.right&&null!==t.right)||(e="center"===i&&"vertical"===o?{left:((u||0)-this.getBBoxSnapshot().width)/2}:"right"===i?{right:c&&c.right||0}:{left:c&&c.left||0}),t&&(void 0!==t.top&&null!==t.top||void 0!==t.bottom&&null!==t.bottom)||(r="middle"===a?{top:((l||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:c&&c.bottom||0}:{top:c&&c.top||0}),eY(eY({},e),r)}},{key:"render",value:function(){var t=this,e=this.props,r=e.content,n=e.width,o=e.height,i=e.wrapperStyle,a=e.payloadUniqBy,c=e.payload,u=eY(eY({position:"absolute",width:n||"auto",height:o||"auto"},this.getDefaultPosition(i)),i);return p().createElement("div",{className:"recharts-legend-wrapper",style:u,ref:function(e){t.wrapperNode=e}},function(t,e){if(p().isValidElement(t))return p().cloneElement(t,e);if("function"==typeof t)return p().createElement(t,e);e.ref;var r=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(e,eV);return p().createElement(eX,r)}(r,eY(eY({},this.props),{},{payload:t5(c,a,e2)})))}}],r=[{key:"getWithHeight",value:function(t,e){var r=eY(eY({},this.defaultProps),t.props).layout;return"vertical"===r&&A(t.props.height)?{height:t.props.height}:"horizontal"===r?{width:t.props.width||e}:null}}],e&&eK(n.prototype,e),r&&eK(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(f.PureComponent);function e3(){return(e3=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}e0(e5,"displayName","Legend"),e0(e5,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var e4=function(t){var e=t.cx,r=t.cy,n=t.r,o=t.className,i=(0,h.A)("recharts-dot",o);return e===+e&&r===+r&&n===+n?p().createElement("circle",e3({},tp(t,!1),K(t),{className:i,cx:e,cy:r,r:n})):null},e6=r(87955),e8=r.n(e6),e7=Object.getOwnPropertyNames,e9=Object.getOwnPropertySymbols,rt=Object.prototype.hasOwnProperty;function re(t,e){return function(r,n,o){return t(r,n,o)&&e(r,n,o)}}function rr(t){return function(e,r,n){if(!e||!r||"object"!=typeof e||"object"!=typeof r)return t(e,r,n);var o=n.cache,i=o.get(e),a=o.get(r);if(i&&a)return i===r&&a===e;o.set(e,r),o.set(r,e);var c=t(e,r,n);return o.delete(e),o.delete(r),c}}function rn(t){return e7(t).concat(e9(t))}var ro=Object.hasOwn||function(t,e){return rt.call(t,e)};function ri(t,e){return t===e||!t&&!e&&t!=t&&e!=e}var ra=Object.getOwnPropertyDescriptor,rc=Object.keys;function ru(t,e,r){var n=t.length;if(e.length!==n)return!1;for(;n-- >0;)if(!r.equals(t[n],e[n],n,n,t,e,r))return!1;return!0}function rl(t,e){return ri(t.getTime(),e.getTime())}function rs(t,e){return t.name===e.name&&t.message===e.message&&t.cause===e.cause&&t.stack===e.stack}function rf(t,e){return t===e}function rp(t,e,r){var n,o,i=t.size;if(i!==e.size)return!1;if(!i)return!0;for(var a=Array(i),c=t.entries(),u=0;(n=c.next())&&!n.done;){for(var l=e.entries(),s=!1,f=0;(o=l.next())&&!o.done;){if(a[f]){f++;continue}var p=n.value,h=o.value;if(r.equals(p[0],h[0],u,f,t,e,r)&&r.equals(p[1],h[1],p[0],h[0],t,e,r)){s=a[f]=!0;break}f++}if(!s)return!1;u++}return!0}function rh(t,e,r){var n=rc(t),o=n.length;if(rc(e).length!==o)return!1;for(;o-- >0;)if(!rx(t,e,r,n[o]))return!1;return!0}function rd(t,e,r){var n,o,i,a=rn(t),c=a.length;if(rn(e).length!==c)return!1;for(;c-- >0;)if(!rx(t,e,r,n=a[c])||(o=ra(t,n),i=ra(e,n),(o||i)&&(!o||!i||o.configurable!==i.configurable||o.enumerable!==i.enumerable||o.writable!==i.writable)))return!1;return!0}function ry(t,e){return ri(t.valueOf(),e.valueOf())}function rv(t,e){return t.source===e.source&&t.flags===e.flags}function rm(t,e,r){var n,o,i=t.size;if(i!==e.size)return!1;if(!i)return!0;for(var a=Array(i),c=t.values();(n=c.next())&&!n.done;){for(var u=e.values(),l=!1,s=0;(o=u.next())&&!o.done;){if(!a[s]&&r.equals(n.value,o.value,n.value,o.value,t,e,r)){l=a[s]=!0;break}s++}if(!l)return!1}return!0}function rb(t,e){var r=t.length;if(e.length!==r)return!1;for(;r-- >0;)if(t[r]!==e[r])return!1;return!0}function rg(t,e){return t.hostname===e.hostname&&t.pathname===e.pathname&&t.protocol===e.protocol&&t.port===e.port&&t.hash===e.hash&&t.username===e.username&&t.password===e.password}function rx(t,e,r,n){return("_owner"===n||"__o"===n||"__v"===n)&&(!!t.$$typeof||!!e.$$typeof)||ro(e,n)&&r.equals(t[n],e[n],n,n,t,e,r)}var rw=Array.isArray,rO="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,rj=Object.assign,rS=Object.prototype.toString.call.bind(Object.prototype.toString),rP=rA();function rA(t){void 0===t&&(t={});var e,r,n,o,i,a,c,u,l,s,f,p,h,d=t.circular,y=t.createInternalComparator,v=t.createState,m=t.strict,b=(r=(e=function(t){var e=t.circular,r=t.createCustomConfig,n=t.strict,o={areArraysEqual:n?rd:ru,areDatesEqual:rl,areErrorsEqual:rs,areFunctionsEqual:rf,areMapsEqual:n?re(rp,rd):rp,areNumbersEqual:ri,areObjectsEqual:n?rd:rh,arePrimitiveWrappersEqual:ry,areRegExpsEqual:rv,areSetsEqual:n?re(rm,rd):rm,areTypedArraysEqual:n?rd:rb,areUrlsEqual:rg};if(r&&(o=rj({},o,r(o))),e){var i=rr(o.areArraysEqual),a=rr(o.areMapsEqual),c=rr(o.areObjectsEqual),u=rr(o.areSetsEqual);o=rj({},o,{areArraysEqual:i,areMapsEqual:a,areObjectsEqual:c,areSetsEqual:u})}return o}(t)).areArraysEqual,n=e.areDatesEqual,o=e.areErrorsEqual,i=e.areFunctionsEqual,a=e.areMapsEqual,c=e.areNumbersEqual,u=e.areObjectsEqual,l=e.arePrimitiveWrappersEqual,s=e.areRegExpsEqual,f=e.areSetsEqual,p=e.areTypedArraysEqual,h=e.areUrlsEqual,function(t,e,d){if(t===e)return!0;if(null==t||null==e)return!1;var y=typeof t;if(y!==typeof e)return!1;if("object"!==y)return"number"===y?c(t,e,d):"function"===y&&i(t,e,d);var v=t.constructor;if(v!==e.constructor)return!1;if(v===Object)return u(t,e,d);if(rw(t))return r(t,e,d);if(null!=rO&&rO(t))return p(t,e,d);if(v===Date)return n(t,e,d);if(v===RegExp)return s(t,e,d);if(v===Map)return a(t,e,d);if(v===Set)return f(t,e,d);var m=rS(t);return"[object Date]"===m?n(t,e,d):"[object RegExp]"===m?s(t,e,d):"[object Map]"===m?a(t,e,d):"[object Set]"===m?f(t,e,d):"[object Object]"===m?"function"!=typeof t.then&&"function"!=typeof e.then&&u(t,e,d):"[object URL]"===m?h(t,e,d):"[object Error]"===m?o(t,e,d):"[object Arguments]"===m?u(t,e,d):("[object Boolean]"===m||"[object Number]"===m||"[object String]"===m)&&l(t,e,d)}),g=y?y(b):function(t,e,r,n,o,i,a){return b(t,e,a)};return function(t){var e=t.circular,r=t.comparator,n=t.createState,o=t.equals,i=t.strict;if(n)return function(t,a){var c=n(),u=c.cache;return r(t,a,{cache:void 0===u?e?new WeakMap:void 0:u,equals:o,meta:c.meta,strict:i})};if(e)return function(t,e){return r(t,e,{cache:new WeakMap,equals:o,meta:void 0,strict:i})};var a={cache:void 0,equals:o,meta:void 0,strict:i};return function(t,e){return r(t,e,a)}}({circular:void 0!==d&&d,comparator:b,createState:v,equals:g,strict:void 0!==m&&m})}function rk(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1;requestAnimationFrame(function n(o){if(r<0&&(r=o),o-r>e)t(o),r=-1;else{var i;i=n,"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(i)}})}function rE(t){return(rE="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rM(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function r_(t){return(r_="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rT(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function rC(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?rT(Object(r),!0).forEach(function(e){rN(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):rT(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function rN(t,e,r){var n;return(n=function(t,e){if("object"!==r_(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==r_(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===r_(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}rA({strict:!0}),rA({circular:!0}),rA({circular:!0,strict:!0}),rA({createInternalComparator:function(){return ri}}),rA({strict:!0,createInternalComparator:function(){return ri}}),rA({circular:!0,createInternalComparator:function(){return ri}}),rA({circular:!0,createInternalComparator:function(){return ri},strict:!0});var rD=function(t){return t},rI=function(t,e){return Object.keys(e).reduce(function(r,n){return rC(rC({},r),{},rN({},n,t(n,e[n])))},{})},rB=function(t,e,r){return t.map(function(t){return"".concat(t.replace(/([A-Z])/g,function(t){return"-".concat(t.toLowerCase())})," ").concat(e,"ms ").concat(r)}).join(",")},rR=function(t,e,r,n,o,i,a,c){};function rL(t,e){if(t){if("string"==typeof t)return rz(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rz(t,e)}}function rz(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var rU=function(t,e){return[0,3*t,3*e-6*t,3*t-3*e+1]},rF=function(t,e){return t.map(function(t,r){return t*Math.pow(e,r)}).reduce(function(t,e){return t+e})},r$=function(t,e){return function(r){return rF(rU(t,e),r)}},rW=function(){for(var t,e,r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];var i=n[0],a=n[1],c=n[2],u=n[3];if(1===n.length)switch(n[0]){case"linear":i=0,a=0,c=1,u=1;break;case"ease":i=.25,a=.1,c=.25,u=1;break;case"ease-in":i=.42,a=0,c=1,u=1;break;case"ease-out":i=.42,a=0,c=.58,u=1;break;case"ease-in-out":i=0,a=0,c=.58,u=1;break;default:var l=n[0].split("(");if("cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length){var s,f=function(t){if(Array.isArray(t))return t}(s=l[1].split(")")[0].split(",").map(function(t){return parseFloat(t)}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(s,4)||rL(s,4)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=f[0],a=f[1],c=f[2],u=f[3]}else rR(!1,"[configBezier]: arguments should be one of oneOf 'linear', 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', instead received %s",n)}rR([i,c,a,u].every(function(t){return"number"==typeof t&&t>=0&&t<=1}),"[configBezier]: arguments should be x1, y1, x2, y2 of [0, 1] instead received %s",n);var p=r$(i,c),h=r$(a,u),d=(t=i,e=c,function(r){var n;return rF([].concat(function(t){if(Array.isArray(t))return rz(t)}(n=rU(t,e).map(function(t,e){return t*e}).slice(1))||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(n)||rL(n)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[0]),r)}),y=function(t){for(var e=t>1?1:t,r=e,n=0;n<8;++n){var o,i=p(r)-e,a=d(r);if(1e-4>Math.abs(i-e)||a<1e-4)break;r=(o=r-i/a)>1?1:o<0?0:o}return h(r)};return y.isStepper=!1,y},rq=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.stiff,r=void 0===e?100:e,n=t.damping,o=void 0===n?8:n,i=t.dt,a=void 0===i?17:i,c=function(t,e,n){var i=n+(-(t-e)*r-n*o)*a/1e3,c=n*a/1e3+t;return 1e-4>Math.abs(c-e)&&1e-4>Math.abs(i)?[e,0]:[c,i]};return c.isStepper=!0,c.dt=a,c},rX=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0];if("string"==typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return rW(n);case"spring":return rq();default:if("cubic-bezier"===n.split("(")[0])return rW(n);rR(!1,"[configEasing]: first argument should be one of 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', 'linear' and 'spring', instead  received %s",e)}return"function"==typeof n?n:(rR(!1,"[configEasing]: first argument type should be function or string, instead received %s",e),null)};function rH(t){return(rH="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rV(t){return function(t){if(Array.isArray(t))return rJ(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||rZ(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function rG(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function rY(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?rG(Object(r),!0).forEach(function(e){rK(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):rG(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function rK(t,e,r){var n;return(n=function(t,e){if("object"!==rH(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==rH(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===rH(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function rZ(t,e){if(t){if("string"==typeof t)return rJ(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rJ(t,e)}}function rJ(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var rQ=function(t,e,r){return t+(e-t)*r},r0=function(t){return t.from!==t.to},r1=function t(e,r,n){var o=rI(function(t,r){if(r0(r)){var n,o=function(t){if(Array.isArray(t))return t}(n=e(r.from,r.to,r.velocity))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(n,2)||rZ(n,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o[1];return rY(rY({},r),{},{from:i,velocity:a})}return r},r);return n<1?rI(function(t,e){return r0(e)?rY(rY({},e),{},{velocity:rQ(e.velocity,o[t].velocity,n),from:rQ(e.from,o[t].from,n)}):e},r):t(e,o,n-1)};let r2=function(t,e,r,n,o){var i,a,c=[Object.keys(t),Object.keys(e)].reduce(function(t,e){return t.filter(function(t){return e.includes(t)})}),u=c.reduce(function(r,n){return rY(rY({},r),{},rK({},n,[t[n],e[n]]))},{}),l=c.reduce(function(r,n){return rY(rY({},r),{},rK({},n,{from:t[n],velocity:0,to:e[n]}))},{}),s=-1,f=function(){return null};return f=r.isStepper?function(n){i||(i=n);var a=(n-i)/r.dt;l=r1(r,l,a),o(rY(rY(rY({},t),e),rI(function(t,e){return e.from},l))),i=n,Object.values(l).filter(r0).length&&(s=requestAnimationFrame(f))}:function(i){a||(a=i);var c=(i-a)/n,l=rI(function(t,e){return rQ.apply(void 0,rV(e).concat([r(c)]))},u);if(o(rY(rY(rY({},t),e),l)),c<1)s=requestAnimationFrame(f);else{var p=rI(function(t,e){return rQ.apply(void 0,rV(e).concat([r(1)]))},u);o(rY(rY(rY({},t),e),p))}},function(){return requestAnimationFrame(f),function(){cancelAnimationFrame(s)}}};function r5(t){return(r5="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var r3=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function r4(t){return function(t){if(Array.isArray(t))return r6(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return r6(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return r6(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function r6(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function r8(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function r7(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?r8(Object(r),!0).forEach(function(e){r9(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):r8(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function r9(t,e,r){return(e=nt(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function nt(t){var e=function(t,e){if("object"!==r5(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==r5(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===r5(e)?e:String(e)}function ne(t,e){return(ne=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function nr(t,e){if(e&&("object"===r5(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return nn(t)}function nn(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function no(t){return(no=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var ni=function(t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");o.prototype=Object.create(t&&t.prototype,{constructor:{value:o,writable:!0,configurable:!0}}),Object.defineProperty(o,"prototype",{writable:!1}),t&&ne(o,t);var e,r,n=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,r=no(o);return t=e?Reflect.construct(r,arguments,no(this).constructor):r.apply(this,arguments),nr(this,t)});function o(t,e){if(!(this instanceof o))throw TypeError("Cannot call a class as a function");var r=n.call(this,t,e),i=r.props,a=i.isActive,c=i.attributeName,u=i.from,l=i.to,s=i.steps,f=i.children,p=i.duration;if(r.handleStyleChange=r.handleStyleChange.bind(nn(r)),r.changeStyle=r.changeStyle.bind(nn(r)),!a||p<=0)return r.state={style:{}},"function"==typeof f&&(r.state={style:l}),nr(r);if(s&&s.length)r.state={style:s[0].style};else if(u){if("function"==typeof f)return r.state={style:u},nr(r);r.state={style:c?r9({},c,u):u}}else r.state={style:{}};return r}return r=[{key:"componentDidMount",value:function(){var t=this.props,e=t.isActive,r=t.canBegin;this.mounted=!0,e&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(t){var e=this.props,r=e.isActive,n=e.canBegin,o=e.attributeName,i=e.shouldReAnimate,a=e.to,c=e.from,u=this.state.style;if(n){if(!r){var l={style:o?r9({},o,a):a};this.state&&u&&(o&&u[o]!==a||!o&&u!==a)&&this.setState(l);return}if(!rP(t.to,a)||!t.canBegin||!t.isActive){var s=!t.canBegin||!t.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var f=s||i?c:t.to;if(this.state&&u){var p={style:o?r9({},o,f):f};(o&&u[o]!==f||!o&&u!==f)&&this.setState(p)}this.runAnimation(r7(r7({},this.props),{},{from:f,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var t=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),t&&t()}},{key:"handleStyleChange",value:function(t){this.changeStyle(t)}},{key:"changeStyle",value:function(t){this.mounted&&this.setState({style:t})}},{key:"runJSAnimation",value:function(t){var e=this,r=t.from,n=t.to,o=t.duration,i=t.easing,a=t.begin,c=t.onAnimationEnd,u=t.onAnimationStart,l=r2(r,n,rX(i),o,this.changeStyle);this.manager.start([u,a,function(){e.stopJSAnimation=l()},o,c])}},{key:"runStepAnimation",value:function(t){var e=this,r=t.steps,n=t.begin,o=t.onAnimationStart,i=r[0],a=i.style,c=i.duration;return this.manager.start([o].concat(r4(r.reduce(function(t,n,o){if(0===o)return t;var i=n.duration,a=n.easing,c=void 0===a?"ease":a,u=n.style,l=n.properties,s=n.onAnimationEnd,f=o>0?r[o-1]:n,p=l||Object.keys(u);if("function"==typeof c||"spring"===c)return[].concat(r4(t),[e.runJSAnimation.bind(e,{from:f.style,to:u,duration:i,easing:c}),i]);var h=rB(p,i,c),d=r7(r7(r7({},f.style),u),{},{transition:h});return[].concat(r4(t),[d,i,s]).filter(rD)},[a,Math.max(void 0===c?0:c,n)])),[t.onAnimationEnd]))}},{key:"runAnimation",value:function(t){this.manager||(this.manager=(r=function(){return null},n=!1,o=function t(e){if(!n){if(Array.isArray(e)){if(!e.length)return;var o=function(t){if(Array.isArray(t))return t}(e)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(e)||function(t,e){if(t){if("string"==typeof t)return rM(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rM(t,e)}}(e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o.slice(1);return"number"==typeof i?void rk(t.bind(null,a),i):(t(i),void rk(t.bind(null,a)))}"object"===rE(e)&&r(e),"function"==typeof e&&e()}},{stop:function(){n=!0},start:function(t){n=!1,o(t)},subscribe:function(t){return r=t,function(){r=function(){return null}}}}));var e,r,n,o,i=t.begin,a=t.duration,c=t.attributeName,u=t.to,l=t.easing,s=t.onAnimationStart,f=t.onAnimationEnd,p=t.steps,h=t.children,d=this.manager;if(this.unSubscribe=d.subscribe(this.handleStyleChange),"function"==typeof l||"function"==typeof h||"spring"===l)return void this.runJSAnimation(t);if(p.length>1)return void this.runStepAnimation(t);var y=c?r9({},c,u):u,v=rB(Object.keys(y),a,l);d.start([s,i,r7(r7({},y),{},{transition:v}),a,f])}},{key:"render",value:function(){var t=this.props,e=t.children,r=(t.begin,t.duration),n=(t.attributeName,t.easing,t.isActive),o=(t.steps,t.from,t.to,t.canBegin,t.onAnimationEnd,t.shouldReAnimate,t.onAnimationReStart,function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r,n,o={},i=Object.keys(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||(o[r]=t[r]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,r3)),i=f.Children.count(e),a=this.state.style;if("function"==typeof e)return e(a);if(!n||0===i||r<=0)return e;var c=function(t){var e=t.props,r=e.style,n=e.className;return(0,f.cloneElement)(t,r7(r7({},o),{},{style:r7(r7({},void 0===r?{}:r),a),className:n}))};return 1===i?c(f.Children.only(e)):p().createElement("div",null,f.Children.map(e,function(t){return c(t)}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,nt(n.key),n)}}(o.prototype,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(f.PureComponent);function na(t){return(na="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nc(){return(nc=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function nu(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nl(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ns(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nl(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=na(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=na(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==na(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nl(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}ni.displayName="Animate",ni.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},ni.propTypes={from:e8().oneOfType([e8().object,e8().string]),to:e8().oneOfType([e8().object,e8().string]),attributeName:e8().string,duration:e8().number,begin:e8().number,easing:e8().oneOfType([e8().string,e8().func]),steps:e8().arrayOf(e8().shape({duration:e8().number.isRequired,style:e8().object.isRequired,easing:e8().oneOfType([e8().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),e8().func]),properties:e8().arrayOf("string"),onAnimationEnd:e8().func})),children:e8().oneOfType([e8().node,e8().func]),isActive:e8().bool,canBegin:e8().bool,onAnimationEnd:e8().func,shouldReAnimate:e8().bool,onAnimationStart:e8().func,onAnimationReStart:e8().func};var nf=function(t,e,r,n,o){var i,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),c=n>=0?1:-1,u=r>=0?1:-1,l=+(n>=0&&r>=0||n<0&&r<0);if(a>0&&o instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=o[f]>a?a:o[f];i="M".concat(t,",").concat(e+c*s[0]),s[0]>0&&(i+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(l,",").concat(t+u*s[0],",").concat(e)),i+="L ".concat(t+r-u*s[1],",").concat(e),s[1]>0&&(i+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(l,",\n        ").concat(t+r,",").concat(e+c*s[1])),i+="L ".concat(t+r,",").concat(e+n-c*s[2]),s[2]>0&&(i+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(l,",\n        ").concat(t+r-u*s[2],",").concat(e+n)),i+="L ".concat(t+u*s[3],",").concat(e+n),s[3]>0&&(i+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(l,",\n        ").concat(t,",").concat(e+n-c*s[3])),i+="Z"}else if(a>0&&o===+o&&o>0){var p=Math.min(a,o);i="M ".concat(t,",").concat(e+c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+u*p,",").concat(e,"\n            L ").concat(t+r-u*p,",").concat(e,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r,",").concat(e+c*p,"\n            L ").concat(t+r,",").concat(e+n-c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r-u*p,",").concat(e+n,"\n            L ").concat(t+u*p,",").concat(e+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t,",").concat(e+n-c*p," Z")}else i="M ".concat(t,",").concat(e," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return i},np=function(t,e){if(!t||!e)return!1;var r=t.x,n=t.y,o=e.x,i=e.y,a=e.width,c=e.height;if(Math.abs(a)>0&&Math.abs(c)>0){var u=Math.min(o,o+a),l=Math.max(o,o+a),s=Math.min(i,i+c),f=Math.max(i,i+c);return r>=u&&r<=l&&n>=s&&n<=f}return!1},nh={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},nd=function(t){var e,r=ns(ns({},nh),t),n=(0,f.useRef)(),o=function(t){if(Array.isArray(t))return t}(e=(0,f.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(e,2)||function(t,e){if(t){if("string"==typeof t)return nu(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nu(t,e)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o[1];(0,f.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var t=n.current.getTotalLength();t&&a(t)}catch(t){}},[]);var c=r.x,u=r.y,l=r.width,s=r.height,d=r.radius,y=r.className,v=r.animationEasing,m=r.animationDuration,b=r.animationBegin,g=r.isAnimationActive,x=r.isUpdateAnimationActive;if(c!==+c||u!==+u||l!==+l||s!==+s||0===l||0===s)return null;var w=(0,h.A)("recharts-rectangle",y);return x?p().createElement(ni,{canBegin:i>0,from:{width:l,height:s,x:c,y:u},to:{width:l,height:s,x:c,y:u},duration:m,animationEasing:v,isActive:x},function(t){var e=t.width,o=t.height,a=t.x,c=t.y;return p().createElement(ni,{canBegin:i>0,from:"0px ".concat(-1===i?1:i,"px"),to:"".concat(i,"px 0px"),attributeName:"strokeDasharray",begin:b,duration:m,isActive:g,easing:v},p().createElement("path",nc({},tp(r,!0),{className:w,d:nf(a,c,e,o,d),ref:n})))}):p().createElement("path",nc({},tp(r,!0),{className:w,d:nf(c,u,l,s,d)}))};function ny(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}function nv(t,e){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof e?this.interpolator(e):this.range(e)}return this}class nm extends Map{constructor(t,e=ng){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(let[e,r]of t)this.set(e,r)}get(t){return super.get(nb(this,t))}has(t){return super.has(nb(this,t))}set(t,e){return super.set(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):(t.set(n,r),r)}(this,t),e)}delete(t){return super.delete(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)&&(r=t.get(n),t.delete(n)),r}(this,t))}}function nb({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):r}function ng(t){return null!==t&&"object"==typeof t?t.valueOf():t}let nx=Symbol("implicit");function nw(){var t=new nm,e=[],r=[],n=nx;function o(o){let i=t.get(o);if(void 0===i){if(n!==nx)return n;t.set(o,i=e.push(o)-1)}return r[i%r.length]}return o.domain=function(r){if(!arguments.length)return e.slice();for(let n of(e=[],t=new nm,r))t.has(n)||t.set(n,e.push(n)-1);return o},o.range=function(t){return arguments.length?(r=Array.from(t),o):r.slice()},o.unknown=function(t){return arguments.length?(n=t,o):n},o.copy=function(){return nw(e,r).unknown(n)},ny.apply(o,arguments),o}function nO(){var t,e,r=nw().unknown(void 0),n=r.domain,o=r.range,i=0,a=1,c=!1,u=0,l=0,s=.5;function f(){var r=n().length,f=a<i,p=f?a:i,h=f?i:a;t=(h-p)/Math.max(1,r-u+2*l),c&&(t=Math.floor(t)),p+=(h-p-t*(r-u))*s,e=t*(1-u),c&&(p=Math.round(p),e=Math.round(e));var d=(function(t,e,r){t*=1,e*=1,r=(o=arguments.length)<2?(e=t,t=0,1):o<3?1:+r;for(var n=-1,o=0|Math.max(0,Math.ceil((e-t)/r)),i=Array(o);++n<o;)i[n]=t+n*r;return i})(r).map(function(e){return p+t*e});return o(f?d.reverse():d)}return delete r.unknown,r.domain=function(t){return arguments.length?(n(t),f()):n()},r.range=function(t){return arguments.length?([i,a]=t,i*=1,a*=1,f()):[i,a]},r.rangeRound=function(t){return[i,a]=t,i*=1,a*=1,c=!0,f()},r.bandwidth=function(){return e},r.step=function(){return t},r.round=function(t){return arguments.length?(c=!!t,f()):c},r.padding=function(t){return arguments.length?(u=Math.min(1,l=+t),f()):u},r.paddingInner=function(t){return arguments.length?(u=Math.min(1,t),f()):u},r.paddingOuter=function(t){return arguments.length?(l=+t,f()):l},r.align=function(t){return arguments.length?(s=Math.max(0,Math.min(1,t)),f()):s},r.copy=function(){return nO(n(),[i,a]).round(c).paddingInner(u).paddingOuter(l).align(s)},ny.apply(f(),arguments)}function nj(){return function t(e){var r=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return t(r())},e}(nO.apply(null,arguments).paddingInner(1))}function nS(t){return(nS="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nP(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function nA(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nP(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=nS(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nS(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nS(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nP(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function nk(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var nE={widthCache:{},cacheCount:0},nM={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},n_="recharts_measurement_span",nT=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==t||t0.isSsr)return{width:0,height:0};var n=(Object.keys(e=nA({},r)).forEach(function(t){e[t]||delete e[t]}),e),o=JSON.stringify({text:t,copyStyle:n});if(nE.widthCache[o])return nE.widthCache[o];try{var i=document.getElementById(n_);i||((i=document.createElement("span")).setAttribute("id",n_),i.setAttribute("aria-hidden","true"),document.body.appendChild(i));var a=nA(nA({},nM),n);Object.assign(i.style,a),i.textContent="".concat(t);var c=i.getBoundingClientRect(),u={width:c.width,height:c.height};return nE.widthCache[o]=u,++nE.cacheCount>2e3&&(nE.cacheCount=0,nE.widthCache={}),u}catch(t){return{width:0,height:0}}};function nC(t){return(nC="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nN(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(t){if("string"==typeof t)return nD(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nD(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nD(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nI(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,function(t){var e=function(t,e){if("object"!=nC(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nC(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nC(e)?e:e+""}(n.key),n)}}var nB=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,nR=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,nL=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,nz=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,nU={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},nF=Object.keys(nU),n$=function(){var t,e;function r(t,e){if(!(this instanceof r))throw TypeError("Cannot call a class as a function");this.num=t,this.unit=e,this.num=t,this.unit=e,Number.isNaN(t)&&(this.unit=""),""===e||nL.test(e)||(this.num=NaN,this.unit=""),nF.includes(e)&&(this.num=t*nU[e],this.unit="px")}return t=[{key:"add",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num+t.num,this.unit)}},{key:"subtract",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num-t.num,this.unit)}},{key:"multiply",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num*t.num,this.unit||t.unit)}},{key:"divide",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num/t.num,this.unit||t.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],e=[{key:"parse",value:function(t){var e,n=nN(null!=(e=nz.exec(t))?e:[],3),o=n[1],i=n[2];return new r(parseFloat(o),null!=i?i:"")}}],t&&nI(r.prototype,t),e&&nI(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();function nW(t){if(t.includes("NaN"))return"NaN";for(var e=t;e.includes("*")||e.includes("/");){var r,n=nN(null!=(r=nB.exec(e))?r:[],4),o=n[1],i=n[2],a=n[3],c=n$.parse(null!=o?o:""),u=n$.parse(null!=a?a:""),l="*"===i?c.multiply(u):c.divide(u);if(l.isNaN())return"NaN";e=e.replace(nB,l.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var s,f=nN(null!=(s=nR.exec(e))?s:[],4),p=f[1],h=f[2],d=f[3],y=n$.parse(null!=p?p:""),v=n$.parse(null!=d?d:""),m="+"===h?y.add(v):y.subtract(v);if(m.isNaN())return"NaN";e=e.replace(nR,m.toString())}return e}var nq=/\(([^()]*)\)/;function nX(t){var e=function(t){try{var e;return e=t.replace(/\s+/g,""),e=function(t){for(var e=t;e.includes("(");){var r=nN(nq.exec(e),2)[1];e=e.replace(nq,nW(r))}return e}(e),e=nW(e)}catch(t){return"NaN"}}(t.slice(5,-1));return"NaN"===e?"":e}var nH=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],nV=["dx","dy","angle","className","breakAll"];function nG(){return(nG=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function nY(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function nK(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(t){if("string"==typeof t)return nZ(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nZ(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nZ(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var nJ=/[ \f\n\r\t\v\u2028\u2029]+/,nQ=function(t){var e=t.children,r=t.breakAll,n=t.style;try{var o=[];L()(e)||(o=r?e.toString().split(""):e.toString().split(nJ));var i=o.map(function(t){return{word:t,width:nT(t,n).width}}),a=r?0:nT("\xa0",n).width;return{wordsWithComputedWidth:i,spaceWidth:a}}catch(t){return null}},n0=function(t,e,r,n,o){var i,a=t.maxLines,c=t.children,u=t.style,l=t.breakAll,s=A(a),f=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.reduce(function(t,e){var i=e.word,a=e.width,c=t[t.length-1];return c&&(null==n||o||c.width+a+r<Number(n))?(c.words.push(i),c.width+=a+r):t.push({words:[i],width:a}),t},[])},p=f(e);if(!s)return p;for(var h=function(t){var e=f(nQ({breakAll:l,style:u,children:c.slice(0,t)+"…"}).wordsWithComputedWidth);return[e.length>a||e.reduce(function(t,e){return t.width>e.width?t:e}).width>Number(n),e]},d=0,y=c.length-1,v=0;d<=y&&v<=c.length-1;){var m=Math.floor((d+y)/2),b=nK(h(m-1),2),g=b[0],x=b[1],w=nK(h(m),1)[0];if(g||w||(d=m+1),g&&w&&(y=m-1),!g&&w){i=x;break}v++}return i||p},n1=function(t){return[{words:L()(t)?[]:t.toString().split(nJ)}]},n2=function(t){var e=t.width,r=t.scaleToFit,n=t.children,o=t.style,i=t.breakAll,a=t.maxLines;if((e||r)&&!t0.isSsr){var c=nQ({breakAll:i,children:n,style:o});if(!c)return n1(n);var u=c.wordsWithComputedWidth,l=c.spaceWidth;return n0({breakAll:i,children:n,maxLines:a,style:o},u,l,e,r)}return n1(n)},n5="#808080",n3=function(t){var e,r=t.x,n=void 0===r?0:r,o=t.y,i=void 0===o?0:o,a=t.lineHeight,c=void 0===a?"1em":a,u=t.capHeight,l=void 0===u?"0.71em":u,s=t.scaleToFit,d=void 0!==s&&s,y=t.textAnchor,v=t.verticalAnchor,m=t.fill,b=void 0===m?n5:m,g=nY(t,nH),x=(0,f.useMemo)(function(){return n2({breakAll:g.breakAll,children:g.children,maxLines:g.maxLines,scaleToFit:d,style:g.style,width:g.width})},[g.breakAll,g.children,g.maxLines,d,g.style,g.width]),w=g.dx,O=g.dy,j=g.angle,S=g.className,P=g.breakAll,E=nY(g,nV);if(!k(n)||!k(i))return null;var M=n+(A(w)?w:0),_=i+(A(O)?O:0);switch(void 0===v?"end":v){case"start":e=nX("calc(".concat(l,")"));break;case"middle":e=nX("calc(".concat((x.length-1)/2," * -").concat(c," + (").concat(l," / 2))"));break;default:e=nX("calc(".concat(x.length-1," * -").concat(c,")"))}var T=[];if(d){var C=x[0].width,N=g.width;T.push("scale(".concat((A(N)?N/C:1)/C,")"))}return j&&T.push("rotate(".concat(j,", ").concat(M,", ").concat(_,")")),T.length&&(E.transform=T.join(" ")),p().createElement("text",nG({},tp(E,!0),{x:M,y:_,className:(0,h.A)("recharts-text",S),textAnchor:void 0===y?"start":y,fill:b.includes("url")?n5:b}),x.map(function(t,r){var n=t.words.join(P?"":" ");return p().createElement("tspan",{x:M,dy:0===r?e:c,key:"".concat(n,"-").concat(r)},n)}))};let n4=Math.sqrt(50),n6=Math.sqrt(10),n8=Math.sqrt(2);function n7(t,e,r){let n,o,i,a=(e-t)/Math.max(0,r),c=Math.floor(Math.log10(a)),u=a/Math.pow(10,c),l=u>=n4?10:u>=n6?5:u>=n8?2:1;return(c<0?(n=Math.round(t*(i=Math.pow(10,-c)/l)),o=Math.round(e*i),n/i<t&&++n,o/i>e&&--o,i=-i):(n=Math.round(t/(i=Math.pow(10,c)*l)),o=Math.round(e/i),n*i<t&&++n,o*i>e&&--o),o<n&&.5<=r&&r<2)?n7(t,e,2*r):[n,o,i]}function n9(t,e,r){if(e*=1,t*=1,!((r*=1)>0))return[];if(t===e)return[t];let n=e<t,[o,i,a]=n?n7(e,t,r):n7(t,e,r);if(!(i>=o))return[];let c=i-o+1,u=Array(c);if(n)if(a<0)for(let t=0;t<c;++t)u[t]=-((i-t)/a);else for(let t=0;t<c;++t)u[t]=(i-t)*a;else if(a<0)for(let t=0;t<c;++t)u[t]=-((o+t)/a);else for(let t=0;t<c;++t)u[t]=(o+t)*a;return u}function ot(t,e,r){return n7(t*=1,e*=1,r*=1)[2]}function oe(t,e,r){e*=1,t*=1,r*=1;let n=e<t,o=n?ot(e,t,r):ot(t,e,r);return(n?-1:1)*(o<0?-(1/o):o)}function or(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function on(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function oo(t){let e,r,n;function o(t,n,i=0,a=t.length){if(i<a){if(0!==e(n,n))return a;do{let e=i+a>>>1;0>r(t[e],n)?i=e+1:a=e}while(i<a)}return i}return 2!==t.length?(e=or,r=(e,r)=>or(t(e),r),n=(e,r)=>t(e)-r):(e=t===or||t===on?t:oi,r=t,n=t),{left:o,center:function(t,e,r=0,i=t.length){let a=o(t,e,r,i-1);return a>r&&n(t[a-1],e)>-n(t[a],e)?a-1:a},right:function(t,n,o=0,i=t.length){if(o<i){if(0!==e(n,n))return i;do{let e=o+i>>>1;0>=r(t[e],n)?o=e+1:i=e}while(o<i)}return o}}}function oi(){return 0}function oa(t){return null===t?NaN:+t}let oc=oo(or),ou=oc.right;function ol(t,e,r){t.prototype=e.prototype=r,r.constructor=t}function os(t,e){var r=Object.create(t.prototype);for(var n in e)r[n]=e[n];return r}function of(){}oc.left,oo(oa).center;var op="\\s*([+-]?\\d+)\\s*",oh="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",od="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",oy=/^#([0-9a-f]{3,8})$/,ov=RegExp(`^rgb\\(${op},${op},${op}\\)$`),om=RegExp(`^rgb\\(${od},${od},${od}\\)$`),ob=RegExp(`^rgba\\(${op},${op},${op},${oh}\\)$`),og=RegExp(`^rgba\\(${od},${od},${od},${oh}\\)$`),ox=RegExp(`^hsl\\(${oh},${od},${od}\\)$`),ow=RegExp(`^hsla\\(${oh},${od},${od},${oh}\\)$`),oO={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function oj(){return this.rgb().formatHex()}function oS(){return this.rgb().formatRgb()}function oP(t){var e,r;return t=(t+"").trim().toLowerCase(),(e=oy.exec(t))?(r=e[1].length,e=parseInt(e[1],16),6===r?oA(e):3===r?new oM(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===r?ok(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===r?ok(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=ov.exec(t))?new oM(e[1],e[2],e[3],1):(e=om.exec(t))?new oM(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=ob.exec(t))?ok(e[1],e[2],e[3],e[4]):(e=og.exec(t))?ok(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=ox.exec(t))?oI(e[1],e[2]/100,e[3]/100,1):(e=ow.exec(t))?oI(e[1],e[2]/100,e[3]/100,e[4]):oO.hasOwnProperty(t)?oA(oO[t]):"transparent"===t?new oM(NaN,NaN,NaN,0):null}function oA(t){return new oM(t>>16&255,t>>8&255,255&t,1)}function ok(t,e,r,n){return n<=0&&(t=e=r=NaN),new oM(t,e,r,n)}function oE(t,e,r,n){var o;return 1==arguments.length?((o=t)instanceof of||(o=oP(o)),o)?new oM((o=o.rgb()).r,o.g,o.b,o.opacity):new oM:new oM(t,e,r,null==n?1:n)}function oM(t,e,r,n){this.r=+t,this.g=+e,this.b=+r,this.opacity=+n}function o_(){return`#${oD(this.r)}${oD(this.g)}${oD(this.b)}`}function oT(){let t=oC(this.opacity);return`${1===t?"rgb(":"rgba("}${oN(this.r)}, ${oN(this.g)}, ${oN(this.b)}${1===t?")":`, ${t})`}`}function oC(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function oN(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function oD(t){return((t=oN(t))<16?"0":"")+t.toString(16)}function oI(t,e,r,n){return n<=0?t=e=r=NaN:r<=0||r>=1?t=e=NaN:e<=0&&(t=NaN),new oR(t,e,r,n)}function oB(t){if(t instanceof oR)return new oR(t.h,t.s,t.l,t.opacity);if(t instanceof of||(t=oP(t)),!t)return new oR;if(t instanceof oR)return t;var e=(t=t.rgb()).r/255,r=t.g/255,n=t.b/255,o=Math.min(e,r,n),i=Math.max(e,r,n),a=NaN,c=i-o,u=(i+o)/2;return c?(a=e===i?(r-n)/c+(r<n)*6:r===i?(n-e)/c+2:(e-r)/c+4,c/=u<.5?i+o:2-i-o,a*=60):c=u>0&&u<1?0:a,new oR(a,c,u,t.opacity)}function oR(t,e,r,n){this.h=+t,this.s=+e,this.l=+r,this.opacity=+n}function oL(t){return(t=(t||0)%360)<0?t+360:t}function oz(t){return Math.max(0,Math.min(1,t||0))}function oU(t,e,r){return(t<60?e+(r-e)*t/60:t<180?r:t<240?e+(r-e)*(240-t)/60:e)*255}function oF(t,e,r,n,o){var i=t*t,a=i*t;return((1-3*t+3*i-a)*e+(4-6*i+3*a)*r+(1+3*t+3*i-3*a)*n+a*o)/6}ol(of,oP,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:oj,formatHex:oj,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return oB(this).formatHsl()},formatRgb:oS,toString:oS}),ol(oM,oE,os(of,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new oM(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new oM(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new oM(oN(this.r),oN(this.g),oN(this.b),oC(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:o_,formatHex:o_,formatHex8:function(){return`#${oD(this.r)}${oD(this.g)}${oD(this.b)}${oD((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:oT,toString:oT})),ol(oR,function(t,e,r,n){return 1==arguments.length?oB(t):new oR(t,e,r,null==n?1:n)},os(of,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new oR(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new oR(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,e=isNaN(t)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*e,o=2*r-n;return new oM(oU(t>=240?t-240:t+120,o,n),oU(t,o,n),oU(t<120?t+240:t-120,o,n),this.opacity)},clamp(){return new oR(oL(this.h),oz(this.s),oz(this.l),oC(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=oC(this.opacity);return`${1===t?"hsl(":"hsla("}${oL(this.h)}, ${100*oz(this.s)}%, ${100*oz(this.l)}%${1===t?")":`, ${t})`}`}}));let o$=t=>()=>t;function oW(t,e){var r,n,o=e-t;return o?(r=t,n=o,function(t){return r+t*n}):o$(isNaN(t)?e:t)}let oq=function t(e){var r,n=1==(r=+e)?oW:function(t,e){var n,o,i;return e-t?(n=t,o=e,n=Math.pow(n,i=r),o=Math.pow(o,i)-n,i=1/i,function(t){return Math.pow(n+t*o,i)}):o$(isNaN(t)?e:t)};function o(t,e){var r=n((t=oE(t)).r,(e=oE(e)).r),o=n(t.g,e.g),i=n(t.b,e.b),a=oW(t.opacity,e.opacity);return function(e){return t.r=r(e),t.g=o(e),t.b=i(e),t.opacity=a(e),t+""}}return o.gamma=t,o}(1);function oX(t){return function(e){var r,n,o=e.length,i=Array(o),a=Array(o),c=Array(o);for(r=0;r<o;++r)n=oE(e[r]),i[r]=n.r||0,a[r]=n.g||0,c[r]=n.b||0;return i=t(i),a=t(a),c=t(c),n.opacity=1,function(t){return n.r=i(t),n.g=a(t),n.b=c(t),n+""}}}oX(function(t){var e=t.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,e-1):Math.floor(r*e),o=t[n],i=t[n+1],a=n>0?t[n-1]:2*o-i,c=n<e-1?t[n+2]:2*i-o;return oF((r-n/e)*e,a,o,i,c)}}),oX(function(t){var e=t.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*e),o=t[(n+e-1)%e],i=t[n%e],a=t[(n+1)%e],c=t[(n+2)%e];return oF((r-n/e)*e,o,i,a,c)}});function oH(t,e){return t*=1,e*=1,function(r){return t*(1-r)+e*r}}var oV=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,oG=RegExp(oV.source,"g");function oY(t,e){var r,n,o=typeof e;return null==e||"boolean"===o?o$(e):("number"===o?oH:"string"===o?(n=oP(e))?(e=n,oq):function(t,e){var r,n,o,i,a,c=oV.lastIndex=oG.lastIndex=0,u=-1,l=[],s=[];for(t+="",e+="";(o=oV.exec(t))&&(i=oG.exec(e));)(a=i.index)>c&&(a=e.slice(c,a),l[u]?l[u]+=a:l[++u]=a),(o=o[0])===(i=i[0])?l[u]?l[u]+=i:l[++u]=i:(l[++u]=null,s.push({i:u,x:oH(o,i)})),c=oG.lastIndex;return c<e.length&&(a=e.slice(c),l[u]?l[u]+=a:l[++u]=a),l.length<2?s[0]?(r=s[0].x,function(t){return r(t)+""}):(n=e,function(){return n}):(e=s.length,function(t){for(var r,n=0;n<e;++n)l[(r=s[n]).i]=r.x(t);return l.join("")})}:e instanceof oP?oq:e instanceof Date?function(t,e){var r=new Date;return t*=1,e*=1,function(n){return r.setTime(t*(1-n)+e*n),r}}:!ArrayBuffer.isView(r=e)||r instanceof DataView?Array.isArray(e)?function(t,e){var r,n=e?e.length:0,o=t?Math.min(n,t.length):0,i=Array(o),a=Array(n);for(r=0;r<o;++r)i[r]=oY(t[r],e[r]);for(;r<n;++r)a[r]=e[r];return function(t){for(r=0;r<o;++r)a[r]=i[r](t);return a}}:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?function(t,e){var r,n={},o={};for(r in(null===t||"object"!=typeof t)&&(t={}),(null===e||"object"!=typeof e)&&(e={}),e)r in t?n[r]=oY(t[r],e[r]):o[r]=e[r];return function(t){for(r in n)o[r]=n[r](t);return o}}:oH:function(t,e){e||(e=[]);var r,n=t?Math.min(e.length,t.length):0,o=e.slice();return function(i){for(r=0;r<n;++r)o[r]=t[r]*(1-i)+e[r]*i;return o}})(t,e)}function oK(t,e){return t*=1,e*=1,function(r){return Math.round(t*(1-r)+e*r)}}function oZ(t){return+t}var oJ=[0,1];function oQ(t){return t}function o0(t,e){var r;return(e-=t*=1)?function(r){return(r-t)/e}:(r=isNaN(e)?NaN:.5,function(){return r})}function o1(t,e,r){var n=t[0],o=t[1],i=e[0],a=e[1];return o<n?(n=o0(o,n),i=r(a,i)):(n=o0(n,o),i=r(i,a)),function(t){return i(n(t))}}function o2(t,e,r){var n=Math.min(t.length,e.length)-1,o=Array(n),i=Array(n),a=-1;for(t[n]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++a<n;)o[a]=o0(t[a],t[a+1]),i[a]=r(e[a],e[a+1]);return function(e){var r=ou(t,e,1,n)-1;return i[r](o[r](e))}}function o5(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function o3(){var t,e,r,n,o,i,a=oJ,c=oJ,u=oY,l=oQ;function s(){var t,e,r,u=Math.min(a.length,c.length);return l!==oQ&&(t=a[0],e=a[u-1],t>e&&(r=t,t=e,e=r),l=function(r){return Math.max(t,Math.min(e,r))}),n=u>2?o2:o1,o=i=null,f}function f(e){return null==e||isNaN(e*=1)?r:(o||(o=n(a.map(t),c,u)))(t(l(e)))}return f.invert=function(r){return l(e((i||(i=n(c,a.map(t),oH)))(r)))},f.domain=function(t){return arguments.length?(a=Array.from(t,oZ),s()):a.slice()},f.range=function(t){return arguments.length?(c=Array.from(t),s()):c.slice()},f.rangeRound=function(t){return c=Array.from(t),u=oK,s()},f.clamp=function(t){return arguments.length?(l=!!t||oQ,s()):l!==oQ},f.interpolate=function(t){return arguments.length?(u=t,s()):u},f.unknown=function(t){return arguments.length?(r=t,f):r},function(r,n){return t=r,e=n,s()}}function o4(){return o3()(oQ,oQ)}var o6=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function o8(t){var e;if(!(e=o6.exec(t)))throw Error("invalid format: "+t);return new o7({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function o7(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function o9(t,e){if((r=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var r,n=t.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+t.slice(r+1)]}function it(t){return(t=o9(Math.abs(t)))?t[1]:NaN}function ie(t,e){var r=o9(t,e);if(!r)return t+"";var n=r[0],o=r[1];return o<0?"0."+Array(-o).join("0")+n:n.length>o+1?n.slice(0,o+1)+"."+n.slice(o+1):n+Array(o-n.length+2).join("0")}o8.prototype=o7.prototype,o7.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let ir={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>ie(100*t,e),r:ie,s:function(t,e){var r=o9(t,e);if(!r)return t+"";var n=r[0],o=r[1],i=o-(cN=3*Math.max(-8,Math.min(8,Math.floor(o/3))))+1,a=n.length;return i===a?n:i>a?n+Array(i-a+1).join("0"):i>0?n.slice(0,i)+"."+n.slice(i):"0."+Array(1-i).join("0")+o9(t,Math.max(0,e+i-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function io(t){return t}var ii=Array.prototype.map,ia=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function ic(t,e,r,n){var o,i,a,c=oe(t,e,r);switch((n=o8(null==n?",f":n)).type){case"s":var u=Math.max(Math.abs(t),Math.abs(e));return null!=n.precision||isNaN(a=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(it(u)/3)))-it(Math.abs(c))))||(n.precision=a),cB(n,u);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(a=Math.max(0,it(Math.abs(Math.max(Math.abs(t),Math.abs(e)))-(o=Math.abs(o=c)))-it(o))+1)||(n.precision=a-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(a=Math.max(0,-it(Math.abs(c))))||(n.precision=a-("%"===n.type)*2)}return cI(n)}function iu(t){var e=t.domain;return t.ticks=function(t){var r=e();return n9(r[0],r[r.length-1],null==t?10:t)},t.tickFormat=function(t,r){var n=e();return ic(n[0],n[n.length-1],null==t?10:t,r)},t.nice=function(r){null==r&&(r=10);var n,o,i=e(),a=0,c=i.length-1,u=i[a],l=i[c],s=10;for(l<u&&(o=u,u=l,l=o,o=a,a=c,c=o);s-- >0;){if((o=ot(u,l,r))===n)return i[a]=u,i[c]=l,e(i);if(o>0)u=Math.floor(u/o)*o,l=Math.ceil(l/o)*o;else if(o<0)u=Math.ceil(u*o)/o,l=Math.floor(l*o)/o;else break;n=o}return t},t}function il(){var t=o4();return t.copy=function(){return o5(t,il())},ny.apply(t,arguments),iu(t)}function is(t,e){t=t.slice();var r,n=0,o=t.length-1,i=t[n],a=t[o];return a<i&&(r=n,n=o,o=r,r=i,i=a,a=r),t[n]=e.floor(i),t[o]=e.ceil(a),t}function ip(t){return Math.log(t)}function ih(t){return Math.exp(t)}function id(t){return-Math.log(-t)}function iy(t){return-Math.exp(-t)}function iv(t){return isFinite(t)?+("1e"+t):t<0?0:t}function im(t){return(e,r)=>-t(-e,r)}function ib(t){let e,r,n=t(ip,ih),o=n.domain,i=10;function a(){var a,c;return e=(a=i)===Math.E?Math.log:10===a&&Math.log10||2===a&&Math.log2||(a=Math.log(a),t=>Math.log(t)/a),r=10===(c=i)?iv:c===Math.E?Math.exp:t=>Math.pow(c,t),o()[0]<0?(e=im(e),r=im(r),t(id,iy)):t(ip,ih),n}return n.base=function(t){return arguments.length?(i=+t,a()):i},n.domain=function(t){return arguments.length?(o(t),a()):o()},n.ticks=t=>{let n,a,c=o(),u=c[0],l=c[c.length-1],s=l<u;s&&([u,l]=[l,u]);let f=e(u),p=e(l),h=null==t?10:+t,d=[];if(!(i%1)&&p-f<h){if(f=Math.floor(f),p=Math.ceil(p),u>0){for(;f<=p;++f)for(n=1;n<i;++n)if(!((a=f<0?n/r(-f):n*r(f))<u)){if(a>l)break;d.push(a)}}else for(;f<=p;++f)for(n=i-1;n>=1;--n)if(!((a=f>0?n/r(-f):n*r(f))<u)){if(a>l)break;d.push(a)}2*d.length<h&&(d=n9(u,l,h))}else d=n9(f,p,Math.min(p-f,h)).map(r);return s?d.reverse():d},n.tickFormat=(t,o)=>{if(null==t&&(t=10),null==o&&(o=10===i?"s":","),"function"!=typeof o&&(i%1||null!=(o=o8(o)).precision||(o.trim=!0),o=cI(o)),t===1/0)return o;let a=Math.max(1,i*t/n.ticks().length);return t=>{let n=t/r(Math.round(e(t)));return n*i<i-.5&&(n*=i),n<=a?o(t):""}},n.nice=()=>o(is(o(),{floor:t=>r(Math.floor(e(t))),ceil:t=>r(Math.ceil(e(t)))})),n}function ig(t){return function(e){return Math.sign(e)*Math.log1p(Math.abs(e/t))}}function ix(t){return function(e){return Math.sign(e)*Math.expm1(Math.abs(e))*t}}function iw(t){var e=1,r=t(ig(1),ix(e));return r.constant=function(r){return arguments.length?t(ig(e=+r),ix(e)):e},iu(r)}function iO(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function ij(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function iS(t){return t<0?-t*t:t*t}function iP(t){var e=t(oQ,oQ),r=1;return e.exponent=function(e){return arguments.length?1==(r=+e)?t(oQ,oQ):.5===r?t(ij,iS):t(iO(r),iO(1/r)):r},iu(e)}function iA(){var t=iP(o3());return t.copy=function(){return o5(t,iA()).exponent(t.exponent())},ny.apply(t,arguments),t}function ik(){return iA.apply(null,arguments).exponent(.5)}function iE(t){return Math.sign(t)*t*t}function iM(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r<e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let o of t)null!=(o=e(o,++n,t))&&(r<o||void 0===r&&o>=o)&&(r=o)}return r}function i_(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r>e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let o of t)null!=(o=e(o,++n,t))&&(r>o||void 0===r&&o>=o)&&(r=o)}return r}cI=(cD=function(t){var e,r,n,o=void 0===t.grouping||void 0===t.thousands?io:(e=ii.call(t.grouping,Number),r=t.thousands+"",function(t,n){for(var o=t.length,i=[],a=0,c=e[0],u=0;o>0&&c>0&&(u+c+1>n&&(c=Math.max(1,n-u)),i.push(t.substring(o-=c,o+c)),!((u+=c+1)>n));)c=e[a=(a+1)%e.length];return i.reverse().join(r)}),i=void 0===t.currency?"":t.currency[0]+"",a=void 0===t.currency?"":t.currency[1]+"",c=void 0===t.decimal?".":t.decimal+"",u=void 0===t.numerals?io:(n=ii.call(t.numerals,String),function(t){return t.replace(/[0-9]/g,function(t){return n[+t]})}),l=void 0===t.percent?"%":t.percent+"",s=void 0===t.minus?"−":t.minus+"",f=void 0===t.nan?"NaN":t.nan+"";function p(t){var e=(t=o8(t)).fill,r=t.align,n=t.sign,p=t.symbol,h=t.zero,d=t.width,y=t.comma,v=t.precision,m=t.trim,b=t.type;"n"===b?(y=!0,b="g"):ir[b]||(void 0===v&&(v=12),m=!0,b="g"),(h||"0"===e&&"="===r)&&(h=!0,e="0",r="=");var g="$"===p?i:"#"===p&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",x="$"===p?a:/[%p]/.test(b)?l:"",w=ir[b],O=/[defgprs%]/.test(b);function j(t){var i,a,l,p=g,j=x;if("c"===b)j=w(t)+j,t="";else{var S=(t*=1)<0||1/t<0;if(t=isNaN(t)?f:w(Math.abs(t),v),m&&(t=function(t){t:for(var e,r=t.length,n=1,o=-1;n<r;++n)switch(t[n]){case".":o=e=n;break;case"0":0===o&&(o=n),e=n;break;default:if(!+t[n])break t;o>0&&(o=0)}return o>0?t.slice(0,o)+t.slice(e+1):t}(t)),S&&0==+t&&"+"!==n&&(S=!1),p=(S?"("===n?n:s:"-"===n||"("===n?"":n)+p,j=("s"===b?ia[8+cN/3]:"")+j+(S&&"("===n?")":""),O){for(i=-1,a=t.length;++i<a;)if(48>(l=t.charCodeAt(i))||l>57){j=(46===l?c+t.slice(i+1):t.slice(i))+j,t=t.slice(0,i);break}}}y&&!h&&(t=o(t,1/0));var P=p.length+t.length+j.length,A=P<d?Array(d-P+1).join(e):"";switch(y&&h&&(t=o(A+t,A.length?d-j.length:1/0),A=""),r){case"<":t=p+t+j+A;break;case"=":t=p+A+t+j;break;case"^":t=A.slice(0,P=A.length>>1)+p+t+j+A.slice(P);break;default:t=A+p+t+j}return u(t)}return v=void 0===v?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),j.toString=function(){return t+""},j}return{format:p,formatPrefix:function(t,e){var r=p(((t=o8(t)).type="f",t)),n=3*Math.max(-8,Math.min(8,Math.floor(it(e)/3))),o=Math.pow(10,-n),i=ia[8+n/3];return function(t){return r(o*t)+i}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,cB=cD.formatPrefix;function iT(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:+(t>e))}function iC(t,e,r){let n=t[e];t[e]=t[r],t[r]=n}let iN=new Date,iD=new Date;function iI(t,e,r,n){function o(e){return t(e=0==arguments.length?new Date:new Date(+e)),e}return o.floor=e=>(t(e=new Date(+e)),e),o.ceil=r=>(t(r=new Date(r-1)),e(r,1),t(r),r),o.round=t=>{let e=o(t),r=o.ceil(t);return t-e<r-t?e:r},o.offset=(t,r)=>(e(t=new Date(+t),null==r?1:Math.floor(r)),t),o.range=(r,n,i)=>{let a,c=[];if(r=o.ceil(r),i=null==i?1:Math.floor(i),!(r<n)||!(i>0))return c;do c.push(a=new Date(+r)),e(r,i),t(r);while(a<r&&r<n);return c},o.filter=r=>iI(e=>{if(e>=e)for(;t(e),!r(e);)e.setTime(e-1)},(t,n)=>{if(t>=t)if(n<0)for(;++n<=0;)for(;e(t,-1),!r(t););else for(;--n>=0;)for(;e(t,1),!r(t););}),r&&(o.count=(e,n)=>(iN.setTime(+e),iD.setTime(+n),t(iN),t(iD),Math.floor(r(iN,iD))),o.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?o.filter(n?e=>n(e)%t==0:e=>o.count(0,e)%t==0):o:null),o}let iB=iI(()=>{},(t,e)=>{t.setTime(+t+e)},(t,e)=>e-t);iB.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?iI(e=>{e.setTime(Math.floor(e/t)*t)},(e,r)=>{e.setTime(+e+r*t)},(e,r)=>(r-e)/t):iB:null,iB.range;let iR=iI(t=>{t.setTime(t-t.getMilliseconds())},(t,e)=>{t.setTime(+t+1e3*e)},(t,e)=>(e-t)/1e3,t=>t.getUTCSeconds());iR.range;let iL=iI(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds())},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getMinutes());iL.range;let iz=iI(t=>{t.setUTCSeconds(0,0)},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getUTCMinutes());iz.range;let iU=iI(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds()-6e4*t.getMinutes())},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getHours());iU.range;let iF=iI(t=>{t.setUTCMinutes(0,0,0)},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getUTCHours());iF.range;let i$=iI(t=>t.setHours(0,0,0,0),(t,e)=>t.setDate(t.getDate()+e),(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/864e5,t=>t.getDate()-1);i$.range;let iW=iI(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>t.getUTCDate()-1);iW.range;let iq=iI(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>Math.floor(t/864e5));function iX(t){return iI(e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)},(t,e)=>{t.setDate(t.getDate()+7*e)},(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/6048e5)}iq.range;let iH=iX(0),iV=iX(1),iG=iX(2),iY=iX(3),iK=iX(4),iZ=iX(5),iJ=iX(6);function iQ(t){return iI(e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+7*e)},(t,e)=>(e-t)/6048e5)}iH.range,iV.range,iG.range,iY.range,iK.range,iZ.range,iJ.range;let i0=iQ(0),i1=iQ(1),i2=iQ(2),i5=iQ(3),i3=iQ(4),i4=iQ(5),i6=iQ(6);i0.range,i1.range,i2.range,i5.range,i3.range,i4.range,i6.range;let i8=iI(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,e)=>{t.setMonth(t.getMonth()+e)},(t,e)=>e.getMonth()-t.getMonth()+(e.getFullYear()-t.getFullYear())*12,t=>t.getMonth());i8.range;let i7=iI(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)},(t,e)=>e.getUTCMonth()-t.getUTCMonth()+(e.getUTCFullYear()-t.getUTCFullYear())*12,t=>t.getUTCMonth());i7.range;let i9=iI(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e)},(t,e)=>e.getFullYear()-t.getFullYear(),t=>t.getFullYear());i9.every=t=>isFinite(t=Math.floor(t))&&t>0?iI(e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)},(e,r)=>{e.setFullYear(e.getFullYear()+r*t)}):null,i9.range;let at=iI(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)},(t,e)=>e.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());function ae(t,e,r,n,o,i){let a=[[iR,1,1e3],[iR,5,5e3],[iR,15,15e3],[iR,30,3e4],[i,1,6e4],[i,5,3e5],[i,15,9e5],[i,30,18e5],[o,1,36e5],[o,3,108e5],[o,6,216e5],[o,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[e,1,2592e6],[e,3,7776e6],[t,1,31536e6]];function c(e,r,n){let o=Math.abs(r-e)/n,i=oo(([,,t])=>t).right(a,o);if(i===a.length)return t.every(oe(e/31536e6,r/31536e6,n));if(0===i)return iB.every(Math.max(oe(e,r,n),1));let[c,u]=a[o/a[i-1][2]<a[i][2]/o?i-1:i];return c.every(u)}return[function(t,e,r){let n=e<t;n&&([t,e]=[e,t]);let o=r&&"function"==typeof r.range?r:c(t,e,r),i=o?o.range(t,+e+1):[];return n?i.reverse():i},c]}at.every=t=>isFinite(t=Math.floor(t))&&t>0?iI(e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,r)=>{e.setUTCFullYear(e.getUTCFullYear()+r*t)}):null,at.range;let[ar,an]=ae(at,i7,i0,iq,iF,iz),[ao,ai]=ae(i9,i8,iH,i$,iU,iL);function aa(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function ac(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function au(t,e,r){return{y:t,m:e,d:r,H:0,M:0,S:0,L:0}}var al={"-":"",_:" ",0:"0"},as=/^\s*\d+/,af=/^%/,ap=/[\\^$*+?|[\]().{}]/g;function ah(t,e,r){var n=t<0?"-":"",o=(n?-t:t)+"",i=o.length;return n+(i<r?Array(r-i+1).join(e)+o:o)}function ad(t){return t.replace(ap,"\\$&")}function ay(t){return RegExp("^(?:"+t.map(ad).join("|")+")","i")}function av(t){return new Map(t.map((t,e)=>[t.toLowerCase(),e]))}function am(t,e,r){var n=as.exec(e.slice(r,r+1));return n?(t.w=+n[0],r+n[0].length):-1}function ab(t,e,r){var n=as.exec(e.slice(r,r+1));return n?(t.u=+n[0],r+n[0].length):-1}function ag(t,e,r){var n=as.exec(e.slice(r,r+2));return n?(t.U=+n[0],r+n[0].length):-1}function ax(t,e,r){var n=as.exec(e.slice(r,r+2));return n?(t.V=+n[0],r+n[0].length):-1}function aw(t,e,r){var n=as.exec(e.slice(r,r+2));return n?(t.W=+n[0],r+n[0].length):-1}function aO(t,e,r){var n=as.exec(e.slice(r,r+4));return n?(t.y=+n[0],r+n[0].length):-1}function aj(t,e,r){var n=as.exec(e.slice(r,r+2));return n?(t.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function aS(t,e,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(r,r+6));return n?(t.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function aP(t,e,r){var n=as.exec(e.slice(r,r+1));return n?(t.q=3*n[0]-3,r+n[0].length):-1}function aA(t,e,r){var n=as.exec(e.slice(r,r+2));return n?(t.m=n[0]-1,r+n[0].length):-1}function ak(t,e,r){var n=as.exec(e.slice(r,r+2));return n?(t.d=+n[0],r+n[0].length):-1}function aE(t,e,r){var n=as.exec(e.slice(r,r+3));return n?(t.m=0,t.d=+n[0],r+n[0].length):-1}function aM(t,e,r){var n=as.exec(e.slice(r,r+2));return n?(t.H=+n[0],r+n[0].length):-1}function a_(t,e,r){var n=as.exec(e.slice(r,r+2));return n?(t.M=+n[0],r+n[0].length):-1}function aT(t,e,r){var n=as.exec(e.slice(r,r+2));return n?(t.S=+n[0],r+n[0].length):-1}function aC(t,e,r){var n=as.exec(e.slice(r,r+3));return n?(t.L=+n[0],r+n[0].length):-1}function aN(t,e,r){var n=as.exec(e.slice(r,r+6));return n?(t.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function aD(t,e,r){var n=af.exec(e.slice(r,r+1));return n?r+n[0].length:-1}function aI(t,e,r){var n=as.exec(e.slice(r));return n?(t.Q=+n[0],r+n[0].length):-1}function aB(t,e,r){var n=as.exec(e.slice(r));return n?(t.s=+n[0],r+n[0].length):-1}function aR(t,e){return ah(t.getDate(),e,2)}function aL(t,e){return ah(t.getHours(),e,2)}function az(t,e){return ah(t.getHours()%12||12,e,2)}function aU(t,e){return ah(1+i$.count(i9(t),t),e,3)}function aF(t,e){return ah(t.getMilliseconds(),e,3)}function a$(t,e){return aF(t,e)+"000"}function aW(t,e){return ah(t.getMonth()+1,e,2)}function aq(t,e){return ah(t.getMinutes(),e,2)}function aX(t,e){return ah(t.getSeconds(),e,2)}function aH(t){var e=t.getDay();return 0===e?7:e}function aV(t,e){return ah(iH.count(i9(t)-1,t),e,2)}function aG(t){var e=t.getDay();return e>=4||0===e?iK(t):iK.ceil(t)}function aY(t,e){return t=aG(t),ah(iK.count(i9(t),t)+(4===i9(t).getDay()),e,2)}function aK(t){return t.getDay()}function aZ(t,e){return ah(iV.count(i9(t)-1,t),e,2)}function aJ(t,e){return ah(t.getFullYear()%100,e,2)}function aQ(t,e){return ah((t=aG(t)).getFullYear()%100,e,2)}function a0(t,e){return ah(t.getFullYear()%1e4,e,4)}function a1(t,e){var r=t.getDay();return ah((t=r>=4||0===r?iK(t):iK.ceil(t)).getFullYear()%1e4,e,4)}function a2(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+ah(e/60|0,"0",2)+ah(e%60,"0",2)}function a5(t,e){return ah(t.getUTCDate(),e,2)}function a3(t,e){return ah(t.getUTCHours(),e,2)}function a4(t,e){return ah(t.getUTCHours()%12||12,e,2)}function a6(t,e){return ah(1+iW.count(at(t),t),e,3)}function a8(t,e){return ah(t.getUTCMilliseconds(),e,3)}function a7(t,e){return a8(t,e)+"000"}function a9(t,e){return ah(t.getUTCMonth()+1,e,2)}function ct(t,e){return ah(t.getUTCMinutes(),e,2)}function ce(t,e){return ah(t.getUTCSeconds(),e,2)}function cr(t){var e=t.getUTCDay();return 0===e?7:e}function cn(t,e){return ah(i0.count(at(t)-1,t),e,2)}function co(t){var e=t.getUTCDay();return e>=4||0===e?i3(t):i3.ceil(t)}function ci(t,e){return t=co(t),ah(i3.count(at(t),t)+(4===at(t).getUTCDay()),e,2)}function ca(t){return t.getUTCDay()}function cc(t,e){return ah(i1.count(at(t)-1,t),e,2)}function cu(t,e){return ah(t.getUTCFullYear()%100,e,2)}function cl(t,e){return ah((t=co(t)).getUTCFullYear()%100,e,2)}function cs(t,e){return ah(t.getUTCFullYear()%1e4,e,4)}function cf(t,e){var r=t.getUTCDay();return ah((t=r>=4||0===r?i3(t):i3.ceil(t)).getUTCFullYear()%1e4,e,4)}function cp(){return"+0000"}function ch(){return"%"}function cd(t){return+t}function cy(t){return Math.floor(t/1e3)}function cv(t){return new Date(t)}function cm(t){return t instanceof Date?+t:+new Date(+t)}function cb(t,e,r,n,o,i,a,c,u,l){var s=o4(),f=s.invert,p=s.domain,h=l(".%L"),d=l(":%S"),y=l("%I:%M"),v=l("%I %p"),m=l("%a %d"),b=l("%b %d"),g=l("%B"),x=l("%Y");function w(t){return(u(t)<t?h:c(t)<t?d:a(t)<t?y:i(t)<t?v:n(t)<t?o(t)<t?m:b:r(t)<t?g:x)(t)}return s.invert=function(t){return new Date(f(t))},s.domain=function(t){return arguments.length?p(Array.from(t,cm)):p().map(cv)},s.ticks=function(e){var r=p();return t(r[0],r[r.length-1],null==e?10:e)},s.tickFormat=function(t,e){return null==e?w:l(e)},s.nice=function(t){var r=p();return t&&"function"==typeof t.range||(t=e(r[0],r[r.length-1],null==t?10:t)),t?p(is(r,t)):s},s.copy=function(){return o5(s,cb(t,e,r,n,o,i,a,c,u,l))},s}function cg(){return ny.apply(cb(ao,ai,i9,i8,iH,i$,iU,iL,iR,cL).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function cx(){return ny.apply(cb(ar,an,at,i7,i0,iW,iF,iz,iR,cz).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function cw(){var t,e,r,n,o,i=0,a=1,c=oQ,u=!1;function l(e){return null==e||isNaN(e*=1)?o:c(0===r?.5:(e=(n(e)-t)*r,u?Math.max(0,Math.min(1,e)):e))}function s(t){return function(e){var r,n;return arguments.length?([r,n]=e,c=t(r,n),l):[c(0),c(1)]}}return l.domain=function(o){return arguments.length?([i,a]=o,t=n(i*=1),e=n(a*=1),r=t===e?0:1/(e-t),l):[i,a]},l.clamp=function(t){return arguments.length?(u=!!t,l):u},l.interpolator=function(t){return arguments.length?(c=t,l):c},l.range=s(oY),l.rangeRound=s(oK),l.unknown=function(t){return arguments.length?(o=t,l):o},function(o){return n=o,t=o(i),e=o(a),r=t===e?0:1/(e-t),l}}function cO(t,e){return e.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function cj(){var t=iP(cw());return t.copy=function(){return cO(t,cj()).exponent(t.exponent())},nv.apply(t,arguments)}function cS(){return cj.apply(null,arguments).exponent(.5)}function cP(){var t,e,r,n,o,i,a,c=0,u=.5,l=1,s=1,f=oQ,p=!1;function h(t){return isNaN(t*=1)?a:(t=.5+((t=+i(t))-e)*(s*t<s*e?n:o),f(p?Math.max(0,Math.min(1,t)):t))}function d(t){return function(e){var r,n,o;return arguments.length?([r,n,o]=e,f=function(t,e){void 0===e&&(e=t,t=oY);for(var r=0,n=e.length-1,o=e[0],i=Array(n<0?0:n);r<n;)i[r]=t(o,o=e[++r]);return function(t){var e=Math.max(0,Math.min(n-1,Math.floor(t*=n)));return i[e](t-e)}}(t,[r,n,o]),h):[f(0),f(.5),f(1)]}}return h.domain=function(a){return arguments.length?([c,u,l]=a,t=i(c*=1),e=i(u*=1),r=i(l*=1),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),s=e<t?-1:1,h):[c,u,l]},h.clamp=function(t){return arguments.length?(p=!!t,h):p},h.interpolator=function(t){return arguments.length?(f=t,h):f},h.range=d(oY),h.rangeRound=d(oK),h.unknown=function(t){return arguments.length?(a=t,h):a},function(a){return i=a,t=a(c),e=a(u),r=a(l),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),s=e<t?-1:1,h}}function cA(){var t=iP(cP());return t.copy=function(){return cO(t,cA()).exponent(t.exponent())},nv.apply(t,arguments)}function ck(){return cA.apply(null,arguments).exponent(.5)}function cE(t,e){if((o=t.length)>1)for(var r,n,o,i=1,a=t[e[0]],c=a.length;i<o;++i)for(n=a,a=t[e[i]],r=0;r<c;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}function cM(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function c_(t){for(var e=t.length,r=Array(e);--e>=0;)r[e]=e;return r}function cT(t,e){return t[e]}function cC(t){let e=[];return e.key=t,e}cL=(cR=function(t){var e=t.dateTime,r=t.date,n=t.time,o=t.periods,i=t.days,a=t.shortDays,c=t.months,u=t.shortMonths,l=ay(o),s=av(o),f=ay(i),p=av(i),h=ay(a),d=av(a),y=ay(c),v=av(c),m=ay(u),b=av(u),g={a:function(t){return a[t.getDay()]},A:function(t){return i[t.getDay()]},b:function(t){return u[t.getMonth()]},B:function(t){return c[t.getMonth()]},c:null,d:aR,e:aR,f:a$,g:aQ,G:a1,H:aL,I:az,j:aU,L:aF,m:aW,M:aq,p:function(t){return o[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:cd,s:cy,S:aX,u:aH,U:aV,V:aY,w:aK,W:aZ,x:null,X:null,y:aJ,Y:a0,Z:a2,"%":ch},x={a:function(t){return a[t.getUTCDay()]},A:function(t){return i[t.getUTCDay()]},b:function(t){return u[t.getUTCMonth()]},B:function(t){return c[t.getUTCMonth()]},c:null,d:a5,e:a5,f:a7,g:cl,G:cf,H:a3,I:a4,j:a6,L:a8,m:a9,M:ct,p:function(t){return o[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:cd,s:cy,S:ce,u:cr,U:cn,V:ci,w:ca,W:cc,x:null,X:null,y:cu,Y:cs,Z:cp,"%":ch},w={a:function(t,e,r){var n=h.exec(e.slice(r));return n?(t.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(t,e,r){var n=f.exec(e.slice(r));return n?(t.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(t,e,r){var n=m.exec(e.slice(r));return n?(t.m=b.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(t,e,r){var n=y.exec(e.slice(r));return n?(t.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(t,r,n){return S(t,e,r,n)},d:ak,e:ak,f:aN,g:aj,G:aO,H:aM,I:aM,j:aE,L:aC,m:aA,M:a_,p:function(t,e,r){var n=l.exec(e.slice(r));return n?(t.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:aP,Q:aI,s:aB,S:aT,u:ab,U:ag,V:ax,w:am,W:aw,x:function(t,e,n){return S(t,r,e,n)},X:function(t,e,r){return S(t,n,e,r)},y:aj,Y:aO,Z:aS,"%":aD};function O(t,e){return function(r){var n,o,i,a=[],c=-1,u=0,l=t.length;for(r instanceof Date||(r=new Date(+r));++c<l;)37===t.charCodeAt(c)&&(a.push(t.slice(u,c)),null!=(o=al[n=t.charAt(++c)])?n=t.charAt(++c):o="e"===n?" ":"0",(i=e[n])&&(n=i(r,o)),a.push(n),u=c+1);return a.push(t.slice(u,c)),a.join("")}}function j(t,e){return function(r){var n,o,i=au(1900,void 0,1);if(S(i,t,r+="",0)!=r.length)return null;if("Q"in i)return new Date(i.Q);if("s"in i)return new Date(1e3*i.s+("L"in i?i.L:0));if(!e||"Z"in i||(i.Z=0),"p"in i&&(i.H=i.H%12+12*i.p),void 0===i.m&&(i.m="q"in i?i.q:0),"V"in i){if(i.V<1||i.V>53)return null;"w"in i||(i.w=1),"Z"in i?(n=(o=(n=ac(au(i.y,0,1))).getUTCDay())>4||0===o?i1.ceil(n):i1(n),n=iW.offset(n,(i.V-1)*7),i.y=n.getUTCFullYear(),i.m=n.getUTCMonth(),i.d=n.getUTCDate()+(i.w+6)%7):(n=(o=(n=aa(au(i.y,0,1))).getDay())>4||0===o?iV.ceil(n):iV(n),n=i$.offset(n,(i.V-1)*7),i.y=n.getFullYear(),i.m=n.getMonth(),i.d=n.getDate()+(i.w+6)%7)}else("W"in i||"U"in i)&&("w"in i||(i.w="u"in i?i.u%7:+("W"in i)),o="Z"in i?ac(au(i.y,0,1)).getUTCDay():aa(au(i.y,0,1)).getDay(),i.m=0,i.d="W"in i?(i.w+6)%7+7*i.W-(o+5)%7:i.w+7*i.U-(o+6)%7);return"Z"in i?(i.H+=i.Z/100|0,i.M+=i.Z%100,ac(i)):aa(i)}}function S(t,e,r,n){for(var o,i,a=0,c=e.length,u=r.length;a<c;){if(n>=u)return -1;if(37===(o=e.charCodeAt(a++))){if(!(i=w[(o=e.charAt(a++))in al?e.charAt(a++):o])||(n=i(t,r,n))<0)return -1}else if(o!=r.charCodeAt(n++))return -1}return n}return g.x=O(r,g),g.X=O(n,g),g.c=O(e,g),x.x=O(r,x),x.X=O(n,x),x.c=O(e,x),{format:function(t){var e=O(t+="",g);return e.toString=function(){return t},e},parse:function(t){var e=j(t+="",!1);return e.toString=function(){return t},e},utcFormat:function(t){var e=O(t+="",x);return e.toString=function(){return t},e},utcParse:function(t){var e=j(t+="",!0);return e.toString=function(){return t},e}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,cR.parse,cz=cR.utcFormat,cR.utcParse,Array.prototype.slice;var cN,cD,cI,cB,cR,cL,cz,cU,cF,c$=r(90453),cW=r.n(c$),cq=r(15883),cX=r.n(cq),cH=r(21592),cV=r.n(cH),cG=r(71967),cY=r.n(cG),cK=!0,cZ="[DecimalError] ",cJ=cZ+"Invalid argument: ",cQ=cZ+"Exponent out of range: ",c0=Math.floor,c1=Math.pow,c2=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,c5=c0(1286742750677284.5),c3={};function c4(t,e){var r,n,o,i,a,c,u,l,s=t.constructor,f=s.precision;if(!t.s||!e.s)return e.s||(e=new s(t)),cK?ui(e,f):e;if(u=t.d,l=e.d,a=t.e,o=e.e,u=u.slice(),i=a-o){for(i<0?(n=u,i=-i,c=l.length):(n=l,o=a,c=u.length),i>(c=(a=Math.ceil(f/7))>c?a+1:c+1)&&(i=c,n.length=1),n.reverse();i--;)n.push(0);n.reverse()}for((c=u.length)-(i=l.length)<0&&(i=c,n=l,l=u,u=n),r=0;i;)r=(u[--i]=u[i]+l[i]+r)/1e7|0,u[i]%=1e7;for(r&&(u.unshift(r),++o),c=u.length;0==u[--c];)u.pop();return e.d=u,e.e=o,cK?ui(e,f):e}function c6(t,e,r){if(t!==~~t||t<e||t>r)throw Error(cJ+t)}function c8(t){var e,r,n,o=t.length-1,i="",a=t[0];if(o>0){for(i+=a,e=1;e<o;e++)(r=7-(n=t[e]+"").length)&&(i+=ur(r)),i+=n;(r=7-(n=(a=t[e])+"").length)&&(i+=ur(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return i+a}c3.absoluteValue=c3.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t},c3.comparedTo=c3.cmp=function(t){var e,r,n,o;if(t=new this.constructor(t),this.s!==t.s)return this.s||-t.s;if(this.e!==t.e)return this.e>t.e^this.s<0?1:-1;for(e=0,r=(n=this.d.length)<(o=t.d.length)?n:o;e<r;++e)if(this.d[e]!==t.d[e])return this.d[e]>t.d[e]^this.s<0?1:-1;return n===o?0:n>o^this.s<0?1:-1},c3.decimalPlaces=c3.dp=function(){var t=this.d.length-1,e=(t-this.e)*7;if(t=this.d[t])for(;t%10==0;t/=10)e--;return e<0?0:e},c3.dividedBy=c3.div=function(t){return c7(this,new this.constructor(t))},c3.dividedToIntegerBy=c3.idiv=function(t){var e=this.constructor;return ui(c7(this,new e(t),0,1),e.precision)},c3.equals=c3.eq=function(t){return!this.cmp(t)},c3.exponent=function(){return ut(this)},c3.greaterThan=c3.gt=function(t){return this.cmp(t)>0},c3.greaterThanOrEqualTo=c3.gte=function(t){return this.cmp(t)>=0},c3.isInteger=c3.isint=function(){return this.e>this.d.length-2},c3.isNegative=c3.isneg=function(){return this.s<0},c3.isPositive=c3.ispos=function(){return this.s>0},c3.isZero=function(){return 0===this.s},c3.lessThan=c3.lt=function(t){return 0>this.cmp(t)},c3.lessThanOrEqualTo=c3.lte=function(t){return 1>this.cmp(t)},c3.logarithm=c3.log=function(t){var e,r=this.constructor,n=r.precision,o=n+5;if(void 0===t)t=new r(10);else if((t=new r(t)).s<1||t.eq(cF))throw Error(cZ+"NaN");if(this.s<1)throw Error(cZ+(this.s?"NaN":"-Infinity"));return this.eq(cF)?new r(0):(cK=!1,e=c7(un(this,o),un(t,o),o),cK=!0,ui(e,n))},c3.minus=c3.sub=function(t){return t=new this.constructor(t),this.s==t.s?ua(this,t):c4(this,(t.s=-t.s,t))},c3.modulo=c3.mod=function(t){var e,r=this.constructor,n=r.precision;if(!(t=new r(t)).s)throw Error(cZ+"NaN");return this.s?(cK=!1,e=c7(this,t,0,1).times(t),cK=!0,this.minus(e)):ui(new r(this),n)},c3.naturalExponential=c3.exp=function(){return c9(this)},c3.naturalLogarithm=c3.ln=function(){return un(this)},c3.negated=c3.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t},c3.plus=c3.add=function(t){return t=new this.constructor(t),this.s==t.s?c4(this,t):ua(this,(t.s=-t.s,t))},c3.precision=c3.sd=function(t){var e,r,n;if(void 0!==t&&!!t!==t&&1!==t&&0!==t)throw Error(cJ+t);if(e=ut(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return t&&e>r?e:r},c3.squareRoot=c3.sqrt=function(){var t,e,r,n,o,i,a,c=this.constructor;if(this.s<1){if(!this.s)return new c(0);throw Error(cZ+"NaN")}for(t=ut(this),cK=!1,0==(o=Math.sqrt(+this))||o==1/0?(((e=c8(this.d)).length+t)%2==0&&(e+="0"),o=Math.sqrt(e),t=c0((t+1)/2)-(t<0||t%2),n=new c(e=o==1/0?"5e"+t:(e=o.toExponential()).slice(0,e.indexOf("e")+1)+t)):n=new c(o.toString()),o=a=(r=c.precision)+3;;)if(n=(i=n).plus(c7(this,i,a+2)).times(.5),c8(i.d).slice(0,a)===(e=c8(n.d)).slice(0,a)){if(e=e.slice(a-3,a+1),o==a&&"4999"==e){if(ui(i,r+1,0),i.times(i).eq(this)){n=i;break}}else if("9999"!=e)break;a+=4}return cK=!0,ui(n,r)},c3.times=c3.mul=function(t){var e,r,n,o,i,a,c,u,l,s=this.constructor,f=this.d,p=(t=new s(t)).d;if(!this.s||!t.s)return new s(0);for(t.s*=this.s,r=this.e+t.e,(u=f.length)<(l=p.length)&&(i=f,f=p,p=i,a=u,u=l,l=a),i=[],n=a=u+l;n--;)i.push(0);for(n=l;--n>=0;){for(e=0,o=u+n;o>n;)c=i[o]+p[n]*f[o-n-1]+e,i[o--]=c%1e7|0,e=c/1e7|0;i[o]=(i[o]+e)%1e7|0}for(;!i[--a];)i.pop();return e?++r:i.shift(),t.d=i,t.e=r,cK?ui(t,s.precision):t},c3.toDecimalPlaces=c3.todp=function(t,e){var r=this,n=r.constructor;return(r=new n(r),void 0===t)?r:(c6(t,0,1e9),void 0===e?e=n.rounding:c6(e,0,8),ui(r,t+ut(r)+1,e))},c3.toExponential=function(t,e){var r,n=this,o=n.constructor;return void 0===t?r=uc(n,!0):(c6(t,0,1e9),void 0===e?e=o.rounding:c6(e,0,8),r=uc(n=ui(new o(n),t+1,e),!0,t+1)),r},c3.toFixed=function(t,e){var r,n,o=this.constructor;return void 0===t?uc(this):(c6(t,0,1e9),void 0===e?e=o.rounding:c6(e,0,8),r=uc((n=ui(new o(this),t+ut(this)+1,e)).abs(),!1,t+ut(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},c3.toInteger=c3.toint=function(){var t=this.constructor;return ui(new t(this),ut(this)+1,t.rounding)},c3.toNumber=function(){return+this},c3.toPower=c3.pow=function(t){var e,r,n,o,i,a,c=this,u=c.constructor,l=+(t=new u(t));if(!t.s)return new u(cF);if(!(c=new u(c)).s){if(t.s<1)throw Error(cZ+"Infinity");return c}if(c.eq(cF))return c;if(n=u.precision,t.eq(cF))return ui(c,n);if(a=(e=t.e)>=(r=t.d.length-1),i=c.s,a){if((r=l<0?-l:l)<=0x1fffffffffffff){for(o=new u(cF),e=Math.ceil(n/7+4),cK=!1;r%2&&uu((o=o.times(c)).d,e),0!==(r=c0(r/2));)uu((c=c.times(c)).d,e);return cK=!0,t.s<0?new u(cF).div(o):ui(o,n)}}else if(i<0)throw Error(cZ+"NaN");return i=i<0&&1&t.d[Math.max(e,r)]?-1:1,c.s=1,cK=!1,o=t.times(un(c,n+12)),cK=!0,(o=c9(o)).s=i,o},c3.toPrecision=function(t,e){var r,n,o=this,i=o.constructor;return void 0===t?(r=ut(o),n=uc(o,r<=i.toExpNeg||r>=i.toExpPos)):(c6(t,1,1e9),void 0===e?e=i.rounding:c6(e,0,8),r=ut(o=ui(new i(o),t,e)),n=uc(o,t<=r||r<=i.toExpNeg,t)),n},c3.toSignificantDigits=c3.tosd=function(t,e){var r=this.constructor;return void 0===t?(t=r.precision,e=r.rounding):(c6(t,1,1e9),void 0===e?e=r.rounding:c6(e,0,8)),ui(new r(this),t,e)},c3.toString=c3.valueOf=c3.val=c3.toJSON=c3[Symbol.for("nodejs.util.inspect.custom")]=function(){var t=ut(this),e=this.constructor;return uc(this,t<=e.toExpNeg||t>=e.toExpPos)};var c7=function(){function t(t,e){var r,n=0,o=t.length;for(t=t.slice();o--;)r=t[o]*e+n,t[o]=r%1e7|0,n=r/1e7|0;return n&&t.unshift(n),t}function e(t,e,r,n){var o,i;if(r!=n)i=r>n?1:-1;else for(o=i=0;o<r;o++)if(t[o]!=e[o]){i=t[o]>e[o]?1:-1;break}return i}function r(t,e,r){for(var n=0;r--;)t[r]-=n,n=+(t[r]<e[r]),t[r]=1e7*n+t[r]-e[r];for(;!t[0]&&t.length>1;)t.shift()}return function(n,o,i,a){var c,u,l,s,f,p,h,d,y,v,m,b,g,x,w,O,j,S,P=n.constructor,A=n.s==o.s?1:-1,k=n.d,E=o.d;if(!n.s)return new P(n);if(!o.s)throw Error(cZ+"Division by zero");for(l=0,u=n.e-o.e,j=E.length,w=k.length,d=(h=new P(A)).d=[];E[l]==(k[l]||0);)++l;if(E[l]>(k[l]||0)&&--u,(b=null==i?i=P.precision:a?i+(ut(n)-ut(o))+1:i)<0)return new P(0);if(b=b/7+2|0,l=0,1==j)for(s=0,E=E[0],b++;(l<w||s)&&b--;l++)g=1e7*s+(k[l]||0),d[l]=g/E|0,s=g%E|0;else{for((s=1e7/(E[0]+1)|0)>1&&(E=t(E,s),k=t(k,s),j=E.length,w=k.length),x=j,v=(y=k.slice(0,j)).length;v<j;)y[v++]=0;(S=E.slice()).unshift(0),O=E[0],E[1]>=1e7/2&&++O;do s=0,(c=e(E,y,j,v))<0?(m=y[0],j!=v&&(m=1e7*m+(y[1]||0)),(s=m/O|0)>1?(s>=1e7&&(s=1e7-1),p=(f=t(E,s)).length,v=y.length,1==(c=e(f,y,p,v))&&(s--,r(f,j<p?S:E,p))):(0==s&&(c=s=1),f=E.slice()),(p=f.length)<v&&f.unshift(0),r(y,f,v),-1==c&&(v=y.length,(c=e(E,y,j,v))<1&&(s++,r(y,j<v?S:E,v))),v=y.length):0===c&&(s++,y=[0]),d[l++]=s,c&&y[0]?y[v++]=k[x]||0:(y=[k[x]],v=1);while((x++<w||void 0!==y[0])&&b--)}return d[0]||d.shift(),h.e=u,ui(h,a?i+ut(h)+1:i)}}();function c9(t,e){var r,n,o,i,a,c=0,u=0,l=t.constructor,s=l.precision;if(ut(t)>16)throw Error(cQ+ut(t));if(!t.s)return new l(cF);for(null==e?(cK=!1,a=s):a=e,i=new l(.03125);t.abs().gte(.1);)t=t.times(i),u+=5;for(a+=Math.log(c1(2,u))/Math.LN10*2+5|0,r=n=o=new l(cF),l.precision=a;;){if(n=ui(n.times(t),a),r=r.times(++c),c8((i=o.plus(c7(n,r,a))).d).slice(0,a)===c8(o.d).slice(0,a)){for(;u--;)o=ui(o.times(o),a);return l.precision=s,null==e?(cK=!0,ui(o,s)):o}o=i}}function ut(t){for(var e=7*t.e,r=t.d[0];r>=10;r/=10)e++;return e}function ue(t,e,r){if(e>t.LN10.sd())throw cK=!0,r&&(t.precision=r),Error(cZ+"LN10 precision limit exceeded");return ui(new t(t.LN10),e)}function ur(t){for(var e="";t--;)e+="0";return e}function un(t,e){var r,n,o,i,a,c,u,l,s,f=1,p=t,h=p.d,d=p.constructor,y=d.precision;if(p.s<1)throw Error(cZ+(p.s?"NaN":"-Infinity"));if(p.eq(cF))return new d(0);if(null==e?(cK=!1,l=y):l=e,p.eq(10))return null==e&&(cK=!0),ue(d,l);if(d.precision=l+=10,n=(r=c8(h)).charAt(0),!(15e14>Math.abs(i=ut(p))))return u=ue(d,l+2,y).times(i+""),p=un(new d(n+"."+r.slice(1)),l-10).plus(u),d.precision=y,null==e?(cK=!0,ui(p,y)):p;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=c8((p=p.times(t)).d)).charAt(0),f++;for(i=ut(p),n>1?(p=new d("0."+r),i++):p=new d(n+"."+r.slice(1)),c=a=p=c7(p.minus(cF),p.plus(cF),l),s=ui(p.times(p),l),o=3;;){if(a=ui(a.times(s),l),c8((u=c.plus(c7(a,new d(o),l))).d).slice(0,l)===c8(c.d).slice(0,l))return c=c.times(2),0!==i&&(c=c.plus(ue(d,l+2,y).times(i+""))),c=c7(c,new d(f),l),d.precision=y,null==e?(cK=!0,ui(c,y)):c;c=u,o+=2}}function uo(t,e){var r,n,o;for((r=e.indexOf("."))>-1&&(e=e.replace(".","")),(n=e.search(/e/i))>0?(r<0&&(r=n),r+=+e.slice(n+1),e=e.substring(0,n)):r<0&&(r=e.length),n=0;48===e.charCodeAt(n);)++n;for(o=e.length;48===e.charCodeAt(o-1);)--o;if(e=e.slice(n,o)){if(o-=n,t.e=c0((r=r-n-1)/7),t.d=[],n=(r+1)%7,r<0&&(n+=7),n<o){for(n&&t.d.push(+e.slice(0,n)),o-=7;n<o;)t.d.push(+e.slice(n,n+=7));n=7-(e=e.slice(n)).length}else n-=o;for(;n--;)e+="0";if(t.d.push(+e),cK&&(t.e>c5||t.e<-c5))throw Error(cQ+r)}else t.s=0,t.e=0,t.d=[0];return t}function ui(t,e,r){var n,o,i,a,c,u,l,s,f=t.d;for(a=1,i=f[0];i>=10;i/=10)a++;if((n=e-a)<0)n+=7,o=e,l=f[s=0];else{if((s=Math.ceil((n+1)/7))>=(i=f.length))return t;for(a=1,l=i=f[s];i>=10;i/=10)a++;n%=7,o=n-7+a}if(void 0!==r&&(c=l/(i=c1(10,a-o-1))%10|0,u=e<0||void 0!==f[s+1]||l%i,u=r<4?(c||u)&&(0==r||r==(t.s<0?3:2)):c>5||5==c&&(4==r||u||6==r&&(n>0?o>0?l/c1(10,a-o):0:f[s-1])%10&1||r==(t.s<0?8:7))),e<1||!f[0])return u?(i=ut(t),f.length=1,e=e-i-1,f[0]=c1(10,(7-e%7)%7),t.e=c0(-e/7)||0):(f.length=1,f[0]=t.e=t.s=0),t;if(0==n?(f.length=s,i=1,s--):(f.length=s+1,i=c1(10,7-n),f[s]=o>0?(l/c1(10,a-o)%c1(10,o)|0)*i:0),u)for(;;)if(0==s){1e7==(f[0]+=i)&&(f[0]=1,++t.e);break}else{if(f[s]+=i,1e7!=f[s])break;f[s--]=0,i=1}for(n=f.length;0===f[--n];)f.pop();if(cK&&(t.e>c5||t.e<-c5))throw Error(cQ+ut(t));return t}function ua(t,e){var r,n,o,i,a,c,u,l,s,f,p=t.constructor,h=p.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new p(t),cK?ui(e,h):e;if(u=t.d,f=e.d,n=e.e,l=t.e,u=u.slice(),a=l-n){for((s=a<0)?(r=u,a=-a,c=f.length):(r=f,n=l,c=u.length),a>(o=Math.max(Math.ceil(h/7),c)+2)&&(a=o,r.length=1),r.reverse(),o=a;o--;)r.push(0);r.reverse()}else{for((s=(o=u.length)<(c=f.length))&&(c=o),o=0;o<c;o++)if(u[o]!=f[o]){s=u[o]<f[o];break}a=0}for(s&&(r=u,u=f,f=r,e.s=-e.s),c=u.length,o=f.length-c;o>0;--o)u[c++]=0;for(o=f.length;o>a;){if(u[--o]<f[o]){for(i=o;i&&0===u[--i];)u[i]=1e7-1;--u[i],u[o]+=1e7}u[o]-=f[o]}for(;0===u[--c];)u.pop();for(;0===u[0];u.shift())--n;return u[0]?(e.d=u,e.e=n,cK?ui(e,h):e):new p(0)}function uc(t,e,r){var n,o=ut(t),i=c8(t.d),a=i.length;return e?(r&&(n=r-a)>0?i=i.charAt(0)+"."+i.slice(1)+ur(n):a>1&&(i=i.charAt(0)+"."+i.slice(1)),i=i+(o<0?"e":"e+")+o):o<0?(i="0."+ur(-o-1)+i,r&&(n=r-a)>0&&(i+=ur(n))):o>=a?(i+=ur(o+1-a),r&&(n=r-o-1)>0&&(i=i+"."+ur(n))):((n=o+1)<a&&(i=i.slice(0,n)+"."+i.slice(n)),r&&(n=r-a)>0&&(o+1===a&&(i+="."),i+=ur(n))),t.s<0?"-"+i:i}function uu(t,e){if(t.length>e)return t.length=e,!0}function ul(t){if(!t||"object"!=typeof t)throw Error(cZ+"Object expected");var e,r,n,o=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<o.length;e+=3)if(void 0!==(n=t[r=o[e]]))if(c0(n)===n&&n>=o[e+1]&&n<=o[e+2])this[r]=n;else throw Error(cJ+r+": "+n);if(void 0!==(n=t[r="LN10"]))if(n==Math.LN10)this[r]=new this(n);else throw Error(cJ+r+": "+n);return this}var cU=function t(e){var r,n,o;function i(t){if(!(this instanceof i))return new i(t);if(this.constructor=i,t instanceof i){this.s=t.s,this.e=t.e,this.d=(t=t.d)?t.slice():t;return}if("number"==typeof t){if(0*t!=0)throw Error(cJ+t);if(t>0)this.s=1;else if(t<0)t=-t,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(t===~~t&&t<1e7){this.e=0,this.d=[t];return}return uo(this,t.toString())}if("string"!=typeof t)throw Error(cJ+t);if(45===t.charCodeAt(0)?(t=t.slice(1),this.s=-1):this.s=1,c2.test(t))uo(this,t);else throw Error(cJ+t)}if(i.prototype=c3,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=t,i.config=i.set=ul,void 0===e&&(e={}),e)for(r=0,o=["precision","rounding","toExpNeg","toExpPos","LN10"];r<o.length;)e.hasOwnProperty(n=o[r++])||(e[n]=this[n]);return i.config(e),i}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});cF=new cU(1);let us=cU;function uf(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var up=function(t){return t},uh={},ud=function(t){return t===uh},uy=function(t){return function e(){return 0==arguments.length||1==arguments.length&&ud(arguments.length<=0?void 0:arguments[0])?e:t.apply(void 0,arguments)}},uv=function(t){return function t(e,r){return 1===e?r:uy(function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];var a=o.filter(function(t){return t!==uh}).length;return a>=e?r.apply(void 0,o):t(e-a,uy(function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];var i=o.map(function(t){return ud(t)?e.shift():t});return r.apply(void 0,((function(t){if(Array.isArray(t))return uf(t)})(i)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(i)||function(t,e){if(t){if("string"==typeof t)return uf(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uf(t,e)}}(i)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).concat(e))}))})}(t.length,t)},um=function(t,e){for(var r=[],n=t;n<e;++n)r[n-t]=n;return r},ub=uv(function(t,e){return Array.isArray(e)?e.map(t):Object.keys(e).map(function(t){return e[t]}).map(t)}),ug=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];if(!e.length)return up;var n=e.reverse(),o=n[0],i=n.slice(1);return function(){return i.reduce(function(t,e){return e(t)},o.apply(void 0,arguments))}},ux=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},uw=function(t){var e=null,r=null;return function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return e&&o.every(function(t,r){return t===e[r]})?r:(e=o,r=t.apply(void 0,o))}};uv(function(t,e,r){var n=+t;return n+r*(e-n)}),uv(function(t,e,r){var n=e-t;return(r-t)/(n=n||1/0)}),uv(function(t,e,r){var n=e-t;return Math.max(0,Math.min(1,(r-t)/(n=n||1/0)))});let uO={rangeStep:function(t,e,r){for(var n=new us(t),o=0,i=[];n.lt(e)&&o<1e5;)i.push(n.toNumber()),n=n.add(r),o++;return i},getDigitCount:function(t){var e;return 0===t?1:Math.floor(new us(t).abs().log(10).toNumber())+1}};function uj(t){return function(t){if(Array.isArray(t))return uA(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||uP(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function uS(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t)){var r=[],n=!0,o=!1,i=void 0;try{for(var a,c=t[Symbol.iterator]();!(n=(a=c.next()).done)&&(r.push(a.value),!e||r.length!==e);n=!0);}catch(t){o=!0,i=t}finally{try{n||null==c.return||c.return()}finally{if(o)throw i}}return r}}(t,e)||uP(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function uP(t,e){if(t){if("string"==typeof t)return uA(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uA(t,e)}}function uA(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function uk(t){var e=uS(t,2),r=e[0],n=e[1],o=r,i=n;return r>n&&(o=n,i=r),[o,i]}function uE(t,e,r){if(t.lte(0))return new us(0);var n=uO.getDigitCount(t.toNumber()),o=new us(10).pow(n),i=t.div(o),a=1!==n?.05:.1,c=new us(Math.ceil(i.div(a).toNumber())).add(r).mul(a).mul(o);return e?c:new us(Math.ceil(c))}function uM(t,e,r){var n=1,o=new us(t);if(!o.isint()&&r){var i=Math.abs(t);i<1?(n=new us(10).pow(uO.getDigitCount(t)-1),o=new us(Math.floor(o.div(n).toNumber())).mul(n)):i>1&&(o=new us(Math.floor(t)))}else 0===t?o=new us(Math.floor((e-1)/2)):r||(o=new us(Math.floor(t)));var a=Math.floor((e-1)/2);return ug(ub(function(t){return o.add(new us(t-a).mul(n)).toNumber()}),um)(0,e)}var u_=uw(function(t){var e=uS(t,2),r=e[0],n=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),c=uS(uk([r,n]),2),u=c[0],l=c[1];if(u===-1/0||l===1/0){var s=l===1/0?[u].concat(uj(um(0,o-1).map(function(){return 1/0}))):[].concat(uj(um(0,o-1).map(function(){return-1/0})),[l]);return r>n?ux(s):s}if(u===l)return uM(u,o,i);var f=function t(e,r,n,o){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((r-e)/(n-1)))return{step:new us(0),tickMin:new us(0),tickMax:new us(0)};var c=uE(new us(r).sub(e).div(n-1),o,a),u=Math.ceil((i=e<=0&&r>=0?new us(0):(i=new us(e).add(r).div(2)).sub(new us(i).mod(c))).sub(e).div(c).toNumber()),l=Math.ceil(new us(r).sub(i).div(c).toNumber()),s=u+l+1;return s>n?t(e,r,n,o,a+1):(s<n&&(l=r>0?l+(n-s):l,u=r>0?u:u+(n-s)),{step:c,tickMin:i.sub(new us(u).mul(c)),tickMax:i.add(new us(l).mul(c))})}(u,l,a,i),p=f.step,h=f.tickMin,d=f.tickMax,y=uO.rangeStep(h,d.add(new us(.1).mul(p)),p);return r>n?ux(y):y});uw(function(t){var e=uS(t,2),r=e[0],n=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),c=uS(uk([r,n]),2),u=c[0],l=c[1];if(u===-1/0||l===1/0)return[r,n];if(u===l)return uM(u,o,i);var s=uE(new us(l).sub(u).div(a-1),i,0),f=ug(ub(function(t){return new us(u).add(new us(t).mul(s)).toNumber()}),um)(0,a).filter(function(t){return t>=u&&t<=l});return r>n?ux(f):f});var uT=uw(function(t,e){var r=uS(t,2),n=r[0],o=r[1],i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=uS(uk([n,o]),2),c=a[0],u=a[1];if(c===-1/0||u===1/0)return[n,o];if(c===u)return[c];var l=Math.max(e,2),s=uE(new us(u).sub(c).div(l-1),i,0),f=[].concat(uj(uO.rangeStep(new us(c),new us(u).sub(new us(.99).mul(s)),s)),[u]);return n>o?ux(f):f}),uC=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function uN(t){return(uN="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function uD(){return(uD=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function uI(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function uB(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(uB=function(){return!!t})()}function uR(t){return(uR=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function uL(t,e){return(uL=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function uz(t,e,r){return(e=uU(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function uU(t){var e=function(t,e){if("object"!=uN(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=uN(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==uN(e)?e:e+""}var uF=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=uR(t),function(t,e){if(e&&("object"===uN(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,uB()?Reflect.construct(t,e||[],uR(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&uL(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.offset,r=t.layout,n=t.width,o=t.dataKey,i=t.data,a=t.dataPointFormatter,c=t.xAxis,u=t.yAxis,l=tp(function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,uC),!1);"x"===this.props.direction&&"number"!==c.type&&tA(!1);var s=i.map(function(t){var i,s,f=a(t,o),h=f.x,d=f.y,y=f.value,v=f.errorVal;if(!v)return null;var m=[];if(Array.isArray(v)){var b=function(t){if(Array.isArray(t))return t}(v)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(v,2)||function(t,e){if(t){if("string"==typeof t)return uI(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uI(t,e)}}(v,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=b[0],s=b[1]}else i=s=v;if("vertical"===r){var g=c.scale,x=d+e,w=x+n,O=x-n,j=g(y-i),S=g(y+s);m.push({x1:S,y1:w,x2:S,y2:O}),m.push({x1:j,y1:x,x2:S,y2:x}),m.push({x1:j,y1:w,x2:j,y2:O})}else if("horizontal"===r){var P=u.scale,A=h+e,k=A-n,E=A+n,M=P(y-i),_=P(y+s);m.push({x1:k,y1:_,x2:E,y2:_}),m.push({x1:A,y1:M,x2:A,y2:_}),m.push({x1:k,y1:M,x2:E,y2:M})}return p().createElement(tC,uD({className:"recharts-errorBar",key:"bar-".concat(m.map(function(t){return"".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))},l),m.map(function(t){return p().createElement("line",uD({},t,{key:"line-".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))}))});return p().createElement(tC,{className:"recharts-errorBars"},s)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,uU(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(p().Component);function u$(t){return(u$="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function uW(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function uq(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?uW(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=u$(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u$(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==u$(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):uW(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}uz(uF,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),uz(uF,"displayName","ErrorBar");var uX=function(t){var e,r=t.children,n=t.formattedGraphicalItems,o=t.legendWidth,i=t.legendContent,a=tu(r,e5);if(!a)return null;var c=e5.defaultProps,u=void 0!==c?uq(uq({},c),a.props):{};return e=a.props&&a.props.payload?a.props&&a.props.payload:"children"===i?(n||[]).reduce(function(t,e){var r=e.item,n=e.props,o=n.sectors||n.data||[];return t.concat(o.map(function(t){return{type:a.props.iconType||r.props.legendType,value:t.name,color:t.fill,payload:t}}))},[]):(n||[]).map(function(t){var e=t.item,r=e.type.defaultProps,n=void 0!==r?uq(uq({},r),e.props):{},o=n.dataKey,i=n.name,a=n.legendType;return{inactive:n.hide,dataKey:o,type:u.iconType||a||"square",color:u1(e),value:i||o,payload:n}}),uq(uq(uq({},u),e5.getWithHeight(a,o)),{},{payload:e,item:a})};function uH(t){return(uH="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function uV(t){return function(t){if(Array.isArray(t))return uG(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return uG(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uG(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function uG(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function uY(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function uK(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?uY(Object(r),!0).forEach(function(e){uZ(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):uY(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function uZ(t,e,r){var n;return(n=function(t,e){if("object"!=uH(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=uH(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==uH(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function uJ(t,e,r){return L()(t)||L()(e)?r:k(e)?w()(t,e,r):U()(e)?e(t):r}function uQ(t,e,r,n){var o=cV()(t,function(t){return uJ(t,e)});if("number"===r){var i=o.filter(function(t){return A(t)||parseFloat(t)});return i.length?[cX()(i),cW()(i)]:[1/0,-1/0]}return(n?o.filter(function(t){return!L()(t)}):o).map(function(t){return k(t)||t instanceof Date?t:""})}var u0=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0,i=-1,a=null!=(e=null==r?void 0:r.length)?e:0;if(a<=1)return 0;if(o&&"angleAxis"===o.axisType&&1e-6>=Math.abs(Math.abs(o.range[1]-o.range[0])-360))for(var c=o.range,u=0;u<a;u++){var l=u>0?n[u-1].coordinate:n[a-1].coordinate,s=n[u].coordinate,f=u>=a-1?n[0].coordinate:n[u+1].coordinate,p=void 0;if(S(s-l)!==S(f-s)){var h=[];if(S(f-s)===S(c[1]-c[0])){p=f;var d=s+c[1]-c[0];h[0]=Math.min(d,(d+l)/2),h[1]=Math.max(d,(d+l)/2)}else{p=l;var y=f+c[1]-c[0];h[0]=Math.min(s,(y+s)/2),h[1]=Math.max(s,(y+s)/2)}var v=[Math.min(s,(p+s)/2),Math.max(s,(p+s)/2)];if(t>v[0]&&t<=v[1]||t>=h[0]&&t<=h[1]){i=n[u].index;break}}else{var m=Math.min(l,f),b=Math.max(l,f);if(t>(m+s)/2&&t<=(b+s)/2){i=n[u].index;break}}}else for(var g=0;g<a;g++)if(0===g&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g>0&&g<a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g===a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2){i=r[g].index;break}return i},u1=function(t){var e,r,n=t.type.displayName,o=null!=(e=t.type)&&e.defaultProps?uK(uK({},t.type.defaultProps),t.props):t.props,i=o.stroke,a=o.fill;switch(n){case"Line":r=i;break;case"Area":case"Radar":r=i&&"none"!==i?i:a;break;default:r=a}return r},u2=function(t){var e=t.barSize,r=t.totalSize,n=t.stackGroups,o=void 0===n?{}:n;if(!o)return{};for(var i={},a=Object.keys(o),c=0,u=a.length;c<u;c++)for(var l=o[a[c]].stackGroups,s=Object.keys(l),f=0,p=s.length;f<p;f++){var h=l[s[f]],d=h.items,y=h.cateAxisId,v=d.filter(function(t){return tn(t.type).indexOf("Bar")>=0});if(v&&v.length){var m=v[0].type.defaultProps,b=void 0!==m?uK(uK({},m),v[0].props):v[0].props,g=b.barSize,x=b[y];i[x]||(i[x]=[]);var w=L()(g)?e:g;i[x].push({item:v[0],stackList:v.slice(1),barSize:L()(w)?void 0:_(w,r,0)})}}return i},u5=function(t){var e,r=t.barGap,n=t.barCategoryGap,o=t.bandSize,i=t.sizeList,a=void 0===i?[]:i,c=t.maxBarSize,u=a.length;if(u<1)return null;var l=_(r,o,0,!0),s=[];if(a[0].barSize===+a[0].barSize){var f=!1,p=o/u,h=a.reduce(function(t,e){return t+e.barSize||0},0);(h+=(u-1)*l)>=o&&(h-=(u-1)*l,l=0),h>=o&&p>0&&(f=!0,p*=.9,h=u*p);var d={offset:((o-h)/2|0)-l,size:0};e=a.reduce(function(t,e){var r={item:e.item,position:{offset:d.offset+d.size+l,size:f?p:e.barSize}},n=[].concat(uV(t),[r]);return d=n[n.length-1].position,e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:d})}),n},s)}else{var y=_(n,o,0,!0);o-2*y-(u-1)*l<=0&&(l=0);var v=(o-2*y-(u-1)*l)/u;v>1&&(v>>=0);var m=c===+c?Math.min(v,c):v;e=a.reduce(function(t,e,r){var n=[].concat(uV(t),[{item:e.item,position:{offset:y+(v+l)*r+(v-m)/2,size:m}}]);return e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:n[n.length-1].position})}),n},s)}return e},u3=function(t,e,r,n){var o=r.children,i=r.width,a=r.margin,c=uX({children:o,legendWidth:i-(a.left||0)-(a.right||0)});if(c){var u=n||{},l=u.width,s=u.height,f=c.align,p=c.verticalAlign,h=c.layout;if(("vertical"===h||"horizontal"===h&&"middle"===p)&&"center"!==f&&A(t[f]))return uK(uK({},t),{},uZ({},f,t[f]+(l||0)));if(("horizontal"===h||"vertical"===h&&"center"===f)&&"middle"!==p&&A(t[p]))return uK(uK({},t),{},uZ({},p,t[p]+(s||0)))}return t},u4=function(t,e,r,n,o){var i=tc(e.props.children,uF).filter(function(t){var e;return e=t.props.direction,!!L()(o)||("horizontal"===n?"yAxis"===o:"vertical"===n||"x"===e?"xAxis"===o:"y"!==e||"yAxis"===o)});if(i&&i.length){var a=i.map(function(t){return t.props.dataKey});return t.reduce(function(t,e){var n=uJ(e,r);if(L()(n))return t;var o=Array.isArray(n)?[cX()(n),cW()(n)]:[n,n],i=a.reduce(function(t,r){var n=uJ(e,r,0),i=o[0]-Math.abs(Array.isArray(n)?n[0]:n),a=o[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(i,t[0]),Math.max(a,t[1])]},[1/0,-1/0]);return[Math.min(i[0],t[0]),Math.max(i[1],t[1])]},[1/0,-1/0])}return null},u6=function(t,e,r,n,o){var i=e.map(function(e){return u4(t,e,r,o,n)}).filter(function(t){return!L()(t)});return i&&i.length?i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]):null},u8=function(t,e,r,n,o){var i=e.map(function(e){var i=e.props.dataKey;return"number"===r&&i&&u4(t,e,i,n)||uQ(t,i,r,o)});if("number"===r)return i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]);var a={};return i.reduce(function(t,e){for(var r=0,n=e.length;r<n;r++)a[e[r]]||(a[e[r]]=!0,t.push(e[r]));return t},[])},u7=function(t,e){return"horizontal"===t&&"xAxis"===e||"vertical"===t&&"yAxis"===e||"centric"===t&&"angleAxis"===e||"radial"===t&&"radiusAxis"===e},u9=function(t,e,r,n){if(n)return t.map(function(t){return t.coordinate});var o,i,a=t.map(function(t){return t.coordinate===e&&(o=!0),t.coordinate===r&&(i=!0),t.coordinate});return o||a.push(e),i||a.push(r),a},lt=function(t,e,r){if(!t)return null;var n=t.scale,o=t.duplicateDomain,i=t.type,a=t.range,c="scaleBand"===t.realScaleType?n.bandwidth()/2:2,u=(e||r)&&"category"===i&&n.bandwidth?n.bandwidth()/c:0;return(u="angleAxis"===t.axisType&&(null==a?void 0:a.length)>=2?2*S(a[0]-a[1])*u:u,e&&(t.ticks||t.niceTicks))?(t.ticks||t.niceTicks).map(function(t){return{coordinate:n(o?o.indexOf(t):t)+u,value:t,offset:u}}).filter(function(t){return!g()(t.coordinate)}):t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(t,e){return{coordinate:n(t)+u,value:t,index:e,offset:u}}):n.ticks&&!r?n.ticks(t.tickCount).map(function(t){return{coordinate:n(t)+u,value:t,offset:u}}):n.domain().map(function(t,e){return{coordinate:n(t)+u,value:o?o[t]:t,index:e,offset:u}})},le=new WeakMap,lr=function(t,e){if("function"!=typeof e)return t;le.has(t)||le.set(t,new WeakMap);var r=le.get(t);if(r.has(e))return r.get(e);var n=function(){t.apply(void 0,arguments),e.apply(void 0,arguments)};return r.set(e,n),n},ln=function(t,e,r){var o=t.scale,i=t.type,a=t.layout,c=t.axisType;if("auto"===o)return"radial"===a&&"radiusAxis"===c?{scale:nO(),realScaleType:"band"}:"radial"===a&&"angleAxis"===c?{scale:il(),realScaleType:"linear"}:"category"===i&&e&&(e.indexOf("LineChart")>=0||e.indexOf("AreaChart")>=0||e.indexOf("ComposedChart")>=0&&!r)?{scale:nj(),realScaleType:"point"}:"category"===i?{scale:nO(),realScaleType:"band"}:{scale:il(),realScaleType:"linear"};if(m()(o)){var u="scale".concat(ei()(o));return{scale:(n[u]||nj)(),realScaleType:n[u]?u:"point"}}return U()(o)?{scale:o}:{scale:nj(),realScaleType:"point"}},lo=function(t){var e=t.domain();if(e&&!(e.length<=2)){var r=e.length,n=t.range(),o=Math.min(n[0],n[1])-1e-4,i=Math.max(n[0],n[1])+1e-4,a=t(e[0]),c=t(e[r-1]);(a<o||a>i||c<o||c>i)&&t.domain([e[0],e[r-1]])}},li=function(t,e){if(!t)return null;for(var r=0,n=t.length;r<n;r++)if(t[r].item===e)return t[r].position;return null},la=function(t,e){if(!e||2!==e.length||!A(e[0])||!A(e[1]))return t;var r=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]),o=[t[0],t[1]];return(!A(t[0])||t[0]<r)&&(o[0]=r),(!A(t[1])||t[1]>n)&&(o[1]=n),o[0]>n&&(o[0]=n),o[1]<r&&(o[1]=r),o},lc={sign:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0,a=0;a<e;++a){var c=g()(t[a][r][1])?t[a][r][0]:t[a][r][1];c>=0?(t[a][r][0]=o,t[a][r][1]=o+c,o=t[a][r][1]):(t[a][r][0]=i,t[a][r][1]=i+c,i=t[a][r][1])}},expand:function(t,e){if((n=t.length)>0){for(var r,n,o,i=0,a=t[0].length;i<a;++i){for(o=r=0;r<n;++r)o+=t[r][i][1]||0;if(o)for(r=0;r<n;++r)t[r][i][1]/=o}cE(t,e)}},none:cE,silhouette:function(t,e){if((r=t.length)>0){for(var r,n=0,o=t[e[0]],i=o.length;n<i;++n){for(var a=0,c=0;a<r;++a)c+=t[a][n][1]||0;o[n][1]+=o[n][0]=-c/2}cE(t,e)}},wiggle:function(t,e){if((o=t.length)>0&&(n=(r=t[e[0]]).length)>0){for(var r,n,o,i=0,a=1;a<n;++a){for(var c=0,u=0,l=0;c<o;++c){for(var s=t[e[c]],f=s[a][1]||0,p=(f-(s[a-1][1]||0))/2,h=0;h<c;++h){var d=t[e[h]];p+=(d[a][1]||0)-(d[a-1][1]||0)}u+=f,l+=p*f}r[a-1][1]+=r[a-1][0]=i,u&&(i-=l/u)}r[a-1][1]+=r[a-1][0]=i,cE(t,e)}},positive:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0;i<e;++i){var a=g()(t[i][r][1])?t[i][r][0]:t[i][r][1];a>=0?(t[i][r][0]=o,t[i][r][1]=o+a,o=t[i][r][1]):(t[i][r][0]=0,t[i][r][1]=0)}}},lu=function(t,e,r){var n=e.map(function(t){return t.props.dataKey}),o=lc[r];return(function(){var t=ew([]),e=c_,r=cE,n=cT;function o(o){var i,a,c=Array.from(t.apply(this,arguments),cC),u=c.length,l=-1;for(let t of o)for(i=0,++l;i<u;++i)(c[i][l]=[0,+n(t,c[i].key,l,o)]).data=t;for(i=0,a=cM(e(c));i<u;++i)c[a[i]].index=i;return r(c,a),c}return o.keys=function(e){return arguments.length?(t="function"==typeof e?e:ew(Array.from(e)),o):t},o.value=function(t){return arguments.length?(n="function"==typeof t?t:ew(+t),o):n},o.order=function(t){return arguments.length?(e=null==t?c_:"function"==typeof t?t:ew(Array.from(t)),o):e},o.offset=function(t){return arguments.length?(r=null==t?cE:t,o):r},o})().keys(n).value(function(t,e){return+uJ(t,e,0)}).order(c_).offset(o)(t)},ll=function(t,e,r,n,o,i){if(!t)return null;var a=(i?e.reverse():e).reduce(function(t,e){var o,i=null!=(o=e.type)&&o.defaultProps?uK(uK({},e.type.defaultProps),e.props):e.props,a=i.stackId;if(i.hide)return t;var c=i[r],u=t[c]||{hasStack:!1,stackGroups:{}};if(k(a)){var l=u.stackGroups[a]||{numericAxisId:r,cateAxisId:n,items:[]};l.items.push(e),u.hasStack=!0,u.stackGroups[a]=l}else u.stackGroups[M("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[e]};return uK(uK({},t),{},uZ({},c,u))},{});return Object.keys(a).reduce(function(e,i){var c=a[i];return c.hasStack&&(c.stackGroups=Object.keys(c.stackGroups).reduce(function(e,i){var a=c.stackGroups[i];return uK(uK({},e),{},uZ({},i,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:lu(t,a.items,o)}))},{})),uK(uK({},e),{},uZ({},i,c))},{})},ls=function(t,e){var r=e.realScaleType,n=e.type,o=e.tickCount,i=e.originalDomain,a=e.allowDecimals,c=r||e.scale;if("auto"!==c&&"linear"!==c)return null;if(o&&"number"===n&&i&&("auto"===i[0]||"auto"===i[1])){var u=t.domain();if(!u.length)return null;var l=u_(u,o,a);return t.domain([cX()(l),cW()(l)]),{niceTicks:l}}return o&&"number"===n?{niceTicks:uT(t.domain(),o,a)}:null};function lf(t){var e=t.axis,r=t.ticks,n=t.bandSize,o=t.entry,i=t.index,a=t.dataKey;if("category"===e.type){if(!e.allowDuplicatedCategory&&e.dataKey&&!L()(o[e.dataKey])){var c=D(r,"value",o[e.dataKey]);if(c)return c.coordinate+n/2}return r[i]?r[i].coordinate+n/2:null}var u=uJ(o,L()(a)?e.dataKey:a);return L()(u)?null:e.scale(u)}var lp=function(t){var e=t.axis,r=t.ticks,n=t.offset,o=t.bandSize,i=t.entry,a=t.index;if("category"===e.type)return r[a]?r[a].coordinate+n:null;var c=uJ(i,e.dataKey,e.domain[a]);return L()(c)?null:e.scale(c)-o/2+n},lh=function(t){var e=t.numericAxis,r=e.scale.domain();if("number"===e.type){var n=Math.min(r[0],r[1]),o=Math.max(r[0],r[1]);return n<=0&&o>=0?0:o<0?o:n}return r[0]},ld=function(t,e){var r,n=(null!=(r=t.type)&&r.defaultProps?uK(uK({},t.type.defaultProps),t.props):t.props).stackId;if(k(n)){var o=e[n];if(o){var i=o.items.indexOf(t);return i>=0?o.stackedData[i]:null}}return null},ly=function(t,e,r){return Object.keys(t).reduce(function(n,o){var i=t[o].stackedData.reduce(function(t,n){var o=n.slice(e,r+1).reduce(function(t,e){return[cX()(e.concat([t[0]]).filter(A)),cW()(e.concat([t[1]]).filter(A))]},[1/0,-1/0]);return[Math.min(t[0],o[0]),Math.max(t[1],o[1])]},[1/0,-1/0]);return[Math.min(i[0],n[0]),Math.max(i[1],n[1])]},[1/0,-1/0]).map(function(t){return t===1/0||t===-1/0?0:t})},lv=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,lm=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,lb=function(t,e,r){if(U()(t))return t(e,r);if(!Array.isArray(t))return e;var n=[];if(A(t[0]))n[0]=r?t[0]:Math.min(t[0],e[0]);else if(lv.test(t[0])){var o=+lv.exec(t[0])[1];n[0]=e[0]-o}else U()(t[0])?n[0]=t[0](e[0]):n[0]=e[0];if(A(t[1]))n[1]=r?t[1]:Math.max(t[1],e[1]);else if(lm.test(t[1])){var i=+lm.exec(t[1])[1];n[1]=e[1]+i}else U()(t[1])?n[1]=t[1](e[1]):n[1]=e[1];return n},lg=function(t,e,r){if(t&&t.scale&&t.scale.bandwidth){var n=t.scale.bandwidth();if(!r||n>0)return n}if(t&&e&&e.length>=2){for(var o=tP()(e,function(t){return t.coordinate}),i=1/0,a=1,c=o.length;a<c;a++){var u=o[a],l=o[a-1];i=Math.min((u.coordinate||0)-(l.coordinate||0),i)}return i===1/0?0:i}return r?void 0:0},lx=function(t,e,r){return!t||!t.length||cY()(t,w()(r,"type.defaultProps.domain"))?e:t},lw=function(t,e){var r=t.type.defaultProps?uK(uK({},t.type.defaultProps),t.props):t.props,n=r.dataKey,o=r.name,i=r.unit,a=r.formatter,c=r.tooltipType,u=r.chartType,l=r.hide;return uK(uK({},tp(t,!1)),{},{dataKey:n,unit:i,formatter:a,name:o||n,color:u1(t),value:uJ(e,n),type:c,payload:e,chartType:u,hide:l})};function lO(t){return(lO="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function lj(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lS(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lj(Object(r),!0).forEach(function(e){lP(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lj(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function lP(t,e,r){var n;return(n=function(t,e){if("object"!=lO(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lO(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==lO(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var lA=["Webkit","Moz","O","ms"],lk=function(t,e){if(!t)return null;var r=t.replace(/(\w)/,function(t){return t.toUpperCase()}),n=lA.reduce(function(t,n){return lS(lS({},t),{},lP({},n+r,e))},{});return n[t]=e,n};function lE(t){return(lE="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function lM(){return(lM=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function l_(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lT(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?l_(Object(r),!0).forEach(function(e){lB(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):l_(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function lC(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,lR(n.key),n)}}function lN(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(lN=function(){return!!t})()}function lD(t){return(lD=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function lI(t,e){return(lI=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function lB(t,e,r){return(e=lR(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function lR(t){var e=function(t,e){if("object"!=lE(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lE(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==lE(e)?e:e+""}var lL=function(t){var e=t.data,r=t.startIndex,n=t.endIndex,o=t.x,i=t.width,a=t.travellerWidth;if(!e||!e.length)return{};var c=e.length,u=nj().domain(tj()(0,c)).range([o,o+i-a]),l=u.domain().map(function(t){return u(t)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:u(r),endX:u(n),scale:u,scaleValues:l}},lz=function(t){return t.changedTouches&&!!t.changedTouches.length},lU=function(t){var e,r;function n(t){var e,r,o;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return r=n,o=[t],r=lD(r),lB(e=function(t,e){if(e&&("object"===lE(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,lN()?Reflect.construct(r,o||[],lD(this).constructor):r.apply(this,o)),"handleDrag",function(t){e.leaveTimer&&(clearTimeout(e.leaveTimer),e.leaveTimer=null),e.state.isTravellerMoving?e.handleTravellerMove(t):e.state.isSlideMoving&&e.handleSlideDrag(t)}),lB(e,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&e.handleDrag(t.changedTouches[0])}),lB(e,"handleDragEnd",function(){e.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var t=e.props,r=t.endIndex,n=t.onDragEnd,o=t.startIndex;null==n||n({endIndex:r,startIndex:o})}),e.detachDragEndListener()}),lB(e,"handleLeaveWrapper",function(){(e.state.isTravellerMoving||e.state.isSlideMoving)&&(e.leaveTimer=window.setTimeout(e.handleDragEnd,e.props.leaveTimeOut))}),lB(e,"handleEnterSlideOrTraveller",function(){e.setState({isTextActive:!0})}),lB(e,"handleLeaveSlideOrTraveller",function(){e.setState({isTextActive:!1})}),lB(e,"handleSlideDragStart",function(t){var r=lz(t)?t.changedTouches[0]:t;e.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:r.pageX}),e.attachDragEndListener()}),e.travellerDragStartHandlers={startX:e.handleTravellerDragStart.bind(e,"startX"),endX:e.handleTravellerDragStart.bind(e,"endX")},e.state={},e}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&lI(n,t),e=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(t){var e=t.startX,r=t.endX,o=this.state.scaleValues,i=this.props,a=i.gap,c=i.data.length-1,u=Math.min(e,r),l=Math.max(e,r),s=n.getIndexInRange(o,u),f=n.getIndexInRange(o,l);return{startIndex:s-s%a,endIndex:f===c?c:f-f%a}}},{key:"getTextOfTick",value:function(t){var e=this.props,r=e.data,n=e.tickFormatter,o=e.dataKey,i=uJ(r[t],o,t);return U()(n)?n(i,t):i}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(t){var e=this.state,r=e.slideMoveStartX,n=e.startX,o=e.endX,i=this.props,a=i.x,c=i.width,u=i.travellerWidth,l=i.startIndex,s=i.endIndex,f=i.onChange,p=t.pageX-r;p>0?p=Math.min(p,a+c-u-o,a+c-u-n):p<0&&(p=Math.max(p,a-n,a-o));var h=this.getIndex({startX:n+p,endX:o+p});(h.startIndex!==l||h.endIndex!==s)&&f&&f(h),this.setState({startX:n+p,endX:o+p,slideMoveStartX:t.pageX})}},{key:"handleTravellerDragStart",value:function(t,e){var r=lz(e)?e.changedTouches[0]:e;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:t,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(t){var e=this.state,r=e.brushMoveStartX,n=e.movingTravellerId,o=e.endX,i=e.startX,a=this.state[n],c=this.props,u=c.x,l=c.width,s=c.travellerWidth,f=c.onChange,p=c.gap,h=c.data,d={startX:this.state.startX,endX:this.state.endX},y=t.pageX-r;y>0?y=Math.min(y,u+l-s-a):y<0&&(y=Math.max(y,u-a)),d[n]=a+y;var v=this.getIndex(d),m=v.startIndex,b=v.endIndex,g=function(){var t=h.length-1;return"startX"===n&&(o>i?m%p==0:b%p==0)||!!(o<i)&&b===t||"endX"===n&&(o>i?b%p==0:m%p==0)||!!(o>i)&&b===t};this.setState(lB(lB({},n,a+y),"brushMoveStartX",t.pageX),function(){f&&g()&&f(v)})}},{key:"handleTravellerMoveKeyboard",value:function(t,e){var r=this,n=this.state,o=n.scaleValues,i=n.startX,a=n.endX,c=this.state[e],u=o.indexOf(c);if(-1!==u){var l=u+t;if(-1!==l&&!(l>=o.length)){var s=o[l];"startX"===e&&s>=a||"endX"===e&&s<=i||this.setState(lB({},e,s),function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))})}}}},{key:"renderBackground",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,o=t.height,i=t.fill,a=t.stroke;return p().createElement("rect",{stroke:a,fill:i,x:e,y:r,width:n,height:o})}},{key:"renderPanorama",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,o=t.height,i=t.data,a=t.children,c=t.padding,u=f.Children.only(a);return u?p().cloneElement(u,{x:e,y:r,width:n,height:o,margin:c,compact:!0,data:i}):null}},{key:"renderTravellerLayer",value:function(t,e){var r,o,i=this,a=this.props,c=a.y,u=a.travellerWidth,l=a.height,s=a.traveller,f=a.ariaLabel,h=a.data,d=a.startIndex,y=a.endIndex,v=Math.max(t,this.props.x),m=lT(lT({},tp(this.props,!1)),{},{x:v,y:c,width:u,height:l}),b=f||"Min value: ".concat(null==(r=h[d])?void 0:r.name,", Max value: ").concat(null==(o=h[y])?void 0:o.name);return p().createElement(tC,{tabIndex:0,role:"slider","aria-label":b,"aria-valuenow":t,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[e],onTouchStart:this.travellerDragStartHandlers[e],onKeyDown:function(t){["ArrowLeft","ArrowRight"].includes(t.key)&&(t.preventDefault(),t.stopPropagation(),i.handleTravellerMoveKeyboard("ArrowRight"===t.key?1:-1,e))},onFocus:function(){i.setState({isTravellerFocused:!0})},onBlur:function(){i.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},n.renderTraveller(s,m))}},{key:"renderSlide",value:function(t,e){var r=this.props,n=r.y,o=r.height,i=r.stroke,a=r.travellerWidth,c=Math.min(t,e)+a,u=Math.max(Math.abs(e-t)-a,0);return p().createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:i,fillOpacity:.2,x:c,y:n,width:u,height:o})}},{key:"renderText",value:function(){var t=this.props,e=t.startIndex,r=t.endIndex,n=t.y,o=t.height,i=t.travellerWidth,a=t.stroke,c=this.state,u=c.startX,l=c.endX,s={pointerEvents:"none",fill:a};return p().createElement(tC,{className:"recharts-brush-texts"},p().createElement(n3,lM({textAnchor:"end",verticalAnchor:"middle",x:Math.min(u,l)-5,y:n+o/2},s),this.getTextOfTick(e)),p().createElement(n3,lM({textAnchor:"start",verticalAnchor:"middle",x:Math.max(u,l)+i+5,y:n+o/2},s),this.getTextOfTick(r)))}},{key:"render",value:function(){var t=this.props,e=t.data,r=t.className,n=t.children,o=t.x,i=t.y,a=t.width,c=t.height,u=t.alwaysShowText,l=this.state,s=l.startX,f=l.endX,d=l.isTextActive,y=l.isSlideMoving,v=l.isTravellerMoving,m=l.isTravellerFocused;if(!e||!e.length||!A(o)||!A(i)||!A(a)||!A(c)||a<=0||c<=0)return null;var b=(0,h.A)("recharts-brush",r),g=1===p().Children.count(n),x=lk("userSelect","none");return p().createElement(tC,{className:b,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:x},this.renderBackground(),g&&this.renderPanorama(),this.renderSlide(s,f),this.renderTravellerLayer(s,"startX"),this.renderTravellerLayer(f,"endX"),(d||y||v||m||u)&&this.renderText())}}],r=[{key:"renderDefaultTraveller",value:function(t){var e=t.x,r=t.y,n=t.width,o=t.height,i=t.stroke,a=Math.floor(r+o/2)-1;return p().createElement(p().Fragment,null,p().createElement("rect",{x:e,y:r,width:n,height:o,fill:i,stroke:"none"}),p().createElement("line",{x1:e+1,y1:a,x2:e+n-1,y2:a,fill:"none",stroke:"#fff"}),p().createElement("line",{x1:e+1,y1:a+2,x2:e+n-1,y2:a+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(t,e){var r;return p().isValidElement(t)?p().cloneElement(t,e):U()(t)?t(e):n.renderDefaultTraveller(e)}},{key:"getDerivedStateFromProps",value:function(t,e){var r=t.data,n=t.width,o=t.x,i=t.travellerWidth,a=t.updateId,c=t.startIndex,u=t.endIndex;if(r!==e.prevData||a!==e.prevUpdateId)return lT({prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n},r&&r.length?lL({data:r,width:n,x:o,travellerWidth:i,startIndex:c,endIndex:u}):{scale:null,scaleValues:null});if(e.scale&&(n!==e.prevWidth||o!==e.prevX||i!==e.prevTravellerWidth)){e.scale.range([o,o+n-i]);var l=e.scale.domain().map(function(t){return e.scale(t)});return{prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n,startX:e.scale(t.startIndex),endX:e.scale(t.endIndex),scaleValues:l}}return null}},{key:"getIndexInRange",value:function(t,e){for(var r=t.length,n=0,o=r-1;o-n>1;){var i=Math.floor((n+o)/2);t[i]>e?o=i:n=i}return e>=t[o]?o:n}}],e&&lC(n.prototype,e),r&&lC(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(f.PureComponent);function lF(t){return(lF="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l$(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lW(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?l$(Object(r),!0).forEach(function(e){(function(t,e,r){var n;(n=function(t,e){if("object"!=lF(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lF(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==lF(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r})(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):l$(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}lB(lU,"displayName","Brush"),lB(lU,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var lq=Math.PI/180,lX=function(t,e,r,n){return{x:t+Math.cos(-lq*n)*r,y:e+Math.sin(-lq*n)*r}},lH=function(t,e){var r=t.x,n=t.y;return Math.sqrt(Math.pow(r-e.x,2)+Math.pow(n-e.y,2))},lV=function(t,e){var r=t.x,n=t.y,o=e.cx,i=e.cy,a=lH({x:r,y:n},{x:o,y:i});if(a<=0)return{radius:a};var c=Math.acos((r-o)/a);return n>i&&(c=2*Math.PI-c),{radius:a,angle:180*c/Math.PI,angleInRadian:c}},lG=function(t){var e=t.startAngle,r=t.endAngle,n=Math.min(Math.floor(e/360),Math.floor(r/360));return{startAngle:e-360*n,endAngle:r-360*n}},lY=function(t,e){var r,n=lV({x:t.x,y:t.y},e),o=n.radius,i=n.angle,a=e.innerRadius,c=e.outerRadius;if(o<a||o>c)return!1;if(0===o)return!0;var u=lG(e),l=u.startAngle,s=u.endAngle,f=i;if(l<=s){for(;f>s;)f-=360;for(;f<l;)f+=360;r=f>=l&&f<=s}else{for(;f>l;)f-=360;for(;f<s;)f+=360;r=f>=s&&f<=l}return r?lW(lW({},e),{},{radius:o,angle:f+360*Math.min(Math.floor(e.startAngle/360),Math.floor(e.endAngle/360))}):null};function lK(t){return(lK="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var lZ=["offset"];function lJ(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function lQ(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function l0(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lQ(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=lK(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lK(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==lK(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lQ(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function l1(){return(l1=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var l2=function(t){var e=t.value,r=t.formatter,n=L()(t.children)?e:t.children;return U()(r)?r(n):n},l5=function(t,e,r){var n,o,i=t.position,a=t.viewBox,c=t.offset,u=t.className,l=a.cx,s=a.cy,f=a.innerRadius,d=a.outerRadius,y=a.startAngle,v=a.endAngle,m=a.clockWise,b=(f+d)/2,g=S(v-y)*Math.min(Math.abs(v-y),360),x=g>=0?1:-1;"insideStart"===i?(n=y+x*c,o=m):"insideEnd"===i?(n=v-x*c,o=!m):"end"===i&&(n=v+x*c,o=m),o=g<=0?o:!o;var w=lX(l,s,b,n),O=lX(l,s,b,n+(o?1:-1)*359),j="M".concat(w.x,",").concat(w.y,"\n    A").concat(b,",").concat(b,",0,1,").concat(+!o,",\n    ").concat(O.x,",").concat(O.y),P=L()(t.id)?M("recharts-radial-line-"):t.id;return p().createElement("text",l1({},r,{dominantBaseline:"central",className:(0,h.A)("recharts-radial-bar-label",u)}),p().createElement("defs",null,p().createElement("path",{id:P,d:j})),p().createElement("textPath",{xlinkHref:"#".concat(P)},e))},l3=function(t){var e=t.viewBox,r=t.offset,n=t.position,o=e.cx,i=e.cy,a=e.innerRadius,c=e.outerRadius,u=(e.startAngle+e.endAngle)/2;if("outside"===n){var l=lX(o,i,c+r,u),s=l.x;return{x:s,y:l.y,textAnchor:s>=o?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"end"};var f=lX(o,i,(a+c)/2,u);return{x:f.x,y:f.y,textAnchor:"middle",verticalAnchor:"middle"}},l4=function(t){var e=t.viewBox,r=t.parentViewBox,n=t.offset,o=t.position,i=e.x,a=e.y,c=e.width,u=e.height,l=u>=0?1:-1,s=l*n,f=l>0?"end":"start",p=l>0?"start":"end",h=c>=0?1:-1,d=h*n,y=h>0?"end":"start",v=h>0?"start":"end";if("top"===o)return l0(l0({},{x:i+c/2,y:a-l*n,textAnchor:"middle",verticalAnchor:f}),r?{height:Math.max(a-r.y,0),width:c}:{});if("bottom"===o)return l0(l0({},{x:i+c/2,y:a+u+s,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(r.y+r.height-(a+u),0),width:c}:{});if("left"===o){var m={x:i-d,y:a+u/2,textAnchor:y,verticalAnchor:"middle"};return l0(l0({},m),r?{width:Math.max(m.x-r.x,0),height:u}:{})}if("right"===o){var b={x:i+c+d,y:a+u/2,textAnchor:v,verticalAnchor:"middle"};return l0(l0({},b),r?{width:Math.max(r.x+r.width-b.x,0),height:u}:{})}var g=r?{width:c,height:u}:{};return"insideLeft"===o?l0({x:i+d,y:a+u/2,textAnchor:v,verticalAnchor:"middle"},g):"insideRight"===o?l0({x:i+c-d,y:a+u/2,textAnchor:y,verticalAnchor:"middle"},g):"insideTop"===o?l0({x:i+c/2,y:a+s,textAnchor:"middle",verticalAnchor:p},g):"insideBottom"===o?l0({x:i+c/2,y:a+u-s,textAnchor:"middle",verticalAnchor:f},g):"insideTopLeft"===o?l0({x:i+d,y:a+s,textAnchor:v,verticalAnchor:p},g):"insideTopRight"===o?l0({x:i+c-d,y:a+s,textAnchor:y,verticalAnchor:p},g):"insideBottomLeft"===o?l0({x:i+d,y:a+u-s,textAnchor:v,verticalAnchor:f},g):"insideBottomRight"===o?l0({x:i+c-d,y:a+u-s,textAnchor:y,verticalAnchor:f},g):$()(o)&&(A(o.x)||P(o.x))&&(A(o.y)||P(o.y))?l0({x:i+_(o.x,c),y:a+_(o.y,u),textAnchor:"end",verticalAnchor:"end"},g):l0({x:i+c/2,y:a+u/2,textAnchor:"middle",verticalAnchor:"middle"},g)};function l6(t){var e,r=t.offset,n=l0({offset:void 0===r?5:r},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,lZ)),o=n.viewBox,i=n.position,a=n.value,c=n.children,u=n.content,l=n.className,s=n.textBreakAll;if(!o||L()(a)&&L()(c)&&!(0,f.isValidElement)(u)&&!U()(u))return null;if((0,f.isValidElement)(u))return(0,f.cloneElement)(u,n);if(U()(u)){if(e=(0,f.createElement)(u,n),(0,f.isValidElement)(e))return e}else e=l2(n);var d="cx"in o&&A(o.cx),y=tp(n,!0);if(d&&("insideStart"===i||"insideEnd"===i||"end"===i))return l5(n,e,y);var v=d?l3(n):l4(n);return p().createElement(n3,l1({className:(0,h.A)("recharts-label",void 0===l?"":l)},y,v,{breakAll:s}),e)}l6.displayName="Label";var l8=function(t){var e=t.cx,r=t.cy,n=t.angle,o=t.startAngle,i=t.endAngle,a=t.r,c=t.radius,u=t.innerRadius,l=t.outerRadius,s=t.x,f=t.y,p=t.top,h=t.left,d=t.width,y=t.height,v=t.clockWise,m=t.labelViewBox;if(m)return m;if(A(d)&&A(y)){if(A(s)&&A(f))return{x:s,y:f,width:d,height:y};if(A(p)&&A(h))return{x:p,y:h,width:d,height:y}}return A(s)&&A(f)?{x:s,y:f,width:0,height:0}:A(e)&&A(r)?{cx:e,cy:r,startAngle:o||n||0,endAngle:i||n||0,innerRadius:u||0,outerRadius:l||c||a||0,clockWise:v}:t.viewBox?t.viewBox:{}};l6.parseViewBox=l8,l6.renderCallByParent=function(t,e){var r,n,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&o&&!t.label)return null;var i=t.children,a=l8(t),c=tc(i,l6).map(function(t,r){return(0,f.cloneElement)(t,{viewBox:e||a,key:"label-".concat(r)})});if(!o)return c;return[(r=t.label,n=e||a,!r?null:!0===r?p().createElement(l6,{key:"label-implicit",viewBox:n}):k(r)?p().createElement(l6,{key:"label-implicit",viewBox:n,value:r}):(0,f.isValidElement)(r)?r.type===l6?(0,f.cloneElement)(r,{key:"label-implicit",viewBox:n}):p().createElement(l6,{key:"label-implicit",content:r,viewBox:n}):U()(r)?p().createElement(l6,{key:"label-implicit",content:r,viewBox:n}):$()(r)?p().createElement(l6,l1({viewBox:n},r,{key:"label-implicit"})):null)].concat(function(t){if(Array.isArray(t))return lJ(t)}(c)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(c)||function(t,e){if(t){if("string"==typeof t)return lJ(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return lJ(t,e)}}(c)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())};var l7=function(t,e){var r=t.alwaysShow,n=t.ifOverflow;return r&&(n="extendDomain"),n===e},l9=r(69691),st=r.n(l9),se=r(47212),sr=r.n(se),sn=function(t){return null};sn.displayName="Cell";var so=r(5359),si=r.n(so);function sa(t){return(sa="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var sc=["valueAccessor"],su=["data","dataKey","clockWise","id","textBreakAll"];function sl(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function ss(){return(ss=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sf(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sp(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sf(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=sa(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sa(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sa(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sf(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function sh(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var sd=function(t){return Array.isArray(t.value)?si()(t.value):t.value};function sy(t){var e=t.valueAccessor,r=void 0===e?sd:e,n=sh(t,sc),o=n.data,i=n.dataKey,a=n.clockWise,c=n.id,u=n.textBreakAll,l=sh(n,su);return o&&o.length?p().createElement(tC,{className:"recharts-label-list"},o.map(function(t,e){var n=L()(i)?r(t,e):uJ(t&&t.payload,i),o=L()(c)?{}:{id:"".concat(c,"-").concat(e)};return p().createElement(l6,ss({},tp(t,!0),l,o,{parentViewBox:t.parentViewBox,value:n,textBreakAll:u,viewBox:l6.parseViewBox(L()(a)?t:sp(sp({},t),{},{clockWise:a})),key:"label-".concat(e),index:e}))})):null}sy.displayName="LabelList",sy.renderCallByParent=function(t,e){var r,n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&n&&!t.label)return null;var o=tc(t.children,sy).map(function(t,r){return(0,f.cloneElement)(t,{data:e,key:"labelList-".concat(r)})});return n?[(r=t.label,!r?null:!0===r?p().createElement(sy,{key:"labelList-implicit",data:e}):p().isValidElement(r)||U()(r)?p().createElement(sy,{key:"labelList-implicit",data:e,content:r}):$()(r)?p().createElement(sy,ss({data:e},r,{key:"labelList-implicit"})):null)].concat(function(t){if(Array.isArray(t))return sl(t)}(o)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(o)||function(t,e){if(t){if("string"==typeof t)return sl(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sl(t,e)}}(o)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):o};var sv=r(38404),sm=r.n(sv),sb=r(98451),sg=r.n(sb);function sx(t){return(sx="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sw(){return(sw=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sO(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function sj(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sS(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sj(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=sx(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sx(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sx(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sj(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var sP=function(t,e,r,n,o){var i,a=r-n;return"M ".concat(t,",").concat(e)+"L ".concat(t+r,",").concat(e)+"L ".concat(t+r-a/2,",").concat(e+o)+"L ".concat(t+r-a/2-n,",").concat(e+o)+"L ".concat(t,",").concat(e," Z")},sA={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},sk=function(t){var e,r=sS(sS({},sA),t),n=(0,f.useRef)(),o=function(t){if(Array.isArray(t))return t}(e=(0,f.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(e,2)||function(t,e){if(t){if("string"==typeof t)return sO(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sO(t,e)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o[1];(0,f.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var t=n.current.getTotalLength();t&&a(t)}catch(t){}},[]);var c=r.x,u=r.y,l=r.upperWidth,s=r.lowerWidth,d=r.height,y=r.className,v=r.animationEasing,m=r.animationDuration,b=r.animationBegin,g=r.isUpdateAnimationActive;if(c!==+c||u!==+u||l!==+l||s!==+s||d!==+d||0===l&&0===s||0===d)return null;var x=(0,h.A)("recharts-trapezoid",y);return g?p().createElement(ni,{canBegin:i>0,from:{upperWidth:0,lowerWidth:0,height:d,x:c,y:u},to:{upperWidth:l,lowerWidth:s,height:d,x:c,y:u},duration:m,animationEasing:v,isActive:g},function(t){var e=t.upperWidth,o=t.lowerWidth,a=t.height,c=t.x,u=t.y;return p().createElement(ni,{canBegin:i>0,from:"0px ".concat(-1===i?1:i,"px"),to:"".concat(i,"px 0px"),attributeName:"strokeDasharray",begin:b,duration:m,easing:v},p().createElement("path",sw({},tp(r,!0),{className:x,d:sP(c,u,e,o,a),ref:n})))}):p().createElement("g",null,p().createElement("path",sw({},tp(r,!0),{className:x,d:sP(c,u,l,s,d)})))};function sE(t){return(sE="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sM(){return(sM=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function s_(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sT(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s_(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=sE(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sE(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sE(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s_(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var sC=function(t){var e=t.cx,r=t.cy,n=t.radius,o=t.angle,i=t.sign,a=t.isExternal,c=t.cornerRadius,u=t.cornerIsExternal,l=c*(a?1:-1)+n,s=Math.asin(c/l)/lq,f=u?o:o+i*s;return{center:lX(e,r,l,f),circleTangency:lX(e,r,n,f),lineTangency:lX(e,r,l*Math.cos(s*lq),u?o-i*s:o),theta:s}},sN=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,o=t.outerRadius,i=t.startAngle,a=t.endAngle,c=S(a-i)*Math.min(Math.abs(a-i),359.999),u=i+c,l=lX(e,r,o,i),s=lX(e,r,o,u),f="M ".concat(l.x,",").concat(l.y,"\n    A ").concat(o,",").concat(o,",0,\n    ").concat(+(Math.abs(c)>180),",").concat(+(i>u),",\n    ").concat(s.x,",").concat(s.y,"\n  ");if(n>0){var p=lX(e,r,n,i),h=lX(e,r,n,u);f+="L ".concat(h.x,",").concat(h.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(c)>180),",").concat(+(i<=u),",\n            ").concat(p.x,",").concat(p.y," Z")}else f+="L ".concat(e,",").concat(r," Z");return f},sD=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,o=t.outerRadius,i=t.cornerRadius,a=t.forceCornerRadius,c=t.cornerIsExternal,u=t.startAngle,l=t.endAngle,s=S(l-u),f=sC({cx:e,cy:r,radius:o,angle:u,sign:s,cornerRadius:i,cornerIsExternal:c}),p=f.circleTangency,h=f.lineTangency,d=f.theta,y=sC({cx:e,cy:r,radius:o,angle:l,sign:-s,cornerRadius:i,cornerIsExternal:c}),v=y.circleTangency,m=y.lineTangency,b=y.theta,g=c?Math.abs(u-l):Math.abs(u-l)-d-b;if(g<0)return a?"M ".concat(h.x,",").concat(h.y,"\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*i,",0\n        a").concat(i,",").concat(i,",0,0,1,").concat(-(2*i),",0\n      "):sN({cx:e,cy:r,innerRadius:n,outerRadius:o,startAngle:u,endAngle:l});var x="M ".concat(h.x,",").concat(h.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(o,",").concat(o,",0,").concat(+(g>180),",").concat(+(s<0),",").concat(v.x,",").concat(v.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(m.x,",").concat(m.y,"\n  ");if(n>0){var w=sC({cx:e,cy:r,radius:n,angle:u,sign:s,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),O=w.circleTangency,j=w.lineTangency,P=w.theta,A=sC({cx:e,cy:r,radius:n,angle:l,sign:-s,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),k=A.circleTangency,E=A.lineTangency,M=A.theta,_=c?Math.abs(u-l):Math.abs(u-l)-P-M;if(_<0&&0===i)return"".concat(x,"L").concat(e,",").concat(r,"Z");x+="L".concat(E.x,",").concat(E.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(k.x,",").concat(k.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(_>180),",").concat(+(s>0),",").concat(O.x,",").concat(O.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(j.x,",").concat(j.y,"Z")}else x+="L".concat(e,",").concat(r,"Z");return x},sI={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},sB=function(t){var e,r=sT(sT({},sI),t),n=r.cx,o=r.cy,i=r.innerRadius,a=r.outerRadius,c=r.cornerRadius,u=r.forceCornerRadius,l=r.cornerIsExternal,s=r.startAngle,f=r.endAngle,d=r.className;if(a<i||s===f)return null;var y=(0,h.A)("recharts-sector",d),v=a-i,m=_(c,v,0,!0);return e=m>0&&360>Math.abs(s-f)?sD({cx:n,cy:o,innerRadius:i,outerRadius:a,cornerRadius:Math.min(m,v/2),forceCornerRadius:u,cornerIsExternal:l,startAngle:s,endAngle:f}):sN({cx:n,cy:o,innerRadius:i,outerRadius:a,startAngle:s,endAngle:f}),p().createElement("path",sM({},tp(r,!0),{className:y,d:e,role:"img"}))},sR=["option","shapeType","propTransformer","activeClassName","isActive"];function sL(t){return(sL="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sz(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sU(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sz(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=sL(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sL(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sL(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sz(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function sF(t){var e=t.shapeType,r=t.elementProps;switch(e){case"rectangle":return p().createElement(nd,r);case"trapezoid":return p().createElement(sk,r);case"sector":return p().createElement(sB,r);case"symbols":if("symbols"===e)return p().createElement(eB,r);break;default:return null}}function s$(t){var e,r=t.option,n=t.shapeType,o=t.propTransformer,i=t.activeClassName,a=t.isActive,c=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,sR);if((0,f.isValidElement)(r))e=(0,f.cloneElement)(r,sU(sU({},c),(0,f.isValidElement)(r)?r.props:r));else if(U()(r))e=r(c);else if(sm()(r)&&!sg()(r)){var u=(void 0===o?function(t,e){return sU(sU({},e),t)}:o)(r,c);e=p().createElement(sF,{shapeType:n,elementProps:u})}else e=p().createElement(sF,{shapeType:n,elementProps:c});return a?p().createElement(tC,{className:void 0===i?"recharts-active-shape":i},e):e}function sW(t,e){return null!=e&&"trapezoids"in t.props}function sq(t,e){return null!=e&&"sectors"in t.props}function sX(t,e){return null!=e&&"points"in t.props}function sH(t,e){var r,n,o=t.x===(null==e||null==(r=e.labelViewBox)?void 0:r.x)||t.x===e.x,i=t.y===(null==e||null==(n=e.labelViewBox)?void 0:n.y)||t.y===e.y;return o&&i}function sV(t,e){var r=t.endAngle===e.endAngle,n=t.startAngle===e.startAngle;return r&&n}function sG(t,e){var r=t.x===e.x,n=t.y===e.y,o=t.z===e.z;return r&&n&&o}var sY=["x","y"];function sK(t){return(sK="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sZ(){return(sZ=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sJ(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sQ(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sJ(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=sK(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sK(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sK(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sJ(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function s0(t,e){var r=t.x,n=t.y,o=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,sY),i=parseInt("".concat(r),10),a=parseInt("".concat(n),10),c=parseInt("".concat(e.height||o.height),10),u=parseInt("".concat(e.width||o.width),10);return sQ(sQ(sQ(sQ(sQ({},e),o),i?{x:i}:{}),a?{y:a}:{}),{},{height:c,width:u,name:e.name,radius:e.radius})}function s1(t){return p().createElement(s$,sZ({shapeType:"rectangle",propTransformer:s0,activeClassName:"recharts-active-bar"},t))}var s2=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(r,n){if("number"==typeof t)return t;var o="number"==typeof r;return o?t(r,n):(o||tA(!1),e)}},s5=["value","background"];function s3(t){return(s3="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s4(){return(s4=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function s6(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function s8(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s6(Object(r),!0).forEach(function(e){fr(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s6(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function s7(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fn(n.key),n)}}function s9(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(s9=function(){return!!t})()}function ft(t){return(ft=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function fe(t,e){return(fe=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function fr(t,e,r){return(e=fn(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fn(t){var e=function(t,e){if("object"!=s3(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s3(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s3(e)?e:e+""}var fo=function(t){var e,r;function n(){var t,e,r;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");for(var o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=n,r=[].concat(i),e=ft(e),fr(t=function(t,e){if(e&&("object"===s3(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,s9()?Reflect.construct(e,r||[],ft(this).constructor):e.apply(this,r)),"state",{isAnimationFinished:!1}),fr(t,"id",M("recharts-bar-")),fr(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),e&&e()}),fr(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),e&&e()}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&fe(n,t),e=[{key:"renderRectanglesStatically",value:function(t){var e=this,r=this.props,n=r.shape,o=r.dataKey,i=r.activeIndex,a=r.activeBar,c=tp(this.props,!1);return t&&t.map(function(t,r){var u=r===i,l=s8(s8(s8({},c),t),{},{isActive:u,option:u?a:n,index:r,dataKey:o,onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd});return p().createElement(tC,s4({className:"recharts-bar-rectangle"},Z(e.props,t,r),{key:"rectangle-".concat(null==t?void 0:t.x,"-").concat(null==t?void 0:t.y,"-").concat(null==t?void 0:t.value,"-").concat(r)}),p().createElement(s1,l))})}},{key:"renderRectanglesWithAnimation",value:function(){var t=this,e=this.props,r=e.data,n=e.layout,o=e.isAnimationActive,i=e.animationBegin,a=e.animationDuration,c=e.animationEasing,u=e.animationId,l=this.state.prevData;return p().createElement(ni,{begin:i,duration:a,isActive:o,easing:c,from:{t:0},to:{t:1},key:"bar-".concat(u),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(e){var o=e.t,i=r.map(function(t,e){var r=l&&l[e];if(r){var i=N(r.x,t.x),a=N(r.y,t.y),c=N(r.width,t.width),u=N(r.height,t.height);return s8(s8({},t),{},{x:i(o),y:a(o),width:c(o),height:u(o)})}if("horizontal"===n){var s=N(0,t.height)(o);return s8(s8({},t),{},{y:t.y+t.height-s,height:s})}var f=N(0,t.width)(o);return s8(s8({},t),{},{width:f})});return p().createElement(tC,null,t.renderRectanglesStatically(i))})}},{key:"renderRectangles",value:function(){var t=this.props,e=t.data,r=t.isAnimationActive,n=this.state.prevData;return r&&e&&e.length&&(!n||!cY()(n,e))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(e)}},{key:"renderBackground",value:function(){var t=this,e=this.props,r=e.data,n=e.dataKey,o=e.activeIndex,i=tp(this.props.background,!1);return r.map(function(e,r){e.value;var a=e.background,c=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(e,s5);if(!a)return null;var u=s8(s8(s8(s8(s8({},c),{},{fill:"#eee"},a),i),Z(t.props,e,r)),{},{onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd,dataKey:n,index:r,className:"recharts-bar-background-rectangle"});return p().createElement(s1,s4({key:"background-bar-".concat(r),option:t.props.background,isActive:r===o},u))})}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.data,o=r.xAxis,i=r.yAxis,a=r.layout,c=tc(r.children,uF);if(!c)return null;var u="vertical"===a?n[0].height/2:n[0].width/2,l=function(t,e){var r=Array.isArray(t.value)?t.value[1]:t.value;return{x:t.x,y:t.y,value:r,errorVal:uJ(t,e)}};return p().createElement(tC,{clipPath:t?"url(#clipPath-".concat(e,")"):null},c.map(function(t){return p().cloneElement(t,{key:"error-bar-".concat(e,"-").concat(t.props.dataKey),data:n,xAxis:o,yAxis:i,layout:a,offset:u,dataPointFormatter:l})}))}},{key:"render",value:function(){var t=this.props,e=t.hide,r=t.data,n=t.className,o=t.xAxis,i=t.yAxis,a=t.left,c=t.top,u=t.width,l=t.height,s=t.isAnimationActive,f=t.background,d=t.id;if(e||!r||!r.length)return null;var y=this.state.isAnimationFinished,v=(0,h.A)("recharts-bar",n),m=o&&o.allowDataOverflow,b=i&&i.allowDataOverflow,g=m||b,x=L()(d)?this.id:d;return p().createElement(tC,{className:v},m||b?p().createElement("defs",null,p().createElement("clipPath",{id:"clipPath-".concat(x)},p().createElement("rect",{x:m?a:a-u/2,y:b?c:c-l/2,width:m?u:2*u,height:b?l:2*l}))):null,p().createElement(tC,{className:"recharts-bar-rectangles",clipPath:g?"url(#clipPath-".concat(x,")"):null},f?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(g,x),(!s||y)&&sy.renderCallByParent(this.props,r))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curData:t.data,prevData:e.curData}:t.data!==e.curData?{curData:t.data}:null}}],e&&s7(n.prototype,e),r&&s7(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(f.PureComponent);function fi(t){return(fi="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fa(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fs(n.key),n)}}function fc(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fu(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fc(Object(r),!0).forEach(function(e){fl(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fc(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fl(t,e,r){return(e=fs(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fs(t){var e=function(t,e){if("object"!=fi(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fi(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fi(e)?e:e+""}fr(fo,"displayName","Bar"),fr(fo,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!t0.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),fr(fo,"getComposedData",function(t){var e=t.props,r=t.item,n=t.barPosition,o=t.bandSize,i=t.xAxis,a=t.yAxis,c=t.xAxisTicks,u=t.yAxisTicks,l=t.stackedData,s=t.dataStartIndex,f=t.displayedData,p=t.offset,h=li(n,r);if(!h)return null;var d=e.layout,y=r.type.defaultProps,v=void 0!==y?s8(s8({},y),r.props):r.props,m=v.dataKey,b=v.children,g=v.minPointSize,x="horizontal"===d?a:i,w=l?x.scale.domain():null,O=lh({numericAxis:x}),j=tc(b,sn),P=f.map(function(t,e){l?f=la(l[s+e],w):Array.isArray(f=uJ(t,m))||(f=[O,f]);var n=s2(g,fo.defaultProps.minPointSize)(f[1],e);if("horizontal"===d){var f,p,y,v,b,x,P,A=[a.scale(f[0]),a.scale(f[1])],k=A[0],E=A[1];p=lp({axis:i,ticks:c,bandSize:o,offset:h.offset,entry:t,index:e}),y=null!=(P=null!=E?E:k)?P:void 0,v=h.size;var M=k-E;if(b=Number.isNaN(M)?0:M,x={x:p,y:a.y,width:v,height:a.height},Math.abs(n)>0&&Math.abs(b)<Math.abs(n)){var _=S(b||n)*(Math.abs(n)-Math.abs(b));y-=_,b+=_}}else{var T=[i.scale(f[0]),i.scale(f[1])],C=T[0],N=T[1];if(p=C,y=lp({axis:a,ticks:u,bandSize:o,offset:h.offset,entry:t,index:e}),v=N-C,b=h.size,x={x:i.x,y:y,width:i.width,height:b},Math.abs(n)>0&&Math.abs(v)<Math.abs(n)){var D=S(v||n)*(Math.abs(n)-Math.abs(v));v+=D}}return s8(s8(s8({},t),{},{x:p,y:y,width:v,height:b,value:l?f:f[1],payload:t,background:x},j&&j[e]&&j[e].props),{},{tooltipPayload:[lw(r,t)],tooltipPosition:{x:p+v/2,y:y+b/2}})});return s8({data:P,layout:d},p)});var ff=function(t,e){var r=t.x,n=t.y,o=e.x,i=e.y;return{x:Math.min(r,o),y:Math.min(n,i),width:Math.abs(o-r),height:Math.abs(i-n)}},fp=function(){var t,e;function r(t){if(!(this instanceof r))throw TypeError("Cannot call a class as a function");this.scale=t}return t=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.bandAware,n=e.position;if(void 0!==t){if(n)switch(n){case"start":default:return this.scale(t);case"middle":var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+o;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(t)+i}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+a}return this.scale(t)}}},{key:"isInRange",value:function(t){var e=this.range(),r=e[0],n=e[e.length-1];return r<=n?t>=r&&t<=n:t>=n&&t<=r}}],e=[{key:"create",value:function(t){return new r(t)}}],t&&fa(r.prototype,t),e&&fa(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();fl(fp,"EPS",1e-4);var fh=function(t){var e=Object.keys(t).reduce(function(e,r){return fu(fu({},e),{},fl({},r,fp.create(t[r])))},{});return fu(fu({},e),{},{apply:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,o=r.position;return st()(t,function(t,r){return e[r].apply(t,{bandAware:n,position:o})})},isInRange:function(t){return sr()(t,function(t,r){return e[r].isInRange(t)})}})},fd=function(t){var e=t.width,r=t.height,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o=(n%180+180)%180*Math.PI/180,i=Math.atan(r/e);return Math.abs(o>i&&o<Math.PI-i?r/Math.sin(o):e/Math.cos(o))};function fy(){return(fy=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function fv(t){return(fv="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fm(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fb(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fm(Object(r),!0).forEach(function(e){fO(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fm(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fg(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(fg=function(){return!!t})()}function fx(t){return(fx=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function fw(t,e){return(fw=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function fO(t,e,r){return(e=fj(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fj(t){var e=function(t,e){if("object"!=fv(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fv(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fv(e)?e:e+""}var fS=function(t){var e=t.x,r=t.y,n=t.xAxis,o=t.yAxis,i=fh({x:n.scale,y:o.scale}),a=i.apply({x:e,y:r},{bandAware:!0});return l7(t,"discard")&&!i.isInRange(a)?null:a},fP=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=fx(t),function(t,e){if(e&&("object"===fv(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,fg()?Reflect.construct(t,e||[],fx(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&fw(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x,n=t.y,o=t.r,i=t.alwaysShow,a=t.clipPathId,c=k(e),u=k(n);if(B(void 0===i,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!c||!u)return null;var l=fS(this.props);if(!l)return null;var s=l.x,f=l.y,d=this.props,y=d.shape,v=d.className,m=fb(fb({clipPath:l7(this.props,"hidden")?"url(#".concat(a,")"):void 0},tp(this.props,!0)),{},{cx:s,cy:f});return p().createElement(tC,{className:(0,h.A)("recharts-reference-dot",v)},r.renderDot(y,m),l6.renderCallByParent(this.props,{x:s-o,y:f-o,width:2*o,height:2*o}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fj(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(p().Component);fO(fP,"displayName","ReferenceDot"),fO(fP,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),fO(fP,"renderDot",function(t,e){var r;return p().isValidElement(t)?p().cloneElement(t,e):U()(t)?t(e):p().createElement(e4,fy({},e,{cx:e.cx,cy:e.cy,className:"recharts-reference-dot-dot"}))});var fA=r(67367),fk=r.n(fA),fE=r(22964),fM=r.n(fE),f_=r(86451),fT=r.n(f_)()(function(t){return{x:t.left,y:t.top,width:t.width,height:t.height}},function(t){return["l",t.left,"t",t.top,"w",t.width,"h",t.height].join("")}),fC=(0,f.createContext)(void 0),fN=(0,f.createContext)(void 0),fD=(0,f.createContext)(void 0),fI=(0,f.createContext)({}),fB=(0,f.createContext)(void 0),fR=(0,f.createContext)(0),fL=(0,f.createContext)(0),fz=function(t){var e=t.state,r=e.xAxisMap,n=e.yAxisMap,o=e.offset,i=t.clipPathId,a=t.children,c=t.width,u=t.height,l=fT(o);return p().createElement(fC.Provider,{value:r},p().createElement(fN.Provider,{value:n},p().createElement(fI.Provider,{value:o},p().createElement(fD.Provider,{value:l},p().createElement(fB.Provider,{value:i},p().createElement(fR.Provider,{value:u},p().createElement(fL.Provider,{value:c},a)))))))},fU=function(t){var e=(0,f.useContext)(fC);null==e&&tA(!1);var r=e[t];return null==r&&tA(!1),r},fF=function(){var t=(0,f.useContext)(fN);return fM()(t,function(t){return sr()(t.domain,Number.isFinite)})||T(t)},f$=function(t){var e=(0,f.useContext)(fN);null==e&&tA(!1);var r=e[t];return null==r&&tA(!1),r},fW=function(){return(0,f.useContext)(fL)},fq=function(){return(0,f.useContext)(fR)};function fX(t){return(fX="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fH(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(fH=function(){return!!t})()}function fV(t){return(fV=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function fG(t,e){return(fG=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function fY(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fK(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fY(Object(r),!0).forEach(function(e){fZ(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fY(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fZ(t,e,r){return(e=fJ(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fJ(t){var e=function(t,e){if("object"!=fX(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fX(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fX(e)?e:e+""}function fQ(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function f0(){return(f0=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var f1=function(t,e){var r;return p().isValidElement(t)?p().cloneElement(t,e):U()(t)?t(e):p().createElement("line",f0({},e,{className:"recharts-reference-line-line"}))},f2=function(t,e,r,n,o,i,a,c,u){var l=o.x,s=o.y,f=o.width,p=o.height;if(r){var h=u.y,d=t.y.apply(h,{position:i});if(l7(u,"discard")&&!t.y.isInRange(d))return null;var y=[{x:l+f,y:d},{x:l,y:d}];return"left"===c?y.reverse():y}if(e){var v=u.x,m=t.x.apply(v,{position:i});if(l7(u,"discard")&&!t.x.isInRange(m))return null;var b=[{x:m,y:s+p},{x:m,y:s}];return"top"===a?b.reverse():b}if(n){var g=u.segment.map(function(e){return t.apply(e,{position:i})});return l7(u,"discard")&&fk()(g,function(e){return!t.isInRange(e)})?null:g}return null};function f5(t){var e,r=t.x,n=t.y,o=t.segment,i=t.xAxisId,a=t.yAxisId,c=t.shape,u=t.className,l=t.alwaysShow,s=(0,f.useContext)(fB),d=fU(i),y=f$(a),v=(0,f.useContext)(fD);if(!s||!v)return null;B(void 0===l,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var m=f2(fh({x:d.scale,y:y.scale}),k(r),k(n),o&&2===o.length,v,t.position,d.orientation,y.orientation,t);if(!m)return null;var b=function(t){if(Array.isArray(t))return t}(m)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(m,2)||function(t,e){if(t){if("string"==typeof t)return fQ(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return fQ(t,e)}}(m,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),g=b[0],x=g.x,w=g.y,O=b[1],j=O.x,S=O.y,P=fK(fK({clipPath:l7(t,"hidden")?"url(#".concat(s,")"):void 0},tp(t,!0)),{},{x1:x,y1:w,x2:j,y2:S});return p().createElement(tC,{className:(0,h.A)("recharts-reference-line",u)},f1(c,P),l6.renderCallByParent(t,ff({x:(e={x1:x,y1:w,x2:j,y2:S}).x1,y:e.y1},{x:e.x2,y:e.y2})))}var f3=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=fV(t),function(t,e){if(e&&("object"===fX(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,fH()?Reflect.construct(t,e||[],fV(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&fG(r,t),e=[{key:"render",value:function(){return p().createElement(f5,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fJ(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(p().Component);function f4(){return(f4=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function f6(t){return(f6="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function f8(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function f7(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f8(Object(r),!0).forEach(function(e){pr(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f8(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}fZ(f3,"displayName","ReferenceLine"),fZ(f3,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function f9(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(f9=function(){return!!t})()}function pt(t){return(pt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function pe(t,e){return(pe=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function pr(t,e,r){return(e=pn(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function pn(t){var e=function(t,e){if("object"!=f6(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f6(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f6(e)?e:e+""}var po=function(t,e,r,n,o){var i=o.x1,a=o.x2,c=o.y1,u=o.y2,l=o.xAxis,s=o.yAxis;if(!l||!s)return null;var f=fh({x:l.scale,y:s.scale}),p={x:t?f.x.apply(i,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(c,{position:"start"}):f.y.rangeMin},h={x:e?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(u,{position:"end"}):f.y.rangeMax};return!l7(o,"discard")||f.isInRange(p)&&f.isInRange(h)?ff(p,h):null},pi=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=pt(t),function(t,e){if(e&&("object"===f6(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,f9()?Reflect.construct(t,e||[],pt(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&pe(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x1,n=t.x2,o=t.y1,i=t.y2,a=t.className,c=t.alwaysShow,u=t.clipPathId;B(void 0===c,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var l=k(e),s=k(n),f=k(o),d=k(i),y=this.props.shape;if(!l&&!s&&!f&&!d&&!y)return null;var v=po(l,s,f,d,this.props);if(!v&&!y)return null;var m=l7(this.props,"hidden")?"url(#".concat(u,")"):void 0;return p().createElement(tC,{className:(0,h.A)("recharts-reference-area",a)},r.renderRect(y,f7(f7({clipPath:m},tp(this.props,!0)),v)),l6.renderCallByParent(this.props,v))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,pn(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(p().Component);function pa(t){return function(t){if(Array.isArray(t))return pc(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return pc(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return pc(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function pc(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}pr(pi,"displayName","ReferenceArea"),pr(pi,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),pr(pi,"renderRect",function(t,e){var r;return p().isValidElement(t)?p().cloneElement(t,e):U()(t)?t(e):p().createElement(nd,f4({},e,{className:"recharts-reference-area-rect"}))});var pu=function(t,e,r,n,o){var i=tc(t,f3),a=tc(t,fP),c=[].concat(pa(i),pa(a)),u=tc(t,pi),l="".concat(n,"Id"),s=n[0],f=e;if(c.length&&(f=c.reduce(function(t,e){if(e.props[l]===r&&l7(e.props,"extendDomain")&&A(e.props[s])){var n=e.props[s];return[Math.min(t[0],n),Math.max(t[1],n)]}return t},f)),u.length){var p="".concat(s,"1"),h="".concat(s,"2");f=u.reduce(function(t,e){if(e.props[l]===r&&l7(e.props,"extendDomain")&&A(e.props[p])&&A(e.props[h])){var n=e.props[p],o=e.props[h];return[Math.min(t[0],n,o),Math.max(t[1],n,o)]}return t},f)}return o&&o.length&&(f=o.reduce(function(t,e){return A(e)?[Math.min(t[0],e),Math.max(t[1],e)]:t},f)),f},pl=r(11117),ps=new(r.n(pl)()),pf="recharts.syncMouseEvents";function pp(t){return(pp="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ph(t,e,r){return(e=pd(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function pd(t){var e=function(t,e){if("object"!=pp(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pp(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pp(e)?e:e+""}var py=function(){var t,e;return t=function t(){if(!(this instanceof t))throw TypeError("Cannot call a class as a function");ph(this,"activeIndex",0),ph(this,"coordinateList",[]),ph(this,"layout","horizontal")},e=[{key:"setDetails",value:function(t){var e,r=t.coordinateList,n=void 0===r?null:r,o=t.container,i=void 0===o?null:o,a=t.layout,c=void 0===a?null:a,u=t.offset,l=void 0===u?null:u,s=t.mouseHandlerCallback,f=void 0===s?null:s;this.coordinateList=null!=(e=null!=n?n:this.coordinateList)?e:[],this.container=null!=i?i:this.container,this.layout=null!=c?c:this.layout,this.offset=null!=l?l:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(t){if(0!==this.coordinateList.length)switch(t.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(t){this.activeIndex=t}},{key:"spoofMouse",value:function(){if("horizontal"===this.layout&&0!==this.coordinateList.length){var t,e,r=this.container.getBoundingClientRect(),n=r.x,o=r.y,i=r.height,a=this.coordinateList[this.activeIndex].coordinate,c=(null==(t=window)?void 0:t.scrollX)||0,u=(null==(e=window)?void 0:e.scrollY)||0,l=o+this.offset.top+i/2+u;this.mouseHandlerCallback({pageX:n+a+c,pageY:l})}}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,pd(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}();function pv(){}function pm(t,e,r){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+e)/6,(t._y0+4*t._y1+r)/6)}function pb(t){this._context=t}function pg(t){this._context=t}function px(t){this._context=t}pb.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:pm(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:pm(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},pg.prototype={areaStart:pv,areaEnd:pv,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._x2=t,this._y2=e;break;case 1:this._point=2,this._x3=t,this._y3=e;break;case 2:this._point=3,this._x4=t,this._y4=e,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+e)/6);break;default:pm(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},px.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+t)/6,n=(this._y0+4*this._y1+e)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:pm(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}};class pw{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e)}this._x0=t,this._y0=e}}function pO(t){this._context=t}function pj(t){this._context=t}function pS(t){return new pj(t)}pO.prototype={areaStart:pv,areaEnd:pv,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t*=1,e*=1,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}};function pP(t,e,r){var n=t._x1-t._x0,o=e-t._x1,i=(t._y1-t._y0)/(n||o<0&&-0),a=(r-t._y1)/(o||n<0&&-0);return((i<0?-1:1)+(a<0?-1:1))*Math.min(Math.abs(i),Math.abs(a),.5*Math.abs((i*o+a*n)/(n+o)))||0}function pA(t,e){var r=t._x1-t._x0;return r?(3*(t._y1-t._y0)/r-e)/2:e}function pk(t,e,r){var n=t._x0,o=t._y0,i=t._x1,a=t._y1,c=(i-n)/3;t._context.bezierCurveTo(n+c,o+c*e,i-c,a-c*r,i,a)}function pE(t){this._context=t}function pM(t){this._context=new p_(t)}function p_(t){this._context=t}function pT(t){this._context=t}function pC(t){var e,r,n=t.length-1,o=Array(n),i=Array(n),a=Array(n);for(o[0]=0,i[0]=2,a[0]=t[0]+2*t[1],e=1;e<n-1;++e)o[e]=1,i[e]=4,a[e]=4*t[e]+2*t[e+1];for(o[n-1]=2,i[n-1]=7,a[n-1]=8*t[n-1]+t[n],e=1;e<n;++e)r=o[e]/i[e-1],i[e]-=r,a[e]-=r*a[e-1];for(o[n-1]=a[n-1]/i[n-1],e=n-2;e>=0;--e)o[e]=(a[e]-o[e+1])/i[e];for(e=0,i[n-1]=(t[n]+o[n-1])/2;e<n-1;++e)i[e]=2*t[e+1]-o[e+1];return[o,i]}function pN(t,e){this._context=t,this._t=e}function pD(t){return t[0]}function pI(t){return t[1]}function pB(t,e){var r=ew(!0),n=null,o=pS,i=null,a=ek(c);function c(c){var u,l,s,f=(c=cM(c)).length,p=!1;for(null==n&&(i=o(s=a())),u=0;u<=f;++u)!(u<f&&r(l=c[u],u,c))===p&&((p=!p)?i.lineStart():i.lineEnd()),p&&i.point(+t(l,u,c),+e(l,u,c));if(s)return i=null,s+""||null}return t="function"==typeof t?t:void 0===t?pD:ew(t),e="function"==typeof e?e:void 0===e?pI:ew(e),c.x=function(e){return arguments.length?(t="function"==typeof e?e:ew(+e),c):t},c.y=function(t){return arguments.length?(e="function"==typeof t?t:ew(+t),c):e},c.defined=function(t){return arguments.length?(r="function"==typeof t?t:ew(!!t),c):r},c.curve=function(t){return arguments.length?(o=t,null!=n&&(i=o(n)),c):o},c.context=function(t){return arguments.length?(null==t?n=i=null:i=o(n=t),c):n},c}function pR(t,e,r){var n=null,o=ew(!0),i=null,a=pS,c=null,u=ek(l);function l(l){var s,f,p,h,d,y=(l=cM(l)).length,v=!1,m=Array(y),b=Array(y);for(null==i&&(c=a(d=u())),s=0;s<=y;++s){if(!(s<y&&o(h=l[s],s,l))===v)if(v=!v)f=s,c.areaStart(),c.lineStart();else{for(c.lineEnd(),c.lineStart(),p=s-1;p>=f;--p)c.point(m[p],b[p]);c.lineEnd(),c.areaEnd()}v&&(m[s]=+t(h,s,l),b[s]=+e(h,s,l),c.point(n?+n(h,s,l):m[s],r?+r(h,s,l):b[s]))}if(d)return c=null,d+""||null}function s(){return pB().defined(o).curve(a).context(i)}return t="function"==typeof t?t:void 0===t?pD:ew(+t),e="function"==typeof e?e:void 0===e?ew(0):ew(+e),r="function"==typeof r?r:void 0===r?pI:ew(+r),l.x=function(e){return arguments.length?(t="function"==typeof e?e:ew(+e),n=null,l):t},l.x0=function(e){return arguments.length?(t="function"==typeof e?e:ew(+e),l):t},l.x1=function(t){return arguments.length?(n=null==t?null:"function"==typeof t?t:ew(+t),l):n},l.y=function(t){return arguments.length?(e="function"==typeof t?t:ew(+t),r=null,l):e},l.y0=function(t){return arguments.length?(e="function"==typeof t?t:ew(+t),l):e},l.y1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:ew(+t),l):r},l.lineX0=l.lineY0=function(){return s().x(t).y(e)},l.lineY1=function(){return s().x(t).y(r)},l.lineX1=function(){return s().x(n).y(e)},l.defined=function(t){return arguments.length?(o="function"==typeof t?t:ew(!!t),l):o},l.curve=function(t){return arguments.length?(a=t,null!=i&&(c=a(i)),l):a},l.context=function(t){return arguments.length?(null==t?i=c=null:c=a(i=t),l):i},l}function pL(t){return(pL="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function pz(){return(pz=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function pU(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function pF(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?pU(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=pL(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pL(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pL(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):pU(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}pj.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e)}}},pE.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:pk(this,this._t0,pA(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){var r=NaN;if(e*=1,(t*=1)!==this._x1||e!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,pk(this,pA(this,r=pP(this,t,e)),r);break;default:pk(this,this._t0,r=pP(this,t,e))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e,this._t0=r}}},(pM.prototype=Object.create(pE.prototype)).point=function(t,e){pE.prototype.point.call(this,e,t)},p_.prototype={moveTo:function(t,e){this._context.moveTo(e,t)},closePath:function(){this._context.closePath()},lineTo:function(t,e){this._context.lineTo(e,t)},bezierCurveTo:function(t,e,r,n,o,i){this._context.bezierCurveTo(e,t,n,r,i,o)}},pT.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,e=this._y,r=t.length;if(r)if(this._line?this._context.lineTo(t[0],e[0]):this._context.moveTo(t[0],e[0]),2===r)this._context.lineTo(t[1],e[1]);else for(var n=pC(t),o=pC(e),i=0,a=1;a<r;++i,++a)this._context.bezierCurveTo(n[0][i],o[0][i],n[1][i],o[1][i],t[a],e[a]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,e){this._x.push(+t),this._y.push(+e)}},pN.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,e),this._context.lineTo(t,e);else{var r=this._x*(1-this._t)+t*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,e)}}this._x=t,this._y=e}};var p$={curveBasisClosed:function(t){return new pg(t)},curveBasisOpen:function(t){return new px(t)},curveBasis:function(t){return new pb(t)},curveBumpX:function(t){return new pw(t,!0)},curveBumpY:function(t){return new pw(t,!1)},curveLinearClosed:function(t){return new pO(t)},curveLinear:pS,curveMonotoneX:function(t){return new pE(t)},curveMonotoneY:function(t){return new pM(t)},curveNatural:function(t){return new pT(t)},curveStep:function(t){return new pN(t,.5)},curveStepAfter:function(t){return new pN(t,1)},curveStepBefore:function(t){return new pN(t,0)}},pW=function(t){return t.x===+t.x&&t.y===+t.y},pq=function(t){return t.x},pX=function(t){return t.y},pH=function(t,e){if(U()(t))return t;var r="curve".concat(ei()(t));return("curveMonotone"===r||"curveBump"===r)&&e?p$["".concat(r).concat("vertical"===e?"Y":"X")]:p$[r]||pS},pV=function(t){var e,r=t.type,n=t.points,o=void 0===n?[]:n,i=t.baseLine,a=t.layout,c=t.connectNulls,u=void 0!==c&&c,l=pH(void 0===r?"linear":r,a),s=u?o.filter(function(t){return pW(t)}):o;if(Array.isArray(i)){var f=u?i.filter(function(t){return pW(t)}):i,p=s.map(function(t,e){return pF(pF({},t),{},{base:f[e]})});return(e="vertical"===a?pR().y(pX).x1(pq).x0(function(t){return t.base.x}):pR().x(pq).y1(pX).y0(function(t){return t.base.y})).defined(pW).curve(l),e(p)}return(e="vertical"===a&&A(i)?pR().y(pX).x1(pq).x0(i):A(i)?pR().x(pq).y1(pX).y0(i):pB().x(pq).y(pX)).defined(pW).curve(l),e(s)},pG=function(t){var e=t.className,r=t.points,n=t.path,o=t.pathRef;if((!r||!r.length)&&!n)return null;var i=r&&r.length?pV(t):n;return p().createElement("path",pz({},tp(t,!1),K(t),{className:(0,h.A)("recharts-curve",e),d:i,ref:o}))};function pY(t){return(pY="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var pK=["x","y","top","left","width","height","className"];function pZ(){return(pZ=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function pJ(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}var pQ=function(t){var e=t.x,r=void 0===e?0:e,n=t.y,o=void 0===n?0:n,i=t.top,a=void 0===i?0:i,c=t.left,u=void 0===c?0:c,l=t.width,s=void 0===l?0:l,f=t.height,d=void 0===f?0:f,y=t.className,v=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?pJ(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=pY(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pY(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pY(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):pJ(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({x:r,y:o,top:a,left:u,width:s,height:d},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,pK));return A(r)&&A(o)&&A(s)&&A(d)&&A(a)&&A(u)?p().createElement("path",pZ({},tp(v,!0),{className:(0,h.A)("recharts-cross",y),d:"M".concat(r,",").concat(a,"v").concat(d,"M").concat(u,",").concat(o,"h").concat(s)})):null};function p0(t){var e=t.cx,r=t.cy,n=t.radius,o=t.startAngle,i=t.endAngle;return{points:[lX(e,r,n,o),lX(e,r,n,i)],cx:e,cy:r,radius:n,startAngle:o,endAngle:i}}function p1(t){return(p1="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p2(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function p5(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?p2(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=p1(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=p1(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==p1(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):p2(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function p3(t){var e,r,n,o,i=t.element,a=t.tooltipEventType,c=t.isActive,u=t.activeCoordinate,l=t.activePayload,s=t.offset,p=t.activeTooltipIndex,d=t.tooltipAxisBandSize,y=t.layout,v=t.chartName,m=null!=(r=i.props.cursor)?r:null==(n=i.type.defaultProps)?void 0:n.cursor;if(!i||!m||!c||!u||"ScatterChart"!==v&&"axis"!==a)return null;var b=pG;if("ScatterChart"===v)o=u,b=pQ;else if("BarChart"===v)e=d/2,o={stroke:"none",fill:"#ccc",x:"horizontal"===y?u.x-e:s.left+.5,y:"horizontal"===y?s.top+.5:u.y-e,width:"horizontal"===y?d:s.width-1,height:"horizontal"===y?s.height-1:d},b=nd;else if("radial"===y){var g=p0(u),x=g.cx,w=g.cy,O=g.radius;o={cx:x,cy:w,startAngle:g.startAngle,endAngle:g.endAngle,innerRadius:O,outerRadius:O},b=sB}else o={points:function(t,e,r){var n,o,i,a;if("horizontal"===t)i=n=e.x,o=r.top,a=r.top+r.height;else if("vertical"===t)a=o=e.y,n=r.left,i=r.left+r.width;else if(null!=e.cx&&null!=e.cy)if("centric"!==t)return p0(e);else{var c=e.cx,u=e.cy,l=e.innerRadius,s=e.outerRadius,f=e.angle,p=lX(c,u,l,f),h=lX(c,u,s,f);n=p.x,o=p.y,i=h.x,a=h.y}return[{x:n,y:o},{x:i,y:a}]}(y,u,s)},b=pG;var j=p5(p5(p5(p5({stroke:"#ccc",pointerEvents:"none"},s),o),tp(m,!1)),{},{payload:l,payloadIndex:p,className:(0,h.A)("recharts-tooltip-cursor",m.className)});return(0,f.isValidElement)(m)?(0,f.cloneElement)(m,j):(0,f.createElement)(b,j)}var p4=["item"],p6=["children","className","width","height","style","compact","title","desc"];function p8(t){return(p8="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p7(){return(p7=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function p9(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||hi(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ht(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function he(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(he=function(){return!!t})()}function hr(t){return(hr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function hn(t,e){return(hn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function ho(t){return function(t){if(Array.isArray(t))return ha(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||hi(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function hi(t,e){if(t){if("string"==typeof t)return ha(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ha(t,e)}}function ha(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function hc(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function hu(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hc(Object(r),!0).forEach(function(e){hl(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hc(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function hl(t,e,r){return(e=hs(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function hs(t){var e=function(t,e){if("object"!=p8(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=p8(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==p8(e)?e:e+""}var hf={xAxis:["bottom","top"],yAxis:["left","right"]},hp={width:"100%",height:"100%"},hh={x:0,y:0};function hd(t){return t}var hy=function(t,e,r,n){var o=e.find(function(t){return t&&t.index===r});if(o){if("horizontal"===t)return{x:o.coordinate,y:n.y};if("vertical"===t)return{x:n.x,y:o.coordinate};if("centric"===t){var i=o.coordinate,a=n.radius;return hu(hu(hu({},n),lX(n.cx,n.cy,a,i)),{},{angle:i,radius:a})}var c=o.coordinate,u=n.angle;return hu(hu(hu({},n),lX(n.cx,n.cy,c,u)),{},{angle:u,radius:c})}return hh},hv=function(t,e){var r=e.graphicalItems,n=e.dataStartIndex,o=e.dataEndIndex,i=(null!=r?r:[]).reduce(function(t,e){var r=e.props.data;return r&&r.length?[].concat(ho(t),ho(r)):t},[]);return i.length>0?i:t&&t.length&&A(n)&&A(o)?t.slice(n,o+1):[]};function hm(t){return"number"===t?[0,"auto"]:void 0}var hb=function(t,e,r,n){var o=t.graphicalItems,i=t.tooltipAxis,a=hv(e,t);return r<0||!o||!o.length||r>=a.length?null:o.reduce(function(o,c){var u,l,s=null!=(u=c.props.data)?u:e;return(s&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=r&&(s=s.slice(t.dataStartIndex,t.dataEndIndex+1)),l=i.dataKey&&!i.allowDuplicatedCategory?D(void 0===s?a:s,i.dataKey,n):s&&s[r]||a[r])?[].concat(ho(o),[lw(c,l)]):o},[])},hg=function(t,e,r,n){var o=n||{x:t.chartX,y:t.chartY},i="horizontal"===r?o.x:"vertical"===r?o.y:"centric"===r?o.angle:o.radius,a=t.orderedTooltipTicks,c=t.tooltipAxis,u=t.tooltipTicks,l=u0(i,a,u,c);if(l>=0&&u){var s=u[l]&&u[l].value,f=hb(t,e,l,s),p=hy(r,a,l,o);return{activeTooltipIndex:l,activeLabel:s,activePayload:f,activeCoordinate:p}}return null},hx=function(t,e){var r=e.axes,n=e.graphicalItems,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.layout,s=t.children,f=t.stackOffset,p=u7(l,o);return r.reduce(function(e,r){var h=void 0!==r.type.defaultProps?hu(hu({},r.type.defaultProps),r.props):r.props,d=h.type,y=h.dataKey,v=h.allowDataOverflow,m=h.allowDuplicatedCategory,b=h.scale,g=h.ticks,x=h.includeHidden,w=h[i];if(e[w])return e;var O=hv(t.data,{graphicalItems:n.filter(function(t){var e;return(i in t.props?t.props[i]:null==(e=t.type.defaultProps)?void 0:e[i])===w}),dataStartIndex:c,dataEndIndex:u}),j=O.length;(function(t,e,r){if("number"===r&&!0===e&&Array.isArray(t)){var n=null==t?void 0:t[0],o=null==t?void 0:t[1];if(n&&o&&A(n)&&A(o))return!0}return!1})(h.domain,v,d)&&(k=lb(h.domain,null,v),p&&("number"===d||"auto"!==b)&&(M=uQ(O,y,"category")));var S=hm(d);if(!k||0===k.length){var P,k,E,M,_,T=null!=(_=h.domain)?_:S;if(y){if(k=uQ(O,y,d),"category"===d&&p){var N=C(k);m&&N?(E=k,k=tj()(0,j)):m||(k=lx(T,k,r).reduce(function(t,e){return t.indexOf(e)>=0?t:[].concat(ho(t),[e])},[]))}else if("category"===d)k=m?k.filter(function(t){return""!==t&&!L()(t)}):lx(T,k,r).reduce(function(t,e){return t.indexOf(e)>=0||""===e||L()(e)?t:[].concat(ho(t),[e])},[]);else if("number"===d){var D=u6(O,n.filter(function(t){var e,r,n=i in t.props?t.props[i]:null==(e=t.type.defaultProps)?void 0:e[i],o="hide"in t.props?t.props.hide:null==(r=t.type.defaultProps)?void 0:r.hide;return n===w&&(x||!o)}),y,o,l);D&&(k=D)}p&&("number"===d||"auto"!==b)&&(M=uQ(O,y,"category"))}else k=p?tj()(0,j):a&&a[w]&&a[w].hasStack&&"number"===d?"expand"===f?[0,1]:ly(a[w].stackGroups,c,u):u8(O,n.filter(function(t){var e=i in t.props?t.props[i]:t.type.defaultProps[i],r="hide"in t.props?t.props.hide:t.type.defaultProps.hide;return e===w&&(x||!r)}),d,l,!0);"number"===d?(k=pu(s,k,w,o,g),T&&(k=lb(T,k,v))):"category"===d&&T&&k.every(function(t){return T.indexOf(t)>=0})&&(k=T)}return hu(hu({},e),{},hl({},w,hu(hu({},h),{},{axisType:o,domain:k,categoricalDomain:M,duplicateDomain:E,originalDomain:null!=(P=h.domain)?P:S,isCategorical:p,layout:l})))},{})},hw=function(t,e){var r=e.graphicalItems,n=e.Axis,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.layout,s=t.children,f=hv(t.data,{graphicalItems:r,dataStartIndex:c,dataEndIndex:u}),p=f.length,h=u7(l,o),d=-1;return r.reduce(function(t,e){var y,v=(void 0!==e.type.defaultProps?hu(hu({},e.type.defaultProps),e.props):e.props)[i],m=hm("number");return t[v]?t:(d++,y=h?tj()(0,p):a&&a[v]&&a[v].hasStack?pu(s,y=ly(a[v].stackGroups,c,u),v,o):pu(s,y=lb(m,u8(f,r.filter(function(t){var e,r,n=i in t.props?t.props[i]:null==(e=t.type.defaultProps)?void 0:e[i],o="hide"in t.props?t.props.hide:null==(r=t.type.defaultProps)?void 0:r.hide;return n===v&&!o}),"number",l),n.defaultProps.allowDataOverflow),v,o),hu(hu({},t),{},hl({},v,hu(hu({axisType:o},n.defaultProps),{},{hide:!0,orientation:w()(hf,"".concat(o,".").concat(d%2),null),domain:y,originalDomain:m,isCategorical:h,layout:l}))))},{})},hO=function(t,e){var r=e.axisType,n=void 0===r?"xAxis":r,o=e.AxisComp,i=e.graphicalItems,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.children,s="".concat(n,"Id"),f=tc(l,o),p={};return f&&f.length?p=hx(t,{axes:f,graphicalItems:i,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:c,dataEndIndex:u}):i&&i.length&&(p=hw(t,{Axis:o,graphicalItems:i,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:c,dataEndIndex:u})),p},hj=function(t){var e=T(t),r=lt(e,!1,!0);return{tooltipTicks:r,orderedTooltipTicks:tP()(r,function(t){return t.coordinate}),tooltipAxis:e,tooltipAxisBandSize:lg(e,r)}},hS=function(t){var e=t.children,r=t.defaultShowTooltip,n=tu(e,lU),o=0,i=0;return t.data&&0!==t.data.length&&(i=t.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(o=n.props.startIndex),n.props.endIndex>=0&&(i=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:o,dataEndIndex:i,activeTooltipIndex:-1,isTooltipActive:!!r}},hP=function(t){return"horizontal"===t?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===t?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===t?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},hA=function(t,e){var r=t.props,n=t.graphicalItems,o=t.xAxisMap,i=void 0===o?{}:o,a=t.yAxisMap,c=void 0===a?{}:a,u=r.width,l=r.height,s=r.children,f=r.margin||{},p=tu(s,lU),h=tu(s,e5),d=Object.keys(c).reduce(function(t,e){var r=c[e],n=r.orientation;return r.mirror||r.hide?t:hu(hu({},t),{},hl({},n,t[n]+r.width))},{left:f.left||0,right:f.right||0}),y=Object.keys(i).reduce(function(t,e){var r=i[e],n=r.orientation;return r.mirror||r.hide?t:hu(hu({},t),{},hl({},n,w()(t,"".concat(n))+r.height))},{top:f.top||0,bottom:f.bottom||0}),v=hu(hu({},y),d),m=v.bottom;p&&(v.bottom+=p.props.height||lU.defaultProps.height),h&&e&&(v=u3(v,n,r,e));var b=u-v.left-v.right,g=l-v.top-v.bottom;return hu(hu({brushBottom:m},v),{},{width:Math.max(b,0),height:Math.max(g,0)})},hk=["type","layout","connectNulls","ref"],hE=["key"];function hM(t){return(hM="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function h_(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function hT(){return(hT=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function hC(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function hN(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hC(Object(r),!0).forEach(function(e){hU(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hC(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function hD(t){return function(t){if(Array.isArray(t))return hI(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return hI(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return hI(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function hI(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function hB(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,hF(n.key),n)}}function hR(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(hR=function(){return!!t})()}function hL(t){return(hL=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function hz(t,e){return(hz=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function hU(t,e,r){return(e=hF(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function hF(t){var e=function(t,e){if("object"!=hM(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hM(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hM(e)?e:e+""}var h$=function(t){var e,r;function n(){var t,e,r;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");for(var o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=n,r=[].concat(i),e=hL(e),hU(t=function(t,e){if(e&&("object"===hM(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,hR()?Reflect.construct(e,r||[],hL(this).constructor):e.apply(this,r)),"state",{isAnimationFinished:!0,totalLength:0}),hU(t,"generateSimpleStrokeDasharray",function(t,e){return"".concat(e,"px ").concat(t-e,"px")}),hU(t,"getStrokeDasharray",function(e,r,o){var i=o.reduce(function(t,e){return t+e});if(!i)return t.generateSimpleStrokeDasharray(r,e);for(var a=Math.floor(e/i),c=e%i,u=r-e,l=[],s=0,f=0;s<o.length;f+=o[s],++s)if(f+o[s]>c){l=[].concat(hD(o.slice(0,s)),[c-f]);break}var p=l.length%2==0?[0,u]:[u];return[].concat(hD(n.repeat(o,a)),hD(l),p).map(function(t){return"".concat(t,"px")}).join(", ")}),hU(t,"id",M("recharts-line-")),hU(t,"pathRef",function(e){t.mainCurve=e}),hU(t,"handleAnimationEnd",function(){t.setState({isAnimationFinished:!0}),t.props.onAnimationEnd&&t.props.onAnimationEnd()}),hU(t,"handleAnimationStart",function(){t.setState({isAnimationFinished:!1}),t.props.onAnimationStart&&t.props.onAnimationStart()}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&hz(n,t),e=[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var t=this.getTotalLength();this.setState({totalLength:t})}}},{key:"componentDidUpdate",value:function(){if(this.props.isAnimationActive){var t=this.getTotalLength();t!==this.state.totalLength&&this.setState({totalLength:t})}}},{key:"getTotalLength",value:function(){var t=this.mainCurve;try{return t&&t.getTotalLength&&t.getTotalLength()||0}catch(t){return 0}}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.points,o=r.xAxis,i=r.yAxis,a=r.layout,c=tc(r.children,uF);if(!c)return null;var u=function(t,e){return{x:t.x,y:t.y,value:t.value,errorVal:uJ(t.payload,e)}};return p().createElement(tC,{clipPath:t?"url(#clipPath-".concat(e,")"):null},c.map(function(t){return p().cloneElement(t,{key:"bar-".concat(t.props.dataKey),data:n,xAxis:o,yAxis:i,layout:a,dataPointFormatter:u})}))}},{key:"renderDots",value:function(t,e,r){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var o=this.props,i=o.dot,a=o.points,c=o.dataKey,u=tp(this.props,!1),l=tp(i,!0),s=a.map(function(t,e){var r=hN(hN(hN({key:"dot-".concat(e),r:3},u),l),{},{index:e,cx:t.x,cy:t.y,value:t.value,dataKey:c,payload:t.payload,points:a});return n.renderDotItem(i,r)}),f={clipPath:t?"url(#clipPath-".concat(e?"":"dots-").concat(r,")"):null};return p().createElement(tC,hT({className:"recharts-line-dots",key:"dots"},f),s)}},{key:"renderCurveStatically",value:function(t,e,r,n){var o=this.props,i=o.type,a=o.layout,c=o.connectNulls,u=hN(hN(hN({},tp((o.ref,h_(o,hk)),!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:e?"url(#clipPath-".concat(r,")"):null,points:t},n),{},{type:i,layout:a,connectNulls:c});return p().createElement(pG,hT({},u,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(t,e){var r=this,n=this.props,o=n.points,i=n.strokeDasharray,a=n.isAnimationActive,c=n.animationBegin,u=n.animationDuration,l=n.animationEasing,s=n.animationId,f=n.animateNewValues,h=n.width,d=n.height,y=this.state,v=y.prevPoints,m=y.totalLength;return p().createElement(ni,{begin:c,duration:u,isActive:a,easing:l,from:{t:0},to:{t:1},key:"line-".concat(s),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(n){var a,c=n.t;if(v){var u=v.length/o.length,l=o.map(function(t,e){var r=Math.floor(e*u);if(v[r]){var n=v[r],o=N(n.x,t.x),i=N(n.y,t.y);return hN(hN({},t),{},{x:o(c),y:i(c)})}if(f){var a=N(2*h,t.x),l=N(d/2,t.y);return hN(hN({},t),{},{x:a(c),y:l(c)})}return hN(hN({},t),{},{x:t.x,y:t.y})});return r.renderCurveStatically(l,t,e)}var s=N(0,m)(c);if(i){var p="".concat(i).split(/[,\s]+/gim).map(function(t){return parseFloat(t)});a=r.getStrokeDasharray(s,m,p)}else a=r.generateSimpleStrokeDasharray(m,s);return r.renderCurveStatically(o,t,e,{strokeDasharray:a})})}},{key:"renderCurve",value:function(t,e){var r=this.props,n=r.points,o=r.isAnimationActive,i=this.state,a=i.prevPoints,c=i.totalLength;return o&&n&&n.length&&(!a&&c>0||!cY()(a,n))?this.renderCurveWithAnimation(t,e):this.renderCurveStatically(n,t,e)}},{key:"render",value:function(){var t,e=this.props,r=e.hide,n=e.dot,o=e.points,i=e.className,a=e.xAxis,c=e.yAxis,u=e.top,l=e.left,s=e.width,f=e.height,d=e.isAnimationActive,y=e.id;if(r||!o||!o.length)return null;var v=this.state.isAnimationFinished,m=1===o.length,b=(0,h.A)("recharts-line",i),g=a&&a.allowDataOverflow,x=c&&c.allowDataOverflow,w=g||x,O=L()(y)?this.id:y,j=null!=(t=tp(n,!1))?t:{r:3,strokeWidth:2},S=j.r,P=j.strokeWidth,A=(n&&"object"===te(n)&&"clipDot"in n?n:{}).clipDot,k=void 0===A||A,E=2*(void 0===S?3:S)+(void 0===P?2:P);return p().createElement(tC,{className:b},g||x?p().createElement("defs",null,p().createElement("clipPath",{id:"clipPath-".concat(O)},p().createElement("rect",{x:g?l:l-s/2,y:x?u:u-f/2,width:g?s:2*s,height:x?f:2*f})),!k&&p().createElement("clipPath",{id:"clipPath-dots-".concat(O)},p().createElement("rect",{x:l-E/2,y:u-E/2,width:s+E,height:f+E}))):null,!m&&this.renderCurve(w,O),this.renderErrorBar(w,O),(m||n)&&this.renderDots(w,k,O),(!d||v)&&sy.renderCallByParent(this.props,o))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curPoints:t.points,prevPoints:e.curPoints}:t.points!==e.curPoints?{curPoints:t.points}:null}},{key:"repeat",value:function(t,e){for(var r=t.length%2!=0?[].concat(hD(t),[0]):t,n=[],o=0;o<e;++o)n=[].concat(hD(n),hD(r));return n}},{key:"renderDotItem",value:function(t,e){var r;if(p().isValidElement(t))r=p().cloneElement(t,e);else if(U()(t))r=t(e);else{var n=e.key,o=h_(e,hE),i=(0,h.A)("recharts-line-dot","boolean"!=typeof t?t.className:"");r=p().createElement(e4,hT({key:n},o,{className:i}))}return r}}],e&&hB(n.prototype,e),r&&hB(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(f.PureComponent);function hW(t,e,r){if(e<1)return[];if(1===e&&void 0===r)return t;for(var n=[],o=0;o<t.length;o+=e)if(void 0!==r&&!0!==r(t[o]))return;else n.push(t[o]);return n}function hq(t,e,r,n,o){if(t*e<t*n||t*e>t*o)return!1;var i=r();return t*(e-t*i/2-n)>=0&&t*(e+t*i/2-o)<=0}function hX(t){return(hX="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function hH(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function hV(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hH(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=hX(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hX(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hX(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hH(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function hG(t,e,r){var n,o,i,a,c,u=t.tick,l=t.ticks,s=t.viewBox,f=t.minTickGap,p=t.orientation,h=t.interval,d=t.tickFormatter,y=t.unit,v=t.angle;if(!l||!l.length||!u)return[];if(A(h)||t0.isSsr)return hW(l,("number"==typeof h&&A(h)?h:0)+1);var m=[],b="top"===p||"bottom"===p?"width":"height",g=y&&"width"===b?nT(y,{fontSize:e,letterSpacing:r}):{width:0,height:0},x=function(t,n){var o,i=U()(d)?d(t.value,n):t.value;return"width"===b?(o=nT(i,{fontSize:e,letterSpacing:r}),fd({width:o.width+g.width,height:o.height+g.height},v)):nT(i,{fontSize:e,letterSpacing:r})[b]},w=l.length>=2?S(l[1].coordinate-l[0].coordinate):1,O=(n="width"===b,o=s.x,i=s.y,a=s.width,c=s.height,1===w?{start:n?o:i,end:n?o+a:i+c}:{start:n?o+a:i+c,end:n?o:i});return"equidistantPreserveStart"===h?function(t,e,r,n,o){for(var i,a=(n||[]).slice(),c=e.start,u=e.end,l=0,s=1,f=c;s<=a.length;)if(i=function(){var e,i=null==n?void 0:n[l];if(void 0===i)return{v:hW(n,s)};var a=l,p=function(){return void 0===e&&(e=r(i,a)),e},h=i.coordinate,d=0===l||hq(t,h,p,f,u);d||(l=0,f=c,s+=1),d&&(f=h+t*(p()/2+o),l+=s)}())return i.v;return[]}(w,O,x,l,f):("preserveStart"===h||"preserveStartEnd"===h?function(t,e,r,n,o,i){var a=(n||[]).slice(),c=a.length,u=e.start,l=e.end;if(i){var s=n[c-1],f=r(s,c-1),p=t*(s.coordinate+t*f/2-l);a[c-1]=s=hV(hV({},s),{},{tickCoord:p>0?s.coordinate-p*t:s.coordinate}),hq(t,s.tickCoord,function(){return f},u,l)&&(l=s.tickCoord-t*(f/2+o),a[c-1]=hV(hV({},s),{},{isShow:!0}))}for(var h=i?c-1:c,d=function(e){var n,i=a[e],c=function(){return void 0===n&&(n=r(i,e)),n};if(0===e){var s=t*(i.coordinate-t*c()/2-u);a[e]=i=hV(hV({},i),{},{tickCoord:s<0?i.coordinate-s*t:i.coordinate})}else a[e]=i=hV(hV({},i),{},{tickCoord:i.coordinate});hq(t,i.tickCoord,c,u,l)&&(u=i.tickCoord+t*(c()/2+o),a[e]=hV(hV({},i),{},{isShow:!0}))},y=0;y<h;y++)d(y);return a}(w,O,x,l,f,"preserveStartEnd"===h):function(t,e,r,n,o){for(var i=(n||[]).slice(),a=i.length,c=e.start,u=e.end,l=function(e){var n,l=i[e],s=function(){return void 0===n&&(n=r(l,e)),n};if(e===a-1){var f=t*(l.coordinate+t*s()/2-u);i[e]=l=hV(hV({},l),{},{tickCoord:f>0?l.coordinate-f*t:l.coordinate})}else i[e]=l=hV(hV({},l),{},{tickCoord:l.coordinate});hq(t,l.tickCoord,s,c,u)&&(u=l.tickCoord-t*(s()/2+o),i[e]=hV(hV({},l),{},{isShow:!0}))},s=a-1;s>=0;s--)l(s);return i}(w,O,x,l,f)).filter(function(t){return t.isShow})}hU(h$,"displayName","Line"),hU(h$,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!t0.isSsr,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1}),hU(h$,"getComposedData",function(t){var e=t.props,r=t.xAxis,n=t.yAxis,o=t.xAxisTicks,i=t.yAxisTicks,a=t.dataKey,c=t.bandSize,u=t.displayedData,l=t.offset,s=e.layout;return hN({points:u.map(function(t,e){var u=uJ(t,a);return"horizontal"===s?{x:lf({axis:r,ticks:o,bandSize:c,entry:t,index:e}),y:L()(u)?null:n.scale(u),value:u,payload:t}:{x:L()(u)?null:r.scale(u),y:lf({axis:n,ticks:i,bandSize:c,entry:t,index:e}),value:u,payload:t}}),layout:s},l)});var hY=["viewBox"],hK=["viewBox"],hZ=["ticks"];function hJ(t){return(hJ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function hQ(){return(hQ=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function h0(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function h1(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?h0(Object(r),!0).forEach(function(e){h8(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):h0(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function h2(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function h5(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,h7(n.key),n)}}function h3(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(h3=function(){return!!t})()}function h4(t){return(h4=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function h6(t,e){return(h6=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function h8(t,e,r){return(e=h7(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function h7(t){var e=function(t,e){if("object"!=hJ(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hJ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hJ(e)?e:e+""}var h9=function(t){var e,r;function n(t){var e,r,o;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return r=n,o=[t],r=h4(r),(e=function(t,e){if(e&&("object"===hJ(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,h3()?Reflect.construct(r,o||[],h4(this).constructor):r.apply(this,o))).state={fontSize:"",letterSpacing:""},e}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&h6(n,t),e=[{key:"shouldComponentUpdate",value:function(t,e){var r=t.viewBox,n=h2(t,hY),o=this.props,i=o.viewBox,a=h2(o,hK);return!q(r,i)||!q(n,a)||!q(e,this.state)}},{key:"componentDidMount",value:function(){var t=this.layerReference;if(t){var e=t.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];e&&this.setState({fontSize:window.getComputedStyle(e).fontSize,letterSpacing:window.getComputedStyle(e).letterSpacing})}}},{key:"getTickLineCoord",value:function(t){var e,r,n,o,i,a,c=this.props,u=c.x,l=c.y,s=c.width,f=c.height,p=c.orientation,h=c.tickSize,d=c.mirror,y=c.tickMargin,v=d?-1:1,m=t.tickSize||h,b=A(t.tickCoord)?t.tickCoord:t.coordinate;switch(p){case"top":e=r=t.coordinate,a=(n=(o=l+!d*f)-v*m)-v*y,i=b;break;case"left":n=o=t.coordinate,i=(e=(r=u+!d*s)-v*m)-v*y,a=b;break;case"right":n=o=t.coordinate,i=(e=(r=u+d*s)+v*m)+v*y,a=b;break;default:e=r=t.coordinate,a=(n=(o=l+d*f)+v*m)+v*y,i=b}return{line:{x1:e,y1:n,x2:r,y2:o},tick:{x:i,y:a}}}},{key:"getTickTextAnchor",value:function(){var t,e=this.props,r=e.orientation,n=e.mirror;switch(r){case"left":t=n?"start":"end";break;case"right":t=n?"end":"start";break;default:t="middle"}return t}},{key:"getTickVerticalAnchor",value:function(){var t=this.props,e=t.orientation,r=t.mirror,n="end";switch(e){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,o=t.height,i=t.orientation,a=t.mirror,c=t.axisLine,u=h1(h1(h1({},tp(this.props,!1)),tp(c,!1)),{},{fill:"none"});if("top"===i||"bottom"===i){var l=+("top"===i&&!a||"bottom"===i&&a);u=h1(h1({},u),{},{x1:e,y1:r+l*o,x2:e+n,y2:r+l*o})}else{var s=+("left"===i&&!a||"right"===i&&a);u=h1(h1({},u),{},{x1:e+s*n,y1:r,x2:e+s*n,y2:r+o})}return p().createElement("line",hQ({},u,{className:(0,h.A)("recharts-cartesian-axis-line",w()(c,"className"))}))}},{key:"renderTicks",value:function(t,e,r){var o=this,i=this.props,a=i.tickLine,c=i.stroke,u=i.tick,l=i.tickFormatter,s=i.unit,f=hG(h1(h1({},this.props),{},{ticks:t}),e,r),d=this.getTickTextAnchor(),y=this.getTickVerticalAnchor(),v=tp(this.props,!1),m=tp(u,!1),b=h1(h1({},v),{},{fill:"none"},tp(a,!1)),g=f.map(function(t,e){var r=o.getTickLineCoord(t),i=r.line,g=r.tick,x=h1(h1(h1(h1({textAnchor:d,verticalAnchor:y},v),{},{stroke:"none",fill:c},m),g),{},{index:e,payload:t,visibleTicksCount:f.length,tickFormatter:l});return p().createElement(tC,hQ({className:"recharts-cartesian-axis-tick",key:"tick-".concat(t.value,"-").concat(t.coordinate,"-").concat(t.tickCoord)},Z(o.props,t,e)),a&&p().createElement("line",hQ({},b,i,{className:(0,h.A)("recharts-cartesian-axis-tick-line",w()(a,"className"))})),u&&n.renderTickItem(u,x,"".concat(U()(l)?l(t.value,e):t.value).concat(s||"")))});return p().createElement("g",{className:"recharts-cartesian-axis-ticks"},g)}},{key:"render",value:function(){var t=this,e=this.props,r=e.axisLine,n=e.width,o=e.height,i=e.ticksGenerator,a=e.className;if(e.hide)return null;var c=this.props,u=c.ticks,l=h2(c,hZ),s=u;return(U()(i)&&(s=i(u&&u.length>0?this.props:l)),n<=0||o<=0||!s||!s.length)?null:p().createElement(tC,{className:(0,h.A)("recharts-cartesian-axis",a),ref:function(e){t.layerReference=e}},r&&this.renderAxisLine(),this.renderTicks(s,this.state.fontSize,this.state.letterSpacing),l6.renderCallByParent(this.props))}}],r=[{key:"renderTickItem",value:function(t,e,r){var n;return p().isValidElement(t)?p().cloneElement(t,e):U()(t)?t(e):p().createElement(n3,hQ({},e,{className:"recharts-cartesian-axis-tick-value"}),r)}}],e&&h5(n.prototype,e),r&&h5(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(f.Component);function dt(t){return(dt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}h8(h9,"displayName","CartesianAxis"),h8(h9,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});function de(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(de=function(){return!!t})()}function dr(t){return(dr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function dn(t,e){return(dn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function di(t,e,r){return(e=da(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function da(t){var e=function(t,e){if("object"!=dt(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=dt(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==dt(e)?e:e+""}function dc(){return(dc=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function du(t){var e=t.xAxisId,r=fW(),n=fq(),o=fU(e);return null==o?null:p().createElement(h9,dc({},o,{className:(0,h.A)("recharts-".concat(o.axisType," ").concat(o.axisType),o.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(t){return lt(t,!0)}}))}var dl=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=dr(t),function(t,e){if(e&&("object"===dt(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,de()?Reflect.construct(t,e||[],dr(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&dn(r,t),e=[{key:"render",value:function(){return p().createElement(du,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,da(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(p().Component);function ds(t){return(ds="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}di(dl,"displayName","XAxis"),di(dl,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});function df(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(df=function(){return!!t})()}function dp(t){return(dp=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function dh(t,e){return(dh=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function dd(t,e,r){return(e=dy(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function dy(t){var e=function(t,e){if("object"!=ds(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ds(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ds(e)?e:e+""}function dv(){return(dv=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var dm=function(t){var e=t.yAxisId,r=fW(),n=fq(),o=f$(e);return null==o?null:p().createElement(h9,dv({},o,{className:(0,h.A)("recharts-".concat(o.axisType," ").concat(o.axisType),o.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(t){return lt(t,!0)}}))},db=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=dp(t),function(t,e){if(e&&("object"===ds(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,df()?Reflect.construct(t,e||[],dp(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&dh(r,t),e=[{key:"render",value:function(){return p().createElement(dm,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,dy(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(p().Component);dd(db,"displayName","YAxis"),dd(db,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});var dg=function(t){var e=t.chartName,r=t.GraphicalChild,n=t.defaultTooltipEventType,o=void 0===n?"axis":n,i=t.validateTooltipEventTypes,a=void 0===i?["axis"]:i,c=t.axisComponents,u=t.legendContent,l=t.formatAxisMap,s=t.defaultProps,d=function(t,e){var r=e.graphicalItems,n=e.stackGroups,o=e.offset,i=e.updateId,a=e.dataStartIndex,u=e.dataEndIndex,l=t.barSize,s=t.layout,f=t.barGap,p=t.barCategoryGap,h=t.maxBarSize,d=hP(s),y=d.numericAxisName,v=d.cateAxisName,m=!!r&&!!r.length&&r.some(function(t){var e=tn(t&&t.type);return e&&e.indexOf("Bar")>=0}),b=[];return r.forEach(function(r,d){var g=hv(t.data,{graphicalItems:[r],dataStartIndex:a,dataEndIndex:u}),x=void 0!==r.type.defaultProps?hu(hu({},r.type.defaultProps),r.props):r.props,w=x.dataKey,O=x.maxBarSize,j=x["".concat(y,"Id")],S=x["".concat(v,"Id")],P=c.reduce(function(t,r){var n=e["".concat(r.axisType,"Map")],o=x["".concat(r.axisType,"Id")];n&&n[o]||"zAxis"===r.axisType||tA(!1);var i=n[o];return hu(hu({},t),{},hl(hl({},r.axisType,i),"".concat(r.axisType,"Ticks"),lt(i)))},{}),A=P[v],k=P["".concat(v,"Ticks")],E=n&&n[j]&&n[j].hasStack&&ld(r,n[j].stackGroups),M=tn(r.type).indexOf("Bar")>=0,_=lg(A,k),T=[],C=m&&u2({barSize:l,stackGroups:n,totalSize:"xAxis"===v?P[v].width:"yAxis"===v?P[v].height:void 0});if(M){var N,D,I=L()(O)?h:O,B=null!=(N=null!=(D=lg(A,k,!0))?D:I)?N:0;T=u5({barGap:f,barCategoryGap:p,bandSize:B!==_?B:_,sizeList:C[S],maxBarSize:I}),B!==_&&(T=T.map(function(t){return hu(hu({},t),{},{position:hu(hu({},t.position),{},{offset:t.position.offset-B/2})})}))}var R=r&&r.type&&r.type.getComposedData;R&&b.push({props:hu(hu({},R(hu(hu({},P),{},{displayedData:g,props:t,dataKey:w,item:r,bandSize:_,barPosition:T,offset:o,stackedData:E,layout:s,dataStartIndex:a,dataEndIndex:u}))),{},hl(hl(hl({key:r.key||"item-".concat(d)},y,P[y]),v,P[v]),"animationId",i)),childIndex:ta(t.children).indexOf(r),item:r})}),b},v=function(t,n){var o=t.props,i=t.dataStartIndex,a=t.dataEndIndex,u=t.updateId;if(!tl({props:o}))return null;var s=o.children,f=o.layout,p=o.stackOffset,h=o.data,y=o.reverseStackOrder,v=hP(f),m=v.numericAxisName,b=v.cateAxisName,g=tc(s,r),x=ll(h,g,"".concat(m,"Id"),"".concat(b,"Id"),p,y),w=c.reduce(function(t,e){var r="".concat(e.axisType,"Map");return hu(hu({},t),{},hl({},r,hO(o,hu(hu({},e),{},{graphicalItems:g,stackGroups:e.axisType===m&&x,dataStartIndex:i,dataEndIndex:a}))))},{}),O=hA(hu(hu({},w),{},{props:o,graphicalItems:g}),null==n?void 0:n.legendBBox);Object.keys(w).forEach(function(t){w[t]=l(o,w[t],O,t.replace("Map",""),e)});var j=hj(w["".concat(b,"Map")]),S=d(o,hu(hu({},w),{},{dataStartIndex:i,dataEndIndex:a,updateId:u,graphicalItems:g,stackGroups:x,offset:O}));return hu(hu({formattedGraphicalItems:S,graphicalItems:g,offset:O,stackGroups:x},j),w)},m=function(t){var r;function n(t){var r,o,i,a,c;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return a=n,c=[t],a=hr(a),hl(i=function(t,e){if(e&&("object"===p8(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,he()?Reflect.construct(a,c||[],hr(this).constructor):a.apply(this,c)),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),hl(i,"accessibilityManager",new py),hl(i,"handleLegendBBoxUpdate",function(t){if(t){var e=i.state,r=e.dataStartIndex,n=e.dataEndIndex,o=e.updateId;i.setState(hu({legendBBox:t},v({props:i.props,dataStartIndex:r,dataEndIndex:n,updateId:o},hu(hu({},i.state),{},{legendBBox:t}))))}}),hl(i,"handleReceiveSyncEvent",function(t,e,r){i.props.syncId===t&&(r!==i.eventEmitterSymbol||"function"==typeof i.props.syncMethod)&&i.applySyncEvent(e)}),hl(i,"handleBrushChange",function(t){var e=t.startIndex,r=t.endIndex;if(e!==i.state.dataStartIndex||r!==i.state.dataEndIndex){var n=i.state.updateId;i.setState(function(){return hu({dataStartIndex:e,dataEndIndex:r},v({props:i.props,dataStartIndex:e,dataEndIndex:r,updateId:n},i.state))}),i.triggerSyncEvent({dataStartIndex:e,dataEndIndex:r})}}),hl(i,"handleMouseEnter",function(t){var e=i.getMouseInfo(t);if(e){var r=hu(hu({},e),{},{isTooltipActive:!0});i.setState(r),i.triggerSyncEvent(r);var n=i.props.onMouseEnter;U()(n)&&n(r,t)}}),hl(i,"triggeredAfterMouseMove",function(t){var e=i.getMouseInfo(t),r=e?hu(hu({},e),{},{isTooltipActive:!0}):{isTooltipActive:!1};i.setState(r),i.triggerSyncEvent(r);var n=i.props.onMouseMove;U()(n)&&n(r,t)}),hl(i,"handleItemMouseEnter",function(t){i.setState(function(){return{isTooltipActive:!0,activeItem:t,activePayload:t.tooltipPayload,activeCoordinate:t.tooltipPosition||{x:t.cx,y:t.cy}}})}),hl(i,"handleItemMouseLeave",function(){i.setState(function(){return{isTooltipActive:!1}})}),hl(i,"handleMouseMove",function(t){t.persist(),i.throttleTriggeredAfterMouseMove(t)}),hl(i,"handleMouseLeave",function(t){i.throttleTriggeredAfterMouseMove.cancel();var e={isTooltipActive:!1};i.setState(e),i.triggerSyncEvent(e);var r=i.props.onMouseLeave;U()(r)&&r(e,t)}),hl(i,"handleOuterEvent",function(t){var e,r,n=tv(t),o=w()(i.props,"".concat(n));n&&U()(o)&&o(null!=(e=/.*touch.*/i.test(n)?i.getMouseInfo(t.changedTouches[0]):i.getMouseInfo(t))?e:{},t)}),hl(i,"handleClick",function(t){var e=i.getMouseInfo(t);if(e){var r=hu(hu({},e),{},{isTooltipActive:!0});i.setState(r),i.triggerSyncEvent(r);var n=i.props.onClick;U()(n)&&n(r,t)}}),hl(i,"handleMouseDown",function(t){var e=i.props.onMouseDown;U()(e)&&e(i.getMouseInfo(t),t)}),hl(i,"handleMouseUp",function(t){var e=i.props.onMouseUp;U()(e)&&e(i.getMouseInfo(t),t)}),hl(i,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&i.throttleTriggeredAfterMouseMove(t.changedTouches[0])}),hl(i,"handleTouchStart",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&i.handleMouseDown(t.changedTouches[0])}),hl(i,"handleTouchEnd",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&i.handleMouseUp(t.changedTouches[0])}),hl(i,"handleDoubleClick",function(t){var e=i.props.onDoubleClick;U()(e)&&e(i.getMouseInfo(t),t)}),hl(i,"handleContextMenu",function(t){var e=i.props.onContextMenu;U()(e)&&e(i.getMouseInfo(t),t)}),hl(i,"triggerSyncEvent",function(t){void 0!==i.props.syncId&&ps.emit(pf,i.props.syncId,t,i.eventEmitterSymbol)}),hl(i,"applySyncEvent",function(t){var e=i.props,r=e.layout,n=e.syncMethod,o=i.state.updateId,a=t.dataStartIndex,c=t.dataEndIndex;if(void 0!==t.dataStartIndex||void 0!==t.dataEndIndex)i.setState(hu({dataStartIndex:a,dataEndIndex:c},v({props:i.props,dataStartIndex:a,dataEndIndex:c,updateId:o},i.state)));else if(void 0!==t.activeTooltipIndex){var u=t.chartX,l=t.chartY,s=t.activeTooltipIndex,f=i.state,p=f.offset,h=f.tooltipTicks;if(!p)return;if("function"==typeof n)s=n(h,t);else if("value"===n){s=-1;for(var d=0;d<h.length;d++)if(h[d].value===t.activeLabel){s=d;break}}var y=hu(hu({},p),{},{x:p.left,y:p.top}),m=Math.min(u,y.x+y.width),b=Math.min(l,y.y+y.height),g=h[s]&&h[s].value,x=hb(i.state,i.props.data,s),w=h[s]?{x:"horizontal"===r?h[s].coordinate:m,y:"horizontal"===r?b:h[s].coordinate}:hh;i.setState(hu(hu({},t),{},{activeLabel:g,activeCoordinate:w,activePayload:x,activeTooltipIndex:s}))}else i.setState(t)}),hl(i,"renderCursor",function(t){var r,n=i.state,o=n.isTooltipActive,a=n.activeCoordinate,c=n.activePayload,u=n.offset,l=n.activeTooltipIndex,s=n.tooltipAxisBandSize,f=i.getTooltipEventType(),h=null!=(r=t.props.active)?r:o,d=i.props.layout,y=t.key||"_recharts-cursor";return p().createElement(p3,{key:y,activeCoordinate:a,activePayload:c,activeTooltipIndex:l,chartName:e,element:t,isActive:h,layout:d,offset:u,tooltipAxisBandSize:s,tooltipEventType:f})}),hl(i,"renderPolarAxis",function(t,e,r){var n=w()(t,"type.axisType"),o=w()(i.state,"".concat(n,"Map")),a=t.type.defaultProps,c=void 0!==a?hu(hu({},a),t.props):t.props,u=o&&o[c["".concat(n,"Id")]];return(0,f.cloneElement)(t,hu(hu({},u),{},{className:(0,h.A)(n,u.className),key:t.key||"".concat(e,"-").concat(r),ticks:lt(u,!0)}))}),hl(i,"renderPolarGrid",function(t){var e=t.props,r=e.radialLines,n=e.polarAngles,o=e.polarRadius,a=i.state,c=a.radiusAxisMap,u=a.angleAxisMap,l=T(c),s=T(u),p=s.cx,h=s.cy,d=s.innerRadius,y=s.outerRadius;return(0,f.cloneElement)(t,{polarAngles:Array.isArray(n)?n:lt(s,!0).map(function(t){return t.coordinate}),polarRadius:Array.isArray(o)?o:lt(l,!0).map(function(t){return t.coordinate}),cx:p,cy:h,innerRadius:d,outerRadius:y,key:t.key||"polar-grid",radialLines:r})}),hl(i,"renderLegend",function(){var t=i.state.formattedGraphicalItems,e=i.props,r=e.children,n=e.width,o=e.height,a=i.props.margin||{},c=uX({children:r,formattedGraphicalItems:t,legendWidth:n-(a.left||0)-(a.right||0),legendContent:u});if(!c)return null;var l=c.item,s=ht(c,p4);return(0,f.cloneElement)(l,hu(hu({},s),{},{chartWidth:n,chartHeight:o,margin:a,onBBoxUpdate:i.handleLegendBBoxUpdate}))}),hl(i,"renderTooltip",function(){var t,e=i.props,r=e.children,n=e.accessibilityLayer,o=tu(r,en);if(!o)return null;var a=i.state,c=a.isTooltipActive,u=a.activeCoordinate,l=a.activePayload,s=a.activeLabel,p=a.offset,h=null!=(t=o.props.active)?t:c;return(0,f.cloneElement)(o,{viewBox:hu(hu({},p),{},{x:p.left,y:p.top}),active:h,label:s,payload:h?l:[],coordinate:u,accessibilityLayer:n})}),hl(i,"renderBrush",function(t){var e=i.props,r=e.margin,n=e.data,o=i.state,a=o.offset,c=o.dataStartIndex,u=o.dataEndIndex,l=o.updateId;return(0,f.cloneElement)(t,{key:t.key||"_recharts-brush",onChange:lr(i.handleBrushChange,t.props.onChange),data:n,x:A(t.props.x)?t.props.x:a.left,y:A(t.props.y)?t.props.y:a.top+a.height+a.brushBottom-(r.bottom||0),width:A(t.props.width)?t.props.width:a.width,startIndex:c,endIndex:u,updateId:"brush-".concat(l)})}),hl(i,"renderReferenceElement",function(t,e,r){if(!t)return null;var n=i.clipPathId,o=i.state,a=o.xAxisMap,c=o.yAxisMap,u=o.offset,l=t.type.defaultProps||{},s=t.props,p=s.xAxisId,h=void 0===p?l.xAxisId:p,d=s.yAxisId,y=void 0===d?l.yAxisId:d;return(0,f.cloneElement)(t,{key:t.key||"".concat(e,"-").concat(r),xAxis:a[h],yAxis:c[y],viewBox:{x:u.left,y:u.top,width:u.width,height:u.height},clipPathId:n})}),hl(i,"renderActivePoints",function(t){var e=t.item,r=t.activePoint,o=t.basePoint,i=t.childIndex,a=t.isRange,c=[],u=e.props.key,l=void 0!==e.item.type.defaultProps?hu(hu({},e.item.type.defaultProps),e.item.props):e.item.props,s=l.activeDot,f=hu(hu({index:i,dataKey:l.dataKey,cx:r.x,cy:r.y,r:4,fill:u1(e.item),strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},tp(s,!1)),K(s));return c.push(n.renderActiveDot(s,f,"".concat(u,"-activePoint-").concat(i))),o?c.push(n.renderActiveDot(s,hu(hu({},f),{},{cx:o.x,cy:o.y}),"".concat(u,"-basePoint-").concat(i))):a&&c.push(null),c}),hl(i,"renderGraphicChild",function(t,e,r){var n=i.filterFormatItem(t,e,r);if(!n)return null;var o=i.getTooltipEventType(),a=i.state,c=a.isTooltipActive,u=a.tooltipAxis,l=a.activeTooltipIndex,s=a.activeLabel,p=tu(i.props.children,en),h=n.props,d=h.points,y=h.isRange,v=h.baseLine,m=void 0!==n.item.type.defaultProps?hu(hu({},n.item.type.defaultProps),n.item.props):n.item.props,b=m.activeDot,g=m.hide,x=m.activeBar,w=m.activeShape,O=!!(!g&&c&&p&&(b||x||w)),j={};"axis"!==o&&p&&"click"===p.props.trigger?j={onClick:lr(i.handleItemMouseEnter,t.props.onClick)}:"axis"!==o&&(j={onMouseLeave:lr(i.handleItemMouseLeave,t.props.onMouseLeave),onMouseEnter:lr(i.handleItemMouseEnter,t.props.onMouseEnter)});var S=(0,f.cloneElement)(t,hu(hu({},n.props),j));if(O)if(l>=0){if(u.dataKey&&!u.allowDuplicatedCategory){var P="function"==typeof u.dataKey?function(t){return"function"==typeof u.dataKey?u.dataKey(t.payload):null}:"payload.".concat(u.dataKey.toString());k=D(d,P,s),E=y&&v&&D(v,P,s)}else k=null==d?void 0:d[l],E=y&&v&&v[l];if(w||x){var A=void 0!==t.props.activeIndex?t.props.activeIndex:l;return[(0,f.cloneElement)(t,hu(hu(hu({},n.props),j),{},{activeIndex:A})),null,null]}if(!L()(k))return[S].concat(ho(i.renderActivePoints({item:n,activePoint:k,basePoint:E,childIndex:l,isRange:y})))}else{var k,E,M,_=(null!=(M=i.getItemByXY(i.state.activeCoordinate))?M:{graphicalItem:S}).graphicalItem,T=_.item,C=void 0===T?t:T,N=_.childIndex,I=hu(hu(hu({},n.props),j),{},{activeIndex:N});return[(0,f.cloneElement)(C,I),null,null]}return y?[S,null,null]:[S,null]}),hl(i,"renderCustomized",function(t,e,r){return(0,f.cloneElement)(t,hu(hu({key:"recharts-customized-".concat(r)},i.props),i.state))}),hl(i,"renderMap",{CartesianGrid:{handler:hd,once:!0},ReferenceArea:{handler:i.renderReferenceElement},ReferenceLine:{handler:hd},ReferenceDot:{handler:i.renderReferenceElement},XAxis:{handler:hd},YAxis:{handler:hd},Brush:{handler:i.renderBrush,once:!0},Bar:{handler:i.renderGraphicChild},Line:{handler:i.renderGraphicChild},Area:{handler:i.renderGraphicChild},Radar:{handler:i.renderGraphicChild},RadialBar:{handler:i.renderGraphicChild},Scatter:{handler:i.renderGraphicChild},Pie:{handler:i.renderGraphicChild},Funnel:{handler:i.renderGraphicChild},Tooltip:{handler:i.renderCursor,once:!0},PolarGrid:{handler:i.renderPolarGrid,once:!0},PolarAngleAxis:{handler:i.renderPolarAxis},PolarRadiusAxis:{handler:i.renderPolarAxis},Customized:{handler:i.renderCustomized}}),i.clipPathId="".concat(null!=(r=t.id)?r:M("recharts"),"-clip"),i.throttleTriggeredAfterMouseMove=y()(i.triggeredAfterMouseMove,null!=(o=t.throttleDelay)?o:1e3/60),i.state={},i}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&hn(n,t),r=[{key:"componentDidMount",value:function(){var t,e;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!=(t=this.props.margin.left)?t:0,top:null!=(e=this.props.margin.top)?e:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var t=this.props,e=t.children,r=t.data,n=t.height,o=t.layout,i=tu(e,en);if(i){var a=i.props.defaultIndex;if("number"==typeof a&&!(a<0)&&!(a>this.state.tooltipTicks.length-1)){var c=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,u=hb(this.state,r,a,c),l=this.state.tooltipTicks[a].coordinate,s=(this.state.offset.top+n)/2,f="horizontal"===o?{x:l,y:s}:{y:l,x:s},p=this.state.formattedGraphicalItems.find(function(t){return"Scatter"===t.item.type.name});p&&(f=hu(hu({},f),p.props.points[a].tooltipPosition),u=p.props.points[a].tooltipPayload);var h={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:c,activePayload:u,activeCoordinate:f};this.setState(h),this.renderCursor(i),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(t,e){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==e.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==t.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==t.margin){var r,n;this.accessibilityManager.setDetails({offset:{left:null!=(r=this.props.margin.left)?r:0,top:null!=(n=this.props.margin.top)?n:0}})}return null}},{key:"componentDidUpdate",value:function(t){th([tu(t.children,en)],[tu(this.props.children,en)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var t=tu(this.props.children,en);if(t&&"boolean"==typeof t.props.shared){var e=t.props.shared?"axis":"item";return a.indexOf(e)>=0?e:o}return o}},{key:"getMouseInfo",value:function(t){if(!this.container)return null;var e=this.container,r=e.getBoundingClientRect(),n={top:r.top+window.scrollY-document.documentElement.clientTop,left:r.left+window.scrollX-document.documentElement.clientLeft},o={chartX:Math.round(t.pageX-n.left),chartY:Math.round(t.pageY-n.top)},i=r.width/e.offsetWidth||1,a=this.inRange(o.chartX,o.chartY,i);if(!a)return null;var c=this.state,u=c.xAxisMap,l=c.yAxisMap,s=this.getTooltipEventType(),f=hg(this.state,this.props.data,this.props.layout,a);if("axis"!==s&&u&&l){var p=T(u).scale,h=T(l).scale,d=p&&p.invert?p.invert(o.chartX):null,y=h&&h.invert?h.invert(o.chartY):null;return hu(hu({},o),{},{xValue:d,yValue:y},f)}return f?hu(hu({},o),f):null}},{key:"inRange",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,o=t/r,i=e/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset;return o>=a.left&&o<=a.left+a.width&&i>=a.top&&i<=a.top+a.height?{x:o,y:i}:null}var c=this.state,u=c.angleAxisMap,l=c.radiusAxisMap;return u&&l?lY({x:o,y:i},T(u)):null}},{key:"parseEventsOfWrapper",value:function(){var t=this.props.children,e=this.getTooltipEventType(),r=tu(t,en),n={};return r&&"axis"===e&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),hu(hu({},K(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){ps.on(pf,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){ps.removeListener(pf,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(t,e,r){for(var n=this.state.formattedGraphicalItems,o=0,i=n.length;o<i;o++){var a=n[o];if(a.item===t||a.props.key===t.key||e===tn(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var t=this.clipPathId,e=this.state.offset,r=e.left,n=e.top,o=e.height,i=e.width;return p().createElement("defs",null,p().createElement("clipPath",{id:t},p().createElement("rect",{x:r,y:n,height:o,width:i})))}},{key:"getXScales",value:function(){var t=this.state.xAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=p9(e,2),n=r[0],o=r[1];return hu(hu({},t),{},hl({},n,o.scale))},{}):null}},{key:"getYScales",value:function(){var t=this.state.yAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=p9(e,2),n=r[0],o=r[1];return hu(hu({},t),{},hl({},n,o.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(t){var e;return null==(e=this.state.xAxisMap)||null==(e=e[t])?void 0:e.scale}},{key:"getYScaleByAxisId",value:function(t){var e;return null==(e=this.state.yAxisMap)||null==(e=e[t])?void 0:e.scale}},{key:"getItemByXY",value:function(t){var e=this.state,r=e.formattedGraphicalItems,n=e.activeItem;if(r&&r.length)for(var o=0,i=r.length;o<i;o++){var a=r[o],c=a.props,u=a.item,l=void 0!==u.type.defaultProps?hu(hu({},u.type.defaultProps),u.props):u.props,s=tn(u.type);if("Bar"===s){var f=(c.data||[]).find(function(e){return np(t,e)});if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===s){var p=(c.data||[]).find(function(e){return lY(t,e)});if(p)return{graphicalItem:a,payload:p}}else if(sW(a,n)||sq(a,n)||sX(a,n)){var h=function(t){var e,r,n,o=t.activeTooltipItem,i=t.graphicalItem,a=t.itemData,c=(sW(i,o)?e="trapezoids":sq(i,o)?e="sectors":sX(i,o)&&(e="points"),e),u=sW(i,o)?null==(r=o.tooltipPayload)||null==(r=r[0])||null==(r=r.payload)?void 0:r.payload:sq(i,o)?null==(n=o.tooltipPayload)||null==(n=n[0])||null==(n=n.payload)?void 0:n.payload:sX(i,o)?o.payload:{},l=a.filter(function(t,e){var r=cY()(u,t),n=i.props[c].filter(function(t){var e;return(sW(i,o)?e=sH:sq(i,o)?e=sV:sX(i,o)&&(e=sG),e)(t,o)}),a=i.props[c].indexOf(n[n.length-1]);return r&&e===a});return a.indexOf(l[l.length-1])}({graphicalItem:a,activeTooltipItem:n,itemData:l.data}),d=void 0===l.activeIndex?h:l.activeIndex;return{graphicalItem:hu(hu({},a),{},{childIndex:d}),payload:sX(a,n)?l.data[h]:a.props.data[h]}}}return null}},{key:"render",value:function(){var t,e,r=this;if(!tl(this))return null;var n=this.props,o=n.children,i=n.className,a=n.width,c=n.height,u=n.style,l=n.compact,s=n.title,f=n.desc,d=tp(ht(n,p6),!1);if(l)return p().createElement(fz,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},p().createElement(tM,p7({},d,{width:a,height:c,title:s,desc:f}),this.renderClipPath(),ty(o,this.renderMap)));this.props.accessibilityLayer&&(d.tabIndex=null!=(t=this.props.tabIndex)?t:0,d.role=null!=(e=this.props.role)?e:"application",d.onKeyDown=function(t){r.accessibilityManager.keyboardEvent(t)},d.onFocus=function(){r.accessibilityManager.focus()});var y=this.parseEventsOfWrapper();return p().createElement(fz,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},p().createElement("div",p7({className:(0,h.A)("recharts-wrapper",i),style:hu({position:"relative",cursor:"default",width:a,height:c},u)},y,{ref:function(t){r.container=t}}),p().createElement(tM,p7({},d,{width:a,height:c,title:s,desc:f,style:hp}),this.renderClipPath(),ty(o,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,hs(n.key),n)}}(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(f.Component);hl(m,"displayName",e),hl(m,"defaultProps",hu({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},s)),hl(m,"getDerivedStateFromProps",function(t,e){var r=t.dataKey,n=t.data,o=t.children,i=t.width,a=t.height,c=t.layout,u=t.stackOffset,l=t.margin,s=e.dataStartIndex,f=e.dataEndIndex;if(void 0===e.updateId){var p=hS(t);return hu(hu(hu({},p),{},{updateId:0},v(hu(hu({props:t},p),{},{updateId:0}),e)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:c,prevStackOffset:u,prevMargin:l,prevChildren:o})}if(r!==e.prevDataKey||n!==e.prevData||i!==e.prevWidth||a!==e.prevHeight||c!==e.prevLayout||u!==e.prevStackOffset||!q(l,e.prevMargin)){var h=hS(t),d={chartX:e.chartX,chartY:e.chartY,isTooltipActive:e.isTooltipActive},y=hu(hu({},hg(e,n,c)),{},{updateId:e.updateId+1}),m=hu(hu(hu({},h),d),y);return hu(hu(hu({},m),v(hu({props:t},m),e)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:c,prevStackOffset:u,prevMargin:l,prevChildren:o})}if(!th(o,e.prevChildren)){var b,g,x,w,O=tu(o,lU),j=O&&null!=(b=null==(g=O.props)?void 0:g.startIndex)?b:s,S=O&&null!=(x=null==(w=O.props)?void 0:w.endIndex)?x:f,P=L()(n)||j!==s||S!==f?e.updateId+1:e.updateId;return hu(hu({updateId:P},v(hu(hu({props:t},e),{},{updateId:P,dataStartIndex:j,dataEndIndex:S}),e)),{},{prevChildren:o,dataStartIndex:j,dataEndIndex:S})}return null}),hl(m,"renderActiveDot",function(t,e,r){var n;return n=(0,f.isValidElement)(t)?(0,f.cloneElement)(t,e):U()(t)?t(e):p().createElement(e4,e),p().createElement(tC,{className:"recharts-active-dot",key:r},n)});var b=(0,f.forwardRef)(function(t,e){return p().createElement(m,p7({},t,{ref:e}))});return b.displayName=m.displayName,b}({chartName:"LineChart",GraphicalChild:h$,axisComponents:[{axisType:"xAxis",AxisComp:dl},{axisType:"yAxis",AxisComp:db}],formatAxisMap:function(t,e,r,n,o){var i=t.width,a=t.height,c=t.layout,u=t.children,l=Object.keys(e),s={left:r.left,leftMirror:r.left,right:i-r.right,rightMirror:i-r.right,top:r.top,topMirror:r.top,bottom:a-r.bottom,bottomMirror:a-r.bottom},f=!!tu(u,fo);return l.reduce(function(i,a){var u,l,p,h,d,y=e[a],v=y.orientation,m=y.domain,b=y.padding,g=void 0===b?{}:b,x=y.mirror,w=y.reversed,O="".concat(v).concat(x?"Mirror":"");if("number"===y.type&&("gap"===y.padding||"no-gap"===y.padding)){var j=m[1]-m[0],S=1/0,P=y.categoricalDomain.sort(I);if(P.forEach(function(t,e){e>0&&(S=Math.min((t||0)-(P[e-1]||0),S))}),Number.isFinite(S)){var A=S/j,k="vertical"===y.layout?r.height:r.width;if("gap"===y.padding&&(u=A*k/2),"no-gap"===y.padding){var E=_(t.barCategoryGap,A*k),M=A*k/2;u=M-E-(M-E)/k*E}}}l="xAxis"===n?[r.left+(g.left||0)+(u||0),r.left+r.width-(g.right||0)-(u||0)]:"yAxis"===n?"horizontal"===c?[r.top+r.height-(g.bottom||0),r.top+(g.top||0)]:[r.top+(g.top||0)+(u||0),r.top+r.height-(g.bottom||0)-(u||0)]:y.range,w&&(l=[l[1],l[0]]);var T=ln(y,o,f),C=T.scale,N=T.realScaleType;C.domain(m).range(l),lo(C);var D=ls(C,fu(fu({},y),{},{realScaleType:N}));"xAxis"===n?(d="top"===v&&!x||"bottom"===v&&x,p=r.left,h=s[O]-d*y.height):"yAxis"===n&&(d="left"===v&&!x||"right"===v&&x,p=s[O]-d*y.width,h=r.top);var B=fu(fu(fu({},y),D),{},{realScaleType:N,x:p,y:h,scale:C,width:"xAxis"===n?r.width:y.width,height:"yAxis"===n?r.height:y.height});return B.bandSize=lg(B,D),y.hide||"xAxis"!==n?y.hide||(s[O]+=(d?-1:1)*B.width):s[O]+=(d?-1:1)*B.height,fu(fu({},i),{},fl({},a,B))},{})}}),dx=["x1","y1","x2","y2","key"],dw=["offset"];function dO(t){return(dO="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function dj(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function dS(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?dj(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=dO(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=dO(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==dO(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):dj(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function dP(){return(dP=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function dA(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var dk=function(t){var e=t.fill;if(!e||"none"===e)return null;var r=t.fillOpacity,n=t.x,o=t.y,i=t.width,a=t.height,c=t.ry;return p().createElement("rect",{x:n,y:o,ry:c,width:i,height:a,stroke:"none",fill:e,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function dE(t,e){var r;if(p().isValidElement(t))r=p().cloneElement(t,e);else if(U()(t))r=t(e);else{var n=e.x1,o=e.y1,i=e.x2,a=e.y2,c=e.key,u=tp(dA(e,dx),!1),l=(u.offset,dA(u,dw));r=p().createElement("line",dP({},l,{x1:n,y1:o,x2:i,y2:a,fill:"none",key:c}))}return r}function dM(t){var e=t.x,r=t.width,n=t.horizontal,o=void 0===n||n,i=t.horizontalPoints;if(!o||!i||!i.length)return null;var a=i.map(function(n,i){return dE(o,dS(dS({},t),{},{x1:e,y1:n,x2:e+r,y2:n,key:"line-".concat(i),index:i}))});return p().createElement("g",{className:"recharts-cartesian-grid-horizontal"},a)}function d_(t){var e=t.y,r=t.height,n=t.vertical,o=void 0===n||n,i=t.verticalPoints;if(!o||!i||!i.length)return null;var a=i.map(function(n,i){return dE(o,dS(dS({},t),{},{x1:n,y1:e,x2:n,y2:e+r,key:"line-".concat(i),index:i}))});return p().createElement("g",{className:"recharts-cartesian-grid-vertical"},a)}function dT(t){var e=t.horizontalFill,r=t.fillOpacity,n=t.x,o=t.y,i=t.width,a=t.height,c=t.horizontalPoints,u=t.horizontal;if(!(void 0===u||u)||!e||!e.length)return null;var l=c.map(function(t){return Math.round(t+o-o)}).sort(function(t,e){return t-e});o!==l[0]&&l.unshift(0);var s=l.map(function(t,c){var u=l[c+1]?l[c+1]-t:o+a-t;if(u<=0)return null;var s=c%e.length;return p().createElement("rect",{key:"react-".concat(c),y:t,x:n,height:u,width:i,stroke:"none",fill:e[s],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return p().createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},s)}function dC(t){var e=t.vertical,r=t.verticalFill,n=t.fillOpacity,o=t.x,i=t.y,a=t.width,c=t.height,u=t.verticalPoints;if(!(void 0===e||e)||!r||!r.length)return null;var l=u.map(function(t){return Math.round(t+o-o)}).sort(function(t,e){return t-e});o!==l[0]&&l.unshift(0);var s=l.map(function(t,e){var u=l[e+1]?l[e+1]-t:o+a-t;if(u<=0)return null;var s=e%r.length;return p().createElement("rect",{key:"react-".concat(e),x:t,y:i,width:u,height:c,stroke:"none",fill:r[s],fillOpacity:n,className:"recharts-cartesian-grid-bg"})});return p().createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},s)}var dN=function(t,e){var r=t.xAxis,n=t.width,o=t.height,i=t.offset;return u9(hG(dS(dS(dS({},h9.defaultProps),r),{},{ticks:lt(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.left,i.left+i.width,e)},dD=function(t,e){var r=t.yAxis,n=t.width,o=t.height,i=t.offset;return u9(hG(dS(dS(dS({},h9.defaultProps),r),{},{ticks:lt(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.top,i.top+i.height,e)},dI={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function dB(t){var e,r,n,o,i,a,c=fW(),u=fq(),l=(0,f.useContext)(fI),s=dS(dS({},t),{},{stroke:null!=(e=t.stroke)?e:dI.stroke,fill:null!=(r=t.fill)?r:dI.fill,horizontal:null!=(n=t.horizontal)?n:dI.horizontal,horizontalFill:null!=(o=t.horizontalFill)?o:dI.horizontalFill,vertical:null!=(i=t.vertical)?i:dI.vertical,verticalFill:null!=(a=t.verticalFill)?a:dI.verticalFill,x:A(t.x)?t.x:l.left,y:A(t.y)?t.y:l.top,width:A(t.width)?t.width:l.width,height:A(t.height)?t.height:l.height}),h=s.x,d=s.y,y=s.width,v=s.height,m=s.syncWithTicks,b=s.horizontalValues,g=s.verticalValues,x=T((0,f.useContext)(fC)),w=fF();if(!A(y)||y<=0||!A(v)||v<=0||!A(h)||h!==+h||!A(d)||d!==+d)return null;var O=s.verticalCoordinatesGenerator||dN,j=s.horizontalCoordinatesGenerator||dD,S=s.horizontalPoints,P=s.verticalPoints;if((!S||!S.length)&&U()(j)){var k=b&&b.length,E=j({yAxis:w?dS(dS({},w),{},{ticks:k?b:w.ticks}):void 0,width:c,height:u,offset:l},!!k||m);B(Array.isArray(E),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(dO(E),"]")),Array.isArray(E)&&(S=E)}if((!P||!P.length)&&U()(O)){var M=g&&g.length,_=O({xAxis:x?dS(dS({},x),{},{ticks:M?g:x.ticks}):void 0,width:c,height:u,offset:l},!!M||m);B(Array.isArray(_),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(dO(_),"]")),Array.isArray(_)&&(P=_)}return p().createElement("g",{className:"recharts-cartesian-grid"},p().createElement(dk,{fill:s.fill,fillOpacity:s.fillOpacity,x:s.x,y:s.y,width:s.width,height:s.height,ry:s.ry}),p().createElement(dM,dP({},s,{offset:l,horizontalPoints:S,xAxis:x,yAxis:w})),p().createElement(d_,dP({},s,{offset:l,verticalPoints:P,xAxis:x,yAxis:w})),p().createElement(dT,dP({},s,{horizontalPoints:S})),p().createElement(dC,dP({},s,{verticalPoints:P})))}dB.displayName="CartesianGrid";let dR=function(){let[t,e]=(0,f.useState)({totalJobs:0,totalApplications:0,totalStudents:0,placementRate:0}),[r,n]=(0,f.useState)([]),[a,p]=(0,f.useState)("2024"),[h,d]=(0,f.useState)("monthly"),y=[{title:"Total Active Jobs",value:t.totalJobs,icon:(0,o.jsx)(i.A,{className:"w-8 h-8"}),color:"from-blue-500 to-blue-600",bgColor:"bg-blue-50",iconColor:"text-blue-600",change:"+12%",changeType:"increase"},{title:"Total Applications",value:t.totalApplications.toLocaleString(),icon:(0,o.jsx)(c,{className:"w-8 h-8"}),color:"from-green-500 to-green-600",bgColor:"bg-green-50",iconColor:"text-green-600",change:"+8%",changeType:"increase"},{title:"Registered Students",value:t.totalStudents.toLocaleString(),icon:(0,o.jsx)(u.A,{className:"w-8 h-8"}),color:"from-purple-500 to-purple-600",bgColor:"bg-purple-50",iconColor:"text-purple-600",change:"+5%",changeType:"increase"},{title:"Placement Rate",value:`${t.placementRate}%`,icon:(0,o.jsx)(l.A,{className:"w-8 h-8"}),color:"from-orange-500 to-orange-600",bgColor:"bg-orange-50",iconColor:"text-orange-600",change:"-3.2%",changeType:"decrease"}];return(0,o.jsxs)("div",{className:"p-6 ml-20 overflow-y-auto h-full",children:[(0,o.jsxs)("div",{className:"mb-8",children:[(0,o.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Welcome back, Admin!"}),(0,o.jsx)("p",{className:"text-gray-600",children:"Here's what's happening at your college today."})]}),(0,o.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:y.map((t,e)=>(0,o.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow duration-200",children:(0,o.jsxs)("div",{className:"p-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,o.jsx)("div",{className:`p-3 rounded-lg ${t.bgColor}`,children:(0,o.jsx)("div",{className:t.iconColor,children:t.icon})}),(0,o.jsxs)("div",{className:`flex items-center text-sm font-medium ${"increase"===t.changeType?"text-green-600":"text-red-600"}`,children:["increase"===t.changeType?(0,o.jsx)(l.A,{className:"w-4 h-4 mr-1"}):(0,o.jsx)(s,{className:"w-4 h-4 mr-1"}),t.change]})]}),(0,o.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-1",children:t.value}),(0,o.jsx)("p",{className:"text-gray-600 text-sm",children:t.title})]})},e))}),(0,o.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-8",children:[(0,o.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6",children:[(0,o.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-2 md:mb-0",children:"Application Status Timeline"}),(0,o.jsxs)("div",{className:"flex flex-wrap items-center gap-2",children:[(0,o.jsx)("div",{className:"flex items-center space-x-2 border rounded-md p-1",children:(0,o.jsx)("button",{className:`px-3 py-1 text-sm rounded-md ${"monthly"===h?"bg-blue-100 text-blue-700":"hover:bg-gray-100"}`,onClick:()=>d("monthly"),children:"Monthly"})}),(0,o.jsx)("select",{value:a,onChange:t=>p(t.target.value),className:"border border-gray-300 rounded-md px-3 py-1 text-sm bg-white",children:(0,o.jsx)("option",{value:"2024",children:"2024"})})]})]}),(0,o.jsxs)("div",{className:"flex flex-wrap gap-4 mb-4",children:[(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("div",{className:"w-3 h-3 rounded-full bg-blue-600 mr-2"}),(0,o.jsx)("span",{className:"text-sm text-gray-700",children:"Applications"})]}),(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("div",{className:"w-3 h-3 rounded-full bg-green-500 mr-2"}),(0,o.jsx)("span",{className:"text-sm text-gray-700",children:"Interviews"})]}),(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("div",{className:"w-3 h-3 rounded-full bg-yellow-500 mr-2"}),(0,o.jsx)("span",{className:"text-sm text-gray-700",children:"Approved"})]}),(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("div",{className:"w-3 h-3 rounded-full bg-purple-500 mr-2"}),(0,o.jsx)("span",{className:"text-sm text-gray-700",children:"Rejected"})]}),(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("div",{className:"w-3 h-3 rounded-full bg-gray-400 mr-2"}),(0,o.jsx)("span",{className:"text-sm text-gray-700",children:"Pending"})]})]}),(0,o.jsx)("div",{className:"h-72",children:(0,o.jsx)(tw,{width:"100%",height:"100%",children:(0,o.jsxs)(dg,{data:r,margin:{top:5,right:30,left:20,bottom:5},children:[(0,o.jsx)(dB,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),(0,o.jsx)(dl,{dataKey:"name",axisLine:{stroke:"#e0e0e0"},tick:{fill:"#666666",fontSize:12}}),(0,o.jsx)(db,{axisLine:{stroke:"#e0e0e0"},tick:{fill:"#666666",fontSize:12}}),(0,o.jsx)(en,{contentStyle:{backgroundColor:"rgba(255, 255, 255, 0.95)",border:"1px solid #e0e0e0",borderRadius:"4px",boxShadow:"0 2px 5px rgba(0,0,0,0.1)"},itemStyle:{padding:0},labelStyle:{fontWeight:"bold",marginBottom:"5px"}}),(0,o.jsx)(h$,{type:"monotone",dataKey:"sent",name:"Applications",stroke:"#4F46E5",strokeWidth:2,dot:{stroke:"#4F46E5",strokeWidth:2,r:4,fill:"white"},activeDot:{r:6,stroke:"#4F46E5",strokeWidth:2,fill:"#4F46E5"}}),(0,o.jsx)(h$,{type:"monotone",dataKey:"interviews",name:"Interviews",stroke:"#10B981",strokeWidth:2,dot:{stroke:"#10B981",strokeWidth:2,r:4,fill:"white"},activeDot:{r:6,stroke:"#10B981",strokeWidth:2,fill:"#10B981"}}),(0,o.jsx)(h$,{type:"monotone",dataKey:"approved",name:"Approved",stroke:"#F59E0B",strokeWidth:2,dot:{stroke:"#F59E0B",strokeWidth:2,r:4,fill:"white"},activeDot:{r:6,stroke:"#F59E0B",strokeWidth:2,fill:"#F59E0B"}}),(0,o.jsx)(h$,{type:"monotone",dataKey:"rejected",name:"Rejected",stroke:"#8B5CF6",strokeWidth:2,dot:{stroke:"#8B5CF6",strokeWidth:2,r:4,fill:"white"},activeDot:{r:6,stroke:"#8B5CF6",strokeWidth:2,fill:"#8B5CF6"}}),(0,o.jsx)(h$,{type:"monotone",dataKey:"pending",name:"Pending",stroke:"#9CA3AF",strokeWidth:2,dot:{stroke:"#9CA3AF",strokeWidth:2,r:4,fill:"white"},activeDot:{r:6,stroke:"#9CA3AF",strokeWidth:2,fill:"#9CA3AF"}})]})})})]}),(0,o.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[(0,o.jsx)("div",{className:"flex justify-between items-center mb-6",children:(0,o.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"Recent Applications"})}),(0,o.jsx)("div",{className:"overflow-x-auto",children:(0,o.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,o.jsx)("thead",{className:"bg-gray-50",children:(0,o.jsxs)("tr",{children:[(0,o.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Student"}),(0,o.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Position"}),(0,o.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Company"}),(0,o.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,o.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"})]})}),(0,o.jsxs)("tbody",{className:"bg-white divide-y divide-gray-200",children:[(0,o.jsxs)("tr",{children:[(0,o.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("div",{className:"h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-700 font-medium",children:"JS"}),(0,o.jsxs)("div",{className:"ml-4",children:[(0,o.jsx)("div",{className:"text-sm font-medium text-gray-900",children:"John Smith"}),(0,o.jsx)("div",{className:"text-sm text-gray-500",children:"Computer Science"})]})]})}),(0,o.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,o.jsx)("div",{className:"text-sm text-gray-900",children:"Software Engineer"})}),(0,o.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,o.jsx)("div",{className:"text-sm text-gray-900",children:"TechCorp Inc."})}),(0,o.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,o.jsx)("div",{className:"text-sm text-gray-900",children:"May 16, 2024"})}),(0,o.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,o.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800",children:"Interview"})})]}),(0,o.jsxs)("tr",{children:[(0,o.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("div",{className:"h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-700 font-medium",children:"AJ"}),(0,o.jsxs)("div",{className:"ml-4",children:[(0,o.jsx)("div",{className:"text-sm font-medium text-gray-900",children:"Amy Johnson"}),(0,o.jsx)("div",{className:"text-sm text-gray-500",children:"Business Admin"})]})]})}),(0,o.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,o.jsx)("div",{className:"text-sm text-gray-900",children:"Marketing Analyst"})}),(0,o.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,o.jsx)("div",{className:"text-sm text-gray-900",children:"Global Marketing Ltd."})}),(0,o.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,o.jsx)("div",{className:"text-sm text-gray-900",children:"May 15, 2024"})}),(0,o.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,o.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800",children:"Approved"})})]}),(0,o.jsxs)("tr",{children:[(0,o.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("div",{className:"h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-700 font-medium",children:"RL"}),(0,o.jsxs)("div",{className:"ml-4",children:[(0,o.jsx)("div",{className:"text-sm font-medium text-gray-900",children:"Robert Lee"}),(0,o.jsx)("div",{className:"text-sm text-gray-500",children:"Electrical Engineering"})]})]})}),(0,o.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,o.jsx)("div",{className:"text-sm text-gray-900",children:"Hardware Engineer"})}),(0,o.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,o.jsx)("div",{className:"text-sm text-gray-900",children:"Tech Innovations"})}),(0,o.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,o.jsx)("div",{className:"text-sm text-gray-900",children:"May 14, 2024"})}),(0,o.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,o.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800",children:"Pending"})})]})]})]})})]})]})}},54765:(t,e,r)=>{var n=r(67554),o=r(32269);t.exports=function(t,e){var r=-1,i=o(t)?Array(t.length):[];return n(t,function(t,n,o){i[++r]=e(t,n,o)}),i}},55048:t=>{t.exports=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}},56506:(t,e,r)=>{var n=r(32269);t.exports=function(t,e){return function(r,o){if(null==r)return r;if(!n(r))return t(r,o);for(var i=r.length,a=e?i:-1,c=Object(r);(e?a--:++a<i)&&!1!==o(c[a],a,c););return r}}},57797:(t,e,r)=>{var n=r(67009);t.exports=function(t,e){for(var r=t.length;r--;)if(n(t[r][0],e))return r;return -1}},57800:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},58141:(t,e,r)=>{t.exports=r(41547)(Object,"create")},58276:t=>{t.exports=function(t,e){return t.has(e)}},58744:(t,e,r)=>{var n=r(57797);t.exports=function(t,e){var r=this.__data__,o=n(r,t);return o<0?(++this.size,r.push([t,e])):r[o][1]=e,this}},59467:(t,e,r)=>{var n=r(35142),o=r(35163),i=r(40542),a=r(38428),c=r(69619),u=r(46436);t.exports=function(t,e,r){e=n(e,t);for(var l=-1,s=e.length,f=!1;++l<s;){var p=u(e[l]);if(!(f=null!=t&&r(t,p)))break;t=t[p]}return f||++l!=s?f:!!(s=null==t?0:t.length)&&c(s)&&a(p,s)&&(i(t)||o(t))}},59774:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t,n){r[++e]=[n,t]}),r}},61320:(t,e,r)=>{var n=r(8336);t.exports=function(t){return n(this,t).has(t)}},61548:(t,e,r)=>{var n=r(5231),o=r(52823),i=r(55048),a=r(12290),c=/^\[object .+?Constructor\]$/,u=Object.prototype,l=Function.prototype.toString,s=u.hasOwnProperty,f=RegExp("^"+l.call(s).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");t.exports=function(t){return!(!i(t)||o(t))&&(n(t)?f:c).test(a(t))}},61837:(t,e,r)=>{var n=r(21367),o=r(22),i=r(54765),a=r(40542);t.exports=function(t,e){return(a(t)?n:i)(t,o(e,3))}},63033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63866:(t,e,r)=>{var n=r(29395),o=r(40542),i=r(27467);t.exports=function(t){return"string"==typeof t||!o(t)&&i(t)&&"[object String]"==n(t)}},63979:(t,e,r)=>{var n=r(52599),o=r(6330),i=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols;t.exports=a?function(t){return null==t?[]:n(a(t=Object(t)),function(e){return i.call(t,e)})}:o},65662:t=>{t.exports=function(t,e){return function(r){return t(e(r))}}},65727:(t,e,r)=>{var n=r(81957);t.exports=function(t,e,r){for(var o=-1,i=t.criteria,a=e.criteria,c=i.length,u=r.length;++o<c;){var l=n(i[o],a[o]);if(l){if(o>=u)return l;return l*("desc"==r[o]?-1:1)}}return t.index-e.index}},65932:(t,e,r)=>{t.exports=r(65662)(Object.getPrototypeOf,Object)},65984:t=>{t.exports=function(t){return function(e,r,n){for(var o=-1,i=Object(e),a=n(e),c=a.length;c--;){var u=a[t?c:++o];if(!1===r(i[u],u,i))break}return e}}},66354:(t,e,r)=>{var n=r(85244),o=Math.max;t.exports=function(t,e,r){return e=o(void 0===e?t.length-1:e,0),function(){for(var i=arguments,a=-1,c=o(i.length-e,0),u=Array(c);++a<c;)u[a]=i[e+a];a=-1;for(var l=Array(e+1);++a<e;)l[a]=i[a];return l[e]=r(u),n(t,this,l)}}},66400:t=>{var e=Date.now;t.exports=function(t){var r=0,n=0;return function(){var o=e(),i=16-(o-n);if(n=o,i>0){if(++r>=800)return arguments[0]}else r=0;return t.apply(void 0,arguments)}}},66713:(t,e,r)=>{var n=r(3105),o=r(34117),i=r(48385);t.exports=function(t){return o(t)?i(t):n(t)}},66837:(t,e,r)=>{var n=r(58141);t.exports=function(){this.__data__=n?n(null):{},this.size=0}},66930:(t,e,r)=>{var n=r(27669),o=r(28837),i=r(94388),a=r(35800),c=r(58744);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=c,t.exports=u},66992:(t,e)=>{"use strict";var r,n=Symbol.for("react.element"),o=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),u=Symbol.for("react.provider"),l=Symbol.for("react.context"),s=Symbol.for("react.server_context"),f=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),h=Symbol.for("react.suspense_list"),d=Symbol.for("react.memo"),y=Symbol.for("react.lazy");Symbol.for("react.offscreen");Symbol.for("react.module.reference"),e.isFragment=function(t){return function(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case n:switch(t=t.type){case i:case c:case a:case p:case h:return t;default:switch(t=t&&t.$$typeof){case s:case l:case f:case y:case d:case u:return t;default:return e}}case o:return e}}}(t)===i}},67009:t=>{t.exports=function(t,e){return t===e||t!=t&&e!=e}},67200:(t,e,r)=>{var n=r(66930),o=r(37575),i=r(75411),a=r(34746),c=r(25118),u=r(30854);function l(t){var e=this.__data__=new n(t);this.size=e.size}l.prototype.clear=o,l.prototype.delete=i,l.prototype.get=a,l.prototype.has=c,l.prototype.set=u,t.exports=l},67367:(t,e,r)=>{var n=r(99525),o=r(22),i=r(75847),a=r(40542),c=r(7383);t.exports=function(t,e,r){var u=a(t)?n:i;return r&&c(t,e,r)&&(e=void 0),u(t,o(e,3))}},67554:(t,e,r)=>{var n=r(99114);t.exports=r(56506)(n)},67619:(t,e,r)=>{var n=r(40542),o=r(49227),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.exports=function(t,e){if(n(t))return!1;var r=typeof t;return!!("number"==r||"symbol"==r||"boolean"==r||null==t||o(t))||a.test(t)||!i.test(t)||null!=e&&t in Object(e)}},69433:(t,e,r)=>{t.exports=r(5566)("toUpperCase")},69619:t=>{t.exports=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=0x1fffffffffffff}},69691:(t,e,r)=>{var n=r(41157),o=r(99114),i=r(22);t.exports=function(t,e){var r={};return e=i(e,3),o(t,function(t,o,i){n(r,o,e(t,o,i))}),r}},70151:(t,e,r)=>{var n=r(85718);t.exports=function(){return n.Date.now()}},70222:(t,e,r)=>{var n=r(79474),o=Object.prototype,i=o.hasOwnProperty,a=o.toString,c=n?n.toStringTag:void 0;t.exports=function(t){var e=i.call(t,c),r=t[c];try{t[c]=void 0;var n=!0}catch(t){}var o=a.call(t);return n&&(e?t[c]=r:delete t[c]),o}},70440:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>o});var n=r(31658);let o=async t=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await t.params,"favicon.ico")+""}]},71960:t=>{t.exports=function(t,e,r){for(var n=-1,o=null==t?0:t.length;++n<o;)if(r(e,t[n]))return!0;return!1}},71967:(t,e,r)=>{var n=r(15871);t.exports=function(t,e){return n(t,e)}},74610:t=>{t.exports=function(t,e,r){for(var n=r-1,o=t.length;++n<o;)if(t[n]===e)return n;return -1}},75254:(t,e,r)=>{var n=r(78418),o=r(93311),i=r(41132);t.exports=function(t){var e=o(t);return 1==e.length&&e[0][2]?i(e[0][0],e[0][1]):function(r){return r===t||n(r,t,e)}}},75411:t=>{t.exports=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r}},75847:(t,e,r)=>{var n=r(67554);t.exports=function(t,e){var r;return n(t,function(t,n,o){return!(r=e(t,n,o))}),!!r}},77822:(t,e,r)=>{var n=r(93490);t.exports=function(t){return n(t)&&t!=+t}},77834:t=>{var e=Object.prototype;t.exports=function(t){var r=t&&t.constructor;return t===("function"==typeof r&&r.prototype||e)}},78418:(t,e,r)=>{var n=r(67200),o=r(15871);t.exports=function(t,e,r,i){var a=r.length,c=a,u=!i;if(null==t)return!c;for(t=Object(t);a--;){var l=r[a];if(u&&l[2]?l[1]!==t[l[0]]:!(l[0]in t))return!1}for(;++a<c;){var s=(l=r[a])[0],f=t[s],p=l[1];if(u&&l[2]){if(void 0===f&&!(s in t))return!1}else{var h=new n;if(i)var d=i(f,p,s,t,e,h);if(!(void 0===d?o(p,f,3,i,h):d))return!1}}return!0}},79474:(t,e,r)=>{t.exports=r(85718).Symbol},79551:t=>{"use strict";t.exports=require("url")},80195:(t,e,r)=>{var n=r(79474),o=r(21367),i=r(40542),a=r(49227),c=1/0,u=n?n.prototype:void 0,l=u?u.toString:void 0;t.exports=function t(e){if("string"==typeof e)return e;if(i(e))return o(e,t)+"";if(a(e))return l?l.call(e):"";var r=e+"";return"0"==r&&1/e==-c?"-0":r}},80329:(t,e,r)=>{t=r.nmd(t);var n=r(85718),o=r(1944),i=e&&!e.nodeType&&e,a=i&&t&&!t.nodeType&&t,c=a&&a.exports===i?n.Buffer:void 0,u=c?c.isBuffer:void 0;t.exports=u||o},80458:(t,e,r)=>{var n=r(29395),o=r(69619),i=r(27467),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,t.exports=function(t){return i(t)&&o(t.length)&&!!a[n(t)]}},80704:(t,e,r)=>{var n=r(96678);t.exports=function(t,e){return!!(null==t?0:t.length)&&n(t,e,0)>-1}},81172:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});var n=(0,r(6445).A)("outline","help","IconHelp",[["path",{d:"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0",key:"svg-0"}],["path",{d:"M12 17l0 .01",key:"svg-1"}],["path",{d:"M12 13.5a1.5 1.5 0 0 1 1 -1.5a2.6 2.6 0 1 0 -3 -4",key:"svg-2"}]])},81488:t=>{t.exports=function(t,e){return null!=t&&e in Object(t)}},81957:(t,e,r)=>{var n=r(49227);t.exports=function(t,e){if(t!==e){var r=void 0!==t,o=null===t,i=t==t,a=n(t),c=void 0!==e,u=null===e,l=e==e,s=n(e);if(!u&&!s&&!a&&t>e||a&&c&&l&&!u&&!s||o&&c&&l||!r&&l||!i)return 1;if(!o&&!a&&!s&&t<e||s&&r&&i&&!o&&!a||u&&r&&i||!c&&i||!l)return -1}return 0}},82038:(t,e,r)=>{var n=r(34821),o=r(35163),i=r(40542),a=r(80329),c=r(38428),u=r(10090),l=Object.prototype.hasOwnProperty;t.exports=function(t,e){var r=i(t),s=!r&&o(t),f=!r&&!s&&a(t),p=!r&&!s&&!f&&u(t),h=r||s||f||p,d=h?n(t.length,String):[],y=d.length;for(var v in t)(e||l.call(t,v))&&!(h&&("length"==v||f&&("offset"==v||"parent"==v)||p&&("buffer"==v||"byteLength"==v||"byteOffset"==v)||c(v,y)))&&d.push(v);return d}},84031:(t,e,r)=>{"use strict";var n=r(34452);function o(){}function i(){}i.resetWarningCache=o,t.exports=function(){function t(t,e,r,o,i,a){if(a!==n){var c=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw c.name="Invariant Violation",c}}function e(){return t}t.isRequired=t;var r={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,elementType:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:i,resetWarningCache:o};return r.PropTypes=r,r}},84261:t=>{t.exports=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=!!e,e}},84482:(t,e,r)=>{var n=r(28977);t.exports=function(t){var e=n(t),r=e%1;return e==e?r?e-r:e:0}},84713:t=>{var e=Object.prototype.toString;t.exports=function(t){return e.call(t)}},85244:t=>{t.exports=function(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}},85406:(t,e,r)=>{t.exports=r(85718)["__core-js_shared__"]},85450:(t,e,r)=>{var n=r(79474),o=r(35163),i=r(40542),a=n?n.isConcatSpreadable:void 0;t.exports=function(t){return i(t)||o(t)||!!(a&&t&&t[a])}},85718:(t,e,r)=>{var n=r(10663),o="object"==typeof self&&self&&self.Object===Object&&self;t.exports=n||o||Function("return this")()},85745:(t,e,r)=>{var n=r(86451);t.exports=function(t){var e=n(t,function(t){return 500===r.size&&r.clear(),t}),r=e.cache;return e}},85938:(t,e,r)=>{var n=r(42205),o=r(17518),i=r(46229),a=r(7383);t.exports=i(function(t,e){if(null==t)return[];var r=e.length;return r>1&&a(t,e[0],e[1])?e=[]:r>2&&a(e[0],e[1],e[2])&&(e=[e[0]]),o(t,n(e,1),[])})},86451:(t,e,r)=>{var n=r(95746);function o(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw TypeError("Expected a function");var r=function(){var n=arguments,o=e?e.apply(this,n):n[0],i=r.cache;if(i.has(o))return i.get(o);var a=t.apply(this,n);return r.cache=i.set(o,a)||i,a};return r.cache=new(o.Cache||n),r}o.Cache=n,t.exports=o},87270:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(!e(t[r],r,t))return!1;return!0}},87321:(t,e,r)=>{var n=r(98798),o=r(7383),i=r(28977);t.exports=function(t){return function(e,r,a){return a&&"number"!=typeof a&&o(e,r,a)&&(r=a=void 0),e=i(e),void 0===r?(r=e,e=0):r=i(r),a=void 0===a?e<r?1:-1:i(a),n(e,r,a,t)}}},87506:(t,e,r)=>{var n=r(66837),o=r(84261),i=r(89492),a=r(90200),c=r(39672);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=c,t.exports=u},87955:(t,e,r)=>{t.exports=r(84031)()},89167:(t,e,r)=>{t.exports=r(41547)(r(85718),"DataView")},89185:t=>{t.exports=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this}},89492:(t,e,r)=>{var n=r(58141),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;if(n){var r=e[t];return"__lodash_hash_undefined__"===r?void 0:r}return o.call(e,t)?e[t]:void 0}},89605:(t,e,r)=>{t.exports=r(65662)(Object.keys,Object)},89624:t=>{t.exports=function(t){return function(e){return t(e)}}},90200:(t,e,r)=>{var n=r(58141),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;return n?void 0!==e[t]:o.call(e,t)}},90453:(t,e,r)=>{var n=r(2984),o=r(99180),i=r(48169);t.exports=function(t){return t&&t.length?n(t,i,o):void 0}},90851:t=>{t.exports=function(t,e){return null==t?void 0:t[e]}},91290:t=>{t.exports=function(t,e,r,n){for(var o=t.length,i=r+(n?1:-1);n?i--:++i<o;)if(e(t[i],i,t))return i;return -1}},91928:(t,e,r)=>{var n=r(41547);t.exports=function(){try{var t=n(Object,"defineProperty");return t({},"",{}),t}catch(t){}}()},92662:(t,e,r)=>{var n=r(46328),o=r(80704),i=r(71960),a=r(58276),c=r(95308),u=r(2408);t.exports=function(t,e,r){var l=-1,s=o,f=t.length,p=!0,h=[],d=h;if(r)p=!1,s=i;else if(f>=200){var y=e?null:c(t);if(y)return u(y);p=!1,s=a,d=new n}else d=e?[]:h;e:for(;++l<f;){var v=t[l],m=e?e(v):v;if(v=r||0!==v?v:0,p&&m==m){for(var b=d.length;b--;)if(d[b]===m)continue e;e&&d.push(m),h.push(v)}else s(d,m,r)||(d!==h&&d.push(m),h.push(v))}return h}},93311:(t,e,r)=>{var n=r(34883),o=r(7651);t.exports=function(t){for(var e=o(t),r=e.length;r--;){var i=e[r],a=t[i];e[r]=[i,a,n(a)]}return e}},93490:(t,e,r)=>{var n=r(29395),o=r(27467);t.exports=function(t){return"number"==typeof t||o(t)&&"[object Number]"==n(t)}},93780:(t,e,r)=>{"use strict";t.exports=r(66992)},94388:(t,e,r)=>{var n=r(57797);t.exports=function(t){var e=this.__data__,r=n(e,t);return r<0?void 0:e[r][1]}},95308:(t,e,r)=>{var n=r(34772),o=r(36959),i=r(2408);t.exports=n&&1/i(new n([,-0]))[1]==1/0?function(t){return new n(t)}:o},95746:(t,e,r)=>{var n=r(15909),o=r(29205),i=r(29508),a=r(61320),c=r(19976);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=c,t.exports=u},95994:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});var n=(0,r(6445).A)("outline","forms","IconForms",[["path",{d:"M12 3a3 3 0 0 0 -3 3v12a3 3 0 0 0 3 3",key:"svg-0"}],["path",{d:"M6 3a3 3 0 0 1 3 3v12a3 3 0 0 1 -3 3",key:"svg-1"}],["path",{d:"M13 7h7a1 1 0 0 1 1 1v8a1 1 0 0 1 -1 1h-7",key:"svg-2"}],["path",{d:"M5 7h-1a1 1 0 0 0 -1 1v8a1 1 0 0 0 1 1h1",key:"svg-3"}],["path",{d:"M17 12h.01",key:"svg-4"}],["path",{d:"M13 12h.01",key:"svg-5"}]])},96678:(t,e,r)=>{var n=r(91290),o=r(39774),i=r(74610);t.exports=function(t,e,r){return e==e?i(t,e,r):n(t,o,r)}},98451:(t,e,r)=>{var n=r(29395),o=r(27467);t.exports=function(t){return!0===t||!1===t||o(t)&&"[object Boolean]"==n(t)}},98798:t=>{var e=Math.ceil,r=Math.max;t.exports=function(t,n,o,i){for(var a=-1,c=r(e((n-t)/(o||1)),0),u=Array(c);c--;)u[i?c:++a]=t,t+=o;return u}},99114:(t,e,r)=>{var n=r(12344),o=r(7651);t.exports=function(t,e){return t&&n(t,e,o)}},99180:t=>{t.exports=function(t,e){return t>e}},99525:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(e(t[r],r,t))return!0;return!1}}};var e=require("../../../webpack-runtime.js");e.C(t);var r=t=>e(e.s=t),n=e.X(0,[4447,681,1658,2305],()=>r(18108));module.exports=n})();