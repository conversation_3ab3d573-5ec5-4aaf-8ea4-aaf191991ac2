(()=>{var e={};e.id=5940,e.ids=[5940],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8819:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10022:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},10722:(e,t,s)=>{Promise.resolve().then(s.bind(s,11527))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11527:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>b});var a=s(60687),r=s(43210),l=s(24664),n=s(98848),i=s(9535),d=s(37325),o=s(81080),c=s(95994),m=s(80556),x=s(90910),u=s(81172),p=s(20798),h=s(58869),g=s(53411);function b({children:e}){let[t,s]=(0,r.useState)(""),b=[{items:[{title:"Dashboard",href:"/admin/dashboard",icon:(0,a.jsx)(n.A,{})},{title:"Jobs",href:"/admin/jobs",icon:(0,a.jsx)(i.A,{})},{title:"Applications",href:"/admin/applications",icon:(0,a.jsx)(d.A,{})},{title:"Posts",href:"/admin/posts",icon:(0,a.jsx)(p.A,{className:"w-5 h-5"})},{title:"Student Management",href:"/admin/student-management",icon:(0,a.jsx)(h.A,{className:"w-5 h-5"})},{title:"Student Analytics",href:"/admin/analytics",icon:(0,a.jsx)(g.A,{className:"w-5 h-5"})},{title:"Company Management",href:"/admin/companymanagement",icon:(0,a.jsx)(o.A,{})},{title:"Forms",href:"/admin/form",icon:(0,a.jsx)(c.A,{})}]}],y=[{title:"My Profile",href:"/admin/profile",icon:(0,a.jsx)(m.A,{})},{title:"Settings",href:"../settings",icon:(0,a.jsx)(x.A,{})},{title:"Contact Support",href:"/admin/helpandsupport",icon:(0,a.jsx)(u.A,{})}];return(0,a.jsx)("div",{className:"h-screen bg-gray-50 overflow-hidden",children:(0,a.jsxs)("div",{className:"flex h-full",children:[(0,a.jsx)(l.A,{sections:b,bottomItems:y,defaultExpanded:!1,navbarHeight:"0",className:"z-20"}),(0,a.jsx)("div",{className:"flex-1 p-6 ml-20 overflow-y-auto",children:(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-md p-6 min-h-full text-black",children:e})})]})})}},12412:e=>{"use strict";e.exports=require("assert")},13861:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20798:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("megaphone",[["path",{d:"m3 11 18-5v12L3 14v-3z",key:"n962bs"}],["path",{d:"M11.6 16.8a3 3 0 1 1-5.8-1.6",key:"1yl0tm"}]])},21820:e=>{"use strict";e.exports=require("os")},23697:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\layout.jsx","default")},26010:(e,t,s)=>{Promise.resolve().then(s.bind(s,41250))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},33873:e=>{"use strict";e.exports=require("path")},35071:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},37325:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var a=(0,s(6445).A)("outline","clipboard-list","IconClipboardList",[["path",{d:"M9 5h-2a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-12a2 2 0 0 0 -2 -2h-2",key:"svg-0"}],["path",{d:"M9 3m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z",key:"svg-1"}],["path",{d:"M9 12l.01 0",key:"svg-2"}],["path",{d:"M13 12l2 0",key:"svg-3"}],["path",{d:"M9 16l.01 0",key:"svg-4"}],["path",{d:"M13 16l2 0",key:"svg-5"}]])},40228:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41250:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\applications\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\applications\\page.jsx","default")},48173:(e,t,s)=>{Promise.resolve().then(s.bind(s,23697))},48730:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},53411:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57800:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},58138:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var a=s(51060);s(51421);let r=a.A.create({baseURL:"http://127.0.0.1:8000",headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>{let t=localStorage.getItem("access_token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,async e=>{let t=e.config;if(e.response?.status===401&&!t._retry){t._retry=!0;try{let e=localStorage.getItem("refresh_token");if(e){let s=await a.A.post("http://127.0.0.1:8000/api/auth/token/refresh/",{refresh:e});return localStorage.setItem("access_token",s.data.access),t.headers.Authorization=`Bearer ${s.data.access}`,r(t)}}catch(e){console.error("Error refreshing token:",e)}}return Promise.reject(e)});let l=r},60290:(e,t,s)=>{Promise.resolve().then(s.bind(s,83239))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},78122:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},79551:e=>{"use strict";e.exports=require("url")},80418:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var a=s(65239),r=s(48088),l=s(88170),n=s.n(l),i=s(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);s.d(t,d);let o={children:["",{children:["admin",{children:["applications",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,41250)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\applications\\page.jsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,23697)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\layout.jsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,75535)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\layout.js"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\admin\\applications\\page.jsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/applications/page",pathname:"/admin/applications",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},81172:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var a=(0,s(6445).A)("outline","help","IconHelp",[["path",{d:"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0",key:"svg-0"}],["path",{d:"M12 17l0 .01",key:"svg-1"}],["path",{d:"M12 13.5a1.5 1.5 0 0 1 1 -1.5a2.6 2.6 0 1 0 -3 -4",key:"svg-2"}]])},81630:e=>{"use strict";e.exports=require("http")},83239:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>I});var a=s(60687),r=s(43210),l=s(16189),n=s(48730),i=s(13861),d=s(5336),o=s(35071),c=s(62688);let m=(0,c.A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]]);var x=s(31158),u=s(78122),p=s(10022),h=s(40228),g=s(99270),b=s(58138),y=s(11860);function j({filters:e,onFilterChange:t,onClose:s}){let[l,n]=(0,r.useState)(e),i=(e,t)=>{n({...l,[e]:t})};return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Filter Applications"}),(0,a.jsx)("button",{onClick:s,className:"p-2 hover:bg-gray-100 rounded-full",children:(0,a.jsx)(y.A,{className:"w-5 h-5"})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Status"}),(0,a.jsx)("select",{value:l.status,onChange:e=>i("status",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[{value:"ALL",label:"All Statuses"},{value:"APPLIED",label:"Applied"},{value:"UNDER_REVIEW",label:"Under Review"},{value:"SHORTLISTED",label:"Shortlisted"},{value:"REJECTED",label:"Rejected"},{value:"HIRED",label:"Hired"}].map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Company"}),(0,a.jsx)("input",{type:"text",placeholder:"Enter company name",value:l.company,onChange:e=>i("company",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Job Title"}),(0,a.jsx)("input",{type:"text",placeholder:"Enter job title",value:l.job_title,onChange:e=>i("job_title",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Student Name"}),(0,a.jsx)("input",{type:"text",placeholder:"Enter student name",value:l.student_name,onChange:e=>i("student_name",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Applied From"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:"date",value:l.date_from,onChange:e=>i("date_from",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,a.jsx)(h.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Applied To"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:"date",value:l.date_to,onChange:e=>i("date_to",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,a.jsx)(h.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none"})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("button",{onClick:()=>{let e={status:"ALL",company:"",job_title:"",date_from:"",date_to:"",student_name:""};n(e),t(e)},className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"Clear All"}),(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsx)("button",{onClick:s,className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"Cancel"}),(0,a.jsx)("button",{onClick:()=>{t(l),s()},className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Apply Filters"})]})]})]})}var f=s(88233),v=s(86561);function N({status:e}){let t=(()=>{switch(e){case"APPLIED":return{color:"bg-blue-100 text-blue-800 border-blue-200",icon:(0,a.jsx)(n.A,{className:"w-3 h-3"}),label:"Applied"};case"UNDER_REVIEW":return{color:"bg-yellow-100 text-yellow-800 border-yellow-200",icon:(0,a.jsx)(i.A,{className:"w-3 h-3"}),label:"Under Review"};case"SHORTLISTED":return{color:"bg-green-100 text-green-800 border-green-200",icon:(0,a.jsx)(d.A,{className:"w-3 h-3"}),label:"Shortlisted"};case"REJECTED":return{color:"bg-red-100 text-red-800 border-red-200",icon:(0,a.jsx)(o.A,{className:"w-3 h-3"}),label:"Rejected"};case"HIRED":return{color:"bg-emerald-100 text-emerald-800 border-emerald-200",icon:(0,a.jsx)(v.A,{className:"w-3 h-3"}),label:"Hired"};default:return{color:"bg-gray-100 text-gray-800 border-gray-200",icon:(0,a.jsx)(n.A,{className:"w-3 h-3"}),label:e||"Unknown"}}})();return(0,a.jsxs)("span",{className:`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${t.color}`,children:[t.icon,t.label]})}function w({applications:e,onViewApplication:t,onDeleteApplication:s}){let[l,n]=(0,r.useState)(new Set),[d,o]=(0,r.useState)(!1),c=Array.isArray(e)?e:[],m=e=>{let t=new Set(l);t.has(e)?t.delete(e):t.add(e),n(t),o(t.size>0)},x=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return(0,a.jsxs)("div",{className:"overflow-x-auto",children:[d&&(0,a.jsxs)("div",{className:"flex items-center justify-between bg-blue-50 border-b border-blue-200 px-6 py-3",children:[(0,a.jsxs)("span",{className:"text-sm text-blue-700",children:[l.size," application",1!==l.size?"s":""," selected"]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{className:"px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700",children:"Bulk Status Update"}),(0,a.jsx)("button",{className:"px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700",children:"Bulk Delete"})]})]}),(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left",children:(0,a.jsx)("input",{type:"checkbox",checked:l.size===c.length&&c.length>0,onChange:()=>{l.size===c.length?(n(new Set),o(!1)):(n(new Set(c.map(e=>e.id))),o(!0))},className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"})}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Student"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Job Title"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Company"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Applied Date"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:c.map(e=>(0,a.jsxs)("tr",{className:`hover:bg-gray-50 ${l.has(e.id)?"bg-blue-50":""}`,children:[(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsx)("input",{type:"checkbox",checked:l.has(e.id),onChange:()=>m(e.id),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 h-10 w-10",children:(0,a.jsx)("div",{className:"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:e.student_name?.charAt(0)||"U"})})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.student_name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.student_email}),(0,a.jsxs)("div",{className:"text-xs text-gray-400",children:["ID: ",e.student_id]})]})]})}),(0,a.jsxs)("td",{className:"px-6 py-4",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.job_title}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.job_location})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.company_name})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)(N,{status:e.status})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:x(e.applied_at)}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("button",{onClick:()=>t(e),className:"inline-flex items-center px-2 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,a.jsx)(i.A,{className:"w-3 h-3 mr-1"}),"View"]}),(0,a.jsxs)("button",{onClick:()=>s(e.id),className:"inline-flex items-center px-2 py-1 border border-red-300 text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:[(0,a.jsx)(f.A,{className:"w-3 h-3 mr-1"}),"Delete"]})]})})]},e.id))})]})]})}var _=s(51421);function A({onClose:e,filters:t}){let{showSuccess:s,showError:l,handleApiError:n}=(0,_.hN)(),[i,d]=(0,r.useState)("csv"),[o,c]=(0,r.useState)(!1),[m,p]=(0,r.useState)(!0),[h,g]=(0,r.useState)([]),[j,f]=(0,r.useState)(["student_name","student_email","job_title","company_name","status","applied_at"]),v=async()=>{if(0===j.length)return void l("Export Error","Please select at least one column to export.");c(!0);try{let a={format:i,columns:j};t&&(t.job_id&&(a.job_id=parseInt(t.job_id)),t.status&&"ALL"!==t.status&&(a.status=[t.status]),t.date_from&&(a.date_from=t.date_from),t.date_to&&(a.date_to=t.date_to)),console.log("Export request payload:",a);let r=await b.A.post("/api/v1/jobs/applications/export/",a,{responseType:"blob"}),l=r.data,n=r.headers["content-disposition"],d=n?n.split("filename=")[1]?.replace(/"/g,""):`applications_export.${i}`,o=window.URL.createObjectURL(l),c=document.createElement("a");c.href=o,c.download=d,document.body.appendChild(c),c.click(),document.body.removeChild(c),window.URL.revokeObjectURL(o),s("Export Successful",`Your ${i.toUpperCase()} file has been downloaded.`),e()}catch(e){console.error("Export error:",e),n(e,"export")}finally{c(!1)}};return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-screen overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Export Applications"}),(0,a.jsx)("button",{onClick:e,className:"p-2 hover:bg-gray-100 rounded-full",children:(0,a.jsx)(y.A,{className:"w-5 h-5"})})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Export Format"}),(0,a.jsx)("div",{className:"flex gap-3",children:["csv","excel","pdf"].map(e=>(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"radio",name:"format",value:e,checked:i===e,onChange:e=>d(e.target.value),className:"mr-2"}),e.toUpperCase()]},e))})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Select Columns to Export"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Choose which fields to include in your export file."}),m?(0,a.jsxs)("div",{className:"flex items-center justify-center py-8 border rounded-lg",children:[(0,a.jsx)(u.A,{className:"w-5 h-5 text-gray-400 animate-spin mr-2"}),(0,a.jsx)("span",{className:"text-gray-600",children:"Loading available columns..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"space-y-4 max-h-64 overflow-y-auto border rounded-lg p-3",children:["student","job","application","academic","contact","additional"].map(e=>{let t=h.filter(t=>t.category===e);return 0===t.length?null:(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-800 mb-2 border-b border-gray-200 pb-1",children:{student:"Student Information",job:"Job Details",application:"Application Data",academic:"Academic Information",contact:"Contact Information",additional:"Additional Fields"}[e]}),(0,a.jsx)("div",{className:"space-y-1",children:t.map(e=>(0,a.jsxs)("label",{className:"flex items-center space-x-3 p-1 hover:bg-gray-50 rounded cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:j.includes(e.key),onChange:t=>{t.target.checked?f(t=>[...t,e.key]):f(t=>t.filter(t=>t!==e.key))},className:"h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),(0,a.jsx)("span",{className:"text-sm text-gray-700 flex-1",children:e.label}),e.type&&(0,a.jsx)("span",{className:"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded",children:e.type})]},e.key))})]},e)})}),(0,a.jsxs)("div",{className:"mt-3 flex justify-between text-sm text-gray-600",children:[(0,a.jsxs)("span",{children:[j.length," columns selected"]}),(0,a.jsxs)("div",{className:"space-x-2",children:[(0,a.jsx)("button",{type:"button",onClick:()=>f([]),className:"text-gray-600 hover:text-gray-700",children:"Clear All"}),(0,a.jsx)("button",{type:"button",onClick:()=>f(h.map(e=>e.key)),className:"text-blue-600 hover:text-blue-700",children:"Select All"})]})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end gap-3",children:[(0,a.jsx)("button",{onClick:e,className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"Cancel"}),(0,a.jsxs)("button",{onClick:v,disabled:0===j.length||o,className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2",children:[(0,a.jsx)(x.A,{className:"w-4 h-4"}),o?"Exporting...":"Export"]})]})]})]})})}var k=s(8819),S=s(58869),C=s(57800);let E=(0,c.A)("file-check",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"m9 15 2 2 4-4",key:"1grp1n"}]]);function D({application:e,onClose:t,onUpdate:s}){let[l,n]=(0,r.useState)(!1),[i,d]=(0,r.useState)({status:e?.status||"APPLIED",admin_notes:e?.admin_notes||""}),[o,c]=(0,r.useState)(null),[m,u]=(0,r.useState)(!0);if(!e)return null;let g=o||e,b=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),j=(e,t)=>(0,a.jsxs)("div",{className:"flex justify-between items-start text-sm py-2 border-b border-gray-100 last:border-b-0",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("span",{className:"font-medium text-gray-700",children:[e.label,e.required&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),e.type&&(0,a.jsxs)("span",{className:"text-xs text-gray-500 ml-2",children:["(",e.type,")"]})]}),(0,a.jsx)("div",{className:"flex-1 text-right",children:(()=>{if(!t&&0!==t)return"Not provided";switch(e.type){case"file":if("string"==typeof t&&t.startsWith("/media/"))return(0,a.jsxs)("a",{href:t,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-800 flex items-center gap-1",children:[(0,a.jsx)(x.A,{className:"w-4 h-4"}),"View File"]});return t||"No file uploaded";case"multiple_choice":return t||"Not selected";default:return t||"Not provided"}})()})]},e.id||e.label);return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-screen overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,a.jsxs)("h2",{className:"text-xl font-semibold text-gray-900",children:["Application Details - ",g.student_name]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[l?(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{onClick:()=>n(!1),className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"Cancel"}),(0,a.jsxs)("button",{onClick:()=>{console.log("Saving application:",i),alert("Save functionality will be implemented in the next phase"),n(!1),s&&s()},className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center gap-2",children:[(0,a.jsx)(k.A,{className:"w-4 h-4"}),"Save"]})]}):(0,a.jsx)("button",{onClick:()=>n(!0),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Edit"}),(0,a.jsx)("button",{onClick:t,className:"p-2 hover:bg-gray-100 rounded-full",children:(0,a.jsx)(y.A,{className:"w-5 h-5"})})]})]}),(0,a.jsx)("div",{className:"p-6",children:m?(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading application details..."})]})}):(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,a.jsx)(S.A,{className:"w-5 h-5 text-gray-600"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Student Information"})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Name"}),(0,a.jsx)("p",{className:"text-gray-900",children:g.student_name})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Email"}),(0,a.jsx)("p",{className:"text-gray-900",children:g.student_email})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Student ID"}),(0,a.jsx)("p",{className:"text-gray-900",children:g.student_id})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Branch"}),(0,a.jsx)("p",{className:"text-gray-900",children:g.branch})]})]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,a.jsx)(C.A,{className:"w-5 h-5 text-gray-600"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Job Information"})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Job Title"}),(0,a.jsx)("p",{className:"text-gray-900",children:g.job_title})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Company"}),(0,a.jsx)("p",{className:"text-gray-900",children:g.company_name})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Location"}),(0,a.jsx)("p",{className:"text-gray-900",children:g.job_location})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,a.jsx)(h.A,{className:"w-5 h-5 text-gray-600"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Application Details"})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Applied Date"}),(0,a.jsx)("p",{className:"text-gray-900",children:b(g.applied_at)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Status"}),l?(0,a.jsxs)("select",{value:i.status,onChange:e=>d({...i,status:e.target.value}),className:"mt-1 w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"APPLIED",children:"Applied"}),(0,a.jsx)("option",{value:"UNDER_REVIEW",children:"Under Review"}),(0,a.jsx)("option",{value:"SHORTLISTED",children:"Shortlisted"}),(0,a.jsx)("option",{value:"REJECTED",children:"Rejected"}),(0,a.jsx)("option",{value:"HIRED",children:"Hired"})]}):(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)(N,{status:g.status})})]}),g.cover_letter&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Cover Letter"}),(0,a.jsx)("p",{className:"text-gray-900 text-sm bg-white p-3 rounded border",children:g.cover_letter})]})]})]}),g.job_additional_fields&&g.job_additional_fields.length>0&&g.custom_responses&&(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,a.jsx)(E,{className:"w-5 h-5 text-gray-600"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Additional Information"})]}),(0,a.jsx)("div",{className:"space-y-1",children:g.job_additional_fields.map(e=>{let t=`field_${e.id}`,s=g.custom_responses[t]||g.custom_responses[e.id]||g.custom_responses[e.label];return j(e,s)})})]}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,a.jsx)(p.A,{className:"w-5 h-5 text-gray-600"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Admin Notes"})]}),l?(0,a.jsx)("textarea",{value:i.admin_notes,onChange:e=>d({...i,admin_notes:e.target.value}),placeholder:"Add internal notes about this application...",rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}):(0,a.jsx)("p",{className:"text-gray-900",children:g.admin_notes||"No admin notes yet."})]}),g.status_history&&g.status_history.length>0&&(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Status History"}),(0,a.jsx)("div",{className:"space-y-2",children:g.status_history.map((e,t)=>(0,a.jsxs)("div",{className:"text-sm bg-white p-2 rounded",children:[(0,a.jsx)("span",{className:"font-medium",children:e.from_status})," → ",(0,a.jsx)("span",{className:"font-medium",children:e.to_status}),(0,a.jsx)("span",{className:"text-gray-500 ml-2",children:b(e.changed_at)})]},t))})]})]})]})})]})})}function M(){let e=(0,l.useSearchParams)().get("job_id"),[t,s]=(0,r.useState)([]),[i,c]=(0,r.useState)([]),[y,f]=(0,r.useState)({total:0,by_status:[],recent:0}),[v,N]=(0,r.useState)(!0),[_,k]=(0,r.useState)(null),[S,C]=(0,r.useState)(null),[E,M]=(0,r.useState)(!1),[I,P]=(0,r.useState)(!1),[R,L]=(0,r.useState)(!1),[T,U]=(0,r.useState)(""),[q,z]=(0,r.useState)({status:"ALL",company:"",job_title:"",date_from:"",date_to:"",student_name:""}),[H,V]=(0,r.useState)(1),[F,O]=(0,r.useState)(1),[J]=(0,r.useState)(20),$=async()=>{try{N(!0);try{let t=await function(e={}){let t=new URLSearchParams;e.page&&t.append("page",e.page),e.page_size&&t.append("page_size",e.page_size),e.status&&"ALL"!==e.status&&t.append("status",e.status),e.job_id&&t.append("job_id",e.job_id),e.company&&t.append("company_name__icontains",e.company),e.job_title&&t.append("job__title__icontains",e.job_title),e.student_name&&t.append("student_name__icontains",e.student_name),e.date_from&&t.append("applied_at__gte",e.date_from),e.date_to&&t.append("applied_at__lte",e.date_to);let s=t.toString(),a=`/api/v1/jobs/applications/${s?`?${s}`:""}`;return b.A.get(a)}({page:H,page_size:J,...e&&{job_id:e},...q});if(console.log("Real API Response:",t),t&&t.data){t.data.results&&t.data.results.results?(s(t.data.results.results||[]),f(t.data.results.stats||{total:0,by_status:[],recent:0})):(s(t.data.results||[]),f(t.data.stats||{total:0,by_status:[],recent:0}));let e=t.data.count||t.data.results?.stats?.total||0;O(Math.ceil(e/J));return}}catch(e){console.log("API Error, falling back to mock data:",e)}let t=[{id:1,job_id:25,student_name:"John Doe",student_email:"<EMAIL>",student_id:"CS2021001",branch:"Computer Science",job_title:"Software Engineer",company_name:"TechCorp Inc",job_location:"San Francisco, CA",status:"APPLIED",applied_at:"2024-01-15T10:30:00Z",cover_letter:"I am very interested in this position...",admin_notes:"",status_history:[]},{id:2,job_id:25,student_name:"Jane Smith",student_email:"<EMAIL>",student_id:"CS2021002",branch:"Computer Science",job_title:"Software Engineer",company_name:"TechCorp Inc",job_location:"San Francisco, CA",status:"UNDER_REVIEW",applied_at:"2024-01-14T14:20:00Z",cover_letter:"My experience in machine learning...",admin_notes:"Strong candidate",status_history:[]},{id:3,job_id:26,student_name:"Mike Johnson",student_email:"<EMAIL>",student_id:"CS2021003",branch:"Computer Science",job_title:"Frontend Developer",company_name:"WebSolutions",job_location:"Austin, TX",status:"SHORTLISTED",applied_at:"2024-01-13T09:15:00Z",cover_letter:"I have extensive experience in React...",admin_notes:"Excellent portfolio",status_history:[]}];e&&(t=t.filter(t=>t.job_id===parseInt(e)));let a={total:t.length,by_status:[{status:"APPLIED",count:t.filter(e=>"APPLIED"===e.status).length},{status:"UNDER_REVIEW",count:t.filter(e=>"UNDER_REVIEW"===e.status).length},{status:"SHORTLISTED",count:t.filter(e=>"SHORTLISTED"===e.status).length}],recent:t.length};console.log("Using Mock Applications:",t),console.log("Mock Stats:",a),console.log("Job ID from URL:",e),s(t),f(a),O(1)}catch(e){console.error("Failed to load applications:",e),k("Failed to load applications")}finally{N(!1)}},W=async e=>{if(confirm("Are you sure you want to delete this application? This action cannot be undone."))try{s(t=>t.filter(t=>t.id!==e)),alert("Application deleted successfully (mock action)")}catch(e){console.error("Failed to delete application:",e),alert("Failed to delete application")}},G=T?Array.isArray(i)?i:[]:Array.isArray(t)?t:[];return console.log("Current state:",{searchTerm:T,applications:t?.length||0,filteredApplications:i?.length||0,displayApplications:G?.length||0,stats:y}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Applications Management"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:e?`Applications for Job ID: ${e}`:"Track and manage all job applications from students"}),e&&(0,a.jsx)("div",{className:"mt-2",children:(0,a.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:["Filtered by Job ID: ",e]})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)("button",{onClick:()=>P(!I),className:`flex items-center gap-2 px-4 py-2 rounded-lg border transition-colors ${I?"bg-blue-50 border-blue-200 text-blue-700":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50"}`,children:[(0,a.jsx)(m,{className:"w-4 h-4"}),"Filters"]}),(0,a.jsxs)("button",{onClick:()=>M(!0),className:"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,a.jsx)(x.A,{className:"w-4 h-4"}),"Export"]}),(0,a.jsxs)("button",{onClick:$,disabled:v,className:"flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors disabled:opacity-50",children:[(0,a.jsx)(u.A,{className:`w-4 h-4 ${v?"animate-spin":""}`}),"Refresh"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Applications"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:y.total})]}),(0,a.jsx)("div",{className:"p-3 bg-blue-100 rounded-full",children:(0,a.jsx)(p.A,{className:"w-6 h-6 text-blue-600"})})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Pending Review"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:y.by_status?.find(e=>"APPLIED"===e.status)?.count||0})]}),(0,a.jsx)("div",{className:"p-3 bg-yellow-100 rounded-full",children:(0,a.jsx)(n.A,{className:"w-6 h-6 text-yellow-600"})})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Shortlisted"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:y.by_status?.find(e=>"SHORTLISTED"===e.status)?.count||0})]}),(0,a.jsx)("div",{className:"p-3 bg-green-100 rounded-full",children:(0,a.jsx)(d.A,{className:"w-6 h-6 text-green-600"})})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Recent (7 days)"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:y.recent})]}),(0,a.jsx)("div",{className:"p-3 bg-purple-100 rounded-full",children:(0,a.jsx)(h.A,{className:"w-6 h-6 text-purple-600"})})]})})]}),I&&(0,a.jsx)(j,{filters:q,onFilterChange:e=>{z(e),V(1)},onClose:()=>P(!1)}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-4",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,a.jsx)("input",{type:"text",placeholder:"Search by student name, email, job title, company, or student ID...",value:T,onChange:e=>U(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[v?(0,a.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,a.jsx)(u.A,{className:"w-8 h-8 text-gray-400 animate-spin"}),(0,a.jsx)("span",{className:"ml-3 text-gray-600",children:"Loading applications..."})]}):_?(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(o.A,{className:"w-12 h-12 text-red-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-900 font-medium",children:"Error loading applications"}),(0,a.jsx)("p",{className:"text-gray-600",children:_}),(0,a.jsx)("button",{onClick:$,className:"mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Try Again"})]})}):0===G.length?(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(p.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-900 font-medium",children:"No applications found"}),(0,a.jsx)("p",{className:"text-gray-600",children:T?"Try adjusting your search criteria":"No applications have been submitted yet"})]})}):(0,a.jsx)(w,{applications:G,onViewApplication:e=>{C(e),L(!0)},onDeleteApplication:W}),F>1&&(0,a.jsxs)("div",{className:"flex items-center justify-between px-6 py-4 border-t border-gray-200",children:[(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["Page ",H," of ",F]})}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("button",{onClick:()=>V(Math.max(1,H-1)),disabled:1===H,className:"px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,a.jsx)("button",{onClick:()=>V(Math.min(F,H+1)),disabled:H===F,className:"px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]})]})]}),E&&(0,a.jsx)(A,{onClose:()=>M(!1),filters:q}),R&&S&&(0,a.jsx)(D,{application:S,onClose:()=>{L(!1),C(null)},onUpdate:$})]})}function I(){return(0,a.jsx)(r.Suspense,{fallback:(0,a.jsx)("div",{children:"Loading..."}),children:(0,a.jsx)(M,{})})}},83997:e=>{"use strict";e.exports=require("tty")},86561:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},88233:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},94735:e=>{"use strict";e.exports=require("events")},95994:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var a=(0,s(6445).A)("outline","forms","IconForms",[["path",{d:"M12 3a3 3 0 0 0 -3 3v12a3 3 0 0 0 3 3",key:"svg-0"}],["path",{d:"M6 3a3 3 0 0 1 3 3v12a3 3 0 0 1 -3 3",key:"svg-1"}],["path",{d:"M13 7h7a1 1 0 0 1 1 1v8a1 1 0 0 1 -1 1h-7",key:"svg-2"}],["path",{d:"M5 7h-1a1 1 0 0 0 -1 1v8a1 1 0 0 0 1 1h1",key:"svg-3"}],["path",{d:"M17 12h.01",key:"svg-4"}],["path",{d:"M13 12h.01",key:"svg-5"}]])},99270:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4447,681,1658,1060,2305],()=>s(80418));module.exports=a})();