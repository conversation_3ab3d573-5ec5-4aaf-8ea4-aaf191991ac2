{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/data/jobsData.js"], "sourcesContent": ["// Centralized data source for jobs, companies, and applications\r\n// This file will serve as the single source of truth for all job-related data\r\n\r\nimport { listCompanies, getCompany, transformCompanyData } from '../api/companies';\r\n\r\n// Static fallback data\r\nexport const companies = [\r\n  {\r\n    id: 1,\r\n    name: \"TechCorp Inc\",\r\n    description: \"Leading technology solutions provider\",\r\n    industry: \"Technology\",\r\n    size: \"500-1000\",\r\n    founded: \"2010\",\r\n    website: \"https://techcorp.com\"\r\n  },\r\n  {\r\n    id: 2,\r\n    name: \"DataCorp\",\r\n    description: \"Data analytics and insights company\",\r\n    industry: \"Data Analytics\",\r\n    size: \"100-500\",\r\n    founded: \"2015\",\r\n    website: \"https://datacorp.com\"\r\n  }\r\n];\r\n\r\nexport const jobPostings = [\r\n  {\r\n    id: 25,\r\n    title: \"Software Engineer\",\r\n    company_id: 1,\r\n    type: \"FULL_TIME\",\r\n    is_active: true,\r\n    is_featured: false,\r\n    remote_eligible: true,\r\n    location: \"San Francisco, CA\"\r\n  },\r\n  {\r\n    id: 26,\r\n    title: \"Data Scientist\",\r\n    company_id: 2,\r\n    type: \"FULL_TIME\",\r\n    is_active: true,\r\n    is_featured: true,\r\n    remote_eligible: false,\r\n    location: \"New York, NY\"\r\n  }\r\n];\r\n\r\nexport const studentApplications = [\r\n  {\r\n    id: 1,\r\n    job_id: 25,\r\n    title: \"Software Engineer\",\r\n    company: \"TechCorp Inc\",\r\n    status: \"APPLIED\",\r\n    application_deadline: \"2024-05-30T23:59:59Z\"\r\n  },\r\n  {\r\n    id: 2,\r\n    job_id: 26,\r\n    title: \"Data Scientist\",\r\n    company: \"DataCorp\",\r\n    status: \"INTERVIEW SCHEDULED\",\r\n    application_deadline: \"2024-06-15T23:59:59Z\"\r\n  }\r\n];\r\n\r\n// Function to fetch companies from the API\r\nexport const fetchCompanies = async (params = {}) => {\r\n  try {\r\n    // Add cache busting parameter\r\n    const fetchParams = { \r\n      ...params, \r\n      _t: new Date().getTime() \r\n    };\r\n    \r\n    console.log('Fetching companies with cache busting...');\r\n    const response = await listCompanies(fetchParams);\r\n    \r\n    // Handle response format consistently\r\n    let companiesData = [];\r\n    if (response.data && Array.isArray(response.data)) {\r\n      companiesData = response.data;\r\n    } else if (response.data && response.data.results && Array.isArray(response.data.results)) {\r\n      companiesData = response.data.results;\r\n    } else if (response.data && response.data.data && Array.isArray(response.data.data)) {\r\n      companiesData = response.data.data;\r\n    }\r\n    \r\n    // Transform the data to match our frontend structure\r\n    const transformedData = companiesData.map(transformCompanyData);\r\n    \r\n    console.log(`Fetched ${transformedData.length} companies from API`);\r\n    \r\n    // Only fall back to static data if we got nothing from the API\r\n    if (transformedData.length === 0) {\r\n      console.warn('API returned empty companies array, using static data');\r\n      return companies;\r\n    }\r\n    \r\n    return transformedData;\r\n  } catch (error) {\r\n    console.error('Error fetching companies:', error);\r\n    \r\n    // Try once more with a different endpoint format\r\n    try {\r\n      console.log('Trying alternate endpoint format...');\r\n      const altResponse = await fetch('/api/v1/college/default-college/companies/');\r\n      if (altResponse.ok) {\r\n        const data = await altResponse.json();\r\n        const altData = Array.isArray(data) ? data : (data.data || data.results || []);\r\n        if (altData.length > 0) {\r\n          console.log('Successfully retrieved companies from alternate endpoint');\r\n          return altData.map(transformCompanyData);\r\n        }\r\n      }\r\n    } catch (altError) {\r\n      console.error('Alternate endpoint also failed:', altError);\r\n    }\r\n    \r\n    // Return static data as final fallback\r\n    return companies;\r\n  }\r\n};\r\n\r\n// Function to get a company by ID\r\nexport const getCompanyById = async (id) => {\r\n  try {\r\n    // First try to get from API\r\n    const response = await getCompany(id);\r\n    return transformCompanyData(response.data);\r\n  } catch (error) {\r\n    console.error(`Error fetching company ${id}:`, error);\r\n    // Fallback to static data\r\n    return companies.find(company => company.id === id) || null;\r\n  }\r\n};\r\n\r\n\r\n\r\n// Helper functions\r\nexport const getActiveJobs = () => {\r\n  return jobPostings.filter(job => job.is_active);\r\n};\r\n\r\nexport const getJobById = (jobId) => {\r\n  return jobPostings.find(job => job.id === jobId);\r\n};\r\n\r\nexport const getCompaniesWithActiveJobs = () => {\r\n  const activeJobCompanyIds = new Set(\r\n    jobPostings.filter(job => job.is_active).map(job => job.company_id)\r\n  );\r\n  return companies.filter(company => activeJobCompanyIds.has(company.id));\r\n};\r\n\r\nexport const getApplicationStats = () => {\r\n  const total = studentApplications.length;\r\n  const pending = studentApplications.filter(app => \r\n    app.status === 'APPLIED' || app.status === 'UNDER REVIEW'\r\n  ).length;\r\n  const interviews = studentApplications.filter(app => \r\n    app.status === 'INTERVIEW SCHEDULED'\r\n  ).length;\r\n  const rejected = studentApplications.filter(app => \r\n    app.status === 'REJECTED'\r\n  ).length;\r\n  const accepted = studentApplications.filter(app => \r\n    app.status === 'ACCEPTED'\r\n  ).length;\r\n\r\n  return { total, pending, interviews, rejected, accepted };\r\n};\r\n\r\nexport const getJobStats = () => {\r\n  const total = jobPostings.length;\r\n  const active = jobPostings.filter(job => job.is_active).length;\r\n  const internships = jobPostings.filter(job => job.type === 'INTERNSHIP').length;\r\n  const fullTime = jobPostings.filter(job => job.type === 'FULL_TIME').length;\r\n  const remote = jobPostings.filter(job => job.remote_eligible).length;\r\n  const featured = jobPostings.filter(job => job.is_featured).length;\r\n\r\n  return { total, active, internships, fullTime, remote, featured };\r\n};\r\n\r\n// Function to get jobs by company\r\nexport function getJobsByCompany(companyId) {\r\n  // This is a placeholder implementation\r\n  // You should replace this with an actual API call to fetch jobs by company ID\r\n  \r\n  // For now, we'll return an empty array to prevent the error\r\n  console.log(`Fetching jobs for company ID: ${companyId}`);\r\n  return [];\r\n}"], "names": [], "mappings": "AAAA,gEAAgE;AAChE,8EAA8E;;;;;;;;;;;;;;AAE9E;;AAGO,MAAM,YAAY;IACvB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,SAAS;QACT,SAAS;IACX;CACD;AAEM,MAAM,cAAc;IACzB;QACE,IAAI;QACJ,OAAO;QACP,YAAY;QACZ,MAAM;QACN,WAAW;QACX,aAAa;QACb,iBAAiB;QACjB,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,YAAY;QACZ,MAAM;QACN,WAAW;QACX,aAAa;QACb,iBAAiB;QACjB,UAAU;IACZ;CACD;AAEM,MAAM,sBAAsB;IACjC;QACE,IAAI;QACJ,QAAQ;QACR,OAAO;QACP,SAAS;QACT,QAAQ;QACR,sBAAsB;IACxB;IACA;QACE,IAAI;QACJ,QAAQ;QACR,OAAO;QACP,SAAS;QACT,QAAQ;QACR,sBAAsB;IACxB;CACD;AAGM,MAAM,iBAAiB,OAAO,SAAS,CAAC,CAAC;IAC9C,IAAI;QACF,8BAA8B;QAC9B,MAAM,cAAc;YAClB,GAAG,MAAM;YACT,IAAI,IAAI,OAAO,OAAO;QACxB;QAEA,QAAQ,GAAG,CAAC;QACZ,MAAM,WAAW,MAAM,CAAA,GAAA,0HAAA,CAAA,gBAAa,AAAD,EAAE;QAErC,sCAAsC;QACtC,IAAI,gBAAgB,EAAE;QACtB,IAAI,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;YACjD,gBAAgB,SAAS,IAAI;QAC/B,OAAO,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,OAAO,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,OAAO,GAAG;YACzF,gBAAgB,SAAS,IAAI,CAAC,OAAO;QACvC,OAAO,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,GAAG;YACnF,gBAAgB,SAAS,IAAI,CAAC,IAAI;QACpC;QAEA,qDAAqD;QACrD,MAAM,kBAAkB,cAAc,GAAG,CAAC,0HAAA,CAAA,uBAAoB;QAE9D,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,gBAAgB,MAAM,CAAC,mBAAmB,CAAC;QAElE,+DAA+D;QAC/D,IAAI,gBAAgB,MAAM,KAAK,GAAG;YAChC,QAAQ,IAAI,CAAC;YACb,OAAO;QACT;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAE3C,iDAAiD;QACjD,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,MAAM,cAAc,MAAM,MAAM;YAChC,IAAI,YAAY,EAAE,EAAE;gBAClB,MAAM,OAAO,MAAM,YAAY,IAAI;gBACnC,MAAM,UAAU,MAAM,OAAO,CAAC,QAAQ,OAAQ,KAAK,IAAI,IAAI,KAAK,OAAO,IAAI,EAAE;gBAC7E,IAAI,QAAQ,MAAM,GAAG,GAAG;oBACtB,QAAQ,GAAG,CAAC;oBACZ,OAAO,QAAQ,GAAG,CAAC,0HAAA,CAAA,uBAAoB;gBACzC;YACF;QACF,EAAE,OAAO,UAAU;YACjB,QAAQ,KAAK,CAAC,mCAAmC;QACnD;QAEA,uCAAuC;QACvC,OAAO;IACT;AACF;AAGO,MAAM,iBAAiB,OAAO;IACnC,IAAI;QACF,4BAA4B;QAC5B,MAAM,WAAW,MAAM,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE;QAClC,OAAO,CAAA,GAAA,0HAAA,CAAA,uBAAoB,AAAD,EAAE,SAAS,IAAI;IAC3C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC,EAAE;QAC/C,0BAA0B;QAC1B,OAAO,UAAU,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK,OAAO;IACzD;AACF;AAKO,MAAM,gBAAgB;IAC3B,OAAO,YAAY,MAAM,CAAC,CAAA,MAAO,IAAI,SAAS;AAChD;AAEO,MAAM,aAAa,CAAC;IACzB,OAAO,YAAY,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;AAC5C;AAEO,MAAM,6BAA6B;IACxC,MAAM,sBAAsB,IAAI,IAC9B,YAAY,MAAM,CAAC,CAAA,MAAO,IAAI,SAAS,EAAE,GAAG,CAAC,CAAA,MAAO,IAAI,UAAU;IAEpE,OAAO,UAAU,MAAM,CAAC,CAAA,UAAW,oBAAoB,GAAG,CAAC,QAAQ,EAAE;AACvE;AAEO,MAAM,sBAAsB;IACjC,MAAM,QAAQ,oBAAoB,MAAM;IACxC,MAAM,UAAU,oBAAoB,MAAM,CAAC,CAAA,MACzC,IAAI,MAAM,KAAK,aAAa,IAAI,MAAM,KAAK,gBAC3C,MAAM;IACR,MAAM,aAAa,oBAAoB,MAAM,CAAC,CAAA,MAC5C,IAAI,MAAM,KAAK,uBACf,MAAM;IACR,MAAM,WAAW,oBAAoB,MAAM,CAAC,CAAA,MAC1C,IAAI,MAAM,KAAK,YACf,MAAM;IACR,MAAM,WAAW,oBAAoB,MAAM,CAAC,CAAA,MAC1C,IAAI,MAAM,KAAK,YACf,MAAM;IAER,OAAO;QAAE;QAAO;QAAS;QAAY;QAAU;IAAS;AAC1D;AAEO,MAAM,cAAc;IACzB,MAAM,QAAQ,YAAY,MAAM;IAChC,MAAM,SAAS,YAAY,MAAM,CAAC,CAAA,MAAO,IAAI,SAAS,EAAE,MAAM;IAC9D,MAAM,cAAc,YAAY,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK,cAAc,MAAM;IAC/E,MAAM,WAAW,YAAY,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK,aAAa,MAAM;IAC3E,MAAM,SAAS,YAAY,MAAM,CAAC,CAAA,MAAO,IAAI,eAAe,EAAE,MAAM;IACpE,MAAM,WAAW,YAAY,MAAM,CAAC,CAAA,MAAO,IAAI,WAAW,EAAE,MAAM;IAElE,OAAO;QAAE;QAAO;QAAQ;QAAa;QAAU;QAAQ;IAAS;AAClE;AAGO,SAAS,iBAAiB,SAAS;IACxC,uCAAuC;IACvC,8EAA8E;IAE9E,4DAA4D;IAC5D,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,WAAW;IACxD,OAAO,EAAE;AACX", "debugId": null}}]}