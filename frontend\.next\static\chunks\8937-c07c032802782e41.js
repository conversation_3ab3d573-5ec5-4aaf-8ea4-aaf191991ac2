"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1318,8937],{37719:(e,t,a)=>{a.d(t,{A:()=>r});var o=a(23464);a(73983);let n=o.A.create({baseURL:"http://127.0.0.1:8000",headers:{"Content-Type":"application/json"}});n.interceptors.request.use(e=>{let t=localStorage.getItem("access_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),n.interceptors.response.use(e=>e,async e=>{var t;let a=e.config;if((null==(t=e.response)?void 0:t.status)===401&&!a._retry){a._retry=!0;try{let e=localStorage.getItem("refresh_token");if(e){let t=await o.A.post("http://127.0.0.1:8000/api/auth/token/refresh/",{refresh:e});return localStorage.setItem("access_token",t.data.access),a.headers.Authorization="Bearer ".concat(t.data.access),n(a)}}catch(e){console.error("Error refreshing token:",e),localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),window.location.href="/login"}}return Promise.reject(e)});let r=n},48937:(e,t,a)=>{a.d(t,{C1:()=>c,Gu:()=>f,JT:()=>s,RC:()=>p,S0:()=>y,Y_:()=>d,bl:()=>u,dl:()=>h,eK:()=>i,fetchCompanies:()=>r,getCompanyStats:()=>l,jQ:()=>m,mm:()=>n,oY:()=>g});var o=a(37719);function n(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.page&&t.append("page",e.page),e.per_page&&t.append("per_page",e.per_page),e.tier&&"ALL"!==e.tier&&t.append("tier",e.tier),e.industry&&"ALL"!==e.industry&&t.append("industry",e.industry),e.campus_recruiting&&t.append("campus_recruiting",e.campus_recruiting),e.search&&t.append("search",e.search),e.sort&&t.append("sort",e.sort),t.append("_t",new Date().getTime());let a=t.toString(),n=["/api/v1/companies/".concat(a?"?".concat(a):""),"/api/v1/college/default-college/companies/".concat(a?"?".concat(a):"")];return o.A.get(n[0]).catch(e=>(console.log("Primary endpoint failed: ".concat(e.message,", trying fallback...")),o.A.get(n[1])))}async function r(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{console.log("Fetching companies from API...");let t=await n(e),a=[];if(t.data&&Array.isArray(t.data)?a=t.data:t.data&&t.data.results&&Array.isArray(t.data.results)?a=t.data.results:t.data&&t.data.data&&Array.isArray(t.data.data)&&(a=t.data.data),console.log("Retrieved ".concat(a.length," companies from API")),a.length>0){let e=await Promise.all(a.map(async e=>{try{let t=await c(e.id);return d(t.data)}catch(t){return console.log("Could not fetch details for company ".concat(e.id,":"),t),d(e)}}));return sessionStorage.setItem("companies_data",JSON.stringify(e)),sessionStorage.setItem("companies_timestamp",Date.now()),e}throw Error("No companies returned from API")}catch(t){console.error("Error fetching companies from API:",t);{let e=sessionStorage.getItem("companies_data"),t=sessionStorage.getItem("companies_timestamp");if(e&&t&&Date.now()-parseInt(t)<3e5)return console.log("Using cached company data (< 5 min old)"),JSON.parse(e)}console.log("Falling back to static company data");let{companies:e}=await a.e(1260).then(a.bind(a,21260));return e}}function c(e){let t=["/api/v1/company/".concat(e,"/"),"/api/v1/companies/".concat(e,"/"),"/api/v1/college/default-college/companies/".concat(e,"/")];return o.A.get(t[0]).catch(e=>(console.log("First company endpoint failed: ".concat(e.message,", trying second...")),o.A.get(t[1]).catch(e=>(console.log("Second company endpoint failed: ".concat(e.message,", trying third...")),o.A.get(t[2])))))}function i(e){let t=new FormData;return Object.keys(e).forEach(a=>{"logo"===a&&e[a]instanceof File?t.append(a,e[a]):null!==e[a]&&void 0!==e[a]&&t.append(a,e[a])}),o.A.post("/api/v1/companies/",t,{headers:{"Content-Type":"multipart/form-data"}})}function s(e,t){let a=new FormData;return Object.keys(t).forEach(e=>{"logo"===e&&t[e]instanceof File?a.append(e,t[e]):null!==t[e]&&void 0!==t[e]&&a.append(e,t[e])}),o.A.put("/api/v1/companies/".concat(e,"/"),a,{headers:{"Content-Type":"multipart/form-data"}})}function p(e){return o.A.delete("/api/v1/companies/".concat(e,"/"))}function l(){return o.A.get("/api/v1/companies/stats/")}function d(e){return{id:e.id,name:e.name,logo:e.logo||"https://via.placeholder.com/48x48/4285F4/FFFFFF?text=".concat(e.name.charAt(0)),description:e.description||"",industry:e.industry||"",size:e.size||"",founded:e.founded||"",location:e.location||"Location not specified",website:e.website||"",tier:e.tier||"Tier 3",campus_recruiting:e.campus_recruiting||!1,totalActiveJobs:e.total_active_jobs||0,totalApplicants:e.total_applicants||0,totalHired:e.total_hired||0,awaitedApproval:e.pending_approval||0}}function u(e){return o.A.get("/api/v1/companies/".concat(e,"/followers/count/"))}function m(e,t){return o.A.post("/api/v1/companies/".concat(e,"/followers/"),{user_id:t})}function g(e,t){return o.A.delete("/api/v1/companies/".concat(e,"/followers/"),{data:{user_id:t}})}function f(e,t){return o.A.get("/api/v1/companies/".concat(e,"/followers/status/?user_id=").concat(t))}function h(e){return o.A.get("/api/v1/users/".concat(e,"/following/"))}function y(){return o.A.get("/api/v1/companies/simple/").then(e=>{if(e.data&&e.data.success)return e.data;throw Error("Failed to fetch companies")}).catch(e=>(console.error("Error fetching companies:",e),{success:!1,data:[],count:0,error:e.message}))}}}]);