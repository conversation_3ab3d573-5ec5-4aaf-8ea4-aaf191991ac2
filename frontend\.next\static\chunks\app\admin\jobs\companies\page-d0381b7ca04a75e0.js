(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1318,3123,8937],{1243:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},4008:(e,t,a)=>{Promise.resolve().then(a.bind(a,16895))},16895:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>p});var r=a(95155),s=a(12115),n=a(35695),o=a(22439);function l(e){let{company:t,onClick:a}=e,[n,o]=(0,s.useState)(!1);return(0,r.jsxs)("div",{className:"bg-gray-100 p-4 rounded-xl shadow w-full hover:shadow-lg transition-shadow",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("div",{className:"w-14 h-14 rounded bg-gray-300 flex items-center justify-center text-sm font-semibold text-gray-600 overflow-hidden",children:n?(0,r.jsx)("span",{children:"Logo"}):(0,r.jsx)("img",{src:t.Logo,alt:"Logo",className:"w-full h-full object-cover",onError:()=>o(!0)})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{onClick:()=>a(t),className:"text-lg font-semibold text-blue-500 cursor-pointer hover:underline",children:t.companyName}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:t.companyDescription})]})]}),(0,r.jsxs)("div",{className:"mt-3 text-sm text-gray-700",children:[(0,r.jsxs)("p",{children:[t.totalActiveJobs," Active Listing, ",t.awaitedApproval," Awaiting Approval"]}),(0,r.jsx)("br",{}),(0,r.jsx)("p",{className:"text-gray-500",children:t.location})]})]})}function c(e){let{label:t,value:a}=e;return(0,r.jsxs)("div",{className:"bg-gray-100 rounded-lg p-4 text-center shadow",children:[(0,r.jsx)("p",{className:"text-xl font-bold",children:a}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:t})]})}function i(e){let{company:t,onClose:a}=e,[n,o]=(0,s.useState)(!1);return(0,r.jsx)("div",{className:"fixed inset-0 z-50 bg-white/80 backdrop-blur-none flex items-center justify-center",children:(0,r.jsxs)("div",{className:"bg-white w-full max-w-4xl rounded-xl p-6 relative shadow-2xl border",children:[(0,r.jsx)("button",{onClick:a,className:"absolute top-3 right-4 text-gray-600 text-xl hover:text-black",children:"\xd7"}),(0,r.jsxs)("div",{className:"flex items-center gap-4 border-b pb-4",children:[(0,r.jsx)("div",{className:"w-20 h-20 bg-gray-200 rounded-full flex items-center justify-center overflow-hidden",children:n?(0,r.jsx)("span",{className:"text-sm text-gray-500",children:"Logo"}):(0,r.jsx)("img",{src:t.Logo,alt:"Logo",className:"w-full h-full object-cover",onError:()=>o(!0)})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-bold",children:t.companyName}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:t.companyDescription}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:t.location}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:[t.employeeCount," Employees"]}),(0,r.jsx)("a",{href:t.website,className:"text-blue-500 underline text-sm",target:"_blank",rel:"noopener noreferrer",children:t.website})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-5 gap-4 my-6",children:[(0,r.jsx)(c,{label:"Total Active Jobs",value:t.totalActiveJobs}),(0,r.jsx)(c,{label:"Total Applicants",value:t.totalApplicants}),(0,r.jsx)(c,{label:"Total Hired",value:t.totalHired}),(0,r.jsx)(c,{label:"Waiting for Approval",value:t.awaitedApproval}),(0,r.jsx)(c,{label:"Other Stat",value:"..."})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-lg mb-2",children:"Active Listing"}),(0,r.jsxs)("table",{className:"w-full table-auto text-sm",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{className:"text-left bg-gray-100",children:[(0,r.jsx)("th",{className:"p-2",children:"Title"}),(0,r.jsx)("th",{className:"p-2",children:"Type"}),(0,r.jsx)("th",{className:"p-2",children:"CTC"}),(0,r.jsx)("th",{className:"p-2",children:"Stipend"}),(0,r.jsx)("th",{className:"p-2",children:"Deadline"})]})}),(0,r.jsx)("tbody",{children:(t.activeListingsData||[]).map((e,t)=>(0,r.jsxs)("tr",{className:"".concat(t%2==0?"bg-white":"bg-gray-50"," text-left"),children:[(0,r.jsx)("td",{className:"p-2",children:e.title}),(0,r.jsx)("td",{className:"p-2",children:e.type}),(0,r.jsx)("td",{className:"px-4 py-4 text-gray-700",children:e.ctc?"".concat(e.ctc," LPA"):"-"}),(0,r.jsx)("td",{className:"px-4 py-4 text-gray-700",children:e.stipend?"".concat(e.stipend," INR "):"-"}),(0,r.jsx)("td",{className:"p-2",children:e.deadline})]},t))})]})]})]})})}var d=a(48937);function p(){let e=(0,n.useRouter)(),[t,a]=(0,s.useState)([]),[c,p]=(0,s.useState)(null),[u,m]=(0,s.useState)(""),[h,g]=(0,s.useState)(!0),[x,y]=(0,s.useState)(null);(0,s.useEffect)(()=>{(async()=>{g(!0);try{console.log("Fetching companies...");let e=await (0,d.S0)();if(e.success&&Array.isArray(e.data))a(e.data),console.log("Loaded ".concat(e.data.length," companies successfully")),y(null);else throw Error(e.error||"Failed to fetch companies")}catch(e){console.error("Failed to fetch companies:",e),y("Failed to load companies. Please try again."),a([])}finally{g(!1)}})()},[]);let f=t.filter(e=>(e.companyName||e.company_name||"").toLowerCase().includes(u.toLowerCase())).sort((e,t)=>(e.companyName||e.company_name||"").localeCompare(t.companyName||t.company_name||""));return h?(0,r.jsx)("div",{className:"h-full flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading companies..."})]})}):x?(0,r.jsx)("div",{className:"h-full flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center text-red-600",children:[(0,r.jsx)("p",{className:"text-xl mb-4",children:x}),(0,r.jsx)("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:"Try Again"})]})}):(0,r.jsx)("div",{className:"p-6",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Companies"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Manage company partnerships and job opportunities"})]}),(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsx)("button",{onClick:()=>e.push("/admin/jobs/listings"),className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700",children:"View Job Listings"}),(0,r.jsx)("button",{onClick:()=>e.push("/admin/jobs/create"),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Post New Job"})]})]}),(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)("div",{className:"relative max-w-md",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(o.A,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsx)("input",{type:"text",placeholder:"Search companies...",value:u,onChange:e=>m(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:0===f.length?(0,r.jsxs)("div",{className:"col-span-full text-center py-12",children:[(0,r.jsx)("p",{className:"text-gray-500 text-lg",children:"No companies found"}),(0,r.jsx)("p",{className:"text-gray-400",children:"Try adjusting your search criteria"})]}):f.map(e=>(0,r.jsx)(l,{company:e,onClick:()=>p(e)},e.id))}),c&&(0,r.jsx)(i,{company:c,isOpen:!!c,onClose:()=>p(null)})]})})}a(34842)},19946:(e,t,a)=>{"use strict";a.d(t,{A:()=>p});var r=a(12115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,a)=>a?a.toUpperCase():t.toLowerCase()),o=e=>{let t=n(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return t.filter((e,t,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===t).join(" ").trim()},c=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,r.forwardRef)((e,t)=>{let{color:a="currentColor",size:s=24,strokeWidth:n=2,absoluteStrokeWidth:o,className:d="",children:p,iconNode:u,...m}=e;return(0,r.createElement)("svg",{ref:t,...i,width:s,height:s,stroke:a,strokeWidth:o?24*Number(n)/Number(s):n,className:l("lucide",d),...!p&&!c(m)&&{"aria-hidden":"true"},...m},[...u.map(e=>{let[t,a]=e;return(0,r.createElement)(t,a)}),...Array.isArray(p)?p:[p]])}),p=(e,t)=>{let a=(0,r.forwardRef)((a,n)=>{let{className:c,...i}=a;return(0,r.createElement)(d,{ref:n,iconNode:t,className:l("lucide-".concat(s(o(e))),"lucide-".concat(e),c),...i})});return a.displayName=o(e),a}},22439:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var r=(0,a(86467).A)("outline","search","IconSearch",[["path",{d:"M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0",key:"svg-0"}],["path",{d:"M21 21l-6 -6",key:"svg-1"}]])},29869:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},33786:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},34842:(e,t,a)=>{"use strict";a.d(t,{G$:()=>l,N6:()=>s,Om:()=>d,T4:()=>p,YQ:()=>o,_S:()=>c,lh:()=>i,vr:()=>n});var r=a(37719);function s(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.page&&t.append("page",e.page),e.per_page&&t.append("per_page",e.per_page),e.job_type&&"ALL"!==e.job_type&&t.append("job_type",e.job_type),e.location&&"ALL"!==e.location&&t.append("location",e.location),e.salary_min&&t.append("salary_min",e.salary_min),e.search&&t.append("search",e.search);let a=t.toString();return r.A.get("/api/v1/college/default-college/jobs/".concat(a?"?".concat(a):""))}function n(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!Object.values(a).some(e=>e instanceof File))return r.A.post("/api/v1/college/default-college/jobs/".concat(e,"/apply/"),{cover_letter:t,additional_field_responses:a});{let s=new FormData;return s.append("cover_letter",t),Object.entries(a).forEach(e=>{let[t,a]=e;a instanceof File?s.append(t,a):s.append(t,JSON.stringify(a))}),r.A.post("/api/v1/college/default-college/jobs/".concat(e,"/apply/"),s,{headers:{"Content-Type":"multipart/form-data"}})}}function o(e){return r.A.get("/api/v1/college/default-college/jobs/".concat(e,"/"))}function l(){return r.A.get("/api/v1/college/default-college/jobs/applied/")}function c(e){return r.A.post("/api/v1/college/default-college/jobs/create/",e)}function i(e,t){return r.A.put("/api/v1/college/default-college/jobs/".concat(e,"/"),t)}function d(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.page&&t.append("page",e.page),e.per_page&&t.append("per_page",e.per_page),e.search&&t.append("search",e.search),e.type&&"All"!==e.type&&t.append("job_type",e.type),e.minCTC&&t.append("salary_min",e.minCTC),e.maxCTC&&t.append("salary_max",e.maxCTC),e.minStipend&&t.append("stipend_min",e.minStipend),e.maxStipend&&t.append("stipend_max",e.maxStipend),e.location&&t.append("location",e.location),void 0!==e.is_published&&t.append("is_published",e.is_published),e.company_id&&t.append("company_id",e.company_id),e.company_name&&t.append("company_name",e.company_name);let a=t.toString(),s="/api/v1/college/default-college/jobs/admin/".concat(a?"?".concat(a):"");return console.log("\uD83C\uDF10 listJobsAdmin calling URL:",s,"with params:",e),r.A.get(s).then(e=>{var t,a,r,s,n,o;return console.log("\uD83C\uDF10 listJobsAdmin response:",{status:e.status,totalJobs:(null==(a=e.data)||null==(t=a.pagination)?void 0:t.total_count)||0,currentPage:(null==(s=e.data)||null==(r=s.pagination)?void 0:r.current_page)||1,totalPages:(null==(o=e.data)||null==(n=o.pagination)?void 0:n.total_pages)||1}),e}).catch(e=>{var t;throw console.error("\uD83C\uDF10 listJobsAdmin error:",e),console.error("\uD83C\uDF10 listJobsAdmin error response:",null==(t=e.response)?void 0:t.data),e})}function p(e){return r.A.patch("/api/v1/jobs/".concat(e,"/toggle-publish/"))}},35695:(e,t,a)=>{"use strict";var r=a(18999);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"usePathname")&&a.d(t,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},37719:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var r=a(23464);a(73983);let s=r.A.create({baseURL:"http://127.0.0.1:8000",headers:{"Content-Type":"application/json"}});s.interceptors.request.use(e=>{let t=localStorage.getItem("access_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),s.interceptors.response.use(e=>e,async e=>{var t;let a=e.config;if((null==(t=e.response)?void 0:t.status)===401&&!a._retry){a._retry=!0;try{let e=localStorage.getItem("refresh_token");if(e){let t=await r.A.post("http://127.0.0.1:8000/api/auth/token/refresh/",{refresh:e});return localStorage.setItem("access_token",t.data.access),a.headers.Authorization="Bearer ".concat(t.data.access),s(a)}}catch(e){console.error("Error refreshing token:",e),localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),window.location.href="/login"}}return Promise.reject(e)});let n=s},40646:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},48937:(e,t,a)=>{"use strict";a.d(t,{C1:()=>o,Gu:()=>g,JT:()=>c,RC:()=>i,S0:()=>y,Y_:()=>p,bl:()=>u,dl:()=>x,eK:()=>l,fetchCompanies:()=>n,getCompanyStats:()=>d,jQ:()=>m,mm:()=>s,oY:()=>h});var r=a(37719);function s(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.page&&t.append("page",e.page),e.per_page&&t.append("per_page",e.per_page),e.tier&&"ALL"!==e.tier&&t.append("tier",e.tier),e.industry&&"ALL"!==e.industry&&t.append("industry",e.industry),e.campus_recruiting&&t.append("campus_recruiting",e.campus_recruiting),e.search&&t.append("search",e.search),e.sort&&t.append("sort",e.sort),t.append("_t",new Date().getTime());let a=t.toString(),s=["/api/v1/companies/".concat(a?"?".concat(a):""),"/api/v1/college/default-college/companies/".concat(a?"?".concat(a):"")];return r.A.get(s[0]).catch(e=>(console.log("Primary endpoint failed: ".concat(e.message,", trying fallback...")),r.A.get(s[1])))}async function n(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{console.log("Fetching companies from API...");let t=await s(e),a=[];if(t.data&&Array.isArray(t.data)?a=t.data:t.data&&t.data.results&&Array.isArray(t.data.results)?a=t.data.results:t.data&&t.data.data&&Array.isArray(t.data.data)&&(a=t.data.data),console.log("Retrieved ".concat(a.length," companies from API")),a.length>0){let e=await Promise.all(a.map(async e=>{try{let t=await o(e.id);return p(t.data)}catch(t){return console.log("Could not fetch details for company ".concat(e.id,":"),t),p(e)}}));return sessionStorage.setItem("companies_data",JSON.stringify(e)),sessionStorage.setItem("companies_timestamp",Date.now()),e}throw Error("No companies returned from API")}catch(t){console.error("Error fetching companies from API:",t);{let e=sessionStorage.getItem("companies_data"),t=sessionStorage.getItem("companies_timestamp");if(e&&t&&Date.now()-parseInt(t)<3e5)return console.log("Using cached company data (< 5 min old)"),JSON.parse(e)}console.log("Falling back to static company data");let{companies:e}=await a.e(1260).then(a.bind(a,21260));return e}}function o(e){let t=["/api/v1/company/".concat(e,"/"),"/api/v1/companies/".concat(e,"/"),"/api/v1/college/default-college/companies/".concat(e,"/")];return r.A.get(t[0]).catch(e=>(console.log("First company endpoint failed: ".concat(e.message,", trying second...")),r.A.get(t[1]).catch(e=>(console.log("Second company endpoint failed: ".concat(e.message,", trying third...")),r.A.get(t[2])))))}function l(e){let t=new FormData;return Object.keys(e).forEach(a=>{"logo"===a&&e[a]instanceof File?t.append(a,e[a]):null!==e[a]&&void 0!==e[a]&&t.append(a,e[a])}),r.A.post("/api/v1/companies/",t,{headers:{"Content-Type":"multipart/form-data"}})}function c(e,t){let a=new FormData;return Object.keys(t).forEach(e=>{"logo"===e&&t[e]instanceof File?a.append(e,t[e]):null!==t[e]&&void 0!==t[e]&&a.append(e,t[e])}),r.A.put("/api/v1/companies/".concat(e,"/"),a,{headers:{"Content-Type":"multipart/form-data"}})}function i(e){return r.A.delete("/api/v1/companies/".concat(e,"/"))}function d(){return r.A.get("/api/v1/companies/stats/")}function p(e){return{id:e.id,name:e.name,logo:e.logo||"https://via.placeholder.com/48x48/4285F4/FFFFFF?text=".concat(e.name.charAt(0)),description:e.description||"",industry:e.industry||"",size:e.size||"",founded:e.founded||"",location:e.location||"Location not specified",website:e.website||"",tier:e.tier||"Tier 3",campus_recruiting:e.campus_recruiting||!1,totalActiveJobs:e.total_active_jobs||0,totalApplicants:e.total_applicants||0,totalHired:e.total_hired||0,awaitedApproval:e.pending_approval||0}}function u(e){return r.A.get("/api/v1/companies/".concat(e,"/followers/count/"))}function m(e,t){return r.A.post("/api/v1/companies/".concat(e,"/followers/"),{user_id:t})}function h(e,t){return r.A.delete("/api/v1/companies/".concat(e,"/followers/"),{data:{user_id:t}})}function g(e,t){return r.A.get("/api/v1/companies/".concat(e,"/followers/status/?user_id=").concat(t))}function x(e){return r.A.get("/api/v1/users/".concat(e,"/following/"))}function y(){return r.A.get("/api/v1/companies/simple/").then(e=>{if(e.data&&e.data.success)return e.data;throw Error("Failed to fetch companies")}).catch(e=>(console.error("Error fetching companies:",e),{success:!1,data:[],count:0,error:e.message}))}},54416:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},71007:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},75525:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},76517:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]])},81284:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},85339:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},86467:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var r=a(12115),s={outline:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"},filled:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",stroke:"none"}};let n=(e,t,a,n)=>{let o=(0,r.forwardRef)((a,o)=>{let{color:l="currentColor",size:c=24,stroke:i=2,title:d,className:p,children:u,...m}=a;return(0,r.createElement)("svg",{ref:o,...s[e],width:c,height:c,className:["tabler-icon","tabler-icon-".concat(t),p].join(" "),..."filled"===e?{fill:l}:{strokeWidth:i,stroke:l},...m},[d&&(0,r.createElement)("title",{key:"svg-title"},d),...n.map(e=>{let[t,a]=e;return(0,r.createElement)(t,a)}),...Array.isArray(u)?u:[u]])});return o.displayName="".concat(a),o}}},e=>{var t=t=>e(e.s=t);e.O(0,[3464,3983,8441,1684,7358],()=>t(4008)),_N_E=e.O()}]);