{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/api/errorHandler.js"], "sourcesContent": ["import { useNotification } from '../contexts/NotificationContext';\r\n\r\n// Error codes and their corresponding handlers\r\nexport const ERROR_PATTERNS = {\r\n  AUTHENTICATION: {\r\n    codes: [401],\r\n    keywords: ['unauthorized', 'authentication', 'token', 'login'],\r\n    handler: 'showAuthError'\r\n  },\r\n  SESSION_EXPIRED: {\r\n    codes: [401],\r\n    keywords: ['expired', 'invalid token', 'token expired'],\r\n    handler: 'showSessionExpiredModal'\r\n  },\r\n  PERMISSION_DENIED: {\r\n    codes: [403],\r\n    keywords: ['permission', 'forbidden', 'access denied'],\r\n    handler: 'showAuthError'\r\n  },\r\n  VALIDATION: {\r\n    codes: [400, 422],\r\n    keywords: ['validation', 'invalid', 'required'],\r\n    handler: 'showValidationError'\r\n  },\r\n  RESUME_REQUIRED: {\r\n    fields: ['resume'],\r\n    keywords: ['resume', 'must be uploaded', 'present in the student profile'],\r\n    handler: 'showMissingResumeModal'\r\n  },\r\n  PROFILE_INCOMPLETE: {\r\n    keywords: ['profile incomplete', 'missing profile', 'update profile'],\r\n    handler: 'showProfileIncompleteModal'\r\n  },\r\n  FILE_UPLOAD: {\r\n    keywords: ['file', 'upload', 'size', 'format', 'extension'],\r\n    handler: 'showFileUploadError'\r\n  },\r\n  NETWORK_ERROR: {\r\n    codes: ['NETWORK_ERROR', 'ECONNREFUSED', 'ERR_NETWORK'],\r\n    keywords: ['network', 'connection', 'timeout'],\r\n    handler: 'showNetworkError'\r\n  },\r\n  MAINTENANCE: {\r\n    codes: [503, 502],\r\n    keywords: ['maintenance', 'service unavailable', 'temporarily unavailable'],\r\n    handler: 'showMaintenanceModal'\r\n  }\r\n};\r\n\r\n// Smart error detection and handling\r\nexport const detectAndHandleError = (error, context = '', notificationHandlers) => {\r\n  const errorData = error?.response?.data || {};\r\n  const errorMessage = (errorData.detail || errorData.message || error.message || '').toLowerCase();\r\n  const statusCode = error?.response?.status;\r\n\r\n  // Check for specific error patterns\r\n  for (const [pattern, config] of Object.entries(ERROR_PATTERNS)) {\r\n    // Check status codes\r\n    if (config.codes && config.codes.includes(statusCode)) {\r\n      // Additional keyword check for more precision\r\n      if (config.keywords && !config.keywords.some(keyword => errorMessage.includes(keyword))) {\r\n        continue;\r\n      }\r\n      \r\n      return handleSpecificError(pattern, error, context, notificationHandlers);\r\n    }\r\n\r\n    // Check for field-specific errors (like resume)\r\n    if (config.fields && config.fields.some(field => errorData[field])) {\r\n      return handleSpecificError(pattern, error, context, notificationHandlers);\r\n    }\r\n\r\n    // Check keywords in error message\r\n    if (config.keywords && config.keywords.some(keyword => errorMessage.includes(keyword))) {\r\n      return handleSpecificError(pattern, error, context, notificationHandlers);\r\n    }\r\n  }\r\n\r\n  // Fallback to generic error handling\r\n  return handleGenericError(error, context, notificationHandlers);\r\n};\r\n\r\nconst handleSpecificError = (pattern, error, context, notificationHandlers) => {\r\n  const config = ERROR_PATTERNS[pattern];\r\n  const handlerName = config.handler;\r\n  \r\n  if (notificationHandlers[handlerName]) {\r\n    switch (handlerName) {\r\n      case 'showMissingResumeModal':\r\n        notificationHandlers.showMissingResumeModal();\r\n        break;\r\n      case 'showSessionExpiredModal':\r\n        notificationHandlers.showSessionExpiredModal();\r\n        break;\r\n      case 'showMaintenanceModal':\r\n        notificationHandlers.showMaintenanceModal();\r\n        break;\r\n      case 'showValidationError':\r\n        const errorData = error?.response?.data || {};\r\n        notificationHandlers.showValidationError(\r\n          `Validation Error ${context ? `in ${context}` : ''}`, \r\n          errorData\r\n        );\r\n        break;\r\n      case 'showAuthError':\r\n        const message = error?.response?.data?.detail || \r\n                       error?.response?.data?.message || \r\n                       `Authentication failed${context ? ` while ${context}` : ''}`;\r\n        notificationHandlers.showAuthError(message);\r\n        break;\r\n      case 'showFileUploadError':\r\n        notificationHandlers.showFileUploadError();\r\n        break;\r\n      case 'showNetworkError':\r\n        notificationHandlers.showNetworkError(error);\r\n        break;\r\n      case 'showProfileIncompleteModal':\r\n        notificationHandlers.showProfileIncompleteModal();\r\n        break;\r\n      default:\r\n        return handleGenericError(error, context, notificationHandlers);\r\n    }\r\n    return true; // Error was handled\r\n  }\r\n  \r\n  return false; // Error not handled\r\n};\r\n\r\nconst handleGenericError = (error, context, notificationHandlers) => {\r\n  if (notificationHandlers.handleApiError) {\r\n    notificationHandlers.handleApiError(error, context);\r\n    return true;\r\n  }\r\n  \r\n  // Ultimate fallback\r\n  console.error('Unhandled error:', error);\r\n  return false;\r\n};\r\n\r\n// Hook for easy error handling in components\r\nexport const useErrorHandler = () => {\r\n  const notificationHandlers = useNotification();\r\n  \r\n  const handleError = (error, context = '') => {\r\n    return detectAndHandleError(error, context, notificationHandlers);\r\n  };\r\n\r\n  return { handleError };\r\n};\r\n\r\n// Axios interceptor setup\r\nexport const setupErrorInterceptor = (axiosInstance, notificationHandlers) => {\r\n  axiosInstance.interceptors.response.use(\r\n    (response) => response,\r\n    (error) => {\r\n      // Automatically handle common errors\r\n      detectAndHandleError(error, 'API request', notificationHandlers);\r\n      return Promise.reject(error);\r\n    }\r\n  );\r\n};\r\n\r\nexport default {\r\n  detectAndHandleError,\r\n  useErrorHandler,\r\n  setupErrorInterceptor,\r\n  ERROR_PATTERNS\r\n}; "], "names": [], "mappings": ";;;;;;;AAAA;;;AAGO,MAAM,iBAAiB;IAC5B,gBAAgB;QACd,OAAO;YAAC;SAAI;QACZ,UAAU;YAAC;YAAgB;YAAkB;YAAS;SAAQ;QAC9D,SAAS;IACX;IACA,iBAAiB;QACf,OAAO;YAAC;SAAI;QACZ,UAAU;YAAC;YAAW;YAAiB;SAAgB;QACvD,SAAS;IACX;IACA,mBAAmB;QACjB,OAAO;YAAC;SAAI;QACZ,UAAU;YAAC;YAAc;YAAa;SAAgB;QACtD,SAAS;IACX;IACA,YAAY;QACV,OAAO;YAAC;YAAK;SAAI;QACjB,UAAU;YAAC;YAAc;YAAW;SAAW;QAC/C,SAAS;IACX;IACA,iBAAiB;QACf,QAAQ;YAAC;SAAS;QAClB,UAAU;YAAC;YAAU;YAAoB;SAAiC;QAC1E,SAAS;IACX;IACA,oBAAoB;QAClB,UAAU;YAAC;YAAsB;YAAmB;SAAiB;QACrE,SAAS;IACX;IACA,aAAa;QACX,UAAU;YAAC;YAAQ;YAAU;YAAQ;YAAU;SAAY;QAC3D,SAAS;IACX;IACA,eAAe;QACb,OAAO;YAAC;YAAiB;YAAgB;SAAc;QACvD,UAAU;YAAC;YAAW;YAAc;SAAU;QAC9C,SAAS;IACX;IACA,aAAa;QACX,OAAO;YAAC;YAAK;SAAI;QACjB,UAAU;YAAC;YAAe;YAAuB;SAA0B;QAC3E,SAAS;IACX;AACF;AAGO,MAAM,uBAAuB,CAAC,OAAO,UAAU,EAAE,EAAE;IACxD,MAAM,YAAY,OAAO,UAAU,QAAQ,CAAC;IAC5C,MAAM,eAAe,CAAC,UAAU,MAAM,IAAI,UAAU,OAAO,IAAI,MAAM,OAAO,IAAI,EAAE,EAAE,WAAW;IAC/F,MAAM,aAAa,OAAO,UAAU;IAEpC,oCAAoC;IACpC,KAAK,MAAM,CAAC,SAAS,OAAO,IAAI,OAAO,OAAO,CAAC,gBAAiB;QAC9D,qBAAqB;QACrB,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,CAAC,QAAQ,CAAC,aAAa;YACrD,8CAA8C;YAC9C,IAAI,OAAO,QAAQ,IAAI,CAAC,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAA,UAAW,aAAa,QAAQ,CAAC,WAAW;gBACvF;YACF;YAEA,OAAO,oBAAoB,SAAS,OAAO,SAAS;QACtD;QAEA,gDAAgD;QAChD,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,CAAA,QAAS,SAAS,CAAC,MAAM,GAAG;YAClE,OAAO,oBAAoB,SAAS,OAAO,SAAS;QACtD;QAEA,kCAAkC;QAClC,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAA,UAAW,aAAa,QAAQ,CAAC,WAAW;YACtF,OAAO,oBAAoB,SAAS,OAAO,SAAS;QACtD;IACF;IAEA,qCAAqC;IACrC,OAAO,mBAAmB,OAAO,SAAS;AAC5C;AAEA,MAAM,sBAAsB,CAAC,SAAS,OAAO,SAAS;IACpD,MAAM,SAAS,cAAc,CAAC,QAAQ;IACtC,MAAM,cAAc,OAAO,OAAO;IAElC,IAAI,oBAAoB,CAAC,YAAY,EAAE;QACrC,OAAQ;YACN,KAAK;gBACH,qBAAqB,sBAAsB;gBAC3C;YACF,KAAK;gBACH,qBAAqB,uBAAuB;gBAC5C;YACF,KAAK;gBACH,qBAAqB,oBAAoB;gBACzC;YACF,KAAK;gBACH,MAAM,YAAY,OAAO,UAAU,QAAQ,CAAC;gBAC5C,qBAAqB,mBAAmB,CACtC,CAAC,iBAAiB,EAAE,UAAU,CAAC,GAAG,EAAE,SAAS,GAAG,IAAI,EACpD;gBAEF;YACF,KAAK;gBACH,MAAM,UAAU,OAAO,UAAU,MAAM,UACxB,OAAO,UAAU,MAAM,WACvB,CAAC,qBAAqB,EAAE,UAAU,CAAC,OAAO,EAAE,SAAS,GAAG,IAAI;gBAC3E,qBAAqB,aAAa,CAAC;gBACnC;YACF,KAAK;gBACH,qBAAqB,mBAAmB;gBACxC;YACF,KAAK;gBACH,qBAAqB,gBAAgB,CAAC;gBACtC;YACF,KAAK;gBACH,qBAAqB,0BAA0B;gBAC/C;YACF;gBACE,OAAO,mBAAmB,OAAO,SAAS;QAC9C;QACA,OAAO,MAAM,oBAAoB;IACnC;IAEA,OAAO,OAAO,oBAAoB;AACpC;AAEA,MAAM,qBAAqB,CAAC,OAAO,SAAS;IAC1C,IAAI,qBAAqB,cAAc,EAAE;QACvC,qBAAqB,cAAc,CAAC,OAAO;QAC3C,OAAO;IACT;IAEA,oBAAoB;IACpB,QAAQ,KAAK,CAAC,oBAAoB;IAClC,OAAO;AACT;AAGO,MAAM,kBAAkB;;IAC7B,MAAM,uBAAuB,CAAA,GAAA,0IAAA,CAAA,kBAAe,AAAD;IAE3C,MAAM,cAAc,CAAC,OAAO,UAAU,EAAE;QACtC,OAAO,qBAAqB,OAAO,SAAS;IAC9C;IAEA,OAAO;QAAE;IAAY;AACvB;GARa;;QACkB,0IAAA,CAAA,kBAAe;;;AAUvC,MAAM,wBAAwB,CAAC,eAAe;IACnD,cAAc,YAAY,CAAC,QAAQ,CAAC,GAAG;qCACrC,CAAC,WAAa;;qCACd,CAAC;YACC,qCAAqC;YACrC,qBAAqB,OAAO,eAAe;YAC3C,OAAO,QAAQ,MAAM,CAAC;QACxB;;AAEJ;uCAEe;IACb;IACA;IACA;IACA;AACF", "debugId": null}}, {"offset": {"line": 233, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/api/client.js"], "sourcesContent": ["import axios from 'axios';\r\nimport { setupErrorInterceptor } from './errorHandler';\r\n\r\nconst client = axios.create({\r\n  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000',\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n});\r\n\r\n// Add a request interceptor to include the auth token\r\nclient.interceptors.request.use(\r\n  (config) => {\r\n    // Get the token from localStorage\r\n    const token = localStorage.getItem('access_token');\r\n    \r\n    // If token exists, add it to the Authorization header\r\n    if (token) {\r\n      config.headers['Authorization'] = `Bearer ${token}`;\r\n    }\r\n    \r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Add a response interceptor to handle 401 errors (token expired)\r\nclient.interceptors.response.use(\r\n  (response) => response,\r\n  async (error) => {\r\n    const originalRequest = error.config;\r\n    \r\n    // If error is 401 and we haven't tried to refresh the token yet\r\n    if (error.response?.status === 401 && !originalRequest._retry) {\r\n      originalRequest._retry = true;\r\n      \r\n      try {\r\n        // Get refresh token\r\n        const refreshToken = localStorage.getItem('refresh_token');\r\n        \r\n        if (refreshToken) {\r\n          // Try to get a new token\r\n          const response = await axios.post('http://127.0.0.1:8000/api/auth/token/refresh/', {\r\n            refresh: refreshToken\r\n          });\r\n          \r\n          // Store the new tokens\r\n          localStorage.setItem('access_token', response.data.access);\r\n          \r\n          // Update the Authorization header\r\n          originalRequest.headers['Authorization'] = `Bearer ${response.data.access}`;\r\n          \r\n          // Retry the original request\r\n          return client(originalRequest);\r\n        }\r\n      } catch (refreshError) {\r\n        console.error('Error refreshing token:', refreshError);\r\n        \r\n        // If token refresh fails, redirect to login\r\n        if (typeof window !== 'undefined') {\r\n          // Clear tokens\r\n          localStorage.removeItem('access_token');\r\n          localStorage.removeItem('refresh_token');\r\n          \r\n          // Redirect to login page\r\n          window.location.href = '/login';\r\n        }\r\n      }\r\n    }\r\n    \r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default client;\r\n"], "names": [], "mappings": ";;;AAIW;AAJX;AACA;;;AAEA,MAAM,SAAS,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC1B,SAAS,6DAAmC;IAC5C,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,sDAAsD;AACtD,OAAO,YAAY,CAAC,OAAO,CAAC,GAAG,CAC7B,CAAC;IACC,kCAAkC;IAClC,MAAM,QAAQ,aAAa,OAAO,CAAC;IAEnC,sDAAsD;IACtD,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;IACrD;IAEA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,kEAAkE;AAClE,OAAO,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC9B,CAAC,WAAa,UACd,OAAO;IACL,MAAM,kBAAkB,MAAM,MAAM;IAEpC,gEAAgE;IAChE,IAAI,MAAM,QAAQ,EAAE,WAAW,OAAO,CAAC,gBAAgB,MAAM,EAAE;QAC7D,gBAAgB,MAAM,GAAG;QAEzB,IAAI;YACF,oBAAoB;YACpB,MAAM,eAAe,aAAa,OAAO,CAAC;YAE1C,IAAI,cAAc;gBAChB,yBAAyB;gBACzB,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,iDAAiD;oBACjF,SAAS;gBACX;gBAEA,uBAAuB;gBACvB,aAAa,OAAO,CAAC,gBAAgB,SAAS,IAAI,CAAC,MAAM;gBAEzD,kCAAkC;gBAClC,gBAAgB,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,SAAS,IAAI,CAAC,MAAM,EAAE;gBAE3E,6BAA6B;gBAC7B,OAAO,OAAO;YAChB;QACF,EAAE,OAAO,cAAc;YACrB,QAAQ,KAAK,CAAC,2BAA2B;YAEzC,4CAA4C;YAC5C,wCAAmC;gBACjC,eAAe;gBACf,aAAa,UAAU,CAAC;gBACxB,aAAa,UAAU,CAAC;gBAExB,yBAAyB;gBACzB,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;QACF;IACF;IAEA,OAAO,QAAQ,MAAM,CAAC;AACxB;uCAGa", "debugId": null}}, {"offset": {"line": 304, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/api/auth.js"], "sourcesContent": ["import client from './client';\r\n\r\n// Register new student\r\nexport function signup(data) {\r\n  return client.post('/api/auth/register/student/', data);\r\n}\r\n\r\n// Login and get tokens\r\nexport function login(data) {\r\n  return client.post('/api/auth/login/', data);\r\n}\r\n\r\n// Upload Resume\r\nexport function uploadResume(file, accessToken) {\r\n  const formData = new FormData();\r\n  formData.append('resume', file);\r\n\r\n  return client.patch('/api/auth/profile/', formData, {\r\n    headers: {\r\n      'Authorization': `Bearer ${accessToken}`,\r\n      'Content-Type': 'multipart/form-data',\r\n    }\r\n  });\r\n}\r\n\r\nexport const getAuthToken = () => {\r\n  if (typeof window !== 'undefined') {\r\n    return localStorage.getItem('access_token');\r\n  }\r\n  return null;\r\n};\r\n\r\nexport const setAuthToken = (token) => {\r\n  if (typeof window !== 'undefined') {\r\n    localStorage.setItem('access_token', token);\r\n  }\r\n};\r\n\r\nexport const removeAuthToken = () => {\r\n  if (typeof window !== 'undefined') {\r\n    localStorage.removeItem('access_token');\r\n    localStorage.removeItem('refresh_token');\r\n  }\r\n};\r\n\r\nexport const getRefreshToken = () => {\r\n  if (typeof window !== 'undefined') {\r\n    return localStorage.getItem('refresh_token');\r\n  }\r\n  return null;\r\n};\r\n\r\nexport const setRefreshToken = (token) => {\r\n  if (typeof window !== 'undefined') {\r\n    localStorage.setItem('refresh_token', token);\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAGO,SAAS,OAAO,IAAI;IACzB,OAAO,uHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,+BAA+B;AACpD;AAGO,SAAS,MAAM,IAAI;IACxB,OAAO,uHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,oBAAoB;AACzC;AAGO,SAAS,aAAa,IAAI,EAAE,WAAW;IAC5C,MAAM,WAAW,IAAI;IACrB,SAAS,MAAM,CAAC,UAAU;IAE1B,OAAO,uHAAA,CAAA,UAAM,CAAC,KAAK,CAAC,sBAAsB,UAAU;QAClD,SAAS;YACP,iBAAiB,CAAC,OAAO,EAAE,aAAa;YACxC,gBAAgB;QAClB;IACF;AACF;AAEO,MAAM,eAAe;IAC1B,wCAAmC;QACjC,OAAO,aAAa,OAAO,CAAC;IAC9B;;AAEF;AAEO,MAAM,eAAe,CAAC;IAC3B,wCAAmC;QACjC,aAAa,OAAO,CAAC,gBAAgB;IACvC;AACF;AAEO,MAAM,kBAAkB;IAC7B,wCAAmC;QACjC,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;IAC1B;AACF;AAEO,MAAM,kBAAkB;IAC7B,wCAAmC;QACjC,OAAO,aAAa,OAAO,CAAC;IAC9B;;AAEF;AAEO,MAAM,kBAAkB,CAAC;IAC9B,wCAAmC;QACjC,aAAa,OAAO,CAAC,iBAAiB;IACxC;AACF", "debugId": null}}, {"offset": {"line": 369, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/app/login/page.jsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport Link from 'next/link';\r\nimport axios from 'axios';\r\nimport { setAuthToken,setRefreshToken } from '../../api/auth';\r\nimport { useNotification } from '../../contexts/NotificationContext';\r\n\r\nexport default function LoginPage() {\r\n  const router = useRouter();\r\n  const { showAuthError, showValidationError, showNetworkError, handleApiError } = useNotification();\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState('');\r\n  const [email, setEmail] = useState('');\r\n  const [password, setPassword] = useState('');\r\n  const [college, setCollege] = useState('');\r\n  const [dropdown, setDropdown] = useState(false);\r\n  const [filtered, setFiltered] = useState([]);\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    await handleLogin(email, password);\r\n  };\r\n\r\n  const handleLogin = async (email, password) => {\r\n    setLoading(true);\r\n    setError('');\r\n\r\n    try {\r\n      const res = await axios.post('http://127.0.0.1:8000/api/auth/login/', {\r\n        email,\r\n        password,\r\n      });\r\n      \r\n      setAuthToken(res.data.access);\r\n      setRefreshToken(res.data.refresh);\r\n\r\n      const { access, refresh, user } = res.data;\r\n\r\n      // Store tokens with both naming conventions\r\n      localStorage.setItem('access', access);\r\n      localStorage.setItem('refresh', refresh);\r\n      localStorage.setItem('access_token', access);\r\n      localStorage.setItem('refresh_token', refresh);\r\n      localStorage.setItem('user', JSON.stringify(user));\r\n      \r\n      document.cookie = `role=${user.user_type}; path=/; max-age=86400`;\r\n\r\n      // Handle different user types\r\n      switch (user.user_type?.toLowerCase()) {\r\n        case 'student':\r\n          router.push('/');\r\n          break;\r\n        case 'admin':\r\n          router.push('/admin/dashboard');\r\n          break;\r\n        case 'employer':\r\n          router.push('/company/dashboard');\r\n          break;\r\n        default:\r\n          router.push('/');\r\n      }\r\n    } catch (err) {\r\n      console.error('Login error:', err);\r\n      \r\n      // Handle freeze status specifically\r\n      if (err.response?.status === 403 && err.response?.data?.freeze_status === 'complete') {\r\n        showAuthError(err.response.data.detail);\r\n        setError(err.response.data.detail);\r\n      } else if (err.response?.status === 401) {\r\n        showValidationError('Login Failed', {\r\n          credentials: 'Invalid email or password. Please check your credentials and try again.'\r\n        });\r\n      } else if (!err.response) {\r\n        showNetworkError(err);\r\n      } else {\r\n        handleApiError(err, 'login');\r\n      }\r\n      \r\n      // Keep the local error state for backward compatibility\r\n      const errorMessage = err.response?.data?.detail || \r\n                          err.response?.data?.message || \r\n                          'Login failed. Please try again.';\r\n      setError(errorMessage);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-r from-[#242734] to-[#241F2A] flex items-center justify-center p-4 login-container\">\r\n      <form\r\n        onSubmit={handleSubmit}\r\n        className=\"w-full max-w-md bg-white rounded-xl shadow-2xl p-10 flex flex-col gap-6 login-form\"\r\n      >\r\n        <h1 className=\"text-center text-2xl text-gray-800 font-bold mb-2\">\r\n          Login to Placeeasy\r\n        </h1>\r\n\r\n        {/* Add error display */}\r\n        {error && (\r\n          <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\">\r\n            {error}\r\n          </div>\r\n        )}\r\n\r\n        <div className=\"flex flex-col\">\r\n          <label className=\"mb-2 font-semibold text-gray-800\">Email</label>\r\n          <input\r\n            type=\"email\"\r\n            value={email}\r\n            onChange={(e) => setEmail(e.target.value)}\r\n            className=\"p-3 rounded-lg border border-gray-300 text-gray-800 text-base outline-none\"\r\n            required\r\n            disabled={loading}\r\n          />\r\n        </div>\r\n        <div className=\"flex flex-col\">\r\n          <label className=\"mb-2 font-semibold text-gray-800\">Password</label>\r\n          <input\r\n            type=\"password\"\r\n            value={password}\r\n            onChange={(e) => setPassword(e.target.value)}\r\n            className=\"p-3 rounded-lg border border-gray-300 text-gray-800 text-base outline-none\"\r\n            required\r\n            disabled={loading}\r\n          />\r\n        </div>\r\n\r\n        <button\r\n          type=\"submit\"\r\n          disabled={loading}\r\n          className={`p-3 rounded-lg cursor-pointer text-white text-base font-medium transition-colors ${\r\n            loading \r\n              ? 'bg-gray-400 cursor-not-allowed' \r\n              : 'bg-indigo-500 hover:bg-indigo-600'\r\n          }`}\r\n        >\r\n          {loading ? 'Logging in...' : 'Login'}\r\n        </button>\r\n\r\n        <button\r\n          onClick={() => handleLogin('<EMAIL>', 'admin')}\r\n          disabled={loading}\r\n          type=\"button\"\r\n          className={`p-3 rounded-lg cursor-pointer text-white text-base font-medium transition-colors ${\r\n            loading \r\n              ? 'bg-gray-400 cursor-not-allowed' \r\n              : 'bg-green-500 hover:bg-green-600'\r\n          }`}\r\n        >\r\n          {loading ? 'Logging in...' : 'Quick Login as Admin'}\r\n        </button>\r\n\r\n        <button\r\n          onClick={() => handleLogin('<EMAIL>', 'student123')}\r\n          disabled={loading}\r\n          type=\"button\"\r\n          className={`p-3 rounded-lg cursor-pointer text-white text-base font-medium transition-colors ${\r\n            loading \r\n              ? 'bg-gray-400 cursor-not-allowed' \r\n              : 'bg-blue-500 hover:bg-blue-600'\r\n          }`}\r\n        >\r\n          {loading ? 'Logging in...' : 'Quick Login as Student'}\r\n        </button>\r\n        \r\n        <Link href='/signup'>\r\n          <div className='p-3 rounded-lg cursor-pointer text-center bg-indigo-500 text-white text-base font-medium hover:bg-indigo-600 transition-colors'>\r\n            Signup \r\n          </div>\r\n        </Link>\r\n      </form>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,aAAa,EAAE,mBAAmB,EAAE,gBAAgB,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,kBAAe,AAAD;IAC/F,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAE3C,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,MAAM,YAAY,OAAO;IAC3B;IAEA,MAAM,cAAc,OAAO,OAAO;QAChC,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,yCAAyC;gBACpE;gBACA;YACF;YAEA,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD,EAAE,IAAI,IAAI,CAAC,MAAM;YAC5B,CAAA,GAAA,qHAAA,CAAA,kBAAe,AAAD,EAAE,IAAI,IAAI,CAAC,OAAO;YAEhC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,IAAI,IAAI;YAE1C,4CAA4C;YAC5C,aAAa,OAAO,CAAC,UAAU;YAC/B,aAAa,OAAO,CAAC,WAAW;YAChC,aAAa,OAAO,CAAC,gBAAgB;YACrC,aAAa,OAAO,CAAC,iBAAiB;YACtC,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;YAE5C,SAAS,MAAM,GAAG,CAAC,KAAK,EAAE,KAAK,SAAS,CAAC,uBAAuB,CAAC;YAEjE,8BAA8B;YAC9B,OAAQ,KAAK,SAAS,EAAE;gBACtB,KAAK;oBACH,OAAO,IAAI,CAAC;oBACZ;gBACF,KAAK;oBACH,OAAO,IAAI,CAAC;oBACZ;gBACF,KAAK;oBACH,OAAO,IAAI,CAAC;oBACZ;gBACF;oBACE,OAAO,IAAI,CAAC;YAChB;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,gBAAgB;YAE9B,oCAAoC;YACpC,IAAI,IAAI,QAAQ,EAAE,WAAW,OAAO,IAAI,QAAQ,EAAE,MAAM,kBAAkB,YAAY;gBACpF,cAAc,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM;gBACtC,SAAS,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM;YACnC,OAAO,IAAI,IAAI,QAAQ,EAAE,WAAW,KAAK;gBACvC,oBAAoB,gBAAgB;oBAClC,aAAa;gBACf;YACF,OAAO,IAAI,CAAC,IAAI,QAAQ,EAAE;gBACxB,iBAAiB;YACnB,OAAO;gBACL,eAAe,KAAK;YACtB;YAEA,wDAAwD;YACxD,MAAM,eAAe,IAAI,QAAQ,EAAE,MAAM,UACrB,IAAI,QAAQ,EAAE,MAAM,WACpB;YACpB,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,UAAU;YACV,WAAU;;8BAEV,6LAAC;oBAAG,WAAU;8BAAoD;;;;;;gBAKjE,uBACC,6LAAC;oBAAI,WAAU;8BACZ;;;;;;8BAIL,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAM,WAAU;sCAAmC;;;;;;sCACpD,6LAAC;4BACC,MAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4BACxC,WAAU;4BACV,QAAQ;4BACR,UAAU;;;;;;;;;;;;8BAGd,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAM,WAAU;sCAAmC;;;;;;sCACpD,6LAAC;4BACC,MAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4BAC3C,WAAU;4BACV,QAAQ;4BACR,UAAU;;;;;;;;;;;;8BAId,6LAAC;oBACC,MAAK;oBACL,UAAU;oBACV,WAAW,CAAC,iFAAiF,EAC3F,UACI,mCACA,qCACJ;8BAED,UAAU,kBAAkB;;;;;;8BAG/B,6LAAC;oBACC,SAAS,IAAM,YAAY,mBAAmB;oBAC9C,UAAU;oBACV,MAAK;oBACL,WAAW,CAAC,iFAAiF,EAC3F,UACI,mCACA,mCACJ;8BAED,UAAU,kBAAkB;;;;;;8BAG/B,6LAAC;oBACC,SAAS,IAAM,YAAY,yBAAyB;oBACpD,UAAU;oBACV,MAAK;oBACL,WAAW,CAAC,iFAAiF,EAC3F,UACI,mCACA,iCACJ;8BAED,UAAU,kBAAkB;;;;;;8BAG/B,6LAAC,+JAAA,CAAA,UAAI;oBAAC,MAAK;8BACT,cAAA,6LAAC;wBAAI,WAAU;kCAAiI;;;;;;;;;;;;;;;;;;;;;;AAO1J;GAvKwB;;QACP,qIAAA,CAAA,YAAS;QACyD,0IAAA,CAAA,kBAAe;;;KAF1E", "debugId": null}}]}