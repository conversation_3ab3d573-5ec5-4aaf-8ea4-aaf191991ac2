module.exports = {

"[project]/src/api/companies.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "checkFollowingStatus": (()=>checkFollowingStatus),
    "createCompany": (()=>createCompany),
    "deleteCompany": (()=>deleteCompany),
    "fetchCompanies": (()=>fetchCompanies),
    "fetchSimpleCompanies": (()=>fetchSimpleCompanies),
    "followCompany": (()=>followCompany),
    "getCompany": (()=>getCompany),
    "getCompanyStats": (()=>getCompanyStats),
    "getFollowersCount": (()=>getFollowersCount),
    "getIndustries": (()=>getIndustries),
    "getUserFollowedCompanies": (()=>getUserFollowedCompanies),
    "listCompanies": (()=>listCompanies),
    "transformCompanyData": (()=>transformCompanyData),
    "unfollowCompany": (()=>unfollowCompany),
    "updateCompany": (()=>updateCompany),
    "uploadCompanyLogo": (()=>uploadCompanyLogo)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/api/client.js [app-ssr] (ecmascript)");
;
function listCompanies(params = {}) {
    const queryParams = new URLSearchParams();
    // Add pagination parameters
    if (params.page) queryParams.append('page', params.page);
    if (params.per_page) queryParams.append('per_page', params.per_page);
    // Add filtering parameters
    if (params.tier && params.tier !== 'ALL') queryParams.append('tier', params.tier);
    if (params.industry && params.industry !== 'ALL') queryParams.append('industry', params.industry);
    if (params.campus_recruiting) queryParams.append('campus_recruiting', params.campus_recruiting);
    if (params.search) queryParams.append('search', params.search);
    if (params.sort) queryParams.append('sort', params.sort);
    // Add cache busting parameter to prevent cached responses
    queryParams.append('_t', new Date().getTime());
    const queryString = queryParams.toString();
    // Try both endpoints to maximize compatibility with backend
    const urls = [
        `/api/v1/companies/${queryString ? `?${queryString}` : ''}`,
        `/api/v1/college/default-college/companies/${queryString ? `?${queryString}` : ''}`
    ];
    // Try primary endpoint first, fall back to secondary if needed
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(urls[0]).catch((error)=>{
        console.log(`Primary endpoint failed: ${error.message}, trying fallback...`);
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(urls[1]);
    });
}
async function fetchCompanies(params = {}) {
    try {
        console.log('Fetching companies from API...');
        const response = await listCompanies(params);
        // Transform the data to match our frontend structure
        let companies = [];
        if (response.data && Array.isArray(response.data)) {
            companies = response.data;
        } else if (response.data && response.data.results && Array.isArray(response.data.results)) {
            companies = response.data.results;
        } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
            companies = response.data.data;
        }
        console.log(`Retrieved ${companies.length} companies from API`);
        // Only proceed with detail fetching if we got a reasonable number of companies
        if (companies.length > 0) {
            // For each company in the list, fetch complete details to get all fields
            const detailedCompanies = await Promise.all(companies.map(async (company)=>{
                try {
                    // Fetch detailed info for each company
                    const detailResponse = await getCompany(company.id);
                    return transformCompanyData(detailResponse.data);
                } catch (error) {
                    console.log(`Could not fetch details for company ${company.id}:`, error);
                    // Fall back to the list data if detail fetch fails
                    return transformCompanyData(company);
                }
            }));
            // Store companies in sessionStorage for quick access
            if ("TURBOPACK compile-time falsy", 0) {
                "TURBOPACK unreachable";
            }
            return detailedCompanies;
        } else {
            throw new Error('No companies returned from API');
        }
    } catch (error) {
        console.error('Error fetching companies from API:', error);
        // Check if we have cached data in sessionStorage
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        // Import the static data as last resort
        console.log('Falling back to static company data');
        const { companies } = await __turbopack_context__.r("[project]/src/data/jobsData.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
        return companies;
    }
}
function getCompany(companyId) {
    // Try both possible endpoints
    const urls = [
        `/api/v1/company/${companyId}/`,
        `/api/v1/companies/${companyId}/`,
        `/api/v1/college/default-college/companies/${companyId}/`
    ];
    // Try each URL in sequence until one works
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(urls[0]).catch((error1)=>{
        console.log(`First company endpoint failed: ${error1.message}, trying second...`);
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(urls[1]).catch((error2)=>{
            console.log(`Second company endpoint failed: ${error2.message}, trying third...`);
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(urls[2]);
        });
    });
}
function createCompany(companyData) {
    const formData = new FormData();
    // Append all fields to the FormData
    Object.keys(companyData).forEach((key)=>{
        // Handle file upload for logo
        if (key === 'logo' && companyData[key] instanceof File) {
            formData.append(key, companyData[key]);
        } else if (companyData[key] !== null && companyData[key] !== undefined) {
            formData.append(key, companyData[key]);
        }
    });
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post('/api/v1/companies/', formData, {
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    });
}
function updateCompany(companyId, companyData) {
    const formData = new FormData();
    // Append all fields to the FormData
    Object.keys(companyData).forEach((key)=>{
        // Handle file upload for logo
        if (key === 'logo' && companyData[key] instanceof File) {
            formData.append(key, companyData[key]);
        } else if (companyData[key] !== null && companyData[key] !== undefined) {
            formData.append(key, companyData[key]);
        }
    });
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].put(`/api/v1/companies/${companyId}/`, formData, {
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    });
}
function deleteCompany(companyId) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].delete(`/api/v1/companies/${companyId}/`);
}
function uploadCompanyLogo(companyId, logoFile) {
    const formData = new FormData();
    formData.append('logo', logoFile);
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/companies/${companyId}/upload-logo/`, formData, {
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    });
}
function getCompanyStats() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get('/api/v1/companies/stats/');
}
function getIndustries() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get('/api/v1/companies/industries/');
}
function transformCompanyData(backendData) {
    return {
        id: backendData.id,
        name: backendData.name,
        logo: backendData.logo || `https://via.placeholder.com/48x48/4285F4/FFFFFF?text=${backendData.name.charAt(0)}`,
        description: backendData.description || '',
        industry: backendData.industry || '',
        size: backendData.size || '',
        founded: backendData.founded || '',
        location: backendData.location || 'Location not specified',
        website: backendData.website || '',
        tier: backendData.tier || 'Tier 3',
        campus_recruiting: backendData.campus_recruiting || false,
        // Standardized field names
        totalActiveJobs: backendData.total_active_jobs || 0,
        totalApplicants: backendData.total_applicants || 0,
        totalHired: backendData.total_hired || 0,
        awaitedApproval: backendData.pending_approval || 0
    };
}
function getFollowersCount(companyId) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/companies/${companyId}/followers/count/`);
}
function followCompany(companyId, userId) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/api/v1/companies/${companyId}/followers/`, {
        user_id: userId
    });
}
function unfollowCompany(companyId, userId) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].delete(`/api/v1/companies/${companyId}/followers/`, {
        data: {
            user_id: userId
        }
    });
}
function checkFollowingStatus(companyId, userId) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/companies/${companyId}/followers/status/?user_id=${userId}`);
}
function getUserFollowedCompanies(userId) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/api/v1/users/${userId}/following/`);
}
function fetchSimpleCompanies() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get('/api/v1/companies/simple/').then((response)=>{
        if (response.data && response.data.success) {
            return response.data;
        }
        throw new Error('Failed to fetch companies');
    }).catch((error)=>{
        console.error('Error fetching companies:', error);
        // Return a fallback structure
        return {
            success: false,
            data: [],
            count: 0,
            error: error.message
        };
    });
}
}}),

};

//# sourceMappingURL=src_api_companies_c3c60fc1.js.map