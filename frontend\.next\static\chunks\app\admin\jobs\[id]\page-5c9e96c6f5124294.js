(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[111],{1243:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},4516:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},12486:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},13717:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},17576:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},19946:(e,a,t)=>{"use strict";t.d(a,{A:()=>p});var s=t(12115);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),r=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,a,t)=>t?t.toUpperCase():a.toLowerCase()),i=e=>{let a=r(e);return a.charAt(0).toUpperCase()+a.slice(1)},n=function(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return a.filter((e,a,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===a).join(" ").trim()},c=e=>{for(let a in e)if(a.startsWith("aria-")||"role"===a||"title"===a)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,s.forwardRef)((e,a)=>{let{color:t="currentColor",size:l=24,strokeWidth:r=2,absoluteStrokeWidth:i,className:o="",children:p,iconNode:m,...u}=e;return(0,s.createElement)("svg",{ref:a,...d,width:l,height:l,stroke:t,strokeWidth:i?24*Number(r)/Number(l):r,className:n("lucide",o),...!p&&!c(u)&&{"aria-hidden":"true"},...u},[...m.map(e=>{let[a,t]=e;return(0,s.createElement)(a,t)}),...Array.isArray(p)?p:[p]])}),p=(e,a)=>{let t=(0,s.forwardRef)((t,r)=>{let{className:c,...d}=t;return(0,s.createElement)(o,{ref:r,iconNode:a,className:n("lucide-".concat(l(i(e))),"lucide-".concat(e),c),...d})});return t.displayName=i(e),t}},25384:(e,a,t)=>{"use strict";t.d(a,{G_:()=>l});var s=t(95155);function l(e){let{description:a,className:t=""}=e,l=a?a.replace(/\n/g,"<br />").replace(/•/g,"•").replace(/\*\s/g,"• ").replace(/-\s/g,"• ").trim():"No description provided.";return(0,s.jsx)("div",{className:"text-gray-700 leading-relaxed ".concat(t),dangerouslySetInnerHTML:{__html:l}})}},29869:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},33786:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},34842:(e,a,t)=>{"use strict";t.d(a,{G$:()=>n,N6:()=>l,Om:()=>o,T4:()=>p,YQ:()=>i,_S:()=>c,lh:()=>d,vr:()=>r});var s=t(37719);function l(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=new URLSearchParams;e.page&&a.append("page",e.page),e.per_page&&a.append("per_page",e.per_page),e.job_type&&"ALL"!==e.job_type&&a.append("job_type",e.job_type),e.location&&"ALL"!==e.location&&a.append("location",e.location),e.salary_min&&a.append("salary_min",e.salary_min),e.search&&a.append("search",e.search);let t=a.toString();return s.A.get("/api/v1/college/default-college/jobs/".concat(t?"?".concat(t):""))}function r(e,a){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!Object.values(t).some(e=>e instanceof File))return s.A.post("/api/v1/college/default-college/jobs/".concat(e,"/apply/"),{cover_letter:a,additional_field_responses:t});{let l=new FormData;return l.append("cover_letter",a),Object.entries(t).forEach(e=>{let[a,t]=e;t instanceof File?l.append(a,t):l.append(a,JSON.stringify(t))}),s.A.post("/api/v1/college/default-college/jobs/".concat(e,"/apply/"),l,{headers:{"Content-Type":"multipart/form-data"}})}}function i(e){return s.A.get("/api/v1/college/default-college/jobs/".concat(e,"/"))}function n(){return s.A.get("/api/v1/college/default-college/jobs/applied/")}function c(e){return s.A.post("/api/v1/college/default-college/jobs/create/",e)}function d(e,a){return s.A.put("/api/v1/college/default-college/jobs/".concat(e,"/"),a)}function o(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=new URLSearchParams;e.page&&a.append("page",e.page),e.per_page&&a.append("per_page",e.per_page),e.search&&a.append("search",e.search),e.type&&"All"!==e.type&&a.append("job_type",e.type),e.minCTC&&a.append("salary_min",e.minCTC),e.maxCTC&&a.append("salary_max",e.maxCTC),e.minStipend&&a.append("stipend_min",e.minStipend),e.maxStipend&&a.append("stipend_max",e.maxStipend),e.location&&a.append("location",e.location),void 0!==e.is_published&&a.append("is_published",e.is_published),e.company_id&&a.append("company_id",e.company_id),e.company_name&&a.append("company_name",e.company_name);let t=a.toString(),l="/api/v1/college/default-college/jobs/admin/".concat(t?"?".concat(t):"");return console.log("\uD83C\uDF10 listJobsAdmin calling URL:",l,"with params:",e),s.A.get(l).then(e=>{var a,t,s,l,r,i;return console.log("\uD83C\uDF10 listJobsAdmin response:",{status:e.status,totalJobs:(null==(t=e.data)||null==(a=t.pagination)?void 0:a.total_count)||0,currentPage:(null==(l=e.data)||null==(s=l.pagination)?void 0:s.current_page)||1,totalPages:(null==(i=e.data)||null==(r=i.pagination)?void 0:r.total_pages)||1}),e}).catch(e=>{var a;throw console.error("\uD83C\uDF10 listJobsAdmin error:",e),console.error("\uD83C\uDF10 listJobsAdmin error response:",null==(a=e.response)?void 0:a.data),e})}function p(e){return s.A.patch("/api/v1/jobs/".concat(e,"/toggle-publish/"))}},35169:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},35695:(e,a,t)=>{"use strict";var s=t(18999);t.o(s,"useParams")&&t.d(a,{useParams:function(){return s.useParams}}),t.o(s,"usePathname")&&t.d(a,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(a,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(a,{useSearchParams:function(){return s.useSearchParams}})},37719:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});var s=t(23464);t(73983);let l=s.A.create({baseURL:"http://127.0.0.1:8000",headers:{"Content-Type":"application/json"}});l.interceptors.request.use(e=>{let a=localStorage.getItem("access_token");return a&&(e.headers.Authorization="Bearer ".concat(a)),e},e=>Promise.reject(e)),l.interceptors.response.use(e=>e,async e=>{var a;let t=e.config;if((null==(a=e.response)?void 0:a.status)===401&&!t._retry){t._retry=!0;try{let e=localStorage.getItem("refresh_token");if(e){let a=await s.A.post("http://127.0.0.1:8000/api/auth/token/refresh/",{refresh:e});return localStorage.setItem("access_token",a.data.access),t.headers.Authorization="Bearer ".concat(a.data.access),l(t)}}catch(e){console.error("Error refreshing token:",e),localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),window.location.href="/login"}}return Promise.reject(e)});let r=l},40646:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},54416:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},55868:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},69074:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},71007:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},71631:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>y});var s=t(95155),l=t(12115),r=t(35695),i=t(35169),n=t(13717),c=t(4516),d=t(17576),o=t(55868),p=t(69074),m=t(92657),u=t(12486),h=t(25384),x=t(34842);function y(e){var a,t;let{params:y}=e,g=parseInt((0,l.use)(y).id),f=(0,r.useRouter)(),[j,b]=(0,l.useState)(null),[v,N]=(0,l.useState)(!0),[_,k]=(0,l.useState)(null);return((0,l.useEffect)(()=>{let e=async()=>{try{console.log("Loading job data for ID:",g);let t=await (0,x.YQ)(g);if(console.log("Job API response:",t),t.data){var e,a;let s=t.data;console.log("Setting job data:",s);let l={id:s.id,title:s.title||"No title",description:s.description||"No description available",location:s.location||"Not specified",job_type:s.job_type||"Not specified",salary_min:s.salary_min||0,salary_max:s.salary_max||0,application_deadline:s.application_deadline||new Date().toISOString().split("T")[0],is_published:s.is_published||!1,company_name:s.company_name||(null==(e=s.company)?void 0:e.name)||"Unknown Company",requirements:Array.isArray(s.requirements)?s.requirements:"string"==typeof s.requirements?s.requirements.split(",").map(e=>e.trim()):[],skills:Array.isArray(s.skills)?s.skills:"string"==typeof s.skills?s.skills.split(",").map(e=>e.trim()):[],created_at:s.created_at||new Date().toISOString(),updated_at:s.updated_at||new Date().toISOString(),applications_count:s.applications_count||0,duration:s.duration||"Not specified",company_id:s.company_id||(null==(a=s.company)?void 0:a.id)||null};b(l)}}catch(e){console.error("Error loading job:",e),k("Failed to load job data. Please try again.")}finally{N(!1)}};g&&e()},[g]),v)?(0,s.jsx)("div",{className:"h-full overflow-y-auto",children:(0,s.jsx)("div",{className:"p-6",children:(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsx)("div",{className:"text-lg",children:"Loading job details..."})})})}):_?(0,s.jsx)("div",{className:"h-full overflow-y-auto",children:(0,s.jsx)("div",{className:"p-6",children:(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsxs)("div",{className:"text-red-600",children:["Error: ",_]})})})}):j?(0,s.jsx)("div",{className:"h-full overflow-y-auto",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsxs)("button",{onClick:()=>f.back(),className:"flex items-center text-gray-600 hover:text-gray-900 mb-4",children:[(0,s.jsx)(i.A,{className:"w-5 h-5 mr-2"}),"Back to Company Management"]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:j.title}),(0,s.jsxs)("p",{className:"text-gray-600 mt-2",children:[j.company_name," • Job ID: ",g]})]}),(0,s.jsxs)("div",{className:"flex space-x-3",children:[(0,s.jsx)("span",{className:"px-3 py-1 rounded-full text-sm font-medium ".concat(j.is_published?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"),children:j.is_published?"Published":"Draft"}),(0,s.jsxs)("button",{onClick:()=>f.push("/admin/jobs/edit/".concat(g)),className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center",children:[(0,s.jsx)(n.A,{className:"w-4 h-4 mr-2"}),"Edit Job"]})]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,s.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,s.jsxs)("div",{className:"bg-white shadow-sm rounded-lg p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Job Details"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(c.A,{className:"w-5 h-5 text-gray-400"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Location"}),(0,s.jsx)("p",{className:"font-medium",children:j.location})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(d.A,{className:"w-5 h-5 text-gray-400"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Type"}),(0,s.jsx)("p",{className:"font-medium",children:j.job_type})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(o.A,{className:"w-5 h-5 text-gray-400"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Salary"}),(0,s.jsx)("p",{className:"font-medium",children:j.salary_min&&j.salary_max?"$".concat(null==(a=j.salary_min)?void 0:a.toLocaleString()," - $").concat(null==(t=j.salary_max)?void 0:t.toLocaleString()):"Not specified"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(p.A,{className:"w-5 h-5 text-gray-400"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Deadline"}),(0,s.jsx)("p",{className:"font-medium",children:j.application_deadline?new Date(j.application_deadline).toLocaleDateString():"No deadline"})]})]})]}),j.duration&&(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(p.A,{className:"w-5 h-5 text-gray-400"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Duration"}),(0,s.jsx)("p",{className:"font-medium",children:j.duration})]})]})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium text-gray-900 mb-3",children:"Description"}),(0,s.jsx)(h.G_,{description:j.description,className:""})]})]}),j.requirements&&j.requirements.length>0&&(0,s.jsxs)("div",{className:"bg-white shadow-sm rounded-lg p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Requirements"}),(0,s.jsx)("ul",{className:"space-y-2",children:j.requirements.map((e,a)=>(0,s.jsxs)("li",{className:"flex items-start space-x-2",children:[(0,s.jsx)("span",{className:"w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"}),(0,s.jsx)("span",{className:"text-gray-700",children:e})]},a))})]}),j.skills&&j.skills.length>0&&(0,s.jsxs)("div",{className:"bg-white shadow-sm rounded-lg p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Required Skills"}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:j.skills.map((e,a)=>(0,s.jsx)("span",{className:"px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium",children:e},a))})]})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"bg-white shadow-sm rounded-lg p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Job Statistics"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Applications"}),(0,s.jsx)("span",{className:"font-semibold text-gray-900",children:j.applications_count})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Created"}),(0,s.jsx)("span",{className:"font-semibold text-gray-900",children:new Date(j.created_at).toLocaleDateString()})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Last Updated"}),(0,s.jsx)("span",{className:"font-semibold text-gray-900",children:new Date(j.updated_at).toLocaleDateString()})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Status"}),(0,s.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(j.is_published?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"),children:j.is_published?"Published":"Draft"})]})]})]}),(0,s.jsxs)("div",{className:"bg-white shadow-sm rounded-lg p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Actions"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("button",{onClick:()=>f.push("/admin/jobs/edit/".concat(g)),className:"w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center justify-center",children:[(0,s.jsx)(n.A,{className:"w-4 h-4 mr-2"}),"Edit Job"]}),(0,s.jsxs)("button",{onClick:()=>f.push("/admin/applications?job_id=".concat(g)),className:"w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 flex items-center justify-center",children:[(0,s.jsx)(m.A,{className:"w-4 h-4 mr-2"}),"View Applications"]}),(0,s.jsxs)("button",{className:"w-full bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center justify-center",children:[(0,s.jsx)(u.A,{className:"w-4 h-4 mr-2"}),"Send Message"]})]})]}),(0,s.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,s.jsx)("h4",{className:"font-semibold text-blue-900 mb-2",children:"Quick Info"}),(0,s.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-blue-700",children:"Job ID:"}),(0,s.jsx)("span",{className:"font-medium text-blue-900",children:j.id})]}),j.company_id&&(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-blue-700",children:"Company ID:"}),(0,s.jsx)("span",{className:"font-medium text-blue-900",children:j.company_id})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-blue-700",children:"Type:"}),(0,s.jsx)("span",{className:"font-medium text-blue-900",children:j.job_type})]})]})]})]})]})]})}):(0,s.jsx)("div",{className:"h-full overflow-y-auto",children:(0,s.jsx)("div",{className:"p-6",children:(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsx)("div",{className:"text-gray-600",children:"Job not found"})})})})}},72741:(e,a,t)=>{Promise.resolve().then(t.bind(t,71631))},75525:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},76517:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]])},81284:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},85339:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},92657:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{var a=a=>e(e.s=a);e.O(0,[3464,3983,8441,1684,7358],()=>a(72741)),_N_E=e.O()}]);