{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/api/errorHandler.js"], "sourcesContent": ["import { useNotification } from '../contexts/NotificationContext';\r\n\r\n// Error codes and their corresponding handlers\r\nexport const ERROR_PATTERNS = {\r\n  AUTHENTICATION: {\r\n    codes: [401],\r\n    keywords: ['unauthorized', 'authentication', 'token', 'login'],\r\n    handler: 'showAuthError'\r\n  },\r\n  SESSION_EXPIRED: {\r\n    codes: [401],\r\n    keywords: ['expired', 'invalid token', 'token expired'],\r\n    handler: 'showSessionExpiredModal'\r\n  },\r\n  PERMISSION_DENIED: {\r\n    codes: [403],\r\n    keywords: ['permission', 'forbidden', 'access denied'],\r\n    handler: 'showAuthError'\r\n  },\r\n  VALIDATION: {\r\n    codes: [400, 422],\r\n    keywords: ['validation', 'invalid', 'required'],\r\n    handler: 'showValidationError'\r\n  },\r\n  RESUME_REQUIRED: {\r\n    fields: ['resume'],\r\n    keywords: ['resume', 'must be uploaded', 'present in the student profile'],\r\n    handler: 'showMissingResumeModal'\r\n  },\r\n  PROFILE_INCOMPLETE: {\r\n    keywords: ['profile incomplete', 'missing profile', 'update profile'],\r\n    handler: 'showProfileIncompleteModal'\r\n  },\r\n  FILE_UPLOAD: {\r\n    keywords: ['file', 'upload', 'size', 'format', 'extension'],\r\n    handler: 'showFileUploadError'\r\n  },\r\n  NETWORK_ERROR: {\r\n    codes: ['NETWORK_ERROR', 'ECONNREFUSED', 'ERR_NETWORK'],\r\n    keywords: ['network', 'connection', 'timeout'],\r\n    handler: 'showNetworkError'\r\n  },\r\n  MAINTENANCE: {\r\n    codes: [503, 502],\r\n    keywords: ['maintenance', 'service unavailable', 'temporarily unavailable'],\r\n    handler: 'showMaintenanceModal'\r\n  }\r\n};\r\n\r\n// Smart error detection and handling\r\nexport const detectAndHandleError = (error, context = '', notificationHandlers) => {\r\n  const errorData = error?.response?.data || {};\r\n  const errorMessage = (errorData.detail || errorData.message || error.message || '').toLowerCase();\r\n  const statusCode = error?.response?.status;\r\n\r\n  // Check for specific error patterns\r\n  for (const [pattern, config] of Object.entries(ERROR_PATTERNS)) {\r\n    // Check status codes\r\n    if (config.codes && config.codes.includes(statusCode)) {\r\n      // Additional keyword check for more precision\r\n      if (config.keywords && !config.keywords.some(keyword => errorMessage.includes(keyword))) {\r\n        continue;\r\n      }\r\n      \r\n      return handleSpecificError(pattern, error, context, notificationHandlers);\r\n    }\r\n\r\n    // Check for field-specific errors (like resume)\r\n    if (config.fields && config.fields.some(field => errorData[field])) {\r\n      return handleSpecificError(pattern, error, context, notificationHandlers);\r\n    }\r\n\r\n    // Check keywords in error message\r\n    if (config.keywords && config.keywords.some(keyword => errorMessage.includes(keyword))) {\r\n      return handleSpecificError(pattern, error, context, notificationHandlers);\r\n    }\r\n  }\r\n\r\n  // Fallback to generic error handling\r\n  return handleGenericError(error, context, notificationHandlers);\r\n};\r\n\r\nconst handleSpecificError = (pattern, error, context, notificationHandlers) => {\r\n  const config = ERROR_PATTERNS[pattern];\r\n  const handlerName = config.handler;\r\n  \r\n  if (notificationHandlers[handlerName]) {\r\n    switch (handlerName) {\r\n      case 'showMissingResumeModal':\r\n        notificationHandlers.showMissingResumeModal();\r\n        break;\r\n      case 'showSessionExpiredModal':\r\n        notificationHandlers.showSessionExpiredModal();\r\n        break;\r\n      case 'showMaintenanceModal':\r\n        notificationHandlers.showMaintenanceModal();\r\n        break;\r\n      case 'showValidationError':\r\n        const errorData = error?.response?.data || {};\r\n        notificationHandlers.showValidationError(\r\n          `Validation Error ${context ? `in ${context}` : ''}`, \r\n          errorData\r\n        );\r\n        break;\r\n      case 'showAuthError':\r\n        const message = error?.response?.data?.detail || \r\n                       error?.response?.data?.message || \r\n                       `Authentication failed${context ? ` while ${context}` : ''}`;\r\n        notificationHandlers.showAuthError(message);\r\n        break;\r\n      case 'showFileUploadError':\r\n        notificationHandlers.showFileUploadError();\r\n        break;\r\n      case 'showNetworkError':\r\n        notificationHandlers.showNetworkError(error);\r\n        break;\r\n      case 'showProfileIncompleteModal':\r\n        notificationHandlers.showProfileIncompleteModal();\r\n        break;\r\n      default:\r\n        return handleGenericError(error, context, notificationHandlers);\r\n    }\r\n    return true; // Error was handled\r\n  }\r\n  \r\n  return false; // Error not handled\r\n};\r\n\r\nconst handleGenericError = (error, context, notificationHandlers) => {\r\n  if (notificationHandlers.handleApiError) {\r\n    notificationHandlers.handleApiError(error, context);\r\n    return true;\r\n  }\r\n  \r\n  // Ultimate fallback\r\n  console.error('Unhandled error:', error);\r\n  return false;\r\n};\r\n\r\n// Hook for easy error handling in components\r\nexport const useErrorHandler = () => {\r\n  const notificationHandlers = useNotification();\r\n  \r\n  const handleError = (error, context = '') => {\r\n    return detectAndHandleError(error, context, notificationHandlers);\r\n  };\r\n\r\n  return { handleError };\r\n};\r\n\r\n// Axios interceptor setup\r\nexport const setupErrorInterceptor = (axiosInstance, notificationHandlers) => {\r\n  axiosInstance.interceptors.response.use(\r\n    (response) => response,\r\n    (error) => {\r\n      // Automatically handle common errors\r\n      detectAndHandleError(error, 'API request', notificationHandlers);\r\n      return Promise.reject(error);\r\n    }\r\n  );\r\n};\r\n\r\nexport default {\r\n  detectAndHandleError,\r\n  useErrorHandler,\r\n  setupErrorInterceptor,\r\n  ERROR_PATTERNS\r\n}; "], "names": [], "mappings": ";;;;;;;AAAA;;;AAGO,MAAM,iBAAiB;IAC5B,gBAAgB;QACd,OAAO;YAAC;SAAI;QACZ,UAAU;YAAC;YAAgB;YAAkB;YAAS;SAAQ;QAC9D,SAAS;IACX;IACA,iBAAiB;QACf,OAAO;YAAC;SAAI;QACZ,UAAU;YAAC;YAAW;YAAiB;SAAgB;QACvD,SAAS;IACX;IACA,mBAAmB;QACjB,OAAO;YAAC;SAAI;QACZ,UAAU;YAAC;YAAc;YAAa;SAAgB;QACtD,SAAS;IACX;IACA,YAAY;QACV,OAAO;YAAC;YAAK;SAAI;QACjB,UAAU;YAAC;YAAc;YAAW;SAAW;QAC/C,SAAS;IACX;IACA,iBAAiB;QACf,QAAQ;YAAC;SAAS;QAClB,UAAU;YAAC;YAAU;YAAoB;SAAiC;QAC1E,SAAS;IACX;IACA,oBAAoB;QAClB,UAAU;YAAC;YAAsB;YAAmB;SAAiB;QACrE,SAAS;IACX;IACA,aAAa;QACX,UAAU;YAAC;YAAQ;YAAU;YAAQ;YAAU;SAAY;QAC3D,SAAS;IACX;IACA,eAAe;QACb,OAAO;YAAC;YAAiB;YAAgB;SAAc;QACvD,UAAU;YAAC;YAAW;YAAc;SAAU;QAC9C,SAAS;IACX;IACA,aAAa;QACX,OAAO;YAAC;YAAK;SAAI;QACjB,UAAU;YAAC;YAAe;YAAuB;SAA0B;QAC3E,SAAS;IACX;AACF;AAGO,MAAM,uBAAuB,CAAC,OAAO,UAAU,EAAE,EAAE;IACxD,MAAM,YAAY,OAAO,UAAU,QAAQ,CAAC;IAC5C,MAAM,eAAe,CAAC,UAAU,MAAM,IAAI,UAAU,OAAO,IAAI,MAAM,OAAO,IAAI,EAAE,EAAE,WAAW;IAC/F,MAAM,aAAa,OAAO,UAAU;IAEpC,oCAAoC;IACpC,KAAK,MAAM,CAAC,SAAS,OAAO,IAAI,OAAO,OAAO,CAAC,gBAAiB;QAC9D,qBAAqB;QACrB,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,CAAC,QAAQ,CAAC,aAAa;YACrD,8CAA8C;YAC9C,IAAI,OAAO,QAAQ,IAAI,CAAC,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAA,UAAW,aAAa,QAAQ,CAAC,WAAW;gBACvF;YACF;YAEA,OAAO,oBAAoB,SAAS,OAAO,SAAS;QACtD;QAEA,gDAAgD;QAChD,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,CAAA,QAAS,SAAS,CAAC,MAAM,GAAG;YAClE,OAAO,oBAAoB,SAAS,OAAO,SAAS;QACtD;QAEA,kCAAkC;QAClC,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAA,UAAW,aAAa,QAAQ,CAAC,WAAW;YACtF,OAAO,oBAAoB,SAAS,OAAO,SAAS;QACtD;IACF;IAEA,qCAAqC;IACrC,OAAO,mBAAmB,OAAO,SAAS;AAC5C;AAEA,MAAM,sBAAsB,CAAC,SAAS,OAAO,SAAS;IACpD,MAAM,SAAS,cAAc,CAAC,QAAQ;IACtC,MAAM,cAAc,OAAO,OAAO;IAElC,IAAI,oBAAoB,CAAC,YAAY,EAAE;QACrC,OAAQ;YACN,KAAK;gBACH,qBAAqB,sBAAsB;gBAC3C;YACF,KAAK;gBACH,qBAAqB,uBAAuB;gBAC5C;YACF,KAAK;gBACH,qBAAqB,oBAAoB;gBACzC;YACF,KAAK;gBACH,MAAM,YAAY,OAAO,UAAU,QAAQ,CAAC;gBAC5C,qBAAqB,mBAAmB,CACtC,CAAC,iBAAiB,EAAE,UAAU,CAAC,GAAG,EAAE,SAAS,GAAG,IAAI,EACpD;gBAEF;YACF,KAAK;gBACH,MAAM,UAAU,OAAO,UAAU,MAAM,UACxB,OAAO,UAAU,MAAM,WACvB,CAAC,qBAAqB,EAAE,UAAU,CAAC,OAAO,EAAE,SAAS,GAAG,IAAI;gBAC3E,qBAAqB,aAAa,CAAC;gBACnC;YACF,KAAK;gBACH,qBAAqB,mBAAmB;gBACxC;YACF,KAAK;gBACH,qBAAqB,gBAAgB,CAAC;gBACtC;YACF,KAAK;gBACH,qBAAqB,0BAA0B;gBAC/C;YACF;gBACE,OAAO,mBAAmB,OAAO,SAAS;QAC9C;QACA,OAAO,MAAM,oBAAoB;IACnC;IAEA,OAAO,OAAO,oBAAoB;AACpC;AAEA,MAAM,qBAAqB,CAAC,OAAO,SAAS;IAC1C,IAAI,qBAAqB,cAAc,EAAE;QACvC,qBAAqB,cAAc,CAAC,OAAO;QAC3C,OAAO;IACT;IAEA,oBAAoB;IACpB,QAAQ,KAAK,CAAC,oBAAoB;IAClC,OAAO;AACT;AAGO,MAAM,kBAAkB;;IAC7B,MAAM,uBAAuB,CAAA,GAAA,0IAAA,CAAA,kBAAe,AAAD;IAE3C,MAAM,cAAc,CAAC,OAAO,UAAU,EAAE;QACtC,OAAO,qBAAqB,OAAO,SAAS;IAC9C;IAEA,OAAO;QAAE;IAAY;AACvB;GARa;;QACkB,0IAAA,CAAA,kBAAe;;;AAUvC,MAAM,wBAAwB,CAAC,eAAe;IACnD,cAAc,YAAY,CAAC,QAAQ,CAAC,GAAG;qCACrC,CAAC,WAAa;;qCACd,CAAC;YACC,qCAAqC;YACrC,qBAAqB,OAAO,eAAe;YAC3C,OAAO,QAAQ,MAAM,CAAC;QACxB;;AAEJ;uCAEe;IACb;IACA;IACA;IACA;AACF", "debugId": null}}, {"offset": {"line": 233, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/api/client.js"], "sourcesContent": ["import axios from 'axios';\r\nimport { setupErrorInterceptor } from './errorHandler';\r\n\r\nconst client = axios.create({\r\n  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000',\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n});\r\n\r\n// Add a request interceptor to include the auth token\r\nclient.interceptors.request.use(\r\n  (config) => {\r\n    // Get the token from localStorage\r\n    const token = localStorage.getItem('access_token');\r\n    \r\n    // If token exists, add it to the Authorization header\r\n    if (token) {\r\n      config.headers['Authorization'] = `Bearer ${token}`;\r\n    }\r\n    \r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Add a response interceptor to handle 401 errors (token expired)\r\nclient.interceptors.response.use(\r\n  (response) => response,\r\n  async (error) => {\r\n    const originalRequest = error.config;\r\n    \r\n    // If error is 401 and we haven't tried to refresh the token yet\r\n    if (error.response?.status === 401 && !originalRequest._retry) {\r\n      originalRequest._retry = true;\r\n      \r\n      try {\r\n        // Get refresh token\r\n        const refreshToken = localStorage.getItem('refresh_token');\r\n        \r\n        if (refreshToken) {\r\n          // Try to get a new token\r\n          const response = await axios.post('http://127.0.0.1:8000/api/auth/token/refresh/', {\r\n            refresh: refreshToken\r\n          });\r\n          \r\n          // Store the new tokens\r\n          localStorage.setItem('access_token', response.data.access);\r\n          \r\n          // Update the Authorization header\r\n          originalRequest.headers['Authorization'] = `Bearer ${response.data.access}`;\r\n          \r\n          // Retry the original request\r\n          return client(originalRequest);\r\n        }\r\n      } catch (refreshError) {\r\n        console.error('Error refreshing token:', refreshError);\r\n        \r\n        // If token refresh fails, redirect to login\r\n        if (typeof window !== 'undefined') {\r\n          // Clear tokens\r\n          localStorage.removeItem('access_token');\r\n          localStorage.removeItem('refresh_token');\r\n          \r\n          // Redirect to login page\r\n          window.location.href = '/login';\r\n        }\r\n      }\r\n    }\r\n    \r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default client;\r\n"], "names": [], "mappings": ";;;AAIW;AAJX;AACA;;;AAEA,MAAM,SAAS,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC1B,SAAS,6DAAmC;IAC5C,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,sDAAsD;AACtD,OAAO,YAAY,CAAC,OAAO,CAAC,GAAG,CAC7B,CAAC;IACC,kCAAkC;IAClC,MAAM,QAAQ,aAAa,OAAO,CAAC;IAEnC,sDAAsD;IACtD,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;IACrD;IAEA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,kEAAkE;AAClE,OAAO,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC9B,CAAC,WAAa,UACd,OAAO;IACL,MAAM,kBAAkB,MAAM,MAAM;IAEpC,gEAAgE;IAChE,IAAI,MAAM,QAAQ,EAAE,WAAW,OAAO,CAAC,gBAAgB,MAAM,EAAE;QAC7D,gBAAgB,MAAM,GAAG;QAEzB,IAAI;YACF,oBAAoB;YACpB,MAAM,eAAe,aAAa,OAAO,CAAC;YAE1C,IAAI,cAAc;gBAChB,yBAAyB;gBACzB,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,iDAAiD;oBACjF,SAAS;gBACX;gBAEA,uBAAuB;gBACvB,aAAa,OAAO,CAAC,gBAAgB,SAAS,IAAI,CAAC,MAAM;gBAEzD,kCAAkC;gBAClC,gBAAgB,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,SAAS,IAAI,CAAC,MAAM,EAAE;gBAE3E,6BAA6B;gBAC7B,OAAO,OAAO;YAChB;QACF,EAAE,OAAO,cAAc;YACrB,QAAQ,KAAK,CAAC,2BAA2B;YAEzC,4CAA4C;YAC5C,wCAAmC;gBACjC,eAAe;gBACf,aAAa,UAAU,CAAC;gBACxB,aAAa,UAAU,CAAC;gBAExB,yBAAyB;gBACzB,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;QACF;IACF;IAEA,OAAO,QAAQ,MAAM,CAAC;AACxB;uCAGa", "debugId": null}}, {"offset": {"line": 304, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/api/optimized.js"], "sourcesContent": ["/**\r\n * Optimized API service for server-side pagination and filtering\r\n * Replaces inefficient client-side data loading patterns\r\n */\r\n\r\nimport client from './client';\r\n\r\n/**\r\n * Generic function for paginated API calls\r\n * @param {string} endpoint - API endpoint\r\n * @param {Object} params - Query parameters\r\n * @returns {Promise} API response\r\n */\r\nexport async function fetchPaginatedData(endpoint, params = {}) {\r\n  try {\r\n    const queryParams = new URLSearchParams();\r\n    \r\n    // Add pagination parameters - backend expects 'per_page' not 'page_size'\r\n    if (params.page) queryParams.append('page', params.page);\r\n    if (params.page_size) queryParams.append('per_page', params.page_size);\r\n    \r\n    // Add filter parameters\r\n    Object.keys(params).forEach(key => {\r\n      if (key !== 'page' && key !== 'page_size' && params[key]) {\r\n        queryParams.append(key, params[key]);\r\n      }\r\n    });\r\n    \r\n    const url = `${endpoint}?${queryParams.toString()}`;\r\n    const response = await client.get(url);\r\n    \r\n    // Handle different response formats from different endpoints\r\n    let data, pagination, metadata;\r\n    \r\n    if (response.data.data && response.data.pagination) {\r\n      // Format 1: {data: [...], pagination: {...}}\r\n      data = response.data.data;\r\n      pagination = {\r\n        current_page: response.data.pagination.current_page,\r\n        total_pages: response.data.pagination.total_pages,\r\n        total_count: response.data.pagination.total_count,\r\n        per_page: response.data.pagination.per_page,\r\n        has_next: response.data.pagination.has_next,\r\n        has_previous: response.data.pagination.has_previous\r\n      };\r\n      metadata = response.data.metadata || {};\r\n    } else if (response.data.results !== undefined) {\r\n      // Format 2: DRF pagination {count, next, previous, results} or {pagination: {}, results: []}\r\n      data = response.data.results;\r\n      \r\n      if (response.data.pagination) {\r\n        // Custom pagination object\r\n        pagination = {\r\n          current_page: response.data.pagination.page || params.page || 1,\r\n          total_pages: response.data.pagination.total_pages || 1,\r\n          total_count: response.data.pagination.total_count || 0,\r\n          per_page: response.data.pagination.page_size || params.page_size || 10,\r\n          has_next: response.data.pagination.has_next || false,\r\n          has_previous: response.data.pagination.has_previous || false\r\n        };\r\n      } else {\r\n        // Standard DRF pagination\r\n        const totalCount = response.data.count || 0;\r\n        const perPage = params.page_size || 10;\r\n        const currentPage = params.page || 1;\r\n        const totalPages = Math.ceil(totalCount / perPage);\r\n        \r\n        pagination = {\r\n          current_page: currentPage,\r\n          total_pages: totalPages,\r\n          total_count: totalCount,\r\n          per_page: perPage,\r\n          has_next: response.data.next ? true : false,\r\n          has_previous: response.data.previous ? true : false\r\n        };\r\n      }\r\n      \r\n      metadata = response.data.metadata || {};\r\n    } else {\r\n      // Fallback for other formats\r\n      data = response.data;\r\n      pagination = {\r\n        current_page: params.page || 1,\r\n        total_pages: 1,\r\n        total_count: Array.isArray(data) ? data.length : 0,\r\n        per_page: params.page_size || 10,\r\n        has_next: false,\r\n        has_previous: false\r\n      };\r\n      metadata = {};\r\n    }\r\n    \r\n    return {\r\n      success: true,\r\n      data: data || [],\r\n      pagination,\r\n      metadata\r\n    };\r\n  } catch (error) {\r\n    console.error(`Error fetching paginated data from ${endpoint}:`, error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Optimized company API functions\r\n */\r\nexport const companiesAPI = {\r\n  /**\r\n   * Fetch companies with server-side pagination and filtering\r\n   * @param {Object} params - Query parameters\r\n   * @returns {Promise} Companies data with pagination\r\n   */\r\n  async getCompanies(params = {}) {\r\n    try {\r\n      // Try the optimized endpoint first\r\n      return await fetchPaginatedData('/api/v1/companies/optimized/', params);\r\n    } catch (error) {\r\n      console.log('Optimized endpoint failed, trying fallback endpoints...');\r\n      \r\n      // Fallback to regular companies endpoint with manual pagination\r\n      try {\r\n        const { fetchCompanies } = await import('./companies');\r\n        const allCompanies = await fetchCompanies();\r\n        \r\n        // Apply client-side filtering and pagination as fallback\r\n        let filteredCompanies = allCompanies;\r\n        \r\n        // Apply search filter\r\n        if (params.search) {\r\n          const searchTerm = params.search.toLowerCase();\r\n          filteredCompanies = filteredCompanies.filter(company =>\r\n            company.name.toLowerCase().includes(searchTerm) ||\r\n            company.industry.toLowerCase().includes(searchTerm) ||\r\n            company.description.toLowerCase().includes(searchTerm)\r\n          );\r\n        }\r\n        \r\n        // Apply tier filter\r\n        if (params.tier && params.tier !== 'ALL') {\r\n          filteredCompanies = filteredCompanies.filter(company => company.tier === params.tier);\r\n        }\r\n        \r\n        // Apply industry filter\r\n        if (params.industry && params.industry !== 'ALL') {\r\n          filteredCompanies = filteredCompanies.filter(company => company.industry === params.industry);\r\n        }\r\n        \r\n        // Apply sorting\r\n        if (params.ordering) {\r\n          filteredCompanies.sort((a, b) => {\r\n            switch (params.ordering) {\r\n              case 'name':\r\n                return a.name.localeCompare(b.name);\r\n              case '-total_active_jobs':\r\n                return (b.totalActiveJobs || 0) - (a.totalActiveJobs || 0);\r\n              case '-total_applicants':\r\n                return (b.totalApplicants || 0) - (a.totalApplicants || 0);\r\n              case 'tier':\r\n                return a.tier.localeCompare(b.tier);\r\n              default:\r\n                return 0;\r\n            }\r\n          });\r\n        }\r\n        \r\n        // Apply pagination\r\n        const page = params.page || 1;\r\n        const pageSize = params.page_size || 10;\r\n        const startIndex = (page - 1) * pageSize;\r\n        const endIndex = startIndex + pageSize;\r\n        const paginatedCompanies = filteredCompanies.slice(startIndex, endIndex);\r\n        \r\n        // Calculate pagination info\r\n        const totalCount = filteredCompanies.length;\r\n        const totalPages = Math.ceil(totalCount / pageSize);\r\n        \r\n        return {\r\n          success: true,\r\n          data: paginatedCompanies,\r\n          pagination: {\r\n            current_page: page,\r\n            total_pages: totalPages,\r\n            total_count: totalCount,\r\n            per_page: pageSize,\r\n            has_next: page < totalPages,\r\n            has_previous: page > 1\r\n          },\r\n          metadata: {}\r\n        };\r\n      } catch (fallbackError) {\r\n        console.error('All company endpoints failed:', fallbackError);\r\n        throw fallbackError;\r\n      }\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get company statistics\r\n   * @param {boolean} forceRefresh - Force refresh cache\r\n   * @returns {Promise} Company statistics\r\n   */\r\n  async getCompanyStats(forceRefresh = false) {\r\n    try {\r\n      // Try the working endpoint from companies.js\r\n      const response = await client.get('/api/v1/companies/stats/');\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error fetching company stats from primary endpoint:', error);\r\n      \r\n      // Try alternative endpoints\r\n      const endpoints = [\r\n        '/api/v1/stats/companies/',\r\n        '/api/v1/jobs/stats/'\r\n      ];\r\n      \r\n      for (const endpoint of endpoints) {\r\n        try {\r\n          const response = await client.get(endpoint);\r\n          if (response.data) {\r\n            console.log(`Successfully fetched stats from ${endpoint}`);\r\n            return response.data;\r\n          }\r\n        } catch (altError) {\r\n          console.log(`Failed to fetch from ${endpoint}: ${altError.message}`);\r\n        }\r\n      }\r\n      \r\n      // Final fallback - calculate from companies data\r\n      console.log('Falling back to calculated stats from companies data');\r\n      return this.calculateStatsFromCompanies();\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Calculate statistics from companies data as fallback\r\n   * @returns {Promise} Calculated statistics\r\n   */\r\n  async calculateStatsFromCompanies() {\r\n    try {\r\n      // Import companies API to avoid circular dependencies\r\n      const { fetchCompanies } = await import('./companies');\r\n      const companies = await fetchCompanies();\r\n      \r\n      // Calculate basic statistics\r\n      const stats = {\r\n        total: companies.length,\r\n        active_jobs: companies.reduce((sum, company) => sum + (company.totalActiveJobs || 0), 0),\r\n        campus_recruiting: companies.filter(c => c.campus_recruiting).length,\r\n        tier1: companies.filter(c => c.tier === 'Tier 1').length,\r\n        tier2: companies.filter(c => c.tier === 'Tier 2').length,\r\n        tier3: companies.filter(c => c.tier === 'Tier 3').length,\r\n      };\r\n      \r\n      return stats;\r\n    } catch (error) {\r\n      console.error('Failed to calculate stats from companies:', error);\r\n      return {\r\n        total: 0,\r\n        active_jobs: 0,\r\n        campus_recruiting: 0,\r\n        tier1: 0,\r\n        tier2: 0,\r\n        tier3: 0\r\n      };\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Search companies with optimized backend search\r\n   * @param {string} searchTerm - Search term\r\n   * @param {Object} filters - Additional filters\r\n   * @param {number} page - Page number\r\n   * @param {number} pageSize - Page size\r\n   * @returns {Promise} Search results\r\n   */\r\n  async searchCompanies(searchTerm, filters = {}, page = 1, pageSize = 20) {\r\n    const params = {\r\n      search: searchTerm,\r\n      page,\r\n      page_size: pageSize,\r\n      ...filters\r\n    };\r\n    return this.getCompanies(params);\r\n  }\r\n};\r\n\r\n/**\r\n * Optimized student API functions\r\n */\r\nexport const studentsAPI = {\r\n  /**\r\n   * Fetch students with server-side pagination and filtering\r\n   * @param {Object} params - Query parameters\r\n   * @returns {Promise} Students data with pagination\r\n   */\r\n  async getStudents(params = {}) {\r\n    try {\r\n      // Map frontend parameter names to backend expected names\r\n      const backendParams = { ...params };\r\n      \r\n      // Special case for getting year stats where we need all records\r\n      const isCountRequest = params.count_only === true;\r\n      if (isCountRequest) {\r\n        // When just counting, use a larger page size to get more comprehensive stats\r\n        backendParams.per_page = 1000;\r\n      }\r\n      \r\n      // Ensure proper parameter mapping\r\n      if (params.page_size) {\r\n        backendParams.per_page = params.page_size;\r\n        delete backendParams.page_size;\r\n      }\r\n      \r\n      // Remove our custom param before sending to backend\r\n      if (backendParams.count_only) {\r\n        delete backendParams.count_only;\r\n      }\r\n      \r\n      // Use the optimized endpoint with proper parameter mapping\r\n      const response = await fetchPaginatedData('/api/accounts/students/optimized/', backendParams);\r\n      \r\n      // For count requests, make sure metadata includes year breakdowns\r\n      if (isCountRequest && !response.metadata?.year_counts && response.data) {\r\n        // Calculate year counts from the returned data if the backend didn't provide it\r\n        const yearCounts = {};\r\n        response.data.forEach(student => {\r\n          const year = student.passout_year || student.graduation_year;\r\n          if (year) {\r\n            yearCounts[year] = (yearCounts[year] || 0) + 1;\r\n          }\r\n        });\r\n        \r\n        if (!response.metadata) response.metadata = {};\r\n        response.metadata.year_counts = yearCounts;\r\n      }\r\n      \r\n      // Ensure consistent response format\r\n      return {\r\n        success: true,\r\n        data: response.data || [],\r\n        pagination: {\r\n          current_page: response.pagination?.current_page || params.page || 1,\r\n          total_pages: response.pagination?.total_pages || 1,\r\n          total_count: response.pagination?.total_count || 0,\r\n          per_page: response.pagination?.per_page || params.page_size || 10,\r\n          has_next: response.pagination?.has_next || false,\r\n          has_previous: response.pagination?.has_previous || false\r\n        },\r\n        metadata: response.metadata || {}\r\n      };\r\n    } catch (error) {\r\n      console.error('Error in studentsAPI.getStudents:', error);\r\n      \r\n      // Fallback to regular students endpoint\r\n      try {\r\n        const fallbackResponse = await fetchPaginatedData('/api/accounts/students/', params);\r\n        return fallbackResponse;\r\n      } catch (fallbackError) {\r\n        console.error('Fallback endpoint also failed:', fallbackError);\r\n        throw fallbackError;\r\n      }\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get student statistics\r\n   * @param {boolean} forceRefresh - Force refresh cache\r\n   * @returns {Promise} Student statistics\r\n   */\r\n  async getStudentStats(forceRefresh = false) {\r\n    try {\r\n      // Use existing student endpoints instead of non-existent metrics endpoint\r\n      const response = await client.get('/api/accounts/students/stats/');\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error fetching student stats:', error);\r\n      // Return default stats if endpoint fails\r\n      return {\r\n        total: 0,\r\n        active: 0,\r\n        graduated: 0,\r\n        placed: 0\r\n      };\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get department statistics\r\n   * @param {boolean} forceRefresh - Force refresh cache\r\n   * @returns {Promise} Department statistics\r\n   */\r\n  async getDepartmentStats(forceRefresh = false) {\r\n    try {\r\n      // Use existing department endpoints instead of non-existent metrics endpoint\r\n      const response = await client.get('/api/accounts/departments/stats/');\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error fetching department stats:', error);\r\n      // Return default stats if endpoint fails\r\n      return {\r\n        total: 0,\r\n        departments: []\r\n      };\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Search students with optimized backend search\r\n   * @param {string} searchTerm - Search term\r\n   * @param {Object} filters - Additional filters\r\n   * @param {number} page - Page number\r\n   * @param {number} pageSize - Page size\r\n   * @returns {Promise} Search results\r\n   */\r\n  async searchStudents(searchTerm, filters = {}, page = 1, pageSize = 20) {\r\n    const params = {\r\n      search: searchTerm,\r\n      page,\r\n      page_size: pageSize,\r\n      ...filters\r\n    };\r\n    return this.getStudents(params);\r\n  },\r\n\r\n  /**\r\n   * Get single student\r\n   * @param {string|number} id - Student ID\r\n   * @returns {Promise} Student data\r\n   */\r\n  async getStudent(id) {\r\n    try {\r\n      const response = await client.get(`/api/accounts/students/${id}/`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error fetching student:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Update student\r\n   * @param {string|number} id - Student ID\r\n   * @param {Object} data - Student data to update\r\n   * @returns {Promise} Updated student data\r\n   */\r\n  async updateStudent(id, data) {\r\n    try {\r\n      console.log('updateStudent called with:', { id, data });\r\n\r\n      // Clean data to ensure proper format\r\n      const cleanedData = { ...data };\r\n      \r\n      // Ensure numeric fields are properly formatted\r\n      ['joining_year', 'passout_year'].forEach(field => {\r\n        if (cleanedData[field] !== null && cleanedData[field] !== undefined) {\r\n          const num = parseInt(cleanedData[field]);\r\n          cleanedData[field] = isNaN(num) ? null : num;\r\n        }\r\n      });\r\n\r\n      // Use standardized field names for student data\r\n      const stringFields = [\r\n        'first_name', 'last_name', 'student_id', 'contact_email', 'phone', 'branch', 'gpa',\r\n        'date_of_birth', 'address', 'city', 'district', 'state', 'pincode', 'country',\r\n        'parent_contact', 'education', 'skills',\r\n        'tenth_cgpa', 'tenth_percentage', 'tenth_board', 'tenth_school', 'tenth_year_of_passing', \r\n        'tenth_location', 'tenth_specialization',\r\n        'twelfth_cgpa', 'twelfth_percentage', 'twelfth_board', 'twelfth_school', 'twelfth_year_of_passing',\r\n        'twelfth_location', 'twelfth_specialization'\r\n      ];\r\n\r\n      stringFields.forEach(field => {\r\n        if (cleanedData[field] !== null && cleanedData[field] !== undefined) {\r\n          cleanedData[field] = String(cleanedData[field]).trim();\r\n        }\r\n      });\r\n\r\n      // Remove undefined values\r\n      Object.keys(cleanedData).forEach(key => {\r\n        if (cleanedData[key] === undefined) {\r\n          delete cleanedData[key];\r\n        }\r\n      });\r\n\r\n      console.log('Cleaned data being sent:', cleanedData);\r\n\r\n      // Try the ViewSet endpoint first (more RESTful)\r\n      try {\r\n        console.log('Trying ViewSet endpoint:', `/api/accounts/profiles/${id}/`);\r\n        const response = await client.patch(`/api/accounts/profiles/${id}/`, cleanedData);\r\n        console.log('ViewSet endpoint success:', response.data);\r\n        return response.data;\r\n      } catch (error) {\r\n        console.error('ViewSet endpoint failed:', {\r\n          status: error.response?.status,\r\n          statusText: error.response?.statusText,\r\n          data: error.response?.data\r\n        });\r\n\r\n        // If ViewSet fails, try the fallback endpoint\r\n        try {\r\n          console.log('Trying fallback endpoint:', `/api/accounts/students/${id}/update/`);\r\n          const response = await client.patch(`/api/accounts/students/${id}/update/`, cleanedData);\r\n          console.log('Fallback endpoint success:', response.data);\r\n          return response.data;\r\n        } catch (updateError) {\r\n          console.error('Failed to update student via both endpoints:', {\r\n            viewSetError: {\r\n              status: error.response?.status,\r\n              data: error.response?.data\r\n            },\r\n            updateViewError: {\r\n              status: updateError.response?.status,\r\n              data: updateError.response?.data\r\n            }\r\n          });\r\n\r\n          // Throw the more specific error\r\n          const primaryError = updateError.response?.status === 400 ? updateError : error;\r\n          throw primaryError;\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Error updating student:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Upload resume for student\r\n   * @param {File} file - Resume file\r\n   * @returns {Promise} Upload response\r\n   */\r\n  async uploadResume(file) {\r\n    try {\r\n      const formData = new FormData();\r\n      formData.append('resume', file);\r\n\r\n      const response = await client.patch('/api/auth/profile/', formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data',\r\n        },\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error uploading resume:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Delete resume for student\r\n   * @param {string|number} resumeId - Resume ID\r\n   * @returns {Promise} Delete response\r\n   */\r\n  async deleteResume(resumeId) {\r\n    try {\r\n      const response = await client.delete(`/api/accounts/resumes/${resumeId}/`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error deleting resume:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Upload certificate for student\r\n   * @param {File} file - Certificate file\r\n   * @param {string} type - Certificate type (tenth/twelfth)\r\n   * @returns {Promise} Upload response\r\n   */\r\n  async uploadCertificate(file, type) {\r\n    try {\r\n      const formData = new FormData();\r\n      formData.append(`${type}_certificate`, file);\r\n\r\n      const response = await client.patch('/api/auth/profile/', formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data',\r\n        },\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error uploading certificate:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Upload semester marksheet for student\r\n   * @param {File} file - Marksheet file\r\n   * @param {number} semester - Semester number\r\n   * @returns {Promise} Upload response\r\n   */\r\n  async uploadSemesterMarksheet(file, semester) {\r\n    try {\r\n      const formData = new FormData();\r\n      formData.append('marksheet', file);\r\n      formData.append('semester', semester);\r\n\r\n      const response = await client.post('/api/accounts/semester-marksheets/', formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data',\r\n        },\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error uploading semester marksheet:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get current user profile data\r\n   * @returns {Promise} User profile data\r\n   */\r\n  async getUserData() {\r\n    try {\r\n      const response = await client.get('/api/auth/profile/');\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error fetching user data:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Update user profile data\r\n   * @param {Object} data - Profile data to update\r\n   * @returns {Promise} Updated profile data\r\n   */\r\n  async updateUserProfile(data) {\r\n    try {\r\n      const response = await client.patch('/api/auth/profile/', data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error updating user profile:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Update user password\r\n   * @param {Object} passwordData - Password update data\r\n   * @returns {Promise} Update response\r\n   */\r\n  async updateUserPassword(passwordData) {\r\n    try {\r\n      // Use the correct endpoint that now exists in backend\r\n      const response = await client.post('/api/auth/change-password/', passwordData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Password update error:', error);\r\n      throw error;\r\n    }\r\n  }\r\n};\r\n\r\n/**\r\n * Admin API functions\r\n */\r\nexport const adminAPI = {\r\n  /**\r\n   * Update system settings (admin only)\r\n   * @param {Object} settings - System settings to update\r\n   * @returns {Promise} Update response\r\n   */\r\n  async updateSystemSettings(settings) {\r\n    try {\r\n      const response = await client.post('/api/admin/system-settings/', settings);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error updating system settings:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get system settings (admin only)\r\n   * @returns {Promise} System settings\r\n   */\r\n  async getSystemSettings() {\r\n    try {\r\n      const response = await client.get('/api/admin/system-settings/');\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error fetching system settings:', error);\r\n      throw error;\r\n    }\r\n  }\r\n};\r\n\r\n/**\r\n * Optimized jobs API functions\r\n */\r\nexport const jobsAPI = {\r\n  /**\r\n   * Fetch jobs with server-side pagination and filtering\r\n   * @param {Object} params - Query parameters\r\n   * @returns {Promise} Jobs data with pagination\r\n   */\r\n  async getJobs(params = {}) {\r\n    return fetchPaginatedData('/api/v1/jobs/', params);\r\n  },\r\n\r\n  /**\r\n   * Get job statistics\r\n   * @param {boolean} forceRefresh - Force refresh cache\r\n   * @returns {Promise} Job statistics\r\n   */\r\n  async getJobStats(forceRefresh = false) {\r\n    try {\r\n      // Use existing job stats endpoint\r\n      const response = await client.get('/api/v1/jobs/stats/');\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error fetching job stats:', error);\r\n      return {\r\n        total: 0,\r\n        active: 0,\r\n        expired: 0,\r\n        applications: 0\r\n      };\r\n    }\r\n  }\r\n};\r\n\r\n/**\r\n * Dashboard API functions\r\n */\r\nexport const dashboardAPI = {\r\n  /**\r\n   * Get dashboard statistics\r\n   * @param {boolean} forceRefresh - Force refresh cache\r\n   * @returns {Promise} Dashboard statistics\r\n   */\r\n  async getDashboardStats(forceRefresh = false) {\r\n    try {\r\n      // Aggregate stats from individual endpoints\r\n      const [companyStats, jobStats] = await Promise.allSettled([\r\n        companiesAPI.getCompanyStats(),\r\n        jobsAPI.getJobStats()\r\n      ]);\r\n      \r\n      return {\r\n        companies: companyStats.status === 'fulfilled' ? companyStats.value : {},\r\n        jobs: jobStats.status === 'fulfilled' ? jobStats.value : {},\r\n        timestamp: new Date().toISOString()\r\n      };\r\n    } catch (error) {\r\n      console.error('Error fetching dashboard stats:', error);\r\n      return {\r\n        companies: {},\r\n        jobs: {},\r\n        timestamp: new Date().toISOString()\r\n      };\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get placement statistics\r\n   * @param {boolean} forceRefresh - Force refresh cache\r\n   * @returns {Promise} Placement statistics\r\n   */\r\n  async getPlacementStats(forceRefresh = false) {\r\n    try {\r\n      // Use existing placement endpoint if available\r\n      const response = await client.get('/api/v1/placements/stats/');\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error fetching placement stats:', error);\r\n      return {\r\n        total_placements: 0,\r\n        placement_rate: 0,\r\n        average_package: 0,\r\n        top_recruiters: []\r\n      };\r\n    }\r\n  }\r\n};\r\n\r\n/**\r\n * Student Metrics API functions\r\n */\r\nexport const studentMetricsAPI = {\r\n  // Get enhanced student metrics\r\n  getEnhancedStudentMetrics: async (type = 'enhanced_student_stats', refresh = false) => {\r\n    try {\r\n      const params = { type };\r\n      if (refresh) params.refresh = 'true';\r\n      \r\n      const response = await client.get('/api/v1/metrics/students/enhanced/', { params });\r\n      return {\r\n        success: true,\r\n        data: response.data\r\n      };\r\n    } catch (error) {\r\n      console.error('Error fetching enhanced student metrics:', error);\r\n      return {\r\n        success: false,\r\n        error: error.response?.data?.message || 'Failed to fetch enhanced student metrics'\r\n      };\r\n    }\r\n  },\r\n\r\n  // Get department-wise student statistics\r\n  getDepartmentStats: async (department = null, refresh = false) => {\r\n    try {\r\n      const params = {};\r\n      if (department) params.department = department;\r\n      if (refresh) params.refresh = 'true';\r\n      \r\n      const response = await client.get('/api/v1/metrics/students/departments/', { params });\r\n      return {\r\n        success: true,\r\n        data: response.data\r\n      };\r\n    } catch (error) {\r\n      console.error('Error fetching department stats:', error);\r\n      return {\r\n        success: false,\r\n        error: error.response?.data?.message || 'Failed to fetch department statistics'\r\n      };\r\n    }\r\n  },\r\n\r\n  // Get year-wise student statistics\r\n  getYearStats: async (year = null, refresh = false, department = null) => {\r\n    try {\r\n      const params = {};\r\n      if (year) params.year = year;\r\n      if (department) params.department = department;\r\n      if (refresh) params.refresh = 'true';\r\n      \r\n      const response = await client.get('/api/v1/metrics/students/years/', { params });\r\n      return {\r\n        success: true,\r\n        data: response.data\r\n      };\r\n    } catch (error) {\r\n      console.error('Error fetching year stats:', error);\r\n      \r\n      // Provide fallback data structure to prevent frontend crashes\r\n      const fallbackData = {\r\n        years: [],\r\n        current_year: new Date().getFullYear(),\r\n        department_filter: department || null,\r\n        last_updated: new Date().toISOString(),\r\n        error_fallback: true\r\n      };\r\n      \r\n      // If it's a server error (500), return fallback data instead of error\r\n      if (error.response?.status === 500) {\r\n        console.warn('Server error for year stats, using fallback data');\r\n        return {\r\n          success: true,\r\n          data: fallbackData,\r\n          fallback: true\r\n        };\r\n      }\r\n      \r\n      return {\r\n        success: false,\r\n        error: error.response?.data?.message || 'Failed to fetch year statistics',\r\n        fallbackData\r\n      };\r\n    }\r\n  },\r\n\r\n  // Get student performance analytics\r\n  getPerformanceAnalytics: async () => {\r\n    try {\r\n      const response = await client.get('/api/v1/metrics/students/performance/');\r\n      return {\r\n        success: true,\r\n        data: response.data\r\n      };\r\n    } catch (error) {\r\n      console.error('Error fetching performance analytics:', error);\r\n      return {\r\n        success: false,\r\n        error: error.response?.data?.message || 'Failed to fetch performance analytics'\r\n      };\r\n    }\r\n  },\r\n\r\n  // Get all student analytics in one call\r\n  getAllStudentAnalytics: async (refresh = false) => {\r\n    try {\r\n      const [enhancedStats, departmentStats, yearStats, performanceStats] = await Promise.allSettled([\r\n        studentMetricsAPI.getEnhancedStudentMetrics('enhanced_student_stats', refresh),\r\n        studentMetricsAPI.getDepartmentStats(null, refresh),\r\n        studentMetricsAPI.getYearStats(null, refresh),\r\n        studentMetricsAPI.getPerformanceAnalytics()\r\n      ]);\r\n\r\n      const results = {\r\n        enhanced: enhancedStats.status === 'fulfilled' ? enhancedStats.value.data : null,\r\n        departments: departmentStats.status === 'fulfilled' ? departmentStats.value.data : null,\r\n        years: yearStats.status === 'fulfilled' ? yearStats.value.data : null,\r\n        performance: performanceStats.status === 'fulfilled' ? performanceStats.value.data : null,\r\n        errors: []\r\n      };\r\n\r\n      // Collect any errors and provide fallbacks\r\n      [enhancedStats, departmentStats, yearStats, performanceStats].forEach((result, index) => {\r\n        const names = ['enhanced', 'departments', 'years', 'performance'];\r\n        const name = names[index];\r\n        \r\n        if (result.status === 'rejected') {\r\n          results.errors.push(`${name}: ${result.reason.message || 'Unknown error'}`);\r\n        } else if (result.value?.fallback) {\r\n          // If we got fallback data, note it but still use the data\r\n          results.errors.push(`${name}: Using fallback data due to server error`);\r\n        } else if (result.value && !result.value.success && result.value.fallbackData) {\r\n          // Use fallback data if API failed but provided fallback\r\n          results[name] = result.value.fallbackData;\r\n          results.errors.push(`${name}: ${result.value.error || 'API error'}, using fallback`);\r\n        }\r\n      });\r\n\r\n      // Provide default empty structures if data is null\r\n      if (!results.enhanced) {\r\n        results.enhanced = { overview: {}, last_updated: new Date().toISOString() };\r\n      }\r\n      if (!results.departments) {\r\n        results.departments = { departments: [], last_updated: new Date().toISOString() };\r\n      }\r\n      if (!results.years) {\r\n        results.years = { years: [], current_year: new Date().getFullYear(), last_updated: new Date().toISOString() };\r\n      }\r\n      if (!results.performance) {\r\n        results.performance = { performance_categories: {}, last_updated: new Date().toISOString() };\r\n      }\r\n\r\n      return {\r\n        success: true,\r\n        data: results,\r\n        last_updated: new Date().toISOString(),\r\n        has_errors: results.errors.length > 0\r\n      };\r\n    } catch (error) {\r\n      console.error('Error fetching all student analytics:', error);\r\n      \r\n      // Provide complete fallback structure\r\n      const fallbackResults = {\r\n        enhanced: { overview: {}, last_updated: new Date().toISOString() },\r\n        departments: { departments: [], last_updated: new Date().toISOString() },\r\n        years: { years: [], current_year: new Date().getFullYear(), last_updated: new Date().toISOString() },\r\n        performance: { performance_categories: {}, last_updated: new Date().toISOString() },\r\n        errors: ['Failed to fetch student analytics data - using fallback']\r\n      };\r\n      \r\n      return {\r\n        success: true, // Return success with fallback data to prevent frontend crashes\r\n        data: fallbackResults,\r\n        last_updated: new Date().toISOString(),\r\n        fallback: true,\r\n        has_errors: true\r\n      };\r\n    }\r\n  },\r\n\r\n  // Refresh all student metrics cache\r\n  refreshAllMetrics: async () => {\r\n    try {\r\n      const refreshPromises = [\r\n        studentMetricsAPI.getEnhancedStudentMetrics('enhanced_student_stats', true),\r\n        studentMetricsAPI.getDepartmentStats(null, true),\r\n        studentMetricsAPI.getYearStats(null, true)\r\n      ];\r\n\r\n      await Promise.all(refreshPromises);\r\n      return { \r\n        success: true, \r\n        message: 'All student metrics refreshed successfully' \r\n      };\r\n    } catch (error) {\r\n      console.error('Error refreshing student metrics:', error);\r\n      return {\r\n        success: false,\r\n        error: 'Failed to refresh student metrics'\r\n      };\r\n    }\r\n  }\r\n};\r\n\r\n/**\r\n * Utility functions for pagination\r\n */\r\nexport const paginationUtils = {\r\n  /**\r\n   * Calculate pagination info\r\n   * @param {number} currentPage - Current page\r\n   * @param {number} totalPages - Total pages\r\n   * @param {number} totalCount - Total items\r\n   * @param {number} pageSize - Items per page\r\n   * @returns {Object} Pagination info\r\n   */\r\n  calculatePaginationInfo(currentPage, totalPages, totalCount, pageSize) {\r\n    return {\r\n      currentPage,\r\n      totalPages,\r\n      totalCount,\r\n      pageSize,\r\n      startIndex: (currentPage - 1) * pageSize + 1,\r\n      endIndex: Math.min(currentPage * pageSize, totalCount),\r\n      hasNext: currentPage < totalPages,\r\n      hasPrevious: currentPage > 1\r\n    };\r\n  },\r\n\r\n  /**\r\n   * Generate page numbers for pagination component\r\n   * @param {number} currentPage - Current page\r\n   * @param {number} totalPages - Total pages\r\n   * @param {number} maxVisible - Maximum visible page numbers\r\n   * @returns {Array} Array of page numbers\r\n   */\r\n  generatePageNumbers(currentPage, totalPages, maxVisible = 5) {\r\n    const pages = [];\r\n    const half = Math.floor(maxVisible / 2);\r\n    \r\n    let start = Math.max(1, currentPage - half);\r\n    let end = Math.min(totalPages, start + maxVisible - 1);\r\n    \r\n    // Adjust start if we're near the end\r\n    if (end - start + 1 < maxVisible) {\r\n      start = Math.max(1, end - maxVisible + 1);\r\n    }\r\n    \r\n    for (let i = start; i <= end; i++) {\r\n      pages.push(i);\r\n    }\r\n    \r\n    return pages;\r\n  }\r\n};\r\n\r\n/**\r\n * Cache management utilities\r\n */\r\nexport const cacheUtils = {\r\n  /**\r\n   * Clear all cached data\r\n   */\r\n  clearAllCache() {\r\n    // Clear localStorage cache\r\n    const keys = Object.keys(localStorage);\r\n    keys.forEach(key => {\r\n      if (key.startsWith('cache_') || key.includes('_data') || key.includes('_timestamp')) {\r\n        localStorage.removeItem(key);\r\n      }\r\n    });\r\n    \r\n    // Clear sessionStorage cache\r\n    const sessionKeys = Object.keys(sessionStorage);\r\n    sessionKeys.forEach(key => {\r\n      if (key.startsWith('cache_') || key.includes('_data') || key.includes('_timestamp')) {\r\n        sessionStorage.removeItem(key);\r\n      }\r\n    });\r\n  },\r\n\r\n  /**\r\n   * Check if cached data is still valid\r\n   * @param {string} key - Cache key\r\n   * @param {number} maxAge - Maximum age in milliseconds\r\n   * @returns {boolean} Whether cache is valid\r\n   */\r\n  isCacheValid(key, maxAge = 5 * 60 * 1000) { // 5 minutes default\r\n    const timestamp = localStorage.getItem(`${key}_timestamp`);\r\n    if (!timestamp) return false;\r\n    \r\n    const age = Date.now() - parseInt(timestamp);\r\n    return age < maxAge;\r\n  }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;AAED;;AAQO,eAAe,mBAAmB,QAAQ,EAAE,SAAS,CAAC,CAAC;IAC5D,IAAI;QACF,MAAM,cAAc,IAAI;QAExB,yEAAyE;QACzE,IAAI,OAAO,IAAI,EAAE,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI;QACvD,IAAI,OAAO,SAAS,EAAE,YAAY,MAAM,CAAC,YAAY,OAAO,SAAS;QAErE,wBAAwB;QACxB,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAA;YAC1B,IAAI,QAAQ,UAAU,QAAQ,eAAe,MAAM,CAAC,IAAI,EAAE;gBACxD,YAAY,MAAM,CAAC,KAAK,MAAM,CAAC,IAAI;YACrC;QACF;QAEA,MAAM,MAAM,GAAG,SAAS,CAAC,EAAE,YAAY,QAAQ,IAAI;QACnD,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAM,CAAC,GAAG,CAAC;QAElC,6DAA6D;QAC7D,IAAI,MAAM,YAAY;QAEtB,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI,CAAC,UAAU,EAAE;YAClD,6CAA6C;YAC7C,OAAO,SAAS,IAAI,CAAC,IAAI;YACzB,aAAa;gBACX,cAAc,SAAS,IAAI,CAAC,UAAU,CAAC,YAAY;gBACnD,aAAa,SAAS,IAAI,CAAC,UAAU,CAAC,WAAW;gBACjD,aAAa,SAAS,IAAI,CAAC,UAAU,CAAC,WAAW;gBACjD,UAAU,SAAS,IAAI,CAAC,UAAU,CAAC,QAAQ;gBAC3C,UAAU,SAAS,IAAI,CAAC,UAAU,CAAC,QAAQ;gBAC3C,cAAc,SAAS,IAAI,CAAC,UAAU,CAAC,YAAY;YACrD;YACA,WAAW,SAAS,IAAI,CAAC,QAAQ,IAAI,CAAC;QACxC,OAAO,IAAI,SAAS,IAAI,CAAC,OAAO,KAAK,WAAW;YAC9C,6FAA6F;YAC7F,OAAO,SAAS,IAAI,CAAC,OAAO;YAE5B,IAAI,SAAS,IAAI,CAAC,UAAU,EAAE;gBAC5B,2BAA2B;gBAC3B,aAAa;oBACX,cAAc,SAAS,IAAI,CAAC,UAAU,CAAC,IAAI,IAAI,OAAO,IAAI,IAAI;oBAC9D,aAAa,SAAS,IAAI,CAAC,UAAU,CAAC,WAAW,IAAI;oBACrD,aAAa,SAAS,IAAI,CAAC,UAAU,CAAC,WAAW,IAAI;oBACrD,UAAU,SAAS,IAAI,CAAC,UAAU,CAAC,SAAS,IAAI,OAAO,SAAS,IAAI;oBACpE,UAAU,SAAS,IAAI,CAAC,UAAU,CAAC,QAAQ,IAAI;oBAC/C,cAAc,SAAS,IAAI,CAAC,UAAU,CAAC,YAAY,IAAI;gBACzD;YACF,OAAO;gBACL,0BAA0B;gBAC1B,MAAM,aAAa,SAAS,IAAI,CAAC,KAAK,IAAI;gBAC1C,MAAM,UAAU,OAAO,SAAS,IAAI;gBACpC,MAAM,cAAc,OAAO,IAAI,IAAI;gBACnC,MAAM,aAAa,KAAK,IAAI,CAAC,aAAa;gBAE1C,aAAa;oBACX,cAAc;oBACd,aAAa;oBACb,aAAa;oBACb,UAAU;oBACV,UAAU,SAAS,IAAI,CAAC,IAAI,GAAG,OAAO;oBACtC,cAAc,SAAS,IAAI,CAAC,QAAQ,GAAG,OAAO;gBAChD;YACF;YAEA,WAAW,SAAS,IAAI,CAAC,QAAQ,IAAI,CAAC;QACxC,OAAO;YACL,6BAA6B;YAC7B,OAAO,SAAS,IAAI;YACpB,aAAa;gBACX,cAAc,OAAO,IAAI,IAAI;gBAC7B,aAAa;gBACb,aAAa,MAAM,OAAO,CAAC,QAAQ,KAAK,MAAM,GAAG;gBACjD,UAAU,OAAO,SAAS,IAAI;gBAC9B,UAAU;gBACV,cAAc;YAChB;YACA,WAAW,CAAC;QACd;QAEA,OAAO;YACL,SAAS;YACT,MAAM,QAAQ,EAAE;YAChB;YACA;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,SAAS,CAAC,CAAC,EAAE;QACjE,MAAM;IACR;AACF;AAKO,MAAM,eAAe;IAC1B;;;;GAIC,GACD,MAAM,cAAa,SAAS,CAAC,CAAC;QAC5B,IAAI;YACF,mCAAmC;YACnC,OAAO,MAAM,mBAAmB,gCAAgC;QAClE,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC;YAEZ,gEAAgE;YAChE,IAAI;gBACF,MAAM,EAAE,cAAc,EAAE,GAAG;gBAC3B,MAAM,eAAe,MAAM;gBAE3B,yDAAyD;gBACzD,IAAI,oBAAoB;gBAExB,sBAAsB;gBACtB,IAAI,OAAO,MAAM,EAAE;oBACjB,MAAM,aAAa,OAAO,MAAM,CAAC,WAAW;oBAC5C,oBAAoB,kBAAkB,MAAM,CAAC,CAAA,UAC3C,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,eACpC,QAAQ,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,eACxC,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC;gBAE/C;gBAEA,oBAAoB;gBACpB,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,KAAK,OAAO;oBACxC,oBAAoB,kBAAkB,MAAM,CAAC,CAAA,UAAW,QAAQ,IAAI,KAAK,OAAO,IAAI;gBACtF;gBAEA,wBAAwB;gBACxB,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,KAAK,OAAO;oBAChD,oBAAoB,kBAAkB,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK,OAAO,QAAQ;gBAC9F;gBAEA,gBAAgB;gBAChB,IAAI,OAAO,QAAQ,EAAE;oBACnB,kBAAkB,IAAI,CAAC,CAAC,GAAG;wBACzB,OAAQ,OAAO,QAAQ;4BACrB,KAAK;gCACH,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;4BACpC,KAAK;gCACH,OAAO,CAAC,EAAE,eAAe,IAAI,CAAC,IAAI,CAAC,EAAE,eAAe,IAAI,CAAC;4BAC3D,KAAK;gCACH,OAAO,CAAC,EAAE,eAAe,IAAI,CAAC,IAAI,CAAC,EAAE,eAAe,IAAI,CAAC;4BAC3D,KAAK;gCACH,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;4BACpC;gCACE,OAAO;wBACX;oBACF;gBACF;gBAEA,mBAAmB;gBACnB,MAAM,OAAO,OAAO,IAAI,IAAI;gBAC5B,MAAM,WAAW,OAAO,SAAS,IAAI;gBACrC,MAAM,aAAa,CAAC,OAAO,CAAC,IAAI;gBAChC,MAAM,WAAW,aAAa;gBAC9B,MAAM,qBAAqB,kBAAkB,KAAK,CAAC,YAAY;gBAE/D,4BAA4B;gBAC5B,MAAM,aAAa,kBAAkB,MAAM;gBAC3C,MAAM,aAAa,KAAK,IAAI,CAAC,aAAa;gBAE1C,OAAO;oBACL,SAAS;oBACT,MAAM;oBACN,YAAY;wBACV,cAAc;wBACd,aAAa;wBACb,aAAa;wBACb,UAAU;wBACV,UAAU,OAAO;wBACjB,cAAc,OAAO;oBACvB;oBACA,UAAU,CAAC;gBACb;YACF,EAAE,OAAO,eAAe;gBACtB,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,MAAM;YACR;QACF;IACF;IAEA;;;;GAIC,GACD,MAAM,iBAAgB,eAAe,KAAK;QACxC,IAAI;YACF,6CAA6C;YAC7C,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAM,CAAC,GAAG,CAAC;YAClC,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uDAAuD;YAErE,4BAA4B;YAC5B,MAAM,YAAY;gBAChB;gBACA;aACD;YAED,KAAK,MAAM,YAAY,UAAW;gBAChC,IAAI;oBACF,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAM,CAAC,GAAG,CAAC;oBAClC,IAAI,SAAS,IAAI,EAAE;wBACjB,QAAQ,GAAG,CAAC,CAAC,gCAAgC,EAAE,UAAU;wBACzD,OAAO,SAAS,IAAI;oBACtB;gBACF,EAAE,OAAO,UAAU;oBACjB,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,SAAS,EAAE,EAAE,SAAS,OAAO,EAAE;gBACrE;YACF;YAEA,iDAAiD;YACjD,QAAQ,GAAG,CAAC;YACZ,OAAO,IAAI,CAAC,2BAA2B;QACzC;IACF;IAEA;;;GAGC,GACD,MAAM;QACJ,IAAI;YACF,sDAAsD;YACtD,MAAM,EAAE,cAAc,EAAE,GAAG;YAC3B,MAAM,YAAY,MAAM;YAExB,6BAA6B;YAC7B,MAAM,QAAQ;gBACZ,OAAO,UAAU,MAAM;gBACvB,aAAa,UAAU,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,CAAC,QAAQ,eAAe,IAAI,CAAC,GAAG;gBACtF,mBAAmB,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,iBAAiB,EAAE,MAAM;gBACpE,OAAO,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,UAAU,MAAM;gBACxD,OAAO,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,UAAU,MAAM;gBACxD,OAAO,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,UAAU,MAAM;YAC1D;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,OAAO;gBACL,OAAO;gBACP,aAAa;gBACb,mBAAmB;gBACnB,OAAO;gBACP,OAAO;gBACP,OAAO;YACT;QACF;IACF;IAEA;;;;;;;GAOC,GACD,MAAM,iBAAgB,UAAU,EAAE,UAAU,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,WAAW,EAAE;QACrE,MAAM,SAAS;YACb,QAAQ;YACR;YACA,WAAW;YACX,GAAG,OAAO;QACZ;QACA,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B;AACF;AAKO,MAAM,cAAc;IACzB;;;;GAIC,GACD,MAAM,aAAY,SAAS,CAAC,CAAC;QAC3B,IAAI;YACF,yDAAyD;YACzD,MAAM,gBAAgB;gBAAE,GAAG,MAAM;YAAC;YAElC,gEAAgE;YAChE,MAAM,iBAAiB,OAAO,UAAU,KAAK;YAC7C,IAAI,gBAAgB;gBAClB,6EAA6E;gBAC7E,cAAc,QAAQ,GAAG;YAC3B;YAEA,kCAAkC;YAClC,IAAI,OAAO,SAAS,EAAE;gBACpB,cAAc,QAAQ,GAAG,OAAO,SAAS;gBACzC,OAAO,cAAc,SAAS;YAChC;YAEA,oDAAoD;YACpD,IAAI,cAAc,UAAU,EAAE;gBAC5B,OAAO,cAAc,UAAU;YACjC;YAEA,2DAA2D;YAC3D,MAAM,WAAW,MAAM,mBAAmB,qCAAqC;YAE/E,kEAAkE;YAClE,IAAI,kBAAkB,CAAC,SAAS,QAAQ,EAAE,eAAe,SAAS,IAAI,EAAE;gBACtE,gFAAgF;gBAChF,MAAM,aAAa,CAAC;gBACpB,SAAS,IAAI,CAAC,OAAO,CAAC,CAAA;oBACpB,MAAM,OAAO,QAAQ,YAAY,IAAI,QAAQ,eAAe;oBAC5D,IAAI,MAAM;wBACR,UAAU,CAAC,KAAK,GAAG,CAAC,UAAU,CAAC,KAAK,IAAI,CAAC,IAAI;oBAC/C;gBACF;gBAEA,IAAI,CAAC,SAAS,QAAQ,EAAE,SAAS,QAAQ,GAAG,CAAC;gBAC7C,SAAS,QAAQ,CAAC,WAAW,GAAG;YAClC;YAEA,oCAAoC;YACpC,OAAO;gBACL,SAAS;gBACT,MAAM,SAAS,IAAI,IAAI,EAAE;gBACzB,YAAY;oBACV,cAAc,SAAS,UAAU,EAAE,gBAAgB,OAAO,IAAI,IAAI;oBAClE,aAAa,SAAS,UAAU,EAAE,eAAe;oBACjD,aAAa,SAAS,UAAU,EAAE,eAAe;oBACjD,UAAU,SAAS,UAAU,EAAE,YAAY,OAAO,SAAS,IAAI;oBAC/D,UAAU,SAAS,UAAU,EAAE,YAAY;oBAC3C,cAAc,SAAS,UAAU,EAAE,gBAAgB;gBACrD;gBACA,UAAU,SAAS,QAAQ,IAAI,CAAC;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YAEnD,wCAAwC;YACxC,IAAI;gBACF,MAAM,mBAAmB,MAAM,mBAAmB,2BAA2B;gBAC7E,OAAO;YACT,EAAE,OAAO,eAAe;gBACtB,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,MAAM;YACR;QACF;IACF;IAEA;;;;GAIC,GACD,MAAM,iBAAgB,eAAe,KAAK;QACxC,IAAI;YACF,0EAA0E;YAC1E,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAM,CAAC,GAAG,CAAC;YAClC,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,yCAAyC;YACzC,OAAO;gBACL,OAAO;gBACP,QAAQ;gBACR,WAAW;gBACX,QAAQ;YACV;QACF;IACF;IAEA;;;;GAIC,GACD,MAAM,oBAAmB,eAAe,KAAK;QAC3C,IAAI;YACF,6EAA6E;YAC7E,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAM,CAAC,GAAG,CAAC;YAClC,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,yCAAyC;YACzC,OAAO;gBACL,OAAO;gBACP,aAAa,EAAE;YACjB;QACF;IACF;IAEA;;;;;;;GAOC,GACD,MAAM,gBAAe,UAAU,EAAE,UAAU,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,WAAW,EAAE;QACpE,MAAM,SAAS;YACb,QAAQ;YACR;YACA,WAAW;YACX,GAAG,OAAO;QACZ;QACA,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B;IAEA;;;;GAIC,GACD,MAAM,YAAW,EAAE;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAM,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC;YACjE,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR;IACF;IAEA;;;;;GAKC,GACD,MAAM,eAAc,EAAE,EAAE,IAAI;QAC1B,IAAI;YACF,QAAQ,GAAG,CAAC,8BAA8B;gBAAE;gBAAI;YAAK;YAErD,qCAAqC;YACrC,MAAM,cAAc;gBAAE,GAAG,IAAI;YAAC;YAE9B,+CAA+C;YAC/C;gBAAC;gBAAgB;aAAe,CAAC,OAAO,CAAC,CAAA;gBACvC,IAAI,WAAW,CAAC,MAAM,KAAK,QAAQ,WAAW,CAAC,MAAM,KAAK,WAAW;oBACnE,MAAM,MAAM,SAAS,WAAW,CAAC,MAAM;oBACvC,WAAW,CAAC,MAAM,GAAG,MAAM,OAAO,OAAO;gBAC3C;YACF;YAEA,gDAAgD;YAChD,MAAM,eAAe;gBACnB;gBAAc;gBAAa;gBAAc;gBAAiB;gBAAS;gBAAU;gBAC7E;gBAAiB;gBAAW;gBAAQ;gBAAY;gBAAS;gBAAW;gBACpE;gBAAkB;gBAAa;gBAC/B;gBAAc;gBAAoB;gBAAe;gBAAgB;gBACjE;gBAAkB;gBAClB;gBAAgB;gBAAsB;gBAAiB;gBAAkB;gBACzE;gBAAoB;aACrB;YAED,aAAa,OAAO,CAAC,CAAA;gBACnB,IAAI,WAAW,CAAC,MAAM,KAAK,QAAQ,WAAW,CAAC,MAAM,KAAK,WAAW;oBACnE,WAAW,CAAC,MAAM,GAAG,OAAO,WAAW,CAAC,MAAM,EAAE,IAAI;gBACtD;YACF;YAEA,0BAA0B;YAC1B,OAAO,IAAI,CAAC,aAAa,OAAO,CAAC,CAAA;gBAC/B,IAAI,WAAW,CAAC,IAAI,KAAK,WAAW;oBAClC,OAAO,WAAW,CAAC,IAAI;gBACzB;YACF;YAEA,QAAQ,GAAG,CAAC,4BAA4B;YAExC,gDAAgD;YAChD,IAAI;gBACF,QAAQ,GAAG,CAAC,4BAA4B,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC;gBACvE,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAM,CAAC,KAAK,CAAC,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC,EAAE;gBACrE,QAAQ,GAAG,CAAC,6BAA6B,SAAS,IAAI;gBACtD,OAAO,SAAS,IAAI;YACtB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;oBACxC,QAAQ,MAAM,QAAQ,EAAE;oBACxB,YAAY,MAAM,QAAQ,EAAE;oBAC5B,MAAM,MAAM,QAAQ,EAAE;gBACxB;gBAEA,8CAA8C;gBAC9C,IAAI;oBACF,QAAQ,GAAG,CAAC,6BAA6B,CAAC,uBAAuB,EAAE,GAAG,QAAQ,CAAC;oBAC/E,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAM,CAAC,KAAK,CAAC,CAAC,uBAAuB,EAAE,GAAG,QAAQ,CAAC,EAAE;oBAC5E,QAAQ,GAAG,CAAC,8BAA8B,SAAS,IAAI;oBACvD,OAAO,SAAS,IAAI;gBACtB,EAAE,OAAO,aAAa;oBACpB,QAAQ,KAAK,CAAC,gDAAgD;wBAC5D,cAAc;4BACZ,QAAQ,MAAM,QAAQ,EAAE;4BACxB,MAAM,MAAM,QAAQ,EAAE;wBACxB;wBACA,iBAAiB;4BACf,QAAQ,YAAY,QAAQ,EAAE;4BAC9B,MAAM,YAAY,QAAQ,EAAE;wBAC9B;oBACF;oBAEA,gCAAgC;oBAChC,MAAM,eAAe,YAAY,QAAQ,EAAE,WAAW,MAAM,cAAc;oBAC1E,MAAM;gBACR;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR;IACF;IAEA;;;;GAIC,GACD,MAAM,cAAa,IAAI;QACrB,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,UAAU;YAE1B,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAM,CAAC,KAAK,CAAC,sBAAsB,UAAU;gBAClE,SAAS;oBACP,gBAAgB;gBAClB;YACF;YACA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR;IACF;IAEA;;;;GAIC,GACD,MAAM,cAAa,QAAQ;QACzB,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAM,CAAC,MAAM,CAAC,CAAC,sBAAsB,EAAE,SAAS,CAAC,CAAC;YACzE,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA;;;;;GAKC,GACD,MAAM,mBAAkB,IAAI,EAAE,IAAI;QAChC,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,GAAG,KAAK,YAAY,CAAC,EAAE;YAEvC,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAM,CAAC,KAAK,CAAC,sBAAsB,UAAU;gBAClE,SAAS;oBACP,gBAAgB;gBAClB;YACF;YACA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACR;IACF;IAEA;;;;;GAKC,GACD,MAAM,yBAAwB,IAAI,EAAE,QAAQ;QAC1C,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,aAAa;YAC7B,SAAS,MAAM,CAAC,YAAY;YAE5B,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,sCAAsC,UAAU;gBACjF,SAAS;oBACP,gBAAgB;gBAClB;YACF;YACA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,MAAM;QACR;IACF;IAEA;;;GAGC,GACD,MAAM;QACJ,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAM,CAAC,GAAG,CAAC;YAClC,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA;;;;GAIC,GACD,MAAM,mBAAkB,IAAI;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAM,CAAC,KAAK,CAAC,sBAAsB;YAC1D,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACR;IACF;IAEA;;;;GAIC,GACD,MAAM,oBAAmB,YAAY;QACnC,IAAI;YACF,sDAAsD;YACtD,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,8BAA8B;YACjE,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;AACF;AAKO,MAAM,WAAW;IACtB;;;;GAIC,GACD,MAAM,sBAAqB,QAAQ;QACjC,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,+BAA+B;YAClE,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;QACR;IACF;IAEA;;;GAGC,GACD,MAAM;QACJ,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAM,CAAC,GAAG,CAAC;YAClC,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;QACR;IACF;AACF;AAKO,MAAM,UAAU;IACrB;;;;GAIC,GACD,MAAM,SAAQ,SAAS,CAAC,CAAC;QACvB,OAAO,mBAAmB,iBAAiB;IAC7C;IAEA;;;;GAIC,GACD,MAAM,aAAY,eAAe,KAAK;QACpC,IAAI;YACF,kCAAkC;YAClC,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAM,CAAC,GAAG,CAAC;YAClC,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO;gBACL,OAAO;gBACP,QAAQ;gBACR,SAAS;gBACT,cAAc;YAChB;QACF;IACF;AACF;AAKO,MAAM,eAAe;IAC1B;;;;GAIC,GACD,MAAM,mBAAkB,eAAe,KAAK;QAC1C,IAAI;YACF,4CAA4C;YAC5C,MAAM,CAAC,cAAc,SAAS,GAAG,MAAM,QAAQ,UAAU,CAAC;gBACxD,aAAa,eAAe;gBAC5B,QAAQ,WAAW;aACpB;YAED,OAAO;gBACL,WAAW,aAAa,MAAM,KAAK,cAAc,aAAa,KAAK,GAAG,CAAC;gBACvE,MAAM,SAAS,MAAM,KAAK,cAAc,SAAS,KAAK,GAAG,CAAC;gBAC1D,WAAW,IAAI,OAAO,WAAW;YACnC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,OAAO;gBACL,WAAW,CAAC;gBACZ,MAAM,CAAC;gBACP,WAAW,IAAI,OAAO,WAAW;YACnC;QACF;IACF;IAEA;;;;GAIC,GACD,MAAM,mBAAkB,eAAe,KAAK;QAC1C,IAAI;YACF,+CAA+C;YAC/C,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAM,CAAC,GAAG,CAAC;YAClC,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,OAAO;gBACL,kBAAkB;gBAClB,gBAAgB;gBAChB,iBAAiB;gBACjB,gBAAgB,EAAE;YACpB;QACF;IACF;AACF;AAKO,MAAM,oBAAoB;IAC/B,+BAA+B;IAC/B,2BAA2B,OAAO,OAAO,wBAAwB,EAAE,UAAU,KAAK;QAChF,IAAI;YACF,MAAM,SAAS;gBAAE;YAAK;YACtB,IAAI,SAAS,OAAO,OAAO,GAAG;YAE9B,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAM,CAAC,GAAG,CAAC,sCAAsC;gBAAE;YAAO;YACjF,OAAO;gBACL,SAAS;gBACT,MAAM,SAAS,IAAI;YACrB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,OAAO;gBACL,SAAS;gBACT,OAAO,MAAM,QAAQ,EAAE,MAAM,WAAW;YAC1C;QACF;IACF;IAEA,yCAAyC;IACzC,oBAAoB,OAAO,aAAa,IAAI,EAAE,UAAU,KAAK;QAC3D,IAAI;YACF,MAAM,SAAS,CAAC;YAChB,IAAI,YAAY,OAAO,UAAU,GAAG;YACpC,IAAI,SAAS,OAAO,OAAO,GAAG;YAE9B,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAM,CAAC,GAAG,CAAC,yCAAyC;gBAAE;YAAO;YACpF,OAAO;gBACL,SAAS;gBACT,MAAM,SAAS,IAAI;YACrB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;gBACL,SAAS;gBACT,OAAO,MAAM,QAAQ,EAAE,MAAM,WAAW;YAC1C;QACF;IACF;IAEA,mCAAmC;IACnC,cAAc,OAAO,OAAO,IAAI,EAAE,UAAU,KAAK,EAAE,aAAa,IAAI;QAClE,IAAI;YACF,MAAM,SAAS,CAAC;YAChB,IAAI,MAAM,OAAO,IAAI,GAAG;YACxB,IAAI,YAAY,OAAO,UAAU,GAAG;YACpC,IAAI,SAAS,OAAO,OAAO,GAAG;YAE9B,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAM,CAAC,GAAG,CAAC,mCAAmC;gBAAE;YAAO;YAC9E,OAAO;gBACL,SAAS;gBACT,MAAM,SAAS,IAAI;YACrB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAE5C,8DAA8D;YAC9D,MAAM,eAAe;gBACnB,OAAO,EAAE;gBACT,cAAc,IAAI,OAAO,WAAW;gBACpC,mBAAmB,cAAc;gBACjC,cAAc,IAAI,OAAO,WAAW;gBACpC,gBAAgB;YAClB;YAEA,sEAAsE;YACtE,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,QAAQ,IAAI,CAAC;gBACb,OAAO;oBACL,SAAS;oBACT,MAAM;oBACN,UAAU;gBACZ;YACF;YAEA,OAAO;gBACL,SAAS;gBACT,OAAO,MAAM,QAAQ,EAAE,MAAM,WAAW;gBACxC;YACF;QACF;IACF;IAEA,oCAAoC;IACpC,yBAAyB;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAM,CAAC,GAAG,CAAC;YAClC,OAAO;gBACL,SAAS;gBACT,MAAM,SAAS,IAAI;YACrB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO;gBACL,SAAS;gBACT,OAAO,MAAM,QAAQ,EAAE,MAAM,WAAW;YAC1C;QACF;IACF;IAEA,wCAAwC;IACxC,wBAAwB,OAAO,UAAU,KAAK;QAC5C,IAAI;YACF,MAAM,CAAC,eAAe,iBAAiB,WAAW,iBAAiB,GAAG,MAAM,QAAQ,UAAU,CAAC;gBAC7F,kBAAkB,yBAAyB,CAAC,0BAA0B;gBACtE,kBAAkB,kBAAkB,CAAC,MAAM;gBAC3C,kBAAkB,YAAY,CAAC,MAAM;gBACrC,kBAAkB,uBAAuB;aAC1C;YAED,MAAM,UAAU;gBACd,UAAU,cAAc,MAAM,KAAK,cAAc,cAAc,KAAK,CAAC,IAAI,GAAG;gBAC5E,aAAa,gBAAgB,MAAM,KAAK,cAAc,gBAAgB,KAAK,CAAC,IAAI,GAAG;gBACnF,OAAO,UAAU,MAAM,KAAK,cAAc,UAAU,KAAK,CAAC,IAAI,GAAG;gBACjE,aAAa,iBAAiB,MAAM,KAAK,cAAc,iBAAiB,KAAK,CAAC,IAAI,GAAG;gBACrF,QAAQ,EAAE;YACZ;YAEA,2CAA2C;YAC3C;gBAAC;gBAAe;gBAAiB;gBAAW;aAAiB,CAAC,OAAO,CAAC,CAAC,QAAQ;gBAC7E,MAAM,QAAQ;oBAAC;oBAAY;oBAAe;oBAAS;iBAAc;gBACjE,MAAM,OAAO,KAAK,CAAC,MAAM;gBAEzB,IAAI,OAAO,MAAM,KAAK,YAAY;oBAChC,QAAQ,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,EAAE,EAAE,OAAO,MAAM,CAAC,OAAO,IAAI,iBAAiB;gBAC5E,OAAO,IAAI,OAAO,KAAK,EAAE,UAAU;oBACjC,0DAA0D;oBAC1D,QAAQ,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,yCAAyC,CAAC;gBACxE,OAAO,IAAI,OAAO,KAAK,IAAI,CAAC,OAAO,KAAK,CAAC,OAAO,IAAI,OAAO,KAAK,CAAC,YAAY,EAAE;oBAC7E,wDAAwD;oBACxD,OAAO,CAAC,KAAK,GAAG,OAAO,KAAK,CAAC,YAAY;oBACzC,QAAQ,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,EAAE,EAAE,OAAO,KAAK,CAAC,KAAK,IAAI,YAAY,gBAAgB,CAAC;gBACrF;YACF;YAEA,mDAAmD;YACnD,IAAI,CAAC,QAAQ,QAAQ,EAAE;gBACrB,QAAQ,QAAQ,GAAG;oBAAE,UAAU,CAAC;oBAAG,cAAc,IAAI,OAAO,WAAW;gBAAG;YAC5E;YACA,IAAI,CAAC,QAAQ,WAAW,EAAE;gBACxB,QAAQ,WAAW,GAAG;oBAAE,aAAa,EAAE;oBAAE,cAAc,IAAI,OAAO,WAAW;gBAAG;YAClF;YACA,IAAI,CAAC,QAAQ,KAAK,EAAE;gBAClB,QAAQ,KAAK,GAAG;oBAAE,OAAO,EAAE;oBAAE,cAAc,IAAI,OAAO,WAAW;oBAAI,cAAc,IAAI,OAAO,WAAW;gBAAG;YAC9G;YACA,IAAI,CAAC,QAAQ,WAAW,EAAE;gBACxB,QAAQ,WAAW,GAAG;oBAAE,wBAAwB,CAAC;oBAAG,cAAc,IAAI,OAAO,WAAW;gBAAG;YAC7F;YAEA,OAAO;gBACL,SAAS;gBACT,MAAM;gBACN,cAAc,IAAI,OAAO,WAAW;gBACpC,YAAY,QAAQ,MAAM,CAAC,MAAM,GAAG;YACtC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YAEvD,sCAAsC;YACtC,MAAM,kBAAkB;gBACtB,UAAU;oBAAE,UAAU,CAAC;oBAAG,cAAc,IAAI,OAAO,WAAW;gBAAG;gBACjE,aAAa;oBAAE,aAAa,EAAE;oBAAE,cAAc,IAAI,OAAO,WAAW;gBAAG;gBACvE,OAAO;oBAAE,OAAO,EAAE;oBAAE,cAAc,IAAI,OAAO,WAAW;oBAAI,cAAc,IAAI,OAAO,WAAW;gBAAG;gBACnG,aAAa;oBAAE,wBAAwB,CAAC;oBAAG,cAAc,IAAI,OAAO,WAAW;gBAAG;gBAClF,QAAQ;oBAAC;iBAA0D;YACrE;YAEA,OAAO;gBACL,SAAS;gBACT,MAAM;gBACN,cAAc,IAAI,OAAO,WAAW;gBACpC,UAAU;gBACV,YAAY;YACd;QACF;IACF;IAEA,oCAAoC;IACpC,mBAAmB;QACjB,IAAI;YACF,MAAM,kBAAkB;gBACtB,kBAAkB,yBAAyB,CAAC,0BAA0B;gBACtE,kBAAkB,kBAAkB,CAAC,MAAM;gBAC3C,kBAAkB,YAAY,CAAC,MAAM;aACtC;YAED,MAAM,QAAQ,GAAG,CAAC;YAClB,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;IACF;AACF;AAKO,MAAM,kBAAkB;IAC7B;;;;;;;GAOC,GACD,yBAAwB,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ;QACnE,OAAO;YACL;YACA;YACA;YACA;YACA,YAAY,CAAC,cAAc,CAAC,IAAI,WAAW;YAC3C,UAAU,KAAK,GAAG,CAAC,cAAc,UAAU;YAC3C,SAAS,cAAc;YACvB,aAAa,cAAc;QAC7B;IACF;IAEA;;;;;;GAMC,GACD,qBAAoB,WAAW,EAAE,UAAU,EAAE,aAAa,CAAC;QACzD,MAAM,QAAQ,EAAE;QAChB,MAAM,OAAO,KAAK,KAAK,CAAC,aAAa;QAErC,IAAI,QAAQ,KAAK,GAAG,CAAC,GAAG,cAAc;QACtC,IAAI,MAAM,KAAK,GAAG,CAAC,YAAY,QAAQ,aAAa;QAEpD,qCAAqC;QACrC,IAAI,MAAM,QAAQ,IAAI,YAAY;YAChC,QAAQ,KAAK,GAAG,CAAC,GAAG,MAAM,aAAa;QACzC;QAEA,IAAK,IAAI,IAAI,OAAO,KAAK,KAAK,IAAK;YACjC,MAAM,IAAI,CAAC;QACb;QAEA,OAAO;IACT;AACF;AAKO,MAAM,aAAa;IACxB;;GAEC,GACD;QACE,2BAA2B;QAC3B,MAAM,OAAO,OAAO,IAAI,CAAC;QACzB,KAAK,OAAO,CAAC,CAAA;YACX,IAAI,IAAI,UAAU,CAAC,aAAa,IAAI,QAAQ,CAAC,YAAY,IAAI,QAAQ,CAAC,eAAe;gBACnF,aAAa,UAAU,CAAC;YAC1B;QACF;QAEA,6BAA6B;QAC7B,MAAM,cAAc,OAAO,IAAI,CAAC;QAChC,YAAY,OAAO,CAAC,CAAA;YAClB,IAAI,IAAI,UAAU,CAAC,aAAa,IAAI,QAAQ,CAAC,YAAY,IAAI,QAAQ,CAAC,eAAe;gBACnF,eAAe,UAAU,CAAC;YAC5B;QACF;IACF;IAEA;;;;;GAKC,GACD,cAAa,GAAG,EAAE,SAAS,IAAI,KAAK,IAAI;QACtC,MAAM,YAAY,aAAa,OAAO,CAAC,GAAG,IAAI,UAAU,CAAC;QACzD,IAAI,CAAC,WAAW,OAAO;QAEvB,MAAM,MAAM,KAAK,GAAG,KAAK,SAAS;QAClC,OAAO,MAAM;IACf;AACF", "debugId": null}}, {"offset": {"line": 1323, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/utils/auth.js"], "sourcesContent": ["/**\r\n * Utility functions for authentication\r\n */\r\n\r\n/**\r\n * Gets the current logged-in user ID\r\n * @returns {string|null} The user ID or null if not logged in\r\n */\r\nexport function getUserId() {\r\n  try {\r\n    // Check for authentication data in localStorage\r\n    const authToken = localStorage.getItem('access');\r\n    \r\n    if (!authToken) {\r\n      return null;\r\n    }\r\n    \r\n    // Get user data from localStorage\r\n    const userData = localStorage.getItem('user');\r\n    if (userData) {\r\n      const parsedUser = JSON.parse(userData);\r\n      return parsedUser.id || parsedUser.user_id || null;\r\n    }\r\n    \r\n    // Alternative: try to get from JWT token if user data is not stored separately\r\n    if (authToken) {\r\n      try {\r\n        // Decode JWT token to get user ID\r\n        // JWT tokens are in format: header.payload.signature\r\n        const payload = authToken.split('.')[1];\r\n        const decodedPayload = JSON.parse(atob(payload));\r\n        return decodedPayload.user_id || decodedPayload.id || decodedPayload.sub || null;\r\n      } catch (e) {\r\n        console.error('Error decoding JWT token:', e);\r\n      }\r\n    }\r\n    \r\n    return null;\r\n  } catch (error) {\r\n    console.error('Error getting user ID:', error);\r\n    return null;\r\n  }\r\n}\r\n\r\n/**\r\n * Checks if the user is logged in\r\n * @returns {boolean} True if logged in, false otherwise\r\n */\r\nexport function isLoggedIn() {\r\n  const token = localStorage.getItem('access');\r\n  return !!token;\r\n}\r\n\r\n/**\r\n * Gets the authentication token\r\n * @returns {string|null} The token or null if not logged in\r\n */\r\nexport function getAuthToken() {\r\n  return localStorage.getItem('access');\r\n}\r\n\r\n/**\r\n * Gets the current user data\r\n * @returns {Object|null} The user data or null if not logged in\r\n */\r\nexport function getUserData() {\r\n  try {\r\n    const userData = localStorage.getItem('user');\r\n    return userData ? JSON.parse(userData) : null;\r\n  } catch (error) {\r\n    console.error('Error getting user data:', error);\r\n    return null;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;;CAGC;;;;;;AACM,SAAS;IACd,IAAI;QACF,gDAAgD;QAChD,MAAM,YAAY,aAAa,OAAO,CAAC;QAEvC,IAAI,CAAC,WAAW;YACd,OAAO;QACT;QAEA,kCAAkC;QAClC,MAAM,WAAW,aAAa,OAAO,CAAC;QACtC,IAAI,UAAU;YACZ,MAAM,aAAa,KAAK,KAAK,CAAC;YAC9B,OAAO,WAAW,EAAE,IAAI,WAAW,OAAO,IAAI;QAChD;QAEA,+EAA+E;QAC/E,IAAI,WAAW;YACb,IAAI;gBACF,kCAAkC;gBAClC,qDAAqD;gBACrD,MAAM,UAAU,UAAU,KAAK,CAAC,IAAI,CAAC,EAAE;gBACvC,MAAM,iBAAiB,KAAK,KAAK,CAAC,KAAK;gBACvC,OAAO,eAAe,OAAO,IAAI,eAAe,EAAE,IAAI,eAAe,GAAG,IAAI;YAC9E,EAAE,OAAO,GAAG;gBACV,QAAQ,KAAK,CAAC,6BAA6B;YAC7C;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;IACT;AACF;AAMO,SAAS;IACd,MAAM,QAAQ,aAAa,OAAO,CAAC;IACnC,OAAO,CAAC,CAAC;AACX;AAMO,SAAS;IACd,OAAO,aAAa,OAAO,CAAC;AAC9B;AAMO,SAAS;IACd,IAAI;QACF,MAAM,WAAW,aAAa,OAAO,CAAC;QACtC,OAAO,WAAW,KAAK,KAAK,CAAC,YAAY;IAC3C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1390, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/app/admin/student-management/StudentDropdown.jsx"], "sourcesContent": ["import { useEffect, useRef, useState } from 'react';\r\nimport { ChevronDown } from \"lucide-react\";\r\n\r\nexport default function CustomDropdown({ \r\n  options, \r\n  value, \r\n  onChange, \r\n  placeholder = \"Select an option\",\r\n  className = \"\",\r\n  disabled = false \r\n}) {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const dropdownRef = useRef(null);\r\n\r\n  useEffect(() => {\r\n    const handleClickOutside = (event) => {\r\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\r\n        setIsOpen(false);\r\n      }\r\n    };\r\n    document.addEventListener('mousedown', handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener('mousedown', handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  const selectedOption = options.find(option => option.value === value);\r\n\r\n  return (\r\n    <div className={`relative ${className}`} ref={dropdownRef}>\r\n      <button\r\n        type=\"button\"\r\n        onClick={() => !disabled && setIsOpen(!isOpen)}\r\n        disabled={disabled}\r\n        className={`w-full flex items-center justify-between gap-2 px-3 py-2 border border-gray-300 rounded-lg bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 text-left ${\r\n          disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'\r\n        }`}\r\n      >\r\n        <span className={`${selectedOption ? 'text-gray-900' : 'text-gray-500'}`}>\r\n          {selectedOption ? selectedOption.label : placeholder}\r\n        </span>\r\n        <ChevronDown className={`w-4 h-4 text-gray-500 transition-transform ${isOpen ? 'rotate-180' : ''}`} />\r\n      </button>\r\n      {isOpen && (\r\n        <div className=\"absolute top-full left-0 mt-1 w-full bg-white border border-gray-300 rounded-lg shadow-lg z-[200] overflow-hidden max-h-60 overflow-y-auto\">\r\n          {options.map((option, index) => (\r\n            <button\r\n              key={option.value}\r\n              type=\"button\"\r\n              onClick={() => {\r\n                onChange(option.value);\r\n                setIsOpen(false);\r\n              }}\r\n              className={`w-full text-left px-4 py-3 hover:bg-gray-50 transition-colors ${\r\n                option.value === value ? 'bg-blue-50 text-blue-700' : 'text-gray-700'\r\n              } ${index === options.length - 1 ? '' : 'border-b border-gray-100'}`}\r\n            >\r\n              {option.label}\r\n            </button>\r\n          ))}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AAEe,SAAS,eAAe,EACrC,OAAO,EACP,KAAK,EACL,QAAQ,EACR,cAAc,kBAAkB,EAChC,YAAY,EAAE,EACd,WAAW,KAAK,EACjB;;IACC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;+DAAqB,CAAC;oBAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAG;wBACtE,UAAU;oBACZ;gBACF;;YACA,SAAS,gBAAgB,CAAC,aAAa;YACvC;4CAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;mCAAG,EAAE;IAEL,MAAM,iBAAiB,QAAQ,IAAI,CAAC,CAAA,SAAU,OAAO,KAAK,KAAK;IAE/D,qBACE,6LAAC;QAAI,WAAW,CAAC,SAAS,EAAE,WAAW;QAAE,KAAK;;0BAC5C,6LAAC;gBACC,MAAK;gBACL,SAAS,IAAM,CAAC,YAAY,UAAU,CAAC;gBACvC,UAAU;gBACV,WAAW,CAAC,mLAAmL,EAC7L,WAAW,kCAAkC,kBAC7C;;kCAEF,6LAAC;wBAAK,WAAW,GAAG,iBAAiB,kBAAkB,iBAAiB;kCACrE,iBAAiB,eAAe,KAAK,GAAG;;;;;;kCAE3C,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAW,CAAC,2CAA2C,EAAE,SAAS,eAAe,IAAI;;;;;;;;;;;;YAEnG,wBACC,6LAAC;gBAAI,WAAU;0BACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,6LAAC;wBAEC,MAAK;wBACL,SAAS;4BACP,SAAS,OAAO,KAAK;4BACrB,UAAU;wBACZ;wBACA,WAAW,CAAC,8DAA8D,EACxE,OAAO,KAAK,KAAK,QAAQ,6BAA6B,gBACvD,CAAC,EAAE,UAAU,QAAQ,MAAM,GAAG,IAAI,KAAK,4BAA4B;kCAEnE,OAAO,KAAK;uBAVR,OAAO,KAAK;;;;;;;;;;;;;;;;AAiB/B;GA7DwB;KAAA", "debugId": null}}, {"offset": {"line": 1493, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/api/auth.js"], "sourcesContent": ["import client from './client';\r\n\r\n// Register new student\r\nexport function signup(data) {\r\n  return client.post('/api/auth/register/student/', data);\r\n}\r\n\r\n// Login and get tokens\r\nexport function login(data) {\r\n  return client.post('/api/auth/login/', data);\r\n}\r\n\r\n// Upload Resume\r\nexport function uploadResume(file, accessToken) {\r\n  const formData = new FormData();\r\n  formData.append('resume', file);\r\n\r\n  return client.patch('/api/auth/profile/', formData, {\r\n    headers: {\r\n      'Authorization': `Bearer ${accessToken}`,\r\n      'Content-Type': 'multipart/form-data',\r\n    }\r\n  });\r\n}\r\n\r\nexport const getAuthToken = () => {\r\n  if (typeof window !== 'undefined') {\r\n    return localStorage.getItem('access_token');\r\n  }\r\n  return null;\r\n};\r\n\r\nexport const setAuthToken = (token) => {\r\n  if (typeof window !== 'undefined') {\r\n    localStorage.setItem('access_token', token);\r\n  }\r\n};\r\n\r\nexport const removeAuthToken = () => {\r\n  if (typeof window !== 'undefined') {\r\n    localStorage.removeItem('access_token');\r\n    localStorage.removeItem('refresh_token');\r\n  }\r\n};\r\n\r\nexport const getRefreshToken = () => {\r\n  if (typeof window !== 'undefined') {\r\n    return localStorage.getItem('refresh_token');\r\n  }\r\n  return null;\r\n};\r\n\r\nexport const setRefreshToken = (token) => {\r\n  if (typeof window !== 'undefined') {\r\n    localStorage.setItem('refresh_token', token);\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAGO,SAAS,OAAO,IAAI;IACzB,OAAO,uHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,+BAA+B;AACpD;AAGO,SAAS,MAAM,IAAI;IACxB,OAAO,uHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,oBAAoB;AACzC;AAGO,SAAS,aAAa,IAAI,EAAE,WAAW;IAC5C,MAAM,WAAW,IAAI;IACrB,SAAS,MAAM,CAAC,UAAU;IAE1B,OAAO,uHAAA,CAAA,UAAM,CAAC,KAAK,CAAC,sBAAsB,UAAU;QAClD,SAAS;YACP,iBAAiB,CAAC,OAAO,EAAE,aAAa;YACxC,gBAAgB;QAClB;IACF;AACF;AAEO,MAAM,eAAe;IAC1B,wCAAmC;QACjC,OAAO,aAAa,OAAO,CAAC;IAC9B;;AAEF;AAEO,MAAM,eAAe,CAAC;IAC3B,wCAAmC;QACjC,aAAa,OAAO,CAAC,gBAAgB;IACvC;AACF;AAEO,MAAM,kBAAkB;IAC7B,wCAAmC;QACjC,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;IAC1B;AACF;AAEO,MAAM,kBAAkB;IAC7B,wCAAmC;QACjC,OAAO,aAAa,OAAO,CAAC;IAC9B;;AAEF;AAEO,MAAM,kBAAkB,CAAC;IAC9B,wCAAmC;QACjC,aAAa,OAAO,CAAC,iBAAiB;IACxC;AACF", "debugId": null}}, {"offset": {"line": 1558, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/api/students.js"], "sourcesContent": ["import axios from 'axios';\r\nimport { getAuthToken } from './auth';\r\n\r\n// Set the base URL for all API requests\r\nconst API_BASE_URL = 'http://localhost:8000';\r\n\r\nconst api = axios.create({\r\n  baseURL: API_BASE_URL,\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n});\r\n\r\n// Add request interceptor to include auth token\r\napi.interceptors.request.use(\r\n  (config) => {\r\n    const token = getAuthToken();\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Add response interceptor for error handling\r\napi.interceptors.response.use(\r\n  (response) => response,\r\n  (error) => {\r\n    if (error.response?.status === 401) {\r\n      // Token expired or invalid\r\n      localStorage.removeItem('access_token');\r\n      localStorage.removeItem('refresh_token');\r\n      window.location.href = '/login';\r\n    }\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport const studentsAPI = {\r\n  // Get all students\r\n  getStudents: async (params = {}) => {\r\n    const response = await api.get('/api/accounts/students/', { params });\r\n    return response.data;\r\n  },\r\n\r\n  // Get students with statistics\r\n  getStudentsWithStats: async (params = {}) => {\r\n    try {\r\n      // First try to get students with built-in statistics\r\n      const response = await api.get('/api/accounts/students/stats/', { params });\r\n      return response.data;\r\n    } catch (error) {\r\n      // Fallback to regular students endpoint\r\n      console.log('Stats endpoint not available, using regular endpoint');\r\n      const response = await api.get('/api/accounts/students/', { params });\r\n      \r\n      // Calculate basic statistics from the response\r\n      const students = response.data.data || response.data;\r\n      if (Array.isArray(students)) {\r\n        const stats = calculateStudentStats(students, params);\r\n        return {\r\n          ...response.data,\r\n          statistics: stats\r\n        };\r\n      }\r\n      \r\n      return response.data;\r\n    }\r\n  },\r\n\r\n  // Get single student\r\n  getStudent: async (id) => {\r\n    const response = await api.get(`/api/accounts/students/${id}/`);\r\n    return response.data;\r\n  },\r\n\r\n  // Update student\r\n  updateStudent: async (id, data) => {\r\n    console.log('updateStudent called with:', { id, data });\r\n\r\n    // Check authentication\r\n    const token = getAuthToken();\r\n    console.log('Auth token available:', !!token);\r\n    if (token) {\r\n      console.log('Token preview:', token.substring(0, 20) + '...');\r\n    }\r\n\r\n    if (!token) {\r\n      throw new Error('Authentication required to update student');\r\n    }\r\n\r\n    // Clean data to ensure proper format\r\n    const cleanedData = { ...data };\r\n    \r\n    // Ensure numeric fields are properly formatted\r\n    ['joining_year', 'passout_year'].forEach(field => {\r\n      if (cleanedData[field] !== null && cleanedData[field] !== undefined) {\r\n        const num = parseInt(cleanedData[field]);\r\n        cleanedData[field] = isNaN(num) ? null : num;\r\n      }\r\n    });\r\n\r\n    // Ensure string fields are properly formatted\r\n    const stringFields = [\r\n      'first_name', 'last_name', 'student_id', 'contact_email', 'phone', 'branch', 'gpa',\r\n      'date_of_birth', 'address', 'city', 'district', 'state', 'pincode', 'country',\r\n      'parent_contact', 'education', 'skills',\r\n      'tenth_cgpa', 'tenth_percentage', 'tenth_board', 'tenth_school', 'tenth_year_of_passing', \r\n      'tenth_location', 'tenth_specialization',\r\n      'twelfth_cgpa', 'twelfth_percentage', 'twelfth_board', 'twelfth_school', 'twelfth_year_of_passing',\r\n      'twelfth_location', 'twelfth_specialization'\r\n    ];\r\n\r\n    stringFields.forEach(field => {\r\n      if (cleanedData[field] !== null && cleanedData[field] !== undefined) {\r\n        cleanedData[field] = String(cleanedData[field]).trim();\r\n      }\r\n    });\r\n\r\n    // Remove undefined values\r\n    Object.keys(cleanedData).forEach(key => {\r\n      if (cleanedData[key] === undefined) {\r\n        delete cleanedData[key];\r\n      }\r\n    });\r\n\r\n    console.log('Cleaned data being sent:', cleanedData);\r\n\r\n    // Try the ViewSet endpoint first (more RESTful)\r\n    try {\r\n      console.log('Trying ViewSet endpoint:', `/api/accounts/profiles/${id}/`);\r\n      const response = await api.patch(`/api/accounts/profiles/${id}/`, cleanedData);\r\n      console.log('ViewSet endpoint success:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('ViewSet endpoint failed:', {\r\n        status: error.response?.status,\r\n        statusText: error.response?.statusText,\r\n        data: error.response?.data,\r\n        headers: error.response?.headers,\r\n        config: error.config\r\n      });\r\n\r\n      // If ViewSet fails, try the fallback endpoint\r\n      try {\r\n        console.log('Trying fallback endpoint:', `/api/accounts/students/${id}/update/`);\r\n        const response = await api.patch(`/api/accounts/students/${id}/update/`, cleanedData);\r\n        console.log('Fallback endpoint success:', response.data);\r\n        return response.data;\r\n      } catch (updateError) {\r\n        console.error('Failed to update student via both endpoints:', {\r\n          viewSetError: {\r\n            status: error.response?.status,\r\n            data: error.response?.data\r\n          },\r\n          updateViewError: {\r\n            status: updateError.response?.status,\r\n            data: updateError.response?.data\r\n          }\r\n        });\r\n\r\n        // Throw the more specific error\r\n        const primaryError = updateError.response?.status === 400 ? updateError : error;\r\n        throw primaryError;\r\n      }\r\n    }\r\n  },\r\n\r\n  // Get current user profile\r\n  getProfile: async () => {\r\n    const token = getAuthToken();\r\n    return api.get('/api/auth/profile/', {\r\n      headers: { Authorization: `Bearer ${token}` },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Update profile information\r\n  updateProfile: async (data) => {\r\n    const token = getAuthToken();\r\n    return api.patch('/api/auth/profile/', data, {\r\n      headers: { Authorization: `Bearer ${token}` },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Upload profile image\r\n  uploadProfileImage: async (file) => {\r\n    const token = getAuthToken();\r\n    const formData = new FormData();\r\n    formData.append('image', file);\r\n\r\n    return api.post('/api/accounts/profiles/me/upload_profile_image/', formData, {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Upload resume using new Resume model\r\n  uploadResume: async (file, name = null, isPrimary = false) => {\r\n    const token = getAuthToken();\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n    if (name) {\r\n      formData.append('name', name);\r\n    }\r\n    formData.append('is_primary', isPrimary);\r\n\r\n    return api.post('/api/accounts/profiles/me/resumes/', formData, {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Admin upload resume for specific student\r\n  adminUploadResume: async (studentId, file, name = null, isPrimary = false) => {\r\n    const token = getAuthToken();\r\n    if (!token) {\r\n      throw new Error('No authentication token found');\r\n    }\r\n\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n    if (name) {\r\n      formData.append('name', name);\r\n    }\r\n    formData.append('is_primary', isPrimary);\r\n\r\n    return api.post(`/api/accounts/profiles/${studentId}/upload_resume/`, formData, {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Admin get resumes for specific student\r\n  adminGetResumes: async (studentId) => {\r\n    const token = getAuthToken();\r\n    if (!token) {\r\n      throw new Error('No authentication token found');\r\n    }\r\n\r\n    return api.get(`/api/accounts/profiles/${studentId}/resumes/`, {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n      },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Admin upload certificate for specific student\r\n  adminUploadCertificate: async (studentId, file, type) => {\r\n    const token = getAuthToken();\r\n    if (!token) {\r\n      throw new Error('No authentication token found');\r\n    }\r\n\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n    formData.append('type', type);\r\n\r\n    return api.post(`/api/accounts/profiles/${studentId}/upload_certificate/`, formData, {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Admin upload semester marksheet for specific student\r\n  adminUploadSemesterMarksheet: async (studentId, file, semester, cgpa) => {\r\n    const token = getAuthToken();\r\n    if (!token) {\r\n      throw new Error('No authentication token found');\r\n    }\r\n\r\n    const formData = new FormData();\r\n    formData.append('marksheet_file', file);\r\n    formData.append('semester', semester);\r\n    formData.append('cgpa', cgpa);\r\n\r\n    return api.post(`/api/accounts/profiles/${studentId}/upload_semester_marksheet/`, formData, {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Legacy resume upload (for backward compatibility)\r\n  uploadResumeToProfile: async (file) => {\r\n    const token = getAuthToken();\r\n    const formData = new FormData();\r\n    formData.append('resume', file);\r\n\r\n    return api.patch('/api/auth/profile/', formData, {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Get all resumes for the student\r\n  getResumes: async () => {\r\n    try {\r\n      const token = getAuthToken();\r\n      if (!token) {\r\n        console.log('No authentication token, returning empty array');\r\n        return [];\r\n      }\r\n\r\n      // Try the new resume endpoint first\r\n      const response = await api.get('/api/accounts/profiles/me/resumes/', {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n\r\n      // Ensure we're getting a proper response\r\n      if (!response.data) {\r\n        return await studentsAPI.getResumesLegacy();\r\n      }\r\n\r\n      // Handle different response formats\r\n      if (Array.isArray(response.data)) {\r\n        return response.data;\r\n      } else if (response.data && response.data.data && Array.isArray(response.data.data)) {\r\n        return response.data.data;\r\n      } else {\r\n        console.log('Response data is not an array, trying fallback. Response:', response.data);\r\n        try {\r\n          return await studentsAPI.getResumesLegacy();\r\n        } catch (fallbackError) {\r\n          console.log('Fallback also failed, returning empty array');\r\n          return [];\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.log('Resume endpoint failed, using fallback method');\r\n      try {\r\n        return await studentsAPI.getResumesLegacy();\r\n      } catch (fallbackError) {\r\n        console.log('Fallback method also failed, returning empty array');\r\n        return [];\r\n      }\r\n    }\r\n  },\r\n\r\n  // Legacy method to get resumes from profile\r\n  getResumesLegacy: async () => {\r\n    try {\r\n      const token = getAuthToken();\r\n      if (!token) {\r\n        console.log('No auth token for legacy resume fetch');\r\n        return [];\r\n      }\r\n\r\n      const profile = await studentsAPI.getProfile();\r\n\r\n      if (profile?.resume || profile?.resume_url) {\r\n        const resumeUrl = profile.resume_url || profile.resume;\r\n        if (resumeUrl && resumeUrl.trim() !== '' && resumeUrl !== 'null' && resumeUrl !== 'undefined') {\r\n          const fileName = resumeUrl.split('/').pop() || 'Resume.pdf';\r\n          return [{\r\n            id: profile.id || 1,\r\n            name: fileName,\r\n            file_url: resumeUrl,\r\n            uploaded_at: profile.updated_at || new Date().toISOString()\r\n          }];\r\n        }\r\n      }\r\n      return [];\r\n    } catch (error) {\r\n      console.log('Legacy resume fetch error:', error.message);\r\n      return [];\r\n    }\r\n  },\r\n\r\n  // Delete a specific resume\r\n  deleteResume: async (resumeId) => {\r\n    const token = getAuthToken();\r\n    try {\r\n      console.log(`Attempting to delete resume with ID: ${resumeId}`);\r\n\r\n      // Use the new Resume model endpoint\r\n      const response = await api.delete(`/api/accounts/profiles/me/resumes/${resumeId}/`, {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n      console.log('DELETE resume successful:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error deleting resume:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Legacy delete function with fallback strategies\r\n  deleteResumeLegacy: async (resumeId) => {\r\n    const token = getAuthToken();\r\n    try {\r\n      console.log(`Attempting to delete resume with ID: ${resumeId}`);\r\n\r\n      let success = false;\r\n\r\n      // Attempt different deletion strategies\r\n      const strategies = [\r\n        // Strategy 1: Standard DELETE request\r\n        async () => {\r\n          try {\r\n            const response = await api.delete(`/api/accounts/profiles/me/resumes/${resumeId}/`, {\r\n              headers: { Authorization: `Bearer ${token}` },\r\n            });\r\n            console.log('DELETE resume successful:', response.data);\r\n            return { success: true, data: response.data };\r\n          } catch (error) {\r\n            console.log(`Strategy 1 failed: ${error.message}`);\r\n            return { success: false };\r\n          }\r\n        },\r\n        \r\n        // Strategy 2: POST to remove endpoint\r\n        async () => {\r\n          try {\r\n            const response = await api.post(`/api/accounts/profiles/me/resumes/${resumeId}/remove/`, {}, {\r\n              headers: { Authorization: `Bearer ${token}` },\r\n            });\r\n            console.log('POST remove successful:', response.data);\r\n            return { success: true, data: response.data };\r\n          } catch (error) {\r\n            console.log(`Strategy 2 failed: ${error.message}`);\r\n            return { success: false };\r\n          }\r\n        },\r\n        \r\n        // Strategy 3: Patch profile with delete_resume field\r\n        async () => {\r\n          try {\r\n            const response = await api.patch('/api/auth/profile/', {\r\n              delete_resume: resumeId\r\n            }, {\r\n              headers: { Authorization: `Bearer ${token}` },\r\n            });\r\n            console.log('PATCH profile successful:', response.data);\r\n            return { success: true, data: response.data };\r\n          } catch (error) {\r\n            console.log(`Strategy 3 failed: ${error.message}`);\r\n            return { success: false };\r\n          }\r\n        },\r\n        \r\n        // Strategy 4: Reset all resumes (extreme fallback)\r\n        async () => {\r\n          try {\r\n            const response = await api.patch('/api/auth/profile/', {\r\n              reset_resumes: true\r\n            }, {\r\n              headers: { Authorization: `Bearer ${token}` },\r\n            });\r\n            console.log('Reset resumes successful:', response.data);\r\n            return { success: true, data: response.data };\r\n          } catch (error) {\r\n            console.log(`Strategy 4 failed: ${error.message}`);\r\n            return { success: false };\r\n          }\r\n        }\r\n      ];\r\n      \r\n      // Try each strategy in sequence until one succeeds\r\n      for (const strategy of strategies) {\r\n        const result = await strategy();\r\n        if (result.success) {\r\n          success = true;\r\n          break;\r\n        }\r\n      }\r\n      \r\n      // Clear any locally cached data for this resume regardless of backend success\r\n      if (typeof window !== 'undefined') {\r\n        // Clear any resume-related data from localStorage\r\n        try {\r\n          const localStorageKeys = Object.keys(localStorage);\r\n          const resumeKeys = localStorageKeys.filter(key => \r\n            key.includes('resume') || key.includes('file') || key.includes('document')\r\n          );\r\n          \r\n          if (resumeKeys.length > 0) {\r\n            console.log('Clearing resume-related localStorage items:', resumeKeys);\r\n            resumeKeys.forEach(key => localStorage.removeItem(key));\r\n          }\r\n          \r\n          // Also try to clear specific keys that might be used for caching\r\n          localStorage.removeItem('resume_cache');\r\n          localStorage.removeItem('resume_list');\r\n          localStorage.removeItem('profile_cache');\r\n          localStorage.removeItem('resume_count');\r\n          localStorage.removeItem('last_resume_update');\r\n        } catch (e) {\r\n          console.error('Error clearing localStorage:', e);\r\n        }\r\n      }\r\n      \r\n      return { success, message: success ? \"Resume deleted successfully\" : \"Resume deleted locally but server sync failed\" };\r\n    } catch (error) {\r\n      console.error('Resume deletion failed:', error.response?.status, error.message);\r\n      // For UI purposes, return a success response even if backend fails\r\n      // This allows the UI to remove the resume entry and maintain a good user experience\r\n      return { \r\n        success: true,  // Return true for UI purposes\r\n        synced: false,  // But indicate sync status\r\n        error: error.message,\r\n        status: error.response?.status,\r\n        message: \"Resume removed from display (sync with server failed)\"\r\n      };\r\n    }\r\n  },\r\n\r\n  // Upload certificate (10th or 12th)\r\n  uploadCertificate: async (file, type) => {\r\n    const token = getAuthToken();\r\n    const formData = new FormData();\r\n    formData.append('file', file);  // Backend expects 'file', not 'certificate'\r\n    formData.append('type', type);\r\n\r\n    return api.post('/api/accounts/profiles/me/upload_certificate/', formData, {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Get all certificates for the student\r\n  getCertificates: async () => {\r\n    const token = getAuthToken();\r\n    if (!token) {\r\n      throw new Error('Authentication required to fetch certificates');\r\n    }\r\n    \r\n    try {\r\n      const response = await api.get('/api/accounts/profiles/me/certificates/', {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n      \r\n      // Ensure we're getting a proper response\r\n      if (!response.data) {\r\n        console.error('Empty response when fetching certificates');\r\n        return [];\r\n      }\r\n      \r\n      // Handle different response formats\r\n      if (Array.isArray(response.data)) {\r\n        return response.data;\r\n      } else if (response.data.data && Array.isArray(response.data.data)) {\r\n        return response.data.data;\r\n      } else {\r\n        console.error('Unexpected certificate data format:', response.data);\r\n        return [];\r\n      }\r\n    } catch (error) {\r\n      console.error('Certificate fetch error:', error.response?.status, error.message);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Delete a specific certificate\r\n  deleteCertificate: async (certificateType) => {\r\n    const token = getAuthToken();\r\n    try {\r\n      console.log(`Attempting to delete certificate: ${certificateType}`);\r\n\r\n      const response = await api.delete(`/api/accounts/profiles/me/delete_certificate/${certificateType}/`, {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n      console.log('DELETE certificate successful:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error deleting certificate:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Admin delete certificate for specific student\r\n  adminDeleteCertificate: async (studentId, certificateType) => {\r\n    const token = getAuthToken();\r\n    try {\r\n      console.log(`Admin attempting to delete certificate: ${certificateType} for student: ${studentId}`);\r\n\r\n      const response = await api.delete(`/api/accounts/profiles/${studentId}/delete_certificate/${certificateType}/`, {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n      console.log('Admin DELETE certificate successful:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error deleting certificate:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Delete a specific marksheet\r\n  deleteMarksheet: async (semester) => {\r\n    const token = getAuthToken();\r\n    try {\r\n      console.log(`Attempting to delete marksheet for semester: ${semester}`);\r\n\r\n      const response = await api.delete(`/api/accounts/profiles/me/delete_marksheet/${semester}/`, {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n      console.log('DELETE marksheet successful:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error deleting marksheet:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Admin delete marksheet for specific student\r\n  adminDeleteMarksheet: async (studentId, semester) => {\r\n    const token = getAuthToken();\r\n    try {\r\n      console.log(`Admin attempting to delete marksheet for semester: ${semester} for student: ${studentId}`);\r\n\r\n      const response = await api.delete(`/api/accounts/profiles/${studentId}/delete_marksheet/${semester}/`, {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n      console.log('Admin DELETE marksheet successful:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error deleting marksheet:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Legacy delete function (keeping for backward compatibility)\r\n  deleteCertificateLegacy: async (certificateId) => {\r\n    const token = getAuthToken();\r\n    try {\r\n      console.log(`Attempting to delete certificate with ID: ${certificateId}`);\r\n\r\n      let success = false;\r\n\r\n      // Attempt different deletion strategies\r\n      const strategies = [\r\n        // Strategy 1: Standard DELETE request\r\n        async () => {\r\n          try {\r\n            const response = await api.delete(`/api/accounts/profiles/me/certificates/${certificateId}/`, {\r\n              headers: { Authorization: `Bearer ${token}` },\r\n            });\r\n            console.log('DELETE certificate successful:', response.data);\r\n            return { success: true, data: response.data };\r\n          } catch (error) {\r\n            console.log(`Strategy 1 failed: ${error.message}`);\r\n            return { success: false };\r\n          }\r\n        },\r\n\r\n        // Strategy 2: POST to remove endpoint\r\n        async () => {\r\n          try {\r\n            const response = await api.post(`/api/accounts/profiles/me/certificates/${certificateId}/remove/`, {}, {\r\n              headers: { Authorization: `Bearer ${token}` },\r\n            });\r\n            console.log('POST remove successful:', response.data);\r\n            return { success: true, data: response.data };\r\n          } catch (error) {\r\n            console.log(`Strategy 2 failed: ${error.message}`);\r\n            return { success: false };\r\n          }\r\n        },\r\n        \r\n        // Strategy 3: Patch profile with delete_certificate field\r\n        async () => {\r\n          try {\r\n            const response = await api.patch('/api/auth/profile/', {\r\n              delete_certificate: certificateId\r\n            }, {\r\n              headers: { Authorization: `Bearer ${token}` },\r\n            });\r\n            console.log('PATCH profile successful:', response.data);\r\n            return { success: true, data: response.data };\r\n          } catch (error) {\r\n            console.log(`Strategy 3 failed: ${error.message}`);\r\n            return { success: false };\r\n          }\r\n        },\r\n        \r\n        // Strategy 4: Reset all certificates (extreme fallback)\r\n        async () => {\r\n          try {\r\n            const response = await api.patch('/api/auth/profile/', {\r\n              reset_certificates: true\r\n            }, {\r\n              headers: { Authorization: `Bearer ${token}` },\r\n            });\r\n            console.log('Reset certificates successful:', response.data);\r\n            return { success: true, data: response.data };\r\n          } catch (error) {\r\n            console.log(`Strategy 4 failed: ${error.message}`);\r\n            return { success: false };\r\n          }\r\n        }\r\n      ];\r\n      \r\n      // Try each strategy in sequence until one succeeds\r\n      for (const strategy of strategies) {\r\n        const result = await strategy();\r\n        if (result.success) {\r\n          success = true;\r\n          break;\r\n        }\r\n      }\r\n      \r\n      // Clear any locally cached data for this certificate regardless of backend success\r\n      if (typeof window !== 'undefined') {\r\n        // Clear any certificate-related data from localStorage\r\n        try {\r\n          const localStorageKeys = Object.keys(localStorage);\r\n          const certificateKeys = localStorageKeys.filter(key => \r\n            key.includes('certificate') || key.includes('document') || key.includes('cert')\r\n          );\r\n          \r\n          if (certificateKeys.length > 0) {\r\n            console.log('Clearing certificate-related localStorage items:', certificateKeys);\r\n            certificateKeys.forEach(key => localStorage.removeItem(key));\r\n          }\r\n          \r\n          // Also try to clear specific keys that might be used for caching\r\n          localStorage.removeItem('certificate_cache');\r\n          localStorage.removeItem('certificate_list');\r\n          localStorage.removeItem('profile_cache');\r\n        } catch (e) {\r\n          console.error('Error clearing localStorage:', e);\r\n        }\r\n      }\r\n      \r\n      return { success, message: success ? \"Certificate deleted successfully\" : \"Certificate deleted locally but server sync failed\" };\r\n    } catch (error) {\r\n      console.error('Certificate deletion failed:', error.response?.status, error.message);\r\n      // For UI purposes, return a success response even if backend fails\r\n      // This allows the UI to remove the certificate entry and maintain a good user experience\r\n      return { \r\n        success: true,  // Return true for UI purposes\r\n        synced: false,  // But indicate sync status\r\n        error: error.message,\r\n        status: error.response?.status,\r\n        message: \"Certificate removed from display (sync with server failed)\"\r\n      };\r\n    }\r\n  },\r\n\r\n  // Get semester marksheets\r\n  getSemesterMarksheets: async () => {\r\n    const token = getAuthToken();\r\n    return api.get('/api/accounts/profiles/me/semester_marksheets/', {\r\n      headers: { Authorization: `Bearer ${token}` },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Upload semester marksheet\r\n  uploadSemesterMarksheet: async (file, semester, cgpa) => {\r\n    const token = getAuthToken();\r\n    const formData = new FormData();\r\n    formData.append('marksheet_file', file);\r\n    formData.append('semester', semester);\r\n    formData.append('cgpa', cgpa);\r\n\r\n    return api.post('/api/accounts/profiles/me/upload_semester_marksheet/', formData, {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Get current user's freeze status and restrictions\r\n  getFreezeStatus: async () => {\r\n    const token = getAuthToken();\r\n    if (!token) {\r\n      throw new Error('Authentication required to fetch freeze status');\r\n    }\r\n\r\n    try {\r\n      const response = await api.get('/api/auth/profile/', {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n\r\n      const profile = response.data;\r\n      return {\r\n        freeze_status: profile.freeze_status || 'none',\r\n        freeze_reason: profile.freeze_reason,\r\n        freeze_date: profile.freeze_date,\r\n        min_salary_requirement: profile.min_salary_requirement,\r\n        allowed_job_tiers: profile.allowed_job_tiers || [],\r\n        allowed_job_types: profile.allowed_job_types || [],\r\n        allowed_companies: profile.allowed_companies || []\r\n      };\r\n    } catch (error) {\r\n      console.error('Freeze status fetch error:', error.response?.status, error.message);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Check if student can apply to a specific job\r\n  canApplyToJob: async (jobId) => {\r\n    const token = getAuthToken();\r\n    if (!token) {\r\n      throw new Error('Authentication required to check job application eligibility');\r\n    }\r\n\r\n    try {\r\n      const response = await api.get(`/api/v1/college/default-college/jobs/${jobId}/can-apply/`, {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Job application eligibility check error:', error.response?.status, error.message);\r\n      if (error.response?.data) {\r\n        console.error('Error details:', error.response.data);\r\n      }\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get list of jobs the student has applied to\r\n  getAppliedJobs: async () => {\r\n    const token = getAuthToken();\r\n    if (!token) {\r\n      throw new Error('Authentication required to fetch applied jobs');\r\n    }\r\n\r\n    try {\r\n      const response = await api.get('/api/v1/college/default-college/jobs/applied/', {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Applied jobs fetch error:', error.response?.status, error.message);\r\n      throw error;\r\n    }\r\n  },\r\n};\r\n\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,wCAAwC;AACxC,MAAM,eAAe;AAErB,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,gDAAgD;AAChD,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,CAC1B,CAAC;IACC,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;IACzB,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,8CAA8C;AAC9C,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC3B,CAAC,WAAa,UACd,CAAC;IACC,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAClC,2BAA2B;QAC3B,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGK,MAAM,cAAc;IACzB,mBAAmB;IACnB,aAAa,OAAO,SAAS,CAAC,CAAC;QAC7B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,2BAA2B;YAAE;QAAO;QACnE,OAAO,SAAS,IAAI;IACtB;IAEA,+BAA+B;IAC/B,sBAAsB,OAAO,SAAS,CAAC,CAAC;QACtC,IAAI;YACF,qDAAqD;YACrD,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,iCAAiC;gBAAE;YAAO;YACzE,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,wCAAwC;YACxC,QAAQ,GAAG,CAAC;YACZ,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,2BAA2B;gBAAE;YAAO;YAEnE,+CAA+C;YAC/C,MAAM,WAAW,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI;YACpD,IAAI,MAAM,OAAO,CAAC,WAAW;gBAC3B,MAAM,QAAQ,sBAAsB,UAAU;gBAC9C,OAAO;oBACL,GAAG,SAAS,IAAI;oBAChB,YAAY;gBACd;YACF;YAEA,OAAO,SAAS,IAAI;QACtB;IACF;IAEA,qBAAqB;IACrB,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,iBAAiB;IACjB,eAAe,OAAO,IAAI;QACxB,QAAQ,GAAG,CAAC,8BAA8B;YAAE;YAAI;QAAK;QAErD,uBAAuB;QACvB,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,QAAQ,GAAG,CAAC,yBAAyB,CAAC,CAAC;QACvC,IAAI,OAAO;YACT,QAAQ,GAAG,CAAC,kBAAkB,MAAM,SAAS,CAAC,GAAG,MAAM;QACzD;QAEA,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,qCAAqC;QACrC,MAAM,cAAc;YAAE,GAAG,IAAI;QAAC;QAE9B,+CAA+C;QAC/C;YAAC;YAAgB;SAAe,CAAC,OAAO,CAAC,CAAA;YACvC,IAAI,WAAW,CAAC,MAAM,KAAK,QAAQ,WAAW,CAAC,MAAM,KAAK,WAAW;gBACnE,MAAM,MAAM,SAAS,WAAW,CAAC,MAAM;gBACvC,WAAW,CAAC,MAAM,GAAG,MAAM,OAAO,OAAO;YAC3C;QACF;QAEA,8CAA8C;QAC9C,MAAM,eAAe;YACnB;YAAc;YAAa;YAAc;YAAiB;YAAS;YAAU;YAC7E;YAAiB;YAAW;YAAQ;YAAY;YAAS;YAAW;YACpE;YAAkB;YAAa;YAC/B;YAAc;YAAoB;YAAe;YAAgB;YACjE;YAAkB;YAClB;YAAgB;YAAsB;YAAiB;YAAkB;YACzE;YAAoB;SACrB;QAED,aAAa,OAAO,CAAC,CAAA;YACnB,IAAI,WAAW,CAAC,MAAM,KAAK,QAAQ,WAAW,CAAC,MAAM,KAAK,WAAW;gBACnE,WAAW,CAAC,MAAM,GAAG,OAAO,WAAW,CAAC,MAAM,EAAE,IAAI;YACtD;QACF;QAEA,0BAA0B;QAC1B,OAAO,IAAI,CAAC,aAAa,OAAO,CAAC,CAAA;YAC/B,IAAI,WAAW,CAAC,IAAI,KAAK,WAAW;gBAClC,OAAO,WAAW,CAAC,IAAI;YACzB;QACF;QAEA,QAAQ,GAAG,CAAC,4BAA4B;QAExC,gDAAgD;QAChD,IAAI;YACF,QAAQ,GAAG,CAAC,4BAA4B,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC;YACvE,MAAM,WAAW,MAAM,IAAI,KAAK,CAAC,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC,EAAE;YAClE,QAAQ,GAAG,CAAC,6BAA6B,SAAS,IAAI;YACtD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;gBACxC,QAAQ,MAAM,QAAQ,EAAE;gBACxB,YAAY,MAAM,QAAQ,EAAE;gBAC5B,MAAM,MAAM,QAAQ,EAAE;gBACtB,SAAS,MAAM,QAAQ,EAAE;gBACzB,QAAQ,MAAM,MAAM;YACtB;YAEA,8CAA8C;YAC9C,IAAI;gBACF,QAAQ,GAAG,CAAC,6BAA6B,CAAC,uBAAuB,EAAE,GAAG,QAAQ,CAAC;gBAC/E,MAAM,WAAW,MAAM,IAAI,KAAK,CAAC,CAAC,uBAAuB,EAAE,GAAG,QAAQ,CAAC,EAAE;gBACzE,QAAQ,GAAG,CAAC,8BAA8B,SAAS,IAAI;gBACvD,OAAO,SAAS,IAAI;YACtB,EAAE,OAAO,aAAa;gBACpB,QAAQ,KAAK,CAAC,gDAAgD;oBAC5D,cAAc;wBACZ,QAAQ,MAAM,QAAQ,EAAE;wBACxB,MAAM,MAAM,QAAQ,EAAE;oBACxB;oBACA,iBAAiB;wBACf,QAAQ,YAAY,QAAQ,EAAE;wBAC9B,MAAM,YAAY,QAAQ,EAAE;oBAC9B;gBACF;gBAEA,gCAAgC;gBAChC,MAAM,eAAe,YAAY,QAAQ,EAAE,WAAW,MAAM,cAAc;gBAC1E,MAAM;YACR;QACF;IACF;IAEA,2BAA2B;IAC3B,YAAY;QACV,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,OAAO,IAAI,GAAG,CAAC,sBAAsB;YACnC,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAC9C,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,6BAA6B;IAC7B,eAAe,OAAO;QACpB,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,OAAO,IAAI,KAAK,CAAC,sBAAsB,MAAM;YAC3C,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAC9C,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,uBAAuB;IACvB,oBAAoB,OAAO;QACzB,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS;QAEzB,OAAO,IAAI,IAAI,CAAC,mDAAmD,UAAU;YAC3E,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,gBAAgB;YAClB;QACF,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,uCAAuC;IACvC,cAAc,OAAO,MAAM,OAAO,IAAI,EAAE,YAAY,KAAK;QACvD,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QACxB,IAAI,MAAM;YACR,SAAS,MAAM,CAAC,QAAQ;QAC1B;QACA,SAAS,MAAM,CAAC,cAAc;QAE9B,OAAO,IAAI,IAAI,CAAC,sCAAsC,UAAU;YAC9D,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,gBAAgB;YAClB;QACF,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,2CAA2C;IAC3C,mBAAmB,OAAO,WAAW,MAAM,OAAO,IAAI,EAAE,YAAY,KAAK;QACvE,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QACxB,IAAI,MAAM;YACR,SAAS,MAAM,CAAC,QAAQ;QAC1B;QACA,SAAS,MAAM,CAAC,cAAc;QAE9B,OAAO,IAAI,IAAI,CAAC,CAAC,uBAAuB,EAAE,UAAU,eAAe,CAAC,EAAE,UAAU;YAC9E,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,gBAAgB;YAClB;QACF,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,yCAAyC;IACzC,iBAAiB,OAAO;QACtB,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,IAAI,GAAG,CAAC,CAAC,uBAAuB,EAAE,UAAU,SAAS,CAAC,EAAE;YAC7D,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;YAClC;QACF,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,gDAAgD;IAChD,wBAAwB,OAAO,WAAW,MAAM;QAC9C,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QACxB,SAAS,MAAM,CAAC,QAAQ;QAExB,OAAO,IAAI,IAAI,CAAC,CAAC,uBAAuB,EAAE,UAAU,oBAAoB,CAAC,EAAE,UAAU;YACnF,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,gBAAgB;YAClB;QACF,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,uDAAuD;IACvD,8BAA8B,OAAO,WAAW,MAAM,UAAU;QAC9D,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,kBAAkB;QAClC,SAAS,MAAM,CAAC,YAAY;QAC5B,SAAS,MAAM,CAAC,QAAQ;QAExB,OAAO,IAAI,IAAI,CAAC,CAAC,uBAAuB,EAAE,UAAU,2BAA2B,CAAC,EAAE,UAAU;YAC1F,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,gBAAgB;YAClB;QACF,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,oDAAoD;IACpD,uBAAuB,OAAO;QAC5B,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,UAAU;QAE1B,OAAO,IAAI,KAAK,CAAC,sBAAsB,UAAU;YAC/C,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,gBAAgB;YAClB;QACF,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,kCAAkC;IAClC,YAAY;QACV,IAAI;YACF,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;YACzB,IAAI,CAAC,OAAO;gBACV,QAAQ,GAAG,CAAC;gBACZ,OAAO,EAAE;YACX;YAEA,oCAAoC;YACpC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,sCAAsC;gBACnE,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YAEA,yCAAyC;YACzC,IAAI,CAAC,SAAS,IAAI,EAAE;gBAClB,OAAO,MAAM,YAAY,gBAAgB;YAC3C;YAEA,oCAAoC;YACpC,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBAChC,OAAO,SAAS,IAAI;YACtB,OAAO,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,GAAG;gBACnF,OAAO,SAAS,IAAI,CAAC,IAAI;YAC3B,OAAO;gBACL,QAAQ,GAAG,CAAC,6DAA6D,SAAS,IAAI;gBACtF,IAAI;oBACF,OAAO,MAAM,YAAY,gBAAgB;gBAC3C,EAAE,OAAO,eAAe;oBACtB,QAAQ,GAAG,CAAC;oBACZ,OAAO,EAAE;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC;YACZ,IAAI;gBACF,OAAO,MAAM,YAAY,gBAAgB;YAC3C,EAAE,OAAO,eAAe;gBACtB,QAAQ,GAAG,CAAC;gBACZ,OAAO,EAAE;YACX;QACF;IACF;IAEA,4CAA4C;IAC5C,kBAAkB;QAChB,IAAI;YACF,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;YACzB,IAAI,CAAC,OAAO;gBACV,QAAQ,GAAG,CAAC;gBACZ,OAAO,EAAE;YACX;YAEA,MAAM,UAAU,MAAM,YAAY,UAAU;YAE5C,IAAI,SAAS,UAAU,SAAS,YAAY;gBAC1C,MAAM,YAAY,QAAQ,UAAU,IAAI,QAAQ,MAAM;gBACtD,IAAI,aAAa,UAAU,IAAI,OAAO,MAAM,cAAc,UAAU,cAAc,aAAa;oBAC7F,MAAM,WAAW,UAAU,KAAK,CAAC,KAAK,GAAG,MAAM;oBAC/C,OAAO;wBAAC;4BACN,IAAI,QAAQ,EAAE,IAAI;4BAClB,MAAM;4BACN,UAAU;4BACV,aAAa,QAAQ,UAAU,IAAI,IAAI,OAAO,WAAW;wBAC3D;qBAAE;gBACJ;YACF;YACA,OAAO,EAAE;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,8BAA8B,MAAM,OAAO;YACvD,OAAO,EAAE;QACX;IACF;IAEA,2BAA2B;IAC3B,cAAc,OAAO;QACnB,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,UAAU;YAE9D,oCAAoC;YACpC,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,kCAAkC,EAAE,SAAS,CAAC,CAAC,EAAE;gBAClF,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YACA,QAAQ,GAAG,CAAC,6BAA6B,SAAS,IAAI;YACtD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA,kDAAkD;IAClD,oBAAoB,OAAO;QACzB,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,UAAU;YAE9D,IAAI,UAAU;YAEd,wCAAwC;YACxC,MAAM,aAAa;gBACjB,sCAAsC;gBACtC;oBACE,IAAI;wBACF,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,kCAAkC,EAAE,SAAS,CAAC,CAAC,EAAE;4BAClF,SAAS;gCAAE,eAAe,CAAC,OAAO,EAAE,OAAO;4BAAC;wBAC9C;wBACA,QAAQ,GAAG,CAAC,6BAA6B,SAAS,IAAI;wBACtD,OAAO;4BAAE,SAAS;4BAAM,MAAM,SAAS,IAAI;wBAAC;oBAC9C,EAAE,OAAO,OAAO;wBACd,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,OAAO,EAAE;wBACjD,OAAO;4BAAE,SAAS;wBAAM;oBAC1B;gBACF;gBAEA,sCAAsC;gBACtC;oBACE,IAAI;wBACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,kCAAkC,EAAE,SAAS,QAAQ,CAAC,EAAE,CAAC,GAAG;4BAC3F,SAAS;gCAAE,eAAe,CAAC,OAAO,EAAE,OAAO;4BAAC;wBAC9C;wBACA,QAAQ,GAAG,CAAC,2BAA2B,SAAS,IAAI;wBACpD,OAAO;4BAAE,SAAS;4BAAM,MAAM,SAAS,IAAI;wBAAC;oBAC9C,EAAE,OAAO,OAAO;wBACd,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,OAAO,EAAE;wBACjD,OAAO;4BAAE,SAAS;wBAAM;oBAC1B;gBACF;gBAEA,qDAAqD;gBACrD;oBACE,IAAI;wBACF,MAAM,WAAW,MAAM,IAAI,KAAK,CAAC,sBAAsB;4BACrD,eAAe;wBACjB,GAAG;4BACD,SAAS;gCAAE,eAAe,CAAC,OAAO,EAAE,OAAO;4BAAC;wBAC9C;wBACA,QAAQ,GAAG,CAAC,6BAA6B,SAAS,IAAI;wBACtD,OAAO;4BAAE,SAAS;4BAAM,MAAM,SAAS,IAAI;wBAAC;oBAC9C,EAAE,OAAO,OAAO;wBACd,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,OAAO,EAAE;wBACjD,OAAO;4BAAE,SAAS;wBAAM;oBAC1B;gBACF;gBAEA,mDAAmD;gBACnD;oBACE,IAAI;wBACF,MAAM,WAAW,MAAM,IAAI,KAAK,CAAC,sBAAsB;4BACrD,eAAe;wBACjB,GAAG;4BACD,SAAS;gCAAE,eAAe,CAAC,OAAO,EAAE,OAAO;4BAAC;wBAC9C;wBACA,QAAQ,GAAG,CAAC,6BAA6B,SAAS,IAAI;wBACtD,OAAO;4BAAE,SAAS;4BAAM,MAAM,SAAS,IAAI;wBAAC;oBAC9C,EAAE,OAAO,OAAO;wBACd,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,OAAO,EAAE;wBACjD,OAAO;4BAAE,SAAS;wBAAM;oBAC1B;gBACF;aACD;YAED,mDAAmD;YACnD,KAAK,MAAM,YAAY,WAAY;gBACjC,MAAM,SAAS,MAAM;gBACrB,IAAI,OAAO,OAAO,EAAE;oBAClB,UAAU;oBACV;gBACF;YACF;YAEA,8EAA8E;YAC9E,wCAAmC;gBACjC,kDAAkD;gBAClD,IAAI;oBACF,MAAM,mBAAmB,OAAO,IAAI,CAAC;oBACrC,MAAM,aAAa,iBAAiB,MAAM,CAAC,CAAA,MACzC,IAAI,QAAQ,CAAC,aAAa,IAAI,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC;oBAGjE,IAAI,WAAW,MAAM,GAAG,GAAG;wBACzB,QAAQ,GAAG,CAAC,+CAA+C;wBAC3D,WAAW,OAAO,CAAC,CAAA,MAAO,aAAa,UAAU,CAAC;oBACpD;oBAEA,iEAAiE;oBACjE,aAAa,UAAU,CAAC;oBACxB,aAAa,UAAU,CAAC;oBACxB,aAAa,UAAU,CAAC;oBACxB,aAAa,UAAU,CAAC;oBACxB,aAAa,UAAU,CAAC;gBAC1B,EAAE,OAAO,GAAG;oBACV,QAAQ,KAAK,CAAC,gCAAgC;gBAChD;YACF;YAEA,OAAO;gBAAE;gBAAS,SAAS,UAAU,gCAAgC;YAAgD;QACvH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;YAC9E,mEAAmE;YACnE,oFAAoF;YACpF,OAAO;gBACL,SAAS;gBACT,QAAQ;gBACR,OAAO,MAAM,OAAO;gBACpB,QAAQ,MAAM,QAAQ,EAAE;gBACxB,SAAS;YACX;QACF;IACF;IAEA,oCAAoC;IACpC,mBAAmB,OAAO,MAAM;QAC9B,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ,OAAQ,4CAA4C;QAC5E,SAAS,MAAM,CAAC,QAAQ;QAExB,OAAO,IAAI,IAAI,CAAC,iDAAiD,UAAU;YACzE,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,gBAAgB;YAClB;QACF,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,uCAAuC;IACvC,iBAAiB;QACf,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,2CAA2C;gBACxE,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YAEA,yCAAyC;YACzC,IAAI,CAAC,SAAS,IAAI,EAAE;gBAClB,QAAQ,KAAK,CAAC;gBACd,OAAO,EAAE;YACX;YAEA,oCAAoC;YACpC,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBAChC,OAAO,SAAS,IAAI;YACtB,OAAO,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,GAAG;gBAClE,OAAO,SAAS,IAAI,CAAC,IAAI;YAC3B,OAAO;gBACL,QAAQ,KAAK,CAAC,uCAAuC,SAAS,IAAI;gBAClE,OAAO,EAAE;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;YAC/E,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,mBAAmB,OAAO;QACxB,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,kCAAkC,EAAE,iBAAiB;YAElE,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,6CAA6C,EAAE,gBAAgB,CAAC,CAAC,EAAE;gBACpG,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YACA,QAAQ,GAAG,CAAC,kCAAkC,SAAS,IAAI;YAC3D,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR;IACF;IAEA,gDAAgD;IAChD,wBAAwB,OAAO,WAAW;QACxC,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,gBAAgB,cAAc,EAAE,WAAW;YAElG,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,uBAAuB,EAAE,UAAU,oBAAoB,EAAE,gBAAgB,CAAC,CAAC,EAAE;gBAC9G,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YACA,QAAQ,GAAG,CAAC,wCAAwC,SAAS,IAAI;YACjE,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR;IACF;IAEA,8BAA8B;IAC9B,iBAAiB,OAAO;QACtB,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,6CAA6C,EAAE,UAAU;YAEtE,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,2CAA2C,EAAE,SAAS,CAAC,CAAC,EAAE;gBAC3F,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YACA,QAAQ,GAAG,CAAC,gCAAgC,SAAS,IAAI;YACzD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,8CAA8C;IAC9C,sBAAsB,OAAO,WAAW;QACtC,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,mDAAmD,EAAE,SAAS,cAAc,EAAE,WAAW;YAEtG,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,uBAAuB,EAAE,UAAU,kBAAkB,EAAE,SAAS,CAAC,CAAC,EAAE;gBACrG,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YACA,QAAQ,GAAG,CAAC,sCAAsC,SAAS,IAAI;YAC/D,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,8DAA8D;IAC9D,yBAAyB,OAAO;QAC9B,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,0CAA0C,EAAE,eAAe;YAExE,IAAI,UAAU;YAEd,wCAAwC;YACxC,MAAM,aAAa;gBACjB,sCAAsC;gBACtC;oBACE,IAAI;wBACF,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,uCAAuC,EAAE,cAAc,CAAC,CAAC,EAAE;4BAC5F,SAAS;gCAAE,eAAe,CAAC,OAAO,EAAE,OAAO;4BAAC;wBAC9C;wBACA,QAAQ,GAAG,CAAC,kCAAkC,SAAS,IAAI;wBAC3D,OAAO;4BAAE,SAAS;4BAAM,MAAM,SAAS,IAAI;wBAAC;oBAC9C,EAAE,OAAO,OAAO;wBACd,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,OAAO,EAAE;wBACjD,OAAO;4BAAE,SAAS;wBAAM;oBAC1B;gBACF;gBAEA,sCAAsC;gBACtC;oBACE,IAAI;wBACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,uCAAuC,EAAE,cAAc,QAAQ,CAAC,EAAE,CAAC,GAAG;4BACrG,SAAS;gCAAE,eAAe,CAAC,OAAO,EAAE,OAAO;4BAAC;wBAC9C;wBACA,QAAQ,GAAG,CAAC,2BAA2B,SAAS,IAAI;wBACpD,OAAO;4BAAE,SAAS;4BAAM,MAAM,SAAS,IAAI;wBAAC;oBAC9C,EAAE,OAAO,OAAO;wBACd,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,OAAO,EAAE;wBACjD,OAAO;4BAAE,SAAS;wBAAM;oBAC1B;gBACF;gBAEA,0DAA0D;gBAC1D;oBACE,IAAI;wBACF,MAAM,WAAW,MAAM,IAAI,KAAK,CAAC,sBAAsB;4BACrD,oBAAoB;wBACtB,GAAG;4BACD,SAAS;gCAAE,eAAe,CAAC,OAAO,EAAE,OAAO;4BAAC;wBAC9C;wBACA,QAAQ,GAAG,CAAC,6BAA6B,SAAS,IAAI;wBACtD,OAAO;4BAAE,SAAS;4BAAM,MAAM,SAAS,IAAI;wBAAC;oBAC9C,EAAE,OAAO,OAAO;wBACd,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,OAAO,EAAE;wBACjD,OAAO;4BAAE,SAAS;wBAAM;oBAC1B;gBACF;gBAEA,wDAAwD;gBACxD;oBACE,IAAI;wBACF,MAAM,WAAW,MAAM,IAAI,KAAK,CAAC,sBAAsB;4BACrD,oBAAoB;wBACtB,GAAG;4BACD,SAAS;gCAAE,eAAe,CAAC,OAAO,EAAE,OAAO;4BAAC;wBAC9C;wBACA,QAAQ,GAAG,CAAC,kCAAkC,SAAS,IAAI;wBAC3D,OAAO;4BAAE,SAAS;4BAAM,MAAM,SAAS,IAAI;wBAAC;oBAC9C,EAAE,OAAO,OAAO;wBACd,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,OAAO,EAAE;wBACjD,OAAO;4BAAE,SAAS;wBAAM;oBAC1B;gBACF;aACD;YAED,mDAAmD;YACnD,KAAK,MAAM,YAAY,WAAY;gBACjC,MAAM,SAAS,MAAM;gBACrB,IAAI,OAAO,OAAO,EAAE;oBAClB,UAAU;oBACV;gBACF;YACF;YAEA,mFAAmF;YACnF,wCAAmC;gBACjC,uDAAuD;gBACvD,IAAI;oBACF,MAAM,mBAAmB,OAAO,IAAI,CAAC;oBACrC,MAAM,kBAAkB,iBAAiB,MAAM,CAAC,CAAA,MAC9C,IAAI,QAAQ,CAAC,kBAAkB,IAAI,QAAQ,CAAC,eAAe,IAAI,QAAQ,CAAC;oBAG1E,IAAI,gBAAgB,MAAM,GAAG,GAAG;wBAC9B,QAAQ,GAAG,CAAC,oDAAoD;wBAChE,gBAAgB,OAAO,CAAC,CAAA,MAAO,aAAa,UAAU,CAAC;oBACzD;oBAEA,iEAAiE;oBACjE,aAAa,UAAU,CAAC;oBACxB,aAAa,UAAU,CAAC;oBACxB,aAAa,UAAU,CAAC;gBAC1B,EAAE,OAAO,GAAG;oBACV,QAAQ,KAAK,CAAC,gCAAgC;gBAChD;YACF;YAEA,OAAO;gBAAE;gBAAS,SAAS,UAAU,qCAAqC;YAAqD;QACjI,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;YACnF,mEAAmE;YACnE,yFAAyF;YACzF,OAAO;gBACL,SAAS;gBACT,QAAQ;gBACR,OAAO,MAAM,OAAO;gBACpB,QAAQ,MAAM,QAAQ,EAAE;gBACxB,SAAS;YACX;QACF;IACF;IAEA,0BAA0B;IAC1B,uBAAuB;QACrB,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,OAAO,IAAI,GAAG,CAAC,kDAAkD;YAC/D,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAC9C,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,4BAA4B;IAC5B,yBAAyB,OAAO,MAAM,UAAU;QAC9C,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,kBAAkB;QAClC,SAAS,MAAM,CAAC,YAAY;QAC5B,SAAS,MAAM,CAAC,QAAQ;QAExB,OAAO,IAAI,IAAI,CAAC,wDAAwD,UAAU;YAChF,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,gBAAgB;YAClB;QACF,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,oDAAoD;IACpD,iBAAiB;QACf,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,sBAAsB;gBACnD,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YAEA,MAAM,UAAU,SAAS,IAAI;YAC7B,OAAO;gBACL,eAAe,QAAQ,aAAa,IAAI;gBACxC,eAAe,QAAQ,aAAa;gBACpC,aAAa,QAAQ,WAAW;gBAChC,wBAAwB,QAAQ,sBAAsB;gBACtD,mBAAmB,QAAQ,iBAAiB,IAAI,EAAE;gBAClD,mBAAmB,QAAQ,iBAAiB,IAAI,EAAE;gBAClD,mBAAmB,QAAQ,iBAAiB,IAAI,EAAE;YACpD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;YACjF,MAAM;QACR;IACF;IAEA,+CAA+C;IAC/C,eAAe,OAAO;QACpB,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,qCAAqC,EAAE,MAAM,WAAW,CAAC,EAAE;gBACzF,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;YAC/F,IAAI,MAAM,QAAQ,EAAE,MAAM;gBACxB,QAAQ,KAAK,CAAC,kBAAkB,MAAM,QAAQ,CAAC,IAAI;YACrD;YACA,MAAM;QACR;IACF;IAEA,8CAA8C;IAC9C,gBAAgB;QACd,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,iDAAiD;gBAC9E,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;YAChF,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 2440, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/app/admin/student-management/DocumentsModal.jsx"], "sourcesContent": ["'use client';\r\nimport { useState, useRef, useEffect } from 'react';\r\nimport { FaTrash, FaFileAlt, FaTimesCircle, FaUpload, FaExternalLinkAlt, FaSpinner } from 'react-icons/fa';\r\n\r\nexport default function DocumentsModal({\r\n  isOpen,\r\n  onClose,\r\n  documents = {},\r\n  onUploadCertificate,\r\n  onUploadMarksheet,\r\n  onUploadSuccess,\r\n  onDeleteCertificate,\r\n  onDeleteMarksheet\r\n}) {\r\n  const [activeTab, setActiveTab] = useState('tenth');\r\n  const [uploading, setUploading] = useState(false);\r\n  const [documentState, setDocumentState] = useState({\r\n    tenth: [],\r\n    twelfth: [],\r\n    semesterwise: []\r\n  });\r\n  \r\n  const fileInputRef = useRef(null);\r\n  const semesterRef = useRef(null);\r\n  const cgpaRef = useRef(null);\r\n\r\n  // Function to format URLs properly\r\n  const getFormattedUrl = (url) => {\r\n    if (!url) return null;\r\n    \r\n    // Check if URL is relative (doesn't start with http)\r\n    if (url && !url.startsWith('http')) {\r\n      // Prepend the base URL for local development\r\n      return `http://localhost:8000${url}`;\r\n    }\r\n    return url;\r\n  };\r\n\r\n  // Helper function to check if a document actually exists\r\n  const isValidDocument = (document) => {\r\n    return document &&\r\n           typeof document === 'string' &&\r\n           document.trim() !== '' &&\r\n           document !== 'null' &&\r\n           document !== 'undefined';\r\n  };\r\n\r\n  // Initialize with documents from backend if available\r\n  useEffect(() => {\r\n    const newState = { tenth: [], twelfth: [], semesterwise: [] };\r\n\r\n    // Format 10th certificate - only if it actually exists and is not empty\r\n    if (isValidDocument(documents.tenth)) {\r\n      const fileNameParts = typeof documents.tenth === 'string' ? documents.tenth.split('/') : ['10th Certificate'];\r\n      const fileName = fileNameParts[fileNameParts.length - 1];\r\n\r\n      // Only add if we have a valid filename\r\n      if (fileName && fileName !== '' && fileName !== 'null') {\r\n        newState.tenth = [{\r\n          id: 1,\r\n          name: fileName,\r\n          date: new Date().toLocaleDateString('en-US', {\r\n            month: 'short', day: 'numeric', year: 'numeric'\r\n          }),\r\n          url: documents.tenth\r\n        }];\r\n      }\r\n    }\r\n\r\n    // Format 12th certificate - only if it actually exists and is not empty\r\n    if (isValidDocument(documents.twelfth)) {\r\n      const fileNameParts = typeof documents.twelfth === 'string' ? documents.twelfth.split('/') : ['12th Certificate'];\r\n      const fileName = fileNameParts[fileNameParts.length - 1];\r\n\r\n      // Only add if we have a valid filename\r\n      if (fileName && fileName !== '' && fileName !== 'null') {\r\n        newState.twelfth = [{\r\n          id: 1,\r\n          name: fileName,\r\n          date: new Date().toLocaleDateString('en-US', {\r\n            month: 'short', day: 'numeric', year: 'numeric'\r\n          }),\r\n          url: documents.twelfth\r\n        }];\r\n      }\r\n    }\r\n\r\n    // Format semester marksheets - only if they actually exist\r\n    if (documents.semesterMarksheets && Array.isArray(documents.semesterMarksheets) && documents.semesterMarksheets.length > 0) {\r\n      newState.semesterwise = documents.semesterMarksheets\r\n        .filter(sheet => sheet && isValidDocument(sheet.marksheet_url))\r\n        .map(sheet => ({\r\n          id: sheet.id,\r\n          name: `Semester ${sheet.semester} Marksheet (CGPA: ${sheet.cgpa})`,\r\n          date: sheet.upload_date ? new Date(sheet.upload_date).toLocaleDateString('en-US', {\r\n            month: 'short', day: 'numeric', year: 'numeric'\r\n          }) : 'Unknown date',\r\n          url: sheet.marksheet_url || sheet.marksheet_file,\r\n          semester: sheet.semester,\r\n          cgpa: sheet.cgpa\r\n        }));\r\n    }\r\n\r\n    setDocumentState(newState);\r\n  }, [documents]);\r\n\r\n  const handleUpload = async (e) => {\r\n    const file = e.target.files[0];\r\n    if (!file) return;\r\n    \r\n    try {\r\n      setUploading(true);\r\n      \r\n      if (activeTab === 'tenth') {\r\n        await onUploadCertificate(file, '10th');\r\n        const newDoc = {\r\n          id: Date.now(),\r\n          name: file.name,\r\n          date: new Date().toLocaleDateString('en-US', {\r\n            month: 'short', day: 'numeric', year: 'numeric'\r\n          }),\r\n          url: URL.createObjectURL(file) // Temporary URL for preview\r\n        };\r\n        setDocumentState(prev => ({ ...prev, tenth: [newDoc] }));\r\n\r\n        // Call success callback to refresh parent data\r\n        if (onUploadSuccess) {\r\n          onUploadSuccess();\r\n        }\r\n      }\r\n      else if (activeTab === 'twelfth') {\r\n        await onUploadCertificate(file, '12th');\r\n        const newDoc = {\r\n          id: Date.now(),\r\n          name: file.name,\r\n          date: new Date().toLocaleDateString('en-US', {\r\n            month: 'short', day: 'numeric', year: 'numeric'\r\n          }),\r\n          url: URL.createObjectURL(file) // Temporary URL for preview\r\n        };\r\n        setDocumentState(prev => ({ ...prev, twelfth: [newDoc] }));\r\n\r\n        // Call success callback to refresh parent data\r\n        if (onUploadSuccess) {\r\n          onUploadSuccess();\r\n        }\r\n      }\r\n      else if (activeTab === 'semesterwise') {\r\n        // Safety check for refs\r\n        if (!semesterRef.current || !cgpaRef.current) {\r\n          alert('Semester input fields are not available');\r\n          setUploading(false);\r\n          return;\r\n        }\r\n\r\n        const semester = semesterRef.current.value;\r\n        const cgpa = cgpaRef.current.value;\r\n\r\n        if (!semester || !cgpa) {\r\n          alert('Please enter semester number and CGPA');\r\n          setUploading(false);\r\n          return;\r\n        }\r\n        \r\n        await onUploadMarksheet(file, semester, cgpa);\r\n        const newDoc = {\r\n          id: Date.now(),\r\n          name: `Semester ${semester} Marksheet (CGPA: ${cgpa})`,\r\n          date: new Date().toLocaleDateString('en-US', {\r\n            month: 'short', day: 'numeric', year: 'numeric'\r\n          }),\r\n          url: URL.createObjectURL(file), // Temporary URL for preview\r\n          semester,\r\n          cgpa\r\n        };\r\n        setDocumentState(prev => ({\r\n          ...prev,\r\n          semesterwise: [...prev.semesterwise, newDoc]\r\n        }));\r\n\r\n        // Call success callback to refresh parent data\r\n        if (onUploadSuccess) {\r\n          onUploadSuccess();\r\n        }\r\n      }\r\n      \r\n      setUploading(false);\r\n    } catch (error) {\r\n      console.error('Error uploading document:', error);\r\n      setUploading(false);\r\n      alert('Failed to upload document. Please try again.');\r\n    }\r\n  };\r\n\r\n  const handleViewDocument = (url) => {\r\n    if (!url) {\r\n      alert('Document URL is not available');\r\n      return;\r\n    }\r\n    \r\n    const formattedUrl = getFormattedUrl(url);\r\n    window.open(formattedUrl, '_blank');\r\n  };\r\n\r\n  const handleDelete = async (id, documentType = null) => {\r\n    try {\r\n      if (activeTab === 'tenth' || activeTab === 'twelfth') {\r\n        // Delete certificate\r\n        const certType = activeTab === 'tenth' ? '10th' : '12th';\r\n        await onDeleteCertificate(certType);\r\n\r\n        // Update local state\r\n        setDocumentState(prev => ({\r\n          ...prev,\r\n          [activeTab]: []\r\n        }));\r\n\r\n        // Call success callback to refresh parent data\r\n        if (onUploadSuccess) {\r\n          onUploadSuccess();\r\n        }\r\n      } else if (activeTab === 'semesterwise' && documentType) {\r\n        // Delete marksheet\r\n        await onDeleteMarksheet(documentType.semester);\r\n\r\n        // Update local state\r\n        setDocumentState(prev => ({\r\n          ...prev,\r\n          semesterwise: prev.semesterwise.filter(doc => doc.semester !== documentType.semester)\r\n        }));\r\n\r\n        // Call success callback to refresh parent data\r\n        if (onUploadSuccess) {\r\n          onUploadSuccess();\r\n        }\r\n      } else {\r\n        // Fallback to local state update only\r\n        setDocumentState(prev => ({\r\n          ...prev,\r\n          [activeTab]: prev[activeTab].filter(doc => doc.id !== id)\r\n        }));\r\n      }\r\n    } catch (error) {\r\n      console.error('Error deleting document:', error);\r\n      alert('Failed to delete document. Please try again.');\r\n    }\r\n  };\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 flex items-center justify-center z-50 p-4\">\r\n      <div className=\"bg-white rounded-lg w-full max-w-2xl shadow-xl\">\r\n        <div className=\"flex justify-between items-center p-6 border-b\">\r\n          <h2 className=\"text-xl font-semibold text-gray-800\">My Documents</h2>\r\n          <button \r\n            onClick={onClose}\r\n            className=\"text-gray-500 hover:text-gray-700\"\r\n          >\r\n            <FaTimesCircle size={24} />\r\n          </button>\r\n        </div>\r\n        \r\n        {/* Tabs */}\r\n        <div className=\"flex border-b\">\r\n          <button \r\n            className={`px-6 py-3 text-sm font-medium ${activeTab === 'tenth' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'}`}\r\n            onClick={() => setActiveTab('tenth')}\r\n          >\r\n            10th Certificate\r\n          </button>\r\n          <button \r\n            className={`px-6 py-3 text-sm font-medium ${activeTab === 'twelfth' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'}`}\r\n            onClick={() => setActiveTab('twelfth')}\r\n          >\r\n            12th Certificate\r\n          </button>\r\n          <button \r\n            className={`px-6 py-3 text-sm font-medium ${activeTab === 'semesterwise' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'}`}\r\n            onClick={() => setActiveTab('semesterwise')}\r\n          >\r\n            Semester Grades\r\n          </button>\r\n        </div>\r\n        \r\n        <div className=\"p-6 max-h-96 overflow-y-auto\">\r\n          {documentState[activeTab].length === 0 ? (\r\n            <div className=\"text-center py-8 text-gray-500\">\r\n              No documents uploaded for this category yet\r\n            </div>\r\n          ) : (\r\n            <div className=\"space-y-4\">\r\n              {documentState[activeTab].map((document, index) => (\r\n                <div\r\n                  key={document.id || `${activeTab}-${index}`}\r\n                  className=\"flex items-center justify-between bg-gray-50 p-4 rounded-lg\"\r\n                >\r\n                  <div \r\n                    className=\"flex items-center flex-grow cursor-pointer hover:bg-gray-100 p-2 rounded\"\r\n                    onClick={() => handleViewDocument(document.url)}\r\n                  >\r\n                    <div className=\"bg-blue-100 p-3 rounded-full mr-4\">\r\n                      <FaFileAlt className=\"text-blue-600\" />\r\n                    </div>\r\n                    <div>\r\n                      <div className=\"flex items-center\">\r\n                        <h3 className=\"font-medium text-gray-800 mr-2\">{document.name}</h3>\r\n                        <FaExternalLinkAlt className=\"text-gray-500 text-xs\" />\r\n                      </div>\r\n                      <p className=\"text-sm text-gray-500\">Uploaded on {document.date}</p>\r\n                    </div>\r\n                  </div>\r\n                  <button\r\n                    onClick={() => handleDelete(document.id, document)}\r\n                    className=\"p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full ml-2\"\r\n                    aria-label=\"Delete document\"\r\n                  >\r\n                    <FaTrash />\r\n                  </button>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n        \r\n        <div className=\"border-t p-6\">\r\n          {/* Semester inputs for semesterwise tab */}\r\n          {activeTab === 'semesterwise' && (\r\n            <div className=\"mb-4 grid grid-cols-2 gap-4\">\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                  Semester Number\r\n                </label>\r\n                <input\r\n                  type=\"number\"\r\n                  min=\"1\"\r\n                  max=\"8\"\r\n                  ref={semesterRef}\r\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                  placeholder=\"e.g., 1\"\r\n                />\r\n              </div>\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                  CGPA\r\n                </label>\r\n                <input\r\n                  type=\"number\"\r\n                  step=\"0.01\"\r\n                  min=\"0\"\r\n                  max=\"10\"\r\n                  ref={cgpaRef}\r\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                  placeholder=\"e.g., 8.5\"\r\n                />\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          <div className=\"flex justify-between items-center\">\r\n            <p className=\"text-sm text-gray-500\">\r\n              Supported formats: PDF, JPG, PNG (max 5MB)\r\n            </p>\r\n            <div>\r\n              <input\r\n                type=\"file\"\r\n                accept=\".pdf,.jpg,.jpeg,.png\"\r\n                className=\"hidden\"\r\n                ref={fileInputRef}\r\n                onChange={handleUpload}\r\n                disabled={uploading}\r\n              />\r\n              <button \r\n                onClick={() => fileInputRef.current.click()}\r\n                className={`bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center ${uploading ? 'opacity-70 cursor-not-allowed' : ''}`}\r\n                disabled={uploading}\r\n              >\r\n                {uploading ? (\r\n                  <>\r\n                    <FaSpinner className=\"mr-2 animate-spin\" /> Uploading...\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <FaUpload className=\"mr-2\" /> Upload Document\r\n                  </>\r\n                )}\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;;;AAFA;;;AAIe,SAAS,eAAe,EACrC,MAAM,EACN,OAAO,EACP,YAAY,CAAC,CAAC,EACd,mBAAmB,EACnB,iBAAiB,EACjB,eAAe,EACf,mBAAmB,EACnB,iBAAiB,EAClB;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACjD,OAAO,EAAE;QACT,SAAS,EAAE;QACX,cAAc,EAAE;IAClB;IAEA,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEvB,mCAAmC;IACnC,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,KAAK,OAAO;QAEjB,qDAAqD;QACrD,IAAI,OAAO,CAAC,IAAI,UAAU,CAAC,SAAS;YAClC,6CAA6C;YAC7C,OAAO,CAAC,qBAAqB,EAAE,KAAK;QACtC;QACA,OAAO;IACT;IAEA,yDAAyD;IACzD,MAAM,kBAAkB,CAAC;QACvB,OAAO,YACA,OAAO,aAAa,YACpB,SAAS,IAAI,OAAO,MACpB,aAAa,UACb,aAAa;IACtB;IAEA,sDAAsD;IACtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,WAAW;gBAAE,OAAO,EAAE;gBAAE,SAAS,EAAE;gBAAE,cAAc,EAAE;YAAC;YAE5D,wEAAwE;YACxE,IAAI,gBAAgB,UAAU,KAAK,GAAG;gBACpC,MAAM,gBAAgB,OAAO,UAAU,KAAK,KAAK,WAAW,UAAU,KAAK,CAAC,KAAK,CAAC,OAAO;oBAAC;iBAAmB;gBAC7G,MAAM,WAAW,aAAa,CAAC,cAAc,MAAM,GAAG,EAAE;gBAExD,uCAAuC;gBACvC,IAAI,YAAY,aAAa,MAAM,aAAa,QAAQ;oBACtD,SAAS,KAAK,GAAG;wBAAC;4BAChB,IAAI;4BACJ,MAAM;4BACN,MAAM,IAAI,OAAO,kBAAkB,CAAC,SAAS;gCAC3C,OAAO;gCAAS,KAAK;gCAAW,MAAM;4BACxC;4BACA,KAAK,UAAU,KAAK;wBACtB;qBAAE;gBACJ;YACF;YAEA,wEAAwE;YACxE,IAAI,gBAAgB,UAAU,OAAO,GAAG;gBACtC,MAAM,gBAAgB,OAAO,UAAU,OAAO,KAAK,WAAW,UAAU,OAAO,CAAC,KAAK,CAAC,OAAO;oBAAC;iBAAmB;gBACjH,MAAM,WAAW,aAAa,CAAC,cAAc,MAAM,GAAG,EAAE;gBAExD,uCAAuC;gBACvC,IAAI,YAAY,aAAa,MAAM,aAAa,QAAQ;oBACtD,SAAS,OAAO,GAAG;wBAAC;4BAClB,IAAI;4BACJ,MAAM;4BACN,MAAM,IAAI,OAAO,kBAAkB,CAAC,SAAS;gCAC3C,OAAO;gCAAS,KAAK;gCAAW,MAAM;4BACxC;4BACA,KAAK,UAAU,OAAO;wBACxB;qBAAE;gBACJ;YACF;YAEA,2DAA2D;YAC3D,IAAI,UAAU,kBAAkB,IAAI,MAAM,OAAO,CAAC,UAAU,kBAAkB,KAAK,UAAU,kBAAkB,CAAC,MAAM,GAAG,GAAG;gBAC1H,SAAS,YAAY,GAAG,UAAU,kBAAkB,CACjD,MAAM;gDAAC,CAAA,QAAS,SAAS,gBAAgB,MAAM,aAAa;+CAC5D,GAAG;gDAAC,CAAA,QAAS,CAAC;4BACb,IAAI,MAAM,EAAE;4BACZ,MAAM,CAAC,SAAS,EAAE,MAAM,QAAQ,CAAC,kBAAkB,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC;4BAClE,MAAM,MAAM,WAAW,GAAG,IAAI,KAAK,MAAM,WAAW,EAAE,kBAAkB,CAAC,SAAS;gCAChF,OAAO;gCAAS,KAAK;gCAAW,MAAM;4BACxC,KAAK;4BACL,KAAK,MAAM,aAAa,IAAI,MAAM,cAAc;4BAChD,UAAU,MAAM,QAAQ;4BACxB,MAAM,MAAM,IAAI;wBAClB,CAAC;;YACL;YAEA,iBAAiB;QACnB;mCAAG;QAAC;KAAU;IAEd,MAAM,eAAe,OAAO;QAC1B,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;QAC9B,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,aAAa;YAEb,IAAI,cAAc,SAAS;gBACzB,MAAM,oBAAoB,MAAM;gBAChC,MAAM,SAAS;oBACb,IAAI,KAAK,GAAG;oBACZ,MAAM,KAAK,IAAI;oBACf,MAAM,IAAI,OAAO,kBAAkB,CAAC,SAAS;wBAC3C,OAAO;wBAAS,KAAK;wBAAW,MAAM;oBACxC;oBACA,KAAK,IAAI,eAAe,CAAC,MAAM,4BAA4B;gBAC7D;gBACA,iBAAiB,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,OAAO;4BAAC;yBAAO;oBAAC,CAAC;gBAEtD,+CAA+C;gBAC/C,IAAI,iBAAiB;oBACnB;gBACF;YACF,OACK,IAAI,cAAc,WAAW;gBAChC,MAAM,oBAAoB,MAAM;gBAChC,MAAM,SAAS;oBACb,IAAI,KAAK,GAAG;oBACZ,MAAM,KAAK,IAAI;oBACf,MAAM,IAAI,OAAO,kBAAkB,CAAC,SAAS;wBAC3C,OAAO;wBAAS,KAAK;wBAAW,MAAM;oBACxC;oBACA,KAAK,IAAI,eAAe,CAAC,MAAM,4BAA4B;gBAC7D;gBACA,iBAAiB,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,SAAS;4BAAC;yBAAO;oBAAC,CAAC;gBAExD,+CAA+C;gBAC/C,IAAI,iBAAiB;oBACnB;gBACF;YACF,OACK,IAAI,cAAc,gBAAgB;gBACrC,wBAAwB;gBACxB,IAAI,CAAC,YAAY,OAAO,IAAI,CAAC,QAAQ,OAAO,EAAE;oBAC5C,MAAM;oBACN,aAAa;oBACb;gBACF;gBAEA,MAAM,WAAW,YAAY,OAAO,CAAC,KAAK;gBAC1C,MAAM,OAAO,QAAQ,OAAO,CAAC,KAAK;gBAElC,IAAI,CAAC,YAAY,CAAC,MAAM;oBACtB,MAAM;oBACN,aAAa;oBACb;gBACF;gBAEA,MAAM,kBAAkB,MAAM,UAAU;gBACxC,MAAM,SAAS;oBACb,IAAI,KAAK,GAAG;oBACZ,MAAM,CAAC,SAAS,EAAE,SAAS,kBAAkB,EAAE,KAAK,CAAC,CAAC;oBACtD,MAAM,IAAI,OAAO,kBAAkB,CAAC,SAAS;wBAC3C,OAAO;wBAAS,KAAK;wBAAW,MAAM;oBACxC;oBACA,KAAK,IAAI,eAAe,CAAC;oBACzB;oBACA;gBACF;gBACA,iBAAiB,CAAA,OAAQ,CAAC;wBACxB,GAAG,IAAI;wBACP,cAAc;+BAAI,KAAK,YAAY;4BAAE;yBAAO;oBAC9C,CAAC;gBAED,+CAA+C;gBAC/C,IAAI,iBAAiB;oBACnB;gBACF;YACF;YAEA,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,aAAa;YACb,MAAM;QACR;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,CAAC,KAAK;YACR,MAAM;YACN;QACF;QAEA,MAAM,eAAe,gBAAgB;QACrC,OAAO,IAAI,CAAC,cAAc;IAC5B;IAEA,MAAM,eAAe,OAAO,IAAI,eAAe,IAAI;QACjD,IAAI;YACF,IAAI,cAAc,WAAW,cAAc,WAAW;gBACpD,qBAAqB;gBACrB,MAAM,WAAW,cAAc,UAAU,SAAS;gBAClD,MAAM,oBAAoB;gBAE1B,qBAAqB;gBACrB,iBAAiB,CAAA,OAAQ,CAAC;wBACxB,GAAG,IAAI;wBACP,CAAC,UAAU,EAAE,EAAE;oBACjB,CAAC;gBAED,+CAA+C;gBAC/C,IAAI,iBAAiB;oBACnB;gBACF;YACF,OAAO,IAAI,cAAc,kBAAkB,cAAc;gBACvD,mBAAmB;gBACnB,MAAM,kBAAkB,aAAa,QAAQ;gBAE7C,qBAAqB;gBACrB,iBAAiB,CAAA,OAAQ,CAAC;wBACxB,GAAG,IAAI;wBACP,cAAc,KAAK,YAAY,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,QAAQ,KAAK,aAAa,QAAQ;oBACtF,CAAC;gBAED,+CAA+C;gBAC/C,IAAI,iBAAiB;oBACnB;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,iBAAiB,CAAA,OAAQ,CAAC;wBACxB,GAAG,IAAI;wBACP,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;oBACxD,CAAC;YACH;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCACpD,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,iJAAA,CAAA,gBAAa;gCAAC,MAAM;;;;;;;;;;;;;;;;;8BAKzB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,WAAW,CAAC,8BAA8B,EAAE,cAAc,UAAU,6CAA6C,qCAAqC;4BACtJ,SAAS,IAAM,aAAa;sCAC7B;;;;;;sCAGD,6LAAC;4BACC,WAAW,CAAC,8BAA8B,EAAE,cAAc,YAAY,6CAA6C,qCAAqC;4BACxJ,SAAS,IAAM,aAAa;sCAC7B;;;;;;sCAGD,6LAAC;4BACC,WAAW,CAAC,8BAA8B,EAAE,cAAc,iBAAiB,6CAA6C,qCAAqC;4BAC7J,SAAS,IAAM,aAAa;sCAC7B;;;;;;;;;;;;8BAKH,6LAAC;oBAAI,WAAU;8BACZ,aAAa,CAAC,UAAU,CAAC,MAAM,KAAK,kBACnC,6LAAC;wBAAI,WAAU;kCAAiC;;;;;6CAIhD,6LAAC;wBAAI,WAAU;kCACZ,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,UAAU,sBACvC,6LAAC;gCAEC,WAAU;;kDAEV,6LAAC;wCACC,WAAU;wCACV,SAAS,IAAM,mBAAmB,SAAS,GAAG;;0DAE9C,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,iJAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;0DAEvB,6LAAC;;kEACC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAAkC,SAAS,IAAI;;;;;;0EAC7D,6LAAC,iJAAA,CAAA,oBAAiB;gEAAC,WAAU;;;;;;;;;;;;kEAE/B,6LAAC;wDAAE,WAAU;;4DAAwB;4DAAa,SAAS,IAAI;;;;;;;;;;;;;;;;;;;kDAGnE,6LAAC;wCACC,SAAS,IAAM,aAAa,SAAS,EAAE,EAAE;wCACzC,WAAU;wCACV,cAAW;kDAEX,cAAA,6LAAC,iJAAA,CAAA,UAAO;;;;;;;;;;;+BAvBL,SAAS,EAAE,IAAI,GAAG,UAAU,CAAC,EAAE,OAAO;;;;;;;;;;;;;;;8BA+BrD,6LAAC;oBAAI,WAAU;;wBAEZ,cAAc,gCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CACC,MAAK;4CACL,KAAI;4CACJ,KAAI;4CACJ,KAAK;4CACL,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAGhB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CACC,MAAK;4CACL,MAAK;4CACL,KAAI;4CACJ,KAAI;4CACJ,KAAK;4CACL,WAAU;4CACV,aAAY;;;;;;;;;;;;;;;;;;sCAMpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;8CAGrC,6LAAC;;sDACC,6LAAC;4CACC,MAAK;4CACL,QAAO;4CACP,WAAU;4CACV,KAAK;4CACL,UAAU;4CACV,UAAU;;;;;;sDAEZ,6LAAC;4CACC,SAAS,IAAM,aAAa,OAAO,CAAC,KAAK;4CACzC,WAAW,CAAC,gFAAgF,EAAE,YAAY,kCAAkC,IAAI;4CAChJ,UAAU;sDAET,0BACC;;kEACE,6LAAC,iJAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAsB;;6EAG7C;;kEACE,6LAAC,iJAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjD;GArYwB;KAAA", "debugId": null}}, {"offset": {"line": 3049, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/app/admin/student-management/ResumeModal.jsx"], "sourcesContent": ["'use client';\r\nimport { useState, useRef, useEffect } from 'react';\r\nimport { FaTrash, FaFileAlt, FaTimesCircle, FaUpload, FaExternalLinkAlt, FaSpinner, FaCheckCircle, FaExclamationCircle } from 'react-icons/fa';\r\nimport { studentsAPI } from '../../../api/students';\r\n\r\nexport default function ResumeModal({ isOpen, onClose, resume, onUpload, onDelete, studentId, isAdminMode = false }) {\r\n  const [resumes, setResumes] = useState([]);\r\n  const [uploading, setUploading] = useState(false);\r\n  const [loading, setLoading] = useState(false);\r\n  const fileInputRef = useRef(null);\r\n\r\n  // Fetch resumes from backend when modal opens\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      fetchResumes();\r\n    }\r\n  }, [isOpen, studentId]);\r\n\r\n  const fetchResumes = async () => {\r\n    try {\r\n      setLoading(true);\r\n      \r\n      // Check if user is authenticated before proceeding\r\n      const token = localStorage.getItem('access_token');\r\n      if (!token) {\r\n        console.error('No authentication token found');\r\n        setResumes([]);\r\n        setLoading(false);\r\n        return;\r\n      }\r\n      \r\n      let resumesData = [];\r\n\r\n      if (isAdminMode && studentId) {\r\n        // Admin mode: fetch resumes for specific student\r\n        try {\r\n          console.log('Admin mode: Fetching resumes for student ID:', studentId);\r\n          resumesData = await studentsAPI.adminGetResumes(studentId);\r\n          console.log('Admin resumes fetched:', resumesData);\r\n\r\n          if (!Array.isArray(resumesData)) {\r\n            console.error('Invalid admin resume data format:', resumesData);\r\n            resumesData = [];\r\n          }\r\n        } catch (adminError) {\r\n          console.error('Failed to fetch admin resumes:', adminError);\r\n          resumesData = [];\r\n        }\r\n      } else {\r\n        // Student mode: fetch resumes for current user\r\n        try {\r\n          console.log('Student mode: Fetching user-specific resumes...');\r\n          resumesData = await studentsAPI.getResumes();\r\n          console.log('Student resumes fetched:', resumesData);\r\n\r\n          // Verify we have user-specific data\r\n          if (!Array.isArray(resumesData)) {\r\n            console.error('Invalid resume data format:', resumesData);\r\n            throw new Error('Invalid resume data format');\r\n          }\r\n        } catch (apiError) {\r\n          console.log('New resumes API not available, falling back to profile data:', apiError);\r\n\r\n          // Fallback: try to get resume from profile\r\n          try {\r\n            const profile = await studentsAPI.getProfile();\r\n            console.log('User profile fetched for resume fallback:', profile?.id);\r\n\r\n            if (profile?.resume || profile?.resume_url) {\r\n              const resumeUrl = profile.resume_url || profile.resume;\r\n              const fileName = resumeUrl.split('/').pop() || 'Resume.pdf';\r\n              resumesData = [{\r\n                id: profile.id || 1,\r\n                name: fileName,\r\n                resume_url: resumeUrl,\r\n                uploaded_at: profile.updated_at || new Date().toISOString()\r\n              }];\r\n            }\r\n          } catch (profileError) {\r\n            console.error('Error fetching profile for resume:', profileError);\r\n          }\r\n        }\r\n      }\r\n      \r\n      // Transform backend data to frontend format\r\n      const transformedResumes = resumesData.map((resume, index) => ({\r\n        id: resume.id || index + 1,\r\n        name: resume.name || resume.file_name || resume.resume_url?.split('/').pop() || `Resume ${index + 1}`,\r\n        date: resume.uploaded_at ? new Date(resume.uploaded_at).toLocaleDateString('en-US', {\r\n          month: 'short',\r\n          day: 'numeric',\r\n          year: 'numeric'\r\n        }) : new Date().toLocaleDateString('en-US', {\r\n          month: 'short',\r\n          day: 'numeric',\r\n          year: 'numeric'\r\n        }),\r\n        url: resume.resume_url || resume.file_url || resume.url,\r\n        status: 'success'\r\n      }));\r\n      \r\n      console.log(`Displaying ${transformedResumes.length} resumes for current user`);\r\n      setResumes(transformedResumes);\r\n      \r\n    } catch (error) {\r\n      console.error('Error fetching resumes:', error);\r\n      // Final fallback to using the resume prop if API fails\r\n      if (resume) {\r\n        const resumeArray = [];\r\n        if (typeof resume === 'string' && resume.trim() !== '') {\r\n          const fileNameParts = resume.split('/');\r\n          const fileName = fileNameParts[fileNameParts.length - 1];\r\n          \r\n          resumeArray.push({\r\n            id: 1,\r\n            name: fileName || \"Resume\",\r\n            date: new Date().toLocaleDateString('en-US', {\r\n              month: 'short', \r\n              day: 'numeric', \r\n              year: 'numeric'\r\n            }),\r\n            url: resume,\r\n            status: 'success'\r\n          });\r\n        }\r\n        setResumes(resumeArray);\r\n      } else {\r\n        // Set empty array if no fallback data\r\n        setResumes([]);\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleUpload = async (e) => {\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      try {\r\n        setUploading(true);\r\n        \r\n        // Verify file size (5MB limit)\r\n        if (file.size > 5 * 1024 * 1024) {\r\n          alert('File size exceeds 5MB limit. Please select a smaller file.');\r\n          setUploading(false);\r\n          return;\r\n        }\r\n        \r\n        // Create a new resume object with initial \"uploading\" status\r\n        const newResume = {\r\n          id: Date.now(),\r\n          name: file.name,\r\n          date: new Date().toLocaleDateString('en-US', { \r\n            month: 'short', \r\n            day: 'numeric', \r\n            year: 'numeric' \r\n          }),\r\n          file: file,\r\n          url: URL.createObjectURL(file), // Temporary URL for preview\r\n          status: 'uploading',\r\n          progress: 0\r\n        };\r\n        \r\n        // Add the new resume to the existing list\r\n        setResumes(prevResumes => [...prevResumes, newResume]);\r\n        \r\n        // Simulate progress updates\r\n        const progressInterval = setInterval(() => {\r\n          setResumes(prevResumes => prevResumes.map(r => \r\n            r.id === newResume.id ? {...r, progress: Math.min(r.progress + 25, 99)} : r\r\n          ));\r\n        }, 500);\r\n        \r\n        try {\r\n          // Upload the file to the server\r\n          await onUpload(file);\r\n          \r\n          // Clear interval and refresh the resumes list\r\n          clearInterval(progressInterval);\r\n          \r\n          // Refresh resumes from backend\r\n          await fetchResumes();\r\n          \r\n        } catch (error) {\r\n          clearInterval(progressInterval);\r\n          setResumes(prevResumes => prevResumes.map(r => \r\n            r.id === newResume.id ? {...r, status: 'error', progress: 0} : r\r\n          ));\r\n          throw error;\r\n        }\r\n        \r\n        setUploading(false);\r\n      } catch (error) {\r\n        console.error('Error uploading resume:', error);\r\n        setUploading(false);\r\n        alert('Failed to upload resume. Please try again.');\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleViewResume = (url) => {\r\n    if (!url) {\r\n      alert('Resume URL is not available');\r\n      return;\r\n    }\r\n    \r\n    // Special handling for different URL types\r\n    if (url.startsWith('blob:')) {\r\n      // Blob URLs should be used as is\r\n      window.open(url, '_blank');\r\n    } else if (!url.startsWith('http://') && !url.startsWith('https://')) {\r\n      // Handle relative URLs by prepending the origin\r\n      const fullUrl = `${window.location.origin}${url.startsWith('/') ? '' : '/'}${url}`;\r\n      window.open(fullUrl, '_blank');\r\n    } else {\r\n      // Absolute URLs can be used directly\r\n      window.open(url, '_blank');\r\n    }\r\n  };\r\n\r\n  const handleDelete = async (id) => {\r\n    try {\r\n      const resumeToDelete = resumes.find(r => r.id === id);\r\n      \r\n      // Remove from local state immediately for better UX\r\n      setResumes(prevResumes => prevResumes.filter(resume => resume.id !== id));\r\n      \r\n      // Call backend delete if resume has a valid backend ID\r\n      if (resumeToDelete && resumeToDelete.id && typeof resumeToDelete.id === 'number') {\r\n        try {\r\n          const result = await studentsAPI.deleteResume(resumeToDelete.id);\r\n          console.log('Resume deletion response:', result);\r\n          \r\n          // Even if the server returns an error, we'll keep the UI updated\r\n          // The important thing is the user experience - they expect the file to be gone\r\n          \r\n          // Clear any local storage cache that might contain resume data\r\n          if (typeof window !== 'undefined') {\r\n            try {\r\n              // Clear all resume-related data from localStorage\r\n              const localStorageKeys = Object.keys(localStorage);\r\n              const resumeKeys = localStorageKeys.filter(key => \r\n                key.includes('resume') || key.includes('file') || key.includes('document')\r\n              );\r\n              \r\n              // Log the keys we're removing for debugging\r\n              if (resumeKeys.length > 0) {\r\n                console.log('Clearing resume-related localStorage items:', resumeKeys);\r\n                resumeKeys.forEach(key => localStorage.removeItem(key));\r\n              }\r\n              \r\n              // Also clear some specific caches that might be used\r\n              localStorage.removeItem('resume_count');\r\n              localStorage.removeItem('last_resume_update');\r\n              \r\n              // Update the user profile cache to remove the resume if applicable\r\n              const profileCache = localStorage.getItem('user_profile');\r\n              if (profileCache) {\r\n                try {\r\n                  const profile = JSON.parse(profileCache);\r\n                  if (profile && profile.resume) {\r\n                    profile.resume = null;\r\n                    localStorage.setItem('user_profile', JSON.stringify(profile));\r\n                  }\r\n                } catch (e) {\r\n                  // Ignore JSON parse errors\r\n                }\r\n              }\r\n            } catch (e) {\r\n              console.error('Error clearing localStorage:', e);\r\n            }\r\n          }\r\n        } catch (error) {\r\n          console.error('Backend delete failed, but UI is updated:', error);\r\n        }\r\n      }\r\n      \r\n      // Call the onDelete callback if provided\r\n      if (typeof onDelete === 'function') {\r\n        try {\r\n          await onDelete(resumeToDelete);\r\n        } catch (callbackError) {\r\n          console.error('onDelete callback error:', callbackError);\r\n        }\r\n      }\r\n      \r\n      // Always force a refresh of the list regardless of success/failure\r\n      // This ensures we're showing the correct state\r\n      await fetchResumes();\r\n      \r\n    } catch (error) {\r\n      console.error('Error in delete process:', error);\r\n      // Refresh the list to ensure UI is in sync with backend\r\n      await fetchResumes();\r\n    }\r\n  };\r\n\r\n  // Helper to render resume status icon\r\n  const renderStatusIcon = (resume) => {\r\n    if (resume.status === 'uploading') {\r\n      return (\r\n        <div className=\"ml-2 text-blue-500\">\r\n          <FaSpinner className=\"animate-spin\" />\r\n        </div>\r\n      );\r\n    } else if (resume.status === 'success') {\r\n      return (\r\n        <div className=\"ml-2 text-green-500\">\r\n          <FaCheckCircle />\r\n        </div>\r\n      );\r\n    } else if (resume.status === 'error') {\r\n      return (\r\n        <div className=\"ml-2 text-red-500\">\r\n          <FaExclamationCircle />\r\n        </div>\r\n      );\r\n    }\r\n    return null;\r\n  };\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 flex items-center justify-center z-50 p-4\">\r\n      <div className=\"bg-white rounded-lg w-full max-w-2xl shadow-xl\">\r\n        <div className=\"flex justify-between items-center p-6 border-b\">\r\n          <div>\r\n            <h2 className=\"text-xl font-semibold text-gray-800\">My Resumes</h2>\r\n            <p className=\"text-sm text-gray-500\">Upload multiple resumes for different job types</p>\r\n          </div>\r\n          <button \r\n            onClick={onClose}\r\n            className=\"text-gray-500 hover:text-gray-700\"\r\n          >\r\n            <FaTimesCircle size={24} />\r\n          </button>\r\n        </div>\r\n        \r\n        <div className=\"p-6 max-h-96 overflow-y-auto\">\r\n          {loading ? (\r\n            <div className=\"flex items-center justify-center py-8\">\r\n              <FaSpinner className=\"animate-spin text-blue-500 text-2xl mr-3\" />\r\n              <span className=\"text-gray-600\">Loading resumes...</span>\r\n            </div>\r\n          ) : resumes.length === 0 ? (\r\n            <div className=\"text-center py-8 text-gray-500\">\r\n              <p>No resumes uploaded yet</p>\r\n              <p className=\"text-sm mt-2\">You can upload multiple resumes for different job applications</p>\r\n            </div>\r\n          ) : (\r\n            <div>\r\n              <div className=\"flex justify-between items-center mb-4\">\r\n                <h3 className=\"text-md font-medium text-gray-700\">Your Resumes ({resumes.length})</h3>\r\n                <div className=\"text-sm text-gray-500\">\r\n                  {resumes.length > 1 ? \"You can use different resumes for different applications\" : \"\"}\r\n                </div>\r\n              </div>\r\n              <div className=\"space-y-4\">\r\n                {resumes.map((resume) => (\r\n                  <div \r\n                    key={resume.id} \r\n                    className=\"flex items-center justify-between bg-gray-50 p-4 rounded-lg\"\r\n                  >\r\n                    <div \r\n                      className=\"flex items-center flex-grow cursor-pointer hover:bg-gray-100 p-2 rounded\"\r\n                      onClick={() => resume.status !== 'uploading' ? handleViewResume(resume.url) : null}\r\n                    >\r\n                      <div className=\"bg-blue-100 p-3 rounded-full mr-4\">\r\n                        <FaFileAlt className=\"text-blue-600\" />\r\n                      </div>\r\n                      <div className=\"flex-grow\">\r\n                        <div className=\"flex items-center\">\r\n                          <h3 className=\"font-medium text-gray-800 mr-2\">{resume.name}</h3>\r\n                          {renderStatusIcon(resume)}\r\n                          {resume.status !== 'uploading' && <FaExternalLinkAlt className=\"text-gray-500 text-xs ml-2\" />}\r\n                        </div>\r\n                        <p className=\"text-sm text-gray-500\">Uploaded on {resume.date}</p>\r\n                        \r\n                        {/* Progress bar for uploading resumes */}\r\n                        {resume.status === 'uploading' && (\r\n                          <div className=\"w-full bg-gray-200 rounded-full h-2.5 mt-2\">\r\n                            <div \r\n                              className=\"bg-blue-600 h-2.5 rounded-full transition-all duration-300\"\r\n                              style={{ width: `${resume.progress}%` }}\r\n                            ></div>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                    <button \r\n                      onClick={() => handleDelete(resume.id)}\r\n                      className=\"p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full ml-2\"\r\n                      aria-label=\"Delete resume\"\r\n                      disabled={resume.status === 'uploading'}\r\n                    >\r\n                      <FaTrash className={resume.status === 'uploading' ? 'opacity-50' : ''} />\r\n                    </button>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n        \r\n        <div className=\"border-t p-6 flex justify-between items-center\">\r\n          <div>\r\n            <p className=\"text-sm text-gray-500\">\r\n              Supported formats: PDF, DOCX (max 5MB)\r\n            </p>\r\n            <p className=\"text-xs text-gray-400 mt-1\">\r\n              You can upload multiple resumes tailored to different positions\r\n            </p>\r\n          </div>\r\n          <div>\r\n            <input \r\n              type=\"file\" \r\n              accept=\".pdf,.docx\" \r\n              className=\"hidden\" \r\n              ref={fileInputRef}\r\n              onChange={handleUpload}\r\n              disabled={uploading}\r\n            />\r\n            <button \r\n              onClick={() => fileInputRef.current.click()}\r\n              className={`bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center ${uploading ? 'opacity-70 cursor-not-allowed' : ''}`}\r\n              disabled={uploading}\r\n            >\r\n              {uploading ? (\r\n                <>\r\n                  <FaSpinner className=\"mr-2 animate-spin\" /> Uploading...\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <FaUpload className=\"mr-2\" /> Add Resume\r\n                </>\r\n              )}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n          //     )}\r\n          //   </button>\r\n          // </div>\r\n\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;AAKe,SAAS,YAAY,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,cAAc,KAAK,EAAE;;IACjH,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE5B,8CAA8C;IAC9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,QAAQ;gBACV;YACF;QACF;gCAAG;QAAC;QAAQ;KAAU;IAEtB,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YAEX,mDAAmD;YACnD,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,CAAC,OAAO;gBACV,QAAQ,KAAK,CAAC;gBACd,WAAW,EAAE;gBACb,WAAW;gBACX;YACF;YAEA,IAAI,cAAc,EAAE;YAEpB,IAAI,eAAe,WAAW;gBAC5B,iDAAiD;gBACjD,IAAI;oBACF,QAAQ,GAAG,CAAC,gDAAgD;oBAC5D,cAAc,MAAM,yHAAA,CAAA,cAAW,CAAC,eAAe,CAAC;oBAChD,QAAQ,GAAG,CAAC,0BAA0B;oBAEtC,IAAI,CAAC,MAAM,OAAO,CAAC,cAAc;wBAC/B,QAAQ,KAAK,CAAC,qCAAqC;wBACnD,cAAc,EAAE;oBAClB;gBACF,EAAE,OAAO,YAAY;oBACnB,QAAQ,KAAK,CAAC,kCAAkC;oBAChD,cAAc,EAAE;gBAClB;YACF,OAAO;gBACL,+CAA+C;gBAC/C,IAAI;oBACF,QAAQ,GAAG,CAAC;oBACZ,cAAc,MAAM,yHAAA,CAAA,cAAW,CAAC,UAAU;oBAC1C,QAAQ,GAAG,CAAC,4BAA4B;oBAExC,oCAAoC;oBACpC,IAAI,CAAC,MAAM,OAAO,CAAC,cAAc;wBAC/B,QAAQ,KAAK,CAAC,+BAA+B;wBAC7C,MAAM,IAAI,MAAM;oBAClB;gBACF,EAAE,OAAO,UAAU;oBACjB,QAAQ,GAAG,CAAC,gEAAgE;oBAE5E,2CAA2C;oBAC3C,IAAI;wBACF,MAAM,UAAU,MAAM,yHAAA,CAAA,cAAW,CAAC,UAAU;wBAC5C,QAAQ,GAAG,CAAC,6CAA6C,SAAS;wBAElE,IAAI,SAAS,UAAU,SAAS,YAAY;4BAC1C,MAAM,YAAY,QAAQ,UAAU,IAAI,QAAQ,MAAM;4BACtD,MAAM,WAAW,UAAU,KAAK,CAAC,KAAK,GAAG,MAAM;4BAC/C,cAAc;gCAAC;oCACb,IAAI,QAAQ,EAAE,IAAI;oCAClB,MAAM;oCACN,YAAY;oCACZ,aAAa,QAAQ,UAAU,IAAI,IAAI,OAAO,WAAW;gCAC3D;6BAAE;wBACJ;oBACF,EAAE,OAAO,cAAc;wBACrB,QAAQ,KAAK,CAAC,sCAAsC;oBACtD;gBACF;YACF;YAEA,4CAA4C;YAC5C,MAAM,qBAAqB,YAAY,GAAG,CAAC,CAAC,QAAQ,QAAU,CAAC;oBAC7D,IAAI,OAAO,EAAE,IAAI,QAAQ;oBACzB,MAAM,OAAO,IAAI,IAAI,OAAO,SAAS,IAAI,OAAO,UAAU,EAAE,MAAM,KAAK,SAAS,CAAC,OAAO,EAAE,QAAQ,GAAG;oBACrG,MAAM,OAAO,WAAW,GAAG,IAAI,KAAK,OAAO,WAAW,EAAE,kBAAkB,CAAC,SAAS;wBAClF,OAAO;wBACP,KAAK;wBACL,MAAM;oBACR,KAAK,IAAI,OAAO,kBAAkB,CAAC,SAAS;wBAC1C,OAAO;wBACP,KAAK;wBACL,MAAM;oBACR;oBACA,KAAK,OAAO,UAAU,IAAI,OAAO,QAAQ,IAAI,OAAO,GAAG;oBACvD,QAAQ;gBACV,CAAC;YAED,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,mBAAmB,MAAM,CAAC,yBAAyB,CAAC;YAC9E,WAAW;QAEb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,uDAAuD;YACvD,IAAI,QAAQ;gBACV,MAAM,cAAc,EAAE;gBACtB,IAAI,OAAO,WAAW,YAAY,OAAO,IAAI,OAAO,IAAI;oBACtD,MAAM,gBAAgB,OAAO,KAAK,CAAC;oBACnC,MAAM,WAAW,aAAa,CAAC,cAAc,MAAM,GAAG,EAAE;oBAExD,YAAY,IAAI,CAAC;wBACf,IAAI;wBACJ,MAAM,YAAY;wBAClB,MAAM,IAAI,OAAO,kBAAkB,CAAC,SAAS;4BAC3C,OAAO;4BACP,KAAK;4BACL,MAAM;wBACR;wBACA,KAAK;wBACL,QAAQ;oBACV;gBACF;gBACA,WAAW;YACb,OAAO;gBACL,sCAAsC;gBACtC,WAAW,EAAE;YACf;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;QAC9B,IAAI,MAAM;YACR,IAAI;gBACF,aAAa;gBAEb,+BAA+B;gBAC/B,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;oBAC/B,MAAM;oBACN,aAAa;oBACb;gBACF;gBAEA,6DAA6D;gBAC7D,MAAM,YAAY;oBAChB,IAAI,KAAK,GAAG;oBACZ,MAAM,KAAK,IAAI;oBACf,MAAM,IAAI,OAAO,kBAAkB,CAAC,SAAS;wBAC3C,OAAO;wBACP,KAAK;wBACL,MAAM;oBACR;oBACA,MAAM;oBACN,KAAK,IAAI,eAAe,CAAC;oBACzB,QAAQ;oBACR,UAAU;gBACZ;gBAEA,0CAA0C;gBAC1C,WAAW,CAAA,cAAe;2BAAI;wBAAa;qBAAU;gBAErD,4BAA4B;gBAC5B,MAAM,mBAAmB,YAAY;oBACnC,WAAW,CAAA,cAAe,YAAY,GAAG,CAAC,CAAA,IACxC,EAAE,EAAE,KAAK,UAAU,EAAE,GAAG;gCAAC,GAAG,CAAC;gCAAE,UAAU,KAAK,GAAG,CAAC,EAAE,QAAQ,GAAG,IAAI;4BAAG,IAAI;gBAE9E,GAAG;gBAEH,IAAI;oBACF,gCAAgC;oBAChC,MAAM,SAAS;oBAEf,8CAA8C;oBAC9C,cAAc;oBAEd,+BAA+B;oBAC/B,MAAM;gBAER,EAAE,OAAO,OAAO;oBACd,cAAc;oBACd,WAAW,CAAA,cAAe,YAAY,GAAG,CAAC,CAAA,IACxC,EAAE,EAAE,KAAK,UAAU,EAAE,GAAG;gCAAC,GAAG,CAAC;gCAAE,QAAQ;gCAAS,UAAU;4BAAC,IAAI;oBAEjE,MAAM;gBACR;gBAEA,aAAa;YACf,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,aAAa;gBACb,MAAM;YACR;QACF;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,KAAK;YACR,MAAM;YACN;QACF;QAEA,2CAA2C;QAC3C,IAAI,IAAI,UAAU,CAAC,UAAU;YAC3B,iCAAiC;YACjC,OAAO,IAAI,CAAC,KAAK;QACnB,OAAO,IAAI,CAAC,IAAI,UAAU,CAAC,cAAc,CAAC,IAAI,UAAU,CAAC,aAAa;YACpE,gDAAgD;YAChD,MAAM,UAAU,GAAG,OAAO,QAAQ,CAAC,MAAM,GAAG,IAAI,UAAU,CAAC,OAAO,KAAK,MAAM,KAAK;YAClF,OAAO,IAAI,CAAC,SAAS;QACvB,OAAO;YACL,qCAAqC;YACrC,OAAO,IAAI,CAAC,KAAK;QACnB;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,iBAAiB,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAElD,oDAAoD;YACpD,WAAW,CAAA,cAAe,YAAY,MAAM,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;YAErE,uDAAuD;YACvD,IAAI,kBAAkB,eAAe,EAAE,IAAI,OAAO,eAAe,EAAE,KAAK,UAAU;gBAChF,IAAI;oBACF,MAAM,SAAS,MAAM,yHAAA,CAAA,cAAW,CAAC,YAAY,CAAC,eAAe,EAAE;oBAC/D,QAAQ,GAAG,CAAC,6BAA6B;oBAEzC,iEAAiE;oBACjE,+EAA+E;oBAE/E,+DAA+D;oBAC/D,wCAAmC;wBACjC,IAAI;4BACF,kDAAkD;4BAClD,MAAM,mBAAmB,OAAO,IAAI,CAAC;4BACrC,MAAM,aAAa,iBAAiB,MAAM,CAAC,CAAA,MACzC,IAAI,QAAQ,CAAC,aAAa,IAAI,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC;4BAGjE,4CAA4C;4BAC5C,IAAI,WAAW,MAAM,GAAG,GAAG;gCACzB,QAAQ,GAAG,CAAC,+CAA+C;gCAC3D,WAAW,OAAO,CAAC,CAAA,MAAO,aAAa,UAAU,CAAC;4BACpD;4BAEA,qDAAqD;4BACrD,aAAa,UAAU,CAAC;4BACxB,aAAa,UAAU,CAAC;4BAExB,mEAAmE;4BACnE,MAAM,eAAe,aAAa,OAAO,CAAC;4BAC1C,IAAI,cAAc;gCAChB,IAAI;oCACF,MAAM,UAAU,KAAK,KAAK,CAAC;oCAC3B,IAAI,WAAW,QAAQ,MAAM,EAAE;wCAC7B,QAAQ,MAAM,GAAG;wCACjB,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;oCACtD;gCACF,EAAE,OAAO,GAAG;gCACV,2BAA2B;gCAC7B;4BACF;wBACF,EAAE,OAAO,GAAG;4BACV,QAAQ,KAAK,CAAC,gCAAgC;wBAChD;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,6CAA6C;gBAC7D;YACF;YAEA,yCAAyC;YACzC,IAAI,OAAO,aAAa,YAAY;gBAClC,IAAI;oBACF,MAAM,SAAS;gBACjB,EAAE,OAAO,eAAe;oBACtB,QAAQ,KAAK,CAAC,4BAA4B;gBAC5C;YACF;YAEA,mEAAmE;YACnE,+CAA+C;YAC/C,MAAM;QAER,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,wDAAwD;YACxD,MAAM;QACR;IACF;IAEA,sCAAsC;IACtC,MAAM,mBAAmB,CAAC;QACxB,IAAI,OAAO,MAAM,KAAK,aAAa;YACjC,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,iJAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;;;;;;QAG3B,OAAO,IAAI,OAAO,MAAM,KAAK,WAAW;YACtC,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,iJAAA,CAAA,gBAAa;;;;;;;;;;QAGpB,OAAO,IAAI,OAAO,MAAM,KAAK,SAAS;YACpC,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,iJAAA,CAAA,sBAAmB;;;;;;;;;;QAG1B;QACA,OAAO;IACT;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAEvC,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,iJAAA,CAAA,gBAAa;gCAAC,MAAM;;;;;;;;;;;;;;;;;8BAIzB,6LAAC;oBAAI,WAAU;8BACZ,wBACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,iJAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,6LAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;+BAEhC,QAAQ,MAAM,KAAK,kBACrB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAE;;;;;;0CACH,6LAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;6CAG9B,6LAAC;;0CACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;;4CAAoC;4CAAe,QAAQ,MAAM;4CAAC;;;;;;;kDAChF,6LAAC;wCAAI,WAAU;kDACZ,QAAQ,MAAM,GAAG,IAAI,6DAA6D;;;;;;;;;;;;0CAGvF,6LAAC;gCAAI,WAAU;0CACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;wCAEC,WAAU;;0DAEV,6LAAC;gDACC,WAAU;gDACV,SAAS,IAAM,OAAO,MAAM,KAAK,cAAc,iBAAiB,OAAO,GAAG,IAAI;;kEAE9E,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,iJAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;kEAEvB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFAAkC,OAAO,IAAI;;;;;;oEAC1D,iBAAiB;oEACjB,OAAO,MAAM,KAAK,6BAAe,6LAAC,iJAAA,CAAA,oBAAiB;wEAAC,WAAU;;;;;;;;;;;;0EAEjE,6LAAC;gEAAE,WAAU;;oEAAwB;oEAAa,OAAO,IAAI;;;;;;;4DAG5D,OAAO,MAAM,KAAK,6BACjB,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEACC,WAAU;oEACV,OAAO;wEAAE,OAAO,GAAG,OAAO,QAAQ,CAAC,CAAC,CAAC;oEAAC;;;;;;;;;;;;;;;;;;;;;;;0DAMhD,6LAAC;gDACC,SAAS,IAAM,aAAa,OAAO,EAAE;gDACrC,WAAU;gDACV,cAAW;gDACX,UAAU,OAAO,MAAM,KAAK;0DAE5B,cAAA,6LAAC,iJAAA,CAAA,UAAO;oDAAC,WAAW,OAAO,MAAM,KAAK,cAAc,eAAe;;;;;;;;;;;;uCAnChE,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;8BA4C1B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;8CAGrC,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAI5C,6LAAC;;8CACC,6LAAC;oCACC,MAAK;oCACL,QAAO;oCACP,WAAU;oCACV,KAAK;oCACL,UAAU;oCACV,UAAU;;;;;;8CAEZ,6LAAC;oCACC,SAAS,IAAM,aAAa,OAAO,CAAC,KAAK;oCACzC,WAAW,CAAC,gFAAgF,EAAE,YAAY,kCAAkC,IAAI;oCAChJ,UAAU;8CAET,0BACC;;0DACE,6LAAC,iJAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAsB;;qEAG7C;;0DACE,6LAAC,iJAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/C,EACU,SAAS;CACT,cAAc;CACd,SAAS;GAzbK;KAAA", "debugId": null}}, {"offset": {"line": 3750, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/app/admin/student-management/FreezeModal.jsx"], "sourcesContent": ["'use client';\r\nimport { useState } from 'react';\r\nimport { X, AlertTriangle, Lock, Unlock } from 'lucide-react';\r\n\r\nexport default function FreezeModal({ \r\n  isOpen, \r\n  onClose, \r\n  onFreeze, \r\n  onUnfreeze, \r\n  studentName, \r\n  currentFreezeStatus = 'none',\r\n  currentFreezeData = null \r\n}) {\r\n  const [freezeType, setFreezeType] = useState('complete');\r\n  const [reason, setReason] = useState('');\r\n  const [loading, setLoading] = useState(false);\r\n  \r\n  // Partial freeze options\r\n  const [minSalary, setMinSalary] = useState('');\r\n  const [allowedJobTiers, setAllowedJobTiers] = useState([]);\r\n  const [allowedJobTypes, setAllowedJobTypes] = useState([]);\r\n  const [allowedCompanies, setAllowedCompanies] = useState([]);\r\n\r\n  const jobTierOptions = [\r\n    { value: 'tier1', label: 'Tier 1 (Top Companies)' },\r\n    { value: 'tier2', label: 'Tier 2 (Mid-level Companies)' },\r\n    { value: 'tier3', label: 'Tier 3 (Startups/Small Companies)' }\r\n  ];\r\n\r\n  const jobTypeOptions = [\r\n    { value: 'fulltime', label: 'Full Time' },\r\n    { value: 'internship', label: 'Internship' },\r\n    { value: 'contract', label: 'Contract' },\r\n    { value: 'parttime', label: 'Part Time' }\r\n  ];\r\n\r\n  const handleFreeze = async () => {\r\n    if (!reason.trim()) {\r\n      alert('Please provide a reason for freezing the account.');\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    try {\r\n      const freezeData = {\r\n        freeze_type: freezeType,\r\n        reason: reason.trim()\r\n      };\r\n\r\n      if (freezeType === 'partial') {\r\n        freezeData.min_salary_requirement = minSalary ? parseFloat(minSalary) : null;\r\n        freezeData.allowed_job_tiers = allowedJobTiers;\r\n        freezeData.allowed_job_types = allowedJobTypes;\r\n        freezeData.allowed_companies = allowedCompanies;\r\n      }\r\n\r\n      await onFreeze(freezeData);\r\n      onClose();\r\n    } catch (error) {\r\n      console.error('Error freezing account:', error);\r\n      alert('Failed to freeze account. Please try again.');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleUnfreeze = async () => {\r\n    setLoading(true);\r\n    try {\r\n      await onUnfreeze();\r\n      onClose();\r\n    } catch (error) {\r\n      console.error('Error unfreezing account:', error);\r\n      alert('Failed to unfreeze account. Please try again.');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleJobTierChange = (tier) => {\r\n    setAllowedJobTiers(prev => \r\n      prev.includes(tier) \r\n        ? prev.filter(t => t !== tier)\r\n        : [...prev, tier]\r\n    );\r\n  };\r\n\r\n  const handleJobTypeChange = (type) => {\r\n    setAllowedJobTypes(prev => \r\n      prev.includes(type) \r\n        ? prev.filter(t => t !== type)\r\n        : [...prev, type]\r\n    );\r\n  };\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n      <div className=\"bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto\">\r\n        <div className=\"flex justify-between items-center mb-6\">\r\n          <h2 className=\"text-2xl font-bold text-gray-800\">\r\n            {currentFreezeStatus === 'none' ? 'Freeze Account' : 'Manage Account Freeze'}\r\n          </h2>\r\n          <button\r\n            onClick={onClose}\r\n            className=\"text-gray-500 hover:text-gray-700\"\r\n          >\r\n            <X size={24} />\r\n          </button>\r\n        </div>\r\n\r\n        {currentFreezeStatus === 'none' ? (\r\n          // Freeze Account Form\r\n          <div className=\"space-y-6\">\r\n            <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\r\n              <div className=\"flex items-center gap-2 mb-2\">\r\n                <AlertTriangle className=\"text-blue-600\" size={20} />\r\n                <span className=\"font-semibold text-blue-800\">Freeze Account: {studentName}</span>\r\n              </div>\r\n              <p className=\"text-blue-700 text-sm\">\r\n                Choose how you want to restrict this student's account access.\r\n              </p>\r\n            </div>\r\n\r\n            {/* Freeze Type Selection */}\r\n            <div>\r\n              <label className=\"block text-sm font-medium text-gray-700 mb-3\">\r\n                Freeze Type *\r\n              </label>\r\n              <div className=\"space-y-3\">\r\n                <label className=\"flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50\">\r\n                  <input\r\n                    type=\"radio\"\r\n                    name=\"freezeType\"\r\n                    value=\"complete\"\r\n                    checked={freezeType === 'complete'}\r\n                    onChange={(e) => setFreezeType(e.target.value)}\r\n                    className=\"mr-3\"\r\n                  />\r\n                  <div>\r\n                    <div className=\"font-medium text-gray-800\">Complete Freeze</div>\r\n                    <div className=\"text-sm text-gray-600\">\r\n                      Student cannot login to the system at all\r\n                    </div>\r\n                  </div>\r\n                </label>\r\n                \r\n                <label className=\"flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50\">\r\n                  <input\r\n                    type=\"radio\"\r\n                    name=\"freezeType\"\r\n                    value=\"partial\"\r\n                    checked={freezeType === 'partial'}\r\n                    onChange={(e) => setFreezeType(e.target.value)}\r\n                    className=\"mr-3\"\r\n                  />\r\n                  <div>\r\n                    <div className=\"font-medium text-gray-800\">Partial Freeze</div>\r\n                    <div className=\"text-sm text-gray-600\">\r\n                      Student can login but with restricted job application access\r\n                    </div>\r\n                  </div>\r\n                </label>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Reason */}\r\n            <div>\r\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                Reason for Freeze *\r\n              </label>\r\n              <textarea\r\n                value={reason}\r\n                onChange={(e) => setReason(e.target.value)}\r\n                className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                rows={3}\r\n                placeholder=\"Enter the reason for freezing this account...\"\r\n                required\r\n              />\r\n            </div>\r\n\r\n            {/* Partial Freeze Options */}\r\n            {freezeType === 'partial' && (\r\n              <div className=\"space-y-6 border-t pt-6\">\r\n                <h3 className=\"text-lg font-semibold text-gray-800\">Partial Freeze Restrictions</h3>\r\n                \r\n                {/* Minimum Salary Requirement */}\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Minimum Salary Requirement (in Lakhs)\r\n                  </label>\r\n                  <input\r\n                    type=\"number\"\r\n                    value={minSalary}\r\n                    onChange={(e) => setMinSalary(e.target.value)}\r\n                    className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                    placeholder=\"e.g., 10 (for 10 LPA minimum)\"\r\n                    min=\"0\"\r\n                    step=\"0.1\"\r\n                  />\r\n                </div>\r\n\r\n                {/* Allowed Job Tiers */}\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Allowed Job Tiers\r\n                  </label>\r\n                  <div className=\"space-y-2\">\r\n                    {jobTierOptions.map(option => (\r\n                      <label key={option.value} className=\"flex items-center\">\r\n                        <input\r\n                          type=\"checkbox\"\r\n                          checked={allowedJobTiers.includes(option.value)}\r\n                          onChange={() => handleJobTierChange(option.value)}\r\n                          className=\"mr-2\"\r\n                        />\r\n                        {option.label}\r\n                      </label>\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Allowed Job Types */}\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Allowed Job Types\r\n                  </label>\r\n                  <div className=\"space-y-2\">\r\n                    {jobTypeOptions.map(option => (\r\n                      <label key={option.value} className=\"flex items-center\">\r\n                        <input\r\n                          type=\"checkbox\"\r\n                          checked={allowedJobTypes.includes(option.value)}\r\n                          onChange={() => handleJobTypeChange(option.value)}\r\n                          className=\"mr-2\"\r\n                        />\r\n                        {option.label}\r\n                      </label>\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Action Buttons */}\r\n            <div className=\"flex justify-end gap-3 pt-6 border-t\">\r\n              <button\r\n                onClick={onClose}\r\n                className=\"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors\"\r\n                disabled={loading}\r\n              >\r\n                Cancel\r\n              </button>\r\n              <button\r\n                onClick={handleFreeze}\r\n                disabled={loading}\r\n                className=\"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50\"\r\n              >\r\n                {loading ? 'Freezing...' : 'Freeze Account'}\r\n              </button>\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          // Current Freeze Status Display\r\n          <div className=\"space-y-6\">\r\n            <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\r\n              <div className=\"flex items-center gap-2 mb-2\">\r\n                <Lock className=\"text-yellow-600\" size={20} />\r\n                <span className=\"font-semibold text-yellow-800\">\r\n                  Account Currently Frozen: {currentFreezeStatus === 'complete' ? 'Complete Freeze' : 'Partial Freeze'}\r\n                </span>\r\n              </div>\r\n              <p className=\"text-yellow-700 text-sm\">\r\n                {currentFreezeData?.freeze_reason || 'No reason provided'}\r\n              </p>\r\n              {currentFreezeData?.freeze_date && (\r\n                <p className=\"text-yellow-700 text-sm mt-1\">\r\n                  Frozen on: {new Date(currentFreezeData.freeze_date).toLocaleDateString()}\r\n                </p>\r\n              )}\r\n            </div>\r\n\r\n            {currentFreezeStatus === 'partial' && currentFreezeData?.freeze_restrictions && (\r\n              <div className=\"bg-gray-50 rounded-lg p-4\">\r\n                <h3 className=\"font-semibold text-gray-800 mb-3\">Current Restrictions:</h3>\r\n                <div className=\"space-y-2 text-sm\">\r\n                  {currentFreezeData.freeze_restrictions.min_salary_requirement && (\r\n                    <p><strong>Min Salary:</strong> {currentFreezeData.freeze_restrictions.min_salary_requirement} LPA</p>\r\n                  )}\r\n                  {currentFreezeData.freeze_restrictions.allowed_job_tiers?.length > 0 && (\r\n                    <p><strong>Allowed Tiers:</strong> {currentFreezeData.freeze_restrictions.allowed_job_tiers.join(', ')}</p>\r\n                  )}\r\n                  {currentFreezeData.freeze_restrictions.allowed_job_types?.length > 0 && (\r\n                    <p><strong>Allowed Types:</strong> {currentFreezeData.freeze_restrictions.allowed_job_types.join(', ')}</p>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Unfreeze Button */}\r\n            <div className=\"flex justify-end gap-3 pt-6 border-t\">\r\n              <button\r\n                onClick={onClose}\r\n                className=\"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors\"\r\n                disabled={loading}\r\n              >\r\n                Close\r\n              </button>\r\n              <button\r\n                onClick={handleUnfreeze}\r\n                disabled={loading}\r\n                className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 flex items-center gap-2\"\r\n              >\r\n                <Unlock size={16} />\r\n                {loading ? 'Unfreezing...' : 'Unfreeze Account'}\r\n              </button>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;;;AAFA;;;AAIe,SAAS,YAAY,EAClC,MAAM,EACN,OAAO,EACP,QAAQ,EACR,UAAU,EACV,WAAW,EACX,sBAAsB,MAAM,EAC5B,oBAAoB,IAAI,EACzB;;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,yBAAyB;IACzB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACzD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAE3D,MAAM,iBAAiB;QACrB;YAAE,OAAO;YAAS,OAAO;QAAyB;QAClD;YAAE,OAAO;YAAS,OAAO;QAA+B;QACxD;YAAE,OAAO;YAAS,OAAO;QAAoC;KAC9D;IAED,MAAM,iBAAiB;QACrB;YAAE,OAAO;YAAY,OAAO;QAAY;QACxC;YAAE,OAAO;YAAc,OAAO;QAAa;QAC3C;YAAE,OAAO;YAAY,OAAO;QAAW;QACvC;YAAE,OAAO;YAAY,OAAO;QAAY;KACzC;IAED,MAAM,eAAe;QACnB,IAAI,CAAC,OAAO,IAAI,IAAI;YAClB,MAAM;YACN;QACF;QAEA,WAAW;QACX,IAAI;YACF,MAAM,aAAa;gBACjB,aAAa;gBACb,QAAQ,OAAO,IAAI;YACrB;YAEA,IAAI,eAAe,WAAW;gBAC5B,WAAW,sBAAsB,GAAG,YAAY,WAAW,aAAa;gBACxE,WAAW,iBAAiB,GAAG;gBAC/B,WAAW,iBAAiB,GAAG;gBAC/B,WAAW,iBAAiB,GAAG;YACjC;YAEA,MAAM,SAAS;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB;QACrB,WAAW;QACX,IAAI;YACF,MAAM;YACN;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,mBAAmB,CAAA,OACjB,KAAK,QAAQ,CAAC,QACV,KAAK,MAAM,CAAC,CAAA,IAAK,MAAM,QACvB;mBAAI;gBAAM;aAAK;IAEvB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,mBAAmB,CAAA,OACjB,KAAK,QAAQ,CAAC,QACV,KAAK,MAAM,CAAC,CAAA,IAAK,MAAM,QACvB;mBAAI;gBAAM;aAAK;IAEvB;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX,wBAAwB,SAAS,mBAAmB;;;;;;sCAEvD,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,MAAM;;;;;;;;;;;;;;;;;gBAIZ,wBAAwB,SACvB,sBAAsB;8BACtB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;4CAAgB,MAAM;;;;;;sDAC/C,6LAAC;4CAAK,WAAU;;gDAA8B;gDAAiB;;;;;;;;;;;;;8CAEjE,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAMvC,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;;8DACf,6LAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAM;oDACN,SAAS,eAAe;oDACxB,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,WAAU;;;;;;8DAEZ,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAA4B;;;;;;sEAC3C,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAM3C,6LAAC;4CAAM,WAAU;;8DACf,6LAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAM;oDACN,SAAS,eAAe;oDACxB,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,WAAU;;;;;;8DAEZ,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAA4B;;;;;;sEAC3C,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAS/C,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;oCACzC,WAAU;oCACV,MAAM;oCACN,aAAY;oCACZ,QAAQ;;;;;;;;;;;;wBAKX,eAAe,2BACd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAGpD,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CACC,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;4CAC5C,WAAU;4CACV,aAAY;4CACZ,KAAI;4CACJ,MAAK;;;;;;;;;;;;8CAKT,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CAAI,WAAU;sDACZ,eAAe,GAAG,CAAC,CAAA,uBAClB,6LAAC;oDAAyB,WAAU;;sEAClC,6LAAC;4DACC,MAAK;4DACL,SAAS,gBAAgB,QAAQ,CAAC,OAAO,KAAK;4DAC9C,UAAU,IAAM,oBAAoB,OAAO,KAAK;4DAChD,WAAU;;;;;;wDAEX,OAAO,KAAK;;mDAPH,OAAO,KAAK;;;;;;;;;;;;;;;;8CAc9B,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CAAI,WAAU;sDACZ,eAAe,GAAG,CAAC,CAAA,uBAClB,6LAAC;oDAAyB,WAAU;;sEAClC,6LAAC;4DACC,MAAK;4DACL,SAAS,gBAAgB,QAAQ,CAAC,OAAO,KAAK;4DAC9C,UAAU,IAAM,oBAAoB,OAAO,KAAK;4DAChD,WAAU;;;;;;wDAEX,OAAO,KAAK;;mDAPH,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;sCAgBlC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,WAAU;oCACV,UAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,UAAU,gBAAgB;;;;;;;;;;;;;;;;;2BAKjC,gCAAgC;8BAChC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;4CAAkB,MAAM;;;;;;sDACxC,6LAAC;4CAAK,WAAU;;gDAAgC;gDACnB,wBAAwB,aAAa,oBAAoB;;;;;;;;;;;;;8CAGxF,6LAAC;oCAAE,WAAU;8CACV,mBAAmB,iBAAiB;;;;;;gCAEtC,mBAAmB,6BAClB,6LAAC;oCAAE,WAAU;;wCAA+B;wCAC9B,IAAI,KAAK,kBAAkB,WAAW,EAAE,kBAAkB;;;;;;;;;;;;;wBAK3E,wBAAwB,aAAa,mBAAmB,qCACvD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,6LAAC;oCAAI,WAAU;;wCACZ,kBAAkB,mBAAmB,CAAC,sBAAsB,kBAC3D,6LAAC;;8DAAE,6LAAC;8DAAO;;;;;;gDAAoB;gDAAE,kBAAkB,mBAAmB,CAAC,sBAAsB;gDAAC;;;;;;;wCAE/F,kBAAkB,mBAAmB,CAAC,iBAAiB,EAAE,SAAS,mBACjE,6LAAC;;8DAAE,6LAAC;8DAAO;;;;;;gDAAuB;gDAAE,kBAAkB,mBAAmB,CAAC,iBAAiB,CAAC,IAAI,CAAC;;;;;;;wCAElG,kBAAkB,mBAAmB,CAAC,iBAAiB,EAAE,SAAS,mBACjE,6LAAC;;8DAAE,6LAAC;8DAAO;;;;;;gDAAuB;gDAAE,kBAAkB,mBAAmB,CAAC,iBAAiB,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;;;sCAOzG,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,WAAU;oCACV,UAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;;sDAEV,6LAAC,+MAAA,CAAA,SAAM;4CAAC,MAAM;;;;;;wCACb,UAAU,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7C;GA/TwB;KAAA", "debugId": null}}, {"offset": {"line": 4459, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/app/admin/student-management/StudentProfile.jsx"], "sourcesContent": ["'use client';\r\nimport { useState, useEffect } from 'react';\r\nimport Image from 'next/image';\r\nimport { ArrowLeft, Edit, Save, X, Lock, Unlock } from 'lucide-react';\r\nimport { FaFileAlt, FaBuilding, FaMapMarkerAlt, FaPhoneAlt, FaUser, FaSpinner } from 'react-icons/fa';\r\nimport { studentsAPI } from '../../../api/students';\r\nimport DocumentsModal from './DocumentsModal';\r\nimport ResumeModal from './ResumeModal';\r\nimport FreezeModal from './FreezeModal';\r\n\r\nexport default function StudentProfile({\r\n  selectedStudent,\r\n  editedStudent,\r\n  isEditing,\r\n  handleBackToList,\r\n  handleEdit,\r\n  handleSave,\r\n  handleCancel,\r\n  handleInputChange,\r\n  departmentOptions\r\n}) {\r\n  const [profile, setProfile] = useState(null);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState(null);\r\n  const [semesterMarksheets, setSemesterMarksheets] = useState([]);\r\n  const [isDocumentsModalOpen, setIsDocumentsModalOpen] = useState(false);\r\n  const [isResumeModalOpen, setIsResumeModalOpen] = useState(false);\r\n  const [isFreezeModalOpen, setIsFreezeModalOpen] = useState(false);\r\n  const [companyStats, setCompanyStats] = useState({\r\n    loading: false,\r\n    totalListings: 0,\r\n    eligibleJobs: 0\r\n  });\r\n  const [resumeCount, setResumeCount] = useState(0);\r\n  const [lastResumeUpdate, setLastResumeUpdate] = useState(null);\r\n  const [freezeStatus, setFreezeStatus] = useState('none');\r\n  const [freezeData, setFreezeData] = useState(null);\r\n\r\n  // Helper function to check if a document actually exists\r\n  const isValidDocument = (document) => {\r\n    return document &&\r\n           typeof document === 'string' &&\r\n           document.trim() !== '' &&\r\n           document !== 'null' &&\r\n           document !== 'undefined';\r\n  };\r\n\r\n  // Calculate actual document count\r\n  const getDocumentCount = () => {\r\n    const tenthCount = isValidDocument(profile?.tenth_certificate) ? 1 : 0;\r\n    const twelfthCount = isValidDocument(profile?.twelfth_certificate) ? 1 : 0;\r\n    const semesterCount = semesterMarksheets ?\r\n      semesterMarksheets.filter(sheet => sheet && isValidDocument(sheet.marksheet_url)).length : 0;\r\n\r\n    return tenthCount + twelfthCount + semesterCount;\r\n  };\r\n\r\n  // Use selectedStudent as the profile data, but allow fetching more details if needed\r\n  useEffect(() => {\r\n    if (selectedStudent) {\r\n      setProfile(selectedStudent);\r\n\r\n      // Optionally fetch additional details if needed\r\n      fetchAdditionalDetails(selectedStudent.id);\r\n\r\n      // Fetch company statistics\r\n      fetchCompanyStats(selectedStudent.id);\r\n\r\n      // Fetch resume information\r\n      fetchResumeInfo(selectedStudent.id);\r\n\r\n      // Fetch freeze status\r\n      fetchFreezeStatus(selectedStudent.id);\r\n    }\r\n  }, [selectedStudent]);\r\n  \r\n  // Fetch additional details if needed\r\n  const fetchAdditionalDetails = async (studentId) => {\r\n    if (!studentId) return;\r\n\r\n    try {\r\n      setLoading(true);\r\n      // Fetch detailed student profile including semester marksheets\r\n      const details = await studentsAPI.getStudent(studentId);\r\n      if (details) {\r\n        // Update the main profile with fresh data (including certificate URLs)\r\n        setProfile(details);\r\n\r\n        // Update semester marksheets\r\n        if (details.semester_marksheets) {\r\n          setSemesterMarksheets(details.semester_marksheets);\r\n        }\r\n      }\r\n      setLoading(false);\r\n    } catch (err) {\r\n      console.error('Error fetching student details:', err);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Fetch company statistics for the student\r\n  const fetchCompanyStats = async (studentId) => {\r\n    if (!studentId) return;\r\n\r\n    try {\r\n      setCompanyStats(prev => ({ ...prev, loading: true }));\r\n\r\n      // Mock implementation - replace with actual API call\r\n      // const stats = await studentsAPI.getStudentCompanyStats(studentId);\r\n\r\n      // For now, provide mock data\r\n      const mockStats = {\r\n        loading: false,\r\n        totalListings: 25,\r\n        eligibleJobs: 18\r\n      };\r\n\r\n      setCompanyStats(mockStats);\r\n    } catch (err) {\r\n      console.error('Error fetching company stats:', err);\r\n      setCompanyStats({\r\n        loading: false,\r\n        totalListings: 0,\r\n        eligibleJobs: 0\r\n      });\r\n    }\r\n  };\r\n\r\n  // Fetch resume information for the student\r\n  const fetchResumeInfo = async (studentId) => {\r\n    if (!studentId) return;\r\n\r\n    try {\r\n      // Get actual resume data from API\r\n      const resumesData = await studentsAPI.adminGetResumes(studentId);\r\n\r\n      if (Array.isArray(resumesData)) {\r\n        setResumeCount(resumesData.length);\r\n\r\n        // Find the most recent upload date\r\n        if (resumesData.length > 0) {\r\n          const latestResume = resumesData.reduce((latest, current) => {\r\n            const currentDate = new Date(current.uploaded_at);\r\n            const latestDate = new Date(latest.uploaded_at);\r\n            return currentDate > latestDate ? current : latest;\r\n          });\r\n          setLastResumeUpdate(latestResume.uploaded_at);\r\n        } else {\r\n          setLastResumeUpdate(null);\r\n        }\r\n      } else {\r\n        setResumeCount(0);\r\n        setLastResumeUpdate(null);\r\n      }\r\n    } catch (err) {\r\n      console.error('Error fetching resume info:', err);\r\n      setResumeCount(0);\r\n      setLastResumeUpdate(null);\r\n    }\r\n  };\r\n\r\n  // Fetch freeze status for the student\r\n  const fetchFreezeStatus = async (studentId) => {\r\n    if (!studentId) return;\r\n\r\n    try {\r\n      const response = await fetch(`http://127.0.0.1:8000/api/accounts/students/${studentId}/freeze/`, {\r\n        headers: {\r\n          'Authorization': `Bearer ${localStorage.getItem('access')}`,\r\n          'Content-Type': 'application/json'\r\n        }\r\n      });\r\n\r\n      if (response.ok) {\r\n        const data = await response.json();\r\n        setFreezeStatus(data.freeze_status);\r\n        setFreezeData(data);\r\n      } else {\r\n        setFreezeStatus('none');\r\n        setFreezeData(null);\r\n      }\r\n    } catch (err) {\r\n      console.error('Error fetching freeze status:', err);\r\n      setFreezeStatus('none');\r\n      setFreezeData(null);\r\n    }\r\n  };\r\n  \r\n  // Get overall CGPA from database\r\n  const getOverallCGPA = () => {\r\n    return editedStudent?.gpa || profile?.gpa || 'N/A';\r\n  };\r\n  \r\n  // Calculate percentage from CGPA (approximation)\r\n  const calculatePercentage = (cgpa) => {\r\n    if (!cgpa || cgpa === 'N/A') return '';\r\n    const numericCgpa = parseFloat(cgpa);\r\n    if (isNaN(numericCgpa)) return '';\r\n    return (numericCgpa * 9.5).toFixed(2) + '%';\r\n  };\r\n  \r\n  // Format year range if available\r\n  const formatEducationPeriod = (joiningYear, passoutYear) => {\r\n    if (joiningYear && passoutYear) {\r\n      return `${joiningYear} - ${passoutYear}`;\r\n    }\r\n    return 'N/A';\r\n  };\r\n\r\n  // Get year range for student\r\n  const getYearRange = () => {\r\n    if (profile?.joining_year && profile?.passout_year) {\r\n      return `${profile.joining_year} - ${profile.passout_year}`;\r\n    }\r\n    if (editedStudent?.joining_year && editedStudent?.passout_year) {\r\n      return `${editedStudent.joining_year} - ${editedStudent.passout_year}`;\r\n    }\r\n    if (profile?.year || editedStudent?.year) {\r\n      return profile?.year || editedStudent?.year;\r\n    }\r\n    return null;\r\n  };\r\n\r\n  // Get semester CGPA\r\n  const getSemesterCGPA = (semester) => {\r\n    // First try to get from semesterMarksheets (if available)\r\n    if (semesterMarksheets && semesterMarksheets.length > 0) {\r\n      return semesterMarksheets[semester - 1]?.cgpa || '-';\r\n    }\r\n\r\n    // Then try to get from editedStudent semester_cgpas\r\n    if (editedStudent?.semester_cgpas && editedStudent.semester_cgpas.length > 0) {\r\n      const semesterData = editedStudent.semester_cgpas.find(s => s.semester === semester);\r\n      return semesterData?.cgpa || '-';\r\n    }\r\n\r\n    // Finally try to get from profile semester_cgpas\r\n    if (profile?.semester_cgpas && profile.semester_cgpas.length > 0) {\r\n      const semesterData = profile.semester_cgpas.find(s => s.semester === semester);\r\n      return semesterData?.cgpa || '-';\r\n    }\r\n\r\n    return '-';\r\n  };\r\n\r\n  // Handle semester CGPA change\r\n  const handleSemesterCGPAChange = (semester, value) => {\r\n    const currentSemesterCGPAs = editedStudent?.semester_cgpas || [];\r\n    const updatedSemesterCGPAs = [...currentSemesterCGPAs];\r\n\r\n    // Find existing semester data or create new one\r\n    const existingIndex = updatedSemesterCGPAs.findIndex(s => s.semester === semester);\r\n\r\n    if (existingIndex >= 0) {\r\n      // Update existing semester\r\n      updatedSemesterCGPAs[existingIndex] = {\r\n        ...updatedSemesterCGPAs[existingIndex],\r\n        cgpa: value\r\n      };\r\n    } else {\r\n      // Add new semester data\r\n      updatedSemesterCGPAs.push({\r\n        semester: semester,\r\n        cgpa: value\r\n      });\r\n    }\r\n\r\n    // Sort by semester number\r\n    updatedSemesterCGPAs.sort((a, b) => a.semester - b.semester);\r\n\r\n    // Update the edited student\r\n    handleInputChange('semester_cgpas', updatedSemesterCGPAs);\r\n  };\r\n\r\n  // Handle resume upload\r\n  const handleResumeUpload = async (file) => {\r\n    try {\r\n      if (!selectedStudent) {\r\n        alert('No student selected');\r\n        return;\r\n      }\r\n\r\n      // Use admin upload function with student ID\r\n      await studentsAPI.adminUploadResume(selectedStudent.id, file, file.name, false);\r\n\r\n      alert('Resume uploaded successfully!');\r\n\r\n      // Refresh resume info after upload\r\n      fetchResumeInfo(selectedStudent.id);\r\n    } catch (err) {\r\n      console.error('Error uploading resume:', err);\r\n      alert(`Failed to upload resume: ${err.response?.data?.error || err.message || 'Unknown error'}`);\r\n    }\r\n  };\r\n\r\n  // Handle resume delete\r\n  const handleResumeDelete = async (resumeId) => {\r\n    try {\r\n      await studentsAPI.deleteResume(resumeId);\r\n      // Refresh resume info after delete\r\n      if (selectedStudent) {\r\n        fetchResumeInfo(selectedStudent.id);\r\n      }\r\n    } catch (err) {\r\n      console.error('Error deleting resume:', err);\r\n      alert('Failed to delete resume. Please try again.');\r\n    }\r\n  };\r\n\r\n  // Handle freeze account\r\n  const handleFreeze = async (freezeData) => {\r\n    try {\r\n      const response = await fetch(`http://127.0.0.1:8000/api/accounts/students/${selectedStudent.id}/freeze/`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Authorization': `Bearer ${localStorage.getItem('access')}`,\r\n          'Content-Type': 'application/json'\r\n        },\r\n        body: JSON.stringify(freezeData)\r\n      });\r\n\r\n      if (response.ok) {\r\n        const result = await response.json();\r\n        alert(result.message);\r\n        // Refresh freeze status\r\n        fetchFreezeStatus(selectedStudent.id);\r\n      } else {\r\n        const error = await response.json();\r\n        throw new Error(error.detail || 'Failed to freeze account');\r\n      }\r\n    } catch (err) {\r\n      console.error('Error freezing account:', err);\r\n      throw err;\r\n    }\r\n  };\r\n\r\n  // Handle unfreeze account\r\n  const handleUnfreeze = async () => {\r\n    try {\r\n      const response = await fetch(`http://127.0.0.1:8000/api/accounts/students/${selectedStudent.id}/freeze/`, {\r\n        method: 'DELETE',\r\n        headers: {\r\n          'Authorization': `Bearer ${localStorage.getItem('access')}`,\r\n          'Content-Type': 'application/json'\r\n        }\r\n      });\r\n\r\n      if (response.ok) {\r\n        const result = await response.json();\r\n        alert(result.message);\r\n        // Refresh freeze status\r\n        fetchFreezeStatus(selectedStudent.id);\r\n      } else {\r\n        const error = await response.json();\r\n        throw new Error(error.detail || 'Failed to unfreeze account');\r\n      }\r\n    } catch (err) {\r\n      console.error('Error unfreezing account:', err);\r\n      throw err;\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center h-64\">\r\n        <FaSpinner className=\"animate-spin text-blue-500 text-xl mr-2\" />\r\n        <span>Loading details...</span>\r\n      </div>\r\n    );\r\n  }\r\n  \r\n  if (!selectedStudent && !editedStudent) {\r\n    return (\r\n      <div className=\"text-center p-8\">\r\n        <p className=\"text-red-500\">No student selected</p>\r\n        <button \r\n          onClick={handleBackToList} \r\n          className=\"mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\r\n        >\r\n          Back to List\r\n        </button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"bg-gray-50 min-h-screen p-6\">\r\n      {/* Header with back button and edit/save buttons */}\r\n      <div className=\"flex justify-between items-center mb-6\">\r\n        <button\r\n          onClick={handleBackToList}\r\n          className=\"flex items-center gap-2 text-blue-600 hover:text-blue-800 transition-colors\"\r\n        >\r\n          <ArrowLeft size={16} />\r\n          <span>Back to List</span>\r\n        </button>\r\n        \r\n        <div className=\"flex gap-2\">\r\n          {isEditing ? (\r\n            <>\r\n              <button\r\n                onClick={handleSave}\r\n                className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\"\r\n              >\r\n                <div className=\"flex items-center gap-2\">\r\n                  <Save size={16} />\r\n                  <span>Save</span>\r\n                </div>\r\n              </button>\r\n              <button\r\n                onClick={handleCancel}\r\n                className=\"px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors\"\r\n              >\r\n                <div className=\"flex items-center gap-2\">\r\n                  <X size={16} />\r\n                  <span>Cancel</span>\r\n                </div>\r\n              </button>\r\n            </>\r\n          ) : (\r\n            <>\r\n              <button\r\n                onClick={handleEdit}\r\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\r\n              >\r\n                <div className=\"flex items-center gap-2\">\r\n                  <Edit size={16} />\r\n                  <span>Edit</span>\r\n                </div>\r\n              </button>\r\n              <button\r\n                onClick={() => setIsFreezeModalOpen(true)}\r\n                className={`px-4 py-2 rounded-lg transition-colors flex items-center gap-2 ${\r\n                  freezeStatus === 'none' \r\n                    ? 'bg-red-600 text-white hover:bg-red-700' \r\n                    : freezeStatus === 'complete'\r\n                    ? 'bg-orange-600 text-white hover:bg-orange-700'\r\n                    : 'bg-yellow-600 text-white hover:bg-yellow-700'\r\n                }`}\r\n              >\r\n                {freezeStatus === 'none' ? (\r\n                  <>\r\n                    <Lock size={16} />\r\n                    <span>Freeze</span>\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <Unlock size={16} />\r\n                    <span>Manage Freeze</span>\r\n                  </>\r\n                )}\r\n              </button>\r\n            </>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"max-w-8xl mx-auto grid grid-cols-1 lg:grid-cols-12 gap-6\">\r\n        {/* Left Column - Student Information */}\r\n        <div className=\"lg:col-span-3 bg-white rounded-lg p-5 shadow-sm h-fit\">\r\n          <div className=\"flex justify-center\">\r\n            <div className=\"w-50 h-50 bg-blue-500 text-white flex items-center justify-center rounded-lg mb-4\">\r\n              <span className=\"text-3xl font-bold\">\r\n                {editedStudent?.name ? editedStudent.name[0].toUpperCase() : 'S'}\r\n              </span>\r\n            </div>\r\n          </div>\r\n          \r\n          <h1 className=\"text-xl font-bold text-center mt-2 text-gray-800\">\r\n            {isEditing ? (\r\n              <input\r\n                type=\"text\"\r\n                value={editedStudent?.name || ''}\r\n                onChange={(e) => handleInputChange('name', e.target.value)}\r\n                className=\"w-full p-1 border rounded text-center\"\r\n              />\r\n            ) : (\r\n              editedStudent?.name || 'Unknown Student'\r\n            )}\r\n          </h1>\r\n          \r\n          <div className=\"mt-4 space-y-3 text-md\">\r\n            <div className=\"flex\">\r\n              <p className=\"text-gray-500 w-20\">Student ID</p>\r\n              <p className=\"font-medium text-gray-800\">: {isEditing ? (\r\n                <input\r\n                  type=\"text\"\r\n                  value={editedStudent?.rollNumber || ''}\r\n                  onChange={(e) => handleInputChange('rollNumber', e.target.value)}\r\n                  className=\"p-1 border rounded\"\r\n                />\r\n              ) : (editedStudent?.rollNumber || '-')}</p>\r\n            </div>\r\n            <div className=\"flex\">\r\n              <p className=\"text-gray-500 w-20\">Major</p>\r\n              <p className=\"font-medium text-gray-800\">: {isEditing ? (\r\n                <select\r\n                  value={editedStudent?.department || ''}\r\n                  onChange={(e) => handleInputChange('department', e.target.value)}\r\n                  className=\"p-1 border rounded\"\r\n                >\r\n                  <option value=\"\">Select Department</option>\r\n                  {departmentOptions.map(option => (\r\n                    <option key={option.value} value={option.value}>\r\n                      {option.label}\r\n                    </option>\r\n                  ))}\r\n                </select>\r\n              ) : (editedStudent?.department || '-')}</p>\r\n            </div>\r\n            <div className=\"flex\">\r\n              <p className=\"text-gray-500 w-20\">Passed Out</p>\r\n              <p className=\"font-medium text-gray-800\">: {isEditing ? (\r\n                <input\r\n                  type=\"text\"\r\n                  value={editedStudent?.passout_year || ''}\r\n                  onChange={(e) => handleInputChange('passout_year', e.target.value)}\r\n                  className=\"p-1 border rounded\"\r\n                />\r\n              ) : (editedStudent?.passout_year || '-')}</p>\r\n            </div>\r\n            <div className=\"flex\">\r\n              <p className=\"text-gray-500 w-20\">Gender</p>\r\n              <p className=\"font-medium text-gray-800\">: {isEditing ? (\r\n                <select\r\n                  value={editedStudent?.gender || ''}\r\n                  onChange={(e) => handleInputChange('gender', e.target.value)}\r\n                  className=\"p-1 border rounded\"\r\n                >\r\n                  <option value=\"\">Select Gender</option>\r\n                  <option value=\"Male\">Male</option>\r\n                  <option value=\"Female\">Female</option>\r\n                  <option value=\"Other\">Other</option>\r\n                </select>\r\n              ) : (editedStudent?.gender || '-')}</p>\r\n            </div>\r\n            <div className=\"flex\">\r\n              <p className=\"text-gray-500 w-20\">Birthday</p>\r\n              <p className=\"font-medium text-gray-800\">: {isEditing ? (\r\n                <input\r\n                  type=\"date\"\r\n                  value={editedStudent?.dateOfBirth || ''}\r\n                  onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}\r\n                  className=\"p-1 border rounded\"\r\n                />\r\n              ) : (editedStudent?.dateOfBirth || '-')}</p>\r\n            </div>\r\n            <div className=\"flex\">\r\n              <p className=\"text-gray-500 w-20\">Phone</p>\r\n              <p className=\"font-medium text-gray-800\">: {isEditing ? (\r\n                <input\r\n                  type=\"tel\"\r\n                  value={editedStudent?.phone || ''}\r\n                  onChange={(e) => handleInputChange('phone', e.target.value)}\r\n                  className=\"p-1 border rounded\"\r\n                />\r\n              ) : (editedStudent?.phone || '-')}</p>\r\n            </div>\r\n            <div className=\"flex\">\r\n              <p className=\"text-gray-500 w-20\">Email</p>\r\n              <p className=\"font-medium text-gray-800\">: {isEditing ? (\r\n                <input\r\n                  type=\"email\"\r\n                  value={editedStudent?.email || ''}\r\n                  onChange={(e) => handleInputChange('email', e.target.value)}\r\n                  className=\"p-1 border rounded\"\r\n                />\r\n              ) : (editedStudent?.email || '-')}</p>\r\n            </div>\r\n            <div className=\"flex\">\r\n              <p className=\"text-gray-500 w-20\">Campus</p>\r\n              <p className=\"font-medium text-gray-800\">: {isEditing ? (\r\n                <input\r\n                  type=\"text\"\r\n                  value={editedStudent?.college_name || ''}\r\n                  onChange={(e) => handleInputChange('college_name', e.target.value)}\r\n                  className=\"p-1 border rounded\"\r\n                />\r\n              ) : (editedStudent?.college_name || '-')}</p>\r\n            </div>\r\n            <div className=\"flex\">\r\n              <p className=\"text-gray-500 w-20\">Placement</p>\r\n              <p className=\"font-medium text-gray-800\">: {getYearRange() || '-'}</p>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Skills Section */}\r\n          <div className=\"mt-6\">\r\n            <h3 className=\"text-lg font-semibold mb-3 text-gray-800\">Skills</h3>\r\n            {isEditing ? (\r\n              <textarea\r\n                value={Array.isArray(editedStudent?.skills) ? editedStudent.skills.join(', ') : editedStudent?.skills || ''}\r\n                onChange={(e) => handleInputChange('skills', e.target.value.split(',').map(s => s.trim()))}\r\n                className=\"w-full p-2 border rounded-lg\"\r\n                rows={3}\r\n                placeholder=\"Enter skills separated by commas\"\r\n              />\r\n            ) : (\r\n              <div className=\"flex flex-wrap gap-2\">\r\n                {(() => {\r\n                  const skills = editedStudent?.skills || profile?.skills || selectedStudent?.skills;\r\n                  \r\n                  if (Array.isArray(skills) && skills.length > 0) {\r\n                    return skills.map((skill, index) => (\r\n                      <span \r\n                        key={index} \r\n                        className=\"bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm\"\r\n                      >\r\n                        {skill}\r\n                      </span>\r\n                    ));\r\n                  } else if (typeof skills === 'string' && skills.trim()) {\r\n                    return skills.split(',').map((skill, index) => (\r\n                      <span \r\n                        key={index} \r\n                        className=\"bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm\"\r\n                      >\r\n                        {skill.trim()}\r\n                      </span>\r\n                    ));\r\n                  } else {\r\n                    return <p className=\"text-gray-600\">No skills listed</p>;\r\n                  }\r\n                })()}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Middle Column - Academic Details */}\r\n        <div className=\"lg:col-span-6 space-y-6\">\r\n          {/* Combined Academic Details */}\r\n          <div className=\"bg-white rounded-lg p-5 shadow-sm\">\r\n            <h2 className=\"text-xl font-semibold mb-6 text-gray-800\">Academic</h2>\r\n            <div className=\"flex justify-between items-center mb-3\">\r\n              <div className=\"flex items-center\">\r\n                <h3 className=\"text-lg font-semibold text-gray-800\">Semester Wise score</h3>\r\n                <div className=\"bg-blue-50 inline-flex items-center px-3 py-1 rounded-full ml-3\">\r\n                  {isEditing ? (\r\n                    <>\r\n                      <input\r\n                        type=\"text\"\r\n                        value={editedStudent?.gpa || ''}\r\n                        onChange={(e) => handleInputChange('gpa', e.target.value)}\r\n                        className=\"w-16 p-1 border rounded text-sm text-blue-600 bg-transparent\"\r\n                        placeholder=\"0.00\"\r\n                      />\r\n                      <span className=\"text-sm text-gray-500 ml-1\">CGPA</span>\r\n                      <span className=\"text-blue-600 ml-2\">{calculatePercentage(editedStudent?.gpa)}</span>\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      <span className=\"text-blue-600 font-medium\">{getOverallCGPA()}</span>\r\n                      <span className=\"text-sm text-gray-500 ml-1\">CGPA</span>\r\n                      <span className=\"text-blue-600 ml-2\">{calculatePercentage(getOverallCGPA())}</span>\r\n                    </>\r\n                  )}\r\n                </div>\r\n              </div>\r\n              <div className=\"text-lg font-semibold text-gray-800\">\r\n                {isEditing ? (\r\n                  <div className=\"flex gap-2\">\r\n                    <input\r\n                      type=\"text\"\r\n                      value={editedStudent?.joining_year || ''}\r\n                      onChange={(e) => handleInputChange('joining_year', e.target.value)}\r\n                      className=\"w-20 p-1 border rounded text-sm\"\r\n                      placeholder=\"2020\"\r\n                    />\r\n                    <span>-</span>\r\n                    <input\r\n                      type=\"text\"\r\n                      value={editedStudent?.passout_year || ''}\r\n                      onChange={(e) => handleInputChange('passout_year', e.target.value)}\r\n                      className=\"w-20 p-1 border rounded text-sm\"\r\n                      placeholder=\"2024\"\r\n                    />\r\n                  </div>\r\n                ) : (\r\n                  formatEducationPeriod(editedStudent?.joining_year, editedStudent?.passout_year)\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Current Semester Scores */}\r\n            <div className=\"mb-6\">\r\n              <div className=\"overflow-x-auto\">\r\n                <table className=\"w-full border-collapse border border-gray-300\">\r\n                  <thead>\r\n                    <tr className=\"bg-gray-50\">\r\n                      <th className=\"border border-gray-300 px-4 py-3 text-sm font-semibold text-gray-700\">Sem</th>\r\n                      {[1, 2, 3, 4, 5, 6, 7, 8].map(sem => (\r\n                        <th key={sem} className=\"border border-gray-300 px-4 py-3 text-sm font-semibold text-gray-700\">{sem}</th>\r\n                      ))}\r\n                    </tr>\r\n                  </thead>\r\n                  <tbody>\r\n                    <tr>\r\n                      <td className=\"border border-gray-300 px-4 py-3 text-sm font-medium text-gray-700\">Cgpa</td>\r\n                      {[1, 2, 3, 4, 5, 6, 7, 8].map(sem => (\r\n                        <td key={sem} className=\"border border-gray-300 px-4 py-3 text-sm text-gray-700\">\r\n                          {isEditing ? (\r\n                            <input\r\n                              type=\"text\"\r\n                              value={getSemesterCGPA(sem) !== '-' ? getSemesterCGPA(sem) : ''}\r\n                              onChange={(e) => handleSemesterCGPAChange(sem, e.target.value)}\r\n                              className=\"w-full p-1 border rounded text-sm text-center\"\r\n                              placeholder=\"0.0\"\r\n                            />\r\n                          ) : (\r\n                            getSemesterCGPA(sem)\r\n                          )}\r\n                        </td>\r\n                      ))}\r\n                    </tr>\r\n                  </tbody>\r\n                </table>\r\n              </div>\r\n            </div>\r\n            <hr className=\"my-6\" />\r\n\r\n            {/* Class XII */}\r\n            <div className=\"mb-6\">\r\n              <div className=\"flex justify-between items-center mb-3\">\r\n                <div className=\"flex items-center\">\r\n                  <h3 className=\"text-lg font-semibold text-gray-800\">Class XII</h3>\r\n                  <div className=\"bg-blue-50 inline-flex items-center px-3 py-1 rounded-full ml-3\">\r\n                    {isEditing ? (\r\n                      <>\r\n                        <input\r\n                          type=\"text\"\r\n                          value={editedStudent?.twelfth_cgpa || ''}\r\n                          onChange={(e) => handleInputChange('twelfth_cgpa', e.target.value)}\r\n                          className=\"w-16 p-1 border rounded text-sm text-blue-600 bg-transparent\"\r\n                          placeholder=\"9.5\"\r\n                        />\r\n                        <span className=\"text-sm text-gray-500 ml-1\">CGPA</span>\r\n                        <input\r\n                          type=\"text\"\r\n                          value={editedStudent?.twelfth_percentage || ''}\r\n                          onChange={(e) => handleInputChange('twelfth_percentage', e.target.value)}\r\n                          className=\"w-16 p-1 border rounded text-sm text-blue-600 bg-transparent ml-2\"\r\n                          placeholder=\"95%\"\r\n                        />\r\n                      </>\r\n                    ) : (\r\n                      <>\r\n                        <span className=\"text-blue-600 font-medium\">{editedStudent?.twelfth_cgpa || '-'}</span>\r\n                        <span className=\"text-sm text-gray-500 ml-1\">CGPA</span>\r\n                        <span className=\"text-blue-600 ml-2\">{editedStudent?.twelfth_percentage || '-'}</span>\r\n                      </>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n                <div className=\"text-lg font-semibold text-gray-800\">\r\n                  {isEditing ? (\r\n                    <input\r\n                      type=\"text\"\r\n                      value={editedStudent?.twelfth_year_of_passing || ''}\r\n                      onChange={(e) => handleInputChange('twelfth_year_of_passing', e.target.value)}\r\n                      className=\"w-20 p-1 border rounded text-sm\"\r\n                      placeholder=\"2020\"\r\n                    />\r\n                  ) : (\r\n                    editedStudent?.twelfth_year_of_passing\r\n                      ? `${parseInt(editedStudent.twelfth_year_of_passing) - 2} - ${editedStudent.twelfth_year_of_passing}`\r\n                      : '-'\r\n                  )}\r\n                </div>\r\n              </div>\r\n              <div className=\"flex justify-between items-start mb-2\">\r\n                <div className=\"grid grid-cols-2 gap-6 w-full\">\r\n                  <div className=\"space-y-3\">\r\n                    <div className=\"flex\">\r\n                      <p className=\"text-gray-500 text-sm w-[120px]\">College :</p>\r\n                      {isEditing ? (\r\n                        <input\r\n                          type=\"text\"\r\n                          value={editedStudent?.twelfth_school || ''}\r\n                          onChange={(e) => handleInputChange('twelfth_school', e.target.value)}\r\n                          className=\"flex-1 p-1 border rounded text-sm\"\r\n                          placeholder=\"School/College name\"\r\n                        />\r\n                      ) : (\r\n                        <p className=\"text-gray-700 font-medium\">{editedStudent?.twelfth_school || '-'}</p>\r\n                      )}\r\n                    </div>\r\n                    <div className=\"flex\">\r\n                      <p className=\"text-gray-500 text-sm w-[120px]\">Board :</p>\r\n                      {isEditing ? (\r\n                        <input\r\n                          type=\"text\"\r\n                          value={editedStudent?.twelfth_board || ''}\r\n                          onChange={(e) => handleInputChange('twelfth_board', e.target.value)}\r\n                          className=\"flex-1 p-1 border rounded text-sm\"\r\n                          placeholder=\"Board name\"\r\n                        />\r\n                      ) : (\r\n                        <p className=\"text-gray-700 font-medium\">{editedStudent?.twelfth_board || '-'}</p>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"space-y-3\">\r\n                    <div className=\"flex\">\r\n                      <p className=\"text-gray-500 text-sm w-[120px]\">Location :</p>\r\n                      {isEditing ? (\r\n                        <input\r\n                          type=\"text\"\r\n                          value={editedStudent?.twelfth_location || ''}\r\n                          onChange={(e) => handleInputChange('twelfth_location', e.target.value)}\r\n                          className=\"flex-1 p-1 border rounded text-sm\"\r\n                          placeholder=\"City, State\"\r\n                        />\r\n                      ) : (\r\n                        <p className=\"text-gray-700 font-medium\">{editedStudent?.twelfth_location || '-'}</p>\r\n                      )}\r\n                    </div>\r\n                    <div className=\"flex\">\r\n                      <p className=\"text-gray-500 text-sm w-[120px]\">Specialization :</p>\r\n                      {isEditing ? (\r\n                        <input\r\n                          type=\"text\"\r\n                          value={editedStudent?.twelfth_specialization || ''}\r\n                          onChange={(e) => handleInputChange('twelfth_specialization', e.target.value)}\r\n                          className=\"flex-1 p-1 border rounded text-sm\"\r\n                          placeholder=\"Science/Commerce/Arts\"\r\n                        />\r\n                      ) : (\r\n                        <p className=\"text-gray-700 font-medium\">{editedStudent?.twelfth_specialization || '-'}</p>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <hr className=\"my-6\" />\r\n\r\n            {/* Class X */}\r\n            <div>\r\n              <div className=\"flex justify-between items-center mb-3\">\r\n                <div className=\"flex items-center\">\r\n                  <h3 className=\"text-lg font-semibold text-gray-800\">Class X</h3>\r\n                  <div className=\"bg-blue-50 inline-flex items-center px-3 py-1 rounded-full ml-3\">\r\n                    {isEditing ? (\r\n                      <>\r\n                        <input\r\n                          type=\"text\"\r\n                          value={editedStudent?.tenth_cgpa || ''}\r\n                          onChange={(e) => handleInputChange('tenth_cgpa', e.target.value)}\r\n                          className=\"w-16 p-1 border rounded text-sm text-blue-600 bg-transparent\"\r\n                          placeholder=\"9.5\"\r\n                        />\r\n                        <span className=\"text-sm text-gray-500 ml-1\">CGPA</span>\r\n                        <input\r\n                          type=\"text\"\r\n                          value={editedStudent?.tenth_percentage || ''}\r\n                          onChange={(e) => handleInputChange('tenth_percentage', e.target.value)}\r\n                          className=\"w-16 p-1 border rounded text-sm text-blue-600 bg-transparent ml-2\"\r\n                          placeholder=\"95%\"\r\n                        />\r\n                      </>\r\n                    ) : (\r\n                      <>\r\n                        <span className=\"text-blue-600 font-medium\">{editedStudent?.tenth_cgpa || '-'}</span>\r\n                        <span className=\"text-sm text-gray-500 ml-1\">CGPA</span>\r\n                        <span className=\"text-blue-600 ml-2\">{editedStudent?.tenth_percentage || '-'}</span>\r\n                      </>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n                <div className=\"text-lg font-semibold text-gray-800\">\r\n                  {isEditing ? (\r\n                    <input\r\n                      type=\"text\"\r\n                      value={editedStudent?.tenth_year_of_passing || ''}\r\n                      onChange={(e) => handleInputChange('tenth_year_of_passing', e.target.value)}\r\n                      className=\"w-20 p-1 border rounded text-sm\"\r\n                      placeholder=\"2018\"\r\n                    />\r\n                  ) : (\r\n                    editedStudent?.tenth_year_of_passing\r\n                      ? `${parseInt(editedStudent.tenth_year_of_passing) - 1} - ${editedStudent.tenth_year_of_passing}`\r\n                      : '-'\r\n                  )}\r\n                </div>\r\n              </div>\r\n              \r\n              <div className=\"flex justify-between items-start mb-2\">\r\n                <div className=\"grid grid-cols-2 gap-6 w-full\">\r\n                  <div className=\"space-y-3\">\r\n                    <div className=\"flex\">\r\n                      <p className=\"text-gray-500 text-sm w-[120px]\">School :</p>\r\n                      {isEditing ? (\r\n                        <input\r\n                          type=\"text\"\r\n                          value={editedStudent?.tenth_school || ''}\r\n                          onChange={(e) => handleInputChange('tenth_school', e.target.value)}\r\n                          className=\"flex-1 p-1 border rounded text-sm\"\r\n                          placeholder=\"School name\"\r\n                        />\r\n                      ) : (\r\n                        <p className=\"text-gray-700 font-medium\">{editedStudent?.tenth_school || '-'}</p>\r\n                      )}\r\n                    </div>\r\n                    <div className=\"flex\">\r\n                      <p className=\"text-gray-500 text-sm w-[120px]\">Board :</p>\r\n                      {isEditing ? (\r\n                        <input\r\n                          type=\"text\"\r\n                          value={editedStudent?.tenth_board || ''}\r\n                          onChange={(e) => handleInputChange('tenth_board', e.target.value)}\r\n                          className=\"flex-1 p-1 border rounded text-sm\"\r\n                          placeholder=\"Board name\"\r\n                        />\r\n                      ) : (\r\n                        <p className=\"text-gray-700 font-medium\">{editedStudent?.tenth_board || '-'}</p>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"space-y-3\">\r\n                    <div className=\"flex\">\r\n                      <p className=\"text-gray-500 text-sm w-[120px]\">Location :</p>\r\n                      {isEditing ? (\r\n                        <input\r\n                          type=\"text\"\r\n                          value={editedStudent?.tenth_location || ''}\r\n                          onChange={(e) => handleInputChange('tenth_location', e.target.value)}\r\n                          className=\"flex-1 p-1 border rounded text-sm\"\r\n                          placeholder=\"City, State\"\r\n                        />\r\n                      ) : (\r\n                        <p className=\"text-gray-700 font-medium\">{editedStudent?.tenth_location || '-'}</p>\r\n                      )}\r\n                    </div>\r\n                    <div className=\"flex\">\r\n                      <p className=\"text-gray-500 text-sm w-[120px]\">Specialization :</p>\r\n                      {isEditing ? (\r\n                        <input\r\n                          type=\"text\"\r\n                          value={editedStudent?.tenth_specialization || ''}\r\n                          onChange={(e) => handleInputChange('tenth_specialization', e.target.value)}\r\n                          className=\"flex-1 p-1 border rounded text-sm\"\r\n                          placeholder=\"General/Other\"\r\n                        />\r\n                      ) : (\r\n                        <p className=\"text-gray-700 font-medium\">{editedStudent?.tenth_specialization || '-'}</p>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          {/* Companies Section */}\r\n          <div className=\"bg-white rounded-lg p-5 shadow-sm\">\r\n            <h2 className=\"text-xl font-semibold mb-4 text-gray-800\">Companies</h2>\r\n              \r\n            {companyStats.loading ? (\r\n              <div className=\"flex items-center justify-center py-4\">\r\n                <FaSpinner className=\"animate-spin text-blue-500 text-xl mr-2\" />\r\n                <span className=\"text-gray-600\">Loading company data...</span>\r\n              </div>\r\n            ) : (\r\n              <>\r\n                {/* Total Listings */}\r\n                <div className=\"mb-4\">\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <p className=\"text-gray-500 text-sm\">Total Listings</p>\r\n                    <p className=\"text-lg font-semibold text-gray-700\">{companyStats.totalListings}</p>\r\n                  </div>\r\n                </div>\r\n                  \r\n                {/* Eligible Jobs */}\r\n                <div className=\"mb-4\">\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <p className=\"text-gray-500 text-sm\">Eligible Jobs</p>\r\n                    <p className=\"text-lg font-semibold text-gray-700\">{companyStats.eligibleJobs}</p>\r\n                  </div>\r\n                </div>\r\n              </>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Right Column - Stats and Files */}\r\n        <div className=\"lg:col-span-3 space-y-6\">\r\n          {/* Files Section */}\r\n          <div className=\"bg-white rounded-lg p-5 shadow-sm\">\r\n            <h2 className=\"text-xl font-semibold mb-4 text-gray-800\">My Files</h2>\r\n            \r\n            <div \r\n              className=\"flex items-center p-2 mb-3 cursor-pointer hover:bg-gray-50 rounded-lg transition-colors\"\r\n              onClick={() => setIsResumeModalOpen(true)}\r\n            >\r\n              <div className=\"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-3\">\r\n                <div className=\"text-blue-600\">\r\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z\" />\r\n                  </svg>\r\n                </div>\r\n              </div>\r\n              <div className=\"flex-grow\">\r\n                <h3 className=\"font-medium text-gray-700\">Resumes</h3>\r\n                <p className=\"text-sm text-gray-500\">\r\n                  {resumeCount > 0 \r\n                    ? `${resumeCount} resume${resumeCount > 1 ? 's' : ''} uploaded` + \r\n                      (lastResumeUpdate ? ` • Last updated ${new Date(lastResumeUpdate).toLocaleDateString()}` : '')\r\n                    : 'No resumes uploaded'\r\n                  }\r\n                </p>\r\n              </div>\r\n              <div className=\"bg-green-50 px-3 py-1 rounded-full\">\r\n                <span className=\"text-green-600 font-medium\">{resumeCount}</span>\r\n              </div>\r\n            </div>\r\n            \r\n            <div \r\n              className=\"flex items-center p-2 cursor-pointer hover:bg-gray-50 rounded-lg transition-colors\"\r\n              onClick={() => setIsDocumentsModalOpen(true)}\r\n            >\r\n              <div className=\"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-3\">\r\n                <div className=\"text-blue-600\">\r\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\r\n                  </svg>\r\n                </div>\r\n              </div>\r\n              <div className=\"flex-grow\">\r\n                <h3 className=\"font-medium text-gray-700\">Documents</h3>\r\n                <p className=\"text-sm text-gray-500\">Academic certificates and marksheets</p>\r\n              </div>\r\n              <div className=\"bg-green-50 px-3 py-1 rounded-full\">\r\n                <span className=\"text-green-600 font-medium\">\r\n                  {getDocumentCount()}\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          {/* Address Section */}\r\n          <div className=\"bg-white rounded-lg p-5 shadow-sm\">\r\n            <div className=\"flex justify-between items-center mb-4\">\r\n              <h2 className=\"text-xl font-semibold text-gray-800\">CURRENT ADDRESS</h2>\r\n            </div>\r\n\r\n            <div className=\"space-y-3 text-sm\">\r\n              <div className=\"flex\">\r\n                <p className=\"text-gray-500 w-20\">City</p>\r\n                <p className=\"font-medium text-gray-700\">: {isEditing ? (\r\n                  <input\r\n                    type=\"text\"\r\n                    value={editedStudent?.city || ''}\r\n                    onChange={(e) => handleInputChange('city', e.target.value)}\r\n                    className=\"flex-1 p-1 border rounded text-sm\"\r\n                    placeholder=\"City name\"\r\n                  />\r\n                ) : (editedStudent?.city || '-')}</p>\r\n              </div>\r\n              <div className=\"flex\">\r\n                <p className=\"text-gray-500 w-20\">District</p>\r\n                <p className=\"font-medium text-gray-700\">: {isEditing ? (\r\n                  <input\r\n                    type=\"text\"\r\n                    value={editedStudent?.district || ''}\r\n                    onChange={(e) => handleInputChange('district', e.target.value)}\r\n                    className=\"flex-1 p-1 border rounded text-sm\"\r\n                    placeholder=\"District name\"\r\n                  />\r\n                ) : (editedStudent?.district || '-')}</p>\r\n              </div>\r\n              <div className=\"flex items-center\">\r\n                <p className=\"text-gray-500 w-20\">State</p>\r\n                <span className=\"mr-2\">:</span>\r\n                {isEditing ? (\r\n                  <input\r\n                    type=\"text\"\r\n                    value={editedStudent?.state || ''}\r\n                    onChange={(e) => handleInputChange('state', e.target.value)}\r\n                    className=\"flex-1 p-2 border rounded text-sm\"\r\n                    placeholder=\"State name\"\r\n                  />\r\n                ) : (\r\n                  <p className=\"font-medium text-gray-700\">{editedStudent?.state || '-'}</p>\r\n                )}\r\n              </div>\r\n              <div className=\"flex items-center\">\r\n                <p className=\"text-gray-500 w-20\">Pin Code</p>\r\n                <span className=\"mr-2\">:</span>\r\n                {isEditing ? (\r\n                  <input\r\n                    type=\"text\"\r\n                    value={editedStudent?.pincode || ''}\r\n                    onChange={(e) => handleInputChange('pincode', e.target.value)}\r\n                    className=\"flex-1 p-2 border rounded text-sm\"\r\n                    placeholder=\"Pin code\"\r\n                  />\r\n                ) : (\r\n                  <p className=\"font-medium text-gray-700\">{editedStudent?.pincode || '-'}</p>\r\n                )}\r\n              </div>\r\n              <div className=\"flex items-center\">\r\n                <p className=\"text-gray-500 w-20\">Country</p>\r\n                <span className=\"mr-2\">:</span>\r\n                {isEditing ? (\r\n                  <input\r\n                    type=\"text\"\r\n                    value={editedStudent?.country || ''}\r\n                    onChange={(e) => handleInputChange('country', e.target.value)}\r\n                    className=\"flex-1 p-2 border rounded text-sm\"\r\n                    placeholder=\"Country name\"\r\n                  />\r\n                ) : (\r\n                  <p className=\"font-medium text-gray-700\">{editedStudent?.country || '-'}</p>\r\n                )}\r\n              </div>\r\n              <div className=\"flex items-start\">\r\n                <p className=\"text-gray-500 w-20 mt-2\">Address</p>\r\n                <span className=\"mr-2 mt-2\">:</span>\r\n                {isEditing ? (\r\n                  <textarea\r\n                    value={editedStudent?.address || ''}\r\n                    onChange={(e) => handleInputChange('address', e.target.value)}\r\n                    className=\"flex-1 p-2 border rounded text-sm\"\r\n                    rows={3}\r\n                    placeholder=\"Full address\"\r\n                  />\r\n                ) : (\r\n                  <p className=\"font-medium text-gray-700 flex-1\">{editedStudent?.address || '-'}</p>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      \r\n      {/* Resume Modal */}\r\n      <ResumeModal\r\n        isOpen={isResumeModalOpen}\r\n        onClose={() => setIsResumeModalOpen(false)}\r\n        resume={profile?.resume_url || profile?.resume}\r\n        onUpload={handleResumeUpload}\r\n        onDelete={handleResumeDelete}\r\n        studentId={selectedStudent?.id}\r\n        isAdminMode={true}\r\n      />\r\n\r\n      {/* Documents Modal */}\r\n      <DocumentsModal\r\n        isOpen={isDocumentsModalOpen}\r\n        onClose={() => setIsDocumentsModalOpen(false)}\r\n        documents={{\r\n          tenth: profile?.tenth_certificate_url || profile?.tenth_certificate,\r\n          twelfth: profile?.twelfth_certificate_url || profile?.twelfth_certificate,\r\n          semesterMarksheets: semesterMarksheets\r\n        }}\r\n        onUploadCertificate={(file, type) => studentsAPI.adminUploadCertificate(selectedStudent.id, file, type)}\r\n        onUploadMarksheet={(file, semester, cgpa) => studentsAPI.adminUploadSemesterMarksheet(selectedStudent.id, file, semester, cgpa)}\r\n        onDeleteCertificate={(type) => studentsAPI.adminDeleteCertificate(selectedStudent.id, type)}\r\n        onDeleteMarksheet={(semester) => studentsAPI.adminDeleteMarksheet(selectedStudent.id, semester)}\r\n        onUploadSuccess={() => {\r\n          // Refresh profile data after successful upload\r\n          if (selectedStudent) {\r\n            fetchAdditionalDetails(selectedStudent.id);\r\n          }\r\n        }}\r\n      />\r\n\r\n      {/* Freeze Modal */}\r\n      <FreezeModal\r\n        isOpen={isFreezeModalOpen}\r\n        onClose={() => setIsFreezeModalOpen(false)}\r\n        onFreeze={handleFreeze}\r\n        onUnfreeze={handleUnfreeze}\r\n        studentName={editedStudent?.name || selectedStudent?.name || 'Unknown Student'}\r\n        currentFreezeStatus={freezeStatus}\r\n        currentFreezeData={freezeData}\r\n      />\r\n    </div>\r\n  </div>\r\n);\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;;AAUe,SAAS,eAAe,EACrC,eAAe,EACf,aAAa,EACb,SAAS,EACT,gBAAgB,EAChB,UAAU,EACV,UAAU,EACV,YAAY,EACZ,iBAAiB,EACjB,iBAAiB,EAClB;;IACC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC/D,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C,SAAS;QACT,eAAe;QACf,cAAc;IAChB;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,yDAAyD;IACzD,MAAM,kBAAkB,CAAC;QACvB,OAAO,YACA,OAAO,aAAa,YACpB,SAAS,IAAI,OAAO,MACpB,aAAa,UACb,aAAa;IACtB;IAEA,kCAAkC;IAClC,MAAM,mBAAmB;QACvB,MAAM,aAAa,gBAAgB,SAAS,qBAAqB,IAAI;QACrE,MAAM,eAAe,gBAAgB,SAAS,uBAAuB,IAAI;QACzE,MAAM,gBAAgB,qBACpB,mBAAmB,MAAM,CAAC,CAAA,QAAS,SAAS,gBAAgB,MAAM,aAAa,GAAG,MAAM,GAAG;QAE7F,OAAO,aAAa,eAAe;IACrC;IAEA,qFAAqF;IACrF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,iBAAiB;gBACnB,WAAW;gBAEX,gDAAgD;gBAChD,uBAAuB,gBAAgB,EAAE;gBAEzC,2BAA2B;gBAC3B,kBAAkB,gBAAgB,EAAE;gBAEpC,2BAA2B;gBAC3B,gBAAgB,gBAAgB,EAAE;gBAElC,sBAAsB;gBACtB,kBAAkB,gBAAgB,EAAE;YACtC;QACF;mCAAG;QAAC;KAAgB;IAEpB,qCAAqC;IACrC,MAAM,yBAAyB,OAAO;QACpC,IAAI,CAAC,WAAW;QAEhB,IAAI;YACF,WAAW;YACX,+DAA+D;YAC/D,MAAM,UAAU,MAAM,yHAAA,CAAA,cAAW,CAAC,UAAU,CAAC;YAC7C,IAAI,SAAS;gBACX,uEAAuE;gBACvE,WAAW;gBAEX,6BAA6B;gBAC7B,IAAI,QAAQ,mBAAmB,EAAE;oBAC/B,sBAAsB,QAAQ,mBAAmB;gBACnD;YACF;YACA,WAAW;QACb,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,mCAAmC;YACjD,WAAW;QACb;IACF;IAEA,2CAA2C;IAC3C,MAAM,oBAAoB,OAAO;QAC/B,IAAI,CAAC,WAAW;QAEhB,IAAI;YACF,gBAAgB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;gBAAK,CAAC;YAEnD,qDAAqD;YACrD,qEAAqE;YAErE,6BAA6B;YAC7B,MAAM,YAAY;gBAChB,SAAS;gBACT,eAAe;gBACf,cAAc;YAChB;YAEA,gBAAgB;QAClB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,gBAAgB;gBACd,SAAS;gBACT,eAAe;gBACf,cAAc;YAChB;QACF;IACF;IAEA,2CAA2C;IAC3C,MAAM,kBAAkB,OAAO;QAC7B,IAAI,CAAC,WAAW;QAEhB,IAAI;YACF,kCAAkC;YAClC,MAAM,cAAc,MAAM,yHAAA,CAAA,cAAW,CAAC,eAAe,CAAC;YAEtD,IAAI,MAAM,OAAO,CAAC,cAAc;gBAC9B,eAAe,YAAY,MAAM;gBAEjC,mCAAmC;gBACnC,IAAI,YAAY,MAAM,GAAG,GAAG;oBAC1B,MAAM,eAAe,YAAY,MAAM,CAAC,CAAC,QAAQ;wBAC/C,MAAM,cAAc,IAAI,KAAK,QAAQ,WAAW;wBAChD,MAAM,aAAa,IAAI,KAAK,OAAO,WAAW;wBAC9C,OAAO,cAAc,aAAa,UAAU;oBAC9C;oBACA,oBAAoB,aAAa,WAAW;gBAC9C,OAAO;oBACL,oBAAoB;gBACtB;YACF,OAAO;gBACL,eAAe;gBACf,oBAAoB;YACtB;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,eAAe;YACf,oBAAoB;QACtB;IACF;IAEA,sCAAsC;IACtC,MAAM,oBAAoB,OAAO;QAC/B,IAAI,CAAC,WAAW;QAEhB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,4CAA4C,EAAE,UAAU,QAAQ,CAAC,EAAE;gBAC/F,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,WAAW;oBAC3D,gBAAgB;gBAClB;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,gBAAgB,KAAK,aAAa;gBAClC,cAAc;YAChB,OAAO;gBACL,gBAAgB;gBAChB,cAAc;YAChB;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,gBAAgB;YAChB,cAAc;QAChB;IACF;IAEA,iCAAiC;IACjC,MAAM,iBAAiB;QACrB,OAAO,eAAe,OAAO,SAAS,OAAO;IAC/C;IAEA,iDAAiD;IACjD,MAAM,sBAAsB,CAAC;QAC3B,IAAI,CAAC,QAAQ,SAAS,OAAO,OAAO;QACpC,MAAM,cAAc,WAAW;QAC/B,IAAI,MAAM,cAAc,OAAO;QAC/B,OAAO,CAAC,cAAc,GAAG,EAAE,OAAO,CAAC,KAAK;IAC1C;IAEA,iCAAiC;IACjC,MAAM,wBAAwB,CAAC,aAAa;QAC1C,IAAI,eAAe,aAAa;YAC9B,OAAO,GAAG,YAAY,GAAG,EAAE,aAAa;QAC1C;QACA,OAAO;IACT;IAEA,6BAA6B;IAC7B,MAAM,eAAe;QACnB,IAAI,SAAS,gBAAgB,SAAS,cAAc;YAClD,OAAO,GAAG,QAAQ,YAAY,CAAC,GAAG,EAAE,QAAQ,YAAY,EAAE;QAC5D;QACA,IAAI,eAAe,gBAAgB,eAAe,cAAc;YAC9D,OAAO,GAAG,cAAc,YAAY,CAAC,GAAG,EAAE,cAAc,YAAY,EAAE;QACxE;QACA,IAAI,SAAS,QAAQ,eAAe,MAAM;YACxC,OAAO,SAAS,QAAQ,eAAe;QACzC;QACA,OAAO;IACT;IAEA,oBAAoB;IACpB,MAAM,kBAAkB,CAAC;QACvB,0DAA0D;QAC1D,IAAI,sBAAsB,mBAAmB,MAAM,GAAG,GAAG;YACvD,OAAO,kBAAkB,CAAC,WAAW,EAAE,EAAE,QAAQ;QACnD;QAEA,oDAAoD;QACpD,IAAI,eAAe,kBAAkB,cAAc,cAAc,CAAC,MAAM,GAAG,GAAG;YAC5E,MAAM,eAAe,cAAc,cAAc,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;YAC3E,OAAO,cAAc,QAAQ;QAC/B;QAEA,iDAAiD;QACjD,IAAI,SAAS,kBAAkB,QAAQ,cAAc,CAAC,MAAM,GAAG,GAAG;YAChE,MAAM,eAAe,QAAQ,cAAc,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;YACrE,OAAO,cAAc,QAAQ;QAC/B;QAEA,OAAO;IACT;IAEA,8BAA8B;IAC9B,MAAM,2BAA2B,CAAC,UAAU;QAC1C,MAAM,uBAAuB,eAAe,kBAAkB,EAAE;QAChE,MAAM,uBAAuB;eAAI;SAAqB;QAEtD,gDAAgD;QAChD,MAAM,gBAAgB,qBAAqB,SAAS,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;QAEzE,IAAI,iBAAiB,GAAG;YACtB,2BAA2B;YAC3B,oBAAoB,CAAC,cAAc,GAAG;gBACpC,GAAG,oBAAoB,CAAC,cAAc;gBACtC,MAAM;YACR;QACF,OAAO;YACL,wBAAwB;YACxB,qBAAqB,IAAI,CAAC;gBACxB,UAAU;gBACV,MAAM;YACR;QACF;QAEA,0BAA0B;QAC1B,qBAAqB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;QAE3D,4BAA4B;QAC5B,kBAAkB,kBAAkB;IACtC;IAEA,uBAAuB;IACvB,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,IAAI,CAAC,iBAAiB;gBACpB,MAAM;gBACN;YACF;YAEA,4CAA4C;YAC5C,MAAM,yHAAA,CAAA,cAAW,CAAC,iBAAiB,CAAC,gBAAgB,EAAE,EAAE,MAAM,KAAK,IAAI,EAAE;YAEzE,MAAM;YAEN,mCAAmC;YACnC,gBAAgB,gBAAgB,EAAE;QACpC,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM,CAAC,yBAAyB,EAAE,IAAI,QAAQ,EAAE,MAAM,SAAS,IAAI,OAAO,IAAI,iBAAiB;QACjG;IACF;IAEA,uBAAuB;IACvB,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,MAAM,yHAAA,CAAA,cAAW,CAAC,YAAY,CAAC;YAC/B,mCAAmC;YACnC,IAAI,iBAAiB;gBACnB,gBAAgB,gBAAgB,EAAE;YACpC;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA,wBAAwB;IACxB,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,4CAA4C,EAAE,gBAAgB,EAAE,CAAC,QAAQ,CAAC,EAAE;gBACxG,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,WAAW;oBAC3D,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,SAAS,MAAM,SAAS,IAAI;gBAClC,MAAM,OAAO,OAAO;gBACpB,wBAAwB;gBACxB,kBAAkB,gBAAgB,EAAE;YACtC,OAAO;gBACL,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,IAAI,MAAM,MAAM,MAAM,IAAI;YAClC;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR;IACF;IAEA,0BAA0B;IAC1B,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,4CAA4C,EAAE,gBAAgB,EAAE,CAAC,QAAQ,CAAC,EAAE;gBACxG,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,WAAW;oBAC3D,gBAAgB;gBAClB;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,SAAS,MAAM,SAAS,IAAI;gBAClC,MAAM,OAAO,OAAO;gBACpB,wBAAwB;gBACxB,kBAAkB,gBAAgB,EAAE;YACtC,OAAO;gBACL,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,IAAI,MAAM,MAAM,MAAM,IAAI;YAClC;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,iJAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;8BACrB,6LAAC;8BAAK;;;;;;;;;;;;IAGZ;IAEA,IAAI,CAAC,mBAAmB,CAAC,eAAe;QACtC,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAE,WAAU;8BAAe;;;;;;8BAC5B,6LAAC;oBACC,SAAS;oBACT,WAAU;8BACX;;;;;;;;;;;;IAKP;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,6LAAC,mNAAA,CAAA,YAAS;gCAAC,MAAM;;;;;;0CACjB,6LAAC;0CAAK;;;;;;;;;;;;kCAGR,6LAAC;wBAAI,WAAU;kCACZ,0BACC;;8CACE,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,MAAM;;;;;;0DACZ,6LAAC;0DAAK;;;;;;;;;;;;;;;;;8CAGV,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+LAAA,CAAA,IAAC;gDAAC,MAAM;;;;;;0DACT,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;yDAKZ;;8CACE,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,8MAAA,CAAA,OAAI;gDAAC,MAAM;;;;;;0DACZ,6LAAC;0DAAK;;;;;;;;;;;;;;;;;8CAGV,6LAAC;oCACC,SAAS,IAAM,qBAAqB;oCACpC,WAAW,CAAC,+DAA+D,EACzE,iBAAiB,SACb,2CACA,iBAAiB,aACjB,iDACA,gDACJ;8CAED,iBAAiB,uBAChB;;0DACE,6LAAC,qMAAA,CAAA,OAAI;gDAAC,MAAM;;;;;;0DACZ,6LAAC;0DAAK;;;;;;;qEAGR;;0DACE,6LAAC,+MAAA,CAAA,SAAM;gDAAC,MAAM;;;;;;0DACd,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;0BASpB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDACb,eAAe,OAAO,cAAc,IAAI,CAAC,EAAE,CAAC,WAAW,KAAK;;;;;;;;;;;;;;;;0CAKnE,6LAAC;gCAAG,WAAU;0CACX,0BACC,6LAAC;oCACC,MAAK;oCACL,OAAO,eAAe,QAAQ;oCAC9B,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;oCACzD,WAAU;;;;;2CAGZ,eAAe,QAAQ;;;;;;0CAI3B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAClC,6LAAC;gDAAE,WAAU;;oDAA4B;oDAAG,0BAC1C,6LAAC;wDACC,MAAK;wDACL,OAAO,eAAe,cAAc;wDACpC,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC/D,WAAU;;;;;+DAET,eAAe,cAAc;;;;;;;;;;;;;kDAEpC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAClC,6LAAC;gDAAE,WAAU;;oDAA4B;oDAAG,0BAC1C,6LAAC;wDACC,OAAO,eAAe,cAAc;wDACpC,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC/D,WAAU;;0EAEV,6LAAC;gEAAO,OAAM;0EAAG;;;;;;4DAChB,kBAAkB,GAAG,CAAC,CAAA,uBACrB,6LAAC;oEAA0B,OAAO,OAAO,KAAK;8EAC3C,OAAO,KAAK;mEADF,OAAO,KAAK;;;;;;;;;;+DAK1B,eAAe,cAAc;;;;;;;;;;;;;kDAEpC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAClC,6LAAC;gDAAE,WAAU;;oDAA4B;oDAAG,0BAC1C,6LAAC;wDACC,MAAK;wDACL,OAAO,eAAe,gBAAgB;wDACtC,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;wDACjE,WAAU;;;;;+DAET,eAAe,gBAAgB;;;;;;;;;;;;;kDAEtC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAClC,6LAAC;gDAAE,WAAU;;oDAA4B;oDAAG,0BAC1C,6LAAC;wDACC,OAAO,eAAe,UAAU;wDAChC,UAAU,CAAC,IAAM,kBAAkB,UAAU,EAAE,MAAM,CAAC,KAAK;wDAC3D,WAAU;;0EAEV,6LAAC;gEAAO,OAAM;0EAAG;;;;;;0EACjB,6LAAC;gEAAO,OAAM;0EAAO;;;;;;0EACrB,6LAAC;gEAAO,OAAM;0EAAS;;;;;;0EACvB,6LAAC;gEAAO,OAAM;0EAAQ;;;;;;;;;;;+DAErB,eAAe,UAAU;;;;;;;;;;;;;kDAEhC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAClC,6LAAC;gDAAE,WAAU;;oDAA4B;oDAAG,0BAC1C,6LAAC;wDACC,MAAK;wDACL,OAAO,eAAe,eAAe;wDACrC,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;wDAChE,WAAU;;;;;+DAET,eAAe,eAAe;;;;;;;;;;;;;kDAErC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAClC,6LAAC;gDAAE,WAAU;;oDAA4B;oDAAG,0BAC1C,6LAAC;wDACC,MAAK;wDACL,OAAO,eAAe,SAAS;wDAC/B,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;wDAC1D,WAAU;;;;;+DAET,eAAe,SAAS;;;;;;;;;;;;;kDAE/B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAClC,6LAAC;gDAAE,WAAU;;oDAA4B;oDAAG,0BAC1C,6LAAC;wDACC,MAAK;wDACL,OAAO,eAAe,SAAS;wDAC/B,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;wDAC1D,WAAU;;;;;+DAET,eAAe,SAAS;;;;;;;;;;;;;kDAE/B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAClC,6LAAC;gDAAE,WAAU;;oDAA4B;oDAAG,0BAC1C,6LAAC;wDACC,MAAK;wDACL,OAAO,eAAe,gBAAgB;wDACtC,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;wDACjE,WAAU;;;;;+DAET,eAAe,gBAAgB;;;;;;;;;;;;;kDAEtC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAClC,6LAAC;gDAAE,WAAU;;oDAA4B;oDAAG,kBAAkB;;;;;;;;;;;;;;;;;;;0CAKlE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;oCACxD,0BACC,6LAAC;wCACC,OAAO,MAAM,OAAO,CAAC,eAAe,UAAU,cAAc,MAAM,CAAC,IAAI,CAAC,QAAQ,eAAe,UAAU;wCACzG,UAAU,CAAC,IAAM,kBAAkB,UAAU,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;wCACtF,WAAU;wCACV,MAAM;wCACN,aAAY;;;;;6DAGd,6LAAC;wCAAI,WAAU;kDACZ,CAAC;4CACA,MAAM,SAAS,eAAe,UAAU,SAAS,UAAU,iBAAiB;4CAE5E,IAAI,MAAM,OAAO,CAAC,WAAW,OAAO,MAAM,GAAG,GAAG;gDAC9C,OAAO,OAAO,GAAG,CAAC,CAAC,OAAO,sBACxB,6LAAC;wDAEC,WAAU;kEAET;uDAHI;;;;;4CAMX,OAAO,IAAI,OAAO,WAAW,YAAY,OAAO,IAAI,IAAI;gDACtD,OAAO,OAAO,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,OAAO,sBACnC,6LAAC;wDAEC,WAAU;kEAET,MAAM,IAAI;uDAHN;;;;;4CAMX,OAAO;gDACL,qBAAO,6LAAC;oDAAE,WAAU;8DAAgB;;;;;;4CACtC;wCACF,CAAC;;;;;;;;;;;;;;;;;;kCAOT,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAsC;;;;;;kEACpD,6LAAC;wDAAI,WAAU;kEACZ,0BACC;;8EACE,6LAAC;oEACC,MAAK;oEACL,OAAO,eAAe,OAAO;oEAC7B,UAAU,CAAC,IAAM,kBAAkB,OAAO,EAAE,MAAM,CAAC,KAAK;oEACxD,WAAU;oEACV,aAAY;;;;;;8EAEd,6LAAC;oEAAK,WAAU;8EAA6B;;;;;;8EAC7C,6LAAC;oEAAK,WAAU;8EAAsB,oBAAoB,eAAe;;;;;;;yFAG3E;;8EACE,6LAAC;oEAAK,WAAU;8EAA6B;;;;;;8EAC7C,6LAAC;oEAAK,WAAU;8EAA6B;;;;;;8EAC7C,6LAAC;oEAAK,WAAU;8EAAsB,oBAAoB;;;;;;;;;;;;;;;;;;;0DAKlE,6LAAC;gDAAI,WAAU;0DACZ,0BACC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,MAAK;4DACL,OAAO,eAAe,gBAAgB;4DACtC,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DACjE,WAAU;4DACV,aAAY;;;;;;sEAEd,6LAAC;sEAAK;;;;;;sEACN,6LAAC;4DACC,MAAK;4DACL,OAAO,eAAe,gBAAgB;4DACtC,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DACjE,WAAU;4DACV,aAAY;;;;;;;;;;;2DAIhB,sBAAsB,eAAe,cAAc,eAAe;;;;;;;;;;;;kDAMxE,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAM,WAAU;;kEACf,6LAAC;kEACC,cAAA,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;oEAAG,WAAU;8EAAuE;;;;;;gEACpF;oEAAC;oEAAG;oEAAG;oEAAG;oEAAG;oEAAG;oEAAG;oEAAG;iEAAE,CAAC,GAAG,CAAC,CAAA,oBAC5B,6LAAC;wEAAa,WAAU;kFAAwE;uEAAvF;;;;;;;;;;;;;;;;kEAIf,6LAAC;kEACC,cAAA,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAqE;;;;;;gEAClF;oEAAC;oEAAG;oEAAG;oEAAG;oEAAG;oEAAG;oEAAG;oEAAG;iEAAE,CAAC,GAAG,CAAC,CAAA,oBAC5B,6LAAC;wEAAa,WAAU;kFACrB,0BACC,6LAAC;4EACC,MAAK;4EACL,OAAO,gBAAgB,SAAS,MAAM,gBAAgB,OAAO;4EAC7D,UAAU,CAAC,IAAM,yBAAyB,KAAK,EAAE,MAAM,CAAC,KAAK;4EAC7D,WAAU;4EACV,aAAY;;;;;mFAGd,gBAAgB;uEAVX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAmBrB,6LAAC;wCAAG,WAAU;;;;;;kDAGd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAAsC;;;;;;0EACpD,6LAAC;gEAAI,WAAU;0EACZ,0BACC;;sFACE,6LAAC;4EACC,MAAK;4EACL,OAAO,eAAe,gBAAgB;4EACtC,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;4EACjE,WAAU;4EACV,aAAY;;;;;;sFAEd,6LAAC;4EAAK,WAAU;sFAA6B;;;;;;sFAC7C,6LAAC;4EACC,MAAK;4EACL,OAAO,eAAe,sBAAsB;4EAC5C,UAAU,CAAC,IAAM,kBAAkB,sBAAsB,EAAE,MAAM,CAAC,KAAK;4EACvE,WAAU;4EACV,aAAY;;;;;;;iGAIhB;;sFACE,6LAAC;4EAAK,WAAU;sFAA6B,eAAe,gBAAgB;;;;;;sFAC5E,6LAAC;4EAAK,WAAU;sFAA6B;;;;;;sFAC7C,6LAAC;4EAAK,WAAU;sFAAsB,eAAe,sBAAsB;;;;;;;;;;;;;;;;;;;kEAKnF,6LAAC;wDAAI,WAAU;kEACZ,0BACC,6LAAC;4DACC,MAAK;4DACL,OAAO,eAAe,2BAA2B;4DACjD,UAAU,CAAC,IAAM,kBAAkB,2BAA2B,EAAE,MAAM,CAAC,KAAK;4DAC5E,WAAU;4DACV,aAAY;;;;;mEAGd,eAAe,0BACX,GAAG,SAAS,cAAc,uBAAuB,IAAI,EAAE,GAAG,EAAE,cAAc,uBAAuB,EAAE,GACnG;;;;;;;;;;;;0DAIV,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFAAkC;;;;;;wEAC9C,0BACC,6LAAC;4EACC,MAAK;4EACL,OAAO,eAAe,kBAAkB;4EACxC,UAAU,CAAC,IAAM,kBAAkB,kBAAkB,EAAE,MAAM,CAAC,KAAK;4EACnE,WAAU;4EACV,aAAY;;;;;iGAGd,6LAAC;4EAAE,WAAU;sFAA6B,eAAe,kBAAkB;;;;;;;;;;;;8EAG/E,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFAAkC;;;;;;wEAC9C,0BACC,6LAAC;4EACC,MAAK;4EACL,OAAO,eAAe,iBAAiB;4EACvC,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;4EAClE,WAAU;4EACV,aAAY;;;;;iGAGd,6LAAC;4EAAE,WAAU;sFAA6B,eAAe,iBAAiB;;;;;;;;;;;;;;;;;;sEAIhF,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFAAkC;;;;;;wEAC9C,0BACC,6LAAC;4EACC,MAAK;4EACL,OAAO,eAAe,oBAAoB;4EAC1C,UAAU,CAAC,IAAM,kBAAkB,oBAAoB,EAAE,MAAM,CAAC,KAAK;4EACrE,WAAU;4EACV,aAAY;;;;;iGAGd,6LAAC;4EAAE,WAAU;sFAA6B,eAAe,oBAAoB;;;;;;;;;;;;8EAGjF,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFAAkC;;;;;;wEAC9C,0BACC,6LAAC;4EACC,MAAK;4EACL,OAAO,eAAe,0BAA0B;4EAChD,UAAU,CAAC,IAAM,kBAAkB,0BAA0B,EAAE,MAAM,CAAC,KAAK;4EAC3E,WAAU;4EACV,aAAY;;;;;iGAGd,6LAAC;4EAAE,WAAU;sFAA6B,eAAe,0BAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQ/F,6LAAC;wCAAG,WAAU;;;;;;kDAGd,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAAsC;;;;;;0EACpD,6LAAC;gEAAI,WAAU;0EACZ,0BACC;;sFACE,6LAAC;4EACC,MAAK;4EACL,OAAO,eAAe,cAAc;4EACpC,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;4EAC/D,WAAU;4EACV,aAAY;;;;;;sFAEd,6LAAC;4EAAK,WAAU;sFAA6B;;;;;;sFAC7C,6LAAC;4EACC,MAAK;4EACL,OAAO,eAAe,oBAAoB;4EAC1C,UAAU,CAAC,IAAM,kBAAkB,oBAAoB,EAAE,MAAM,CAAC,KAAK;4EACrE,WAAU;4EACV,aAAY;;;;;;;iGAIhB;;sFACE,6LAAC;4EAAK,WAAU;sFAA6B,eAAe,cAAc;;;;;;sFAC1E,6LAAC;4EAAK,WAAU;sFAA6B;;;;;;sFAC7C,6LAAC;4EAAK,WAAU;sFAAsB,eAAe,oBAAoB;;;;;;;;;;;;;;;;;;;kEAKjF,6LAAC;wDAAI,WAAU;kEACZ,0BACC,6LAAC;4DACC,MAAK;4DACL,OAAO,eAAe,yBAAyB;4DAC/C,UAAU,CAAC,IAAM,kBAAkB,yBAAyB,EAAE,MAAM,CAAC,KAAK;4DAC1E,WAAU;4DACV,aAAY;;;;;mEAGd,eAAe,wBACX,GAAG,SAAS,cAAc,qBAAqB,IAAI,EAAE,GAAG,EAAE,cAAc,qBAAqB,EAAE,GAC/F;;;;;;;;;;;;0DAKV,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFAAkC;;;;;;wEAC9C,0BACC,6LAAC;4EACC,MAAK;4EACL,OAAO,eAAe,gBAAgB;4EACtC,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;4EACjE,WAAU;4EACV,aAAY;;;;;iGAGd,6LAAC;4EAAE,WAAU;sFAA6B,eAAe,gBAAgB;;;;;;;;;;;;8EAG7E,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFAAkC;;;;;;wEAC9C,0BACC,6LAAC;4EACC,MAAK;4EACL,OAAO,eAAe,eAAe;4EACrC,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;4EAChE,WAAU;4EACV,aAAY;;;;;iGAGd,6LAAC;4EAAE,WAAU;sFAA6B,eAAe,eAAe;;;;;;;;;;;;;;;;;;sEAI9E,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFAAkC;;;;;;wEAC9C,0BACC,6LAAC;4EACC,MAAK;4EACL,OAAO,eAAe,kBAAkB;4EACxC,UAAU,CAAC,IAAM,kBAAkB,kBAAkB,EAAE,MAAM,CAAC,KAAK;4EACnE,WAAU;4EACV,aAAY;;;;;iGAGd,6LAAC;4EAAE,WAAU;sFAA6B,eAAe,kBAAkB;;;;;;;;;;;;8EAG/E,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFAAkC;;;;;;wEAC9C,0BACC,6LAAC;4EACC,MAAK;4EACL,OAAO,eAAe,wBAAwB;4EAC9C,UAAU,CAAC,IAAM,kBAAkB,wBAAwB,EAAE,MAAM,CAAC,KAAK;4EACzE,WAAU;4EACV,aAAY;;;;;iGAGd,6LAAC;4EAAE,WAAU;sFAA6B,eAAe,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAU/F,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;oCAExD,aAAa,OAAO,iBACnB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;6DAGlC;;0DAEE,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,6LAAC;4DAAE,WAAU;sEAAuC,aAAa,aAAa;;;;;;;;;;;;;;;;;0DAKlF,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,6LAAC;4DAAE,WAAU;sEAAuC,aAAa,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASzF,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDAEzD,6LAAC;wCACC,WAAU;wCACV,SAAS,IAAM,qBAAqB;;0DAEpC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,OAAM;wDAA6B,WAAU;wDAAU,MAAK;wDAAO,SAAQ;wDAAY,QAAO;kEACjG,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;;;;;;0DAI3E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA4B;;;;;;kEAC1C,6LAAC;wDAAE,WAAU;kEACV,cAAc,IACX,GAAG,YAAY,OAAO,EAAE,cAAc,IAAI,MAAM,GAAG,SAAS,CAAC,GAC7D,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,IAAI,KAAK,kBAAkB,kBAAkB,IAAI,GAAG,EAAE,IAC7F;;;;;;;;;;;;0DAIR,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAA8B;;;;;;;;;;;;;;;;;kDAIlD,6LAAC;wCACC,WAAU;wCACV,SAAS,IAAM,wBAAwB;;0DAEvC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,OAAM;wDAA6B,WAAU;wDAAU,MAAK;wDAAO,SAAQ;wDAAY,QAAO;kEACjG,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;;;;;;0DAI3E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA4B;;;;;;kEAC1C,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DACb;;;;;;;;;;;;;;;;;;;;;;;0CAOT,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAG,WAAU;sDAAsC;;;;;;;;;;;kDAGtD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAqB;;;;;;kEAClC,6LAAC;wDAAE,WAAU;;4DAA4B;4DAAG,0BAC1C,6LAAC;gEACC,MAAK;gEACL,OAAO,eAAe,QAAQ;gEAC9B,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;gEACzD,WAAU;gEACV,aAAY;;;;;uEAEX,eAAe,QAAQ;;;;;;;;;;;;;0DAE9B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAqB;;;;;;kEAClC,6LAAC;wDAAE,WAAU;;4DAA4B;4DAAG,0BAC1C,6LAAC;gEACC,MAAK;gEACL,OAAO,eAAe,YAAY;gEAClC,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;gEAC7D,WAAU;gEACV,aAAY;;;;;uEAEX,eAAe,YAAY;;;;;;;;;;;;;0DAElC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAqB;;;;;;kEAClC,6LAAC;wDAAK,WAAU;kEAAO;;;;;;oDACtB,0BACC,6LAAC;wDACC,MAAK;wDACL,OAAO,eAAe,SAAS;wDAC/B,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;wDAC1D,WAAU;wDACV,aAAY;;;;;6EAGd,6LAAC;wDAAE,WAAU;kEAA6B,eAAe,SAAS;;;;;;;;;;;;0DAGtE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAqB;;;;;;kEAClC,6LAAC;wDAAK,WAAU;kEAAO;;;;;;oDACtB,0BACC,6LAAC;wDACC,MAAK;wDACL,OAAO,eAAe,WAAW;wDACjC,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;wDAC5D,WAAU;wDACV,aAAY;;;;;6EAGd,6LAAC;wDAAE,WAAU;kEAA6B,eAAe,WAAW;;;;;;;;;;;;0DAGxE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAqB;;;;;;kEAClC,6LAAC;wDAAK,WAAU;kEAAO;;;;;;oDACtB,0BACC,6LAAC;wDACC,MAAK;wDACL,OAAO,eAAe,WAAW;wDACjC,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;wDAC5D,WAAU;wDACV,aAAY;;;;;6EAGd,6LAAC;wDAAE,WAAU;kEAA6B,eAAe,WAAW;;;;;;;;;;;;0DAGxE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAA0B;;;;;;kEACvC,6LAAC;wDAAK,WAAU;kEAAY;;;;;;oDAC3B,0BACC,6LAAC;wDACC,OAAO,eAAe,WAAW;wDACjC,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;wDAC5D,WAAU;wDACV,MAAM;wDACN,aAAY;;;;;6EAGd,6LAAC;wDAAE,WAAU;kEAAoC,eAAe,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQvF,6LAAC,+JAAA,CAAA,UAAW;wBACV,QAAQ;wBACR,SAAS,IAAM,qBAAqB;wBACpC,QAAQ,SAAS,cAAc,SAAS;wBACxC,UAAU;wBACV,UAAU;wBACV,WAAW,iBAAiB;wBAC5B,aAAa;;;;;;kCAIf,6LAAC,kKAAA,CAAA,UAAc;wBACb,QAAQ;wBACR,SAAS,IAAM,wBAAwB;wBACvC,WAAW;4BACT,OAAO,SAAS,yBAAyB,SAAS;4BAClD,SAAS,SAAS,2BAA2B,SAAS;4BACtD,oBAAoB;wBACtB;wBACA,qBAAqB,CAAC,MAAM,OAAS,yHAAA,CAAA,cAAW,CAAC,sBAAsB,CAAC,gBAAgB,EAAE,EAAE,MAAM;wBAClG,mBAAmB,CAAC,MAAM,UAAU,OAAS,yHAAA,CAAA,cAAW,CAAC,4BAA4B,CAAC,gBAAgB,EAAE,EAAE,MAAM,UAAU;wBAC1H,qBAAqB,CAAC,OAAS,yHAAA,CAAA,cAAW,CAAC,sBAAsB,CAAC,gBAAgB,EAAE,EAAE;wBACtF,mBAAmB,CAAC,WAAa,yHAAA,CAAA,cAAW,CAAC,oBAAoB,CAAC,gBAAgB,EAAE,EAAE;wBACtF,iBAAiB;4BACf,+CAA+C;4BAC/C,IAAI,iBAAiB;gCACnB,uBAAuB,gBAAgB,EAAE;4BAC3C;wBACF;;;;;;kCAIF,6LAAC,+JAAA,CAAA,UAAW;wBACV,QAAQ;wBACR,SAAS,IAAM,qBAAqB;wBACpC,UAAU;wBACV,YAAY;wBACZ,aAAa,eAAe,QAAQ,iBAAiB,QAAQ;wBAC7D,qBAAqB;wBACrB,mBAAmB;;;;;;;;;;;;;;;;;;AAK3B;GAnpCwB;KAAA", "debugId": null}}, {"offset": {"line": 7063, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/app/admin/student-management/DepartmentCards.jsx"], "sourcesContent": ["import { <PERSON>raduation<PERSON>ap, BarChart3, Award, TrendingUp, RefreshCw, Users, BookOpen } from \"lucide-react\";\r\nimport { useState, useEffect } from 'react';\r\nimport { studentMetricsAPI } from '../../../api/optimized';\r\n\r\nexport default function DepartmentCards({ departmentOptions, departmentStats, totalStudents, onSelect }) {\r\n  const [analytics, setAnalytics] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [refreshing, setRefreshing] = useState(false);\r\n\r\n  // Fetch analytics data\r\n  const fetchAnalytics = async (forceRefresh = false) => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      \r\n      const result = await studentMetricsAPI.getAllStudentAnalytics(forceRefresh);\r\n      \r\n      if (result.success) {\r\n        setAnalytics(result.data);\r\n        \r\n        // Show warning if we're using fallback data\r\n        if (result.fallback || result.has_errors) {\r\n          const errorMessages = result.data?.errors || [];\r\n          console.warn('Analytics loaded with warnings:', errorMessages);\r\n          setError(`Some data may be incomplete: ${errorMessages.join(', ')}`);\r\n        }\r\n      } else {\r\n        setError(result.error || 'Failed to load analytics');\r\n      }\r\n    } catch (err) {\r\n      console.error('Error fetching analytics:', err);\r\n      setError('Failed to load student analytics');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Refresh analytics\r\n  const handleRefresh = async () => {\r\n    setRefreshing(true);\r\n    try {\r\n      const result = await studentMetricsAPI.refreshAllMetrics();\r\n      if (result.success) {\r\n        await fetchAnalytics(true);\r\n      } else {\r\n        setError(result.error || 'Failed to refresh metrics');\r\n      }\r\n    } catch (err) {\r\n      console.error('Error refreshing metrics:', err);\r\n      setError('Failed to refresh metrics');\r\n    } finally {\r\n      setRefreshing(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchAnalytics();\r\n  }, []);\r\n\r\n  // Get analytics data with fallbacks\r\n  const overview = analytics?.enhanced?.overview || {};\r\n  const departmentData = analytics?.departments?.departments || [];\r\n  const yearData = analytics?.years?.years || [];\r\n  const performanceData = analytics?.performance?.performance_categories || {};\r\n\r\n  // Calculate enhanced stats\r\n  const enhancedStats = {\r\n    totalStudents: overview.total_students || totalStudents || 0,\r\n    activeDepartments: overview.active_departments || departmentStats.length || 0,\r\n    highPerformers: overview.high_performers || 0,\r\n    highPerformerPercentage: overview.high_performer_percentage || 0,\r\n    placementReady: overview.placement_ready || 0,\r\n    averageGPA: departmentData.length > 0 \r\n      ? (departmentData.reduce((sum, dept) => sum + (dept.avg_gpa || 0), 0) / departmentData.length).toFixed(2)\r\n      : '0.00'\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {/* Header with Refresh Button */}\r\n      <div className=\"mb-8 flex justify-between items-center\">\r\n        <div>\r\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">Student Management & Analytics</h1>\r\n          <p className=\"text-gray-600\">Comprehensive overview of student data and performance</p>\r\n        </div>\r\n        <button\r\n          onClick={handleRefresh}\r\n          disabled={refreshing}\r\n          className=\"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors\"\r\n        >\r\n          <RefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />\r\n          {refreshing ? 'Refreshing...' : 'Refresh Data'}\r\n        </button>\r\n      </div>\r\n\r\n      {/* Error Handling */}\r\n      {error && (\r\n        <div className={`mb-6 rounded-lg p-4 ${\r\n          error.includes('incomplete') || error.includes('fallback') \r\n            ? 'bg-yellow-50 border border-yellow-200' \r\n            : 'bg-red-50 border border-red-200'\r\n        }`}>\r\n          <p className={`${\r\n            error.includes('incomplete') || error.includes('fallback')\r\n              ? 'text-yellow-600'\r\n              : 'text-red-600'\r\n          }`}>\r\n            {error.includes('incomplete') || error.includes('fallback') \r\n              ? `⚠️ ${error}` \r\n              : error\r\n            }\r\n          </p>\r\n          <button \r\n            onClick={() => fetchAnalytics()} \r\n            className={`mt-2 px-4 py-2 text-white rounded hover:opacity-90 text-sm ${\r\n              error.includes('incomplete') || error.includes('fallback')\r\n                ? 'bg-yellow-600 hover:bg-yellow-700'\r\n                : 'bg-red-600 hover:bg-red-700'\r\n            }`}\r\n          >\r\n            Retry Load\r\n          </button>\r\n        </div>\r\n      )}\r\n\r\n      {/* Enhanced Summary Stats */}\r\n      <div className=\"mb-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\r\n        <div className=\"flex items-center justify-between mb-6\">\r\n          <h2 className=\"text-lg font-semibold text-gray-800\">Overview Analytics</h2>\r\n          <BarChart3 className=\"w-5 h-5 text-gray-400\" />\r\n        </div>\r\n        \r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\r\n          {/* Total Students */}\r\n          <div className=\"bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <div>\r\n                <div className=\"text-2xl font-bold text-blue-600\">{enhancedStats.totalStudents.toLocaleString()}</div>\r\n                <div className=\"text-sm text-blue-700 font-medium\">Total Students</div>\r\n                <div className=\"text-xs text-blue-600 mt-1\">All registered students</div>\r\n              </div>\r\n              <Users className=\"w-8 h-8 text-blue-600\" />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Active Departments */}\r\n          <div className=\"bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <div>\r\n                <div className=\"text-2xl font-bold text-green-600\">{enhancedStats.activeDepartments}</div>\r\n                <div className=\"text-sm text-green-700 font-medium\">Active Departments</div>\r\n                <div className=\"text-xs text-green-600 mt-1\">Avg GPA: {enhancedStats.averageGPA}</div>\r\n              </div>\r\n              <BookOpen className=\"w-8 h-8 text-green-600\" />\r\n            </div>\r\n          </div>\r\n\r\n          {/* High Performers */}\r\n          <div className=\"bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-4\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <div>\r\n                <div className=\"text-2xl font-bold text-purple-600\">{enhancedStats.highPerformers.toLocaleString()}</div>\r\n                <div className=\"text-sm text-purple-700 font-medium\">High Performers</div>\r\n                <div className=\"text-xs text-purple-600 mt-1\">{enhancedStats.highPerformerPercentage.toFixed(1)}% (GPA ≥ 8.5)</div>\r\n              </div>\r\n              <Award className=\"w-8 h-8 text-purple-600\" />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Placement Ready */}\r\n          <div className=\"bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg p-4\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <div>\r\n                <div className=\"text-2xl font-bold text-orange-600\">{enhancedStats.placementReady.toLocaleString()}</div>\r\n                <div className=\"text-sm text-orange-700 font-medium\">Placement Ready</div>\r\n                <div className=\"text-xs text-orange-600 mt-1\">Current year eligible</div>\r\n              </div>\r\n              <GraduationCap className=\"w-8 h-8 text-orange-600\" />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Performance Analytics */}\r\n      {/* {!loading && Object.keys(performanceData).length > 0 && ( */}\r\n        {/* <div className=\"mb-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\r\n          <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">Performance Distribution</h3>\r\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\r\n            {Object.entries(performanceData).map(([category, count]) => {\r\n              const colors = {\r\n                'high_performers': { bg: 'bg-green-100', text: 'text-green-800', bar: 'bg-green-500' },\r\n                'good_performers': { bg: 'bg-blue-100', text: 'text-blue-800', bar: 'bg-blue-500' },\r\n                'average_performers': { bg: 'bg-yellow-100', text: 'text-yellow-800', bar: 'bg-yellow-500' },\r\n                'poor_performers': { bg: 'bg-red-100', text: 'text-red-800', bar: 'bg-red-500' }\r\n              };\r\n              const color = colors[category] || { bg: 'bg-gray-100', text: 'text-gray-800', bar: 'bg-gray-500' };\r\n              const percentage = enhancedStats.totalStudents > 0 ? (count / enhancedStats.totalStudents * 100) : 0;\r\n              \r\n              return (\r\n                <div key={category} className={`${color.bg} rounded-lg p-4`}>\r\n                  <div className=\"text-center\">\r\n                    <div className={`text-xl font-bold ${color.text}`}>{count}</div>\r\n                    <div className={`text-sm font-medium ${color.text} capitalize`}>\r\n                      {category.replace('_', ' ')}\r\n                    </div>\r\n                    <div className=\"mt-2 bg-white bg-opacity-50 rounded-full h-2\">\r\n                      <div \r\n                        className={`${color.bar} h-2 rounded-full transition-all duration-300`}\r\n                        style={{ width: `${percentage}%` }}\r\n                      ></div>\r\n                    </div>\r\n                    <div className={`text-xs ${color.text} mt-1`}>{percentage.toFixed(1)}%</div>\r\n                  </div>\r\n                </div>\r\n              );\r\n            })}\r\n          </div>\r\n        </div>\r\n      )} */}\r\n\r\n      {/* Top Departments Analytics */}\r\n      {/* {!loading && departmentData.length > 0 && ( */}\r\n        {/* <div className=\"mb-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\r\n          <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">Top Performing Departments</h3>\r\n          <div className=\"space-y-4\">\r\n            {departmentData\r\n              .sort((a, b) => (b.avg_gpa || 0) - (a.avg_gpa || 0))\r\n              .slice(0, 5)\r\n              .map((dept, index) => (\r\n                <div key={index} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\r\n                  <div className=\"flex-1\">\r\n                    <div className=\"flex justify-between items-center mb-2\">\r\n                      <span className=\"font-medium text-gray-900\">{dept.branch || 'Unknown Department'}</span>\r\n                      <span className=\"text-sm text-gray-600\">{dept.total_students} students</span>\r\n                    </div>\r\n                    <div className=\"flex justify-between text-xs text-gray-500 mb-1\">\r\n                      <span>Avg GPA: {dept.avg_gpa ? dept.avg_gpa.toFixed(2) : 'N/A'}</span>\r\n                      <span>High Performers: {dept.high_performers || 0}</span>\r\n                      <span>Placement Rate: {dept.placement_rate ? dept.placement_rate.toFixed(1) + '%' : 'N/A'}</span>\r\n                    </div>\r\n                    <div className=\"bg-gray-200 rounded-full h-2\">\r\n                      <div \r\n                        className=\"bg-blue-500 h-2 rounded-full transition-all duration-300\" \r\n                        style={{ \r\n                          width: `${dept.avg_gpa ? (dept.avg_gpa / 10) * 100 : 0}%` \r\n                        }}\r\n                      ></div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              ))\r\n            }\r\n          </div>\r\n        </div>\r\n      )} */}\r\n\r\n      {/* Department Cards with Enhanced Data */}\r\n      <div className=\"mb-6\">\r\n        <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">Browse by Department</h3>\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\r\n          {departmentOptions.map((dept) => {\r\n            // More robust department matching\r\n            const stats = departmentStats.find(stat => \r\n              stat.department?.toLowerCase().trim() === dept.value?.toLowerCase().trim() ||\r\n              stat.department?.toLowerCase().includes(dept.value?.toLowerCase()) ||\r\n              dept.value?.toLowerCase().includes(stat.department?.toLowerCase())\r\n            );\r\n            const analyticsStats = departmentData.find(analytic => \r\n              analytic.branch?.toLowerCase().trim() === dept.value?.toLowerCase().trim() ||\r\n              analytic.branch?.toLowerCase().includes(dept.value?.toLowerCase()) ||\r\n              dept.value?.toLowerCase().includes(analytic.branch?.toLowerCase())\r\n            );\r\n            \r\n            // Fallback to analytics data if stats is not found\r\n            const studentCount = stats?.count || analyticsStats?.total_students || 0;\r\n            \r\n            return (\r\n              <div\r\n                key={dept.value}\r\n                onClick={() => onSelect(dept.value)}\r\n                className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md hover:border-blue-300 cursor-pointer transition-all duration-200 group\"\r\n              >\r\n                <div className=\"flex items-center justify-between mb-4\">\r\n                  <div className=\"p-3 bg-blue-50 rounded-lg group-hover:bg-blue-100 transition-colors\">\r\n                    <GraduationCap className=\"w-6 h-6 text-blue-600\" />\r\n                  </div>\r\n                  <div className=\"text-right\">\r\n                    <div className=\"text-2xl font-bold text-gray-900\">{studentCount}</div>\r\n                    <div className=\"text-xs text-gray-500\">Students</div>\r\n                  </div>\r\n                </div>\r\n                <h3 className=\"font-semibold text-gray-900 mb-2\">{dept.label}</h3>\r\n                <p className=\"text-sm text-gray-600 mb-3\">View and manage {dept.label.toLowerCase()} students</p>\r\n                \r\n                {/* Enhanced Department Stats */}\r\n                {analyticsStats && (\r\n                  <div className=\"border-t pt-3 mt-3\">\r\n                    <div className=\"grid grid-cols-2 gap-2 text-xs\">\r\n                      <div className=\"text-center\">\r\n                        <div className=\"font-semibold text-blue-600\">{analyticsStats.avg_gpa ? analyticsStats.avg_gpa.toFixed(2) : 'N/A'}</div>\r\n                        <div className=\"text-gray-500\">Avg GPA</div>\r\n                      </div>\r\n                      <div className=\"text-center\">\r\n                        <div className=\"font-semibold text-purple-600\">{analyticsStats.high_performers || 0}</div>\r\n                        <div className=\"text-gray-500\">Top Performers</div>\r\n                      </div>\r\n                    </div>\r\n                    {/* {analyticsStats.placement_rate && (\r\n                      <div className=\"mt-2 text-center\">\r\n                        <div className=\"text-xs text-gray-500\">Placement Rate</div>\r\n                        <div className=\"font-semibold text-green-600\">{analyticsStats.placement_rate.toFixed(1)}%</div>\r\n                      </div>\r\n                    )} */}\r\n                  </div>\r\n                )}\r\n              </div>\r\n            );\r\n          })}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Loading State */}\r\n      {loading && (\r\n        <div className=\"flex items-center justify-center h-32\">\r\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\r\n          <span className=\"ml-2 text-gray-600\">Loading analytics...</span>\r\n        </div>\r\n      )}\r\n\r\n      {/* Last Updated */}\r\n      {analytics?.enhanced?.last_updated && (\r\n        <div className=\"text-center text-sm text-gray-500 mt-6\">\r\n          Last updated: {new Date(analytics.enhanced.last_updated).toLocaleString()}\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;;;;AAEe,SAAS,gBAAgB,EAAE,iBAAiB,EAAE,eAAe,EAAE,aAAa,EAAE,QAAQ,EAAE;;IACrG,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,uBAAuB;IACvB,MAAM,iBAAiB,OAAO,eAAe,KAAK;QAChD,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,SAAS,MAAM,0HAAA,CAAA,oBAAiB,CAAC,sBAAsB,CAAC;YAE9D,IAAI,OAAO,OAAO,EAAE;gBAClB,aAAa,OAAO,IAAI;gBAExB,4CAA4C;gBAC5C,IAAI,OAAO,QAAQ,IAAI,OAAO,UAAU,EAAE;oBACxC,MAAM,gBAAgB,OAAO,IAAI,EAAE,UAAU,EAAE;oBAC/C,QAAQ,IAAI,CAAC,mCAAmC;oBAChD,SAAS,CAAC,6BAA6B,EAAE,cAAc,IAAI,CAAC,OAAO;gBACrE;YACF,OAAO;gBACL,SAAS,OAAO,KAAK,IAAI;YAC3B;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,oBAAoB;IACpB,MAAM,gBAAgB;QACpB,cAAc;QACd,IAAI;YACF,MAAM,SAAS,MAAM,0HAAA,CAAA,oBAAiB,CAAC,iBAAiB;YACxD,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM,eAAe;YACvB,OAAO;gBACL,SAAS,OAAO,KAAK,IAAI;YAC3B;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,SAAS;QACX,SAAU;YACR,cAAc;QAChB;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;QACF;oCAAG,EAAE;IAEL,oCAAoC;IACpC,MAAM,WAAW,WAAW,UAAU,YAAY,CAAC;IACnD,MAAM,iBAAiB,WAAW,aAAa,eAAe,EAAE;IAChE,MAAM,WAAW,WAAW,OAAO,SAAS,EAAE;IAC9C,MAAM,kBAAkB,WAAW,aAAa,0BAA0B,CAAC;IAE3E,2BAA2B;IAC3B,MAAM,gBAAgB;QACpB,eAAe,SAAS,cAAc,IAAI,iBAAiB;QAC3D,mBAAmB,SAAS,kBAAkB,IAAI,gBAAgB,MAAM,IAAI;QAC5E,gBAAgB,SAAS,eAAe,IAAI;QAC5C,yBAAyB,SAAS,yBAAyB,IAAI;QAC/D,gBAAgB,SAAS,eAAe,IAAI;QAC5C,YAAY,eAAe,MAAM,GAAG,IAChC,CAAC,eAAe,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,OAAO,IAAI,CAAC,GAAG,KAAK,eAAe,MAAM,EAAE,OAAO,CAAC,KACrG;IACN;IAEA,qBACE;;0BAEE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAE/B,6LAAC;wBACC,SAAS;wBACT,UAAU;wBACV,WAAU;;0CAEV,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAW,CAAC,aAAa,EAAE,aAAa,iBAAiB,IAAI;;;;;;4BACvE,aAAa,kBAAkB;;;;;;;;;;;;;YAKnC,uBACC,6LAAC;gBAAI,WAAW,CAAC,oBAAoB,EACnC,MAAM,QAAQ,CAAC,iBAAiB,MAAM,QAAQ,CAAC,cAC3C,0CACA,mCACJ;;kCACA,6LAAC;wBAAE,WAAW,GACZ,MAAM,QAAQ,CAAC,iBAAiB,MAAM,QAAQ,CAAC,cAC3C,oBACA,gBACJ;kCACC,MAAM,QAAQ,CAAC,iBAAiB,MAAM,QAAQ,CAAC,cAC5C,CAAC,GAAG,EAAE,OAAO,GACb;;;;;;kCAGN,6LAAC;wBACC,SAAS,IAAM;wBACf,WAAW,CAAC,2DAA2D,EACrE,MAAM,QAAQ,CAAC,iBAAiB,MAAM,QAAQ,CAAC,cAC3C,sCACA,+BACJ;kCACH;;;;;;;;;;;;0BAOL,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,6LAAC,qNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;;kCAGvB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DAAoC,cAAc,aAAa,CAAC,cAAc;;;;;;8DAC7F,6LAAC;oDAAI,WAAU;8DAAoC;;;;;;8DACnD,6LAAC;oDAAI,WAAU;8DAA6B;;;;;;;;;;;;sDAE9C,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAKrB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DAAqC,cAAc,iBAAiB;;;;;;8DACnF,6LAAC;oDAAI,WAAU;8DAAqC;;;;;;8DACpD,6LAAC;oDAAI,WAAU;;wDAA8B;wDAAU,cAAc,UAAU;;;;;;;;;;;;;sDAEjF,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAKxB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DAAsC,cAAc,cAAc,CAAC,cAAc;;;;;;8DAChG,6LAAC;oDAAI,WAAU;8DAAsC;;;;;;8DACrD,6LAAC;oDAAI,WAAU;;wDAAgC,cAAc,uBAAuB,CAAC,OAAO,CAAC;wDAAG;;;;;;;;;;;;;sDAElG,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAKrB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DAAsC,cAAc,cAAc,CAAC,cAAc;;;;;;8DAChG,6LAAC;oDAAI,WAAU;8DAAsC;;;;;;8DACrD,6LAAC;oDAAI,WAAU;8DAA+B;;;;;;;;;;;;sDAEhD,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAgFjC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;kCACZ,kBAAkB,GAAG,CAAC,CAAC;4BACtB,kCAAkC;4BAClC,MAAM,QAAQ,gBAAgB,IAAI,CAAC,CAAA,OACjC,KAAK,UAAU,EAAE,cAAc,WAAW,KAAK,KAAK,EAAE,cAAc,UACpE,KAAK,UAAU,EAAE,cAAc,SAAS,KAAK,KAAK,EAAE,kBACpD,KAAK,KAAK,EAAE,cAAc,SAAS,KAAK,UAAU,EAAE;4BAEtD,MAAM,iBAAiB,eAAe,IAAI,CAAC,CAAA,WACzC,SAAS,MAAM,EAAE,cAAc,WAAW,KAAK,KAAK,EAAE,cAAc,UACpE,SAAS,MAAM,EAAE,cAAc,SAAS,KAAK,KAAK,EAAE,kBACpD,KAAK,KAAK,EAAE,cAAc,SAAS,SAAS,MAAM,EAAE;4BAGtD,mDAAmD;4BACnD,MAAM,eAAe,OAAO,SAAS,gBAAgB,kBAAkB;4BAEvE,qBACE,6LAAC;gCAEC,SAAS,IAAM,SAAS,KAAK,KAAK;gCAClC,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;;;;;;0DAE3B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAoC;;;;;;kEACnD,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;kDAG3C,6LAAC;wCAAG,WAAU;kDAAoC,KAAK,KAAK;;;;;;kDAC5D,6LAAC;wCAAE,WAAU;;4CAA6B;4CAAiB,KAAK,KAAK,CAAC,WAAW;4CAAG;;;;;;;oCAGnF,gCACC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAA+B,eAAe,OAAO,GAAG,eAAe,OAAO,CAAC,OAAO,CAAC,KAAK;;;;;;sEAC3G,6LAAC;4DAAI,WAAU;sEAAgB;;;;;;;;;;;;8DAEjC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAiC,eAAe,eAAe,IAAI;;;;;;sEAClF,6LAAC;4DAAI,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;+BA1BlC,KAAK,KAAK;;;;;wBAuCrB;;;;;;;;;;;;YAKH,yBACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAK,WAAU;kCAAqB;;;;;;;;;;;;YAKxC,WAAW,UAAU,8BACpB,6LAAC;gBAAI,WAAU;;oBAAyC;oBACvC,IAAI,KAAK,UAAU,QAAQ,CAAC,YAAY,EAAE,cAAc;;;;;;;;;AAKjF;GA9UwB;KAAA", "debugId": null}}, {"offset": {"line": 7720, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/app/admin/student-management/PassoutYearCards.jsx"], "sourcesContent": ["import { ArrowLeft, Calendar, AlertCircle, Loader2 } from \"lucide-react\";\r\n\r\nexport default function PassoutYearCards({\r\n  departmentLabel,\r\n  onBack,\r\n  getAvailablePassoutYears,\r\n  selectedDepartment,\r\n  onSelectYear,\r\n  yearStats = [],\r\n  students = [],\r\n  isLoading = false,\r\n  error = null\r\n}) {\r\n  // Debug logging\r\n  console.log('PassoutYearCards - yearStats:', yearStats);\r\n  console.log('PassoutYearCards - students:', students);\r\n  console.log('PassoutYearCards - selectedDepartment:', selectedDepartment);\r\n\r\n  // Get available years\r\n  // Sort years in descending order\r\n  const availableYears = getAvailablePassoutYears().slice().sort((a, b) => Number(b) - Number(a));\r\n\r\n  return (\r\n    <>\r\n      <div className=\"mb-6\">\r\n        <div className=\"flex items-center gap-4 mb-4\">\r\n          <button\r\n            onClick={onBack}\r\n            className=\"p-2 hover:bg-gray-100 rounded-full transition-colors\"\r\n          >\r\n            <ArrowLeft className=\"w-5 h-5 text-gray-600\" />\r\n          </button>\r\n          <div>\r\n            <h1 className=\"text-3xl font-bold text-gray-900\">\r\n              {departmentLabel} - Passout Years\r\n            </h1>\r\n            <p className=\"text-gray-600\">Select a passout year to view students</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      {isLoading && (\r\n        <div className=\"flex flex-col items-center justify-center py-12\">\r\n          <Loader2 className=\"w-10 h-10 text-blue-500 animate-spin mb-4\" />\r\n          <p className=\"text-gray-600\">Loading passout years data...</p>\r\n        </div>\r\n      )}\r\n      \r\n      {error && (\r\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\">\r\n          <div className=\"flex items-center\">\r\n            <AlertCircle className=\"w-5 h-5 text-red-500 mr-2\" />\r\n            <p className=\"text-red-600\">Error loading data: {error}</p>\r\n          </div>\r\n        </div>\r\n      )}\r\n      \r\n      {!isLoading && !error && availableYears.length === 0 && (\r\n        <div className=\"bg-gray-50 border border-gray-200 rounded-lg p-8 text-center\">\r\n          <Calendar className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\r\n          <h3 className=\"text-xl font-semibold text-gray-700 mb-2\">No Passout Years Found</h3>\r\n          <p className=\"text-gray-600 mb-4\">There are no passout years available for {departmentLabel}</p>\r\n        </div>\r\n      )}\r\n      \r\n      {!isLoading && !error && availableYears.length > 0 && (\r\n        <div className=\"grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6\">\r\n          {availableYears.map(year => {\r\n            // Find matching year statistics with more flexible matching\r\n            let yearStat = yearStats.find(stat => {\r\n              const statYear = stat.passout_year || stat.year || stat.passoutYear;\r\n              return statYear?.toString() === year?.toString();\r\n            });\r\n            \r\n            // Get counts from year statistics with improved accuracy\r\n            let count = yearStat?.total_students || yearStat?.count || 0;\r\n            \r\n            // For more accurate counts, check if we have metadata about total students per year\r\n            if (count === 0 && yearStats.length > 0) {\r\n              // If we don't have a direct match, look in the metadata for this year\r\n              const yearMetadata = yearStats.find(s => s.year_metadata && \r\n                s.year_metadata[year] && s.year_metadata[year].total);\r\n              \r\n              if (yearMetadata && yearMetadata.year_metadata) {\r\n                count = yearMetadata.year_metadata[year].total || 0;\r\n              }\r\n            }\r\n            \r\n            // If we still don't have a count, calculate from students array\r\n            if (count === 0 && students.length > 0) {\r\n              const matchingStudents = students.filter(student => {\r\n                // Check if student belongs to selected department (improved matching)\r\n                const studentDepartment = (student.department || student.branch || student.dept || '').toLowerCase().trim();\r\n                const selectedDepartmentLower = (selectedDepartment || '').toLowerCase().trim();\r\n                \r\n                // Try exact match first, then contains match\r\n                const departmentMatch = studentDepartment === selectedDepartmentLower ||\r\n                                      studentDepartment.includes(selectedDepartmentLower) ||\r\n                                      selectedDepartmentLower.includes(studentDepartment);\r\n                \r\n                // Check if student belongs to this year (try multiple field variations)\r\n                const studentYear = student.passoutYear || \r\n                                  student.passout_year || \r\n                                  student.year || \r\n                                  student.expectedGraduationYear || \r\n                                  student.expected_graduation_year ||\r\n                                  student.graduation_year;\r\n                                  \r\n                return studentYear?.toString() === year?.toString();\r\n              });\r\n              \r\n              // Use the length of matching students for the count\r\n              count = matchingStudents.length;\r\n            }\r\n            \r\n            // Use total_count from pagination if we have filtered by year\r\n            if (count === 0 && selectedDepartment) {\r\n              // Try to get count from the total_count in parent component's state\r\n              const parentComponent = window.parent_component_context;\r\n              if (parentComponent && \r\n                  parentComponent.totalStudents && \r\n                  parentComponent.selectedPassoutYear === year) {\r\n                count = parentComponent.totalStudents;\r\n              }\r\n            }\r\n            \r\n            return (\r\n              <div\r\n                key={year}\r\n                onClick={() => onSelectYear(year)}\r\n                className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md hover:border-blue-300 cursor-pointer transition-all duration-200 group\"\r\n              >\r\n                <div className=\"flex items-center justify-between mb-4\">\r\n                  <div className=\"p-3 bg-purple-50 rounded-lg group-hover:bg-purple-100 transition-colors\">\r\n                    <Calendar className=\"w-6 h-6 text-purple-600\" />\r\n                  </div>\r\n                  <div className=\"text-right\">\r\n                    <div className=\"text-2xl font-bold text-gray-900\">{count}</div>\r\n                    <div className=\"text-xs text-gray-500\">Total Students</div>\r\n\r\n                  </div>\r\n                </div>\r\n                <h3 className=\"font-semibold text-gray-900 mb-2\">{year}</h3>\r\n                <p className=\"text-sm text-gray-600\">Passout Year</p>\r\n              </div>\r\n            );\r\n          })}\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n}\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;;;AAEe,SAAS,iBAAiB,EACvC,eAAe,EACf,MAAM,EACN,wBAAwB,EACxB,kBAAkB,EAClB,YAAY,EACZ,YAAY,EAAE,EACd,WAAW,EAAE,EACb,YAAY,KAAK,EACjB,QAAQ,IAAI,EACb;IACC,gBAAgB;IAChB,QAAQ,GAAG,CAAC,iCAAiC;IAC7C,QAAQ,GAAG,CAAC,gCAAgC;IAC5C,QAAQ,GAAG,CAAC,0CAA0C;IAEtD,sBAAsB;IACtB,iCAAiC;IACjC,MAAM,iBAAiB,2BAA2B,KAAK,GAAG,IAAI,CAAC,CAAC,GAAG,IAAM,OAAO,KAAK,OAAO;IAE5F,qBACE;;0BACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;sCAEvB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;;wCACX;wCAAgB;;;;;;;8CAEnB,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;YAKlC,2BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;YAIhC,uBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,6LAAC;4BAAE,WAAU;;gCAAe;gCAAqB;;;;;;;;;;;;;;;;;;YAKtD,CAAC,aAAa,CAAC,SAAS,eAAe,MAAM,KAAK,mBACjD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAE,WAAU;;4BAAqB;4BAA0C;;;;;;;;;;;;;YAI/E,CAAC,aAAa,CAAC,SAAS,eAAe,MAAM,GAAG,mBAC/C,6LAAC;gBAAI,WAAU;0BACZ,eAAe,GAAG,CAAC,CAAA;oBAClB,4DAA4D;oBAC5D,IAAI,WAAW,UAAU,IAAI,CAAC,CAAA;wBAC5B,MAAM,WAAW,KAAK,YAAY,IAAI,KAAK,IAAI,IAAI,KAAK,WAAW;wBACnE,OAAO,UAAU,eAAe,MAAM;oBACxC;oBAEA,yDAAyD;oBACzD,IAAI,QAAQ,UAAU,kBAAkB,UAAU,SAAS;oBAE3D,oFAAoF;oBACpF,IAAI,UAAU,KAAK,UAAU,MAAM,GAAG,GAAG;wBACvC,sEAAsE;wBACtE,MAAM,eAAe,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,aAAa,IACtD,EAAE,aAAa,CAAC,KAAK,IAAI,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK;wBAEtD,IAAI,gBAAgB,aAAa,aAAa,EAAE;4BAC9C,QAAQ,aAAa,aAAa,CAAC,KAAK,CAAC,KAAK,IAAI;wBACpD;oBACF;oBAEA,gEAAgE;oBAChE,IAAI,UAAU,KAAK,SAAS,MAAM,GAAG,GAAG;wBACtC,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA;4BACvC,sEAAsE;4BACtE,MAAM,oBAAoB,CAAC,QAAQ,UAAU,IAAI,QAAQ,MAAM,IAAI,QAAQ,IAAI,IAAI,EAAE,EAAE,WAAW,GAAG,IAAI;4BACzG,MAAM,0BAA0B,CAAC,sBAAsB,EAAE,EAAE,WAAW,GAAG,IAAI;4BAE7E,6CAA6C;4BAC7C,MAAM,kBAAkB,sBAAsB,2BACxB,kBAAkB,QAAQ,CAAC,4BAC3B,wBAAwB,QAAQ,CAAC;4BAEvD,wEAAwE;4BACxE,MAAM,cAAc,QAAQ,WAAW,IACrB,QAAQ,YAAY,IACpB,QAAQ,IAAI,IACZ,QAAQ,sBAAsB,IAC9B,QAAQ,wBAAwB,IAChC,QAAQ,eAAe;4BAEzC,OAAO,aAAa,eAAe,MAAM;wBAC3C;wBAEA,oDAAoD;wBACpD,QAAQ,iBAAiB,MAAM;oBACjC;oBAEA,8DAA8D;oBAC9D,IAAI,UAAU,KAAK,oBAAoB;wBACrC,oEAAoE;wBACpE,MAAM,kBAAkB,OAAO,wBAAwB;wBACvD,IAAI,mBACA,gBAAgB,aAAa,IAC7B,gBAAgB,mBAAmB,KAAK,MAAM;4BAChD,QAAQ,gBAAgB,aAAa;wBACvC;oBACF;oBAEA,qBACE,6LAAC;wBAEC,SAAS,IAAM,aAAa;wBAC5B,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAoC;;;;;;0DACnD,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAI3C,6LAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;uBAfhC;;;;;gBAkBX;;;;;;;;AAKV;KArJwB", "debugId": null}}, {"offset": {"line": 8027, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/app/admin/student-management/StudentList.jsx"], "sourcesContent": ["import { ArrowLeft, Search } from \"lucide-react\";\r\n\r\nexport default function StudentList({\r\n  departmentLabel,\r\n  passoutYear,\r\n  onBack,\r\n  searchTerm,\r\n  handleSearchInputChange,\r\n  handleSearchKeyDown,\r\n  cgpaMin,\r\n  setCgpaMin,\r\n  cgpaMax,\r\n  setCgpaMax,\r\n  handleSearch,\r\n  getFilteredStudents,\r\n  currentPage,\r\n  handlePageChange,\r\n  handleStudentClick,\r\n  loading,\r\n  totalPages,\r\n  totalStudents\r\n}) {\r\n  // Use data directly from backend instead of client-side filtering\r\n  const students = getFilteredStudents();\r\n  \r\n  // Use backend pagination data\r\n  const actualTotalPages = totalPages || 1;\r\n  const actualTotalStudents = totalStudents || 0;\r\n  \r\n  // Students are already paginated from backend, no need to slice again\r\n  const pageStudents = students;\r\n\r\n  return (\r\n    <>\r\n      <div className=\"mb-6\">\r\n        <div className=\"flex items-center gap-4 mb-4\">\r\n          <button\r\n            onClick={onBack}\r\n            className=\"p-2 hover:bg-gray-100 rounded-full transition-colors\"\r\n          >\r\n            <ArrowLeft className=\"w-5 h-5 text-gray-600\" />\r\n          </button>\r\n          <div>\r\n            <h1 className=\"text-3xl font-bold text-gray-900\">\r\n              {departmentLabel} - {passoutYear} Students\r\n            </h1>\r\n            <p className=\"text-gray-600\">\r\n              Manage students from {departmentLabel?.toLowerCase()}, passout year {passoutYear}\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      {/* Search and Filter Section */}\r\n      <div className=\"bg-white rounded-lg shadow-sm p-6 mb-6\">\r\n        <div className=\"flex gap-4 flex-wrap\">\r\n          {/* Search Input */}\r\n          <div className=\"flex-1 min-w-[200px]\">\r\n            <div className=\"relative\">\r\n              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                <Search className=\"h-5 w-5 text-gray-400\" />\r\n              </div>\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Search by name or roll number...\"\r\n                value={searchTerm}\r\n                onChange={handleSearchInputChange}\r\n                onKeyDown={(e) => {\r\n                  if (e.key === 'Enter') {\r\n                    handleSearch();\r\n                  }\r\n                  handleSearchKeyDown && handleSearchKeyDown(e);\r\n                }}\r\n                className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n              />\r\n            </div>\r\n          </div>\r\n          {/* CGPA Filter */}\r\n          <div className=\"flex items-center gap-2 min-w-[220px]\">\r\n            <label className=\"text-gray-700 text-sm\">CGPA:</label>\r\n            <input\r\n              type=\"number\"\r\n              step=\"0.01\"\r\n              min=\"0\"\r\n              max=\"10\"\r\n              value={cgpaMin}\r\n              onChange={e => setCgpaMin(e.target.value)}\r\n              placeholder=\"Min\"\r\n              className=\"w-20 px-2 py-1 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n            />\r\n            <span className=\"text-gray-500\">-</span>\r\n            <input\r\n              type=\"number\"\r\n              step=\"0.01\"\r\n              min=\"0\"\r\n              max=\"10\"\r\n              value={cgpaMax}\r\n              onChange={e => setCgpaMax(e.target.value)}\r\n              placeholder=\"Max\"\r\n              className=\"w-20 px-2 py-1 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n            />\r\n          </div>\r\n          {/* Search Button */}\r\n          <button\r\n            onClick={handleSearch}\r\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 whitespace-nowrap\"\r\n          >\r\n            Search\r\n          </button>\r\n        </div>\r\n      </div>\r\n      {/* Students Table */}\r\n      <div className=\"bg-white rounded-lg shadow-sm overflow-hidden\">\r\n        <div className=\"px-6 py-4 border-b border-gray-200\">\r\n          <h2 className=\"text-lg font-semibold text-gray-800 mb-4\">\r\n            Students ({actualTotalStudents} total, showing {pageStudents.length} on page {currentPage})\r\n          </h2>\r\n        </div>\r\n        <div className=\"overflow-x-auto\">\r\n          <table className=\"w-full\">\r\n            <thead className=\"bg-gray-50\">\r\n              <tr>\r\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                  Roll Number\r\n                </th>\r\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                  Name\r\n                </th>\r\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                  Email\r\n                </th>\r\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                  Year\r\n                </th>\r\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                  CGPA\r\n                </th>\r\n              </tr>\r\n            </thead>\r\n            <tbody className=\"bg-white divide-y divide-gray-200\">\r\n              {pageStudents.map((student) => (\r\n                <tr\r\n                  key={student.id}\r\n                  onClick={() => handleStudentClick(student)}\r\n                  className=\"hover:bg-gray-50 cursor-pointer transition-colors\"\r\n                >\r\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\r\n                    {student.rollNumber}\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    <div className=\"flex items-center\">\r\n                      <div className=\"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-medium text-sm mr-3\">\r\n                        {student.name.charAt(0)}\r\n                      </div>\r\n                      <div className=\"text-sm font-medium text-gray-900\">\r\n                        {student.name}\r\n                      </div>\r\n                    </div>\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                    {student.email}\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                    {student.year}\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    <span className=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800\">\r\n                      {student.cgpa}\r\n                    </span>\r\n                  </td>\r\n                </tr>\r\n              ))}\r\n            </tbody>\r\n          </table>\r\n          {loading && (\r\n            <div className=\"text-center py-12\">\r\n              <div className=\"inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mb-2\"></div>\r\n              <div className=\"text-gray-500\">Loading students...</div>\r\n            </div>\r\n          )}\r\n          {pageStudents.length === 0 && !loading && (\r\n            <div className=\"text-center py-12\">\r\n              <div className=\"text-gray-500 text-lg mb-2\">No students found</div>\r\n              <p className=\"text-gray-400\">\r\n                {searchTerm ? 'Try adjusting your search criteria' : 'No students in this department and passout year'}\r\n              </p>\r\n            </div>\r\n          )}\r\n        </div>\r\n        {/* Pagination Controls */}\r\n        {actualTotalPages > 1 && (\r\n          <div className=\"flex items-center justify-between px-6 py-4 border-t border-gray-200 bg-gray-50\">\r\n            <div className=\"text-sm text-gray-700\">\r\n              Page {currentPage} of {actualTotalPages} ({actualTotalStudents} total students)\r\n            </div>\r\n            <div className=\"flex items-center gap-2\">\r\n              <button\r\n                onClick={() => handlePageChange(currentPage - 1)}\r\n                disabled={currentPage === 1}\r\n                className={`px-3 py-2 rounded border text-sm ${\r\n                  currentPage === 1 \r\n                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed border-gray-200' \r\n                    : 'bg-white hover:bg-gray-50 border-gray-300 text-gray-700'\r\n                }`}\r\n              >\r\n                Previous\r\n              </button>\r\n              {/* Page Numbers */}\r\n              <div className=\"flex gap-1\">\r\n                {Array.from({ length: Math.min(5, actualTotalPages) }, (_, i) => {\r\n                  let pageNum;\r\n                  if (actualTotalPages <= 5) {\r\n                    pageNum = i + 1;\r\n                  } else if (currentPage <= 3) {\r\n                    pageNum = i + 1;\r\n                  } else if (currentPage >= actualTotalPages - 2) {\r\n                    pageNum = actualTotalPages - 4 + i;\r\n                  } else {\r\n                    pageNum = currentPage - 2 + i;\r\n                  }\r\n                  return (\r\n                    <button\r\n                      key={pageNum}\r\n                      onClick={() => handlePageChange(pageNum)}\r\n                      className={`px-3 py-2 rounded text-sm ${\r\n                        pageNum === currentPage\r\n                          ? 'bg-blue-600 text-white'\r\n                          : 'bg-white hover:bg-gray-50 border border-gray-300 text-gray-700'\r\n                      }`}\r\n                    >\r\n                      {pageNum}\r\n                    </button>\r\n                  );\r\n                })}\r\n              </div>\r\n              <button\r\n                onClick={() => handlePageChange(currentPage + 1)}\r\n                disabled={currentPage === actualTotalPages}\r\n                className={`px-3 py-2 rounded border text-sm ${\r\n                  currentPage === actualTotalPages \r\n                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed border-gray-200' \r\n                    : 'bg-white hover:bg-gray-50 border-gray-300 text-gray-700'\r\n                }`}\r\n              >\r\n                Next\r\n              </button>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;;AAEe,SAAS,YAAY,EAClC,eAAe,EACf,WAAW,EACX,MAAM,EACN,UAAU,EACV,uBAAuB,EACvB,mBAAmB,EACnB,OAAO,EACP,UAAU,EACV,OAAO,EACP,UAAU,EACV,YAAY,EACZ,mBAAmB,EACnB,WAAW,EACX,gBAAgB,EAChB,kBAAkB,EAClB,OAAO,EACP,UAAU,EACV,aAAa,EACd;IACC,kEAAkE;IAClE,MAAM,WAAW;IAEjB,8BAA8B;IAC9B,MAAM,mBAAmB,cAAc;IACvC,MAAM,sBAAsB,iBAAiB;IAE7C,sEAAsE;IACtE,MAAM,eAAe;IAErB,qBACE;;0BACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;sCAEvB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;;wCACX;wCAAgB;wCAAI;wCAAY;;;;;;;8CAEnC,6LAAC;oCAAE,WAAU;;wCAAgB;wCACL,iBAAiB;wCAAc;wCAAgB;;;;;;;;;;;;;;;;;;;;;;;;0BAM7E,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU;wCACV,WAAW,CAAC;4CACV,IAAI,EAAE,GAAG,KAAK,SAAS;gDACrB;4CACF;4CACA,uBAAuB,oBAAoB;wCAC7C;wCACA,WAAU;;;;;;;;;;;;;;;;;sCAKhB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,WAAU;8CAAwB;;;;;;8CACzC,6LAAC;oCACC,MAAK;oCACL,MAAK;oCACL,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,UAAU,CAAA,IAAK,WAAW,EAAE,MAAM,CAAC,KAAK;oCACxC,aAAY;oCACZ,WAAU;;;;;;8CAEZ,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,6LAAC;oCACC,MAAK;oCACL,MAAK;oCACL,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,UAAU,CAAA,IAAK,WAAW,EAAE,MAAM,CAAC,KAAK;oCACxC,aAAY;oCACZ,WAAU;;;;;;;;;;;;sCAId,6LAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;0BAML,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;;gCAA2C;gCAC5C;gCAAoB;gCAAiB,aAAa,MAAM;gCAAC;gCAAU;gCAAY;;;;;;;;;;;;kCAG9F,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,WAAU;;kDACf,6LAAC;wCAAM,WAAU;kDACf,cAAA,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;;;;;;;;;;;;kDAKnG,6LAAC;wCAAM,WAAU;kDACd,aAAa,GAAG,CAAC,CAAC,wBACjB,6LAAC;gDAEC,SAAS,IAAM,mBAAmB;gDAClC,WAAU;;kEAEV,6LAAC;wDAAG,WAAU;kEACX,QAAQ,UAAU;;;;;;kEAErB,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACZ,QAAQ,IAAI,CAAC,MAAM,CAAC;;;;;;8EAEvB,6LAAC;oEAAI,WAAU;8EACZ,QAAQ,IAAI;;;;;;;;;;;;;;;;;kEAInB,6LAAC;wDAAG,WAAU;kEACX,QAAQ,KAAK;;;;;;kEAEhB,6LAAC;wDAAG,WAAU;kEACX,QAAQ,IAAI;;;;;;kEAEf,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAK,WAAU;sEACb,QAAQ,IAAI;;;;;;;;;;;;+CAzBZ,QAAQ,EAAE;;;;;;;;;;;;;;;;4BAgCtB,yBACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;4BAGlC,aAAa,MAAM,KAAK,KAAK,CAAC,yBAC7B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAA6B;;;;;;kDAC5C,6LAAC;wCAAE,WAAU;kDACV,aAAa,uCAAuC;;;;;;;;;;;;;;;;;;oBAM5D,mBAAmB,mBAClB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCAAwB;oCAC/B;oCAAY;oCAAK;oCAAiB;oCAAG;oCAAoB;;;;;;;0CAEjE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,iBAAiB,cAAc;wCAC9C,UAAU,gBAAgB;wCAC1B,WAAW,CAAC,iCAAiC,EAC3C,gBAAgB,IACZ,iEACA,2DACJ;kDACH;;;;;;kDAID,6LAAC;wCAAI,WAAU;kDACZ,MAAM,IAAI,CAAC;4CAAE,QAAQ,KAAK,GAAG,CAAC,GAAG;wCAAkB,GAAG,CAAC,GAAG;4CACzD,IAAI;4CACJ,IAAI,oBAAoB,GAAG;gDACzB,UAAU,IAAI;4CAChB,OAAO,IAAI,eAAe,GAAG;gDAC3B,UAAU,IAAI;4CAChB,OAAO,IAAI,eAAe,mBAAmB,GAAG;gDAC9C,UAAU,mBAAmB,IAAI;4CACnC,OAAO;gDACL,UAAU,cAAc,IAAI;4CAC9B;4CACA,qBACE,6LAAC;gDAEC,SAAS,IAAM,iBAAiB;gDAChC,WAAW,CAAC,0BAA0B,EACpC,YAAY,cACR,2BACA,kEACJ;0DAED;+CARI;;;;;wCAWX;;;;;;kDAEF,6LAAC;wCACC,SAAS,IAAM,iBAAiB,cAAc;wCAC9C,UAAU,gBAAgB;wCAC1B,WAAW,CAAC,iCAAiC,EAC3C,gBAAgB,mBACZ,iEACA,2DACJ;kDACH;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;KAzPwB", "debugId": null}}, {"offset": {"line": 8576, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/app/admin/student-management/page.jsx"], "sourcesContent": ["'use client';\r\n\r\nimport {\r\n  ArrowLeft,\r\n  Calendar,\r\n  RefreshCw,\r\n  Save,\r\n  Search,\r\n  User,\r\n  X,\r\n  GraduationCap,\r\n  Upload\r\n} from \"lucide-react\";\r\nimport { useEffect, useRef, useState } from 'react';\r\nimport { useRouter, useSearchParams } from 'next/navigation';\r\nimport { studentsAPI, studentMetricsAPI } from '../../../api/optimized';\r\nimport { getAuthToken } from '../../../utils/auth';\r\nimport { useNotification } from '../../../contexts/NotificationContext';\r\nimport CustomDropdown from './StudentDropdown';\r\nimport StudentProfile from './StudentProfile';\r\nimport DepartmentCards from './DepartmentCards';\r\nimport PassoutYearCards from './PassoutYearCards';\r\nimport StudentList from './StudentList';\r\n\r\nexport default function StudentManagement() {\r\n  const router = useRouter();\r\n  const searchParams = useSearchParams();\r\n  const { handleApiError, showValidationError, showSuccess } = useNotification();\r\n  \r\n  // Initialize state from URL parameters\r\n  const [searchTerm, setSearchTerm] = useState(searchParams.get('search') || '');\r\n  const [selectedDepartment, setSelectedDepartment] = useState(searchParams.get('department') || null);\r\n  const [selectedYear, setSelectedYear] = useState(searchParams.get('year') || 'all');\r\n  const [selectedStudent, setSelectedStudent] = useState(null);\r\n  const [isEditing, setIsEditing] = useState(false);\r\n  const [editedStudent, setEditedStudent] = useState(null);\r\n  const dropdownRef = useRef(null);\r\n  const [isYearDropdownOpen, setIsYearDropdownOpen] = useState(false);\r\n  const [students, setStudents] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [isRetrying, setIsRetrying] = useState(false);\r\n  const [currentPage, setCurrentPage] = useState(parseInt(searchParams.get('page')) || 1);\r\n  const [totalPages, setTotalPages] = useState(1);\r\n  const [totalStudents, setTotalStudents] = useState(0);\r\n  const [pageSize, setPageSize] = useState(10);\r\n  const [availableYears, setAvailableYears] = useState([]);\r\n  const [departmentStats, setDepartmentStats] = useState([]);\r\n  const [selectedPassoutYear, setSelectedPassoutYear] = useState(searchParams.get('passout_year') || null);\r\n  const [cgpaMin, setCgpaMin] = useState(searchParams.get('cgpa_min') || '');\r\n  const [cgpaMax, setCgpaMax] = useState(searchParams.get('cgpa_max') || '');\r\n  const [yearStats, setYearStats] = useState([]);\r\n  const [globalSearchTerm, setGlobalSearchTerm] = useState('');\r\n  const [showSearchResults, setShowSearchResults] = useState(false);\r\n  const [searchResults, setSearchResults] = useState([]);\r\n\r\n  // Function to update URL with current state\r\n  const updateURL = (params = {}) => {\r\n    const newParams = new URLSearchParams();\r\n    \r\n    // Always include current values unless overridden\r\n    const currentParams = {\r\n      page: params.page || currentPage.toString(),\r\n      department: params.department !== undefined ? params.department : selectedDepartment,\r\n      passout_year: params.passout_year !== undefined ? params.passout_year : selectedPassoutYear,\r\n      search: params.search !== undefined ? params.search : searchTerm,\r\n      cgpa_min: params.cgpa_min !== undefined ? params.cgpa_min : cgpaMin,\r\n      cgpa_max: params.cgpa_max !== undefined ? params.cgpa_max : cgpaMax,\r\n      ...params\r\n    };\r\n\r\n    // Only add non-empty parameters to URL\r\n    Object.entries(currentParams).forEach(([key, value]) => {\r\n      if (value && value !== 'all' && value !== '' && value !== 'null' && value !== null) {\r\n        newParams.set(key, value);\r\n      }\r\n    });\r\n\r\n    const newURL = `${window.location.pathname}?${newParams.toString()}`;\r\n    window.history.pushState({}, '', newURL);\r\n  };\r\n\r\n  // Dropdown options\r\n  const departmentOptions = [\r\n    { value: 'Computer Science', label: 'Computer Science' },\r\n    { value: 'Electronics', label: 'Electronics' },\r\n    { value: 'Mechanical', label: 'Mechanical' },\r\n    { value: 'Civil', label: 'Civil' },\r\n    { value: 'Electrical', label: 'Electrical' },\r\n    { value: 'Information Technology', label: 'Information Technology' },\r\n    { value: 'Chemical', label: 'Chemical' },\r\n    { value: 'Biotechnology', label: 'Biotechnology' }\r\n  ];\r\n\r\n  // Transform student data from API response\r\n  const transformStudentData = (student) => ({\r\n    id: student.id,\r\n    rollNumber: student.student_id || 'N/A',\r\n    name: `${student.first_name || ''} ${student.last_name || ''}`.trim() || 'Unknown',\r\n    email: student.contact_email || student.email || 'N/A',\r\n    phone: student.phone || 'N/A',\r\n    department: student.branch || 'N/A',\r\n    year: getYearFromBranch(student.branch, student),\r\n    cgpa: student.gpa || 'N/A',\r\n    gpa: student.gpa || 'N/A', // Overall CGPA from database\r\n    address: student.address || 'N/A',\r\n    dateOfBirth: student.date_of_birth || '',\r\n    parentContact: student.parent_contact || 'N/A',\r\n    education: student.education || 'N/A',\r\n    skills: student.skills || [],\r\n\r\n    // Academic details\r\n        joining_year: student.joining_year || student.admission_year || '',\r\n        passout_year: student.passout_year || student.graduation_year || '',\r\n\r\n        // Class XII details\r\n        twelfth_cgpa: student.twelfth_cgpa || student.class_12_cgpa || '',\r\n        twelfth_percentage: student.twelfth_percentage || student.class_12_percentage || '',\r\n        twelfth_year_of_passing: student.twelfth_year_of_passing || student.class_12_year || '',\r\n        twelfth_school: student.twelfth_school || student.class_12_school || '',\r\n        twelfth_board: student.twelfth_board || student.class_12_board || '',\r\n        twelfth_location: student.twelfth_location || student.class_12_location || '',\r\n        twelfth_specialization: student.twelfth_specialization || student.class_12_stream || '',\r\n\r\n        // Class X details\r\n        tenth_cgpa: student.tenth_cgpa || student.class_10_cgpa || '',\r\n        tenth_percentage: student.tenth_percentage || student.class_10_percentage || '',\r\n        tenth_year_of_passing: student.tenth_year_of_passing || student.class_10_year || '',\r\n        tenth_school: student.tenth_school || student.class_10_school || '',\r\n        tenth_board: student.tenth_board || student.class_10_board || '',\r\n        tenth_location: student.tenth_location || student.class_10_location || '',\r\n        tenth_specialization: student.tenth_specialization || student.class_10_stream || '',\r\n\r\n        // Address details\r\n        city: student.city || '',\r\n        district: student.district || '',\r\n        state: student.state || '',\r\n        pincode: student.pincode || student.pin_code || '',\r\n        country: student.country || 'India',\r\n\r\n        // Certificate URLs\r\n        tenth_certificate: student.tenth_certificate || student.class_10_certificate || '',\r\n        twelfth_certificate: student.twelfth_certificate || student.class_12_certificate || '',\r\n        tenth_certificate_url: student.tenth_certificate_url || student.class_10_certificate_url || '',\r\n        twelfth_certificate_url: student.twelfth_certificate_url || student.class_12_certificate_url || '',\r\n\r\n        // Resume details\r\n        resume: student.resume || '',\r\n        resume_url: student.resume_url || '',\r\n\r\n        // Semester-wise CGPA data - use actual backend data\r\n        semester_cgpas: student.semester_marksheets || [],\r\n        semester_marksheets: student.semester_marksheets || [],\r\n      });\r\n\r\n  // Remove debounced search - we'll use button-based search instead\r\n  // useEffect(() => {\r\n  //   const timer = setTimeout(() => {\r\n  //     setDebouncedSearchTerm(searchTerm);\r\n  //   }, 300); // 300ms delay\r\n\r\n  //   return () => clearTimeout(timer);\r\n  // }, [searchTerm]);\r\n\r\n  // Fetch students with server-side pagination and filtering\r\n  const fetchStudents = async (page = 1) => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      setIsRetrying(false);\r\n\r\n      const token = getAuthToken();\r\n      if (!token) {\r\n        throw new Error('No authentication token found. Please login first.');\r\n      }\r\n\r\n      // Build filter parameters for server-side filtering\r\n      const params = {\r\n        page,\r\n        page_size: pageSize,\r\n        ordering: 'student_id', // Sort by roll number (student_id) instead of name/email\r\n      };\r\n\r\n      // Add search filter\r\n      if (searchTerm) {\r\n        params.search = searchTerm;\r\n      }\r\n\r\n      // Add department filter\r\n      if (selectedDepartment) {\r\n        params.department = selectedDepartment;\r\n      }\r\n\r\n      // Add passout year filter - this is the key fix\r\n      if (selectedPassoutYear) {\r\n        params.passout_year = selectedPassoutYear;\r\n      }\r\n\r\n      // Add year filter (convert to passout year if needed)\r\n      if (selectedYear !== 'all') {\r\n        params.year = selectedYear;\r\n      }\r\n\r\n      // Add CGPA filters\r\n      if (cgpaMin) {\r\n        params.cgpa_min = cgpaMin;\r\n      }\r\n      if (cgpaMax) {\r\n        params.cgpa_max = cgpaMax;\r\n      }\r\n\r\n      // Debug logging\r\n      console.log('Fetching students with params:', params);\r\n\r\n      // Fetch data from optimized API\r\n      const response = await studentsAPI.getStudents(params);\r\n\r\n      // Transform the data\r\n      const transformedStudents = response.data.map(transformStudentData);\r\n\r\n      // Backend now handles sorting by student_id, so no client-side sorting needed\r\n      setStudents(transformedStudents);\r\n      setCurrentPage(page);\r\n      setTotalPages(response.pagination.total_pages);\r\n      setTotalStudents(response.pagination.total_count);\r\n      \r\n      // Update available years and department stats from metadata\r\n      if (response.metadata) {\r\n        if (response.metadata.available_years) {\r\n          setAvailableYears(response.metadata.available_years);\r\n        }\r\n        if (response.metadata.available_departments) {\r\n          // Convert to department stats format\r\n          const deptStats = response.metadata.available_departments.map(dept => ({\r\n            department: dept,\r\n            count: 0 // We'll need to fetch department counts separately or calculate them\r\n          }));\r\n          setDepartmentStats(deptStats);\r\n        }\r\n      }\r\n\r\n      setLoading(false);\r\n    } catch (err) {\r\n      console.error('Error fetching students:', err);\r\n\r\n      if (err.response?.status === 401) {\r\n        handleApiError(err, 'loading student data');\r\n        setError('Authentication failed. Please login again.');\r\n      } else if (err.response?.status === 403) {\r\n        handleApiError(err, 'accessing student management');\r\n        setError('You do not have permission to view students. Admin access required.');\r\n      } else if (err.message.includes('token')) {\r\n        handleApiError(err, 'authentication');\r\n        setError('Please login to access student management.');\r\n      } else {\r\n        handleApiError(err, 'loading student data');\r\n        setError(`Error: ${err.message}`);\r\n      }\r\n\r\n      setStudents([]);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Fetch year statistics for a specific department\r\n  const fetchYearStats = async (department) => {\r\n    try {\r\n      const token = getAuthToken();\r\n      if (!token) return;\r\n\r\n      console.log('Fetching year stats for department:', department);\r\n      \r\n      // Use the optimized API to get year statistics filtered by department\r\n      const result = await studentMetricsAPI.getYearStats(null, false, department);\r\n      \r\n      if (result.success && result.data && result.data.years) {\r\n        console.log('Year stats from API:', result.data.years);\r\n        setYearStats(result.data.years);\r\n      } else {\r\n        console.error('Failed to fetch year stats:', result.error);\r\n        \r\n        // Fallback: Calculate from current students data\r\n        const params = {\r\n          department: department,\r\n          page_size: 1000, // Get more data for accurate counting - increased from default\r\n          count_only: true // Add a parameter to indicate we just need counts\r\n        };\r\n\r\n        const response = await studentsAPI.getStudents(params);\r\n        console.log('Year stats response:', response);\r\n\r\n        // Calculate year statistics from the filtered department data\r\n        if (response.data) {\r\n          const yearCounts = {};\r\n          const currentYear = new Date().getFullYear();\r\n          \r\n          response.data.forEach(student => {\r\n            // Try different year field variations\r\n            const passoutYear = student.passout_year || \r\n                               student.graduation_year || \r\n                               student.expected_graduation_year ||\r\n                               student.passoutYear;\r\n            \r\n            if (passoutYear) {\r\n              const year = passoutYear.toString();\r\n              if (!yearCounts[year]) {\r\n                yearCounts[year] = {\r\n                  passout_year: parseInt(year),\r\n                  total_students: 0,\r\n                  current_year_students: 0\r\n                };\r\n              }\r\n              yearCounts[year].total_students++;\r\n              \r\n              // Check if this is a current year student (assuming final year students)\r\n              if (parseInt(year) === currentYear || parseInt(year) === currentYear + 1) {\r\n                yearCounts[year].current_year_students++;\r\n              }\r\n            }\r\n          });\r\n\r\n          const calculatedYearStats = Object.values(yearCounts);\r\n          console.log('Calculated year stats for department:', department, calculatedYearStats);\r\n          setYearStats(calculatedYearStats);\r\n        }\r\n      }\r\n\r\n    } catch (err) {\r\n      console.error('Error fetching year stats:', err);\r\n      setYearStats([]);\r\n    }\r\n  };\r\n\r\n  // Helper function to determine year from branch (you can customize this logic)\r\n  const getYearFromBranch = (branch, student) => {\r\n    if (student && student.joining_year && student.passout_year) {\r\n      return `${student.joining_year}-${student.passout_year}`;\r\n    }\r\n    return 'N/A';\r\n  };\r\n\r\n  // Initial data fetch\r\n  useEffect(() => {\r\n    fetchStudents();\r\n  }, []);\r\n\r\n  // Handle browser back/forward navigation\r\n  useEffect(() => {\r\n    const handlePopState = () => {\r\n      const searchParams = new URLSearchParams(window.location.search);\r\n      setCurrentPage(parseInt(searchParams.get('page')) || 1);\r\n      setSelectedDepartment(searchParams.get('department') || null);\r\n      setSelectedPassoutYear(searchParams.get('passout_year') || null);\r\n      setSearchTerm(searchParams.get('search') || '');\r\n      setCgpaMin(searchParams.get('cgpa_min') || '');\r\n      setCgpaMax(searchParams.get('cgpa_max') || '');\r\n      \r\n      // Fetch data with new parameters\r\n      fetchStudents(parseInt(searchParams.get('page')) || 1);\r\n    };\r\n\r\n    window.addEventListener('popstate', handlePopState);\r\n    return () => window.removeEventListener('popstate', handlePopState);\r\n  }, []);\r\n\r\n  // Load specific student if ID is in URL\r\n  useEffect(() => {\r\n    const studentId = searchParams.get('student_id');\r\n    if (studentId && students.length > 0) {\r\n      const student = students.find(s => s.id.toString() === studentId);\r\n      if (student) {\r\n        setSelectedStudent(student);\r\n        setEditedStudent({ ...student });\r\n      }\r\n    }\r\n  }, [students, searchParams]);\r\n\r\n  // Add this useEffect after your existing useEffect\r\n  useEffect(() => {\r\n    // Check if user is authenticated\r\n    const token = getAuthToken();\r\n    if (!token) {\r\n      // Redirect to login page or show login prompt\r\n      setError('Please login to access student management.');\r\n      setLoading(false);\r\n      return;\r\n    }\r\n    \r\n    fetchStudents();\r\n  }, []);\r\n\r\n  // Helper function to extract year from student ID (assuming format like *********)\r\n  const getYearFromStudentId = (studentId) => {\r\n    if (studentId && studentId.length >= 6) {\r\n      const yearPart = studentId.substring(2, 6);\r\n      if (!isNaN(yearPart)) {\r\n        return `${4 - (new Date().getFullYear() - parseInt(yearPart))}th Year`;\r\n      }\r\n    }\r\n    return 'Unknown';\r\n  };\r\n\r\n  // Close dropdown when clicking outside\r\n  useEffect(() => {\r\n    const handleClickOutside = (event) => {\r\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\r\n        setIsYearDropdownOpen(false);\r\n      }\r\n    };\r\n\r\n    document.addEventListener('mousedown', handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener('mousedown', handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  // Get available passout years from the metadata\r\n  const getAvailablePassoutYears = () => {\r\n    return availableYears;\r\n  };\r\n\r\n  // Get available years from students data\r\n  const getAvailableYears = (studentsData) => {\r\n    const years = [...new Set(studentsData.map(student => student.year).filter(year => year && year !== 'N/A'))];\r\n    return years.sort();\r\n  };\r\n\r\n  // Get department statistics\r\n  const getDepartmentStats = (studentsData) => {\r\n    const stats = {};\r\n    studentsData.forEach(student => {\r\n      if (student.department && student.department !== 'N/A') {\r\n        stats[student.department] = (stats[student.department] || 0) + 1;\r\n      }\r\n    });\r\n    return Object.entries(stats).map(([department, count]) => ({ department, count }));\r\n  };\r\n\r\n  // This function is no longer needed with server-side pagination\r\n  // Available years will be fetched from the backend\r\n\r\n  // Filtering is now handled server-side in fetchStudents\r\n\r\n  // Update filters and refetch when dependencies change (except search)\r\n  useEffect(() => {\r\n    setCurrentPage(1); // Reset to page 1 when filters change\r\n    fetchStudents(1); // Reset to page 1 when filters change\r\n  }, [selectedDepartment, selectedYear, selectedPassoutYear, cgpaMin, cgpaMax, pageSize]);\r\n\r\n  // Separate effect for page changes without resetting filters\r\n  useEffect(() => {\r\n    if (currentPage > 1) {\r\n      fetchStudents(currentPage);\r\n    }\r\n  }, [currentPage]);\r\n\r\n  // Filter students based on selected department, year, and search term\r\n  const filteredStudents = students; // Students are already filtered in fetchStudents\r\n\r\n  // Function to get filtered students for StudentList component\r\n  const getFilteredStudents = () => {\r\n    return filteredStudents;\r\n  };\r\n\r\n  const handleStudentClick = (student) => {\r\n    setSelectedStudent(student);\r\n    setEditedStudent({ ...student });\r\n    setIsEditing(false);\r\n    \r\n    // Update URL to include student ID\r\n    updateURL({ student_id: student.id });\r\n  };\r\n\r\n  const handleBackToList = () => {\r\n    setSelectedStudent(null);\r\n    setIsEditing(false);\r\n    setEditedStudent(null);\r\n    \r\n    // Remove student_id from URL\r\n    const params = new URLSearchParams(window.location.search);\r\n    params.delete('student_id');\r\n    const newURL = `${window.location.pathname}?${params.toString()}`;\r\n    window.history.pushState({}, '', newURL);\r\n  };\r\n\r\n  const handleBackToDepartments = () => {\r\n    setSelectedDepartment(null);\r\n    setSelectedYear('all');\r\n    setSearchTerm('');\r\n    setSelectedPassoutYear(null);\r\n    setCgpaMin('');\r\n    setCgpaMax('');\r\n    setCurrentPage(1);\r\n    \r\n    // Clear URL parameters\r\n    window.history.pushState({}, '', window.location.pathname);\r\n  };\r\n\r\n  // Handle department selection\r\n  const handleDepartmentSelect = (department) => {\r\n    setSelectedDepartment(department);\r\n    setCurrentPage(1);\r\n    updateURL({ department, page: '1' });\r\n    \r\n    // Fetch year statistics for this department\r\n    fetchYearStats(department);\r\n  };\r\n\r\n  // Handle passout year selection\r\n  const handlePassoutYearSelect = (passoutYear) => {\r\n    console.log('Selected passout year:', passoutYear, 'for department:', selectedDepartment);\r\n    setSelectedPassoutYear(passoutYear);\r\n    setCurrentPage(1);\r\n    // Ensure page parameter is explicitly included in URL\r\n    updateURL({ passout_year: passoutYear, page: '1' });\r\n  };\r\n\r\n  // Handle CGPA filter changes\r\n  const handleCgpaMinChange = (value) => {\r\n    setCgpaMin(value);\r\n    setCurrentPage(1);\r\n    updateURL({ cgpa_min: value, page: '1' });\r\n  };\r\n\r\n  const handleCgpaMaxChange = (value) => {\r\n    setCgpaMax(value);\r\n    setCurrentPage(1);\r\n    updateURL({ cgpa_max: value, page: '1' });\r\n  };\r\n\r\n  const handleSave = async () => {\r\n    try {\r\n      setLoading(true);\r\n\r\n      // Helper function to clean data\r\n      const cleanValue = (value) => {\r\n        if (value === '' || value === null || value === undefined) {\r\n          return null;\r\n        }\r\n        // Handle string values\r\n        if (typeof value === 'string') {\r\n          const trimmed = value.trim();\r\n          return trimmed === '' ? null : trimmed;\r\n        }\r\n        return value;\r\n      };\r\n\r\n      // Helper function to clean numeric values\r\n      const cleanNumericValue = (value) => {\r\n        if (value === '' || value === null || value === undefined) {\r\n          return null;\r\n        }\r\n        if (typeof value === 'string') {\r\n          const trimmed = value.trim();\r\n          if (trimmed === '') return null;\r\n          const parsed = parseInt(trimmed);\r\n          return isNaN(parsed) ? null : parsed;\r\n        }\r\n        if (typeof value === 'number') {\r\n          return isNaN(value) ? null : value;\r\n        }\r\n        return null;\r\n      };\r\n\r\n      // Helper function to clean string values specifically\r\n      const cleanStringValue = (value) => {\r\n        if (value === '' || value === null || value === undefined) {\r\n          return '';\r\n        }\r\n        return typeof value === 'string' ? value.trim() : String(value).trim();\r\n      };\r\n\r\n      // Split the name properly\r\n      const nameParts = editedStudent.name ? editedStudent.name.trim().split(' ') : [];\r\n      const firstName = nameParts[0] || '';\r\n      const lastName = nameParts.slice(1).join(' ') || '';\r\n\r\n      // Prepare the data for backend update\r\n      const updateData = {\r\n        // Basic information - ensure strings are not empty\r\n        first_name: cleanStringValue(firstName),\r\n        last_name: cleanStringValue(lastName),\r\n        student_id: cleanStringValue(editedStudent.rollNumber),\r\n        contact_email: cleanValue(editedStudent.email),\r\n        phone: cleanStringValue(editedStudent.phone),\r\n        branch: cleanStringValue(editedStudent.department),\r\n        gpa: cleanStringValue(editedStudent.gpa), // Overall CGPA as string\r\n\r\n        // Academic details - these should be integers\r\n        joining_year: cleanNumericValue(editedStudent.joining_year),\r\n        passout_year: cleanNumericValue(editedStudent.passout_year),\r\n\r\n        // Personal details\r\n        date_of_birth: cleanValue(editedStudent.dateOfBirth),\r\n        address: cleanStringValue(editedStudent.address),\r\n        city: cleanStringValue(editedStudent.city),\r\n        district: cleanStringValue(editedStudent.district),\r\n        state: cleanStringValue(editedStudent.state),\r\n        pincode: cleanStringValue(editedStudent.pincode),\r\n        country: cleanStringValue(editedStudent.country),\r\n        parent_contact: cleanStringValue(editedStudent.parentContact),\r\n        education: cleanStringValue(editedStudent.education),\r\n        skills: Array.isArray(editedStudent.skills) \r\n          ? editedStudent.skills.filter(skill => skill && skill.trim()).join(', ')\r\n          : cleanStringValue(editedStudent.skills),\r\n\r\n        // Academic scores - all as strings to match model\r\n        tenth_cgpa: cleanStringValue(editedStudent.tenth_cgpa),\r\n        tenth_percentage: cleanStringValue(editedStudent.tenth_percentage),\r\n        tenth_board: cleanStringValue(editedStudent.tenth_board),\r\n        tenth_school: cleanStringValue(editedStudent.tenth_school),\r\n        tenth_year_of_passing: cleanStringValue(editedStudent.tenth_year_of_passing),\r\n        tenth_location: cleanStringValue(editedStudent.tenth_location),\r\n        tenth_specialization: cleanStringValue(editedStudent.tenth_specialization),\r\n\r\n        twelfth_cgpa: cleanStringValue(editedStudent.twelfth_cgpa),\r\n        twelfth_percentage: cleanStringValue(editedStudent.twelfth_percentage),\r\n        twelfth_board: cleanStringValue(editedStudent.twelfth_board),\r\n        twelfth_school: cleanStringValue(editedStudent.twelfth_school),\r\n        twelfth_year_of_passing: cleanStringValue(editedStudent.twelfth_year_of_passing),\r\n        twelfth_location: cleanStringValue(editedStudent.twelfth_location),\r\n        twelfth_specialization: cleanStringValue(editedStudent.twelfth_specialization),\r\n      };\r\n\r\n      // Add semester CGPAs if they exist\r\n      if (editedStudent.semester_cgpas && Array.isArray(editedStudent.semester_cgpas)) {\r\n        editedStudent.semester_cgpas.forEach(semesterData => {\r\n          if (semesterData.semester >= 1 && semesterData.semester <= 8 && semesterData.cgpa) {\r\n            updateData[`semester${semesterData.semester}_cgpa`] = cleanStringValue(semesterData.cgpa);\r\n          }\r\n        });\r\n      }\r\n\r\n      // Remove empty string values but keep nulls for proper field clearing\r\n      const cleanedUpdateData = Object.fromEntries(\r\n        Object.entries(updateData).filter(([key, value]) => {\r\n          // Keep nulls for clearing fields, remove empty strings except for required fields\r\n          const requiredFields = ['first_name', 'last_name', 'student_id', 'gpa'];\r\n          if (requiredFields.includes(key)) {\r\n            return value !== null && value !== undefined;\r\n          }\r\n          return value !== null && value !== undefined && value !== '';\r\n        })\r\n      );\r\n\r\n      // Ensure required fields have default values if missing\r\n      if (!cleanedUpdateData.first_name) cleanedUpdateData.first_name = 'Student';\r\n      if (!cleanedUpdateData.last_name) cleanedUpdateData.last_name = '';\r\n      if (!cleanedUpdateData.student_id) cleanedUpdateData.student_id = `TEMP_${Date.now()}`;\r\n      if (!cleanedUpdateData.gpa) cleanedUpdateData.gpa = '0.0';\r\n\r\n      // Debug logging\r\n      console.log('Original editedStudent:', editedStudent);\r\n      console.log('Update data being sent:', cleanedUpdateData);\r\n      console.log('Student ID:', editedStudent.id);\r\n\r\n      // Make API call to update student\r\n      const updatedStudent = await studentsAPI.updateStudent(editedStudent.id, cleanedUpdateData);\r\n\r\n      // Update the student in the list with the response data\r\n      const updatedStudentData = {\r\n        ...editedStudent,\r\n        ...updatedStudent,\r\n        name: `${updatedStudent.first_name || ''} ${updatedStudent.last_name || ''}`.trim(),\r\n        rollNumber: updatedStudent.student_id,\r\n        email: updatedStudent.contact_email,\r\n        department: updatedStudent.branch,\r\n        gpa: updatedStudent.gpa,\r\n      };\r\n\r\n      setStudents(prev =>\r\n        prev.map(student =>\r\n          student.id === editedStudent.id ? updatedStudentData : student\r\n        )\r\n      );\r\n\r\n      setSelectedStudent(updatedStudentData);\r\n      setIsEditing(false);\r\n\r\n      // Show success message\r\n      showSuccess('Student Updated!', 'Student profile has been updated successfully.');\r\n\r\n    } catch (error) {\r\n      console.error('Error updating student:', error);\r\n      \r\n      // More detailed error logging\r\n      if (error.response) {\r\n        console.error('Error response status:', error.response.status);\r\n        console.error('Error response data:', error.response.data);\r\n        console.error('Error response headers:', error.response.headers);\r\n      }\r\n      \r\n      let errorMessage = 'Failed to update student profile. Please try again.';\r\n      \r\n      if (error.response?.data) {\r\n        // Handle validation errors\r\n        if (typeof error.response.data === 'object') {\r\n          const errorDetails = [];\r\n          for (const [field, messages] of Object.entries(error.response.data)) {\r\n            if (Array.isArray(messages)) {\r\n              errorDetails.push(`${field}: ${messages.join(', ')}`);\r\n            } else {\r\n              errorDetails.push(`${field}: ${messages}`);\r\n            }\r\n          }\r\n          if (errorDetails.length > 0) {\r\n            errorMessage = `Validation errors:\\n${errorDetails.join('\\n')}`;\r\n          }\r\n        } else if (error.response.data.detail) {\r\n          errorMessage = error.response.data.detail;\r\n        } else if (error.response.data.message) {\r\n          errorMessage = error.response.data.message;\r\n        }\r\n      }\r\n      \r\n      showValidationError('Update Failed', {\r\n        details: errorMessage\r\n      });\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    setEditedStudent({ ...selectedStudent });\r\n    setIsEditing(false);\r\n  };\r\n\r\n  const handleInputChange = (field, value) => {\r\n    setEditedStudent(prev => ({\r\n      ...prev,\r\n      [field]: value\r\n    }));\r\n  };\r\n\r\n  // Handle retry button click\r\n  const handleRetry = () => {\r\n    setIsRetrying(true);\r\n    fetchStudents();\r\n  };\r\n\r\n  // Help developers find the correct API endpoint\r\n  const debugBackend = () => {\r\n    window.open('http://localhost:8000/admin/');\r\n  };\r\n\r\n  // Handle pagination\r\n  const handlePageChange = (newPage) => {\r\n    if (newPage >= 1 && newPage <= totalPages && newPage !== currentPage) {\r\n      setCurrentPage(newPage);\r\n      updateURL({ page: newPage.toString() });\r\n      // fetchStudents will be called by useEffect\r\n    }\r\n  };\r\n\r\n  // Handle search input change (only update state, don't trigger search)\r\n  const handleSearchInputChange = (e) => {\r\n    const value = e.target.value;\r\n    setSearchTerm(value);\r\n    // Don't trigger immediate search, let button handle it\r\n  };\r\n\r\n  // Handle search input key press\r\n  const handleSearchKeyDown = (e) => {\r\n    if (e.key === 'Enter') {\r\n      e.preventDefault();\r\n      // If we're on a specific department and passout year page, use regular search\r\n      if (selectedDepartment && selectedPassoutYear) {\r\n        handleSearch();\r\n      } else {\r\n        // Otherwise use global search\r\n        handleGlobalSearch();\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleSearch = () => {\r\n    // Force immediate search with current search term\r\n    setCurrentPage(1);\r\n    updateURL({ search: searchTerm, page: '1' });\r\n    fetchStudents(1);\r\n  };\r\n\r\n  const handleGlobalSearch = async () => {\r\n    try {\r\n      setLoading(true);\r\n      setShowSearchResults(false);\r\n\r\n      const token = getAuthToken();\r\n      if (!token) {\r\n        throw new Error('No authentication token found. Please login first.');\r\n      }\r\n\r\n      const params = {\r\n        search: globalSearchTerm,\r\n        page_size: 10, // Limit results for popup\r\n      };\r\n\r\n      const response = await studentsAPI.getStudents(params);\r\n      const transformedResults = response.data.map(transformStudentData);\r\n\r\n      setSearchResults(transformedResults);\r\n      setShowSearchResults(true);\r\n      setLoading(false);\r\n    } catch (err) {\r\n      console.error('Error fetching search results:', err);\r\n      handleApiError(err, 'searching students');\r\n      setSearchResults([]);\r\n      setShowSearchResults(true);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const closeSearchResults = () => {\r\n    setShowSearchResults(false);\r\n    setSearchResults([]);\r\n  };\r\n\r\n  // Handle edit function - redirect to existing student profile edit page\r\n  const handleEdit = () => {\r\n    if (selectedStudent) {\r\n      // Set filters to match the student's department and passout year\r\n      setSelectedDepartment(selectedStudent.department);\r\n      setSelectedPassoutYear(selectedStudent.passout_year);\r\n      \r\n      // Update URL to include the student's department and year context\r\n      updateURL({ \r\n        department: selectedStudent.department, \r\n        passout_year: selectedStudent.passout_year, \r\n        student_id: selectedStudent.id \r\n      });\r\n      \r\n      // Enable editing mode\r\n      setIsEditing(true);\r\n    }\r\n  };\r\n\r\n  if (loading) return (\r\n    <div className=\"flex-1 p-6 ml-20 overflow-y-auto h-full\">\r\n      <div className=\"flex flex-col items-center justify-center h-full\">\r\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4\"></div>\r\n        <div className=\"text-gray-600\">Loading students...</div>\r\n      </div>\r\n    </div>\r\n  );\r\n  \r\n  if (error && students.length === 0) return (\r\n    <div className=\"flex-1 p-6 ml-20 overflow-y-auto h-full\">\r\n      <div className=\"flex flex-col items-center justify-center h-full\">\r\n        <div className=\"text-red-500 mb-4 text-center max-w-md\">\r\n          <p className=\"font-semibold text-lg mb-2\">Access Error</p>\r\n          <p>{error}</p>\r\n          <div className=\"mt-4 text-sm text-gray-600\">\r\n            <p>Possible solutions:</p>\r\n            <ul className=\"list-disc list-inside mt-2 text-left\">\r\n              <li>Make sure you're logged in with admin credentials</li>\r\n              <li>Check if your session has expired</li>\r\n              <li>Verify Django server is running on port 8000</li>\r\n              <li>Ensure proper permissions are set in Django</li>\r\n            </ul>\r\n          </div>\r\n        </div>\r\n        <div className=\"flex gap-3 mt-4\">\r\n          {!error.includes('login') && (\r\n            <button \r\n              onClick={handleRetry}\r\n              className=\"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\r\n              disabled={isRetrying}\r\n            >\r\n              <RefreshCw className={`w-4 h-4 ${isRetrying ? 'animate-spin' : ''}`} />\r\n              {isRetrying ? 'Retrying...' : 'Retry'}\r\n            </button>\r\n          )}\r\n          <button \r\n            onClick={() => window.location.href = '/login'}\r\n            className=\"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\"\r\n          >\r\n            <User className=\"w-4 h-4\" />\r\n            Go to Login\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <div className=\"flex-1 p-6 overflow-y-auto h-full\">\r\n      {/* Global Search Bar - Only show on main page (department cards view) */}\r\n      {!selectedDepartment && !selectedStudent && (\r\n        <div className=\"mb-6\">\r\n          <div className=\"flex items-center mb-4\">\r\n            <input\r\n              type=\"text\"\r\n              value={globalSearchTerm}\r\n              onChange={(e) => setGlobalSearchTerm(e.target.value)}\r\n              onKeyDown={handleSearchKeyDown}\r\n              placeholder=\"Search students globally...\"\r\n              className=\"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n            />\r\n            <button\r\n              onClick={handleGlobalSearch}\r\n              className=\"ml-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\r\n            >\r\n              <Search className=\"w-4 h-4 inline-block mr-1\" />\r\n              Search\r\n            </button>\r\n            <button\r\n              onClick={() => router.push('/admin/student-management/updatepage')}\r\n              className=\"ml-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2\"\r\n            >\r\n              <Upload className=\"w-4 h-4\" />\r\n              Upload Excel\r\n            </button>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Search Results Popup */}\r\n      {showSearchResults && (\r\n        <div className=\"fixed inset-0 bg-opacity-50 flex items-center justify-center z-50\">\r\n          <div className=\"bg-white rounded-lg shadow-lg p-6 w-3/4 max-w-4xl max-h-3/4 overflow-y-auto\">\r\n            <div className=\"flex justify-between items-center mb-4\">\r\n              <h2 className=\"text-lg font-semibold\">Global Search Results</h2>\r\n              <button\r\n                onClick={closeSearchResults}\r\n                className=\"text-gray-500 hover:text-gray-700\"\r\n              >\r\n                <X className=\"w-5 h-5\" />\r\n              </button>\r\n            </div>\r\n            {searchResults.length > 0 ? (\r\n              <div className=\"space-y-2\">\r\n                {searchResults.map((student) => (\r\n                  <div\r\n                    key={student.id}\r\n                    className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors\"\r\n                    onClick={() => {\r\n                      setSelectedStudent(student);\r\n                      setEditedStudent({ ...student });\r\n                      setShowSearchResults(false);\r\n                    }}\r\n                  >\r\n                    <div className=\"flex justify-between items-start\">\r\n                      <div>\r\n                        <p className=\"font-medium text-lg\">{student.name}</p>\r\n                        <p className=\"text-sm text-gray-600\">Roll: {student.rollNumber}</p>\r\n                        <p className=\"text-sm text-gray-600\">Department: {student.department}</p>\r\n                        <p className=\"text-sm text-gray-600\">Email: {student.email}</p>\r\n                      </div>\r\n                      <div className=\"text-right\">\r\n                        <p className=\"text-sm text-gray-500\">CGPA: {student.cgpa}</p>\r\n                        <p className=\"text-sm text-gray-500\">Year: {student.passout_year}</p>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            ) : (\r\n              <div className=\"text-center py-8\">\r\n                <p className=\"text-gray-600 text-lg\">No results found for \"{globalSearchTerm}\"</p>\r\n                <p className=\"text-gray-500 text-sm mt-2\">Try searching with different keywords like name, roll number, or email</p>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Existing content */}\r\n      {!selectedStudent ? (\r\n        <>\r\n          {!selectedDepartment ? (\r\n            <DepartmentCards\r\n              departmentOptions={departmentOptions}\r\n              departmentStats={departmentStats}\r\n              totalStudents={totalStudents}\r\n              onSelect={handleDepartmentSelect}\r\n            />\r\n          ) : !selectedPassoutYear ? (\r\n            <PassoutYearCards\r\n              departmentLabel={departmentOptions.find(d => d.value === selectedDepartment)?.label}\r\n              onBack={handleBackToDepartments}\r\n              getAvailablePassoutYears={getAvailablePassoutYears}\r\n              selectedDepartment={selectedDepartment}\r\n              onSelectYear={handlePassoutYearSelect}\r\n              yearStats={yearStats}\r\n              students={students}\r\n            />\r\n          ) : (\r\n            <StudentList\r\n              departmentLabel={departmentOptions.find(d => d.value === selectedDepartment)?.label}\r\n              passoutYear={selectedPassoutYear}\r\n              onBack={() => setSelectedPassoutYear(null)}\r\n              searchTerm={searchTerm}\r\n              handleSearchInputChange={handleSearchInputChange}\r\n              handleSearchKeyDown={handleSearchKeyDown}\r\n              cgpaMin={cgpaMin}\r\n              setCgpaMin={handleCgpaMinChange}\r\n              cgpaMax={cgpaMax}\r\n              setCgpaMax={handleCgpaMaxChange}\r\n              handleSearch={handleSearch}\r\n              getFilteredStudents={getFilteredStudents}\r\n              currentPage={currentPage}\r\n              handlePageChange={handlePageChange}\r\n              handleStudentClick={handleStudentClick}\r\n              loading={loading}\r\n              totalPages={totalPages}\r\n              totalStudents={totalStudents}\r\n            />\r\n          )}\r\n        </>\r\n      ) : (\r\n        <StudentProfile\r\n          selectedStudent={selectedStudent}\r\n          editedStudent={editedStudent}\r\n          isEditing={isEditing}\r\n          handleBackToList={handleBackToList}\r\n          handleEdit={handleEdit}\r\n          handleSave={handleSave}\r\n          handleCancel={handleCancel}\r\n          handleInputChange={handleInputChange}\r\n          departmentOptions={departmentOptions}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAtBA;;;;;;;;;;;;AAwBe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,EAAE,cAAc,EAAE,mBAAmB,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,kBAAe,AAAD;IAE3E,uCAAuC;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,GAAG,CAAC,aAAa;IAC3E,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,GAAG,CAAC,iBAAiB;IAC/F,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,GAAG,CAAC,WAAW;IAC7E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,aAAa,GAAG,CAAC,YAAY;IACrF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACzD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,GAAG,CAAC,mBAAmB;IACnG,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,GAAG,CAAC,eAAe;IACvE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,GAAG,CAAC,eAAe;IACvE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAErD,4CAA4C;IAC5C,MAAM,YAAY,CAAC,SAAS,CAAC,CAAC;QAC5B,MAAM,YAAY,IAAI;QAEtB,kDAAkD;QAClD,MAAM,gBAAgB;YACpB,MAAM,OAAO,IAAI,IAAI,YAAY,QAAQ;YACzC,YAAY,OAAO,UAAU,KAAK,YAAY,OAAO,UAAU,GAAG;YAClE,cAAc,OAAO,YAAY,KAAK,YAAY,OAAO,YAAY,GAAG;YACxE,QAAQ,OAAO,MAAM,KAAK,YAAY,OAAO,MAAM,GAAG;YACtD,UAAU,OAAO,QAAQ,KAAK,YAAY,OAAO,QAAQ,GAAG;YAC5D,UAAU,OAAO,QAAQ,KAAK,YAAY,OAAO,QAAQ,GAAG;YAC5D,GAAG,MAAM;QACX;QAEA,uCAAuC;QACvC,OAAO,OAAO,CAAC,eAAe,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YACjD,IAAI,SAAS,UAAU,SAAS,UAAU,MAAM,UAAU,UAAU,UAAU,MAAM;gBAClF,UAAU,GAAG,CAAC,KAAK;YACrB;QACF;QAEA,MAAM,SAAS,GAAG,OAAO,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,UAAU,QAAQ,IAAI;QACpE,OAAO,OAAO,CAAC,SAAS,CAAC,CAAC,GAAG,IAAI;IACnC;IAEA,mBAAmB;IACnB,MAAM,oBAAoB;QACxB;YAAE,OAAO;YAAoB,OAAO;QAAmB;QACvD;YAAE,OAAO;YAAe,OAAO;QAAc;QAC7C;YAAE,OAAO;YAAc,OAAO;QAAa;QAC3C;YAAE,OAAO;YAAS,OAAO;QAAQ;QACjC;YAAE,OAAO;YAAc,OAAO;QAAa;QAC3C;YAAE,OAAO;YAA0B,OAAO;QAAyB;QACnE;YAAE,OAAO;YAAY,OAAO;QAAW;QACvC;YAAE,OAAO;YAAiB,OAAO;QAAgB;KAClD;IAED,2CAA2C;IAC3C,MAAM,uBAAuB,CAAC,UAAY,CAAC;YACzC,IAAI,QAAQ,EAAE;YACd,YAAY,QAAQ,UAAU,IAAI;YAClC,MAAM,GAAG,QAAQ,UAAU,IAAI,GAAG,CAAC,EAAE,QAAQ,SAAS,IAAI,IAAI,CAAC,IAAI,MAAM;YACzE,OAAO,QAAQ,aAAa,IAAI,QAAQ,KAAK,IAAI;YACjD,OAAO,QAAQ,KAAK,IAAI;YACxB,YAAY,QAAQ,MAAM,IAAI;YAC9B,MAAM,kBAAkB,QAAQ,MAAM,EAAE;YACxC,MAAM,QAAQ,GAAG,IAAI;YACrB,KAAK,QAAQ,GAAG,IAAI;YACpB,SAAS,QAAQ,OAAO,IAAI;YAC5B,aAAa,QAAQ,aAAa,IAAI;YACtC,eAAe,QAAQ,cAAc,IAAI;YACzC,WAAW,QAAQ,SAAS,IAAI;YAChC,QAAQ,QAAQ,MAAM,IAAI,EAAE;YAE5B,mBAAmB;YACf,cAAc,QAAQ,YAAY,IAAI,QAAQ,cAAc,IAAI;YAChE,cAAc,QAAQ,YAAY,IAAI,QAAQ,eAAe,IAAI;YAEjE,oBAAoB;YACpB,cAAc,QAAQ,YAAY,IAAI,QAAQ,aAAa,IAAI;YAC/D,oBAAoB,QAAQ,kBAAkB,IAAI,QAAQ,mBAAmB,IAAI;YACjF,yBAAyB,QAAQ,uBAAuB,IAAI,QAAQ,aAAa,IAAI;YACrF,gBAAgB,QAAQ,cAAc,IAAI,QAAQ,eAAe,IAAI;YACrE,eAAe,QAAQ,aAAa,IAAI,QAAQ,cAAc,IAAI;YAClE,kBAAkB,QAAQ,gBAAgB,IAAI,QAAQ,iBAAiB,IAAI;YAC3E,wBAAwB,QAAQ,sBAAsB,IAAI,QAAQ,eAAe,IAAI;YAErF,kBAAkB;YAClB,YAAY,QAAQ,UAAU,IAAI,QAAQ,aAAa,IAAI;YAC3D,kBAAkB,QAAQ,gBAAgB,IAAI,QAAQ,mBAAmB,IAAI;YAC7E,uBAAuB,QAAQ,qBAAqB,IAAI,QAAQ,aAAa,IAAI;YACjF,cAAc,QAAQ,YAAY,IAAI,QAAQ,eAAe,IAAI;YACjE,aAAa,QAAQ,WAAW,IAAI,QAAQ,cAAc,IAAI;YAC9D,gBAAgB,QAAQ,cAAc,IAAI,QAAQ,iBAAiB,IAAI;YACvE,sBAAsB,QAAQ,oBAAoB,IAAI,QAAQ,eAAe,IAAI;YAEjF,kBAAkB;YAClB,MAAM,QAAQ,IAAI,IAAI;YACtB,UAAU,QAAQ,QAAQ,IAAI;YAC9B,OAAO,QAAQ,KAAK,IAAI;YACxB,SAAS,QAAQ,OAAO,IAAI,QAAQ,QAAQ,IAAI;YAChD,SAAS,QAAQ,OAAO,IAAI;YAE5B,mBAAmB;YACnB,mBAAmB,QAAQ,iBAAiB,IAAI,QAAQ,oBAAoB,IAAI;YAChF,qBAAqB,QAAQ,mBAAmB,IAAI,QAAQ,oBAAoB,IAAI;YACpF,uBAAuB,QAAQ,qBAAqB,IAAI,QAAQ,wBAAwB,IAAI;YAC5F,yBAAyB,QAAQ,uBAAuB,IAAI,QAAQ,wBAAwB,IAAI;YAEhG,iBAAiB;YACjB,QAAQ,QAAQ,MAAM,IAAI;YAC1B,YAAY,QAAQ,UAAU,IAAI;YAElC,oDAAoD;YACpD,gBAAgB,QAAQ,mBAAmB,IAAI,EAAE;YACjD,qBAAqB,QAAQ,mBAAmB,IAAI,EAAE;QACxD,CAAC;IAEL,kEAAkE;IAClE,oBAAoB;IACpB,qCAAqC;IACrC,0CAA0C;IAC1C,4BAA4B;IAE5B,sCAAsC;IACtC,oBAAoB;IAEpB,2DAA2D;IAC3D,MAAM,gBAAgB,OAAO,OAAO,CAAC;QACnC,IAAI;YACF,WAAW;YACX,SAAS;YACT,cAAc;YAEd,MAAM,QAAQ,CAAA,GAAA,uHAAA,CAAA,eAAY,AAAD;YACzB,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,oDAAoD;YACpD,MAAM,SAAS;gBACb;gBACA,WAAW;gBACX,UAAU;YACZ;YAEA,oBAAoB;YACpB,IAAI,YAAY;gBACd,OAAO,MAAM,GAAG;YAClB;YAEA,wBAAwB;YACxB,IAAI,oBAAoB;gBACtB,OAAO,UAAU,GAAG;YACtB;YAEA,gDAAgD;YAChD,IAAI,qBAAqB;gBACvB,OAAO,YAAY,GAAG;YACxB;YAEA,sDAAsD;YACtD,IAAI,iBAAiB,OAAO;gBAC1B,OAAO,IAAI,GAAG;YAChB;YAEA,mBAAmB;YACnB,IAAI,SAAS;gBACX,OAAO,QAAQ,GAAG;YACpB;YACA,IAAI,SAAS;gBACX,OAAO,QAAQ,GAAG;YACpB;YAEA,gBAAgB;YAChB,QAAQ,GAAG,CAAC,kCAAkC;YAE9C,gCAAgC;YAChC,MAAM,WAAW,MAAM,0HAAA,CAAA,cAAW,CAAC,WAAW,CAAC;YAE/C,qBAAqB;YACrB,MAAM,sBAAsB,SAAS,IAAI,CAAC,GAAG,CAAC;YAE9C,8EAA8E;YAC9E,YAAY;YACZ,eAAe;YACf,cAAc,SAAS,UAAU,CAAC,WAAW;YAC7C,iBAAiB,SAAS,UAAU,CAAC,WAAW;YAEhD,4DAA4D;YAC5D,IAAI,SAAS,QAAQ,EAAE;gBACrB,IAAI,SAAS,QAAQ,CAAC,eAAe,EAAE;oBACrC,kBAAkB,SAAS,QAAQ,CAAC,eAAe;gBACrD;gBACA,IAAI,SAAS,QAAQ,CAAC,qBAAqB,EAAE;oBAC3C,qCAAqC;oBACrC,MAAM,YAAY,SAAS,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;4BACrE,YAAY;4BACZ,OAAO,EAAE,qEAAqE;wBAChF,CAAC;oBACD,mBAAmB;gBACrB;YACF;YAEA,WAAW;QACb,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,4BAA4B;YAE1C,IAAI,IAAI,QAAQ,EAAE,WAAW,KAAK;gBAChC,eAAe,KAAK;gBACpB,SAAS;YACX,OAAO,IAAI,IAAI,QAAQ,EAAE,WAAW,KAAK;gBACvC,eAAe,KAAK;gBACpB,SAAS;YACX,OAAO,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU;gBACxC,eAAe,KAAK;gBACpB,SAAS;YACX,OAAO;gBACL,eAAe,KAAK;gBACpB,SAAS,CAAC,OAAO,EAAE,IAAI,OAAO,EAAE;YAClC;YAEA,YAAY,EAAE;YACd,WAAW;QACb;IACF;IAEA,kDAAkD;IAClD,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,MAAM,QAAQ,CAAA,GAAA,uHAAA,CAAA,eAAY,AAAD;YACzB,IAAI,CAAC,OAAO;YAEZ,QAAQ,GAAG,CAAC,uCAAuC;YAEnD,sEAAsE;YACtE,MAAM,SAAS,MAAM,0HAAA,CAAA,oBAAiB,CAAC,YAAY,CAAC,MAAM,OAAO;YAEjE,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,KAAK,EAAE;gBACtD,QAAQ,GAAG,CAAC,wBAAwB,OAAO,IAAI,CAAC,KAAK;gBACrD,aAAa,OAAO,IAAI,CAAC,KAAK;YAChC,OAAO;gBACL,QAAQ,KAAK,CAAC,+BAA+B,OAAO,KAAK;gBAEzD,iDAAiD;gBACjD,MAAM,SAAS;oBACb,YAAY;oBACZ,WAAW;oBACX,YAAY,KAAK,kDAAkD;gBACrE;gBAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,cAAW,CAAC,WAAW,CAAC;gBAC/C,QAAQ,GAAG,CAAC,wBAAwB;gBAEpC,8DAA8D;gBAC9D,IAAI,SAAS,IAAI,EAAE;oBACjB,MAAM,aAAa,CAAC;oBACpB,MAAM,cAAc,IAAI,OAAO,WAAW;oBAE1C,SAAS,IAAI,CAAC,OAAO,CAAC,CAAA;wBACpB,sCAAsC;wBACtC,MAAM,cAAc,QAAQ,YAAY,IACrB,QAAQ,eAAe,IACvB,QAAQ,wBAAwB,IAChC,QAAQ,WAAW;wBAEtC,IAAI,aAAa;4BACf,MAAM,OAAO,YAAY,QAAQ;4BACjC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE;gCACrB,UAAU,CAAC,KAAK,GAAG;oCACjB,cAAc,SAAS;oCACvB,gBAAgB;oCAChB,uBAAuB;gCACzB;4BACF;4BACA,UAAU,CAAC,KAAK,CAAC,cAAc;4BAE/B,yEAAyE;4BACzE,IAAI,SAAS,UAAU,eAAe,SAAS,UAAU,cAAc,GAAG;gCACxE,UAAU,CAAC,KAAK,CAAC,qBAAqB;4BACxC;wBACF;oBACF;oBAEA,MAAM,sBAAsB,OAAO,MAAM,CAAC;oBAC1C,QAAQ,GAAG,CAAC,yCAAyC,YAAY;oBACjE,aAAa;gBACf;YACF;QAEF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,aAAa,EAAE;QACjB;IACF;IAEA,+EAA+E;IAC/E,MAAM,oBAAoB,CAAC,QAAQ;QACjC,IAAI,WAAW,QAAQ,YAAY,IAAI,QAAQ,YAAY,EAAE;YAC3D,OAAO,GAAG,QAAQ,YAAY,CAAC,CAAC,EAAE,QAAQ,YAAY,EAAE;QAC1D;QACA,OAAO;IACT;IAEA,qBAAqB;IACrB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR;QACF;sCAAG,EAAE;IAEL,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM;8DAAiB;oBACrB,MAAM,eAAe,IAAI,gBAAgB,OAAO,QAAQ,CAAC,MAAM;oBAC/D,eAAe,SAAS,aAAa,GAAG,CAAC,YAAY;oBACrD,sBAAsB,aAAa,GAAG,CAAC,iBAAiB;oBACxD,uBAAuB,aAAa,GAAG,CAAC,mBAAmB;oBAC3D,cAAc,aAAa,GAAG,CAAC,aAAa;oBAC5C,WAAW,aAAa,GAAG,CAAC,eAAe;oBAC3C,WAAW,aAAa,GAAG,CAAC,eAAe;oBAE3C,iCAAiC;oBACjC,cAAc,SAAS,aAAa,GAAG,CAAC,YAAY;gBACtD;;YAEA,OAAO,gBAAgB,CAAC,YAAY;YACpC;+CAAO,IAAM,OAAO,mBAAmB,CAAC,YAAY;;QACtD;sCAAG,EAAE;IAEL,wCAAwC;IACxC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM,YAAY,aAAa,GAAG,CAAC;YACnC,IAAI,aAAa,SAAS,MAAM,GAAG,GAAG;gBACpC,MAAM,UAAU,SAAS,IAAI;2DAAC,CAAA,IAAK,EAAE,EAAE,CAAC,QAAQ,OAAO;;gBACvD,IAAI,SAAS;oBACX,mBAAmB;oBACnB,iBAAiB;wBAAE,GAAG,OAAO;oBAAC;gBAChC;YACF;QACF;sCAAG;QAAC;QAAU;KAAa;IAE3B,mDAAmD;IACnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,iCAAiC;YACjC,MAAM,QAAQ,CAAA,GAAA,uHAAA,CAAA,eAAY,AAAD;YACzB,IAAI,CAAC,OAAO;gBACV,8CAA8C;gBAC9C,SAAS;gBACT,WAAW;gBACX;YACF;YAEA;QACF;sCAAG,EAAE;IAEL,mFAAmF;IACnF,MAAM,uBAAuB,CAAC;QAC5B,IAAI,aAAa,UAAU,MAAM,IAAI,GAAG;YACtC,MAAM,WAAW,UAAU,SAAS,CAAC,GAAG;YACxC,IAAI,CAAC,MAAM,WAAW;gBACpB,OAAO,GAAG,IAAI,CAAC,IAAI,OAAO,WAAW,KAAK,SAAS,SAAS,EAAE,OAAO,CAAC;YACxE;QACF;QACA,OAAO;IACT;IAEA,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM;kEAAqB,CAAC;oBAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAG;wBACtE,sBAAsB;oBACxB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;+CAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;sCAAG,EAAE;IAEL,gDAAgD;IAChD,MAAM,2BAA2B;QAC/B,OAAO;IACT;IAEA,yCAAyC;IACzC,MAAM,oBAAoB,CAAC;QACzB,MAAM,QAAQ;eAAI,IAAI,IAAI,aAAa,GAAG,CAAC,CAAA,UAAW,QAAQ,IAAI,EAAE,MAAM,CAAC,CAAA,OAAQ,QAAQ,SAAS;SAAQ;QAC5G,OAAO,MAAM,IAAI;IACnB;IAEA,4BAA4B;IAC5B,MAAM,qBAAqB,CAAC;QAC1B,MAAM,QAAQ,CAAC;QACf,aAAa,OAAO,CAAC,CAAA;YACnB,IAAI,QAAQ,UAAU,IAAI,QAAQ,UAAU,KAAK,OAAO;gBACtD,KAAK,CAAC,QAAQ,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,UAAU,CAAC,IAAI,CAAC,IAAI;YACjE;QACF;QACA,OAAO,OAAO,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,YAAY,MAAM,GAAK,CAAC;gBAAE;gBAAY;YAAM,CAAC;IAClF;IAEA,gEAAgE;IAChE,mDAAmD;IAEnD,wDAAwD;IAExD,sEAAsE;IACtE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,eAAe,IAAI,sCAAsC;YACzD,cAAc,IAAI,sCAAsC;QAC1D;sCAAG;QAAC;QAAoB;QAAc;QAAqB;QAAS;QAAS;KAAS;IAEtF,6DAA6D;IAC7D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,cAAc,GAAG;gBACnB,cAAc;YAChB;QACF;sCAAG;QAAC;KAAY;IAEhB,sEAAsE;IACtE,MAAM,mBAAmB,UAAU,iDAAiD;IAEpF,8DAA8D;IAC9D,MAAM,sBAAsB;QAC1B,OAAO;IACT;IAEA,MAAM,qBAAqB,CAAC;QAC1B,mBAAmB;QACnB,iBAAiB;YAAE,GAAG,OAAO;QAAC;QAC9B,aAAa;QAEb,mCAAmC;QACnC,UAAU;YAAE,YAAY,QAAQ,EAAE;QAAC;IACrC;IAEA,MAAM,mBAAmB;QACvB,mBAAmB;QACnB,aAAa;QACb,iBAAiB;QAEjB,6BAA6B;QAC7B,MAAM,SAAS,IAAI,gBAAgB,OAAO,QAAQ,CAAC,MAAM;QACzD,OAAO,MAAM,CAAC;QACd,MAAM,SAAS,GAAG,OAAO,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI;QACjE,OAAO,OAAO,CAAC,SAAS,CAAC,CAAC,GAAG,IAAI;IACnC;IAEA,MAAM,0BAA0B;QAC9B,sBAAsB;QACtB,gBAAgB;QAChB,cAAc;QACd,uBAAuB;QACvB,WAAW;QACX,WAAW;QACX,eAAe;QAEf,uBAAuB;QACvB,OAAO,OAAO,CAAC,SAAS,CAAC,CAAC,GAAG,IAAI,OAAO,QAAQ,CAAC,QAAQ;IAC3D;IAEA,8BAA8B;IAC9B,MAAM,yBAAyB,CAAC;QAC9B,sBAAsB;QACtB,eAAe;QACf,UAAU;YAAE;YAAY,MAAM;QAAI;QAElC,4CAA4C;QAC5C,eAAe;IACjB;IAEA,gCAAgC;IAChC,MAAM,0BAA0B,CAAC;QAC/B,QAAQ,GAAG,CAAC,0BAA0B,aAAa,mBAAmB;QACtE,uBAAuB;QACvB,eAAe;QACf,sDAAsD;QACtD,UAAU;YAAE,cAAc;YAAa,MAAM;QAAI;IACnD;IAEA,6BAA6B;IAC7B,MAAM,sBAAsB,CAAC;QAC3B,WAAW;QACX,eAAe;QACf,UAAU;YAAE,UAAU;YAAO,MAAM;QAAI;IACzC;IAEA,MAAM,sBAAsB,CAAC;QAC3B,WAAW;QACX,eAAe;QACf,UAAU;YAAE,UAAU;YAAO,MAAM;QAAI;IACzC;IAEA,MAAM,aAAa;QACjB,IAAI;YACF,WAAW;YAEX,gCAAgC;YAChC,MAAM,aAAa,CAAC;gBAClB,IAAI,UAAU,MAAM,UAAU,QAAQ,UAAU,WAAW;oBACzD,OAAO;gBACT;gBACA,uBAAuB;gBACvB,IAAI,OAAO,UAAU,UAAU;oBAC7B,MAAM,UAAU,MAAM,IAAI;oBAC1B,OAAO,YAAY,KAAK,OAAO;gBACjC;gBACA,OAAO;YACT;YAEA,0CAA0C;YAC1C,MAAM,oBAAoB,CAAC;gBACzB,IAAI,UAAU,MAAM,UAAU,QAAQ,UAAU,WAAW;oBACzD,OAAO;gBACT;gBACA,IAAI,OAAO,UAAU,UAAU;oBAC7B,MAAM,UAAU,MAAM,IAAI;oBAC1B,IAAI,YAAY,IAAI,OAAO;oBAC3B,MAAM,SAAS,SAAS;oBACxB,OAAO,MAAM,UAAU,OAAO;gBAChC;gBACA,IAAI,OAAO,UAAU,UAAU;oBAC7B,OAAO,MAAM,SAAS,OAAO;gBAC/B;gBACA,OAAO;YACT;YAEA,sDAAsD;YACtD,MAAM,mBAAmB,CAAC;gBACxB,IAAI,UAAU,MAAM,UAAU,QAAQ,UAAU,WAAW;oBACzD,OAAO;gBACT;gBACA,OAAO,OAAO,UAAU,WAAW,MAAM,IAAI,KAAK,OAAO,OAAO,IAAI;YACtE;YAEA,0BAA0B;YAC1B,MAAM,YAAY,cAAc,IAAI,GAAG,cAAc,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,OAAO,EAAE;YAChF,MAAM,YAAY,SAAS,CAAC,EAAE,IAAI;YAClC,MAAM,WAAW,UAAU,KAAK,CAAC,GAAG,IAAI,CAAC,QAAQ;YAEjD,sCAAsC;YACtC,MAAM,aAAa;gBACjB,mDAAmD;gBACnD,YAAY,iBAAiB;gBAC7B,WAAW,iBAAiB;gBAC5B,YAAY,iBAAiB,cAAc,UAAU;gBACrD,eAAe,WAAW,cAAc,KAAK;gBAC7C,OAAO,iBAAiB,cAAc,KAAK;gBAC3C,QAAQ,iBAAiB,cAAc,UAAU;gBACjD,KAAK,iBAAiB,cAAc,GAAG;gBAEvC,8CAA8C;gBAC9C,cAAc,kBAAkB,cAAc,YAAY;gBAC1D,cAAc,kBAAkB,cAAc,YAAY;gBAE1D,mBAAmB;gBACnB,eAAe,WAAW,cAAc,WAAW;gBACnD,SAAS,iBAAiB,cAAc,OAAO;gBAC/C,MAAM,iBAAiB,cAAc,IAAI;gBACzC,UAAU,iBAAiB,cAAc,QAAQ;gBACjD,OAAO,iBAAiB,cAAc,KAAK;gBAC3C,SAAS,iBAAiB,cAAc,OAAO;gBAC/C,SAAS,iBAAiB,cAAc,OAAO;gBAC/C,gBAAgB,iBAAiB,cAAc,aAAa;gBAC5D,WAAW,iBAAiB,cAAc,SAAS;gBACnD,QAAQ,MAAM,OAAO,CAAC,cAAc,MAAM,IACtC,cAAc,MAAM,CAAC,MAAM,CAAC,CAAA,QAAS,SAAS,MAAM,IAAI,IAAI,IAAI,CAAC,QACjE,iBAAiB,cAAc,MAAM;gBAEzC,kDAAkD;gBAClD,YAAY,iBAAiB,cAAc,UAAU;gBACrD,kBAAkB,iBAAiB,cAAc,gBAAgB;gBACjE,aAAa,iBAAiB,cAAc,WAAW;gBACvD,cAAc,iBAAiB,cAAc,YAAY;gBACzD,uBAAuB,iBAAiB,cAAc,qBAAqB;gBAC3E,gBAAgB,iBAAiB,cAAc,cAAc;gBAC7D,sBAAsB,iBAAiB,cAAc,oBAAoB;gBAEzE,cAAc,iBAAiB,cAAc,YAAY;gBACzD,oBAAoB,iBAAiB,cAAc,kBAAkB;gBACrE,eAAe,iBAAiB,cAAc,aAAa;gBAC3D,gBAAgB,iBAAiB,cAAc,cAAc;gBAC7D,yBAAyB,iBAAiB,cAAc,uBAAuB;gBAC/E,kBAAkB,iBAAiB,cAAc,gBAAgB;gBACjE,wBAAwB,iBAAiB,cAAc,sBAAsB;YAC/E;YAEA,mCAAmC;YACnC,IAAI,cAAc,cAAc,IAAI,MAAM,OAAO,CAAC,cAAc,cAAc,GAAG;gBAC/E,cAAc,cAAc,CAAC,OAAO,CAAC,CAAA;oBACnC,IAAI,aAAa,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,aAAa,IAAI,EAAE;wBACjF,UAAU,CAAC,CAAC,QAAQ,EAAE,aAAa,QAAQ,CAAC,KAAK,CAAC,CAAC,GAAG,iBAAiB,aAAa,IAAI;oBAC1F;gBACF;YACF;YAEA,sEAAsE;YACtE,MAAM,oBAAoB,OAAO,WAAW,CAC1C,OAAO,OAAO,CAAC,YAAY,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC7C,kFAAkF;gBAClF,MAAM,iBAAiB;oBAAC;oBAAc;oBAAa;oBAAc;iBAAM;gBACvE,IAAI,eAAe,QAAQ,CAAC,MAAM;oBAChC,OAAO,UAAU,QAAQ,UAAU;gBACrC;gBACA,OAAO,UAAU,QAAQ,UAAU,aAAa,UAAU;YAC5D;YAGF,wDAAwD;YACxD,IAAI,CAAC,kBAAkB,UAAU,EAAE,kBAAkB,UAAU,GAAG;YAClE,IAAI,CAAC,kBAAkB,SAAS,EAAE,kBAAkB,SAAS,GAAG;YAChE,IAAI,CAAC,kBAAkB,UAAU,EAAE,kBAAkB,UAAU,GAAG,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;YACtF,IAAI,CAAC,kBAAkB,GAAG,EAAE,kBAAkB,GAAG,GAAG;YAEpD,gBAAgB;YAChB,QAAQ,GAAG,CAAC,2BAA2B;YACvC,QAAQ,GAAG,CAAC,2BAA2B;YACvC,QAAQ,GAAG,CAAC,eAAe,cAAc,EAAE;YAE3C,kCAAkC;YAClC,MAAM,iBAAiB,MAAM,0HAAA,CAAA,cAAW,CAAC,aAAa,CAAC,cAAc,EAAE,EAAE;YAEzE,wDAAwD;YACxD,MAAM,qBAAqB;gBACzB,GAAG,aAAa;gBAChB,GAAG,cAAc;gBACjB,MAAM,GAAG,eAAe,UAAU,IAAI,GAAG,CAAC,EAAE,eAAe,SAAS,IAAI,IAAI,CAAC,IAAI;gBACjF,YAAY,eAAe,UAAU;gBACrC,OAAO,eAAe,aAAa;gBACnC,YAAY,eAAe,MAAM;gBACjC,KAAK,eAAe,GAAG;YACzB;YAEA,YAAY,CAAA,OACV,KAAK,GAAG,CAAC,CAAA,UACP,QAAQ,EAAE,KAAK,cAAc,EAAE,GAAG,qBAAqB;YAI3D,mBAAmB;YACnB,aAAa;YAEb,uBAAuB;YACvB,YAAY,oBAAoB;QAElC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YAEzC,8BAA8B;YAC9B,IAAI,MAAM,QAAQ,EAAE;gBAClB,QAAQ,KAAK,CAAC,0BAA0B,MAAM,QAAQ,CAAC,MAAM;gBAC7D,QAAQ,KAAK,CAAC,wBAAwB,MAAM,QAAQ,CAAC,IAAI;gBACzD,QAAQ,KAAK,CAAC,2BAA2B,MAAM,QAAQ,CAAC,OAAO;YACjE;YAEA,IAAI,eAAe;YAEnB,IAAI,MAAM,QAAQ,EAAE,MAAM;gBACxB,2BAA2B;gBAC3B,IAAI,OAAO,MAAM,QAAQ,CAAC,IAAI,KAAK,UAAU;oBAC3C,MAAM,eAAe,EAAE;oBACvB,KAAK,MAAM,CAAC,OAAO,SAAS,IAAI,OAAO,OAAO,CAAC,MAAM,QAAQ,CAAC,IAAI,EAAG;wBACnE,IAAI,MAAM,OAAO,CAAC,WAAW;4BAC3B,aAAa,IAAI,CAAC,GAAG,MAAM,EAAE,EAAE,SAAS,IAAI,CAAC,OAAO;wBACtD,OAAO;4BACL,aAAa,IAAI,CAAC,GAAG,MAAM,EAAE,EAAE,UAAU;wBAC3C;oBACF;oBACA,IAAI,aAAa,MAAM,GAAG,GAAG;wBAC3B,eAAe,CAAC,oBAAoB,EAAE,aAAa,IAAI,CAAC,OAAO;oBACjE;gBACF,OAAO,IAAI,MAAM,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE;oBACrC,eAAe,MAAM,QAAQ,CAAC,IAAI,CAAC,MAAM;gBAC3C,OAAO,IAAI,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE;oBACtC,eAAe,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;gBAC5C;YACF;YAEA,oBAAoB,iBAAiB;gBACnC,SAAS;YACX;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe;QACnB,iBAAiB;YAAE,GAAG,eAAe;QAAC;QACtC,aAAa;IACf;IAEA,MAAM,oBAAoB,CAAC,OAAO;QAChC,iBAAiB,CAAA,OAAQ,CAAC;gBACxB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,4BAA4B;IAC5B,MAAM,cAAc;QAClB,cAAc;QACd;IACF;IAEA,gDAAgD;IAChD,MAAM,eAAe;QACnB,OAAO,IAAI,CAAC;IACd;IAEA,oBAAoB;IACpB,MAAM,mBAAmB,CAAC;QACxB,IAAI,WAAW,KAAK,WAAW,cAAc,YAAY,aAAa;YACpE,eAAe;YACf,UAAU;gBAAE,MAAM,QAAQ,QAAQ;YAAG;QACrC,4CAA4C;QAC9C;IACF;IAEA,uEAAuE;IACvE,MAAM,0BAA0B,CAAC;QAC/B,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,cAAc;IACd,uDAAuD;IACzD;IAEA,gCAAgC;IAChC,MAAM,sBAAsB,CAAC;QAC3B,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB,EAAE,cAAc;YAChB,8EAA8E;YAC9E,IAAI,sBAAsB,qBAAqB;gBAC7C;YACF,OAAO;gBACL,8BAA8B;gBAC9B;YACF;QACF;IACF;IAEA,MAAM,eAAe;QACnB,kDAAkD;QAClD,eAAe;QACf,UAAU;YAAE,QAAQ;YAAY,MAAM;QAAI;QAC1C,cAAc;IAChB;IAEA,MAAM,qBAAqB;QACzB,IAAI;YACF,WAAW;YACX,qBAAqB;YAErB,MAAM,QAAQ,CAAA,GAAA,uHAAA,CAAA,eAAY,AAAD;YACzB,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,SAAS;gBACb,QAAQ;gBACR,WAAW;YACb;YAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,cAAW,CAAC,WAAW,CAAC;YAC/C,MAAM,qBAAqB,SAAS,IAAI,CAAC,GAAG,CAAC;YAE7C,iBAAiB;YACjB,qBAAqB;YACrB,WAAW;QACb,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,kCAAkC;YAChD,eAAe,KAAK;YACpB,iBAAiB,EAAE;YACnB,qBAAqB;YACrB,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB;QACzB,qBAAqB;QACrB,iBAAiB,EAAE;IACrB;IAEA,wEAAwE;IACxE,MAAM,aAAa;QACjB,IAAI,iBAAiB;YACnB,iEAAiE;YACjE,sBAAsB,gBAAgB,UAAU;YAChD,uBAAuB,gBAAgB,YAAY;YAEnD,kEAAkE;YAClE,UAAU;gBACR,YAAY,gBAAgB,UAAU;gBACtC,cAAc,gBAAgB,YAAY;gBAC1C,YAAY,gBAAgB,EAAE;YAChC;YAEA,sBAAsB;YACtB,aAAa;QACf;IACF;IAEA,IAAI,SAAS,qBACX,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAI,WAAU;8BAAgB;;;;;;;;;;;;;;;;;IAKrC,IAAI,SAAS,SAAS,MAAM,KAAK,GAAG,qBAClC,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAC1C,6LAAC;sCAAG;;;;;;sCACJ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CAAE;;;;;;8CACH,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;8BAIV,6LAAC;oBAAI,WAAU;;wBACZ,CAAC,MAAM,QAAQ,CAAC,0BACf,6LAAC;4BACC,SAAS;4BACT,WAAU;4BACV,UAAU;;8CAEV,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAW,CAAC,QAAQ,EAAE,aAAa,iBAAiB,IAAI;;;;;;gCAClE,aAAa,gBAAgB;;;;;;;sCAGlC,6LAAC;4BACC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;4BACtC,WAAU;;8CAEV,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;;;;;;;;;;;;;IAQtC,qBACE,6LAAC;QAAI,WAAU;;YAEZ,CAAC,sBAAsB,CAAC,iCACvB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,MAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;4BACnD,WAAW;4BACX,aAAY;4BACZ,WAAU;;;;;;sCAEZ,6LAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAA8B;;;;;;;sCAGlD,6LAAC;4BACC,SAAS,IAAM,OAAO,IAAI,CAAC;4BAC3B,WAAU;;8CAEV,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;;;;;;;YAQrC,mCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;wBAGhB,cAAc,MAAM,GAAG,kBACtB,6LAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,wBAClB,6LAAC;oCAEC,WAAU;oCACV,SAAS;wCACP,mBAAmB;wCACnB,iBAAiB;4CAAE,GAAG,OAAO;wCAAC;wCAC9B,qBAAqB;oCACvB;8CAEA,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAuB,QAAQ,IAAI;;;;;;kEAChD,6LAAC;wDAAE,WAAU;;4DAAwB;4DAAO,QAAQ,UAAU;;;;;;;kEAC9D,6LAAC;wDAAE,WAAU;;4DAAwB;4DAAa,QAAQ,UAAU;;;;;;;kEACpE,6LAAC;wDAAE,WAAU;;4DAAwB;4DAAQ,QAAQ,KAAK;;;;;;;;;;;;;0DAE5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;;4DAAwB;4DAAO,QAAQ,IAAI;;;;;;;kEACxD,6LAAC;wDAAE,WAAU;;4DAAwB;4DAAO,QAAQ,YAAY;;;;;;;;;;;;;;;;;;;mCAjB/D,QAAQ,EAAE;;;;;;;;;iDAwBrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;;wCAAwB;wCAAuB;wCAAiB;;;;;;;8CAC7E,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;;;;;;;;;;;;YAQnD,CAAC,gCACA;0BACG,CAAC,mCACA,6LAAC,mKAAA,CAAA,UAAe;oBACd,mBAAmB;oBACnB,iBAAiB;oBACjB,eAAe;oBACf,UAAU;;;;;2BAEV,CAAC,oCACH,6LAAC,oKAAA,CAAA,UAAgB;oBACf,iBAAiB,kBAAkB,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,qBAAqB;oBAC9E,QAAQ;oBACR,0BAA0B;oBAC1B,oBAAoB;oBACpB,cAAc;oBACd,WAAW;oBACX,UAAU;;;;;yCAGZ,6LAAC,+JAAA,CAAA,UAAW;oBACV,iBAAiB,kBAAkB,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,qBAAqB;oBAC9E,aAAa;oBACb,QAAQ,IAAM,uBAAuB;oBACrC,YAAY;oBACZ,yBAAyB;oBACzB,qBAAqB;oBACrB,SAAS;oBACT,YAAY;oBACZ,SAAS;oBACT,YAAY;oBACZ,cAAc;oBACd,qBAAqB;oBACrB,aAAa;oBACb,kBAAkB;oBAClB,oBAAoB;oBACpB,SAAS;oBACT,YAAY;oBACZ,eAAe;;;;;;8CAKrB,6LAAC,kKAAA,CAAA,UAAc;gBACb,iBAAiB;gBACjB,eAAe;gBACf,WAAW;gBACX,kBAAkB;gBAClB,YAAY;gBACZ,YAAY;gBACZ,cAAc;gBACd,mBAAmB;gBACnB,mBAAmB;;;;;;;;;;;;AAK7B;GAz+BwB;;QACP,qIAAA,CAAA,YAAS;QACH,qIAAA,CAAA,kBAAe;QACyB,0IAAA,CAAA,kBAAe;;;KAHtD", "debugId": null}}]}