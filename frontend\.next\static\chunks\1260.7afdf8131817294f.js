"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1260],{21260:(a,t,e)=>{e.d(t,{Ly:()=>r,_N:()=>i,companies:()=>o,zZ:()=>c});var n=e(48937);let o=[{id:1,name:"TechCorp Inc",description:"Leading technology solutions provider",industry:"Technology",size:"500-1000",founded:"2010",website:"https://techcorp.com"},{id:2,name:"DataCorp",description:"Data analytics and insights company",industry:"Data Analytics",size:"100-500",founded:"2015",website:"https://datacorp.com"}],r=[{id:1,job_id:25,title:"Software Engineer",company:"TechCorp Inc",status:"APPLIED",application_deadline:"2024-05-30T23:59:59Z"},{id:2,job_id:26,title:"Data Scientist",company:"DataCorp",status:"INTERVIEW SCHEDULED",application_deadline:"2024-06-15T23:59:59Z"}],i=async function(){let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{let t={...a,_t:new Date().getTime()};console.log("Fetching companies with cache busting...");let e=await (0,n.mm)(t),r=[];e.data&&Array.isArray(e.data)?r=e.data:e.data&&e.data.results&&Array.isArray(e.data.results)?r=e.data.results:e.data&&e.data.data&&Array.isArray(e.data.data)&&(r=e.data.data);let i=r.map(n.Y_);if(console.log("Fetched ".concat(i.length," companies from API")),0===i.length)return console.warn("API returned empty companies array, using static data"),o;return i}catch(a){console.error("Error fetching companies:",a);try{console.log("Trying alternate endpoint format...");let a=await fetch("/api/v1/college/default-college/companies/");if(a.ok){let t=await a.json(),e=Array.isArray(t)?t:t.data||t.results||[];if(e.length>0)return console.log("Successfully retrieved companies from alternate endpoint"),e.map(n.Y_)}}catch(a){console.error("Alternate endpoint also failed:",a)}return o}};function c(a){return console.log("Fetching jobs for company ID: ".concat(a)),[]}}}]);