(()=>{var e={};e.id=5085,e.ids=[1286,4335,5085],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},14335:(e,t,s)=>{"use strict";s.d(t,{C1:()=>n,Gu:()=>u,JT:()=>c,RC:()=>o,S0:()=>y,Y_:()=>p,bl:()=>m,dl:()=>g,eK:()=>l,fetchCompanies:()=>i,getCompanyStats:()=>d,jQ:()=>h,mm:()=>r,oY:()=>x});var a=s(58138);function r(e={}){let t=new URLSearchParams;e.page&&t.append("page",e.page),e.per_page&&t.append("per_page",e.per_page),e.tier&&"ALL"!==e.tier&&t.append("tier",e.tier),e.industry&&"ALL"!==e.industry&&t.append("industry",e.industry),e.campus_recruiting&&t.append("campus_recruiting",e.campus_recruiting),e.search&&t.append("search",e.search),e.sort&&t.append("sort",e.sort),t.append("_t",new Date().getTime());let s=t.toString(),i=[`/api/v1/companies/${s?`?${s}`:""}`,`/api/v1/college/default-college/companies/${s?`?${s}`:""}`];return a.A.get(i[0]).catch(e=>(console.log(`Primary endpoint failed: ${e.message}, trying fallback...`),a.A.get(i[1])))}async function i(e={}){try{console.log("Fetching companies from API...");let t=await r(e),s=[];if(t.data&&Array.isArray(t.data)?s=t.data:t.data&&t.data.results&&Array.isArray(t.data.results)?s=t.data.results:t.data&&t.data.data&&Array.isArray(t.data.data)&&(s=t.data.data),console.log(`Retrieved ${s.length} companies from API`),s.length>0)return await Promise.all(s.map(async e=>{try{let t=await n(e.id);return p(t.data)}catch(t){return console.log(`Could not fetch details for company ${e.id}:`,t),p(e)}}));throw Error("No companies returned from API")}catch(t){console.error("Error fetching companies from API:",t),console.log("Falling back to static company data");let{companies:e}=await s.e(1286).then(s.bind(s,61286));return e}}function n(e){let t=[`/api/v1/company/${e}/`,`/api/v1/companies/${e}/`,`/api/v1/college/default-college/companies/${e}/`];return a.A.get(t[0]).catch(e=>(console.log(`First company endpoint failed: ${e.message}, trying second...`),a.A.get(t[1]).catch(e=>(console.log(`Second company endpoint failed: ${e.message}, trying third...`),a.A.get(t[2])))))}function l(e){let t=new FormData;return Object.keys(e).forEach(s=>{"logo"===s&&e[s]instanceof File?t.append(s,e[s]):null!==e[s]&&void 0!==e[s]&&t.append(s,e[s])}),a.A.post("/api/v1/companies/",t,{headers:{"Content-Type":"multipart/form-data"}})}function c(e,t){let s=new FormData;return Object.keys(t).forEach(e=>{"logo"===e&&t[e]instanceof File?s.append(e,t[e]):null!==t[e]&&void 0!==t[e]&&s.append(e,t[e])}),a.A.put(`/api/v1/companies/${e}/`,s,{headers:{"Content-Type":"multipart/form-data"}})}function o(e){return a.A.delete(`/api/v1/companies/${e}/`)}function d(){return a.A.get("/api/v1/companies/stats/")}function p(e){return{id:e.id,name:e.name,logo:e.logo||`https://via.placeholder.com/48x48/4285F4/FFFFFF?text=${e.name.charAt(0)}`,description:e.description||"",industry:e.industry||"",size:e.size||"",founded:e.founded||"",location:e.location||"Location not specified",website:e.website||"",tier:e.tier||"Tier 3",campus_recruiting:e.campus_recruiting||!1,totalActiveJobs:e.total_active_jobs||0,totalApplicants:e.total_applicants||0,totalHired:e.total_hired||0,awaitedApproval:e.pending_approval||0}}function m(e){return a.A.get(`/api/v1/companies/${e}/followers/count/`)}function h(e,t){return a.A.post(`/api/v1/companies/${e}/followers/`,{user_id:t})}function x(e,t){return a.A.delete(`/api/v1/companies/${e}/followers/`,{data:{user_id:t}})}function u(e,t){return a.A.get(`/api/v1/companies/${e}/followers/status/?user_id=${t}`)}function g(e){return a.A.get(`/api/v1/users/${e}/following/`)}function y(){return a.A.get("/api/v1/companies/simple/").then(e=>{if(e.data&&e.data.success)return e.data;throw Error("Failed to fetch companies")}).catch(e=>(console.error("Error fetching companies:",e),{success:!1,data:[],count:0,error:e.message}))}},17313:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},18310:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>o});var a=s(65239),r=s(48088),i=s(88170),n=s.n(i),l=s(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);s.d(t,c);let o={children:["",{children:["calendar",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,43963)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\calendar\\page.jsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,75535)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\layout.js"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\calendar\\page.jsx"],p={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/calendar/page",pathname:"/calendar",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20561:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>k});var a=s(60687),r=s(43210),i=s(93613),n=s(41312),l=s(17313),c=s(28947),o=s(97051),d=s(5336),p=s(40228),m=s(48730),h=s(99270),x=s(62688);let u=(0,x.A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]),g=(0,x.A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]]);var y=s(47033);let f=(0,x.A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);var j=s(97992),v=s(58869);s(61286);let N={APPLICATION_DEADLINE:"APPLICATION_DEADLINE",INTERVIEW:"INTERVIEW",COMPANY_EVENT:"COMPANY_EVENT",CAREER_FAIR:"CAREER_FAIR",DEADLINE_REMINDER:"DEADLINE_REMINDER",OFFER_RESPONSE:"OFFER_RESPONSE"};N.COMPANY_EVENT,N.COMPANY_EVENT;let b=[{id:1,title:"Microsoft Application Deadline",type:N.APPLICATION_DEADLINE,date:"2024-05-20",time:"23:59",company:"Microsoft",description:"Final deadline for Microsoft Summer Internship applications",status:"upcoming",priority:"high",color:"#dc2626",location:"Online"},{id:2,title:"Google Technical Interview",type:N.INTERVIEW,date:"2024-05-22",time:"14:00",company:"Google",description:"Technical interview for Software Engineer position",status:"scheduled",priority:"high",color:"#059669",location:"Virtual - Google Meet",duration:"90 minutes"},{id:3,title:"Career Fair 2024",type:N.CAREER_FAIR,date:"2024-05-25",time:"10:00",company:"Multiple Companies",description:"Annual career fair with 50+ companies",status:"upcoming",priority:"medium",color:"#2563eb",location:"Student Center",duration:"6 hours"}],A=e=>b.filter(t=>t.type===e),w=(e=7)=>{let t=new Date,s=new Date(t.getTime()+24*e*36e5);return b.filter(e=>{let a=new Date(e.date);return a>=t&&a<=s}).sort((e,t)=>new Date(e.date)-new Date(t.date))},E=()=>{let e=b.length,t=A(N.INTERVIEW).length,s=A(N.APPLICATION_DEADLINE).length;return{total:e,interviews:t,deadlines:s,companyEvents:A(N.COMPANY_EVENT).length,upcoming:w().length}};function k(){let[e,t]=(0,r.useState)(new Date),[s,x]=(0,r.useState)(null),[b,A]=(0,r.useState)([]),[k,C]=(0,r.useState)([]),[D,_]=(0,r.useState)("calendar"),[I,M]=(0,r.useState)("ALL"),[P,S]=(0,r.useState)(""),[R,T]=(0,r.useState)(null),F=s=>{let a=new Date(e);a.setMonth(e.getMonth()+s),t(a)},L=t=>{if(!t)return[];let s=`${e.getFullYear()}-${String(e.getMonth()+1).padStart(2,"0")}-${String(t).padStart(2,"0")}`;return k.filter(e=>e.date===s)},$=e=>{switch(e){case N.APPLICATION_DEADLINE:return(0,a.jsx)(i.A,{className:"w-4 h-4"});case N.INTERVIEW:return(0,a.jsx)(n.A,{className:"w-4 h-4"});case N.COMPANY_EVENT:return(0,a.jsx)(l.A,{className:"w-4 h-4"});case N.CAREER_FAIR:return(0,a.jsx)(c.A,{className:"w-4 h-4"});case N.DEADLINE_REMINDER:return(0,a.jsx)(o.A,{className:"w-4 h-4"});case N.OFFER_RESPONSE:return(0,a.jsx)(d.A,{className:"w-4 h-4"});default:return(0,a.jsx)(p.A,{className:"w-4 h-4"})}},O=e=>{switch(e){case"high":return"border-red-500 bg-red-50 text-red-700";case"medium":return"border-yellow-500 bg-yellow-50 text-yellow-700";case"low":return"border-green-500 bg-green-50 text-green-700";default:return"border-gray-500 bg-gray-50 text-gray-700"}},q=E(),V=w(7),z=e=>{T(e)},Y=()=>{T(null)},U=t=>{let s=new Date;return t&&e.getFullYear()===s.getFullYear()&&e.getMonth()===s.getMonth()&&t===s.getDate()};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"My Calendar"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Track your application deadlines, interviews, and career events"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4 mb-8",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-100",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,a.jsx)(p.A,{className:"w-5 h-5 text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:q.total}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Total Events"})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-100",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,a.jsx)(n.A,{className:"w-5 h-5 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:q.interviews}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Interviews"})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-100",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"p-2 bg-red-100 rounded-lg",children:(0,a.jsx)(i.A,{className:"w-5 h-5 text-red-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:q.deadlines}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Deadlines"})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-100",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,a.jsx)(l.A,{className:"w-5 h-5 text-purple-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:q.companyEvents}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Company Events"})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-100",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"p-2 bg-amber-100 rounded-lg",children:(0,a.jsx)(m.A,{className:"w-5 h-5 text-amber-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:q.upcoming}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"This Week"})]})]})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-100 mb-6",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex gap-4 flex-1",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(h.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,a.jsx)("input",{type:"text",placeholder:"Search events...",value:P,onChange:e=>S(e.target.value),className:"pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[300px]"})]}),(0,a.jsxs)("select",{value:I,onChange:e=>M(e.target.value),className:"px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"ALL",children:"All Events"}),(0,a.jsx)("option",{value:N.APPLICATION_DEADLINE,children:"Application Deadlines"}),(0,a.jsx)("option",{value:N.INTERVIEW,children:"Interviews"}),(0,a.jsx)("option",{value:N.COMPANY_EVENT,children:"Company Events"}),(0,a.jsx)("option",{value:N.CAREER_FAIR,children:"Career Fairs"}),(0,a.jsx)("option",{value:N.DEADLINE_REMINDER,children:"Reminders"}),(0,a.jsx)("option",{value:N.OFFER_RESPONSE,children:"Offer Responses"})]})]}),(0,a.jsxs)("div",{className:"flex bg-gray-100 rounded-lg p-1",children:[(0,a.jsx)("button",{onClick:()=>_("calendar"),className:`p-2 rounded-md transition-colors ${"calendar"===D?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:(0,a.jsx)(u,{className:"w-5 h-5"})}),(0,a.jsx)("button",{onClick:()=>_("list"),className:`p-2 rounded-md transition-colors ${"list"===D?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:(0,a.jsx)(g,{className:"w-5 h-5"})})]})]}),(0,a.jsxs)("div",{className:"mt-4 text-sm text-gray-600",children:["Showing ",k.length," of ",b.length," events"]})]})]}),"calendar"===D?(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-100",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("h2",{className:"text-xl font-semibold text-gray-900",children:[["January","February","March","April","May","June","July","August","September","October","November","December"][e.getMonth()]," ",e.getFullYear()]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{onClick:()=>F(-1),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,a.jsx)(y.A,{className:"w-5 h-5"})}),(0,a.jsx)("button",{onClick:()=>t(new Date),className:"px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors text-sm font-medium",children:"Today"}),(0,a.jsx)("button",{onClick:()=>F(1),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,a.jsx)(f,{className:"w-5 h-5"})})]})]})}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("div",{className:"grid grid-cols-7 gap-4 mb-4",children:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"].map(e=>(0,a.jsx)("div",{className:"text-center text-sm font-medium text-gray-500 py-2",children:e},e))}),(0,a.jsx)("div",{className:"grid grid-cols-7 gap-4",children:(e=>{let t=e.getFullYear(),s=e.getMonth(),a=new Date(t,s,1),r=new Date(t,s+1,0).getDate(),i=a.getDay(),n=[];for(let e=0;e<i;e++)n.push(null);for(let e=1;e<=r;e++)n.push(e);return n})(e).map((e,t)=>{let s=L(e),r=U(e);return(0,a.jsx)("div",{className:`min-h-[120px] p-2 border rounded-lg ${r?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"} ${e?"cursor-pointer":""}`,onClick:()=>e&&x(e),children:e&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:`text-sm font-medium mb-2 ${r?"text-blue-700":"text-gray-900"}`,children:e}),(0,a.jsxs)("div",{className:"space-y-1",children:[s.slice(0,3).map(e=>(0,a.jsx)("div",{onClick:t=>{t.stopPropagation(),z(e)},className:"text-xs p-1 rounded truncate cursor-pointer hover:opacity-80",style:{backgroundColor:e.color+"20",color:e.color},children:e.title},e.id)),s.length>3&&(0,a.jsxs)("div",{className:"text-xs text-gray-500 p-1",children:["+",s.length-3," more"]})]})]})},t)})})]})]}):(0,a.jsx)("div",{className:"space-y-4",children:0===k.length?(0,a.jsxs)("div",{className:"text-center py-16 bg-white rounded-lg",children:[(0,a.jsx)("div",{className:"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(p.A,{className:"w-12 h-12 text-gray-400"})}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"No events found"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Try adjusting your search or filters to find more events"})]}):k.map(e=>(0,a.jsx)("div",{onClick:()=>z(e),className:"bg-white rounded-lg p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200 cursor-pointer",children:(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[(0,a.jsx)("div",{className:"p-3 rounded-lg",style:{backgroundColor:e.color+"20",color:e.color},children:$(e.type)}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:e.title}),(0,a.jsx)("span",{className:`px-3 py-1 rounded-full text-xs font-medium border ${O(e.priority)}`,children:e.priority?.toUpperCase()||"MEDIUM"})]}),(0,a.jsx)("p",{className:"text-gray-600 mb-3",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(p.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:new Date(e.date).toLocaleDateString()})]}),e.time&&(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(m.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:e.time})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(l.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:e.company})]}),e.location&&(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(j.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:e.location})]})]})]})]})},e.id))}),V.length>0&&(0,a.jsxs)("div",{className:"fixed right-6 top-24 w-80 bg-white rounded-lg shadow-lg border border-gray-100 p-4 max-h-96 overflow-y-auto",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Upcoming This Week"}),(0,a.jsx)("div",{className:"space-y-3",children:V.slice(0,5).map(e=>(0,a.jsxs)("div",{onClick:()=>z(e),className:"p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:e.color}}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e.company})]}),(0,a.jsx)("p",{className:"text-sm text-gray-700 line-clamp-2",children:e.title}),(0,a.jsxs)("div",{className:"flex items-center gap-2 mt-2 text-xs text-gray-500",children:[(0,a.jsx)(p.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:new Date(e.date).toLocaleDateString()}),e.time&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m.A,{className:"w-3 h-3 ml-2"}),(0,a.jsx)("span",{children:e.time})]})]})]},e.id))})]}),R&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsx)("div",{className:"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-6",children:[(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[(0,a.jsx)("div",{className:"p-3 rounded-lg",style:{backgroundColor:R.color+"20",color:R.color},children:$(R.type)}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:R.title}),(0,a.jsx)("p",{className:"text-gray-600",children:R.description})]})]}),(0,a.jsx)("button",{onClick:Y,className:"text-gray-400 hover:text-gray-600 p-2",children:"\xd7"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(p.A,{className:"w-5 h-5 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Date"}),(0,a.jsx)("p",{className:"font-medium",children:new Date(R.date).toLocaleDateString()})]})]}),R.time&&(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(m.A,{className:"w-5 h-5 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Time"}),(0,a.jsx)("p",{className:"font-medium",children:R.time})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(l.A,{className:"w-5 h-5 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Company"}),(0,a.jsx)("p",{className:"font-medium",children:R.company})]})]}),R.location&&(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(j.A,{className:"w-5 h-5 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Location"}),(0,a.jsx)("p",{className:"font-medium",children:R.location})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[R.interviewer&&(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(v.A,{className:"w-5 h-5 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Interviewer"}),(0,a.jsx)("p",{className:"font-medium",children:R.interviewer})]})]}),R.duration&&(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(m.A,{className:"w-5 h-5 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Duration"}),(0,a.jsx)("p",{className:"font-medium",children:R.duration})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(i.A,{className:"w-5 h-5 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Priority"}),(0,a.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium border ${O(R.priority)}`,children:R.priority?.toUpperCase()||"MEDIUM"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(d.A,{className:"w-5 h-5 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Status"}),(0,a.jsx)("p",{className:"font-medium capitalize",children:R.status})]})]})]})]}),(0,a.jsxs)("div",{className:"flex gap-3 mt-8",children:[(0,a.jsx)("button",{onClick:Y,className:"flex-1 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:"Close"}),(0,a.jsx)("button",{className:"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Add to Google Calendar"})]})]})})})]})}},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28947:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40228:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41312:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},41952:(e,t,s)=>{Promise.resolve().then(s.bind(s,43963))},43963:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\calendar\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\calendar\\page.jsx","default")},47033:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},48730:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58138:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(51060);s(51421);let r=a.A.create({baseURL:"http://127.0.0.1:8000",headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>{let t=localStorage.getItem("access_token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,async e=>{let t=e.config;if(e.response?.status===401&&!t._retry){t._retry=!0;try{let e=localStorage.getItem("refresh_token");if(e){let s=await a.A.post("http://127.0.0.1:8000/api/auth/token/refresh/",{refresh:e});return localStorage.setItem("access_token",s.data.access),t.headers.Authorization=`Bearer ${s.data.access}`,r(t)}}catch(e){console.error("Error refreshing token:",e)}}return Promise.reject(e)});let i=r},61286:(e,t,s)=>{"use strict";s.d(t,{Ly:()=>i,_N:()=>n,companies:()=>r,zZ:()=>l});var a=s(14335);let r=[{id:1,name:"TechCorp Inc",description:"Leading technology solutions provider",industry:"Technology",size:"500-1000",founded:"2010",website:"https://techcorp.com"},{id:2,name:"DataCorp",description:"Data analytics and insights company",industry:"Data Analytics",size:"100-500",founded:"2015",website:"https://datacorp.com"}],i=[{id:1,job_id:25,title:"Software Engineer",company:"TechCorp Inc",status:"APPLIED",application_deadline:"2024-05-30T23:59:59Z"},{id:2,job_id:26,title:"Data Scientist",company:"DataCorp",status:"INTERVIEW SCHEDULED",application_deadline:"2024-06-15T23:59:59Z"}],n=async(e={})=>{try{let t={...e,_t:new Date().getTime()};console.log("Fetching companies with cache busting...");let s=await (0,a.mm)(t),i=[];s.data&&Array.isArray(s.data)?i=s.data:s.data&&s.data.results&&Array.isArray(s.data.results)?i=s.data.results:s.data&&s.data.data&&Array.isArray(s.data.data)&&(i=s.data.data);let n=i.map(a.Y_);if(console.log(`Fetched ${n.length} companies from API`),0===n.length)return console.warn("API returned empty companies array, using static data"),r;return n}catch(e){console.error("Error fetching companies:",e);try{console.log("Trying alternate endpoint format...");let e=await fetch("/api/v1/college/default-college/companies/");if(e.ok){let t=await e.json(),s=Array.isArray(t)?t:t.data||t.results||[];if(s.length>0)return console.log("Successfully retrieved companies from alternate endpoint"),s.map(a.Y_)}}catch(e){console.error("Alternate endpoint also failed:",e)}return r}};function l(e){return console.log(`Fetching jobs for company ID: ${e}`),[]}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71328:(e,t,s)=>{Promise.resolve().then(s.bind(s,20561))},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},97051:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},97992:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},99270:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4447,681,1658,1060,2305],()=>s(18310));module.exports=a})();