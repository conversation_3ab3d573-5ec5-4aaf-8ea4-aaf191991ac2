{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/api/companies.js"], "sourcesContent": ["import client from './client';\r\n\r\n// List all companies with optional filtering\r\nexport function listCompanies(params = {}) {\r\n  const queryParams = new URLSearchParams();\r\n  \r\n  // Add pagination parameters\r\n  if (params.page) queryParams.append('page', params.page);\r\n  if (params.per_page) queryParams.append('per_page', params.per_page);\r\n  \r\n  // Add filtering parameters\r\n  if (params.tier && params.tier !== 'ALL') queryParams.append('tier', params.tier);\r\n  if (params.industry && params.industry !== 'ALL') queryParams.append('industry', params.industry);\r\n  if (params.campus_recruiting) queryParams.append('campus_recruiting', params.campus_recruiting);\r\n  if (params.search) queryParams.append('search', params.search);\r\n  if (params.sort) queryParams.append('sort', params.sort);\r\n  \r\n  // Add cache busting parameter to prevent cached responses\r\n  queryParams.append('_t', new Date().getTime());\r\n  \r\n  const queryString = queryParams.toString();\r\n  // Try both endpoints to maximize compatibility with backend\r\n  const urls = [\r\n    `/api/v1/companies/${queryString ? `?${queryString}` : ''}`,\r\n    `/api/v1/college/default-college/companies/${queryString ? `?${queryString}` : ''}`\r\n  ];\r\n  \r\n  // Try primary endpoint first, fall back to secondary if needed\r\n  return client.get(urls[0])\r\n    .catch(error => {\r\n      console.log(`Primary endpoint failed: ${error.message}, trying fallback...`);\r\n      return client.get(urls[1]);\r\n    });\r\n}\r\n\r\n// Function to fetch companies from the API with improved reliability\r\nexport async function fetchCompanies(params = {}) {\r\n  try {\r\n    console.log('Fetching companies from API...');\r\n    const response = await listCompanies(params);\r\n    // Transform the data to match our frontend structure\r\n    let companies = [];\r\n    \r\n    if (response.data && Array.isArray(response.data)) {\r\n      companies = response.data;\r\n    } else if (response.data && response.data.results && Array.isArray(response.data.results)) {\r\n      companies = response.data.results;\r\n    } else if (response.data && response.data.data && Array.isArray(response.data.data)) {\r\n      companies = response.data.data;\r\n    }\r\n    \r\n    console.log(`Retrieved ${companies.length} companies from API`);\r\n    \r\n    // Only proceed with detail fetching if we got a reasonable number of companies\r\n    if (companies.length > 0) {\r\n      // For each company in the list, fetch complete details to get all fields\r\n      const detailedCompanies = await Promise.all(\r\n        companies.map(async (company) => {\r\n          try {\r\n            // Fetch detailed info for each company\r\n            const detailResponse = await getCompany(company.id);\r\n            return transformCompanyData(detailResponse.data);\r\n          } catch (error) {\r\n            console.log(`Could not fetch details for company ${company.id}:`, error);\r\n            // Fall back to the list data if detail fetch fails\r\n            return transformCompanyData(company);\r\n          }\r\n        })\r\n      );\r\n      \r\n      // Store companies in sessionStorage for quick access\r\n      if (typeof window !== 'undefined') {\r\n        sessionStorage.setItem('companies_data', JSON.stringify(detailedCompanies));\r\n        sessionStorage.setItem('companies_timestamp', Date.now());\r\n      }\r\n      \r\n      return detailedCompanies;\r\n    } else {\r\n      throw new Error('No companies returned from API');\r\n    }\r\n  } catch (error) {\r\n    console.error('Error fetching companies from API:', error);\r\n    \r\n    // Check if we have cached data in sessionStorage\r\n    if (typeof window !== 'undefined') {\r\n      const cachedData = sessionStorage.getItem('companies_data');\r\n      const timestamp = sessionStorage.getItem('companies_timestamp');\r\n      \r\n      if (cachedData && timestamp) {\r\n        // Only use cached data if it's less than 5 minutes old\r\n        const age = Date.now() - parseInt(timestamp);\r\n        if (age < 5 * 60 * 1000) {\r\n          console.log('Using cached company data (< 5 min old)');\r\n          return JSON.parse(cachedData);\r\n        }\r\n      }\r\n    }\r\n    \r\n    // Import the static data as last resort\r\n    console.log('Falling back to static company data');\r\n    const { companies } = await import('../data/jobsData');\r\n    return companies;\r\n  }\r\n}\r\n\r\n// Get a single company by ID with better error handling\r\nexport function getCompany(companyId) {\r\n  // Try both possible endpoints\r\n  const urls = [\r\n    `/api/v1/company/${companyId}/`,\r\n    `/api/v1/companies/${companyId}/`,\r\n    `/api/v1/college/default-college/companies/${companyId}/`\r\n  ];\r\n  \r\n  // Try each URL in sequence until one works\r\n  return client.get(urls[0])\r\n    .catch(error1 => {\r\n      console.log(`First company endpoint failed: ${error1.message}, trying second...`);\r\n      return client.get(urls[1])\r\n        .catch(error2 => {\r\n          console.log(`Second company endpoint failed: ${error2.message}, trying third...`);\r\n          return client.get(urls[2]);\r\n        });\r\n    });\r\n}\r\n\r\n// Create a new company\r\nexport function createCompany(companyData) {\r\n  const formData = new FormData();\r\n  \r\n  // Append all fields to the FormData\r\n  Object.keys(companyData).forEach(key => {\r\n    // Handle file upload for logo\r\n    if (key === 'logo' && companyData[key] instanceof File) {\r\n      formData.append(key, companyData[key]);\r\n    } else if (companyData[key] !== null && companyData[key] !== undefined) {\r\n      formData.append(key, companyData[key]);\r\n    }\r\n  });\r\n  \r\n  return client.post('/api/v1/companies/', formData, {\r\n    headers: {\r\n      'Content-Type': 'multipart/form-data',\r\n    },\r\n  });\r\n}\r\n\r\n// Update company details\r\nexport function updateCompany(companyId, companyData) {\r\n  const formData = new FormData();\r\n  \r\n  // Append all fields to the FormData\r\n  Object.keys(companyData).forEach(key => {\r\n    // Handle file upload for logo\r\n    if (key === 'logo' && companyData[key] instanceof File) {\r\n      formData.append(key, companyData[key]);\r\n    } else if (companyData[key] !== null && companyData[key] !== undefined) {\r\n      formData.append(key, companyData[key]);\r\n    }\r\n  });\r\n  \r\n  return client.put(`/api/v1/companies/${companyId}/`, formData, {\r\n    headers: {\r\n      'Content-Type': 'multipart/form-data',\r\n    },\r\n  });\r\n}\r\n\r\n// Delete a company\r\nexport function deleteCompany(companyId) {\r\n  return client.delete(`/api/v1/companies/${companyId}/`);\r\n}\r\n\r\n// Upload company logo\r\nexport function uploadCompanyLogo(companyId, logoFile) {\r\n  const formData = new FormData();\r\n  formData.append('logo', logoFile);\r\n  \r\n  return client.post(`/api/v1/companies/${companyId}/upload-logo/`, formData, {\r\n    headers: {\r\n      'Content-Type': 'multipart/form-data',\r\n    },\r\n  });\r\n}\r\n\r\n// Get company statistics\r\nexport function getCompanyStats() {\r\n  return client.get('/api/v1/companies/stats/');\r\n}\r\n\r\n// Get unique industries\r\nexport function getIndustries() {\r\n  return client.get('/api/v1/companies/industries/');\r\n}\r\n\r\n// Transform backend company data to match frontend structure\r\nexport function transformCompanyData(backendData) {\r\n  return {\r\n    id: backendData.id,\r\n    name: backendData.name,\r\n    logo: backendData.logo || `https://via.placeholder.com/48x48/4285F4/FFFFFF?text=${backendData.name.charAt(0)}`,\r\n    description: backendData.description || '',\r\n    industry: backendData.industry || '',\r\n    size: backendData.size || '',\r\n    founded: backendData.founded || '',\r\n    location: backendData.location || 'Location not specified',\r\n    website: backendData.website || '',\r\n    tier: backendData.tier || 'Tier 3',\r\n    campus_recruiting: backendData.campus_recruiting || false,\r\n    // Standardized field names\r\n    totalActiveJobs: backendData.total_active_jobs || 0,\r\n    totalApplicants: backendData.total_applicants || 0, \r\n    totalHired: backendData.total_hired || 0,\r\n    awaitedApproval: backendData.pending_approval || 0,\r\n  };\r\n}\r\n\r\n// Get followers count for a company\r\nexport function getFollowersCount(companyId) {\r\n  return client.get(`/api/v1/companies/${companyId}/followers/count/`);\r\n}\r\n\r\n// Follow a company\r\nexport function followCompany(companyId, userId) {\r\n  return client.post(`/api/v1/companies/${companyId}/followers/`, { user_id: userId });\r\n}\r\n\r\n// Unfollow a company\r\nexport function unfollowCompany(companyId, userId) {\r\n  return client.delete(`/api/v1/companies/${companyId}/followers/`, { data: { user_id: userId } });\r\n}\r\n\r\n// Check if user is following a company\r\nexport function checkFollowingStatus(companyId, userId) {\r\n  return client.get(`/api/v1/companies/${companyId}/followers/status/?user_id=${userId}`);\r\n}\r\n\r\n// Get all companies a user is following\r\nexport function getUserFollowedCompanies(userId) {\r\n  return client.get(`/api/v1/users/${userId}/following/`);\r\n}\r\n\r\n// Simple, reliable function to fetch all companies\r\nexport function fetchSimpleCompanies() {\r\n  return client.get('/api/v1/companies/simple/')\r\n    .then(response => {\r\n      if (response.data && response.data.success) {\r\n        return response.data;\r\n      }\r\n      throw new Error('Failed to fetch companies');\r\n    })\r\n    .catch(error => {\r\n      console.error('Error fetching companies:', error);\r\n      // Return a fallback structure\r\n      return {\r\n        success: false,\r\n        data: [],\r\n        count: 0,\r\n        error: error.message\r\n      };\r\n    });\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;;AAGO,SAAS,cAAc,SAAS,CAAC,CAAC;IACvC,MAAM,cAAc,IAAI;IAExB,4BAA4B;IAC5B,IAAI,OAAO,IAAI,EAAE,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI;IACvD,IAAI,OAAO,QAAQ,EAAE,YAAY,MAAM,CAAC,YAAY,OAAO,QAAQ;IAEnE,2BAA2B;IAC3B,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,KAAK,OAAO,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI;IAChF,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,KAAK,OAAO,YAAY,MAAM,CAAC,YAAY,OAAO,QAAQ;IAChG,IAAI,OAAO,iBAAiB,EAAE,YAAY,MAAM,CAAC,qBAAqB,OAAO,iBAAiB;IAC9F,IAAI,OAAO,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;IAC7D,IAAI,OAAO,IAAI,EAAE,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI;IAEvD,0DAA0D;IAC1D,YAAY,MAAM,CAAC,MAAM,IAAI,OAAO,OAAO;IAE3C,MAAM,cAAc,YAAY,QAAQ;IACxC,4DAA4D;IAC5D,MAAM,OAAO;QACX,CAAC,kBAAkB,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAC3D,CAAC,0CAA0C,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;KACpF;IAED,+DAA+D;IAC/D,OAAO,oHAAA,CAAA,UAAM,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EACtB,KAAK,CAAC,CAAA;QACL,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,MAAM,OAAO,CAAC,oBAAoB,CAAC;QAC3E,OAAO,oHAAA,CAAA,UAAM,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;IAC3B;AACJ;AAGO,eAAe,eAAe,SAAS,CAAC,CAAC;IAC9C,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,MAAM,WAAW,MAAM,cAAc;QACrC,qDAAqD;QACrD,IAAI,YAAY,EAAE;QAElB,IAAI,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;YACjD,YAAY,SAAS,IAAI;QAC3B,OAAO,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,OAAO,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,OAAO,GAAG;YACzF,YAAY,SAAS,IAAI,CAAC,OAAO;QACnC,OAAO,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,GAAG;YACnF,YAAY,SAAS,IAAI,CAAC,IAAI;QAChC;QAEA,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,UAAU,MAAM,CAAC,mBAAmB,CAAC;QAE9D,+EAA+E;QAC/E,IAAI,UAAU,MAAM,GAAG,GAAG;YACxB,yEAAyE;YACzE,MAAM,oBAAoB,MAAM,QAAQ,GAAG,CACzC,UAAU,GAAG,CAAC,OAAO;gBACnB,IAAI;oBACF,uCAAuC;oBACvC,MAAM,iBAAiB,MAAM,WAAW,QAAQ,EAAE;oBAClD,OAAO,qBAAqB,eAAe,IAAI;gBACjD,EAAE,OAAO,OAAO;oBACd,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE;oBAClE,mDAAmD;oBACnD,OAAO,qBAAqB;gBAC9B;YACF;YAGF,qDAAqD;YACrD,uCAAmC;;YAGnC;YAEA,OAAO;QACT,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QAEpD,iDAAiD;QACjD,uCAAmC;;QAYnC;QAEA,wCAAwC;QACxC,QAAQ,GAAG,CAAC;QACZ,MAAM,EAAE,SAAS,EAAE,GAAG;QACtB,OAAO;IACT;AACF;AAGO,SAAS,WAAW,SAAS;IAClC,8BAA8B;IAC9B,MAAM,OAAO;QACX,CAAC,gBAAgB,EAAE,UAAU,CAAC,CAAC;QAC/B,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC;QACjC,CAAC,0CAA0C,EAAE,UAAU,CAAC,CAAC;KAC1D;IAED,2CAA2C;IAC3C,OAAO,oHAAA,CAAA,UAAM,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EACtB,KAAK,CAAC,CAAA;QACL,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,OAAO,OAAO,CAAC,kBAAkB,CAAC;QAChF,OAAO,oHAAA,CAAA,UAAM,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EACtB,KAAK,CAAC,CAAA;YACL,QAAQ,GAAG,CAAC,CAAC,gCAAgC,EAAE,OAAO,OAAO,CAAC,iBAAiB,CAAC;YAChF,OAAO,oHAAA,CAAA,UAAM,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;QAC3B;IACJ;AACJ;AAGO,SAAS,cAAc,WAAW;IACvC,MAAM,WAAW,IAAI;IAErB,oCAAoC;IACpC,OAAO,IAAI,CAAC,aAAa,OAAO,CAAC,CAAA;QAC/B,8BAA8B;QAC9B,IAAI,QAAQ,UAAU,WAAW,CAAC,IAAI,YAAY,MAAM;YACtD,SAAS,MAAM,CAAC,KAAK,WAAW,CAAC,IAAI;QACvC,OAAO,IAAI,WAAW,CAAC,IAAI,KAAK,QAAQ,WAAW,CAAC,IAAI,KAAK,WAAW;YACtE,SAAS,MAAM,CAAC,KAAK,WAAW,CAAC,IAAI;QACvC;IACF;IAEA,OAAO,oHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,sBAAsB,UAAU;QACjD,SAAS;YACP,gBAAgB;QAClB;IACF;AACF;AAGO,SAAS,cAAc,SAAS,EAAE,WAAW;IAClD,MAAM,WAAW,IAAI;IAErB,oCAAoC;IACpC,OAAO,IAAI,CAAC,aAAa,OAAO,CAAC,CAAA;QAC/B,8BAA8B;QAC9B,IAAI,QAAQ,UAAU,WAAW,CAAC,IAAI,YAAY,MAAM;YACtD,SAAS,MAAM,CAAC,KAAK,WAAW,CAAC,IAAI;QACvC,OAAO,IAAI,WAAW,CAAC,IAAI,KAAK,QAAQ,WAAW,CAAC,IAAI,KAAK,WAAW;YACtE,SAAS,MAAM,CAAC,KAAK,WAAW,CAAC,IAAI;QACvC;IACF;IAEA,OAAO,oHAAA,CAAA,UAAM,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC,EAAE,UAAU;QAC7D,SAAS;YACP,gBAAgB;QAClB;IACF;AACF;AAGO,SAAS,cAAc,SAAS;IACrC,OAAO,oHAAA,CAAA,UAAM,CAAC,MAAM,CAAC,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC;AACxD;AAGO,SAAS,kBAAkB,SAAS,EAAE,QAAQ;IACnD,MAAM,WAAW,IAAI;IACrB,SAAS,MAAM,CAAC,QAAQ;IAExB,OAAO,oHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,CAAC,kBAAkB,EAAE,UAAU,aAAa,CAAC,EAAE,UAAU;QAC1E,SAAS;YACP,gBAAgB;QAClB;IACF;AACF;AAGO,SAAS;IACd,OAAO,oHAAA,CAAA,UAAM,CAAC,GAAG,CAAC;AACpB;AAGO,SAAS;IACd,OAAO,oHAAA,CAAA,UAAM,CAAC,GAAG,CAAC;AACpB;AAGO,SAAS,qBAAqB,WAAW;IAC9C,OAAO;QACL,IAAI,YAAY,EAAE;QAClB,MAAM,YAAY,IAAI;QACtB,MAAM,YAAY,IAAI,IAAI,CAAC,qDAAqD,EAAE,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI;QAC9G,aAAa,YAAY,WAAW,IAAI;QACxC,UAAU,YAAY,QAAQ,IAAI;QAClC,MAAM,YAAY,IAAI,IAAI;QAC1B,SAAS,YAAY,OAAO,IAAI;QAChC,UAAU,YAAY,QAAQ,IAAI;QAClC,SAAS,YAAY,OAAO,IAAI;QAChC,MAAM,YAAY,IAAI,IAAI;QAC1B,mBAAmB,YAAY,iBAAiB,IAAI;QACpD,2BAA2B;QAC3B,iBAAiB,YAAY,iBAAiB,IAAI;QAClD,iBAAiB,YAAY,gBAAgB,IAAI;QACjD,YAAY,YAAY,WAAW,IAAI;QACvC,iBAAiB,YAAY,gBAAgB,IAAI;IACnD;AACF;AAGO,SAAS,kBAAkB,SAAS;IACzC,OAAO,oHAAA,CAAA,UAAM,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,UAAU,iBAAiB,CAAC;AACrE;AAGO,SAAS,cAAc,SAAS,EAAE,MAAM;IAC7C,OAAO,oHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,CAAC,kBAAkB,EAAE,UAAU,WAAW,CAAC,EAAE;QAAE,SAAS;IAAO;AACpF;AAGO,SAAS,gBAAgB,SAAS,EAAE,MAAM;IAC/C,OAAO,oHAAA,CAAA,UAAM,CAAC,MAAM,CAAC,CAAC,kBAAkB,EAAE,UAAU,WAAW,CAAC,EAAE;QAAE,MAAM;YAAE,SAAS;QAAO;IAAE;AAChG;AAGO,SAAS,qBAAqB,SAAS,EAAE,MAAM;IACpD,OAAO,oHAAA,CAAA,UAAM,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,UAAU,2BAA2B,EAAE,QAAQ;AACxF;AAGO,SAAS,yBAAyB,MAAM;IAC7C,OAAO,oHAAA,CAAA,UAAM,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,OAAO,WAAW,CAAC;AACxD;AAGO,SAAS;IACd,OAAO,oHAAA,CAAA,UAAM,CAAC,GAAG,CAAC,6BACf,IAAI,CAAC,CAAA;QACJ,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;YAC1C,OAAO,SAAS,IAAI;QACtB;QACA,MAAM,IAAI,MAAM;IAClB,GACC,KAAK,CAAC,CAAA;QACL,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,8BAA8B;QAC9B,OAAO;YACL,SAAS;YACT,MAAM,EAAE;YACR,OAAO;YACP,OAAO,MAAM,OAAO;QACtB;IACF;AACJ", "debugId": null}}]}