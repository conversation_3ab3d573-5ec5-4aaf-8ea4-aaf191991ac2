(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1260,1318,6208,8937],{21260:(e,t,a)=>{"use strict";a.d(t,{Ly:()=>n,_N:()=>l,companies:()=>r,zZ:()=>i});var s=a(48937);let r=[{id:1,name:"TechCorp Inc",description:"Leading technology solutions provider",industry:"Technology",size:"500-1000",founded:"2010",website:"https://techcorp.com"},{id:2,name:"DataCorp",description:"Data analytics and insights company",industry:"Data Analytics",size:"100-500",founded:"2015",website:"https://datacorp.com"}],n=[{id:1,job_id:25,title:"Software Engineer",company:"TechCorp Inc",status:"APPLIED",application_deadline:"2024-05-30T23:59:59Z"},{id:2,job_id:26,title:"Data Scientist",company:"DataCorp",status:"INTERVIEW SCHEDULED",application_deadline:"2024-06-15T23:59:59Z"}],l=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{let t={...e,_t:new Date().getTime()};console.log("Fetching companies with cache busting...");let a=await (0,s.mm)(t),n=[];a.data&&Array.isArray(a.data)?n=a.data:a.data&&a.data.results&&Array.isArray(a.data.results)?n=a.data.results:a.data&&a.data.data&&Array.isArray(a.data.data)&&(n=a.data.data);let l=n.map(s.Y_);if(console.log("Fetched ".concat(l.length," companies from API")),0===l.length)return console.warn("API returned empty companies array, using static data"),r;return l}catch(e){console.error("Error fetching companies:",e);try{console.log("Trying alternate endpoint format...");let e=await fetch("/api/v1/college/default-college/companies/");if(e.ok){let t=await e.json(),a=Array.isArray(t)?t:t.data||t.results||[];if(a.length>0)return console.log("Successfully retrieved companies from alternate endpoint"),a.map(s.Y_)}}catch(e){console.error("Alternate endpoint also failed:",e)}return r}};function i(e){return console.log("Fetching jobs for company ID: ".concat(e)),[]}},25384:(e,t,a)=>{"use strict";a.d(t,{G_:()=>r});var s=a(95155);function r(e){let{description:t,className:a=""}=e,r=t?t.replace(/\n/g,"<br />").replace(/•/g,"•").replace(/\*\s/g,"• ").replace(/-\s/g,"• ").trim():"No description provided.";return(0,s.jsx)("div",{className:"text-gray-700 leading-relaxed ".concat(a),dangerouslySetInnerHTML:{__html:r}})}},28835:(e,t,a)=>{"use strict";function s(){try{let e=localStorage.getItem("access");if(!e)return null;let t=localStorage.getItem("user");if(t){let e=JSON.parse(t);return e.id||e.user_id||null}if(e)try{let t=e.split(".")[1],a=JSON.parse(atob(t));return a.user_id||a.id||a.sub||null}catch(e){console.error("Error decoding JWT token:",e)}return null}catch(e){return console.error("Error getting user ID:",e),null}}function r(){return localStorage.getItem("access")}function n(){try{let e=localStorage.getItem("user");return e?JSON.parse(e):null}catch(e){return console.error("Error getting user data:",e),null}}a.d(t,{F6:()=>s,c4:()=>r,gL:()=>n})},37719:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var s=a(23464);a(73983);let r=s.A.create({baseURL:"http://127.0.0.1:8000",headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>{let t=localStorage.getItem("access_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,async e=>{var t;let a=e.config;if((null==(t=e.response)?void 0:t.status)===401&&!a._retry){a._retry=!0;try{let e=localStorage.getItem("refresh_token");if(e){let t=await s.A.post("http://127.0.0.1:8000/api/auth/token/refresh/",{refresh:e});return localStorage.setItem("access_token",t.data.access),a.headers.Authorization="Bearer ".concat(t.data.access),r(a)}}catch(e){console.error("Error refreshing token:",e),localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),window.location.href="/login"}}return Promise.reject(e)});let n=r},48937:(e,t,a)=>{"use strict";a.d(t,{C1:()=>l,Gu:()=>g,JT:()=>o,RC:()=>c,S0:()=>b,Y_:()=>m,bl:()=>x,dl:()=>u,eK:()=>i,fetchCompanies:()=>n,getCompanyStats:()=>d,jQ:()=>p,mm:()=>r,oY:()=>h});var s=a(37719);function r(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.page&&t.append("page",e.page),e.per_page&&t.append("per_page",e.per_page),e.tier&&"ALL"!==e.tier&&t.append("tier",e.tier),e.industry&&"ALL"!==e.industry&&t.append("industry",e.industry),e.campus_recruiting&&t.append("campus_recruiting",e.campus_recruiting),e.search&&t.append("search",e.search),e.sort&&t.append("sort",e.sort),t.append("_t",new Date().getTime());let a=t.toString(),r=["/api/v1/companies/".concat(a?"?".concat(a):""),"/api/v1/college/default-college/companies/".concat(a?"?".concat(a):"")];return s.A.get(r[0]).catch(e=>(console.log("Primary endpoint failed: ".concat(e.message,", trying fallback...")),s.A.get(r[1])))}async function n(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{console.log("Fetching companies from API...");let t=await r(e),a=[];if(t.data&&Array.isArray(t.data)?a=t.data:t.data&&t.data.results&&Array.isArray(t.data.results)?a=t.data.results:t.data&&t.data.data&&Array.isArray(t.data.data)&&(a=t.data.data),console.log("Retrieved ".concat(a.length," companies from API")),a.length>0){let e=await Promise.all(a.map(async e=>{try{let t=await l(e.id);return m(t.data)}catch(t){return console.log("Could not fetch details for company ".concat(e.id,":"),t),m(e)}}));return sessionStorage.setItem("companies_data",JSON.stringify(e)),sessionStorage.setItem("companies_timestamp",Date.now()),e}throw Error("No companies returned from API")}catch(t){console.error("Error fetching companies from API:",t);{let e=sessionStorage.getItem("companies_data"),t=sessionStorage.getItem("companies_timestamp");if(e&&t&&Date.now()-parseInt(t)<3e5)return console.log("Using cached company data (< 5 min old)"),JSON.parse(e)}console.log("Falling back to static company data");let{companies:e}=await a.e(1260).then(a.bind(a,21260));return e}}function l(e){let t=["/api/v1/company/".concat(e,"/"),"/api/v1/companies/".concat(e,"/"),"/api/v1/college/default-college/companies/".concat(e,"/")];return s.A.get(t[0]).catch(e=>(console.log("First company endpoint failed: ".concat(e.message,", trying second...")),s.A.get(t[1]).catch(e=>(console.log("Second company endpoint failed: ".concat(e.message,", trying third...")),s.A.get(t[2])))))}function i(e){let t=new FormData;return Object.keys(e).forEach(a=>{"logo"===a&&e[a]instanceof File?t.append(a,e[a]):null!==e[a]&&void 0!==e[a]&&t.append(a,e[a])}),s.A.post("/api/v1/companies/",t,{headers:{"Content-Type":"multipart/form-data"}})}function o(e,t){let a=new FormData;return Object.keys(t).forEach(e=>{"logo"===e&&t[e]instanceof File?a.append(e,t[e]):null!==t[e]&&void 0!==t[e]&&a.append(e,t[e])}),s.A.put("/api/v1/companies/".concat(e,"/"),a,{headers:{"Content-Type":"multipart/form-data"}})}function c(e){return s.A.delete("/api/v1/companies/".concat(e,"/"))}function d(){return s.A.get("/api/v1/companies/stats/")}function m(e){return{id:e.id,name:e.name,logo:e.logo||"https://via.placeholder.com/48x48/4285F4/FFFFFF?text=".concat(e.name.charAt(0)),description:e.description||"",industry:e.industry||"",size:e.size||"",founded:e.founded||"",location:e.location||"Location not specified",website:e.website||"",tier:e.tier||"Tier 3",campus_recruiting:e.campus_recruiting||!1,totalActiveJobs:e.total_active_jobs||0,totalApplicants:e.total_applicants||0,totalHired:e.total_hired||0,awaitedApproval:e.pending_approval||0}}function x(e){return s.A.get("/api/v1/companies/".concat(e,"/followers/count/"))}function p(e,t){return s.A.post("/api/v1/companies/".concat(e,"/followers/"),{user_id:t})}function h(e,t){return s.A.delete("/api/v1/companies/".concat(e,"/followers/"),{data:{user_id:t}})}function g(e,t){return s.A.get("/api/v1/companies/".concat(e,"/followers/status/?user_id=").concat(t))}function u(e){return s.A.get("/api/v1/users/".concat(e,"/following/"))}function b(){return s.A.get("/api/v1/companies/simple/").then(e=>{if(e.data&&e.data.success)return e.data;throw Error("Failed to fetch companies")}).catch(e=>(console.error("Error fetching companies:",e),{success:!1,data:[],count:0,error:e.message}))}},61716:(e,t,a)=>{Promise.resolve().then(a.bind(a,80432))},80432:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>I});var s=a(95155),r=a(12115),n=a(35695),l=a(94631),i=a(85339),o=a(35169),c=a(23227),d=a(66516),m=a(4516),x=a(17580),p=a(69074),h=a(34869),g=a(33786),u=a(38564),b=a(17576),f=a(33109),y=a(14186),j=a(40646),v=a(55868),N=a(51976),w=a(28883),A=a(48937),_=a(21260),S=a(28835),k=a(25384);function I(){let e=(0,n.useRouter)(),t=parseInt((0,n.useParams)().id),[a,I]=(0,r.useState)(null),[C,F]=(0,r.useState)([]),[E,T]=(0,r.useState)("overview"),[P,D]=(0,r.useState)(!1),[L,J]=(0,r.useState)(new Set),[O,z]=(0,r.useState)(0),[R,H]=(0,r.useState)(!0),[W,Y]=(0,r.useState)(null),G=C.filter(e=>e.is_active);(0,r.useEffect)(()=>{(async()=>{H(!0);try{let a=await (0,A.C1)(t),s=(0,A.Y_)(a.data);if(s){I(s);let e=(0,_.zZ)(t);F(e),U(t),B(t),Y(null)}else Y("Company not found"),e.push("/companies")}catch(e){console.error("Error fetching company:",e),Y("Failed to load company data. Please try again.")}finally{H(!1)}})()},[t,e]);let U=async e=>{try{let t=await (0,A.bl)(e);z(t.data.count||0)}catch(e){console.error("Error fetching follower count:",e),z(0)}},B=async e=>{try{let t=(0,S.F6)();if(!t)return;let a=await (0,A.Gu)(e,t);D(a.data.is_following||!1)}catch(e){console.error("Error checking following status:",e),D(!1)}},M=async()=>{try{let e=(0,S.F6)();if(!e)return void alert("Please log in to follow companies");P?(await (0,A.oY)(t,e),z(e=>Math.max(0,e-1))):(await (0,A.jQ)(t,e),z(e=>e+1)),D(!P)}catch(e){console.error("Error toggling follow status:",e),alert("Failed to update follow status. Please try again.")}};return R?(0,s.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(l.A,{className:"h-12 w-12 animate-spin text-blue-600 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Loading company details..."})]})}):W?(0,s.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,s.jsxs)("div",{className:"text-center max-w-md",children:[(0,s.jsx)("div",{className:"text-red-500 mb-4",children:(0,s.jsx)(i.A,{className:"w-16 h-16 mx-auto"})}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Error Loading Company"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:W}),(0,s.jsx)("button",{onClick:()=>e.push("/companies"),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Return to Companies"})]})}):a?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"mb-4",children:(0,s.jsxs)("button",{onClick:()=>e.back(),className:"flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors",children:[(0,s.jsx)(o.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Back to Companies"})]})}),(0,s.jsxs)("div",{className:"relative mb-6",children:[(0,s.jsxs)("div",{className:"h-48 bg-gradient-to-r from-blue-600 to-blue-800 relative overflow-hidden rounded-xl",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-black opacity-20"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"}),(0,s.jsx)("div",{className:"absolute bottom-0 left-0 right-0 p-6",children:(0,s.jsxs)("div",{className:"flex items-end gap-6",children:[(0,s.jsxs)("div",{className:"w-24 h-24 bg-white rounded-xl shadow-lg flex items-center justify-center overflow-hidden border-4 border-white",children:[(0,s.jsx)("img",{src:a.logo,alt:a.name,className:"w-16 h-16 rounded-lg object-cover",onError:e=>{e.target.style.display="none",e.target.nextSibling.style.display="flex"}}),(0,s.jsx)(c.A,{className:"w-12 h-12 text-gray-600 hidden"})]}),(0,s.jsxs)("div",{className:"flex-1 text-white pb-2",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-2",children:a.name}),(0,s.jsxs)("div",{className:"flex items-center gap-4 text-white/90",children:[(0,s.jsx)("span",{children:a.industry}),(0,s.jsx)("span",{children:"•"}),(0,s.jsxs)("span",{children:[O.toLocaleString()," followers"]})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3 pb-2",children:[(0,s.jsx)("button",{onClick:M,className:"px-6 py-2 rounded-lg font-medium transition-colors ".concat(P?"bg-white/20 text-white border border-white/30 hover:bg-white/30":"bg-blue-600 text-white hover:bg-blue-700"),children:P?"Following":"Follow"}),(0,s.jsx)("button",{className:"p-2 rounded-lg bg-white/20 text-white border border-white/30 hover:bg-white/30 transition-colors",children:(0,s.jsx)(d.A,{className:"w-5 h-5"})})]})]})})]}),(0,s.jsx)("div",{className:"bg-white shadow-sm border border-gray-200 rounded-lg mt-4",children:(0,s.jsx)("div",{className:"px-6 py-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-6 text-sm text-gray-600",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(m.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:a.location})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(x.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:a.size})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(p.A,{className:"w-4 h-4"}),(0,s.jsxs)("span",{children:["Founded ",a.founded]})]}),(0,s.jsxs)("a",{href:a.website,target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-2 text-blue-600 hover:text-blue-800",children:[(0,s.jsx)(h.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Website"}),(0,s.jsx)(g.A,{className:"w-3 h-3"})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)("div",{className:"inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium border ".concat((e=>{switch(e){case"Tier 1":return"bg-emerald-100 text-emerald-800 border-emerald-200";case"Tier 2":return"bg-blue-100 text-blue-800 border-blue-200";case"Tier 3":return"bg-purple-100 text-purple-800 border-purple-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}})(a.tier)),children:[(0,s.jsx)(u.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:a.tier})]}),a.campus_recruiting&&(0,s.jsx)("span",{className:"px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium",children:"Campus Recruiting"})]})]})})})]}),(0,s.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg mb-6",children:(0,s.jsx)("div",{className:"px-6",children:(0,s.jsx)("nav",{className:"flex space-x-8",children:[{id:"overview",label:"Overview"},{id:"jobs",label:"Jobs"},{id:"posts",label:"Posts"}].map(e=>(0,s.jsx)("button",{onClick:()=>T(e.id),className:"py-4 px-1 border-b-2 font-medium text-sm transition-colors ".concat(E===e.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:e.label},e.id))})})}),(0,s.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-6",children:["overview"===E&&(0,s.jsx)("div",{className:"max-w-4xl",children:(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:["About ",a.name]}),(0,s.jsx)("p",{className:"text-gray-700 leading-relaxed text-lg",children:a.description})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Company Metrics"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6",children:[(0,s.jsxs)("div",{className:"bg-blue-50 rounded-lg p-6 text-center",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-3",children:[(0,s.jsx)(b.A,{className:"w-6 h-6 text-blue-600"}),(0,s.jsx)("h4",{className:"font-semibold text-blue-900",children:"Active Jobs"})]}),(0,s.jsx)("p",{className:"text-3xl font-bold text-blue-900",children:a.totalActiveJobs})]}),(0,s.jsxs)("div",{className:"bg-green-50 rounded-lg p-6 text-center",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-3",children:[(0,s.jsx)(x.A,{className:"w-6 h-6 text-green-600"}),(0,s.jsx)("h4",{className:"font-semibold text-green-900",children:"Total Applicants"})]}),(0,s.jsx)("p",{className:"text-3xl font-bold text-green-900",children:a.totalApplicants})]}),(0,s.jsxs)("div",{className:"bg-purple-50 rounded-lg p-6 text-center",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-3",children:[(0,s.jsx)(f.A,{className:"w-6 h-6 text-purple-600"}),(0,s.jsx)("h4",{className:"font-semibold text-purple-900",children:"Hired"})]}),(0,s.jsx)("p",{className:"text-3xl font-bold text-purple-900",children:a.totalHired})]}),(0,s.jsxs)("div",{className:"bg-amber-50 rounded-lg p-6 text-center",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-3",children:[(0,s.jsx)(y.A,{className:"w-6 h-6 text-amber-600"}),(0,s.jsx)("h4",{className:"font-semibold text-amber-900",children:"Pending"})]}),(0,s.jsx)("p",{className:"text-3xl font-bold text-amber-900",children:a.awaitedApproval})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Work Life"}),(0,s.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,s.jsx)("p",{className:"text-gray-700 leading-relaxed mb-4",children:"Tier 1"===a.tier?"".concat(a.name," offers a dynamic work environment with cutting-edge technology, comprehensive benefits, and opportunities for professional growth. Our culture emphasizes innovation, collaboration, and work-life balance."):"Tier 2"===a.tier?"At ".concat(a.name,", we foster a collaborative environment where innovation thrives. We offer competitive benefits, flexible work arrangements, and continuous learning opportunities for our employees."):"".concat(a.name," provides a supportive workplace focused on growth and development. We believe in empowering our employees with the tools and opportunities they need to succeed.")}),(0,s.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:[{label:"Remote Work",available:!0},{label:"Health Insurance",available:!0},{label:"Stock Options",available:"Tier 1"===a.tier||"Tier 2"===a.tier},{label:"Learning Budget",available:!0},{label:"Flexible Hours",available:!0},{label:"Gym Membership",available:"Tier 1"===a.tier}].map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(j.A,{className:"w-4 h-4 ".concat(e.available?"text-green-600":"text-gray-400")}),(0,s.jsx)("span",{className:e.available?"text-gray-900":"text-gray-500",children:e.label})]},t))})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Company Details"}),(0,s.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-6",children:(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Industry"}),(0,s.jsx)("p",{className:"text-gray-700",children:a.industry})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Company Size"}),(0,s.jsx)("p",{className:"text-gray-700",children:a.size})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Founded"}),(0,s.jsx)("p",{className:"text-gray-700",children:a.founded})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Headquarters"}),(0,s.jsx)("p",{className:"text-gray-700",children:a.location})]})]})})]})]})}),"jobs"===E&&(0,s.jsxs)("div",{className:"max-w-4xl",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Job Openings"}),(0,s.jsxs)("p",{className:"text-gray-600",children:[G.length," active position",1!==G.length?"s":""," available"]})]}),G.length>0?(0,s.jsx)("div",{className:"space-y-6",children:G.map(e=>(0,s.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow",children:(0,s.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:e.title}),(0,s.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat("INTERNSHIP"===e.type?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800"),children:"INTERNSHIP"===e.type?"Internship":"Full-time"}),e.is_featured&&(0,s.jsx)("span",{className:"px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium",children:"Featured"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-6 text-sm text-gray-600 mb-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(m.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:e.location})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(v.A,{className:"w-4 h-4"}),(0,s.jsxs)("span",{children:["$",e.salary_min,"k - $",e.salary_max,"k / ",e.per]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(y.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:e.duration})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(p.A,{className:"w-4 h-4"}),(0,s.jsxs)("span",{children:["Apply by ",new Date(e.deadline).toLocaleDateString()]})]})]}),(0,s.jsx)(k.G_,{description:e.description,className:"mb-4"}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-2 mb-4",children:[e.skills.slice(0,5).map((e,t)=>(0,s.jsx)("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium",children:e},t)),e.skills.length>5&&(0,s.jsxs)("span",{className:"px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-xs",children:["+",e.skills.length-5," more"]})]})]}),(0,s.jsxs)("div",{className:"flex flex-col gap-2 ml-6",children:[(0,s.jsx)("button",{className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium",children:"Apply Now"}),(0,s.jsx)("button",{className:"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",children:"Save Job"})]})]})},e.id))}):(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(b.A,{className:"w-8 h-8 text-gray-400"})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No Active Job Openings"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"This company doesn't have any active job postings right now."}),(0,s.jsx)("button",{onClick:M,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Follow Company for Updates"})]})]}),"posts"===E&&(0,s.jsxs)("div",{className:"max-w-4xl",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Company Posts"}),(0,s.jsxs)("p",{className:"text-gray-600",children:["Latest updates and news from ",a.name]})]}),(0,s.jsx)("div",{className:"space-y-6",children:[{id:1,title:"We're excited to announce our new internship program!",content:"Applications are now open for our Summer 2024 internship program. Join our team and work on cutting-edge projects while learning from industry experts.",date:"2024-01-15",likes:42,comments:8},{id:2,title:"Company Culture Spotlight",content:"Take a behind-the-scenes look at our innovative workspace and learn about our commitment to employee well-being and professional development.",date:"2024-01-10",likes:28,comments:5}].map(e=>(0,s.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-6",children:(0,s.jsxs)("div",{className:"flex items-start gap-4",children:[(0,s.jsxs)("div",{className:"w-12 h-12 bg-white rounded-lg flex items-center justify-center shadow-sm",children:[(0,s.jsx)("img",{src:a.logo,alt:a.name,className:"w-8 h-8 rounded object-cover",onError:e=>{e.target.style.display="none",e.target.nextSibling.style.display="flex"}}),(0,s.jsx)(c.A,{className:"w-6 h-6 text-gray-600 hidden"})]}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,s.jsx)("h3",{className:"font-semibold text-gray-900",children:a.name}),(0,s.jsx)("span",{className:"text-gray-500",children:"•"}),(0,s.jsx)("span",{className:"text-sm text-gray-500",children:new Date(e.date).toLocaleDateString()})]}),(0,s.jsx)("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:e.title}),(0,s.jsx)("p",{className:"text-gray-700 mb-4",children:e.content}),(0,s.jsxs)("div",{className:"flex items-center gap-6 text-sm text-gray-500",children:[(0,s.jsxs)("button",{className:"flex items-center gap-2 hover:text-blue-600 transition-colors",children:[(0,s.jsx)(N.A,{className:"w-4 h-4"}),(0,s.jsxs)("span",{children:[e.likes," likes"]})]}),(0,s.jsxs)("button",{className:"flex items-center gap-2 hover:text-blue-600 transition-colors",children:[(0,s.jsx)(w.A,{className:"w-4 h-4"}),(0,s.jsxs)("span",{children:[e.comments," comments"]})]}),(0,s.jsxs)("button",{className:"flex items-center gap-2 hover:text-blue-600 transition-colors",children:[(0,s.jsx)(d.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Share"})]})]})]})]})},e.id))}),(0,s.jsxs)("div",{className:"text-center py-12 mt-8",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(w.A,{className:"w-8 h-8 text-gray-400"})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"That's all for now"}),(0,s.jsxs)("p",{className:"text-gray-600",children:["Follow ",a.name," to get notified about new posts and updates."]})]})]})]})]}):(0,s.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-gray-600",children:"No company data available"}),(0,s.jsx)("button",{onClick:()=>e.push("/companies"),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors mt-4",children:"Return to Companies"})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[3464,4344,3983,8441,1684,7358],()=>t(61716)),_N_E=e.O()}]);