(()=>{var e={};e.id=6636,e.ids=[6636],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},25743:(e,s,t)=>{Promise.resolve().then(t.bind(t,72160))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},33991:(e,s,t)=>{Promise.resolve().then(t.bind(t,37359))},37359:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>p});var a=t(60687),r=t(43210),l=t(30474),i=t(23877),n=t(33322);function c({isOpen:e,onClose:s,resume:t,onUpload:l,onDelete:c}){let[d,o]=(0,r.useState)([]),[m,x]=(0,r.useState)(!1),[u,p]=(0,r.useState)(!1),h=(0,r.useRef)(null),f=async()=>{try{if(p(!0),!localStorage.getItem("access_token")){console.error("No authentication token found"),o([]),p(!1);return}let e=[];try{if(console.log("Fetching user-specific resumes..."),e=await n.N.getResumes(),console.log("Resumes fetched:",e),!Array.isArray(e))throw console.error("Invalid resume data format:",e),Error("Invalid resume data format")}catch(s){console.log("New resumes API not available, falling back to profile data:",s);try{let s=await n.N.getProfile();if(console.log("User profile fetched for resume fallback:",s?.id),s?.resume||s?.resume_url){let t=s.resume_url||s.resume,a=t.split("/").pop()||"Resume.pdf";e=[{id:s.id||1,name:a,resume_url:t,uploaded_at:s.updated_at||new Date().toISOString()}]}}catch(e){console.error("Error fetching profile for resume:",e)}}let s=e.map((e,s)=>({id:e.id||s+1,name:e.name||e.file_name||e.resume_url?.split("/").pop()||`Resume ${s+1}`,date:e.uploaded_at?new Date(e.uploaded_at).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"}):new Date().toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"}),url:e.resume_url||e.file_url||e.url,status:"success"}));console.log(`Displaying ${s.length} resumes for current user`),o(s)}catch(e){if(console.error("Error fetching resumes:",e),t){let e=[];if("string"==typeof t&&""!==t.trim()){let s=t.split("/"),a=s[s.length-1];e.push({id:1,name:a||"Resume",date:new Date().toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"}),url:t,status:"success"})}o(e)}else o([])}finally{p(!1)}},g=async e=>{let s=e.target.files[0];if(s)try{if(x(!0),s.size>5242880){alert("File size exceeds 5MB limit. Please select a smaller file."),x(!1);return}let e={id:Date.now(),name:s.name,date:new Date().toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"}),file:s,url:URL.createObjectURL(s),status:"uploading",progress:0};o(s=>[...s,e]);let t=setInterval(()=>{o(s=>s.map(s=>s.id===e.id?{...s,progress:Math.min(s.progress+25,99)}:s))},500);try{await n.N.uploadResume(s,s.name,!1),clearInterval(t),f().catch(e=>console.log("Resume refresh failed after upload:",e))}catch(s){throw clearInterval(t),o(s=>s.map(s=>s.id===e.id?{...s,status:"error",progress:0}:s)),s}x(!1)}catch(e){console.error("Error uploading resume:",e),x(!1),alert("Failed to upload resume. Please try again.")}},j=e=>{if(!e)return void alert("Resume URL is not available");if(e.startsWith("blob:"))window.open(e,"_blank");else if(e.startsWith("http://")||e.startsWith("https://"))window.open(e,"_blank");else{let s=`${window.location.origin}${e.startsWith("/")?"":"/"}${e}`;window.open(s,"_blank")}},y=async e=>{try{if(e.url){let s=document.createElement("a");s.href=e.url,s.download=e.name||"resume.pdf",document.body.appendChild(s),s.click(),document.body.removeChild(s)}else alert("Resume file is not available for download")}catch(e){console.error("Error downloading resume:",e),alert("Failed to download resume. Please try again.")}},N=async e=>{try{let s=d.find(s=>s.id===e);if(o(s=>s.filter(s=>s.id!==e)),s&&s.id&&"number"==typeof s.id)try{let e=await n.N.deleteResume(s.id);console.log("Resume deletion response:",e)}catch(e){console.error("Backend delete failed, but UI is updated:",e)}if("function"==typeof c)try{await c(s)}catch(e){console.error("onDelete callback error:",e)}f().catch(e=>console.log("Resume refresh failed after delete:",e))}catch(e){console.error("Error in delete process:",e),f().catch(e=>console.log("Resume refresh failed after delete error:",e))}},b=e=>"uploading"===e.status?(0,a.jsx)("div",{className:"ml-2 text-blue-500",children:(0,a.jsx)(i.hW,{className:"animate-spin"})}):"success"===e.status?(0,a.jsx)("div",{className:"ml-2 text-green-500",children:(0,a.jsx)(i.A7C,{})}):"error"===e.status?(0,a.jsx)("div",{className:"ml-2 text-red-500",children:(0,a.jsx)(i.TNq,{})}):null;return e?(0,a.jsx)("div",{className:"fixed inset-0 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg w-full max-w-2xl shadow-xl",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center p-6 border-b",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-800",children:"My Resumes"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Upload multiple resumes for different job types"})]}),(0,a.jsx)("button",{onClick:s,className:"text-gray-500 hover:text-gray-700",children:(0,a.jsx)(i._Hm,{size:24})})]}),(0,a.jsx)("div",{className:"p-6 max-h-96 overflow-y-auto",children:u?(0,a.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,a.jsx)(i.hW,{className:"animate-spin text-blue-500 text-2xl mr-3"}),(0,a.jsx)("span",{className:"text-gray-600",children:"Loading resumes..."})]}):0===d.length?(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)("p",{children:"No resumes uploaded yet"}),(0,a.jsx)("p",{className:"text-sm mt-2",children:"You can upload multiple resumes for different job applications"})]}):(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsxs)("h3",{className:"text-md font-medium text-gray-700",children:["Your Resumes (",d.length,")"]}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:d.length>1?"You can use different resumes for different applications":""})]}),(0,a.jsx)("div",{className:"space-y-4",children:d.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between bg-gray-50 p-4 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center flex-grow cursor-pointer hover:bg-gray-100 p-2 rounded",onClick:()=>"uploading"!==e.status?j(e.url):null,children:[(0,a.jsx)("div",{className:"bg-blue-100 p-3 rounded-full mr-4",children:(0,a.jsx)(i.t69,{className:"text-blue-600"})}),(0,a.jsxs)("div",{className:"flex-grow",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("h3",{className:"font-medium text-gray-800 mr-2",children:e.name}),b(e),"uploading"!==e.status&&(0,a.jsx)(i.EQc,{className:"text-gray-500 text-xs ml-2"})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Uploaded on ",e.date]}),"uploading"===e.status&&(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5 mt-2",children:(0,a.jsx)("div",{className:"bg-blue-600 h-2.5 rounded-full transition-all duration-300",style:{width:`${e.progress}%`}})})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>y(e),className:"p-2 text-green-500 hover:text-green-700 hover:bg-green-50 rounded-full","aria-label":"Download resume",disabled:"uploading"===e.status,title:"Download resume",children:(0,a.jsx)(i.dIn,{className:"uploading"===e.status?"opacity-50":""})}),(0,a.jsx)("button",{onClick:()=>N(e.id),className:"p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full","aria-label":"Delete resume",disabled:"uploading"===e.status,title:"Delete resume",children:(0,a.jsx)(i.qbC,{className:"uploading"===e.status?"opacity-50":""})})]})]},e.id))})]})}),(0,a.jsxs)("div",{className:"border-t p-6 flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Supported formats: PDF, DOCX (max 5MB)"}),(0,a.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"You can upload multiple resumes tailored to different positions"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("input",{type:"file",accept:".pdf,.docx",className:"hidden",ref:h,onChange:g,disabled:m}),(0,a.jsx)("button",{onClick:()=>h.current.click(),className:`bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center ${m?"opacity-70 cursor-not-allowed":""}`,disabled:m,children:m?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.hW,{className:"mr-2 animate-spin"})," Uploading..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.HVe,{className:"mr-2"})," Add Resume"]})})]})]})]})}):null}function d({isOpen:e,onClose:s,documents:t={},onUploadCertificate:l,onUploadMarksheet:n,onDeleteCertificate:c,onDeleteMarksheet:d}){let[o,m]=(0,r.useState)("tenth"),[x,u]=(0,r.useState)(!1),[p,h]=(0,r.useState)({tenth:[],twelfth:[],semesterwise:[]}),f=(0,r.useRef)(null),g=(0,r.useRef)(null),j=(0,r.useRef)(null),y=e=>e?e&&!e.startsWith("http")?`http://localhost:8000${e}`:e:null,N=async e=>{let s=e.target.files[0];if(s)try{if(u(!0),"tenth"===o){await l(s,"tenth");let e={id:Date.now(),name:s.name,date:new Date().toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"}),url:URL.createObjectURL(s)};h(s=>({...s,tenth:[e]}))}else if("twelfth"===o){await l(s,"twelfth");let e={id:Date.now(),name:s.name,date:new Date().toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"}),url:URL.createObjectURL(s)};h(s=>({...s,twelfth:[e]}))}else if("semesterwise"===o){if(!g.current||!j.current){alert("Semester input fields are not available"),u(!1);return}let e=g.current.value,t=j.current.value;if(!e||!t){alert("Please enter semester number and CGPA"),u(!1);return}await n(s,e,t);let a={id:Date.now(),name:`Semester ${e} Marksheet (CGPA: ${t})`,date:new Date().toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"}),url:URL.createObjectURL(s),semester:e,cgpa:t};h(e=>({...e,semesterwise:[...e.semesterwise,a]}))}u(!1)}catch(e){console.error("Error uploading document:",e),u(!1),alert("Failed to upload document. Please try again.")}},b=e=>{if(!e)return void alert("Document URL is not available");let s=y(e);window.open(s,"_blank")},w=e=>{try{if(e.url){let s=y(e.url),t=e.createElement("a");t.href=s,t.download=e.name||"document.pdf",e.body.appendChild(t),t.click(),e.body.removeChild(t)}else alert("Document file is not available for download")}catch(e){console.error("Error downloading document:",e),alert("Failed to download document. Please try again.")}},v=async(e,s=null)=>{try{"tenth"===o||"twelfth"===o?(await c("tenth"===o?"10th":"12th"),h(e=>({...e,[o]:[]}))):"semesterwise"===o&&s?(await d(s.semester),h(e=>({...e,semesterwise:e.semesterwise.filter(e=>e.semester!==s.semester)}))):h(s=>({...s,[o]:s[o].filter(s=>s.id!==e)}))}catch(e){console.error("Error deleting document:",e),alert("Failed to delete document. Please try again.")}};return e?(0,a.jsx)("div",{className:"fixed inset-0 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg w-full max-w-2xl shadow-xl",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center p-6 border-b",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-800",children:"My Documents"}),(0,a.jsx)("button",{onClick:s,className:"text-gray-500 hover:text-gray-700",children:(0,a.jsx)(i._Hm,{size:24})})]}),(0,a.jsxs)("div",{className:"flex border-b",children:[(0,a.jsx)("button",{className:`px-6 py-3 text-sm font-medium ${"tenth"===o?"text-blue-600 border-b-2 border-blue-600":"text-gray-500 hover:text-gray-700"}`,onClick:()=>m("tenth"),children:"10th Certificate"}),(0,a.jsx)("button",{className:`px-6 py-3 text-sm font-medium ${"twelfth"===o?"text-blue-600 border-b-2 border-blue-600":"text-gray-500 hover:text-gray-700"}`,onClick:()=>m("twelfth"),children:"12th Certificate"}),(0,a.jsx)("button",{className:`px-6 py-3 text-sm font-medium ${"semesterwise"===o?"text-blue-600 border-b-2 border-blue-600":"text-gray-500 hover:text-gray-700"}`,onClick:()=>m("semesterwise"),children:"Semester Grades"})]}),(0,a.jsx)("div",{className:"p-6 max-h-96 overflow-y-auto",children:0===p[o].length?(0,a.jsx)("div",{className:"text-center py-8 text-gray-500",children:"No documents uploaded for this category yet"}):(0,a.jsx)("div",{className:"space-y-4",children:p[o].map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between bg-gray-50 p-4 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center flex-grow cursor-pointer hover:bg-gray-100 p-2 rounded",onClick:()=>b(e.url),children:[(0,a.jsx)("div",{className:"bg-blue-100 p-3 rounded-full mr-4",children:(0,a.jsx)(i.t69,{className:"text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("h3",{className:"font-medium text-gray-800 mr-2",children:e.name}),(0,a.jsx)(i.EQc,{className:"text-gray-500 text-xs"})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Uploaded on ",e.date]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>w(e),className:"p-2 text-green-500 hover:text-green-700 hover:bg-green-50 rounded-full","aria-label":"Download document",title:"Download document",children:(0,a.jsx)(i.dIn,{})}),(0,a.jsx)("button",{onClick:()=>v(e.id,e),className:"p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full","aria-label":"Delete document",title:"Delete document",children:(0,a.jsx)(i.qbC,{})})]})]},e.id||`${o}-${s}`))})}),(0,a.jsxs)("div",{className:"border-t p-6",children:["semesterwise"===o&&(0,a.jsxs)("div",{className:"mb-4 grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Semester Number"}),(0,a.jsx)("input",{type:"number",min:"1",max:"8",ref:g,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"e.g., 1"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"CGPA"}),(0,a.jsx)("input",{type:"number",step:"0.01",min:"0",max:"10",ref:j,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"e.g., 8.5"})]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Supported formats: PDF, JPG, PNG (max 5MB)"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("input",{type:"file",accept:".pdf,.jpg,.jpeg,.png",className:"hidden",ref:f,onChange:N,disabled:x}),(0,a.jsx)("button",{onClick:()=>f.current.click(),className:`bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center ${x?"opacity-70 cursor-not-allowed":""}`,disabled:x,children:x?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.hW,{className:"mr-2 animate-spin"})," Uploading..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.HVe,{className:"mr-2"})," Upload Document"]})})]})]})]})]})}):null}var o=t(51421);let m={resume:{maxSize:5242880,allowedTypes:["application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],allowedExtensions:[".pdf",".doc",".docx"],displayName:"Resume"},profile_image:{maxSize:2097152,allowedTypes:["image/jpeg","image/png","image/gif","image/webp"],allowedExtensions:[".jpg",".jpeg",".png",".gif",".webp"],displayName:"Profile Image"},certificate:{maxSize:5242880,allowedTypes:["application/pdf","image/jpeg","image/png"],allowedExtensions:[".pdf",".jpg",".jpeg",".png"],displayName:"Certificate"},marksheet:{maxSize:5242880,allowedTypes:["application/pdf","image/jpeg","image/png"],allowedExtensions:[".pdf",".jpg",".jpeg",".png"],displayName:"Marksheet"},cover_letter:{maxSize:2097152,allowedTypes:["application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],allowedExtensions:[".pdf",".doc",".docx"],displayName:"Cover Letter"},generic_document:{maxSize:0xa00000,allowedTypes:["application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document","image/jpeg","image/png"],allowedExtensions:[".pdf",".doc",".docx",".jpg",".jpeg",".png"],displayName:"Document"}},x=(e,s="generic_document")=>{let t=m[s],a=[],r=[];if(!e)return{isValid:!1,errors:["No file selected"],warnings:[],file:null};if(e.size>t.maxSize){let s=Math.round(t.maxSize/1048576),r=Math.round(e.size/1048576*10)/10;a.push(`File size (${r}MB) exceeds maximum allowed size of ${s}MB`)}if(!t.allowedTypes.includes(e.type)){let e=t.allowedExtensions.join(", ");a.push(`File type not supported. Allowed formats: ${e}`)}let l="."+e.name.split(".").pop().toLowerCase();if(!t.allowedExtensions.includes(l)){let e=t.allowedExtensions.join(", ");a.push(`File extension not allowed. Supported extensions: ${e}`)}switch(s){case"resume":e.size<51200&&r.push("Resume file seems very small. Please ensure it contains adequate content."),e.name.length>100&&r.push("File name is very long. Consider using a shorter name.");break;case"profile_image":e.size<10240&&r.push("Image file seems very small. Please ensure it's a clear photo.");break;case"certificate":case"marksheet":e.size<102400&&r.push("Document file seems small. Please ensure it's clearly readable.")}return(e.name.includes("..")||e.name.includes("/")||e.name.includes("\\"))&&a.push("File name contains invalid characters"),e.name.length>255&&a.push("File name is too long (maximum 255 characters)"),{isValid:0===a.length,errors:a,warnings:r,file:e,size:e.size,sizeDisplay:u(e.size),type:e.type,name:e.name}},u=e=>{if(0===e)return"0 Bytes";let s=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,s)).toFixed(1))+" "+["Bytes","KB","MB","GB"][s]};function p(){var e,s,t;let{handleApiError:m,showFileUploadError:u}=(0,o.hN)(),[p,h]=(0,r.useState)(!1),[f,g]=(0,r.useState)(!1),[j,y]=(0,r.useState)(null),[N,b]=(0,r.useState)(!0),[w,v]=(0,r.useState)(null),[_,C]=(0,r.useState)([]),[S,k]=(0,r.useState)(0),[D,P]=(0,r.useState)(null),[R,E]=(0,r.useState)({totalListings:0,eligibleJobs:0,loading:!0}),$=async()=>{try{let e=await n.N.getResumes();if(k(e.length),e.length>0){let s=e.reduce((e,s)=>{let t=new Date(s.uploaded_at||s.created_at),a=new Date(e);return t>a?s.uploaded_at||s.created_at:e},e[0].uploaded_at||e[0].created_at);P(s)}}catch(e){console.error("Error fetching resumes:",e),j?.resume&&(k(1),P(j.updated_at))}},z=async e=>{let s=x(e,"resume");if(!s.isValid)return void u(s.errors);try{await n.N.uploadResume(e,e.name,!1),await $()}catch(e){console.error("Error uploading resume:",e),u(["Failed to upload resume","Please check the file format and size","Supported formats: PDF, DOC, DOCX (max 5MB)"])}},U=async e=>{try{if(await $(),e?.url===j?.resume){let e={...j,resume:null};y(e)}}catch(e){console.error("Error handling resume deletion:",e)}},L=()=>j?.gpa||"0.00",M=e=>e&&"string"==typeof e&&""!==e.trim()&&"null"!==e&&"undefined"!==e,A=e=>j&&j[`semester${e}_cgpa`]||"-";return N?(0,a.jsxs)("div",{className:"flex items-center justify-center min-h-screen bg-gray-50",children:[(0,a.jsx)(i.hW,{className:"animate-spin text-blue-500 text-4xl mr-3"}),(0,a.jsx)("span",{className:"text-xl text-gray-700",children:"Loading profile..."})]}):w?(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen bg-gray-50",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-red-500 text-xl mb-4",children:w}),(0,a.jsx)("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600",children:"Retry"})]})}):(0,a.jsxs)("div",{className:"bg-gray-50 min-h-screen p-6",children:[(0,a.jsxs)("div",{className:"max-w-8xl mx-auto grid grid-cols-1 lg:grid-cols-12 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-3 bg-white rounded-lg p-5 shadow-sm h-fit content-card profile-container",children:[(0,a.jsx)("div",{className:"flex justify-between",children:N?(0,a.jsx)("div",{className:"w-50 h-50 bg-blue-100 flex items-center justify-center rounded-lg mb-4",children:(0,a.jsx)(i.hW,{className:"animate-spin text-blue-500 text-2xl"})}):j?.profile_image_url?(0,a.jsx)("div",{className:"w-50 h-50 bg-blue-100 object-center text-center rounded-lg mb-4 relative mx-auto",children:(0,a.jsx)(l.default,{src:j.profile_image_url,alt:`${j.first_name} ${j.last_name}`,fill:!0,className:"rounded-lg object-cover"})}):(0,a.jsx)("div",{className:"w-50 h-50 bg-blue-500 text-white flex items-center justify-center rounded-lg mb-4 mx-auto",children:(0,a.jsx)("span",{className:"text-3xl font-bold",children:j?.initial||(j?.first_name?j.first_name[0].toUpperCase():"S")})})}),(0,a.jsx)("h1",{className:"text-xl font-bold text-center mt-2 text-gray-800",children:j?.first_name&&j?.last_name?`${j.first_name} ${j.last_name}`:"-"}),(0,a.jsxs)("div",{className:"mt-4 space-y-3 text-md",children:[(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"Student ID"}),(0,a.jsxs)("p",{className:"font-medium text-gray-800",children:[": ",j?.student_id||"-"]})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"Major"}),(0,a.jsxs)("p",{className:"font-medium text-gray-800",children:[": ",j?.branch||"-"]})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"Passed Out"}),(0,a.jsxs)("p",{className:"font-medium text-gray-800",children:[": ",j?.passout_year||"-"]})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"Gender"}),(0,a.jsxs)("p",{className:"font-medium text-gray-800",children:[": ",j?.gender||"-"]})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"Birthday"}),(0,a.jsxs)("p",{className:"font-medium text-gray-800",children:[": ",j?.date_of_birth||"-"]})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"Phone"}),(0,a.jsxs)("p",{className:"font-medium text-gray-800",children:[": ",j?.phone||"-"]})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"Email"}),(0,a.jsxs)("p",{className:"font-medium text-gray-800",children:[": ",j?.contact_email||j?.user?.email||"-"]})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"Campus"}),(0,a.jsxs)("p",{className:"font-medium text-gray-800",children:[": ",j?.college_name||"-"]})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"Placement"}),(0,a.jsxs)("p",{className:"font-medium text-gray-800",children:[": ",j?.joining_year&&j?.passout_year?`${j.joining_year}-${j.passout_year}`:"-"]})]})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-3 text-gray-800",children:"Skills"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:(()=>{let e=j?.skills||selectedStudent?.skills;return Array.isArray(e)&&e.length>0?e.map((e,s)=>(0,a.jsx)("span",{className:"bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm",children:e},s)):"string"==typeof e&&e.trim()?e.split(",").map((e,s)=>(0,a.jsx)("span",{className:"bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm",children:e.trim()},s)):(0,a.jsx)("p",{className:"text-gray-600",children:"No skills listed"})})()})]})]}),(0,a.jsxs)("div",{className:"lg:col-span-6 space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg p-5 shadow-sm content-card",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-6 text-gray-800",children:"Academic"}),(0,a.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"Semester Wise score"}),(0,a.jsxs)("div",{className:"bg-blue-50 inline-flex items-center px-3 py-1 rounded-full ml-3",children:[(0,a.jsx)("span",{className:"text-blue-600 font-medium",children:L()}),(0,a.jsx)("span",{className:"text-sm text-gray-500 ml-1",children:"CGPA"}),(0,a.jsx)("span",{className:"text-blue-600 ml-2",children:(e=L())&&"-"!==e?(9.5*parseFloat(e)).toFixed(2)+"%":"-"})]})]}),(0,a.jsx)("div",{className:"text-lg font-semibold text-gray-800",children:(s=j?.joining_year,t=j?.passout_year,s&&t?`${s} - ${t}`:"-")})]}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full border-collapse border border-gray-300",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"bg-gray-50",children:[(0,a.jsx)("th",{className:"border border-gray-300 px-4 py-3 text-sm font-semibold text-gray-700",children:"Sem"}),[1,2,3,4,5,6,7,8].map(e=>(0,a.jsx)("th",{className:"border border-gray-300 px-4 py-3 text-sm font-semibold text-gray-700",children:e},e))]})}),(0,a.jsx)("tbody",{children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"border border-gray-300 px-4 py-3 text-sm font-medium text-gray-700",children:"Cgpa"}),[1,2,3,4,5,6,7,8].map(e=>(0,a.jsx)("td",{className:"border border-gray-300 px-4 py-3 text-sm text-gray-700",children:A(e)},e))]})})]})})}),(0,a.jsx)("hr",{className:"my-6"}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"Class XII"}),(0,a.jsxs)("div",{className:"bg-blue-50 inline-flex items-center px-3 py-1 rounded-full ml-3",children:[(0,a.jsx)("span",{className:"text-blue-600 font-medium",children:j?.twelfth_cgpa||"-"}),(0,a.jsx)("span",{className:"text-sm text-gray-500 ml-1",children:"CGPA"}),(0,a.jsx)("span",{className:"text-blue-600 ml-2",children:j?.twelfth_percentage||"-"})]})]}),(0,a.jsx)("div",{className:"text-lg font-semibold text-gray-800",children:j?.twelfth_year_of_passing?`${parseInt(j.twelfth_year_of_passing)-2} - ${j.twelfth_year_of_passing}`:"-"})]}),(0,a.jsx)("div",{className:"flex justify-between items-start mb-2",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-6 w-full",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 text-sm w-[120px]",children:"College :"}),(0,a.jsx)("p",{className:"text-gray-700 font-medium",children:j?.twelfth_school||"-"})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 text-sm w-[120px]",children:"Board :"}),(0,a.jsx)("p",{className:"text-gray-700 font-medium",children:j?.twelfth_board||"-"})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 text-sm w-[120px]",children:"Location :"}),(0,a.jsx)("p",{className:"text-gray-700 font-medium",children:j?.city||"-"})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 text-sm w-[120px]",children:"Specialization :"}),(0,a.jsx)("p",{className:"text-gray-700 font-medium",children:j?.twelfth_specialization||"-"})]})]})]})})]}),(0,a.jsx)("hr",{className:"my-6"}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"Class X"}),(0,a.jsxs)("div",{className:"bg-blue-50 inline-flex items-center px-3 py-1 rounded-full ml-3",children:[(0,a.jsx)("span",{className:"text-blue-600 font-medium",children:j?.tenth_cgpa||"-"}),(0,a.jsx)("span",{className:"text-sm text-gray-500 ml-1",children:"CGPA"}),(0,a.jsx)("span",{className:"text-blue-600 ml-2",children:j?.tenth_percentage||"-"})]})]}),(0,a.jsx)("div",{className:"text-lg font-semibold text-gray-800",children:j?.tenth_year_of_passing?`${parseInt(j.tenth_year_of_passing)-1} - ${j.tenth_year_of_passing}`:"-"})]}),(0,a.jsx)("div",{className:"flex justify-between items-start mb-2",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-6 w-full",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 text-sm w-[120px]",children:"School :"}),(0,a.jsx)("p",{className:"text-gray-700 font-medium",children:j?.tenth_school||"-"})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 text-sm w-[120px]",children:"Board :"}),(0,a.jsx)("p",{className:"text-gray-700 font-medium",children:j?.tenth_board||"-"})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 text-sm w-[120px]",children:"Location :"}),(0,a.jsx)("p",{className:"text-gray-700 font-medium",children:j?.city||"-"})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 text-sm w-[120px]",children:"Specialization :"}),(0,a.jsx)("p",{className:"text-gray-700 font-medium",children:"-"})]})]})]})})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-5 shadow-sm content-card",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4 text-gray-800",children:"Companies"}),R.loading?(0,a.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,a.jsx)(i.hW,{className:"animate-spin text-blue-500 text-xl mr-2"}),(0,a.jsx)("span",{className:"text-gray-600",children:"Loading company data..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("p",{className:"text-gray-500 text-sm",children:"Total Listings"}),(0,a.jsx)("p",{className:"text-lg font-semibold text-gray-700",children:R.totalListings})]})}),(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("p",{className:"text-gray-500 text-sm",children:"Eligible Jobs"}),(0,a.jsx)("p",{className:"text-lg font-semibold text-gray-700",children:R.eligibleJobs})]})})]})]})]}),(0,a.jsxs)("div",{className:"lg:col-span-3 space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg p-5 shadow-sm content-card",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4 text-gray-800",children:"My Files"}),(0,a.jsxs)("div",{className:"flex items-center p-2 mb-3 cursor-pointer hover:bg-gray-50 rounded-lg transition-colors",onClick:()=>h(!0),children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-3",children:(0,a.jsx)("div",{className:"text-blue-600",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"})})})}),(0,a.jsxs)("div",{className:"flex-grow",children:[(0,a.jsx)("h3",{className:"font-medium text-gray-700",children:"Resumes"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:S>0?`${S} resume${S>1?"s":""} uploaded`+(D?` • Last updated ${new Date(D).toLocaleDateString()}`:""):"No resumes uploaded"})]}),(0,a.jsx)("div",{className:"bg-green-50 px-3 py-1 rounded-full",children:(0,a.jsx)("span",{className:"text-green-600 font-medium",children:S})})]}),(0,a.jsxs)("div",{className:"flex items-center p-2 cursor-pointer hover:bg-gray-50 rounded-lg transition-colors",onClick:()=>g(!0),children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-3",children:(0,a.jsx)("div",{className:"text-blue-600",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})})}),(0,a.jsxs)("div",{className:"flex-grow",children:[(0,a.jsx)("h3",{className:"font-medium text-gray-700",children:"Documents"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Academic certificates and marksheets"})]}),(0,a.jsx)("div",{className:"bg-green-50 px-3 py-1 rounded-full",children:(0,a.jsx)("span",{className:"text-green-600 font-medium",children:(()=>{let e=+!!M(j?.tenth_certificate);return e+ +!!M(j?.twelfth_certificate)+(_?_.filter(e=>e&&M(e.marksheet_url)).length:0)})()})})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-5 shadow-sm content-card",children:[(0,a.jsx)("div",{className:"flex justify-between items-center mb-4",children:(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-800",children:"CURRENT ADDRESS"})}),(0,a.jsxs)("div",{className:"space-y-3 text-sm",children:[(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"City"}),(0,a.jsxs)("p",{className:"font-medium text-gray-700",children:[": ",j?.city||"-"]})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"District"}),(0,a.jsxs)("p",{className:"font-medium text-gray-700",children:[": ",j?.district||"-"]})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"State"}),(0,a.jsxs)("p",{className:"font-medium text-gray-700",children:[": ",j?.state||"-"]})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"Pin Code"}),(0,a.jsxs)("p",{className:"font-medium text-gray-700",children:[": ",j?.pincode||"-"]})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"Country"}),(0,a.jsxs)("p",{className:"font-medium text-gray-700",children:[": ",j?.country||"-"]})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("p",{className:"text-gray-500 w-20",children:"Address"}),(0,a.jsxs)("p",{className:"font-medium text-gray-700",children:[": ",j?.address||"-"]})]})]})]})]})]}),(0,a.jsx)(c,{isOpen:p,onClose:()=>h(!1),resume:j?.resume_url||j?.resume,onUpload:z,onDelete:U}),(0,a.jsx)(d,{isOpen:f,onClose:()=>g(!1),documents:{tenth:j?.tenth_certificate_url||j?.tenth_certificate,twelfth:j?.twelfth_certificate_url||j?.twelfth_certificate,semesterMarksheets:_},onUploadCertificate:n.N.uploadCertificate,onUploadMarksheet:n.N.uploadSemesterMarksheet,onDeleteCertificate:n.N.deleteCertificate,onDeleteMarksheet:n.N.deleteMarksheet})]})}},50424:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>d});var a=t(65239),r=t(48088),l=t(88170),i=t.n(l),n=t(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d={children:["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,72160)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\profile\\page.jsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,75535)),"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\layout.js"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\profile\\page.jsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58138:(e,s,t)=>{"use strict";t.d(s,{A:()=>l});var a=t(51060);t(51421);let r=a.A.create({baseURL:"http://127.0.0.1:8000",headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>{let s=localStorage.getItem("access_token");return s&&(e.headers.Authorization=`Bearer ${s}`),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,async e=>{let s=e.config;if(e.response?.status===401&&!s._retry){s._retry=!0;try{let e=localStorage.getItem("refresh_token");if(e){let t=await a.A.post("http://127.0.0.1:8000/api/auth/token/refresh/",{refresh:e});return localStorage.setItem("access_token",t.data.access),s.headers.Authorization=`Bearer ${t.data.access}`,r(s)}}catch(e){console.error("Error refreshing token:",e)}}return Promise.reject(e)});let l=r},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var a=t(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},72160:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\profile\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\VS CODE\\combine\\frontend\\src\\app\\profile\\page.jsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[4447,681,1658,1060,7300,2305,3322],()=>t(50424));module.exports=a})();